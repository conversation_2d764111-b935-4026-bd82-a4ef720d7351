{"key": {"identifiers": {"COST_OPERATION_ID": {"key": "1", "version": {"major": 1, "minor": 0}}, "CO2_OPERATION_ID": {"key": "1", "version": {"major": 1, "minor": 0}}, "PROCUREMENT_TYPE_ID": {"procurementTypeResult": "INHOUSE"}}}, "cards": {"overhead": {"views": ["co2", "cost"], "title": {"cost": "Indirect after production", "co2": "Indirect after production"}, "kpi": {"cost": "#Cost_ManufacturedMaterial_Total_IndirectCostsAfterProduction", "co2": "#CO2_ManufacturedMaterial_Total_IndirectCO2eAfterProduction"}, "tableVariations": {"cost": ["Production"], "co2": ["Production"]}}}, "tableConfigs": {"overhead_cost_Production": {"type": "field", "rows": ["SpecialDirectCosts_ManufacturedMaterial", "OverheadsAfterPC_ManufacturedMaterial", "Profit_ManufacturedMaterial", "TermsOfPayment_ManufacturedMaterial", "IncoTerms_ManufacturedMaterial"], "rowDefinitions": {"DevelopmentCosts-Rm_ManufacturedMaterial": {"id": "DevelopmentCosts-Rm_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Raw material", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_DevelopmentCosts-Rm_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_DevelopmentCosts-Rm_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_DevelopmentCosts-Rm"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_DevelopmentCosts-Rm"}]}, "DevelopmentCosts-Pp_ManufacturedMaterial": {"id": "DevelopmentCosts-Pp_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Purchased part", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_DevelopmentCosts-Pp_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_DevelopmentCosts-Pp_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_DevelopmentCosts-Pp"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_DevelopmentCosts-Pp"}]}, "DevelopmentCosts-Mfg_ManufacturedMaterial": {"id": "DevelopmentCosts-Mfg_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_DevelopmentCosts-Mfg_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_DevelopmentCosts-Mfg_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_DevelopmentCosts-Mfg"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_DevelopmentCosts-Mfg"}]}, "DevelopmentCosts_ManufacturedMaterial": {"id": "DevelopmentCosts_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Development", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_DevelopmentCosts_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_DevelopmentCosts"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_DevelopmentCosts"}], "rows": ["DevelopmentCosts-Rm_ManufacturedMaterial", "DevelopmentCosts-Pp_ManufacturedMaterial", "DevelopmentCosts-Mfg_ManufacturedMaterial"], "isCollapsed": true}, "RampUpCosts-Rm_ManufacturedMaterial": {"id": "RampUpCosts-Rm_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Raw material", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_RampUpCosts-Rm_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_RampUpCosts-Rm_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_RampUpCosts-Rm"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_RampUpCosts-Rm"}]}, "RampUpCosts-Pp_ManufacturedMaterial": {"id": "RampUpCosts-Pp_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Purchased part", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_RampUpCosts-Pp_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_RampUpCosts-Pp_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_RampUpCosts-Pp"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_RampUpCosts-Pp"}]}, "RampUpCosts-Mfg_ManufacturedMaterial": {"id": "RampUpCosts-Mfg_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_RampUpCosts-Mfg_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_RampUpCosts-Mfg_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_RampUpCosts-Mfg"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_RampUpCosts-Mfg"}]}, "RampUpCosts_ManufacturedMaterial": {"id": "RampUpCosts_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Ramp up", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_RampUpCosts_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_RampUpCosts"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_RampUpCosts"}], "rows": ["RampUpCosts-Rm_ManufacturedMaterial", "RampUpCosts-Pp_ManufacturedMaterial", "RampUpCosts-Mfg_ManufacturedMaterial"], "isCollapsed": true}, "PackagingAndCarrierCosts-Rm_ManufacturedMaterial": {"id": "PackagingAndCarrierCosts-Rm_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Raw material", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_PackagingAndCarrierCosts-Rm_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_PackagingAndCarrierCosts-Rm_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_PackagingAndCarrierCosts-Rm"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_PackagingAndCarrierCosts-Rm"}]}, "PackagingAndCarrierCosts-Pp_ManufacturedMaterial": {"id": "PackagingAndCarrierCosts-Pp_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Purchased part", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_PackagingAndCarrierCosts-Pp_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_PackagingAndCarrierCosts-Pp_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_PackagingAndCarrierCosts-Pp"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_PackagingAndCarrierCosts-Pp"}]}, "PackagingAndCarrierCosts-Mfg_ManufacturedMaterial": {"id": "PackagingAndCarrierCosts-Mfg_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_PackagingAndCarrierCosts-Mfg_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_PackagingAndCarrierCosts-Mfg_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_PackagingAndCarrierCosts-Mfg"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_PackagingAndCarrierCosts-Mfg"}]}, "PackagingAndCarrierCosts_ManufacturedMaterial": {"id": "PackagingAndCarrierCosts_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Packaging and carrier", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_PackagingAndCarrierCosts_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_PackagingAndCarrierCosts"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_PackagingAndCarrierCosts"}], "rows": ["PackagingAndCarrierCosts-Rm_ManufacturedMaterial", "PackagingAndCarrierCosts-Pp_ManufacturedMaterial", "PackagingAndCarrierCosts-Mfg_ManufacturedMaterial"], "isCollapsed": true}, "SpecialDirectCosts_ManufacturedMaterial": {"id": "SpecialDirectCosts_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Special direct cost", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_SpecialDirectCosts_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_SpecialDirectCosts"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_SpecialDirectCosts"}], "rows": ["DevelopmentCosts_ManufacturedMaterial", "RampUpCosts_ManufacturedMaterial", "PackagingAndCarrierCosts_ManufacturedMaterial"]}, "SalesAndGeneralAdministrationCosts-Rm_ManufacturedMaterial": {"id": "SalesAndGeneralAdministrationCosts-Rm_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Raw material", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_SalesAndGeneralAdministrationCosts-Rm_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_SalesAndGeneralAdministrationCosts-Rm_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_SalesAndGeneralAdministrationCosts-Rm"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_SalesAndGeneralAdministrationCosts-Rm"}]}, "SalesAndGeneralAdministrationCosts-Pp_ManufacturedMaterial": {"id": "SalesAndGeneralAdministrationCosts-Pp_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Purchased part", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_SalesAndGeneralAdministrationCosts-Pp_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_SalesAndGeneralAdministrationCosts-Pp_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_SalesAndGeneralAdministrationCosts-Pp"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_SalesAndGeneralAdministrationCosts-Pp"}]}, "SalesAndGeneralAdministrationCosts-Mfg_ManufacturedMaterial": {"id": "SalesAndGeneralAdministrationCosts-Mfg_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_SalesAndGeneralAdministrationCosts-Mfg_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_SalesAndGeneralAdministrationCosts-Mfg_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_SalesAndGeneralAdministrationCosts-Mfg"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_SalesAndGeneralAdministrationCosts-Mfg"}]}, "SalesAndGeneralAdministrationCosts_ManufacturedMaterial": {"id": "SalesAndGeneralAdministrationCosts_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Sales, general and administration", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_SalesAndGeneralAdministrationCosts_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_SalesAndGeneralAdministrationCosts"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_SalesAndGeneralAdministrationCosts"}], "rows": ["SalesAndGeneralAdministrationCosts-Rm_ManufacturedMaterial", "SalesAndGeneralAdministrationCosts-Pp_ManufacturedMaterial", "SalesAndGeneralAdministrationCosts-Mfg_ManufacturedMaterial"], "isCollapsed": true}, "ResearchAndDevelopmentCosts-Rm_ManufacturedMaterial": {"id": "ResearchAndDevelopmentCosts-Rm_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Raw material", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_ResearchAndDevelopmentCosts-Rm_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ResearchAndDevelopmentCosts-Rm_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_ResearchAndDevelopmentCosts-Rm"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_ResearchAndDevelopmentCosts-Rm"}]}, "ResearchAndDevelopmentCosts-Pp_ManufacturedMaterial": {"id": "ResearchAndDevelopmentCosts-Pp_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Purchased part", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_ResearchAndDevelopmentCosts-Pp_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ResearchAndDevelopmentCosts-Pp_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_ResearchAndDevelopmentCosts-Pp"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_ResearchAndDevelopmentCosts-Pp"}]}, "ResearchAndDevelopmentCosts-Mfg_ManufacturedMaterial": {"id": "ResearchAndDevelopmentCosts-Mfg_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_ResearchAndDevelopmentCosts-Mfg_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ResearchAndDevelopmentCosts-Mfg_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_ResearchAndDevelopmentCosts-Mfg"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_ResearchAndDevelopmentCosts-Mfg"}]}, "ResearchAndDevelopmentCosts_ManufacturedMaterial": {"id": "ResearchAndDevelopmentCosts_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Research and development", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_ResearchAndDevelopmentCosts_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_ResearchAndDevelopmentCosts"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_ResearchAndDevelopmentCosts"}], "rows": ["ResearchAndDevelopmentCosts-Rm_ManufacturedMaterial", "ResearchAndDevelopmentCosts-Pp_ManufacturedMaterial", "ResearchAndDevelopmentCosts-Mfg_ManufacturedMaterial"], "isCollapsed": true}, "BusinessRiskCosts-Rm_ManufacturedMaterial": {"id": "BusinessRiskCosts-Rm_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Raw material", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_BusinessRiskCosts-Rm_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_BusinessRiskCosts-Rm_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_BusinessRiskCosts-Rm"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_BusinessRiskCosts-Rm"}]}, "BusinessRiskCosts-Pp_ManufacturedMaterial": {"id": "BusinessRiskCosts-Pp_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Purchased part", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_BusinessRiskCosts-Pp_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_BusinessRiskCosts-Pp_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_BusinessRiskCosts-Pp"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_BusinessRiskCosts-Pp"}]}, "BusinessRiskCosts-Mfg_ManufacturedMaterial": {"id": "BusinessRiskCosts-Mfg_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_BusinessRiskCosts-Mfg_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_BusinessRiskCosts-Mfg_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_BusinessRiskCosts-Mfg"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_BusinessRiskCosts-Mfg"}]}, "BusinessRiskCosts_ManufacturedMaterial": {"id": "BusinessRiskCosts_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Business risk", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_BusinessRiskCosts_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_BusinessRiskCosts"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_BusinessRiskCosts"}], "rows": ["BusinessRiskCosts-Rm_ManufacturedMaterial", "BusinessRiskCosts-Pp_ManufacturedMaterial", "BusinessRiskCosts-Mfg_ManufacturedMaterial"], "isCollapsed": true}, "InterestOnFinishProductStock_ManufacturedMaterial": {"id": "InterestOnFinishProductStock_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Interest on finished product stock", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_InterestOnFinishProductStock_Base"}, {"type": "lookup", "columnId": "interestPeriod", "collectFrom": {"type": "self"}, "fieldName": "#Cost_InterestOnFinishProductStock_Time"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_InterestOnFinishProductStock_InterestRate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_InterestOnFinishProductStock"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_InterestOnFinishProductStock"}]}, "OtherExpendituresAfterPC-Rm_ManufacturedMaterial": {"id": "OtherExpendituresAfterPC-Rm_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Raw material", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_OtherExpendituresAfterPC-Rm_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_OtherExpendituresAfterPC-Rm_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_OtherExpendituresAfterPC-Rm"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_OtherExpendituresAfterPC-Rm"}]}, "OtherExpendituresAfterPC-Pp_ManufacturedMaterial": {"id": "OtherExpendituresAfterPC-Pp_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Purchased part", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_OtherExpendituresAfterPC-Pp_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_OtherExpendituresAfterPC-Pp_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_OtherExpendituresAfterPC-Pp"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_OtherExpendituresAfterPC-Pp"}]}, "OtherExpendituresAfterPC-Mfg_ManufacturedMaterial": {"id": "OtherExpendituresAfterPC-Mfg_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_OtherExpendituresAfterPC-Mfg_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_OtherExpendituresAfterPC-Mfg_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_OtherExpendituresAfterPC-Mfg"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_OtherExpendituresAfterPC-Mfg"}]}, "OtherExpendituresAfterPC_ManufacturedMaterial": {"id": "OtherExpendituresAfterPC_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Other expenditures after production", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_OtherExpendituresAfterPC_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_OtherExpendituresAfterPC"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_OtherExpendituresAfterPC"}], "rows": ["OtherExpendituresAfterPC-Rm_ManufacturedMaterial", "OtherExpendituresAfterPC-Pp_ManufacturedMaterial", "OtherExpendituresAfterPC-Mfg_ManufacturedMaterial"], "isCollapsed": true}, "DirectOverheadsAfterPC-Rm_ManufacturedMaterial": {"id": "DirectOverheadsAfterPC-Rm_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Raw material", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_DirectOverheadsAfterPC-Rm_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_DirectOverheadsAfterPC-Rm_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_DirectOverheadsAfterPC-Rm"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_DirectOverheadsAfterPC-Rm"}]}, "DirectOverheadsAfterPC-Pp_ManufacturedMaterial": {"id": "DirectOverheadsAfterPC-Pp_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Purchased part", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_DirectOverheadsAfterPC-Pp_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_DirectOverheadsAfterPC-Pp_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_DirectOverheadsAfterPC-Pp"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_DirectOverheadsAfterPC-Pp"}]}, "DirectOverheadsAfterPC-Mfg_ManufacturedMaterial": {"id": "DirectOverheadsAfterPC-Mfg_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_DirectOverheadsAfterPC-Mfg_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_DirectOverheadsAfterPC-Mfg_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_DirectOverheadsAfterPC-Mfg"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_DirectOverheadsAfterPC-Mfg"}]}, "DirectOverheadsAfterPC_ManufacturedMaterial": {"id": "DirectOverheadsAfterPC_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Direct overheads after production", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_DirectOverheadsAfterPC_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_DirectOverheadsAfterPC"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_DirectOverheadsAfterPC"}], "rows": ["DirectOverheadsAfterPC-Rm_ManufacturedMaterial", "DirectOverheadsAfterPC-Pp_ManufacturedMaterial", "DirectOverheadsAfterPC-Mfg_ManufacturedMaterial"], "isCollapsed": true}, "OverheadsAfterPC_ManufacturedMaterial": {"id": "OverheadsAfterPC_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Overheads after production", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_OverheadsAfterPC_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_OverheadsAfterPC"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_OverheadsAfterPC"}], "rows": ["SalesAndGeneralAdministrationCosts_ManufacturedMaterial", "ResearchAndDevelopmentCosts_ManufacturedMaterial", "BusinessRiskCosts_ManufacturedMaterial", "InterestOnFinishProductStock_ManufacturedMaterial", "OtherExpendituresAfterPC_ManufacturedMaterial", "DirectOverheadsAfterPC_ManufacturedMaterial"]}, "Profit-Rm_ManufacturedMaterial": {"id": "Profit-Rm_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Raw material", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_Profit-Rm_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_Profit-Rm_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_Profit-Rm"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_Profit-Rm"}]}, "Profit-Pp_ManufacturedMaterial": {"id": "Profit-Pp_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Purchased part", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_Profit-Pp_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_Profit-Pp_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_Profit-Pp"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_Profit-Pp"}]}, "Profit-Mfg_ManufacturedMaterial": {"id": "Profit-Mfg_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_Profit-Mfg_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_Profit-Mfg_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_Profit-Mfg"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_Profit-Mfg"}]}, "Profit_ManufacturedMaterial": {"id": "Profit_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Profit", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_Profit_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_Profit"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_Profit"}], "rows": ["Profit-Rm_ManufacturedMaterial", "Profit-Pp_ManufacturedMaterial", "Profit-Mfg_ManufacturedMaterial"], "isCollapsed": true}, "InterestForTermsOfPayment_ManufacturedMaterial": {"id": "InterestForTermsOfPayment_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Interest for terms of payment", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_InterestForTermsOfPayment_Base"}, {"type": "lookup", "columnId": "interestPeriod", "collectFrom": {"type": "self"}, "fieldName": "#Cost_InterestForTermsOfPayment_Time"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_InterestForTermsOfPayment_InterestRate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_InterestForTermsOfPayment"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_InterestForTermsOfPayment"}]}, "Discount_ManufacturedMaterial": {"id": "Discount_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Discount", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_Discount_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_Discount_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_Discount"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_Discount"}]}, "TermsOfPayment_ManufacturedMaterial": {"id": "TermsOfPayment_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Terms of payment", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_TermsOfPayment_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_TermsOfPayment"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_TermsOfPayment"}], "rows": ["InterestForTermsOfPayment_ManufacturedMaterial", "Discount_ManufacturedMaterial"]}, "TransportCosts_ManufacturedMaterial": {"id": "TransportCosts_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Transport", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "subCalculatorUsage", "collectFrom": {"criteria": [{"operator": "eq", "key": "ref", "value": "TRANSPORT_CALCULATOR"}], "type": "child"}, "fieldName": "useTransportCalculator"}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_TransportCosts_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_TransportCosts_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_TransportCosts"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_TransportCosts"}], "navigateToEntityLocator": {"criteria": [{"operator": "eq", "key": "ref", "value": "TRANSPORT_CALCULATOR"}, {"operator": "list", "key": "fields", "value": [{"operator": "eq", "key": "name", "value": "useTransportCalculator"}, {"operator": "eq", "key": "value", "value": true}]}], "type": "child"}}, "CustomsDuty_ManufacturedMaterial": {"id": "CustomsDuty_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Customs duty", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_CustomsDuty_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_CustomsDuty_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_CustomsDuty"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_CustomsDuty"}]}, "IncoTerms_ManufacturedMaterial": {"id": "IncoTerms_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Incoterms", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_IncoTerms_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_InHouse_IncoTerms"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturedMaterial_Total_IncoTerms"}], "rows": ["TransportCosts_ManufacturedMaterial", "CustomsDuty_ManufacturedMaterial"]}}, "columns": [{"id": "display", "options": {"mainColumn": true, "editable": false, "displayDesignation": "displayDesignation", "widthGrow": 3}}, {"id": "subCalculatorUsage", "options": {"displayDesignation": "Usage of sub calculator result", "widthGrow": 2}}, {"id": "base", "options": {"displayDesignation": "Base", "widthGrow": 1}}, {"id": "interestPeriod", "options": {"displayDesignation": "Interest period", "widthGrow": 1}}, {"id": "rate", "options": {"displayDesignation": "Rate", "widthGrow": 1}}, {"id": "inHouseSubparts", "options": {"hasTotal": true, "displayDesignation": "Cost in-house subparts", "widthGrow": 1}}, {"id": "value", "options": {"hasTotal": true, "displayDesignation": "Cost", "widthGrow": 1}}]}, "overhead_co2_Production": {"type": "field", "rows": ["SpecialDirectCO2e_ManufacturedMaterial", "OverheadsCO2eAfterP_ManufacturedMaterial", "ProfitCO2e_ManufacturedMaterial", "TermsOfPaymentCO2e_ManufacturedMaterial", "IncoTermsCO2e_ManufacturedMaterial"], "rowDefinitions": {"DevelopmentCO2e-Rm_ManufacturedMaterial": {"id": "DevelopmentCO2e-Rm_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Raw material", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_DevelopmentCO2e-Rm_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_DevelopmentCO2e-Rm_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_DevelopmentCO2e-Rm"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_DevelopmentCO2e-Rm"}]}, "DevelopmentCO2e-Pp_ManufacturedMaterial": {"id": "DevelopmentCO2e-Pp_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Purchased part", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_DevelopmentCO2e-Pp_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_DevelopmentCO2e-Pp_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_DevelopmentCO2e-Pp"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_DevelopmentCO2e-Pp"}]}, "DevelopmentCO2e-Mfg_ManufacturedMaterial": {"id": "DevelopmentCO2e-Mfg_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_DevelopmentCO2e-Mfg_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_DevelopmentCO2e-Mfg_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_DevelopmentCO2e-Mfg"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_DevelopmentCO2e-Mfg"}]}, "DevelopmentCO2e_ManufacturedMaterial": {"id": "DevelopmentCO2e_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Development", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_DevelopmentCO2e_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_DevelopmentCO2e"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_DevelopmentCO2e"}], "rows": ["DevelopmentCO2e-Rm_ManufacturedMaterial", "DevelopmentCO2e-Pp_ManufacturedMaterial", "DevelopmentCO2e-Mfg_ManufacturedMaterial"], "isCollapsed": true}, "RampUpCO2e-Rm_ManufacturedMaterial": {"id": "RampUpCO2e-Rm_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Raw material", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_RampUpCO2e-Rm_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_RampUpCO2e-Rm_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_RampUpCO2e-Rm"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_RampUpCO2e-Rm"}]}, "RampUpCO2e-Pp_ManufacturedMaterial": {"id": "RampUpCO2e-Pp_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Purchased part", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_RampUpCO2e-Pp_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_RampUpCO2e-Pp_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_RampUpCO2e-Pp"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_RampUpCO2e-Pp"}]}, "RampUpCO2e-Mfg_ManufacturedMaterial": {"id": "RampUpCO2e-Mfg_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_RampUpCO2e-Mfg_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_RampUpCO2e-Mfg_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_RampUpCO2e-Mfg"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_RampUpCO2e-Mfg"}]}, "RampUpCO2e_ManufacturedMaterial": {"id": "RampUpCO2e_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Ramp up", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_RampUpCO2e_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_RampUpCO2e"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_RampUpCO2e"}], "rows": ["RampUpCO2e-Rm_ManufacturedMaterial", "RampUpCO2e-Pp_ManufacturedMaterial", "RampUpCO2e-Mfg_ManufacturedMaterial"], "isCollapsed": true}, "PackagingAndCarrierCO2e-Rm_ManufacturedMaterial": {"id": "PackagingAndCarrierCO2e-Rm_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Raw material", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_PackagingAndCarrierCO2e-Rm_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_PackagingAndCarrierCO2e-Rm_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_PackagingAndCarrierCO2e-Rm"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_PackagingAndCarrierCO2e-Rm"}]}, "PackagingAndCarrierCO2e-Pp_ManufacturedMaterial": {"id": "PackagingAndCarrierCO2e-Pp_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Purchased part", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_PackagingAndCarrierCO2e-Pp_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_PackagingAndCarrierCO2e-Pp_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_PackagingAndCarrierCO2e-Pp"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_PackagingAndCarrierCO2e-Pp"}]}, "PackagingAndCarrierCO2e-Mfg_ManufacturedMaterial": {"id": "PackagingAndCarrierCO2e-Mfg_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_PackagingAndCarrierCO2e-Mfg_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_PackagingAndCarrierCO2e-Mfg_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_PackagingAndCarrierCO2e-Mfg"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_PackagingAndCarrierCO2e-Mfg"}]}, "PackagingAndCarrierCO2e_ManufacturedMaterial": {"id": "PackagingAndCarrierCO2e_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Packaging and carrier", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_PackagingAndCarrierCO2e_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_PackagingAndCarrierCO2e"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_PackagingAndCarrierCO2e"}], "rows": ["PackagingAndCarrierCO2e-Rm_ManufacturedMaterial", "PackagingAndCarrierCO2e-Pp_ManufacturedMaterial", "PackagingAndCarrierCO2e-Mfg_ManufacturedMaterial"], "isCollapsed": true}, "SpecialDirectCO2e_ManufacturedMaterial": {"id": "SpecialDirectCO2e_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Special direct emission", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_SpecialDirectCO2e_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_SpecialDirectCO2e"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_SpecialDirectCO2e"}], "rows": ["DevelopmentCO2e_ManufacturedMaterial", "RampUpCO2e_ManufacturedMaterial", "PackagingAndCarrierCO2e_ManufacturedMaterial"]}, "SalesAndGeneralAdministrationCO2e-Rm_ManufacturedMaterial": {"id": "SalesAndGeneralAdministrationCO2e-Rm_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Raw material", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_SalesAndGeneralAdministrationCO2e-Rm_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_SalesAndGeneralAdministrationCO2e-Rm_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_SalesAndGeneralAdministrationCO2e-Rm"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_SalesAndGeneralAdministrationCO2e-Rm"}]}, "SalesAndGeneralAdministrationCO2e-Pp_ManufacturedMaterial": {"id": "SalesAndGeneralAdministrationCO2e-Pp_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Purchased part", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_SalesAndGeneralAdministrationCO2e-Pp_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_SalesAndGeneralAdministrationCO2e-Pp_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_SalesAndGeneralAdministrationCO2e-Pp"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_SalesAndGeneralAdministrationCO2e-Pp"}]}, "SalesAndGeneralAdministrationCO2e-Mfg_ManufacturedMaterial": {"id": "SalesAndGeneralAdministrationCO2e-Mfg_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_SalesAndGeneralAdministrationCO2e-Mfg_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_SalesAndGeneralAdministrationCO2e-Mfg_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_SalesAndGeneralAdministrationCO2e-Mfg"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_SalesAndGeneralAdministrationCO2e-Mfg"}]}, "SalesAndGeneralAdministrationCO2e_ManufacturedMaterial": {"id": "SalesAndGeneralAdministrationCO2e_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Sales, general and administration", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_SalesAndGeneralAdministrationCO2e_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_SalesAndGeneralAdministrationCO2e"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_SalesAndGeneralAdministrationCO2e"}], "rows": ["SalesAndGeneralAdministrationCO2e-Rm_ManufacturedMaterial", "SalesAndGeneralAdministrationCO2e-Pp_ManufacturedMaterial", "SalesAndGeneralAdministrationCO2e-Mfg_ManufacturedMaterial"], "isCollapsed": true}, "ResearchAndDevelopmentCO2e-Rm_ManufacturedMaterial": {"id": "ResearchAndDevelopmentCO2e-Rm_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Raw material", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_ResearchAndDevelopmentCO2e-Rm_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ResearchAndDevelopmentCO2e-Rm_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_ResearchAndDevelopmentCO2e-Rm"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_ResearchAndDevelopmentCO2e-Rm"}]}, "ResearchAndDevelopmentCO2e-Pp_ManufacturedMaterial": {"id": "ResearchAndDevelopmentCO2e-Pp_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Purchased part", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_ResearchAndDevelopmentCO2e-Pp_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ResearchAndDevelopmentCO2e-Pp_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_ResearchAndDevelopmentCO2e-Pp"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_ResearchAndDevelopmentCO2e-Pp"}]}, "ResearchAndDevelopmentCO2e-Mfg_ManufacturedMaterial": {"id": "ResearchAndDevelopmentCO2e-Mfg_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_ResearchAndDevelopmentCO2e-Mfg_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ResearchAndDevelopmentCO2e-Mfg_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_ResearchAndDevelopmentCO2e-Mfg"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_ResearchAndDevelopmentCO2e-Mfg"}]}, "ResearchAndDevelopmentCO2e_ManufacturedMaterial": {"id": "ResearchAndDevelopmentCO2e_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Research and development", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_ResearchAndDevelopmentCO2e_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_ResearchAndDevelopmentCO2e"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_ResearchAndDevelopmentCO2e"}], "rows": ["ResearchAndDevelopmentCO2e-Rm_ManufacturedMaterial", "ResearchAndDevelopmentCO2e-Pp_ManufacturedMaterial", "ResearchAndDevelopmentCO2e-Mfg_ManufacturedMaterial"], "isCollapsed": true}, "BusinessRiskCO2e-Rm_ManufacturedMaterial": {"id": "BusinessRiskCO2e-Rm_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Raw material", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_BusinessRiskCO2e-Rm_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_BusinessRiskCO2e-Rm_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_BusinessRiskCO2e-Rm"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_BusinessRiskCO2e-Rm"}]}, "BusinessRiskCO2e-Pp_ManufacturedMaterial": {"id": "BusinessRiskCO2e-Pp_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Purchased part", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_BusinessRiskCO2e-Pp_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_BusinessRiskCO2e-Pp_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_BusinessRiskCO2e-Pp"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_BusinessRiskCO2e-Pp"}]}, "BusinessRiskCO2e-Mfg_ManufacturedMaterial": {"id": "BusinessRiskCO2e-Mfg_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_BusinessRiskCO2e-Mfg_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_BusinessRiskCO2e-Mfg_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_BusinessRiskCO2e-Mfg"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_BusinessRiskCO2e-Mfg"}]}, "BusinessRiskCO2e_ManufacturedMaterial": {"id": "BusinessRiskCO2e_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Business risk", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_BusinessRiskCO2e_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_BusinessRiskCO2e"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_BusinessRiskCO2e"}], "rows": ["BusinessRiskCO2e-Rm_ManufacturedMaterial", "BusinessRiskCO2e-Pp_ManufacturedMaterial", "BusinessRiskCO2e-Mfg_ManufacturedMaterial"], "isCollapsed": true}, "OtherExpendituresCO2eAfterP-Rm_ManufacturedMaterial": {"id": "OtherExpendituresCO2eAfterP-Rm_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Raw material", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_OtherExpendituresCO2eAfterP-Rm_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_OtherExpendituresCO2eAfterP-Rm_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_OtherExpendituresCO2eAfterP-Rm"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_OtherExpendituresCO2eAfterP-Rm"}]}, "OtherExpendituresCO2eAfterP-Pp_ManufacturedMaterial": {"id": "OtherExpendituresCO2eAfterP-Pp_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Purchased part", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_OtherExpendituresCO2eAfterP-Pp_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_OtherExpendituresCO2eAfterP-Pp_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_OtherExpendituresCO2eAfterP-Pp"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_OtherExpendituresCO2eAfterP-Pp"}]}, "OtherExpendituresCO2eAfterP-Mfg_ManufacturedMaterial": {"id": "OtherExpendituresCO2eAfterP-Mfg_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_OtherExpendituresCO2eAfterP-Mfg_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_OtherExpendituresCO2eAfterP-Mfg_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_OtherExpendituresCO2eAfterP-Mfg"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_OtherExpendituresCO2eAfterP-Mfg"}]}, "OtherExpendituresCO2eAfterP_ManufacturedMaterial": {"id": "OtherExpendituresCO2eAfterP_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Other expenditures after production", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_OtherExpendituresCO2eAfterP_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_OtherExpendituresCO2eAfterP"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_OtherExpendituresCO2eAfterP"}], "rows": ["OtherExpendituresCO2eAfterP-Rm_ManufacturedMaterial", "OtherExpendituresCO2eAfterP-Pp_ManufacturedMaterial", "OtherExpendituresCO2eAfterP-Mfg_ManufacturedMaterial"], "isCollapsed": true}, "DirectOverheadsCO2eAfterP-Rm_ManufacturedMaterial": {"id": "DirectOverheadsCO2eAfterP-Rm_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Raw material", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_DirectOverheadsCO2eAfterP-Rm_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_DirectOverheadsCO2eAfterP-Rm_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_DirectOverheadsCO2eAfterP-Rm"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_DirectOverheadsCO2eAfterP-Rm"}]}, "DirectOverheadsCO2eAfterP-Pp_ManufacturedMaterial": {"id": "DirectOverheadsCO2eAfterP-Pp_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Purchased part", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_DirectOverheadsCO2eAfterP-Pp_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_DirectOverheadsCO2eAfterP-Pp_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_DirectOverheadsCO2eAfterP-Pp"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_DirectOverheadsCO2eAfterP-Pp"}]}, "DirectOverheadsCO2eAfterP-Mfg_ManufacturedMaterial": {"id": "DirectOverheadsCO2eAfterP-Mfg_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_DirectOverheadsCO2eAfterP-Mfg_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_DirectOverheadsCO2eAfterP-Mfg_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_DirectOverheadsCO2eAfterP-Mfg"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_DirectOverheadsCO2eAfterP-Mfg"}]}, "DirectOverheadsCO2eAfterP_ManufacturedMaterial": {"id": "DirectOverheadsCO2eAfterP_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Direct overheads after production", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_DirectOverheadsCO2eAfterP_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_DirectOverheadsCO2eAfterP"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_DirectOverheadsCO2eAfterP"}], "rows": ["DirectOverheadsCO2eAfterP-Rm_ManufacturedMaterial", "DirectOverheadsCO2eAfterP-Pp_ManufacturedMaterial", "DirectOverheadsCO2eAfterP-Mfg_ManufacturedMaterial"], "isCollapsed": true}, "OverheadsCO2eAfterP_ManufacturedMaterial": {"id": "OverheadsCO2eAfterP_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Overheads after production", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_OverheadsCO2eAfterP_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_OverheadsCO2eAfterP"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_OverheadsCO2eAfterP"}], "rows": ["SalesAndGeneralAdministrationCO2e_ManufacturedMaterial", "ResearchAndDevelopmentCO2e_ManufacturedMaterial", "BusinessRiskCO2e_ManufacturedMaterial", "OtherExpendituresCO2eAfterP_ManufacturedMaterial", "DirectOverheadsCO2eAfterP_ManufacturedMaterial"]}, "ProfitCO2e-Rm_ManufacturedMaterial": {"id": "ProfitCO2e-Rm_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Raw material", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_ProfitCO2e-Rm_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ProfitCO2e-Rm_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_ProfitCO2e-Rm"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_ProfitCO2e-Rm"}]}, "ProfitCO2e-Pp_ManufacturedMaterial": {"id": "ProfitCO2e-Pp_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Purchased part", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_ProfitCO2e-Pp_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ProfitCO2e-Pp_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_ProfitCO2e-Pp"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_ProfitCO2e-Pp"}]}, "ProfitCO2e-Mfg_ManufacturedMaterial": {"id": "ProfitCO2e-Mfg_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_ProfitCO2e-Mfg_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ProfitCO2e-Mfg_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_ProfitCO2e-Mfg"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_ProfitCO2e-Mfg"}]}, "ProfitCO2e_ManufacturedMaterial": {"id": "ProfitCO2e_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Profit", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_ProfitCO2e_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_ProfitCO2e"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_ProfitCO2e"}], "rows": ["ProfitCO2e-Rm_ManufacturedMaterial", "ProfitCO2e-Pp_ManufacturedMaterial", "ProfitCO2e-Mfg_ManufacturedMaterial"], "isCollapsed": true}, "DiscountCO2e_ManufacturedMaterial": {"id": "DiscountCO2e_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Discount", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_DiscountCO2e_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_DiscountCO2e_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_DiscountCO2e"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_DiscountCO2e"}]}, "TermsOfPaymentCO2e_ManufacturedMaterial": {"id": "TermsOfPaymentCO2e_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Terms of payment", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_TermsOfPaymentCO2e_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_TermsOfPaymentCO2e"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_TermsOfPaymentCO2e"}], "rows": ["DiscountCO2e_ManufacturedMaterial"]}, "TransportCO2e_ManufacturedMaterial": {"id": "TransportCO2e_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Transport", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "subCalculatorUsage", "collectFrom": {"criteria": [{"operator": "eq", "key": "ref", "value": "TRANSPORT_CALCULATOR"}], "type": "child"}, "fieldName": "useTransportCalculator"}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_TransportCO2e_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_TransportCO2e_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_TransportCO2e"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_TransportCO2e"}], "navigateToEntityLocator": {"criteria": [{"operator": "eq", "key": "ref", "value": "TRANSPORT_CALCULATOR"}, {"operator": "list", "key": "fields", "value": [{"operator": "eq", "key": "name", "value": "useTransportCalculator"}, {"operator": "eq", "key": "value", "value": true}]}], "type": "child"}}, "CustomsDutyCO2e_ManufacturedMaterial": {"id": "CustomsDutyCO2e_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Customs duty", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_CustomsDutyCO2e_Base"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_CustomsDutyCO2e_Rate"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_CustomsDutyCO2e"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_CustomsDutyCO2e"}]}, "IncoTermsCO2e_ManufacturedMaterial": {"id": "IncoTermsCO2e_ManufacturedMaterial", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Incoterms", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "base", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_IncoTermsCO2e_Base"}, {"type": "lookup", "columnId": "inHouseSubparts", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_InHouse_IncoTermsCO2e"}, {"type": "lookup", "columnId": "value", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturedMaterial_Total_IncoTermsCO2e"}], "rows": ["TransportCO2e_ManufacturedMaterial", "CustomsDutyCO2e_ManufacturedMaterial"]}}, "columns": [{"id": "display", "options": {"mainColumn": true, "editable": false, "displayDesignation": "displayDesignation", "widthGrow": 3}}, {"id": "subCalculatorUsage", "options": {"displayDesignation": "Usage of sub calculator result", "widthGrow": 2}}, {"id": "base", "options": {"displayDesignation": "Base", "widthGrow": 1}}, {"id": "rate", "options": {"displayDesignation": "Rate", "widthGrow": 1}}, {"id": "inHouseSubparts", "options": {"hasTotal": true, "displayDesignation": "Emission in-house subparts", "widthGrow": 1}}, {"id": "value", "options": {"hasTotal": true, "displayDesignation": "Emission", "widthGrow": 1}}]}}}