{"key": {"identifiers": {"COST_OPERATION_ID": {"key": "1", "version": {"major": 1, "minor": 0}}, "CO2_OPERATION_ID": {"key": "1", "version": {"major": 1, "minor": 0}}, "PROCUREMENT_TYPE_ID": {"procurementTypeResult": "PURCHASE"}}}, "cards": {"rmocScrap": {"views": ["cost", "co2"], "title": {"cost": "Breakdown", "co2": "Breakdown"}, "kpi": {"cost": "#Cost_ManufacturingStep_This_ManufacturingCosts3", "co2": "#CO2_ManufacturingStep_This_ManufacturingCO2e"}, "tableVariations": {"co2": ["Production", "Activity"], "cost": ["Production", "Activity"]}}}, "tableConfigs": {"rmocScrap_co2_Production": {"type": "field", "rows": ["DirectManufacturingCO2e_ManufacturingStep", "ManufacturingScrapCO2e_ManufacturingStep", "ManufacturingOverheadCO2e_ManufacturingStep", "ToolCO2e_Tool"], "rowDefinitions": {"MachineDepreciationCO2e_Machine": {"id": "MachineDepreciationCO2e_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Machine depreciation", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingStep_This_MachineDepreciationCO2e"}]}, "RoughMachineCO2e_Machine": {"id": "RoughMachineCO2e_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Rough machine", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingStep_This_RoughMachineCO2e"}]}, "MachineFixCO2e_Machine": {"id": "MachineFixCO2e_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Machine fix", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingStep_This_MachineFixCO2e"}], "rows": ["MachineDepreciationCO2e_Machine", "RoughMachineCO2e_Machine"], "isCollapsed": true}, "MachineElectricEnergyCO2e_Machine": {"id": "MachineElectricEnergyCO2e_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Machine electric energy", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingStep_This_MachineElectricEnergyCO2e"}]}, "MachineGasEnergyActiveCO2e_Machine": {"id": "MachineGasEnergyActiveCO2e_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Machine gas energy active", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingStep_This_MachineGasEnergyActiveCO2e"}]}, "MachineGasEnergyPassiveCO2e_Machine": {"id": "MachineGasEnergyPassiveCO2e_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Machine gas energy passive", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingStep_This_MachineGasEnergyPassiveCO2e"}]}, "MachineGasEnergyCO2e_Machine": {"id": "MachineGasEnergyCO2e_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Machine gas energy", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingStep_This_MachineGasEnergyCO2e"}], "rows": ["MachineGasEnergyActiveCO2e_Machine", "MachineGasEnergyPassiveCO2e_Machine"], "isCollapsed": true}, "MachineVariableCO2e_Machine": {"id": "MachineVariableCO2e_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Machine variable", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingStep_This_MachineVariableCO2e"}], "rows": ["MachineElectricEnergyCO2e_Machine", "MachineGasEnergyCO2e_Machine"], "isCollapsed": true}, "MachineCO2e_Machine": {"id": "MachineCO2e_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Machine", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingStep_This_MachineCO2e"}], "rows": ["MachineFixCO2e_Machine", "MachineVariableCO2e_Machine"], "isCollapsed": true}, "RoughProcessCO2e_RoughProcess": {"id": "RoughProcessCO2e_RoughProcess", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Rough manufacturing step", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingStep_This_RoughProcessCO2e"}]}, "DirectManufacturingCO2e_ManufacturingStep": {"id": "DirectManufacturingCO2e_ManufacturingStep", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Direct manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingStep_This_DirectManufacturingCO2e"}], "rows": ["MachineCO2e_Machine", "RoughProcessCO2e_RoughProcess"]}, "ManufacturingScrapCO2e_ManufacturingStep": {"id": "ManufacturingScrapCO2e_ManufacturingStep", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing scrap", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "manufacturingScrapRate"}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingStep_This_ManufacturingScrapCO2e"}]}, "ManufacturingOverheadCO2e_ManufacturingStep": {"id": "ManufacturingOverheadCO2e_ManufacturingStep", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing overhead", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingOverheadCO2e_Rate"}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingStep_This_ManufacturingOverheadCO2e"}]}, "ToolCO2e_Tool": {"id": "ToolCO2e_Tool", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Tool", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingStep_This_ToolCO2e"}]}}, "columns": [{"id": "display", "options": {"mainColumn": true, "editable": false, "displayDesignation": "displayDesignation", "widthGrow": 3}}, {"id": "rate", "options": {"displayDesignation": "Rate", "widthGrow": 1}}, {"id": "thisPartOnly", "options": {"hasTotal": true, "displayDesignation": "Emission", "widthGrow": 1}}]}, "rmocScrap_co2_Activity": {"type": "field", "rows": ["DirectManufacturingActivity_ManufacturingStep", "ManufacturingScrapCO2e_ManufacturingStep", "ManufacturingOverheadCO2e_ManufacturingStep", "ToolCO2e_Tool"], "rowDefinitions": {"OccupancyMachineActivity_Machine": {"id": "OccupancyMachineActivity_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Occupancy machine", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingStep_This_OccupancyMachineActivity"}]}, "NonOccupancyMachineActivity_Machine": {"id": "NonOccupancyMachineActivity_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Non-occupancy machine", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingStep_This_NonOccupancyMachineActivity"}]}, "ProductionMachineActivity_Machine": {"id": "ProductionMachineActivity_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Production machine", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingStep_This_ProductionMachineActivity"}], "rows": ["OccupancyMachineActivity_Machine", "NonOccupancyMachineActivity_Machine"], "isCollapsed": true}, "RoughProcessActivity_RoughProcess": {"id": "RoughProcessActivity_RoughProcess", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Rough manufacturing step", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingStep_This_RoughProcessActivity"}]}, "DirectManufacturingActivity_ManufacturingStep": {"id": "DirectManufacturingActivity_ManufacturingStep", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Direct manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingStep_This_DirectManufacturingActivity"}], "rows": ["ProductionMachineActivity_Machine", "RoughProcessActivity_RoughProcess"]}, "ManufacturingScrapCO2e_ManufacturingStep": {"id": "ManufacturingScrapCO2e_ManufacturingStep", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing scrap", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "manufacturingScrapRate"}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingStep_This_ManufacturingScrapCO2e"}]}, "ManufacturingOverheadCO2e_ManufacturingStep": {"id": "ManufacturingOverheadCO2e_ManufacturingStep", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing overhead", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingOverheadCO2e_Rate"}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingStep_This_ManufacturingOverheadCO2e"}]}, "ToolCO2e_Tool": {"id": "ToolCO2e_Tool", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Tool", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#CO2_ManufacturingStep_This_ToolCO2e"}]}}, "columns": [{"id": "display", "options": {"mainColumn": true, "editable": false, "displayDesignation": "displayDesignation", "widthGrow": 3}}, {"id": "rate", "options": {"displayDesignation": "Rate", "widthGrow": 1}}, {"id": "thisPartOnly", "options": {"hasTotal": true, "displayDesignation": "Emission", "widthGrow": 1}}]}, "rmocScrap_cost_Production": {"type": "field", "rows": ["ManufacturingCosts2_ManufacturingStep", "ManufacturingInterestCosts_ManufacturingStep"], "rowDefinitions": {"LaborCosts_Labor": {"id": "LaborCosts_Labor", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Labor", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_LaborCosts"}]}, "MachineDepreciationCosts_Machine": {"id": "MachineDepreciationCosts_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Machine depreciation", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_MachineDepreciationCosts"}]}, "MachineInterestCosts_Machine": {"id": "MachineInterestCosts_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Machine interest", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_MachineInterestCosts"}]}, "MachineAreaCosts_Machine": {"id": "MachineAreaCosts_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Machine area", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_MachineAreaCosts"}]}, "RoughMachineCosts_Machine": {"id": "RoughMachineCosts_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Rough machine", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_RoughMachineCosts"}]}, "MachineFixCosts_Machine": {"id": "MachineFixCosts_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Machine fix", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_MachineFixCosts"}], "rows": ["MachineDepreciationCosts_Machine", "MachineInterestCosts_Machine", "MachineAreaCosts_Machine", "RoughMachineCosts_Machine"]}, "MachineEnergyCosts_Machine": {"id": "MachineEnergyCosts_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Machine energy", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_MachineEnergyCosts"}]}, "MachineMaintenanceCosts_Machine": {"id": "MachineMaintenanceCosts_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Machine maintenance", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_MachineMaintenanceCosts"}]}, "MachineOperationSupplyCosts_Machine": {"id": "MachineOperationSupplyCosts_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Machine operation supply", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_MachineOperationSupplyCosts"}]}, "MachineVariableCosts_Machine": {"id": "MachineVariableCosts_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Machine variable", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_MachineVariableCosts"}], "rows": ["MachineEnergyCosts_Machine", "MachineMaintenanceCosts_Machine", "MachineOperationSupplyCosts_Machine"], "isCollapsed": true}, "MachineCosts_Machine": {"id": "MachineCosts_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Machine", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_MachineCosts"}], "rows": ["MachineFixCosts_Machine", "MachineVariableCosts_Machine"], "isCollapsed": true}, "ToolMaintenanceCosts_Tool": {"id": "ToolMaintenanceCosts_Tool", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Tool maintenance", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_ToolMaintenanceCosts"}]}, "RoughProcessCosts_RoughProcess": {"id": "RoughProcessCosts_RoughProcess", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Rough manufacturing step", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_RoughProcessCosts"}]}, "DirectManufacturingCosts_ManufacturingStep": {"id": "DirectManufacturingCosts_ManufacturingStep", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Direct manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_DirectManufacturingCosts"}], "rows": ["LaborCosts_Labor", "MachineCosts_Machine", "ToolMaintenanceCosts_Tool", "RoughProcessCosts_RoughProcess"]}, "ManufacturingScrapCosts_ManufacturingStep": {"id": "ManufacturingScrapCosts_ManufacturingStep", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing scrap", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "manufacturingScrapRate"}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_ManufacturingScrapCosts"}]}, "ManufacturingOverheadCosts_ManufacturingStep": {"id": "ManufacturingOverheadCosts_ManufacturingStep", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing overhead", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingOverheadCosts_Rate"}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_ManufacturingOverheadCosts"}]}, "ToolAllocationCosts_Tool": {"id": "ToolAllocationCosts_Tool", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Tool allocation", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_ToolAllocationCosts"}]}, "ToolInterestCosts_Tool": {"id": "ToolInterestCosts_Tool", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Tool interest", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_ToolInterestCosts"}]}, "ManufacturingCostsAfterDirectManufacturingCosts_ManufacturingStep": {"id": "ManufacturingCostsAfterDirectManufacturingCosts_ManufacturingStep", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing after direct manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_ManufacturingCostsAfterDirectManufacturingCosts"}], "rows": ["ManufacturingScrapCosts_ManufacturingStep", "ManufacturingOverheadCosts_ManufacturingStep", "ToolAllocationCosts_Tool", "ToolInterestCosts_Tool"]}, "ManufacturingCosts2_ManufacturingStep": {"id": "ManufacturingCosts2_ManufacturingStep", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing 2", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_ManufacturingCosts2"}], "rows": ["DirectManufacturingCosts_ManufacturingStep", "ManufacturingCostsAfterDirectManufacturingCosts_ManufacturingStep"]}, "ManufacturingInterestCosts_ManufacturingStep": {"id": "ManufacturingInterestCosts_ManufacturingStep", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing interest", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "interestPeriod", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingInterestCosts_Time"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingInterestCosts_InterestRate"}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_ManufacturingInterestCosts"}]}}, "columns": [{"id": "display", "options": {"mainColumn": true, "editable": false, "displayDesignation": "displayDesignation", "widthGrow": 3}}, {"id": "interestPeriod", "options": {"displayDesignation": "Interest period", "widthGrow": 1}}, {"id": "rate", "options": {"displayDesignation": "Rate", "widthGrow": 1}}, {"id": "thisPartOnly", "options": {"hasTotal": true, "displayDesignation": "Cost", "widthGrow": 1}}]}, "rmocScrap_cost_Activity": {"type": "field", "rows": ["ManufacturingCosts2_ManufacturingStep", "ManufacturingInterestCosts_ManufacturingStep"], "rowDefinitions": {"ProductionLaborActivity_Labor": {"id": "ProductionLaborActivity_Labor", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Production labor", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_ProductionLaborActivity"}]}, "OccupancyMachineActivity_Machine": {"id": "OccupancyMachineActivity_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Occupancy machine", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_OccupancyMachineActivity"}]}, "NonOccupancyMachineActivity_Machine": {"id": "NonOccupancyMachineActivity_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Non-occupancy machine", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_NonOccupancyMachineActivity"}]}, "ProductionMachineActivity_Machine": {"id": "ProductionMachineActivity_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Production machine", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_ProductionMachineActivity"}], "rows": ["OccupancyMachineActivity_Machine", "NonOccupancyMachineActivity_Machine"], "isCollapsed": true}, "SetupMachineActivity_Machine": {"id": "SetupMachineActivity_Machine", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Setup machine", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_SetupMachineActivity"}]}, "SetupOperatorActivity_Labor": {"id": "SetupOperatorActivity_Labor", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Setup operator", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_SetupOperatorActivity"}]}, "SetupSetupWorkerActivity_Labor": {"id": "SetupSetupWorkerActivity_Labor", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Setup setup-worker", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_SetupSetupWorkerActivity"}]}, "SetupLaborActivity_ManufacturingStep": {"id": "SetupLaborActivity_ManufacturingStep", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Setup labor", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_SetupLaborActivity"}], "rows": ["SetupOperatorActivity_Labor", "SetupSetupWorkerActivity_Labor"], "isCollapsed": true}, "SetupActivity_ManufacturingStep": {"id": "SetupActivity_ManufacturingStep", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Setup", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_SetupActivity"}], "rows": ["SetupMachineActivity_Machine", "SetupLaborActivity_ManufacturingStep"], "isCollapsed": true}, "ToolMaintenanceActivity_Tool": {"id": "ToolMaintenanceActivity_Tool", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Tool maintenance", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_ToolMaintenanceActivity"}]}, "RoughProcessActivity_RoughProcess": {"id": "RoughProcessActivity_RoughProcess", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Rough manufacturing step", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_RoughProcessActivity"}]}, "DirectManufacturingActivity_ManufacturingStep": {"id": "DirectManufacturingActivity_ManufacturingStep", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Direct manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_DirectManufacturingActivity"}], "rows": ["ProductionLaborActivity_Labor", "ProductionMachineActivity_Machine", "SetupActivity_ManufacturingStep", "ToolMaintenanceActivity_Tool", "RoughProcessActivity_RoughProcess"]}, "ManufacturingScrapCosts_ManufacturingStep": {"id": "ManufacturingScrapCosts_ManufacturingStep", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing scrap", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "manufacturingScrapRate"}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_ManufacturingScrapCosts"}]}, "ManufacturingOverheadCosts_ManufacturingStep": {"id": "ManufacturingOverheadCosts_ManufacturingStep", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing overhead", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingOverheadCosts_Rate"}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_ManufacturingOverheadCosts"}]}, "ToolAllocationCosts_Tool": {"id": "ToolAllocationCosts_Tool", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Tool allocation", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_ToolAllocationCosts"}]}, "ToolInterestCosts_Tool": {"id": "ToolInterestCosts_Tool", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Tool interest", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_ToolInterestCosts"}]}, "ManufacturingCostsAfterDirectManufacturingCosts_ManufacturingStep": {"id": "ManufacturingCostsAfterDirectManufacturingCosts_ManufacturingStep", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing after direct manufacturing", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_ManufacturingCostsAfterDirectManufacturingCosts"}], "rows": ["ManufacturingScrapCosts_ManufacturingStep", "ManufacturingOverheadCosts_ManufacturingStep", "ToolAllocationCosts_Tool", "ToolInterestCosts_Tool"]}, "ManufacturingCosts2_ManufacturingStep": {"id": "ManufacturingCosts2_ManufacturingStep", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing 2", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_ManufacturingCosts2"}], "rows": ["DirectManufacturingActivity_ManufacturingStep", "ManufacturingCostsAfterDirectManufacturingCosts_ManufacturingStep"]}, "ManufacturingInterestCosts_ManufacturingStep": {"id": "ManufacturingInterestCosts_ManufacturingStep", "cells": [{"type": "value", "columnId": "display", "field": {"name": "displayDesignation", "value": "Manufacturing interest", "type": "Text", "source": null, "currencyInfo": null}}, {"type": "lookup", "columnId": "interestPeriod", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingInterestCosts_Time"}, {"type": "lookup", "columnId": "rate", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingInterestCosts_InterestRate"}, {"type": "lookup", "columnId": "thisPartOnly", "collectFrom": {"type": "self"}, "fieldName": "#Cost_ManufacturingStep_This_ManufacturingInterestCosts"}]}}, "columns": [{"id": "display", "options": {"mainColumn": true, "editable": false, "displayDesignation": "displayDesignation", "widthGrow": 3}}, {"id": "interestPeriod", "options": {"displayDesignation": "Interest period", "widthGrow": 1}}, {"id": "rate", "options": {"displayDesignation": "Rate", "widthGrow": 1}}, {"id": "thisPartOnly", "options": {"hasTotal": true, "displayDesignation": "Cost", "widthGrow": 1}}]}}}