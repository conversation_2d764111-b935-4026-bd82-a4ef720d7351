{"key": {"identifiers": {"COST_OPERATION_ID": {"key": "1", "version": {"major": 1, "minor": 0}}, "CO2_OPERATION_ID": {"key": "1", "version": {"major": 1, "minor": 0}}, "PROCUREMENT_TYPE_ID": {"procurementTypeResult": "PURCHASE"}}}, "cards": {"manufacturingStepsTable": {"views": ["co2", "cost"], "title": {"cost": "Manufacturing 3", "co2": "Manufacturing"}, "tableVariations": {"cost": ["Production", "Activity"], "co2": ["Production", "Activity"]}}}, "tableConfigs": {"manufacturingStepsTable_cost_Production": {"type": "entity", "rows": ["step"], "rowDefinitions": {"step": {"id": "step", "collectBy": {"criteria": [{"operator": "eq", "key": "type", "value": "MANUFACTURING_STEP"}], "type": "ancestor"}}}, "columns": [{"id": "displayDesignation", "field": "displayDesignation", "options": {"mainColumn": true, "displayDesignation": "Designation", "widthGrow": 2}}, {"id": "cycleTime", "field": "cycleTime", "options": {"editable": false, "widthGrow": 1}}, {"id": "partsPerCycle", "field": "partsPerCycle", "options": {"editable": false, "widthGrow": 1}}, {"id": "throughput", "field": "throughput", "options": {"editable": false, "widthGrow": 1}}, {"id": "theoreticalThroughput", "field": "theoreticalThroughput", "options": {"editable": false, "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_LaborCosts", "field": "#Cost_ManufacturingStep_This_LaborCosts", "options": {"hasTotal": true, "displayDesignation": "Labor", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_MachineFixCosts", "field": "#Cost_ManufacturingStep_This_MachineFixCosts", "options": {"hasTotal": true, "displayDesignation": "Machine fix", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_MachineVariableCosts", "field": "#Cost_ManufacturingStep_This_MachineVariableCosts", "options": {"hasTotal": true, "displayDesignation": "Machine variable", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_ToolMaintenanceCosts", "field": "#Cost_ManufacturingStep_This_ToolMaintenanceCosts", "options": {"hasTotal": true, "displayDesignation": "Tool maintenance", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_RoughProcessCosts", "field": "#Cost_ManufacturingStep_This_RoughProcessCosts", "options": {"hasTotal": true, "displayDesignation": "Rough manufacturing step", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_ManufacturingScrapCosts", "field": "#Cost_ManufacturingStep_This_ManufacturingScrapCosts", "options": {"hasTotal": true, "displayDesignation": "Manufacturing scrap", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_ManufacturingOverheadCosts", "field": "#Cost_ManufacturingStep_This_ManufacturingOverheadCosts", "options": {"hasTotal": true, "displayDesignation": "Residual manufacturing overhead", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_ToolAllocationCosts", "field": "#Cost_ManufacturingStep_This_ToolAllocationCosts", "options": {"hasTotal": true, "displayDesignation": "Tool allocation", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_ToolInterestCosts", "field": "#Cost_ManufacturingStep_This_ToolInterestCosts", "options": {"hasTotal": true, "displayDesignation": "Tool interest", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_ManufacturingInterestCosts", "field": "#Cost_ManufacturingStep_This_ManufacturingInterestCosts", "options": {"hasTotal": true, "displayDesignation": "Interest on work in progress", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_ManufacturingCosts3", "field": "#Cost_ManufacturingStep_This_ManufacturingCosts3", "options": {"hasTotal": true, "displayDesignation": "Manufacturing 3", "widthGrow": 1}}]}, "manufacturingStepsTable_cost_Activity": {"type": "entity", "rows": ["step"], "rowDefinitions": {"step": {"id": "step", "collectBy": {"criteria": [{"operator": "eq", "key": "type", "value": "MANUFACTURING_STEP"}], "type": "ancestor"}}}, "columns": [{"id": "displayDesignation", "field": "displayDesignation", "options": {"mainColumn": true, "displayDesignation": "Designation", "widthGrow": 2}}, {"id": "cycleTime", "field": "cycleTime", "options": {"editable": false, "widthGrow": 1}}, {"id": "partsPerCycle", "field": "partsPerCycle", "options": {"editable": false, "widthGrow": 1}}, {"id": "throughput", "field": "throughput", "options": {"editable": false, "widthGrow": 1}}, {"id": "theoreticalThroughput", "field": "theoreticalThroughput", "options": {"editable": false, "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_ProductionLaborActivity", "field": "#Cost_ManufacturingStep_This_ProductionLaborActivity", "options": {"hasTotal": true, "displayDesignation": "Production labor", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_OccupancyMachineActivity", "field": "#Cost_ManufacturingStep_This_OccupancyMachineActivity", "options": {"hasTotal": true, "displayDesignation": "Occupancy machine", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_NonOccupancyMachineActivity", "field": "#Cost_ManufacturingStep_This_NonOccupancyMachineActivity", "options": {"hasTotal": true, "displayDesignation": "Non-occupancy machine", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_SetupMachineActivity", "field": "#Cost_ManufacturingStep_This_SetupMachineActivity", "options": {"hasTotal": true, "displayDesignation": "Setup machine", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_SetupOperatorActivity", "field": "#Cost_ManufacturingStep_This_SetupOperatorActivity", "options": {"hasTotal": true, "displayDesignation": "Setup operator", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_SetupSetupWorkerActivity", "field": "#Cost_ManufacturingStep_This_SetupSetupWorkerActivity", "options": {"hasTotal": true, "displayDesignation": "Setup setup-worker", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_ToolMaintenanceActivity", "field": "#Cost_ManufacturingStep_This_ToolMaintenanceActivity", "options": {"hasTotal": true, "displayDesignation": "Tool maintenance", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_RoughProcessActivity", "field": "#Cost_ManufacturingStep_This_RoughProcessActivity", "options": {"hasTotal": true, "displayDesignation": "Rough manufacturing step", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_ManufacturingScrapCosts", "field": "#Cost_ManufacturingStep_This_ManufacturingScrapCosts", "options": {"hasTotal": true, "displayDesignation": "Manufacturing scrap", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_ManufacturingOverheadCosts", "field": "#Cost_ManufacturingStep_This_ManufacturingOverheadCosts", "options": {"hasTotal": true, "displayDesignation": "Residual manufacturing overhead", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_ToolAllocationCosts", "field": "#Cost_ManufacturingStep_This_ToolAllocationCosts", "options": {"hasTotal": true, "displayDesignation": "Tool allocation", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_ToolInterestCosts", "field": "#Cost_ManufacturingStep_This_ToolInterestCosts", "options": {"hasTotal": true, "displayDesignation": "Tool interest", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_ManufacturingInterestCosts", "field": "#Cost_ManufacturingStep_This_ManufacturingInterestCosts", "options": {"hasTotal": true, "displayDesignation": "Interest on work in progress", "widthGrow": 1}}, {"id": "#Cost_ManufacturingStep_This_ManufacturingCosts3", "field": "#Cost_ManufacturingStep_This_ManufacturingCosts3", "options": {"hasTotal": true, "displayDesignation": "Manufacturing 3", "widthGrow": 1}}]}, "manufacturingStepsTable_co2_Production": {"type": "entity", "rows": ["step"], "rowDefinitions": {"step": {"id": "step", "collectBy": {"criteria": [{"operator": "eq", "key": "type", "value": "MANUFACTURING_STEP"}], "type": "ancestor"}}}, "columns": [{"id": "displayDesignation", "field": "displayDesignation", "options": {"mainColumn": true, "displayDesignation": "Designation", "widthGrow": 2}}, {"id": "cycleTime", "field": "cycleTime", "options": {"editable": false, "widthGrow": 1}}, {"id": "partsPerCycle", "field": "partsPerCycle", "options": {"editable": false, "widthGrow": 1}}, {"id": "throughput", "field": "throughput", "options": {"editable": false, "widthGrow": 1}}, {"id": "theoreticalThroughput", "field": "theoreticalThroughput", "options": {"editable": false, "widthGrow": 1}}, {"id": "#CO2_ManufacturingStep_This_MachineCO2e", "field": "#CO2_ManufacturingStep_This_MachineCO2e", "options": {"hasTotal": true, "displayDesignation": "Machine", "widthGrow": 1}}, {"id": "#CO2_ManufacturingStep_This_RoughProcessCO2e", "field": "#CO2_ManufacturingStep_This_RoughProcessCO2e", "options": {"hasTotal": true, "displayDesignation": "Rough manufacturing step", "widthGrow": 1}}, {"id": "#CO2_ManufacturingStep_This_ManufacturingScrapCO2e", "field": "#CO2_ManufacturingStep_This_ManufacturingScrapCO2e", "options": {"hasTotal": true, "displayDesignation": "Manufacturing scrap", "widthGrow": 1}}, {"id": "#CO2_ManufacturingStep_This_ManufacturingOverheadCO2e", "field": "#CO2_ManufacturingStep_This_ManufacturingOverheadCO2e", "options": {"hasTotal": true, "displayDesignation": "Residual manufacturing overhead", "widthGrow": 1}}, {"id": "#CO2_ManufacturingStep_This_ToolCO2e", "field": "#CO2_ManufacturingStep_This_ToolCO2e", "options": {"hasTotal": true, "displayDesignation": "Tool", "widthGrow": 1}}, {"id": "#CO2_ManufacturingStep_This_ManufacturingCO2e", "field": "#CO2_ManufacturingStep_This_ManufacturingCO2e", "options": {"hasTotal": true, "displayDesignation": "Manufacturing", "widthGrow": 1}}]}, "manufacturingStepsTable_co2_Activity": {"type": "entity", "rows": ["step"], "rowDefinitions": {"step": {"id": "step", "collectBy": {"criteria": [{"operator": "eq", "key": "type", "value": "MANUFACTURING_STEP"}], "type": "ancestor"}}}, "columns": [{"id": "displayDesignation", "field": "displayDesignation", "options": {"mainColumn": true, "displayDesignation": "Designation", "widthGrow": 2}}, {"id": "cycleTime", "field": "cycleTime", "options": {"editable": false, "widthGrow": 1}}, {"id": "partsPerCycle", "field": "partsPerCycle", "options": {"editable": false, "widthGrow": 1}}, {"id": "throughput", "field": "throughput", "options": {"editable": false, "widthGrow": 1}}, {"id": "theoreticalThroughput", "field": "theoreticalThroughput", "options": {"editable": false, "widthGrow": 1}}, {"id": "#CO2_ManufacturingStep_This_ProductionMachineActivity", "field": "#CO2_ManufacturingStep_This_ProductionMachineActivity", "options": {"hasTotal": true, "displayDesignation": "Production machine", "widthGrow": 1}}, {"id": "#CO2_ManufacturingStep_This_RoughProcessActivity", "field": "#CO2_ManufacturingStep_This_RoughProcessActivity", "options": {"hasTotal": true, "displayDesignation": "Rough manufacturing step", "widthGrow": 1}}, {"id": "#CO2_ManufacturingStep_This_ManufacturingScrapCO2e", "field": "#CO2_ManufacturingStep_This_ManufacturingScrapCO2e", "options": {"hasTotal": true, "displayDesignation": "Manufacturing scrap", "widthGrow": 1}}, {"id": "#CO2_ManufacturingStep_This_ManufacturingOverheadCO2e", "field": "#CO2_ManufacturingStep_This_ManufacturingOverheadCO2e", "options": {"hasTotal": true, "displayDesignation": "Residual manufacturing overhead", "widthGrow": 1}}, {"id": "#CO2_ManufacturingStep_This_ToolCO2e", "field": "#CO2_ManufacturingStep_This_ToolCO2e", "options": {"hasTotal": true, "displayDesignation": "Tool", "widthGrow": 1}}, {"id": "#CO2_ManufacturingStep_This_ManufacturingCO2e", "field": "#CO2_ManufacturingStep_This_ManufacturingCO2e", "options": {"hasTotal": true, "displayDesignation": "Manufacturing", "widthGrow": 1}}]}}}