package com.nu.bom.core.manufacturing.service

import com.nu.bom.core.controller.CalculationResultDto
import com.nu.bom.core.controller.InputParameter
import com.nu.bom.core.controller.ManufacturingParameters
import com.nu.bom.core.controller.NewEntityDto
import com.nu.bom.core.mainBranchId
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.extension.volumeandscrap.VolumeCalculationBomEntry
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.testentities.TestMachine
import com.nu.bom.core.manufacturing.testentities.TestManufacturing
import com.nu.bom.core.manufacturing.testentities.TestManufacturingStep
import com.nu.bom.core.manufacturing.testentities.lazyloading.TestBomEntry
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.toBomNodeId
import com.nu.bom.core.model.toBranchId
import com.nu.bom.core.model.toMongoBranchId
import com.nu.bom.core.model.toMongoID
import com.nu.bom.core.mongoBranchId
import com.nu.bom.core.mongoProjectId
import com.nu.bom.core.projectId
import com.nu.bom.core.service.BomNodeService
import com.nu.bom.core.service.ManufacturingCalculationService
import com.nu.bom.core.service.MasterDataService
import com.nu.bom.core.service.TestDataService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.AccountTestUtil
import com.nu.bom.core.utils.LoadTestEntityClasses
import com.nu.bomrads.dto.BranchViewDTO
import com.nu.bomrads.dto.ProjectCreationDTO
import com.nu.bomrads.enumeration.BranchTarget
import org.bson.types.ObjectId
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.kotlin.any
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.test.context.ActiveProfiles
import reactor.core.publisher.Mono
import java.math.BigDecimal

@SpringBootTest
@ActiveProfiles("test")
@LoadTestEntityClasses(
    classes = [TestManufacturing::class, TestManufacturingStep::class, TestMachine::class, TestBomEntry::class],
)
class CalculationServiceMultiManufacturingTests {
    @Autowired
    private lateinit var bomNodeService: BomNodeService

    @MockBean
    private lateinit var masterDataService: MasterDataService

    @Autowired
    private lateinit var testDataService: TestDataService

    @Autowired
    private lateinit var manufacturingCalculationService: ManufacturingCalculationService

    @Autowired
    private lateinit var accountUtil: AccountTestUtil

    private lateinit var projectCreation: ProjectCreationDTO
    private lateinit var projectId: ProjectId
    private lateinit var partId: ObjectId
    private lateinit var branchId: BranchId

    private lateinit var accessCheck: AccessCheck

    @BeforeEach
    fun setup() {
        Mockito
            .`when`(masterDataService.getFilteredUniqueMasterData(any(), any<MasterDataType>(), any()))
            .thenReturn(Mono.empty())
        partId = ObjectId.get()
        val setup = accountUtil.setupWithProject("CalServiMulti", key = "CSM")
        accessCheck = setup.first
        projectCreation = setup.second
        projectId = projectCreation.mongoProjectId()
        branchId = projectCreation.mainBranch.id.toMongoBranchId()
    }

    @AfterEach
    fun teardown() {
        Mockito.reset(masterDataService)
        accountUtil.cleanup()
    }

    @Test
    fun calculateWithOneSub() {
        val (result, _) = setupParentAndChild()

        val calculatedParent = bomNodeService.getBomNode(accessCheck, nodeId = BomNodeId(result.bomNode.id), branch = branchId).block()!!
        val calculatedChild =
            bomNodeService
                .getBomNode(
                    accessCheck,
                    calculatedParent.subNodes[0].bomNodeId,
                    branch = branchId,
                ).block()!!
                .manufacturing!!

        assertField(calculatedParent.manufacturing!!, "totalValues", 700.toBigDecimal())
        assertField(calculatedParent.manufacturing!!, "subTotal", 1.toBigDecimal())
        assertField(calculatedChild, "totalValues", 1.toBigDecimal())
    }

    data class Setup(
        val calcResult: CalculationResultDto,
        val bomEntryId: ObjectId,
    )

    private fun setupParentAndChild(): Setup {
        val (bomEntryId, fullSnapshot) = createParent()
        val parentBomNodeId = fullSnapshot.bomNodeId()

        val child = createSub("54")
        val linked =
            testDataService
                .addAsChild(
                    accessCheck,
                    projectId = projectCreation.projectId(),
                    parentBomNodeId = parentBomNodeId,
                    childNodeId = child.bomNodeId(),
                    parentBomEntryId = bomEntryId,
                    branchId = projectCreation.mainBranchId(),
                    branchTarget = BranchTarget.user,
                ).block()!!

        branchId = linked.bomradBranchId().toMongoBranchId()

        /**calculationResult = */
            val (result, _) =
            manufacturingCalculationService
                .updateAndCalculate(
                    parameters =
                        ManufacturingParameters(
                            bomNodeId = parentBomNodeId,
                            parameters = emptyList(),
                        ),
                    accessCheck = accessCheck,
                    branchId = linked.bomradBranchId(),
                ).block()!!

        return Setup(result, bomEntryId)
    }

    @Test
    fun calculateTwiceWithOneSub() {
        val (result, bomEntryId) = setupParentAndChild()

        // A new branch was created with the updateAndCalculate
        branchId =
            result.bomNode.branch.id
                .toMongoID()

        val calculatedParent = bomNodeService.getBomNode(accessCheck, BomNodeId(result.bomNode.id), branch = branchId).block()!!
        val calculatedChild =
            bomNodeService
                .getBomNode(
                    accessCheck,
                    calculatedParent.subNodes[0].bomNodeId,
                    branch = branchId,
                ).block()!!
                .manufacturing!!

        assertField(calculatedParent.manufacturing!!, "totalValues", 700.toBigDecimal())
        assertField(calculatedParent.manufacturing!!, "subTotal", 1.toBigDecimal())
        assertField(calculatedChild, "totalValues", 1.toBigDecimal())

        /**calculationResult = */
        val secondResult =
            manufacturingCalculationService
                .updateAndCalculate(
                    ManufacturingParameters(
                        bomNodeId = BomNodeId(result.bomNode.id),
                        parameters =
                            listOf(
                                InputParameter(
                                    name = "quantity",
                                    entityId = bomEntryId.toString(),
                                    type = "com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit",
                                    value = 200000.0.toBigDecimal(),
                                ),
                                InputParameter(
                                    name = VolumeCalculationBomEntry::peakVolumePerYear.name,
                                    entityId = bomEntryId.toString(),
                                    type = "com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit",
                                    value = 100000.0.toBigDecimal(),
                                ),
                            ),
                    ),
                    accessCheck = accessCheck,
                    branchId = branchId.toBranchId(),
                ).block()!!
                .result

        // Second calculation does not checkout again because already checked out
        assertEquals(result.bomNode.id, secondResult.bomNode.id)
        val secondCalculatedParent = bomNodeService.getBomNode(accessCheck, BomNodeId(result.bomNode.id), branch = branchId).block()!!
        assertField(secondCalculatedParent.manufacturing!!, "subTotal", 200000.toBigDecimal())
    }

    private fun createParent(): Pair<ObjectId, BomNodeSnapshot> {
        /** create entities with MasterData (current bomNode year - 2019 - will be used for initial MD selection) */
        val snapshot =
            testDataService.createSingleNode(
                accessCheck,
                projectCreation.projectId(),
                branchId = branchId,
                name = "Parent Node",
                title = "Parent Title",
            )
        val bomNodeId = snapshot.bomNodeId()
        branchId = snapshot.bomradBranchId().toMongoBranchId()
        val (man1, _) = addManufacturing(bomNodeId, "1")

        val (step1, _) =
            addManufacturingStep(
                bomNodeId,
                man1._id,
                "1",
                mapOf(
                    "stepWeight" to Rate(0.7.toBigDecimal()),
                ),
            )

        addMachine(
            bomNodeId,
            step1._id,
            "1",
            mapOf(
                "baseRate" to Rate(0.1.toBigDecimal()),
                "baseValue" to Money(10000.toBigDecimal()),
            ),
        )
        val (entity, _) =
            addBomEntry(
                bomNodeId,
                step1._id,
                "1",
                mapOf(
                    "quantity" to Pieces(1.toBigDecimal()),
                    VolumeCalculationBomEntry::peakVolumePerYear.name to Pieces(100000.toBigDecimal()),
                ),
            )

        val bomEntryId = entity._id
        val fullSnapshot = entity.snapshot!!
        val res =
            testDataService.publishBranch(
                accessCheck,
                branchId = fullSnapshot.bomradBranchId(),
                nodeId = fullSnapshot.bomradBomNodeId()!!,
            )
        branchId = projectCreation.mongoBranchId()

        return bomEntryId to res.publishedNode
    }

    private fun createSub(suffix: String): BomNodeSnapshot {
        val snapshot =
            testDataService.createSingleNode(
                accessCheck,
                projectCreation.projectId(),
                branchId = branchId,
                name = "Child Node $suffix",
                title = "Child Title",
            )
        val childNodeId = snapshot.bomNodeId()
        val newChildBranchId = snapshot.bomradBranchId()
        branchId = newChildBranchId.toMongoBranchId()
        // use the new child only branch
        val (man2, _) = addManufacturing(childNodeId, suffix)

        val (step2, _) =
            addManufacturingStep(
                childNodeId,
                man2._id,
                suffix,
                mapOf(
                    "stepWeight" to Rate(1.0.toBigDecimal()),
                ),
            )
        addMachine(
            childNodeId,
            step2._id,
            suffix,
            mapOf(
                "baseRate" to Rate(1.0.toBigDecimal()),
                "baseValue" to Money(1.toBigDecimal()),
            ),
        )

        val finalPublish = testDataService.publishBranch(accessCheck, childNodeId.toBomNodeId(), newChildBranchId)
        assertNotNull(finalPublish, "finalPublish exists")

        val publishedSnapshot = finalPublish.publishedNode
        // Switch back to the main branch
        branchId = projectCreation.mongoBranchId()

        return publishedSnapshot
    }

    private fun addManufacturing(
        bomNodeId: BomNodeId,
        suffix: String,
        initialFields: Map<String, FieldResult<*, *>>? = null,
    ): Pair<ManufacturingEntity, BranchViewDTO> =
        testDataService
            .addManufacturingEntityWithBranchView(
                accessCheck = accessCheck,
                bomNodeId = bomNodeId,
                branchId = branchId,
                newEntityDto =
                    NewEntityDto(
                        type = Entities.MANUFACTURING,
                        clazz = TestManufacturing::class.qualifiedName!!,
                        name = "TestManufacturing$suffix",
                        args = hashMapOf("partId" to partId.toString(), "isPart" to true),
                        initialFields = initialFields,
                    ),
            ).block()!!

    private fun addManufacturingStep(
        bomNodeId: BomNodeId,
        parent: ObjectId,
        suffix: String,
        initialFields: Map<String, FieldResult<*, *>>? = null,
    ): Pair<ManufacturingEntity, BranchViewDTO> =
        testDataService
            .addManufacturingEntityWithBranchView(
                accessCheck = accessCheck,
                bomNodeId = bomNodeId,
                branchId = branchId,
                newEntityDto =
                    NewEntityDto(
                        type = Entities.MANUFACTURING_STEP,
                        clazz = TestManufacturingStep::class.qualifiedName!!,
                        name = "TestManufacturingStep$suffix",
                        parentId = parent.toString(),
                        initialFields = initialFields,
                    ),
            ).block()!!

    private fun addMachine(
        bomNodeId: BomNodeId,
        parent: ObjectId,
        suffix: String,
        initialFields: Map<String, FieldResult<*, *>>? = null,
    ): Pair<ManufacturingEntity, BranchViewDTO> =
        testDataService
            .addManufacturingEntityWithBranchView(
                accessCheck = accessCheck,
                bomNodeId = bomNodeId,
                branchId = branchId,
                newEntityDto =
                    NewEntityDto(
                        type = Entities.MACHINE,
                        clazz = TestMachine::class.qualifiedName!!,
                        name = "TestMachine$suffix",
                        parentId = parent.toString(),
                        initialFields = initialFields,
                    ),
            ).block()!!

    private fun addBomEntry(
        bomNodeId: BomNodeId,
        parent: ObjectId,
        suffix: String,
        initialFields: Map<String, FieldResult<*, *>>? = null,
    ): Pair<ManufacturingEntity, BranchViewDTO> =
        testDataService
            .addManufacturingEntityWithBranchView(
                accessCheck = accessCheck,
                bomNodeId = bomNodeId,
                branchId = branchId,
                newEntityDto =
                    NewEntityDto(
                        type = Entities.BOM_ENTRY,
                        name = "Bom Entry$suffix",
                        clazz = TestBomEntry::class.qualifiedName,
                        parentId = parent.toString(),
                        initialFields = initialFields,
                    ),
            ).block()!!

    private fun assertField(
        entity: ManufacturingEntity,
        fieldName: String,
        expected: BigDecimal,
    ) {
        val fieldsToAssert = entity.fieldWithResults.filter { it.name.name == fieldName }
        assertEquals(1, fieldsToAssert.size, "[$fieldName] found results: $fieldsToAssert")
        val fieldToAssert = fieldsToAssert[0]
        assertNotNull(fieldToAssert, "[$fieldName] is not null")
        assertEquals(
            expected,
            (fieldToAssert.result.res as BigDecimal).setScale(expected.scale()),
            "[$fieldName] expected to $expected but ${fieldToAssert.result}",
        )
    }
}
