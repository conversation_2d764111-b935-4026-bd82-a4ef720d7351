package com.nu.bom.core.service.config

import com.nu.bom.core.manufacturing.configFields.TEST_CFG_GROUP
import com.nu.bom.core.manufacturing.configFields.TestConfigTypeA
import com.nu.bom.core.manufacturing.configFields.TestConfigTypeB
import com.nu.bom.core.manufacturing.configFields.TestConfiguration
import com.nu.bom.core.model.AccountId
import com.nu.bom.core.model.configurations.ConfigId
import com.nu.bom.core.model.configurations.SemanticVersion
import com.nu.bom.core.model.id
import com.nu.bom.core.service.configurations.BundledTsetConfigurations
import com.nu.bom.core.service.configurations.ConfigCache
import com.nu.bom.core.service.configurations.ConfigType
import com.nu.bom.core.service.configurations.ConfigurationInitializerService
import com.nu.bom.core.service.configurations.ConfigurationInitializerServiceImpl
import com.nu.bom.core.service.configurations.ConfigurationMigrationFactory
import com.nu.bom.core.service.configurations.ConfigurationService
import com.nu.bom.core.service.configurations.ConfigurationServiceImpl
import com.nu.bom.core.service.configurations.ConfigurationTypeRegistry
import com.nu.bom.core.service.configurations.TestConfigTypeRegistry
import com.nu.bom.core.service.configurations.TsetConfiguration
import com.nu.bom.core.service.configurations.TsetConfigurationFactory
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.AccountUtil
import com.nu.bom.core.utils.ConfigurationTestUtil
import com.nu.bom.core.utils.then
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mock
import org.mockito.Mockito.mock
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.ReactiveMongoTemplate
import org.springframework.test.context.ActiveProfiles
import reactor.test.StepVerifier

private typealias V = SemanticVersion

@SpringBootTest
@ActiveProfiles("test")
class ConfigurationInitializerServiceImplTest {
    @Mock
    private lateinit var configurationMigrationFactory: ConfigurationMigrationFactory

    @Autowired
    private lateinit var template: ReactiveMongoTemplate

    @Autowired
    private lateinit var actualFactory: TsetConfigurationFactory

    @Mock
    private lateinit var mockFactory: TsetConfigurationFactory

    private val configurationInitializerService: ConfigurationInitializerService by lazy {
        ConfigurationInitializerServiceImpl(mockFactory, template, configurationMigrationFactory)
    }

    private val typeRegistry: ConfigurationTypeRegistry = TestConfigTypeRegistry()

    private val configurationService: ConfigurationService by lazy {
        // Silly little dance to resolve the cyclic dependency
        val cacheMock: ConfigCache = mock()
        val service = ConfigurationServiceImpl(configurationMigrationFactory, template, typeRegistry, cacheMock)
        val actual = ConfigCache(service)
        whenever(cacheMock.defaults).thenReturn(actual.defaults)
        service
    }

    private val configurationTestUtil: ConfigurationTestUtil by lazy {
        ConfigurationTestUtil(configurationInitializerService, configurationService, template)
    }

    companion object {
        private fun bundledSimple(
            type: ConfigType<*>,
            configs: List<List<SemanticVersion>>,
            defaultIdx: Int = 0,
        ) = bundledConfig(
            type,
            configs.map { versions -> versions.map { it to (10 * it.major + it.minor) } },
            defaultIdx,
        )

        private fun bundledConfig(
            type: ConfigType<*>,
            configs: List<List<Pair<SemanticVersion, Int>>>,
            defaultIdx: Int = 0,
        ) = BundledTsetConfigurations(
            type,
            configs.mapIndexed { i, versions ->
                val key = key(type, i)
                val values = versions.map { (version, value) -> TsetConfiguration.Version(key, version, TestConfiguration(value)) }
                TsetConfiguration(key, i == defaultIdx, false, values)
            },
        )

        private fun key(
            type: ConfigType<*>,
            i: Int,
        ) = ConfigurationService.TSET_KEY_PREFIX + type.type + "$i"
    }

    private val account by lazy { accessCheck.toAccountId() }
    private lateinit var accessCheck: AccessCheck

    @BeforeEach
    fun setup() {
        whenever(
            configurationMigrationFactory.tryMigrate(any(), any(), any()),
        ).thenAnswer { it.arguments[2] }
        accessCheck = AccountUtil.dummyAccessCheck()
        configurationTestUtil.cleanupTestConfigs().block()
    }

    @AfterEach
    fun teardown() {
        configurationTestUtil.cleanupTestConfigs().block()
        configurationTestUtil.cleanup(account)
    }

    private fun deployConfigs(vararg bundled: BundledTsetConfigurations) = deployConfigs(bundled.toList())

    private fun deployConfigs(bundled: List<BundledTsetConfigurations>) {
        setBundledConfigs(bundled)
        StepVerifier
            .create(configurationInitializerService.updateTsetConfigurations())
            .verifyComplete()
    }

    private fun setBundledConfigs(bundled: List<BundledTsetConfigurations>) {
        // We only run tests on the mocked bundles, but include the "actual" ones too.
        // We reuse the db across tests, and their absence would lead to errors.
        val actual = actualFactory.getTsetConfigurations().filterNot { it.type.group == TEST_CFG_GROUP }
        whenever(mockFactory.getTsetConfigurations())
            .thenReturn(actual + bundled)
    }

    private fun initAccount(
        accountId: AccountId,
        expectUninit: Boolean = true,
    ) = StepVerifier
        .create(configurationInitializerService.initializeAccount(accountId))
        .assertNext { uninit -> assertThat(uninit).isEqualTo(expectUninit) }
        .verifyComplete()

    @Test
    fun `initialize configs and account`() {
        val versions = listOf(V(1, 0), V(1, 1), V(2, 0))
        val cfgs = listOf(versions, versions)
        val configAs = bundledSimple(TestConfigTypeA, cfgs)
        val configBs = bundledSimple(TestConfigTypeB, cfgs)
        deployConfigs(configAs, configBs)

        initAccount(account.id)
        for (cfgType in listOf(configAs, configBs)) {
            StepVerifier
                .create(configurationService.findAllOfType(accessCheck, cfgType.type).collectList())
                .assertNext { active ->
                    assertThat(active.map { it.id.key })
                        .containsExactlyInAnyOrderElementsOf(cfgType.configurations.map { it.key })

                    assertThat(active.single { it.id.key.endsWith('0') }.isDefault).isTrue()
                }
        }
    }

    @Test
    fun `update with new version`() {
        val ogVersions = listOf(V(1, 0), V(2, 0))
        val newVersions = listOf(V(1, 1), V(2, 1))
        val cfgInBoth = listOf(V(1, 0))

        val init = bundledSimple(TestConfigTypeA, listOf(ogVersions, cfgInBoth))
        val update = bundledSimple(TestConfigTypeA, listOf((ogVersions + newVersions).sorted(), cfgInBoth))

        deployConfigs(init)
        initAccount(account)

        deployConfigs(update)

        StepVerifier
            .create(configurationService.findAllOfType(accessCheck, init.type).collectList())
            .assertNext { active ->
                val (default, other) = active.partition { it.isDefault }
                assertThat(default.single().id.version)
                    .withFailMessage { "New version was not set as default" }
                    .isEqualTo(V(2, 1))
                assertThat(other.map { Pair(it.id.key, it.id.version) })
                    .containsExactlyInAnyOrder(
                        // Previous version is not deactivated!
                        // A0 1.1 is not active as 1.0 was never active
                        key(TestConfigTypeA, 0) to V(2, 0),
                        key(TestConfigTypeA, 1) to V(1, 0),
                    )
            }.verifyComplete()
    }

    @Test
    fun `new type, config, versions are initialized`() {
        val init = listOf(V(1, 0))
        deployConfigs(bundledSimple(TestConfigTypeA, listOf(init)))
        initAccount(account)

        val update = init + listOf(V(1, 1), V(2, 0), V(3, 0))
        val twoConfigs = listOf(update, update)
        deployConfigs(bundledSimple(TestConfigTypeA, twoConfigs), bundledSimple(TestConfigTypeB, twoConfigs))

        StepVerifier
            .create(configurationService.findAllOfType(accessCheck, TestConfigTypeA, includeInactive = true))
            .expectNextCount(8)
            .verifyComplete()

        StepVerifier
            .create(configurationService.findAllOfType(accessCheck, TestConfigTypeB, includeInactive = true))
            .expectNextCount(8)
            .verifyComplete()
    }

    @Test
    fun `new account has only latest version active`() {
        val versions = listOf(V(1, 0), V(1, 1), V(2, 0), V(3, 0))
        deployConfigs(bundledSimple(TestConfigTypeA, listOf(versions, versions)))
        initAccount(account)
        StepVerifier
            .create(configurationService.findAllOfType(accessCheck, TestConfigTypeA))
            .assertNext { assertThat(it.id.version).isEqualTo(V(3, 0)) }
            .assertNext { assertThat(it.id.version).isEqualTo(V(3, 0)) }
            .verifyComplete()
    }

    @Test
    fun `update chain - still newest is default`() {
        val init = listOf(V(1, 0), V(1, 1), V(2, 0))
        val new = listOf(V(1, 2), V(2, 1), V(3, 0), V(4, 0), V(4, 1))

        deployConfigs(bundledSimple(TestConfigTypeA, listOf(init)))
        initAccount(account)
        deployConfigs(bundledSimple(TestConfigTypeA, listOf((init + new).sorted())))

        StepVerifier
            .create(configurationService.getDefault(accessCheck, TestConfigTypeA))
            .assertNext { assertThat(it.id.version).isEqualTo(SemanticVersion(4, 1)) }
            .verifyComplete()

        StepVerifier
            .create(configurationService.findAllOfType(accessCheck, TestConfigTypeA))
            // 2.0, 2.1, 3.0, 4.0, 4.1
            .expectNextCount(5)
            .verifyComplete()
    }

    @Test
    fun `update chain - legacy version is active`() {
        val init = listOf(V(1, 0), V(1, 1), V(2, 0))
        val new = listOf(V(1, 2), V(2, 1), V(3, 0), V(4, 0), V(4, 1))

        deployConfigs(bundledSimple(TestConfigTypeA, listOf(init)))
        initAccount(account)
        configurationService
            .setDefault(accessCheck, ConfigId(TestConfigTypeA, key(TestConfigTypeA, 0), V(1, 1)))
            .then {
                configurationService.deactivate(accessCheck, ConfigId(TestConfigTypeA, key(TestConfigTypeA, 0), V(2, 0)))
            }.block()

        deployConfigs(bundledSimple(TestConfigTypeA, listOf((init + new).sorted())))

        StepVerifier
            .create(configurationService.getDefault(accessCheck, TestConfigTypeA))
            .assertNext { assertThat(it.id.version).isEqualTo(SemanticVersion(1, 2)) }
            .verifyComplete()

        StepVerifier
            .create(configurationService.findAllOfType(accessCheck, TestConfigTypeA))
            // 1.1, 1.2
            .expectNextCount(2)
            .verifyComplete()
    }

    @Test
    fun `changing value throws error`() {
        val versions = listOf(V(1, 0) to 1, V(1, 1) to 11, V(2, 0) to 2)
        val bundle = bundledConfig(TestConfigTypeA, listOf(versions))
        deployConfigs(bundle)

        val changed = versions.map { (v, i) -> v to i + 1 }
        setBundledConfigs(listOf(bundledConfig(TestConfigTypeA, listOf(changed))))

        StepVerifier
            .create(configurationInitializerService.updateTsetConfigurations())
            .verifyError()
    }

    @Test
    fun `missing throws error`() {
        val versions = listOf(V(1, 0), V(1, 1), V(2, 0))
        val bundleA = bundledSimple(TestConfigTypeA, listOf(versions, versions))
        val bundleB = bundledSimple(TestConfigTypeB, listOf(versions, versions))
        deployConfigs(bundleA, bundleB)

        val missingBundle = listOf(bundleA)
        setBundledConfigs(missingBundle)
        StepVerifier
            .create(configurationInitializerService.updateTsetConfigurations())
            .verifyError()

        val missingConfig = listOf(bundleA, bundledSimple(TestConfigTypeB, listOf(versions)))
        setBundledConfigs(missingConfig)
        StepVerifier
            .create(configurationInitializerService.updateTsetConfigurations())
            .verifyError()

        val missingVersion = listOf(bundleA, bundledSimple(TestConfigTypeB, listOf(versions, versions.dropLast(1))))
        setBundledConfigs(missingVersion)
        StepVerifier
            .create(configurationInitializerService.updateTsetConfigurations())
            .verifyError()
    }
}
