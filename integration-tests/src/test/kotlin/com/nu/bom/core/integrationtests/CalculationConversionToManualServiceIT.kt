package com.nu.bom.core.service

import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.Part
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.mongoProjectId
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.user.withAccessCheck
import com.nu.bom.core.utils.AccountTestUtil
import com.tset.core.module.bom.calculation.CalculationConvertToManualInput
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

private const val CALCULATION_1_NAME = "Calculation 1"
private const val CALCULATION_2_NAME = "Calculation 2"
private const val STEP_1_NAME = "Test Step 1"
private const val STEP_2_NAME = "Test Step 2"
private const val STEP_3_NAME = "Test Step 3"
private const val MACHINE_1_NAME = "Machine 1"

@SpringBootTest
@ActiveProfiles("test")
class CalculationConversionToManualServiceIT {

    @Autowired
    private lateinit var accountUtil: AccountTestUtil

    @Autowired
    private lateinit var partService: PartService

    @Autowired
    private lateinit var bomNodeService: BomNodeService

    @Autowired
    private lateinit var testDataService: TestDataService

    @Autowired
    private lateinit var conversionService: CalculationConversionToManualService

    private lateinit var access: AccessCheck
    private lateinit var projectId: ProjectId
    private lateinit var part: Part

    @BeforeEach
    fun setup() {
        val (ac, project) = accountUtil.setupWithProject(name = "Test Project", key = "CCMS")

        access = ac
        projectId = project.mongoProjectId()

        part = partService.newPart(
            designation = "TestPart",
            number = "1234",
            accessCheck = access,
            projectId = projectId
        ).block()!!
    }

    @AfterEach
    fun teardown() {
        accountUtil.cleanup()
    }

    @Test
    @Disabled("disabled until testing framework in MR !1981 is prepared")
    fun `convert calculation in project root`() {
        val bomNode = createBomNodeTree()
        val oldCalculation = bomNode.manufacturing!!

        val newSnapshot = conversionService.convertEntity(
            access,
            CalculationConvertToManualInput(
                bomNodeId = bomNode.bomNodeId(),
                branchId = null,
            )
        ).withAccessCheck(access).map { it.newRootSnapshot }.block()!!

        val newCalculation = newSnapshot.manufacturing
        val exists = bomNodeService.nodeExists(access, bomNode.id()).block()!!
        assertFalse(exists, "old bomNode should not exist")
        assertNotEquals(bomNode.bomNodeId, newSnapshot.bomNodeId)
        assertNotEquals(oldCalculation._id, newCalculation?._id)
        assertEquals(oldCalculation.name, newCalculation?.name)
    }

    private fun createBomNodeTree(): BomNodeSnapshot {
        TODO()
    }
}
