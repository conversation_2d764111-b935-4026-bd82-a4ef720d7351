package com.nu.bom.core.integrationtests

import com.nu.bom.core.api.dtos.AddMasterDataDto
import com.nu.bom.core.api.dtos.BomNodeDto
import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.api.dtos.MasterDataCompositeKey
import com.nu.bom.core.api.dtos.MasterDataForEditDto
import com.nu.bom.core.manufacturing.entities.BaseMaterial
import com.nu.bom.core.manufacturing.entities.Consumable
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.utils.FieldExtractionUtils.Companion.BASE_CURRENCY_FIELD_NAME
import com.nu.bom.core.manufacturing.utils.FieldExtractionUtils.Companion.EXCHANGE_RATES_FIELD_NAME
import com.nu.bom.core.manufacturing.utils.TestFieldFiller
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.mongoProjectId
import com.nu.bom.core.service.masterdata.MdDetailCrudService
import com.nu.bom.core.service.masterdata.MdHeaderCrudService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.NbkClient
import com.nu.bom.core.utils.toUpperCamelCase
import com.nu.bom.tests.docker.MasterdataUpdateTest
import com.nu.masterdata.dto.v1.detail.CurrencyMeasurementDto
import com.nu.masterdata.dto.v1.detail.DetailDto
import com.nu.masterdata.dto.v1.detail.NumericDetailValueDto
import com.nu.masterdata.dto.v1.detail.UnitMeasurementDto
import com.nu.masterdata.dto.v1.header.HeaderDto
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import com.nu.masterdata.dto.v1.schema.AnyCurrencyTypeDto
import com.nu.masterdata.dto.v1.schema.DetailValueTypeDto
import com.nu.masterdata.dto.v1.schema.DimensionTypeDto
import com.nu.masterdata.dto.v1.schema.NumericFieldSchemaDto
import com.nu.masterdata.dto.v1.schema.UnitOfMeasurementTypeDto
import com.nu.masterdata.dto.v1.schema.UnitTypeDto
import com.nu.masterdata.dto.v1.schema.ValueTypeDetailValueSchemaDto
import com.tset.bom.clients.nuledge.FieldParameterDto
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@MasterdataUpdateTest
@ActiveProfiles("test", "lookup-init", "config-init")
@AutoConfigureWebTestClient
class MasterDataConsumableIT
    @Autowired
    constructor(
        override val nbkClient: NbkClient,
        private val fieldFiller: TestFieldFiller,
        private val headerService: MdHeaderCrudService,
        private val detailService: MdDetailCrudService,
    ) : NbkClient.With {
        private lateinit var accessCheck: AccessCheck
        private lateinit var projectId: ProjectId

        @BeforeEach
        fun setup() {
            val (ac, project) = nbkClient.setupWithProject(name = this::class.simpleName!!, key = "MDCOI")

            accessCheck = ac
            projectId = project.mongoProjectId()

            fieldFiller.init()
        }

        @AfterEach
        fun teardown() {
            fieldFiller.reset()
            nbkClient.cleanup()
        }

        @Test
        fun `create consumable without details creates a consumable without price and co2 values`() {
            val key = "consumableKeyWithoutDetails"
            createConsumable(key)
            val result =
                testCreateEntityFromNewMasterData(
                    key = key,
                    type = MasterDataType.CONSUMABLE,
                    entityType = Entities.CONSUMABLE,
                    mdClassificationKey = "tset.ref.classification.consumable",
                )
            val consumable = result.findInTree { it.type == Entities.CONSUMABLE }
            assertThat(consumable).isNotNull
            assertThat(consumable!!.getField(BaseMaterial::pricePerUnit.name).value).isNull()
            assertThat(consumable.getField(BaseMaterial::cO2PerUnit.name).value).isNull()
            assertThat(consumable.getField(BaseMaterial::dimension.name).value).isEqualTo("MASS")
            assertThat(consumable.getField(BaseMaterial::costUnit.name).value).isEqualTo("KILOGRAM")
        }

        @Test
        fun `create consumable without price creates a consumable with only co2 values`() {
            val key = "consumableKeyWithCO2"
            createConsumable(key)
            createCO2Detail(key)
            val result =
                testCreateEntityFromNewMasterData(
                    key = key,
                    type = MasterDataType.CONSUMABLE,
                    entityType = Entities.CONSUMABLE,
                    mdClassificationKey = "tset.ref.classification.consumable",
                )
            val consumable = result.findInTree { it.type == Entities.CONSUMABLE }
            assertThat(consumable).isNotNull
            assertThat(consumable!!.getField(BaseMaterial::pricePerUnit.name).value).isNull()
            assertThat(consumable.getField(BaseMaterial::cO2PerUnit.name).value).isEqualTo(2.0)
            assertThat(consumable.getField(BaseMaterial::dimension.name).value).isEqualTo("MASS")
            assertThat(consumable.getField(BaseMaterial::costUnit.name).value).isEqualTo("KILOGRAM")
        }

        @Test
        fun `create consumable without co2 creates a consumable with only price values`() {
            val key = "consumableKeyWithPrice"
            createConsumable(key)
            createPriceDetail(key)
            val result =
                testCreateEntityFromNewMasterData(
                    key = key,
                    type = MasterDataType.CONSUMABLE,
                    entityType = Entities.CONSUMABLE,
                    mdClassificationKey = "tset.ref.classification.consumable",
                )
            val consumable = result.findInTree { it.type == Entities.CONSUMABLE }
            assertThat(consumable).isNotNull
            assertThat(consumable!!.getField(BaseMaterial::pricePerUnit.name).value).isEqualTo(2.0)
            assertThat(consumable.getField(BaseMaterial::cO2PerUnit.name).value).isNull()
            assertThat(consumable.getField(BaseMaterial::dimension.name).value).isEqualTo("MASS")
            assertThat(consumable.getField(BaseMaterial::costUnit.name).value).isEqualTo("KILOGRAM")
        }

        @Test
        fun `create consumable with different dimensions fails`() {
            val key = "consumableKeyDifferent"
            createConsumableDifferentDimensions(key)
            assertThrows<AssertionError> {
                testCreateEntityFromNewMasterData(
                    key = key,
                    type = MasterDataType.CONSUMABLE,
                    entityType = Entities.CONSUMABLE,
                    mdClassificationKey = "tset.ref.classification.consumable",
                )
            }
        }

        @Test
        fun `create consumable with non supported dimension fails`() {
            val key = "consumableKeyWrongDimension"
            createConsumableUnsupportedDimension(key)
            assertThrows<AssertionError> {
                testCreateEntityFromNewMasterData(
                    key = key,
                    type = MasterDataType.CONSUMABLE,
                    entityType = Entities.CONSUMABLE,
                    mdClassificationKey = "tset.ref.classification.consumable",
                )
            }
        }

        private fun createCO2Detail(key: String) {
            detailService.createDetail(
                accessCheck,
                SimpleKeyDto("tset.ref.header-type.material"),
                DetailDto(
                    active = true,
                    effectivities = emptyMap(),
                    headerKey = SimpleKeyDto(key),
                    value =
                        NumericDetailValueDto(
                            numerator =
                                UnitMeasurementDto(
                                    key = SimpleKeyDto("tset.unit.emission.kilogram_co2e"),
                                    displayName = "kg CO₂e",
                                ),
                            denominator =
                                UnitMeasurementDto(
                                    key = SimpleKeyDto("tset.unit.mass.kilogram"),
                                    displayName = "Mass",
                                ),
                            value = 2.0,
                        ),
                    detailValueTypeKey = SimpleKeyDto("emission"),
                    detailValueTypeDisplayName = "Emission",
                ),
            ).block()
        }

        private fun createPriceDetail(key: String) {
            detailService.createDetail(
                accessCheck,
                SimpleKeyDto("tset.ref.header-type.material"),
                DetailDto(
                    active = true,
                    effectivities = emptyMap(),
                    headerKey = SimpleKeyDto(key),
                    value =
                        NumericDetailValueDto(
                            numerator =
                                CurrencyMeasurementDto(
                                    key = SimpleKeyDto("EUR"),
                                    displayName = "Euro",
                                ),
                            denominator =
                                UnitMeasurementDto(
                                    key = SimpleKeyDto("tset.unit.mass.kilogram"),
                                    displayName = "Mass",
                                ),
                            value = 2.0,
                        ),
                    detailValueTypeKey = SimpleKeyDto("price"),
                    detailValueTypeDisplayName = "Price",
                ),
            ).block()
        }

        private fun createConsumable(key: String) =
            headerService.createHeader(
                accessCheck,
                HeaderDto(
                    SimpleKeyDto(key),
                    "consumable name",
                    SimpleKeyDto("tset.ref.header-type.material"),
                    true,
                    ValueTypeDetailValueSchemaDto(
                        mapOf(
                            SimpleKeyDto("emission") to
                                DetailValueTypeDto(
                                    key = SimpleKeyDto("emission"),
                                    name = "Emission",
                                    index = 1,
                                    detailValueSchema =
                                        NumericFieldSchemaDto(
                                            UnitOfMeasurementTypeDto(
                                                denominator =
                                                    DimensionTypeDto(
                                                        defaultUnitKey = SimpleKeyDto("tset.unit.mass.kilogram"),
                                                        displayName = "Mass",
                                                        key = SimpleKeyDto("tset.dimension.mass"),
                                                    ),
                                                numerator =
                                                    UnitTypeDto(
                                                        key = SimpleKeyDto("tset.unit.emission.kilogram_co2e"),
                                                        displayName = "kg CO₂e",
                                                    ),
                                            ),
                                        ),
                                ),
                            SimpleKeyDto("price") to
                                DetailValueTypeDto(
                                    key = SimpleKeyDto("price"),
                                    name = "Price",
                                    index = 0,
                                    detailValueSchema =
                                        NumericFieldSchemaDto(
                                            UnitOfMeasurementTypeDto(
                                                denominator =
                                                    DimensionTypeDto(
                                                        defaultUnitKey = SimpleKeyDto("tset.unit.mass.kilogram"),
                                                        displayName = "Mass",
                                                        key = SimpleKeyDto("tset.dimension.mass"),
                                                    ),
                                                numerator =
                                                    AnyCurrencyTypeDto(
                                                        defaultCurrencyKey = SimpleKeyDto("EUR"),
                                                        defaultCurrencyDisplayName = "Euro",
                                                    ),
                                            ),
                                        ),
                                ),
                        ),
                    ),
                    classifications =
                        mapOf(
                            SimpleKeyDto("tset.ref.classification-type.material") to
                                listOf(SimpleKeyDto("tset.ref.classification.consumable")),
                        ),
                ),
            ).block()

        private fun createConsumableUnsupportedDimension(key: String) =
            headerService.createHeader(
                accessCheck,
                HeaderDto(
                    SimpleKeyDto(key),
                    "consumable name",
                    SimpleKeyDto("tset.ref.header-type.material"),
                    true,
                    ValueTypeDetailValueSchemaDto(
                        mapOf(
                            SimpleKeyDto("emission") to
                                DetailValueTypeDto(
                                    key = SimpleKeyDto("emission"),
                                    name = "Emission",
                                    index = 1,
                                    detailValueSchema =
                                        NumericFieldSchemaDto(
                                            UnitOfMeasurementTypeDto(
                                                denominator =
                                                    DimensionTypeDto(
                                                        defaultUnitKey = SimpleKeyDto("tset.unit.time.second"),
                                                        displayName = "Time",
                                                        key = SimpleKeyDto("tset.dimension.time"),
                                                    ),
                                                numerator =
                                                    UnitTypeDto(
                                                        key = SimpleKeyDto("tset.unit.emission.kilogram_co2e"),
                                                        displayName = "kg CO₂e",
                                                    ),
                                            ),
                                        ),
                                ),
                            SimpleKeyDto("price") to
                                DetailValueTypeDto(
                                    key = SimpleKeyDto("price"),
                                    name = "Price",
                                    index = 0,
                                    detailValueSchema =
                                        NumericFieldSchemaDto(
                                            UnitOfMeasurementTypeDto(
                                                denominator =
                                                    DimensionTypeDto(
                                                        defaultUnitKey = SimpleKeyDto("tset.unit.time.second"),
                                                        displayName = "Time",
                                                        key = SimpleKeyDto("tset.dimension.time"),
                                                    ),
                                                numerator =
                                                    AnyCurrencyTypeDto(
                                                        defaultCurrencyKey = SimpleKeyDto("EUR"),
                                                        defaultCurrencyDisplayName = "Euro",
                                                    ),
                                            ),
                                        ),
                                ),
                        ),
                    ),
                    classifications =
                        mapOf(
                            SimpleKeyDto("tset.ref.classification-type.material") to
                                listOf(SimpleKeyDto("tset.ref.classification.consumable")),
                        ),
                ),
            ).block()

        private fun createConsumableDifferentDimensions(key: String) =
            headerService.createHeader(
                accessCheck,
                HeaderDto(
                    SimpleKeyDto(key),
                    "consumable name",
                    SimpleKeyDto("tset.ref.header-type.material"),
                    true,
                    ValueTypeDetailValueSchemaDto(
                        mapOf(
                            SimpleKeyDto("emission") to
                                DetailValueTypeDto(
                                    key = SimpleKeyDto("emission"),
                                    name = "Emission",
                                    index = 1,
                                    detailValueSchema =
                                        NumericFieldSchemaDto(
                                            UnitOfMeasurementTypeDto(
                                                denominator =
                                                    DimensionTypeDto(
                                                        defaultUnitKey = SimpleKeyDto("tset.unit.mass.kilogram"),
                                                        displayName = "Mass",
                                                        key = SimpleKeyDto("tset.dimension.mass"),
                                                    ),
                                                numerator =
                                                    UnitTypeDto(
                                                        key = SimpleKeyDto("tset.unit.emission.kilogram_co2e"),
                                                        displayName = "kg CO₂e",
                                                    ),
                                            ),
                                        ),
                                ),
                            SimpleKeyDto("price") to
                                DetailValueTypeDto(
                                    key = SimpleKeyDto("price"),
                                    name = "Price",
                                    index = 0,
                                    detailValueSchema =
                                        NumericFieldSchemaDto(
                                            UnitOfMeasurementTypeDto(
                                                denominator =
                                                    DimensionTypeDto(
                                                        defaultUnitKey = SimpleKeyDto("tset.unit.time.second"),
                                                        displayName = "Time",
                                                        key = SimpleKeyDto("tset.dimension.time"),
                                                    ),
                                                numerator =
                                                    AnyCurrencyTypeDto(
                                                        defaultCurrencyKey = SimpleKeyDto("EUR"),
                                                        defaultCurrencyDisplayName = "Euro",
                                                    ),
                                            ),
                                        ),
                                ),
                        ),
                    ),
                    classifications =
                        mapOf(
                            SimpleKeyDto("tset.ref.classification-type.material") to
                                listOf(SimpleKeyDto("tset.ref.classification.consumable")),
                        ),
                ),
            ).block()

        private fun testCreateEntityFromNewMasterData(
            key: String,
            type: MasterDataType,
            entityType: Entities,
            mdClassificationKey: String? = null,
        ): BomNodeDto {
            val mdCompositeKey =
                if (entityType == Entities.CONSUMABLE) {
                    // we use here Water from tset reference data
                    if (mdClassificationKey == null) {
                        MasterDataCompositeKey(
                            type = MasterDataType.CONSUMABLE.name,
                            key = key,
                            year = null,
                            location = null,
                        )
                    } else {
                        MasterDataCompositeKey(
                            type = MasterDataType.NONE.name,
                            key = key,
                            year = null,
                            location = null,
                            mdClassificationKey = mdClassificationKey,
                        )
                    }
                } else {
                    // use generated display name
                    val displayDesignation = type.name.toUpperCamelCase() + "1"

                    // create initial masterdata entry
                    val tmpMasterData = createNewMasterData(type = type, displayDesignation = displayDesignation)
                    tmpMasterData.current.composite
                }

            // 1 - create std calculation
            val (node, step) =
                wizardBuilder
                    .createStandard()
                    .bomNode
                    .let {
                        manufacturingCrudTestClient.addStep(
                            it,
                            name = "Step1",
                            initialCycleTime = Time(1.0, TimeUnits.SECOND),
                        )
                    }.let {
                        manufacturingCrudTestClient.publish(it)
                    }.let { node ->
                        val stepId = node.findInTree { entity -> entity.name == "Step1" }!!
                        node to stepId
                    }

            val fields =
                if (entityType == Entities.CONSUMABLE) {
                    // for new master data we do not send fields because they would overwrite the lookup values
                    listOf(
                        FieldParameterDto(
                            name = BaseMaterial::headerKey.name,
                            type = "Text",
                            value = mdCompositeKey.key,
                            denominatorUnit = null,
                        ),
                        FieldParameterDto(
                            name = Consumable::quantity.name,
                            type = "QuantityUnit",
                            value = 1,
                            denominatorUnit = null,
                        ),
                    )
                } else {
                    getAndFillMandatoryEntityFields(
                        bomNodeId = node.id,
                        branchId = node.branch.id,
                        // node.mainBranchId!!,
                        entityType = entityType,
                        composite = mdCompositeKey,
                    )
                }

            val updatedNode =
                manufacturingCrudTestClient.addEntity(
                    node = node,
                    parentId = step.id,
                    name = null,
                    entityType = entityType,
                    masterDataKey = mdCompositeKey,
                    fields = fields,
                )
            return updatedNode
        }

        /**
         * Creates a new masterdata entry of the given type
         *
         * 0 - Create hardcoded [displayDesignation] field
         * 1 - Get mandatory fields for [type]
         * 2 - Fill mandatory fields with defaults using [TestFieldFiller]
         * 3 - Call add endpoint with the above fields
         * 4 - Call update endpoint with the response
         *
         * */
        private fun createNewMasterData(
            type: MasterDataType,
            displayDesignation: String,
            fields: List<FieldParameter> = emptyList(),
        ): MasterDataForEditDto {
            val hardCodedFields =
                listOf(
                    FieldParameter(
                        name = "displayDesignation",
                        type = "Text",
                        value = displayDesignation,
                        denominatorUnit = null,
                    ),
                    FieldParameter(
                        name = BASE_CURRENCY_FIELD_NAME,
                        type = "Currency",
                        value = "EUR",
                        denominatorUnit = null,
                    ),
                    FieldParameter(
                        name = EXCHANGE_RATES_FIELD_NAME,
                        type = "ExchangeRatesField",
                        value = "{\"EUR\":1.000000000000}",
                        denominatorUnit = null,
                    ),
                )

            val mandatoryFields = masterDataClient.getMandatoryFields(type)

            return masterDataClient.addAndSave(
                AddMasterDataDto(
                    type = type,
                    fields = fieldFiller.fillAll(mandatoryFields) + fields + hardCodedFields,
                    year = null,
                    location = null,
                ),
            )
        }

        private fun getAndFillMandatoryEntityFields(
            bomNodeId: String,
            branchId: String,
            entityType: Entities,
            composite: MasterDataCompositeKey,
        ): List<FieldParameterDto> =
            masterDataClient
                .getMandatoryEntityFields(projectId, bomNodeId, branchId, entityType, composite)
                .map(fieldFiller::fill)
                .map { it.toFieldParameterDto() }
    }
