package com.nu.bom.core.service.uiconfig.toplevel

import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.TestUiConfigFeDtoCreator.createEntityTableCost
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.TestUiConfigFeDtoCreator.createFieldTableCO2
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.TopLevelUiConfigService
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.CardIdentifier
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableName
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.ValueTypeFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.CardConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.TableConfigFeDto
import com.nu.bom.core.service.uiconfig.UIConfigITHelpers.provideCardFieldSectionsByValueTypeFeDto
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test", "topLevelUiConfigServiceTest")
class TopLevelUiConfigServiceIT(
    @Autowired
    override val builderService: TopLevelUiConfigService,
) : TopLevelUIConfigCardBuilderITBase(builderService, TopLevelTestCardBuilder::class) {
    @Test
    fun testFullUiConfigService() {
        createUiConfigAndVerify(
            verifyCards = ::verifyCards,
            verifyTables = ::verifyTables,
            TestInputForCardBuilder.getDefaultWithManufacturingType(ManufacturingType.PURCHASE),
        )
    }

    fun verifyCards(cards: Map<CardIdentifier, CardConfigFeDto>) {
        val expectedCardConfig =
            CardConfigFeDto(
                setOf(ValueTypeFeDto.COST, ValueTypeFeDto.CO2),
                mapOf(
                    ValueTypeFeDto.COST to "TestTitleCost",
                    ValueTypeFeDto.CO2 to "TestTitleCO2",
                ),
                mapOf(
                    ValueTypeFeDto.COST to "TestKPICost",
                    ValueTypeFeDto.CO2 to "TestKPICO2",
                ),
                provideCardFieldSectionsByValueTypeFeDto("Top Level"),
                mapOf(
                    ValueTypeFeDto.COST to listOf("Production"),
                    ValueTypeFeDto.CO2 to listOf("Production"),
                ),
            )

        Assertions.assertEquals(cards.keys, setOf("TestConfigCard"))
        Assertions.assertEquals(expectedCardConfig, cards.values.single())
    }

    fun verifyTables(tableConfigs: Map<TableName, TableConfigFeDto>) {
        val expectedTables =
            mapOf(
                TableName("TestConfigCard", ValueTypeFeDto.CO2, "Production") to createFieldTableCO2(),
                TableName("TestConfigCard", ValueTypeFeDto.COST, "Production") to createEntityTableCost(),
            )
        Assertions.assertEquals(expectedTables.keys, tableConfigs.keys)
        expectedTables.forEach { (key, expectedTable) ->
            Assertions.assertEquals(expectedTable, tableConfigs[key])
        }
    }
}
