package com.nu.bom.core.integrationtests

import com.nu.bom.core.model.PartId
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.mongoProjectId
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.NbkClient
import com.nu.bomrads.enumeration.BomNodeStatus
import org.assertj.core.api.Assertions.assertThat
import org.bson.types.ObjectId
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test", "lookup-init")
@AutoConfigureWebTestClient
class BomNodeUpdateIT : NbkClient.With {
    @Autowired
    override lateinit var nbkClient: NbkClient

    private lateinit var accessCheck: AccessCheck
    private lateinit var projectId: ProjectId

    private lateinit var partId: PartId

    @BeforeEach
    fun setup() {
        val (ac, project) = nbkClient.setupWithProject(name = "Test Project", key = "BNUIT")

        accessCheck = ac
        projectId = project.mongoProjectId()

        // TODO kw delete entirely?
        partId = ObjectId.get()
    }

    @AfterEach
    fun cleanup() {
//        partService.delete(accessCheck, partId).block()
        nbkClient.cleanup()
    }

    @Test
    fun updateStatus() {
        val bomNode = wizardBuilder.createStandard(partId = partId.toHexString()).bomNode

        // assert default value
        assertThat(bomNode.status).isEqualTo(BomNodeStatus.TODO)

        val updatedBomNode = manufacturingCrudTestClient.updateStatus(bomNode.id, BomNodeStatus.IN_PROGRESS, bomNode.branch.id)

        // assert updated value
        assertThat(updatedBomNode.status).isEqualTo(BomNodeStatus.IN_PROGRESS)
    }
}
