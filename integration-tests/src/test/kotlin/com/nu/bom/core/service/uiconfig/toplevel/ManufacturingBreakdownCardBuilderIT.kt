package com.nu.bom.core.service.uiconfig.toplevel

import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationRole
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.fieldnamebuilders.ValueFieldNameBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCO2eCalculationElementType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCostCalculationElementType
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.TopLevelUiConfigService
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.TableOptionEnum
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel.ManufacturingBreakdownCardBuilder
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.CardIdentifier
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableName
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.ValueTypeFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.CardConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableColumnDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.TableConfigFeDto
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test", "manufacturingBreakdownCardBuilderTest")
class ManufacturingBreakdownCardBuilderIT(
    @Autowired
    builderService: TopLevelUiConfigService,
) : TopLevelUIConfigCardBuilderITBase(builderService, ManufacturingBreakdownCardBuilder::class) {
    @Test
    fun `ManufacturingBreakdownCardBuilder for default`() {
        createUiConfigAndVerify(
            verifyCards = ::verifyCards,
            verifyTables = ::verifyTables,
            testInputForCardBuilder = TestInputForCardBuilder.getDefaultWithManufacturingType(ManufacturingType.PURCHASE),
            maybeFileNameForComparison = "ExpectedDefaultManufacturingBreakdownCardUIConfig",
        )
    }

    fun verifyCards(cards: Map<CardIdentifier, CardConfigFeDto>) {
        val expectedCardConfig =
            CardConfigFeDto(
                setOf(
                    ValueTypeFeDto.COST,
                    ValueTypeFeDto.CO2,
                ),
                mapOf(
                    ValueTypeFeDto.COST to "Breakdown",
                    ValueTypeFeDto.CO2 to "Breakdown",
                ),
                mapOf(
                    ValueTypeFeDto.COST to
                        ValueFieldNameBuilder(
                            ValueType.COST,
                            AggregationLevel.MANUFACTURED_MATERIAL,
                            AggregationRole.TOTAL,
                            TsetCostCalculationElementType.MANUFACTURING_COSTS_3.fieldName,
                        ).fieldName,
                    ValueTypeFeDto.CO2 to
                        ValueFieldNameBuilder(
                            ValueType.CO2,
                            AggregationLevel.MANUFACTURED_MATERIAL,
                            AggregationRole.TOTAL,
                            TsetCO2eCalculationElementType.MANUFACTURING_CO2E.fieldName,
                        ).fieldName,
                ),
                null,
                mapOf(
                    ValueTypeFeDto.COST to TableOptionEnum.entries.map { it.displayableOptionName },
                    ValueTypeFeDto.CO2 to TableOptionEnum.entries.map { it.displayableOptionName },
                ),
            )

        Assertions.assertEquals(cards.keys, setOf("manufacturingCost"))
        Assertions.assertEquals(expectedCardConfig, cards.values.single())
    }

    fun verifyTables(tableConfigs: Map<TableName, TableConfigFeDto>) {
        val expectedTableKeys =
            TableOptionEnum.entries.map { it.displayableOptionName }
                .flatMap { tableOption ->
                    ValueTypeFeDto.entries.map { valueTypeFeDto ->
                        TableName("manufacturingCost", valueTypeFeDto, tableOption)
                    }
                }.toSet()
        Assertions.assertEquals(expectedTableKeys, tableConfigs.keys)

        val costColumnsCount = 4
        val cO2ColumnsCount = 4

        val costRowCount =
            mapOf(
                TableOptionEnum.PRODUCTION.displayableOptionName to 21,
                TableOptionEnum.ACTIVITY.displayableOptionName to 19,
            )
        val cO2RowCount =
            mapOf(
                TableOptionEnum.PRODUCTION.displayableOptionName to 14,
                TableOptionEnum.ACTIVITY.displayableOptionName to 8,
            )

        val costUpLevelRowCount = 2
        val cO2UpLevelRowCount = 4

        tableConfigs.forEach { (tableName, tableConfig) ->
            Assertions.assertTrue(tableConfig is FieldTableConfigFeDto)

            when (tableName.valueTypeFeDto) {
                ValueTypeFeDto.CO2 -> {
                    Assertions.assertEquals(cO2ColumnsCount, tableConfig.columns.size)
                    Assertions.assertEquals(cO2RowCount[tableName.option], tableConfig.rowDefinitions.size)
                    Assertions.assertEquals(cO2UpLevelRowCount, tableConfig.rows.size)
                }

                ValueTypeFeDto.COST -> {
                    Assertions.assertEquals(costColumnsCount, tableConfig.columns.size)
                    Assertions.assertEquals(costRowCount[tableName.option], tableConfig.rowDefinitions.size)
                    Assertions.assertEquals(costUpLevelRowCount, tableConfig.rows.size)
                }
            }

            tableConfig.columns.forEach { column ->
                Assertions.assertTrue(column is FieldTableColumnDefinitionFeDto)
                Assertions.assertTrue((column as FieldTableColumnDefinitionFeDto).options?.displayDesignation != null)
            }
        }
    }
}
