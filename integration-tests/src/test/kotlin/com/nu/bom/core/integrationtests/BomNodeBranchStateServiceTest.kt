package com.nu.bom.core.integrationtests

import com.nu.bom.core.api.ManufacturingQueryController
import com.nu.bom.core.api.ManufacturingUpdateController
import com.nu.bom.core.api.dtos.CreateManufacturingResult
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYearsUnit
import com.nu.bom.core.manufacturing.utils.ManufacturingCrudTestClient
import com.nu.bom.core.manufacturing.utils.ManufacturingWizardTestBuilder
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.MergeSourceType
import com.nu.bom.core.model.PartId
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.id
import com.nu.bom.core.mongoProjectId
import com.nu.bom.core.service.ManufacturingCalculationService
import com.nu.bom.core.service.PartService
import com.nu.bom.core.service.bomnode.SaveToSourceBranchService
import com.nu.bom.core.service.dto.DirtyStateHandling
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.NbkClient
import com.nu.bom.core.utils.toObjectId
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test", "lookup-init", "masterdata-init", "config-init")
@AutoConfigureWebTestClient
class BomNodeBranchStateServiceTest : NbkClient.With {
    @Autowired
    private lateinit var crudClient: ManufacturingCrudTestClient

    @Autowired
    private lateinit var calculationBuilder: ManufacturingWizardTestBuilder

    @Autowired
    override lateinit var nbkClient: NbkClient

    @Autowired
    private lateinit var saveToSourceBranchService: SaveToSourceBranchService

    @Autowired
    private lateinit var manufacturingQueryController: ManufacturingQueryController

    @Autowired
    private lateinit var manufacturingCalculationService: ManufacturingCalculationService

    @Autowired
    private lateinit var partService: PartService

    private lateinit var accessCheck: AccessCheck
    private lateinit var projectId: ProjectId
    private var branchId: BranchId = BranchId()
    private lateinit var bomNodeId: String
    private lateinit var partId: PartId
    private lateinit var createManufacturingResult: CreateManufacturingResult

    @BeforeEach
    fun setup() {
        val (ac, project) = nbkClient.setupWithProject(name = "Test Project", key = "BNBS")
        accessCheck = ac
        projectId = project.mongoProjectId()

        partId =
            partService
                .newPart(
                    designation = "TestPart",
                    number = "1234",
                    accessCheck = accessCheck,
                    projectId = projectId,
                ).mapNotNull { it._id }
                .block()!!

        createManufacturingResult =
            calculationBuilder
                .withStandardFields(partId.toHexString()) {
                    calculationTitle = Text("Main")
                    location = Text("tset.ref.classification.austria")
                    lifeTime = TimeInYears(10.0, TimeInYearsUnit.YEAR)
                    peakUsableProductionVolumePerYear = QuantityUnit(10000.0)
                    averageUsableProductionVolumePerYear = QuantityUnit(10000.0)
                    dimension = Dimension(Dimension.Selection.NUMBER)
                }.execute()
                .getOrThrow()

        bomNodeId = createManufacturingResult.bomNode.id

        branchId =
            manufacturingCrudTestClient
                .checkout(bomNodeId)
                .branch.id
                .toObjectId()!!
    }

    @AfterEach
    fun teardown() {
        nbkClient.cleanup()
    }

    @Test
    fun updateOpenMergesIfExistOnSourceBranch() {
        // create root
        val (parentNode, initialMainBranch, manufacturingId) =
            Triple(
                createManufacturingResult.bomNode.id.toObjectId()!!,
                createManufacturingResult.bomNode.branch.id
                    .toObjectId()!!,
                createManufacturingResult.bomNode.manufacturing
                    ?.id
                    .toObjectId()!!,
            )

        // variant 1
        val variantBranchDto =
            crudClient.saveAsPublicVariant(initialMainBranch.toHexString(), parentNode.toHexString(), "variant-1")

        // modify param on root and update
        val updatedBranch =
            updateAndPublish(
                initialMainBranch,
                parentNode,
                ManufacturingUpdateController.InputParameterApi(
                    name = "lifeTime",
                    entityId = manufacturingId.toHexString(),
                    type = "TimeInYears",
                    value = 40.toBigDecimal(),
                    unit = "YEAR",
                ),
            )
        val updatedBomNode =
            manufacturingQueryController
                .getManufacturing(
                    accessCheck,
                    parentNode,
                    updatedBranch.toHexString(),
                    null,
                    null,
                    false,
                    null,
                ).block()!!
        assertThat(
            updatedBomNode.manufacturing
                ?.getField("lifeTime")
                ?.value
                .toString()
                .toDouble(),
        ).isEqualTo(40.0)

        // check variant for changes
        val variantBomNode =
            manufacturingQueryController
                .getManufacturing(
                    accessCheck,
                    parentNode,
                    variantBranchDto.id,
                    null,
                    null,
                    true,
                    null,
                ).block()!!
        assertThat(variantBomNode.openMergesAvailable.size).isEqualTo(1)
        assertThat(variantBomNode.openMergesAvailable.firstOrNull()).isEqualTo(MergeSourceType.MASTER)
        assertThat(variantBomNode.branch.id).isEqualTo(variantBranchDto.id)
        assertThat(variantBomNode.title).isEqualTo("variant-1")

        // update
        val updatedCalculation =
            manufacturingCalculationService
                .mergeAndRemoveOpenMerges(
                    accessCheck,
                    BranchId(variantBomNode.branch.id),
                    MergeSourceType.MASTER,
                    parentNode,
                ).block()!!
        assertThat(updatedCalculation.result.openMergesAvailable.size).isEqualTo(0)

        val mergeResult =
            saveToSourceBranchService
                .saveToSourceBranch(
                    accessCheck,
                    parentNode,
                    updatedCalculation.branch!!.id,
                    DirtyStateHandling.FAIL_ON_DIRTY,
                ).block()

        val publishedBomNode =
            manufacturingQueryController
                .getManufacturing(
                    accessCheck,
                    parentNode,
                    mergeResult!!.id,
                    null,
                    null,
                    false,
                    null,
                ).block()!!
        assertThat(publishedBomNode.openMergesAvailable.size).isEqualTo(0)
    }

    private fun updateAndPublish(
        sourceBranch: BranchId,
        nodeToChange: BomNodeId,
        fieldUpdate: ManufacturingUpdateController.InputParameterApi,
    ): BranchId {
        val checkedOutSnapshot =
            crudClient.updateField(
                bomNodeId = nodeToChange.toHexString(),
                branch = sourceBranch.toHexString(),
                field = fieldUpdate,
            )
        val publishedSnapshot = crudClient.publish(nodeToChange.toHexString(), checkedOutSnapshot.branch.id)
        val publishedBranch = publishedSnapshot.branch
        return publishedBranch.id.toObjectId()!!
    }
}
