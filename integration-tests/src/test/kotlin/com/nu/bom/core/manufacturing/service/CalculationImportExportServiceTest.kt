package com.nu.bom.core.manufacturing.service

import com.nu.bom.core.api.dtos.ImportExportDto
import com.nu.bom.core.api.dtos.ManufacturingImportDto
import com.nu.bom.core.controller.ManufacturingParameters
import com.nu.bom.core.controller.MasterDataSelectorDto
import com.nu.bom.core.controller.NewEntityDto
import com.nu.bom.core.manufacturing.entities.BaseManufacturingFields
import com.nu.bom.core.manufacturing.entities.RawMaterialManual
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.ConfigIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.FieldWithResult
import com.nu.bom.core.manufacturing.fieldTypes.Json
import com.nu.bom.core.manufacturing.fieldTypes.MultiSelect
import com.nu.bom.core.manufacturing.testentities.TestManufacturing
import com.nu.bom.core.manufacturing.testentities.TestManufacturingStep
import com.nu.bom.core.manufacturing.testentities.TestManufacturingWithSub
import com.nu.bom.core.model.AccountMasterData
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.IMasterData
import com.nu.bom.core.model.MasterData
import com.nu.bom.core.model.MasterDataSelector
import com.nu.bom.core.model.configurations.SemanticVersion
import com.nu.bom.core.model.toBranchId
import com.nu.bom.core.model.toMongoBranchId
import com.nu.bom.core.model.toMongoProjectId
import com.nu.bom.core.projectId
import com.nu.bom.core.service.AccountMasterDataService
import com.nu.bom.core.service.BomTreeService
import com.nu.bom.core.service.ManufacturingCalculationService
import com.nu.bom.core.service.MasterDataService
import com.nu.bom.core.service.TestDataService
import com.nu.bom.core.service.imports.CalculationImportExportService
import com.nu.bom.core.technologies.manufacturings.alex.ManufacturingAluExtrusion
import com.nu.bom.core.technologies.manufacturings.cera.ManufacturingCeramicMold
import com.nu.bom.core.technologies.manufacturings.dfor.ManufacturingDieForging
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.user.withAccessCheck
import com.nu.bom.core.utils.AccountTestUtil
import com.nu.bom.core.utils.LoadTestEntityClasses
import org.assertj.core.api.Assertions.assertThat
import org.bson.types.ObjectId
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import reactor.core.publisher.Mono
import java.time.Instant
import com.nu.bom.core.model.BranchId as MongoBranchId
import com.nu.bom.core.service.configurations.AlExCostModuleTsetConfigurationService.Companion.TSET_CONFIGURATION_KEY as ALEX_CONFIG_KEY
import com.nu.bom.core.service.configurations.DForCostModuleTsetConfigurationService.Companion.TSET_CONFIGURATION_KEY as DFOR_CONFIG_KEY

@SpringBootTest
@ActiveProfiles("test", "disable-masterdata-mock", "disable-registry-mock")
@LoadTestEntityClasses(classes = [TestManufacturing::class, TestManufacturingWithSub::class, TestManufacturingStep::class])
class CalculationImportExportServiceTest {
    @Autowired
    private lateinit var masterDataService: MasterDataService

    @Autowired
    private lateinit var accountMasterDataService: AccountMasterDataService

    @Autowired
    private lateinit var calculationImportExportService: CalculationImportExportService

    @Autowired
    private lateinit var manufacturingCalculationService: ManufacturingCalculationService

    @Autowired
    private lateinit var bomTreeService: BomTreeService

    @Autowired
    private lateinit var accountUtil: AccountTestUtil

    @Autowired
    private lateinit var testDataService: TestDataService

    private lateinit var partId: ObjectId
    private lateinit var projectId: com.nu.bomrads.id.ProjectId
    private var bomNodeId: BomNodeId = BomNodeId()
    private lateinit var branchId: MongoBranchId
    private lateinit var mainBranchId: MongoBranchId

    private lateinit var manufacturingId: ObjectId

    private lateinit var accessCheck: AccessCheck

    @BeforeEach
    fun setup() {
        val result = accountUtil.setupWithProject("CalculationImportExportServiceTest", "CIEST")
        accessCheck = result.first
        partId = ObjectId.get()
        val project = result.second
        projectId = project.projectId()
        mainBranchId = project.mainBranch.id.toMongoBranchId()
    }

    @AfterEach
    fun teardown() {
        accountUtil.cleanup()
        masterDataService.deleteAll().block()
        testDataService.cleanup()
    }

    @Test
    fun `applyIncludingChildren is really recursive (see COST-81859)`() {
        val importDto =
            run {
                val root = createDummyManufacturingImportDto("root")
                val child = createDummyManufacturingImportDto("child")
                val grandchild = createDummyManufacturingImportDto("grandchild")
                child.children.add(grandchild)
                root.children.add(child)
                root
            }

        val initialVersion = importDto.version

        val applyExcludingChildren = { _: AccessCheck, dto: ManufacturingImportDto ->
            val newVersion =
                when (dto.name) {
                    "root" -> dto.version + 1
                    "child" -> dto.version + 2
                    "grandchild" -> dto.version + 3
                    else -> -1
                }
            Mono.just(dto.copy(version = newVersion))
        }

        val modifiedDto =
            CalculationImportExportService.Companion
                .applyIncludingChildren(
                    accessCheck,
                    importDto,
                    applyExcludingChildren,
                ).block()!!

        Assertions.assertEquals(initialVersion + 1, modifiedDto.version)
        val child = modifiedDto.children.single()
        Assertions.assertEquals(initialVersion + 2, child.version)
        val grandChild = child.children.single()
        Assertions.assertEquals(initialVersion + 3, grandChild.version)
    }

    @Test
    fun `exported calculation with account masterdata entity recreates entity on import if it does not exist`() {
        val material = saveMaterialAccountMasterData()

        prepareCalculation(material)

        manufacturingCalculationService
            .updateAndCalculate(
                ManufacturingParameters(bomNodeId = bomNodeId),
                accessCheck = accessCheck,
                branchId = branchId.toBranchId(),
            ).withAccessCheck(accessCheck)
            .block()!!

        val calcToImport =
            calculationImportExportService
                .getManufacturingImportDto(
                    accessCheck,
                    bomNodeId,
                    branchId,
                    true,
                ).block()!!

        val selector = getSelector(calcToImport)
        deleteMaterialAndVerify(selector)

        calculationImportExportService
            .prepareData(
                accessCheck,
                projectId.toMongoProjectId(),
                calcToImport.manufacturingDto,
                emptyList(),
                emptyList(),
                emptyList(),
                true,
            ).block()

        val maybeMaterial = masterDataService.getMasterData(accessCheck, selector).block()!!
        Assertions.assertTrue(maybeMaterial.exists())
        verifyCreatedMaterial(material, maybeMaterial.value!!)
    }

    @Test
    fun `exported calculation with masterdata entity recreates entity on import if it does not exist`() {
        var material = saveMaterialMasterData()

        prepareCalculation(material)

        manufacturingCalculationService
            .updateAndCalculate(
                ManufacturingParameters(bomNodeId = bomNodeId),
                accessCheck = accessCheck,
                branchId = branchId.toBranchId(),
            ).withAccessCheck(accessCheck)
            .block()!!

        val calcToImport =
            calculationImportExportService
                .getManufacturingImportDto(
                    accessCheck,
                    bomNodeId,
                    branchId,
                    true,
                ).block()!!

        val selector = getSelector(calcToImport)
        material = deletedMasterdata()

        calculationImportExportService
            .prepareData(
                accessCheck,
                projectId.toMongoProjectId(),
                calcToImport.manufacturingDto,
                emptyList(),
                emptyList(),
                emptyList(),
                true,
            ).block()

        val maybeMaterial = masterDataService.getMasterData(accessCheck, selector).block()!!
        Assertions.assertTrue(maybeMaterial.exists())
        verifyCreatedMaterial(material, maybeMaterial.value!!)
    }

    private fun deletedMasterdata(): MasterData {
        masterDataService.deleteAll().block()
        val material = saveMaterialMasterData(false)
        Assertions.assertFalse(
            masterDataService
                .getLatestMasterDataByCompositeKey(accessCheck, material.selector, activeOnly = false)
                .block()!!
                .active!!,
        )
        return material
    }

    @Test
    fun `exported calculation with masterdata entity recovers entity on import if it is soft deleted`() {
        val material = saveMaterialAccountMasterData()

        prepareCalculation(material)

        manufacturingCalculationService
            .updateAndCalculate(
                ManufacturingParameters(bomNodeId = bomNodeId),
                accessCheck = accessCheck,
                branchId = branchId.toBranchId(),
            ).withAccessCheck(accessCheck)
            .block()!!

        val calcToImport =
            calculationImportExportService
                .getManufacturingImportDto(
                    accessCheck,
                    bomNodeId,
                    branchId,
                    recursive = true,
                ).block()!!

        val selector = getSelector(calcToImport)
        softDeleteAccountMaterialAndVerify(selector)

        calculationImportExportService
            .prepareData(
                accessCheck,
                projectId.toMongoProjectId(),
                calcToImport.manufacturingDto,
                emptyList(),
                emptyList(),
                emptyList(),
                true,
            ).block()

        val maybeMaterial = masterDataService.getMasterData(accessCheck, selector).block()!!
        Assertions.assertTrue(maybeMaterial.exists())
        verifyCreatedMaterial(material, maybeMaterial.value!!)
    }

    @Test
    fun `imported tree gets all configurations assigned`() {
        val calculationToCreate = createManufacturingTreeOfCostModules(emptyList())
        val preparedData =
            calculationImportExportService
                .prepareData(
                    accessCheck,
                    projectId.toMongoProjectId(),
                    calculationToCreate,
                    emptyList(),
                    emptyList(),
                    emptyList(),
                    true,
                ).block()!!
        assertThat(preparedData).isNotNull
        assertThat(preparedData.dto.initialFieldsWithResult).size().isEqualTo(1)
        var config = preparedData.dto.initialFieldsWithResult[0].result.res as Json.Jsondata<*>
        assertThat(config.data)
            .isEqualTo(ConfigurationIdentifier.tset(DFOR_CONFIG_KEY, SemanticVersion.initialVersion()))
        assertThat(preparedData.dto.children).size().isEqualTo(1)
        val child = preparedData.dto.children[0]
        config = child.initialFieldsWithResult[0].result.res as Json.Jsondata<*>
        assertThat(config.data)
            .isEqualTo(ConfigurationIdentifier.tset(ALEX_CONFIG_KEY, SemanticVersion.initialVersion()))
    }

    @Test
    fun `wrong technology does not get a configuration`() {
        val calculationToCreate = createWrongRootCostModules()
        val preparedData =
            calculationImportExportService
                .prepareData(
                    accessCheck,
                    projectId.toMongoProjectId(),
                    calculationToCreate,
                    emptyList(),
                    emptyList(),
                    emptyList(),
                    true,
                ).block()!!
        assertThat(preparedData).isNotNull
        assertThat(preparedData.dto.initialFieldsWithResult).isEmpty()
    }

    @Test
    fun `initial config is kept`() {
        val configIdentifier = ConfigIdentifier(ConfigurationIdentifier("INTERNAL", SemanticVersion.initialVersion()))
        val initialFieldsWithResults =
            listOf(
                FieldWithResult(
                    result = configIdentifier,
                    name =
                        FieldKey(
                            name = BaseManufacturingFields::costModuleConfigurationIdentifier.name,
                            entityId = "entityId",
                            entityType = "DFOR",
                            type = "com.nu.bom.core.manufacturing.fieldTypes.Json",
                            version = 1,
                        ),
                ),
            )
        val calculationToCreate = createManufacturingTreeOfCostModules(initialFieldsWithResults)
        val preparedData =
            calculationImportExportService
                .prepareData(
                    accessCheck,
                    projectId.toMongoProjectId(),
                    calculationToCreate,
                    emptyList(),
                    emptyList(),
                    emptyList(),
                    true,
                ).block()!!
        assertThat(preparedData).isNotNull
        assertThat(preparedData.dto.initialFieldsWithResult).size().isEqualTo(1)
        val config = preparedData.dto.initialFieldsWithResult[0].result.res as Json.Jsondata<*>
        assertThat(config.data)
            .isEqualTo(configIdentifier.res.data)
    }

    private fun createManufacturingTreeOfCostModules(initialFieldsWithResults: List<FieldWithResult>) =
        ManufacturingImportDto(
            id = ObjectId().toHexString(),
            type = Entities.MANUFACTURING,
            name = "root",
            ref = ManufacturingDieForging::class.java.simpleName,
            version = 1,
            part = null,
            partName = "",
            data = mapOf(),
            className = ManufacturingDieForging::class.java.simpleName,
            fieldsWithResult = emptyList(),
            initialFieldsWithResult = initialFieldsWithResults,
            children =
                mutableListOf(
                    ManufacturingImportDto(
                        id = ObjectId().toHexString(),
                        type = Entities.MANUFACTURING,
                        name = "root",
                        ref = ManufacturingAluExtrusion::class.java.simpleName,
                        version = 1,
                        part = null,
                        partName = "",
                        data = mapOf(),
                        className = ManufacturingAluExtrusion::class.java.simpleName,
                        fieldsWithResult = emptyList(),
                        initialFieldsWithResult = emptyList(),
                        children = mutableListOf(),
                        model = null,
                        createdByField = null,
                        providerField = null,
                        createdByMocked = false,
                        createdOnBranch = null,
                        masterDataKey = null,
                        year = 2024,
                        protectedAt = Instant.now(),
                        protectedBy = null,
                    ),
                ),
            model = null,
            createdByField = null,
            providerField = null,
            createdByMocked = false,
            createdOnBranch = null,
            masterDataKey = null,
            year = 2024,
            protectedAt = Instant.now(),
            protectedBy = null,
        )

    private fun createWrongRootCostModules() =
        ManufacturingImportDto(
            id = ObjectId().toHexString(),
            type = Entities.MANUFACTURING,
            name = "root",
            ref = ManufacturingCeramicMold::class.java.simpleName,
            version = 1,
            part = null,
            partName = "",
            data = mapOf(),
            className = ManufacturingCeramicMold::class.java.simpleName,
            fieldsWithResult = emptyList(),
            initialFieldsWithResult = emptyList(),
            children = mutableListOf(),
            model = null,
            createdByField = null,
            providerField = null,
            createdByMocked = false,
            createdOnBranch = null,
            masterDataKey = null,
            year = 2024,
            protectedAt = Instant.now(),
            protectedBy = null,
        )

    private fun saveMaterialAccountMasterData() =
        masterDataService
            .saveAccountMasterData(
                AccountMasterData(
                    accessCheck.asAccountId(),
                    MasterData(
                        selector =
                            MasterDataSelector(
                                type = MasterDataType.RAW_MATERIAL_MANUAL,
                                key = "MaterialBar",
                                year = 2019,
                                location = "Global",
                            ),
                        data =
                            mapOf(
                                "technology" to
                                    MultiSelect(
                                        listOf(MultiSelect.Selectable("TechnologyX", "TECHX")),
                                    ),
                            ),
                    ),
                    currentGlobal = ObjectId.get(),
                    historicGlobal = ObjectId.get(),
                ),
            ).block()!!

    private fun saveMaterialMasterData(active: Boolean = true) =
        masterDataService
            .saveGlobalMasterData(
                MasterData(
                    selector =
                        MasterDataSelector(
                            type = MasterDataType.RAW_MATERIAL_MANUAL,
                            key = "MaterialBar",
                            year = 2019,
                            location = "Global",
                        ),
                    data =
                        mapOf(
                            "technology" to
                                MultiSelect(
                                    listOf(MultiSelect.Selectable("TechnologyX", "TECHX")),
                                ),
                        ),
                    active = active,
                ),
            ).block()!!

    private fun prepareCalculation(material: IMasterData) {
        createBranchAndBom()
        addManufacturing()
        addMaterial(material.selector)
    }

    private fun deleteMaterialAndVerify(selector: MasterDataSelector) {
        masterDataService.deleteAll().block()
        Assertions.assertNull(
            masterDataService
                .getLatestAccountMasterDataByCompositeKey(accessCheck, selector, activeOnly = false)
                .block(),
        )
    }

    private fun softDeleteAccountMaterialAndVerify(selector: MasterDataSelector) {
        accountMasterDataService.deleteMasterdata(accessCheck, selector).block()
        Assertions.assertFalse(
            masterDataService
                .getLatestAccountMasterDataByCompositeKey(accessCheck, selector, activeOnly = false)
                .block()!!
                .active!!,
        )
    }

    private fun verifyCreatedMaterial(
        originalMaterial: IMasterData,
        addedMaterial: IMasterData,
    ) {
        Assertions.assertEquals(originalMaterial.type, addedMaterial.type)
        Assertions.assertEquals(originalMaterial.key, addedMaterial.key)
        Assertions.assertEquals(originalMaterial.location, addedMaterial.location)
        Assertions.assertEquals(originalMaterial.category, addedMaterial.category)
    }

    private fun getSelector(calcToImport: ImportExportDto) =
        calcToImport.manufacturingDto.children[0].masterDataKey!!.let {
            MasterDataSelector(
                type = it.type,
                key = it.key,
                year = it.year,
                location = it.location,
            )
        }

    private fun createBranchAndBom() {
        val snapshot =
            testDataService.createBomWithEmptySnapshot(
                accessCheck = accessCheck,
                projectId = projectId,
                branchId = mainBranchId,
            )

        branchId = snapshot.bomradBranchId().toMongoBranchId()
        bomNodeId = snapshot.bomNodeId()
    }

    private fun addManufacturing(
        masterDataSelectorDto: MasterDataSelectorDto? = null,
        initialFields: Map<String, FieldResult<*, *>>? = null,
    ): BomNodeSnapshot? {
        manufacturingId =
            testDataService
                .addManufacturingEntity(
                    accessCheck = accessCheck,
                    bomNodeId = bomNodeId,
                    branchId = branchId,
                    newEntityDto =
                        NewEntityDto(
                            type = Entities.MANUFACTURING,
                            clazz = TestManufacturing::class.qualifiedName!!,
                            name = "TestManufacturing",
                            args =
                                hashMapOf(
                                    "partId" to partId.toString(),
                                    "isPart" to true,
                                ),
                            masterDataSelector = masterDataSelectorDto,
                            initialFields = initialFields,
                        ),
                ).map { it._id }
                .block()!!

        return bomTreeService.checkout(accessCheck, nodeId = bomNodeId, branchId = branchId).block()
    }

    private fun addMaterial(
        selector: MasterDataSelector,
        initialFields: Map<String, FieldResult<*, *>>? = null,
    ) {
        testDataService
            .addManufacturingEntity(
                accessCheck = accessCheck,
                bomNodeId = bomNodeId,
                branchId = branchId,
                newEntityDto =
                    NewEntityDto(
                        type = Entities.MATERIAL,
                        clazz = RawMaterialManual::class.qualifiedName!!,
                        name = "MaterialBar",
                        parentId = manufacturingId.toString(),
                        masterDataSelector = MasterDataSelectorDto(selector.key, selector.location),
                        initialFields = initialFields,
                    ),
                masterDataType = selector.type,
            ).map { it._id }
            .block()!!
    }

    companion object {
        private fun createDummyManufacturingImportDto(name: String = ""): ManufacturingImportDto =
            ManufacturingImportDto(
                id = "",
                type = null,
                name = name,
                ref = "",
                version = 0,
                part = null,
                partName = null,
                className = null,
                fieldsWithResult = emptyList(),
                initialFieldsWithResult = emptyList(),
                children = mutableListOf(),
                model = null,
                createdByField = null,
                providerField = null,
                createdByMocked = null,
                createdOnBranch = null,
                masterDataKey = null,
                year = 2025,
                protectedAt = null,
                protectedBy = null,
            )
    }
}
