package com.nu.bom.core.integrationtests

import com.nu.bom.core.api.dtos.BomNodeDto
import com.nu.bom.core.api.dtos.ManufacturingDto
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.service.TestDataService
import com.nu.bom.core.utils.NbkClient
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@AutoConfigureWebTestClient
class ManufacturingEntityRearrangementControllerIT : NbkClient.With {
    companion object {
        val logger: Logger = LoggerFactory.getLogger(ManufacturingEntityRearrangementControllerIT::class.java)
    }

    private lateinit var bomNode: BomNodeDto
    private lateinit var branch: String
    private lateinit var bomMapping: Map<String, String>

    @Autowired
    override lateinit var nbkClient: NbkClient

    @Autowired
    private lateinit var testDataService: TestDataService

    @BeforeEach
    fun setup() {
        nbkClient.setupWithProject(name = "Test Project", key = "MERC")

        this.bomNode =
            wizardBuilder
                .createStandard()
                .bomNode
        branch = manufacturingCrudTestClient.checkoutBranch(bomNodeId = bomNode.id)
        this.bomMapping = setupSteps() + setupMachines()
    }

    fun setupSteps(): Map<String, String> {
        assertNotNull(bomNode.manufacturing)

        var prevId = bomNode.manufacturing!!.id

        val map = mutableMapOf(bomNode.manufacturing!!.name to bomNode.manufacturing!!.id)

        for (i in 1..3) {
            val updatedBomNode = manufacturingCrudTestClient.addStep(bomNode.id, branch, prevId, name = "MyStep $i")
            logger.debug("updatedBomNode: {}", updatedBomNode)
            assertEquals(
                "ManualManufacturing",
                updatedBomNode.manufacturing!!.name,
                "bomNode.manufacturing.name",
            )

            assertNotNull(updatedBomNode.subNodes, "bomNode.subNodes")

            val childStep = findByName(updatedBomNode.manufacturing!!, "MyStep $i")
            assertNotNull(childStep, "bomNode.manufacturing.children")
            assertNotNull(childStep?.id, "bomNode.manufacturing.children.id")
            assertEquals("MyStep $i", childStep?.name, "bomNode.manufacturing.children.name")

            assertEquals(Entities.MANUFACTURING_STEP, childStep!!.type)
            map[childStep.name] = childStep.id
            prevId = childStep.id
            this.bomNode = updatedBomNode
        }
        val origSteps = nameList(bomNode.manufacturing!!)
        assertEquals(
            listOf("ManualManufacturing", "MyStep 1", "MyStep 2", "MyStep 3"),
            origSteps,
            "manufacturing.steps",
        )
        return map
    }

    fun setupMachines(): Map<String, String> {
        assertNotNull(bomNode.manufacturing) { "bomNode.manufacturing" }

        var step1 = findByName(bomNode.manufacturing!!, "MyStep 1")
        assertNotNull(step1) { "bomNode.manufacturing.step1" }

        val map = mutableMapOf<String, String>()

        for (i in 1..3) {
            val updatedBomNode = manufacturingCrudTestClient.addMachine(bomNode.id, branch, step1!!.id, "MyMachine $i")
            logger.debug("updatedBomNode: {}", updatedBomNode)
            assertEquals(
                "ManualManufacturing",
                updatedBomNode.manufacturing!!.name,
                "bomNode.manufacturing.name",
            )

            assertNotNull(updatedBomNode.subNodes, "bomNode.subNodes")

            step1 = findByName(updatedBomNode.manufacturing!!, "MyStep 1")
            assertNotNull(step1) { "bomNode.manufacturing.step1" }

            val childMachine = findChildByName(step1!!, "MyMachine $i")
            assertNotNull(childMachine, "bomNode.manufacturing.step1.machine")
            assertNotNull(childMachine?.id, "bomNode.manufacturing.step1.machine.id")
            assertEquals("MyMachine $i", childMachine?.name, "bomNode.manufacturing.step1.machine.name")

            assertEquals(Entities.MACHINE, childMachine!!.type)
            map[childMachine.name] = childMachine.id
            this.bomNode = updatedBomNode
        }
        val origMachines = childNameList(step1!!, Entities.MACHINE)
        assertEquals(
            listOf("MyMachine 1", "MyMachine 2", "MyMachine 3"),
            origMachines,
            "manufacturing.steps1.machines",
        )
        return map
    }

    fun findByName(
        dto: ManufacturingDto,
        name: String,
        filter: (ManufacturingDto) -> Boolean = { it.type == Entities.MANUFACTURING || it.type == Entities.MANUFACTURING_STEP },
    ): ManufacturingDto? {
        return if (dto.name == name) {
            dto
        } else {
            if (!dto.children.none(filter)) {
                findByName(dto.children.filter(filter)[0], name)
            } else {
                null
            }
        }
    }

    fun findChildByName(
        dto: ManufacturingDto,
        name: String,
    ): ManufacturingDto? {
        return dto.children.find { it.name == name }
    }

    fun nameList(
        dto: ManufacturingDto,
        filter: (ManufacturingDto) -> Boolean = { it.type == Entities.MANUFACTURING || it.type == Entities.MANUFACTURING_STEP },
    ): List<String> {
        return if (!dto.children.none(filter)) {
            listOf(dto.name) + nameList(dto.children.filter(filter)[0])
        } else {
            listOf(dto.name)
        }
    }

    fun childNameList(
        dto: ManufacturingDto,
        typeFilter: Entities,
    ): List<String> {
        return dto.children
            .filter { it.type == typeFilter }
            .map { it.name }
    }

    fun testMove(
        node: String,
        newParent: String,
        expectedResult: List<String>,
    ) {
        val updatedBom =
            manufacturingCrudTestClient.moveEntity(bomNode.id, bomMapping[node]!!, bomMapping[newParent]!!, branch)
        assertNotNull(updatedBom, "updatedBom")
        assertNotNull(updatedBom!!.manufacturing, "updatedBom.manufacturing")
        val steps = nameList(updatedBom.manufacturing!!)
        assertEquals(expectedResult, steps, "manufacturing.steps")
    }

    fun testReorder(
        entity: String,
        reorderBeforeEntity: String?,
        reorderAfterEntity: String?,
        expectedResult: List<String>,
    ) {
        val updatedBom =
            manufacturingCrudTestClient.reorder(
                bomNodeId = bomNode.id,
                entityId = bomMapping[entity]!!,
                parentId = bomMapping["MyStep 1"]!!,
                insertBeforeEntityId = reorderBeforeEntity?.let { bomMapping[reorderBeforeEntity]!! },
                insertAfterEntityId = reorderAfterEntity?.let { bomMapping[reorderAfterEntity]!! },
                branch = branch,
            )
        assertNotNull(updatedBom, "updatedBom")
        assertNotNull(updatedBom!!.manufacturing, "updatedBom.manufacturing")

        val step1 = findByName(updatedBom.manufacturing!!, "MyStep 1")
        assertNotNull(step1) { "bomNode.manufacturing.step1" }

        val machines = childNameList(step1!!, Entities.MACHINE)
        assertEquals(expectedResult, machines, "manufacturing.step1.machines")
    }

    /** Step rearrangement tests */

    @Test
    fun checkSelfParenting() {
        testMove("MyStep 1", "MyStep 1", listOf("ManualManufacturing", "MyStep 1", "MyStep 2", "MyStep 3"))
    }

    @Test
    fun checkNonRealMoving() {
        testMove("MyStep 2", "MyStep 1", listOf("ManualManufacturing", "MyStep 1", "MyStep 2", "MyStep 3"))
    }

    @Test
    fun checkMoveToEnd() {
        testMove("MyStep 2", "MyStep 3", listOf("ManualManufacturing", "MyStep 1", "MyStep 3", "MyStep 2"))
    }

    @Test
    fun checkMoveFromStartToEnd() {
        testMove("MyStep 1", "MyStep 3", listOf("ManualManufacturing", "MyStep 2", "MyStep 3", "MyStep 1"))
    }

    @Test
    fun checkMoveToStart() {
        testMove(
            "MyStep 2",
            "ManualManufacturing",
            listOf("ManualManufacturing", "MyStep 2", "MyStep 1", "MyStep 3"),
        )
    }

    /** Step child entity reorder tests */

    @Test
    fun checkReorderBeforeSimple() {
        testReorder(
            entity = "MyMachine 2",
            reorderBeforeEntity = "MyMachine 1",
            reorderAfterEntity = null,
            expectedResult = listOf("MyMachine 2", "MyMachine 1", "MyMachine 3"),
        )
    }

    @Test
    fun checkReorderAfterSimple() {
        testReorder(
            entity = "MyMachine 1",
            reorderBeforeEntity = null,
            reorderAfterEntity = "MyMachine 2",
            expectedResult = listOf("MyMachine 2", "MyMachine 1", "MyMachine 3"),
        )
    }

    @Test
    fun checkReorderBeforeSelf() {
        testReorder(
            entity = "MyMachine 1",
            reorderBeforeEntity = "MyMachine 1",
            reorderAfterEntity = null,
            expectedResult = listOf("MyMachine 1", "MyMachine 2", "MyMachine 3"),
        )
    }

    @Test
    fun checkReorderAfterSelf() {
        testReorder(
            entity = "MyMachine 1",
            reorderBeforeEntity = null,
            reorderAfterEntity = "MyMachine 1",
            expectedResult = listOf("MyMachine 1", "MyMachine 2", "MyMachine 3"),
        )
    }

    @Test
    fun checkReorderBeforeAlreadyBefore() {
        testReorder(
            entity = "MyMachine 1",
            reorderBeforeEntity = "MyMachine 2",
            reorderAfterEntity = null,
            expectedResult = listOf("MyMachine 1", "MyMachine 2", "MyMachine 3"),
        )
    }

    @Test
    fun checkReorderAfterAlreadyAfter() {
        testReorder(
            entity = "MyMachine 2",
            reorderBeforeEntity = null,
            reorderAfterEntity = "MyMachine 1",
            expectedResult = listOf("MyMachine 1", "MyMachine 2", "MyMachine 3"),
        )
    }

    @Test
    fun checkReorderToEnd() {
        testReorder(
            entity = "MyMachine 1",
            reorderBeforeEntity = null,
            reorderAfterEntity = "MyMachine 3",
            expectedResult = listOf("MyMachine 2", "MyMachine 3", "MyMachine 1"),
        )
    }

    @Test
    fun checkReorderToStart() {
        testReorder(
            entity = "MyMachine 3",
            reorderBeforeEntity = "MyMachine 1",
            reorderAfterEntity = null,
            expectedResult = listOf("MyMachine 3", "MyMachine 1", "MyMachine 2"),
        )
    }

    @AfterEach
    fun teardown() {
        testDataService.cleanup()
        nbkClient.cleanup()
    }
}
