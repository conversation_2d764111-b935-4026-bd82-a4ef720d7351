package com.nu.bom.core.service

import com.nu.bom.core.api.ManufacturingUpdateController
import com.nu.bom.core.api.dtos.BomNodeDto
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostMaterialUsage
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetProcurementType
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CustomProcurementType
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYearsUnit
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.TsetDefaultSkillType
import com.nu.bom.core.manufacturing.fieldTypes.mdKeyAsText
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.MAIN_BRANCH
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.mongoBranchId
import com.nu.bom.core.mongoProjectId
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.NbkClient
import com.tset.bom.clients.nuledge.FieldParameterDto
import com.tset.common.testing.IdNaming
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import java.math.BigDecimal

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test", "masterdata-init", "lookup-init", "config-init")
@AutoConfigureWebTestClient
class BomPublishIT : NbkClient.With {
    @Autowired
    override lateinit var nbkClient: NbkClient

    @Autowired
    private lateinit var testDataService: TestDataService

    private lateinit var access: AccessCheck

    private lateinit var projectId: ProjectId
    private lateinit var mainBranchId: BranchId

    private val branches =
        object : IdNaming<BranchId>() {
            override fun createNew(): BranchId = BranchId()

            fun set(
                name: String,
                idAsString: String?,
            ) {
                if (idAsString != null) {
                    set(name, BranchId(idAsString))
                }
            }

            fun assertName(
                name: String,
                idAsString: String?,
            ) {
                if (idAsString != null) {
                    assertName(name, BranchId(idAsString))
                }
            }
        }

    @BeforeEach
    fun setup() {
        nbkClient.cleanup()
        testDataService.cleanup()
        branches.clear()
        val result =
            nbkClient.setupWithProject(
                name = "Test Project",
                key = "BPIT",
            )
        access = result.first
        projectId = result.second.mongoProjectId()
        mainBranchId = result.second.mongoBranchId()
        branches.set("main-branch", mainBranchId)
    }

    @AfterEach
    fun teardown() {
        nbkClient.cleanup()
    }

    @Test
    fun publish1Layer() {
        // Create Root
        val (bomNodeWithStep, _) = rootWithStep(initialCycleTime = Time(1.0, TimeUnits.SECOND))
        val rootNodeId = bomNodeWithStep.id
        val rootStepId = bomNodeWithStep.manufacturing!!.children.find { it.type == Entities.MANUFACTURING_STEP }!!.id

        // Add Sub
        val bomNodeAfterPublish = addSubAndPublish(bomNodeWithStep, rootStepId)
        val subNodeId = bomNodeAfterPublish.subNodes[0].bomNodeId
        val subNode = manufacturingCrudTestClient.getBomNode(subNodeId)

        // Add Step+Labor to Sub
        val subWithStep =
            addStep(
                subNodeId,
                subNode?.manufacturing!!.id,
                subNode.branch.id,
                initialCycleTime = Time(1.0, TimeUnits.SECOND),
            )
        val subStepId = subWithStep.manufacturing!!.children.find { it.type == Entities.MANUFACTURING_STEP }!!.id
        val subNodeWithStepAndLabor = addLabor(subNodeId, subStepId, subWithStep.branch.id)

        val rootNodeBeforeSubPublish = manufacturingCrudTestClient.getBomNode(rootNodeId)!!
        val rootCostsBeforeSubPublish = getCosts(rootNodeBeforeSubPublish)

        // Publish Sub
        val publishedSubBomNodeWithStepAndLabor = publish(subNodeWithStepAndLabor)

        Assertions.assertNotNull(publishedSubBomNodeWithStepAndLabor, "publishedSubBomNodeWithStepAndLabor")
        Assertions.assertEquals(
            0,
            publishedSubBomNodeWithStepAndLabor.subNodes.size,
            "publishedSubBomNodeWithStepAndLabor.subNodes.size",
        )

        val rootNodeAfterSubPublish = manufacturingCrudTestClient.getBomNode(rootNodeId)!!
        val rootCostsAfterSubPublish = getCosts(rootNodeAfterSubPublish)

        Assertions.assertNotNull(rootNodeAfterSubPublish, "rootNodeAfterSubPublish")
        Assertions.assertEquals(1, rootNodeAfterSubPublish.subNodes.size, "rootNodeAfterSubPublish.subNodes.size")

        // Root node had 0 costs before and has costs > 0 after sub changes published
        Assertions.assertEquals(0.0, rootCostsBeforeSubPublish, "rootCostsBeforeSubPublish $rootCostsBeforeSubPublish")
        Assertions.assertNotEquals(0.0, rootCostsAfterSubPublish, "rootCostsAfterSubPublish $rootCostsAfterSubPublish")
    }

    @ParameterizedTest
    @ValueSource(booleans = [ true, false ])
    fun renameDescendantsShouldWork(renameDescendants: Boolean) {
        // Create Root1
        val (parentNode, nodeAfterCreation) = rootWithStep("Parent")
        val root1NodeId = parentNode.id
        val root1StepId = parentNode.manufacturing!!.children.find { it.type == Entities.MANUFACTURING_STEP }!!.id
        branches.set("parent-branch", parentNode.branch.id)
        branches.assertName("main-branch", nodeAfterCreation.branch.id)

        // Add Sub
        val bomNodeAfterPublish = addSubAndPublish(parentNode, root1StepId)
        val subNodeId1 = bomNodeAfterPublish.subNodes[0].bomNodeId
        branches.assertName("main-branch", bomNodeAfterPublish.branch.id)
        val subNode1 = manufacturingCrudTestClient.getBomNode(subNodeId1)!!

        val branch =
            manufacturingCrudTestClient.saveAsPublicVariant(
                bomNodeId = parentNode.id,
                branchId = subNode1.branch.id,
                title = "other-branch",
                renameDescendants = renameDescendants,
            )
        Assertions.assertEquals("other-branch", branch.name, "branch.name (==title)")
        Assertions.assertEquals(true, branch.global, "branch.global")
        val rootNodeAfter = manufacturingCrudTestClient.getBomNode(root1NodeId, branch.id)!!
        val subNodeAfter = manufacturingCrudTestClient.getBomNode(subNodeId1, branch.id)!!
        Assertions.assertEquals("other-branch", rootNodeAfter.title, "rootNodeAfter.title")
        Assertions.assertEquals(if (renameDescendants) "other-branch" else subNode1.title, subNodeAfter.title, "subNodeAfter.title")

        val copyBranch =
            manufacturingCrudTestClient.copyBranch(
                branch.id,
                "copy-of-branch",
                root1NodeId,
                renameDescendants = renameDescendants,
            )
        val rootNodeAfter2 = manufacturingCrudTestClient.getBomNode(root1NodeId, copyBranch.id)!!
        val subNodeAfter2 = manufacturingCrudTestClient.getBomNode(subNodeId1, copyBranch.id)!!
        Assertions.assertEquals("copy-of-branch", rootNodeAfter2.title, "rootNodeAfter2.title")
        Assertions.assertEquals(if (renameDescendants) "copy-of-branch" else subNode1.title, subNodeAfter2.title, "subNodeAfter2.title")

        val renamedNode = manufacturingCrudTestClient.renameNode(root1NodeId, "renamed", copyBranch.id, renameDescendants)
        val rootNodeAfter3 = manufacturingCrudTestClient.getBomNode(root1NodeId, renamedNode.branch.id)!!
        val subNodeAfter3 = manufacturingCrudTestClient.getBomNode(subNodeId1, renamedNode.branch.id)!!
        Assertions.assertEquals("renamed", rootNodeAfter3.title, "rootNodeAfter3.title")
        Assertions.assertEquals(if (renameDescendants) "renamed" else subNode1.title, subNodeAfter3.title, "subNodeAfter3.title")
    }

    @Test
    fun publish2Layers() {
        // Create Root
        val (bomNodeWithStep, _) = rootWithStep(initialCycleTime = Time(1.0, TimeUnits.SECOND))
        val rootNodeId = bomNodeWithStep.id
        val rootStepId = bomNodeWithStep.manufacturing!!.children.find { it.type == Entities.MANUFACTURING_STEP }!!.id

        // Add Sub
        val bomNodeAfterPublish = addSubAndPublish(bomNodeWithStep, rootStepId)
        val subNodeId1 = bomNodeAfterPublish.subNodes[0].bomNodeId
        val subNode1 = manufacturingCrudTestClient.getBomNode(subNodeId1)!!

        // Add Another Sub
        val subWithStep = addStep(subNode1)
        val subBomNodeAfterPublish = addSubAndPublish(subWithStep)
        val subNodeId2 = subBomNodeAfterPublish.subNodes[0].bomNodeId
        val subNode2 = manufacturingCrudTestClient.getBomNode(subNodeId2)!!

        // Add Step + Labor to last sub
        val sub2WithStep = addStep(subNode2, initialCycleTime = Time(1.0, TimeUnits.SECOND))
        val subNodeWithStepAndLabor = addLabor(sub2WithStep)

        val rootNodeBeforeSubPublish = manufacturingCrudTestClient.getBomNode(rootNodeId)!!
        val rootCostsBeforeSubPublish = getCosts(rootNodeBeforeSubPublish)

        // Publish Sub
        val publishLastSub = publish(subNodeWithStepAndLabor)

        Assertions.assertNotNull(publishLastSub, "publishedSubBomNodeWithStepAndLabor")
        Assertions.assertEquals(0, publishLastSub.subNodes.size, "publishedSubBomNodeWithStepAndLabor.subNodes.size")

        val rootNodeAfterSubPublish = manufacturingCrudTestClient.getBomNode(rootNodeId)!!
        val rootCostsAfterSubPublish = getCosts(rootNodeAfterSubPublish)

        Assertions.assertNotNull(rootNodeAfterSubPublish, "rootNodeAfterSubPublish")
        Assertions.assertEquals(1, rootNodeAfterSubPublish.subNodes.size, "rootNodeAfterSubPublish.subNodes.size")

        // Root node had 0 costs before and has costs > 0 after sub changes published
        Assertions.assertEquals(0.0, rootCostsBeforeSubPublish, "rootCostsBeforeSubPublish $rootCostsBeforeSubPublish")
        Assertions.assertNotEquals(0.0, rootCostsAfterSubPublish, "rootCostsAfterSubPublish $rootCostsAfterSubPublish")
    }

    @Test
    fun concurrentChanges() {
        // Create Root
        val (bomNodeWithStep, _) = rootWithStep()
        val rootNodeId = bomNodeWithStep.id
        val rootStepId = bomNodeWithStep.manufacturing!!.children.find { it.type == Entities.MANUFACTURING_STEP }!!.id

        // Add Sub
        val bomNodeAfterPublish = addSubAndPublish(bomNodeWithStep, rootStepId)
        val subNodeId = bomNodeAfterPublish.subNodes[0].bomNodeId
        val subNode = manufacturingCrudTestClient.getBomNode(subNodeId)

        // Add Step+Labor to Sub
        val subWithStep = addStep(subNodeId, subNode?.manufacturing!!.id, subNode.branch.id)
        val subStepId = subWithStep.manufacturing!!.children.find { it.type == Entities.MANUFACTURING_STEP }!!.id
        val subNodeWithStepAndLabor = addLabor(subNodeId, subStepId, subWithStep.branch.id)

        // Publish Sub
        val publishedSubBomNodeWithStepAndLabor = publish(subNodeWithStepAndLabor)

        // Update Sub (-> creates unsaved changed branch) and get Volume used for checked out version
        val updatedSub = updateField(publishedSubBomNodeWithStepAndLabor, "scrapRate", "Rate", 0.2, "PIECE")
        val subVolume = getField(updatedSub, "peakUsableProductionVolumePerYear")

        // Update and Publish Volume of Root
        val rootNode = manufacturingCrudTestClient.getBomNode(rootNodeId)!!
        val updatedRoot = updateField(rootNode, "peakUsableProductionVolumePerYear", "Pieces", 1, "PIECE")
        publish(updatedRoot)

        // Update Sub again and check volume now
        val updatedSubAgain = updateField(updatedSub, "scrapRate", "Rate", 0.3, null)
        val subVolumeAfterSecondUpdate = getField(updatedSubAgain, "peakUsableProductionVolumePerYear")

        Assertions.assertEquals(subVolume, subVolumeAfterSecondUpdate)
    }

    private fun updateField(
        node: BomNodeDto,
        fieldName: String,
        type: String,
        value: Any,
        unit: String?,
    ): BomNodeDto {
        return manufacturingCrudTestClient.updateField(
            node.id,
            node.branch.id,
            ManufacturingUpdateController.InputParameterApi(
                name = fieldName,
                entityId = node.manufacturing?.id!!,
                type = type,
                unit = unit,
                value = value,
            ),
        )
    }

    private fun addSubAndPublish(bomNodeWithStep: BomNodeDto): BomNodeDto {
        val rootStepId = bomNodeWithStep.manufacturing!!.children.find { it.type == Entities.MANUFACTURING_STEP }!!.id
        return addSubAndPublish(bomNodeWithStep, rootStepId)
    }

    private fun addSubAndPublish(
        bomNodeWithStep: BomNodeDto,
        rootStepId: String,
    ): BomNodeDto {
        val nodeWithSub = addSubManufacturing(bomNodeWithStep.id, rootStepId, bomNodeWithStep.branch.id)
        return manufacturingCrudTestClient.publish(nodeWithSub.id, nodeWithSub.branch.id)
    }

    fun publish(node: BomNodeDto): BomNodeDto {
        return manufacturingCrudTestClient.publish(node.id, node.branch.id)
    }

    @Test
    fun baseRootManufacturing() {
        val (bomNodeWithStep, _) = rootWithStep()
        val stepId = bomNodeWithStep.manufacturing!!.children.find { it.type == Entities.MANUFACTURING_STEP }!!.id
        addLabor(bomNodeWithStep.id, stepId, bomNodeWithStep.branch.id)

        manufacturingCrudTestClient.publish(bomNodeWithStep.id, bomNodeWithStep.branch.id)
    }

    private fun rootWithStep(
        name: String = "test",
        initialCycleTime: Time = Time(0.0, TimeUnits.SECOND),
    ): Pair<BomNodeDto, BomNodeDto> {
        val bomNodeAfterCreation = createRootManufacturing(name)
        val bomNodeWithStep =
            addStep(
                bomNodeAfterCreation.id,
                bomNodeAfterCreation.manufacturing!!.id,
                bomNodeAfterCreation.branch.id,
                name = "Step1-$name",
                initialCycleTime = initialCycleTime,
            )
        return bomNodeWithStep to bomNodeAfterCreation
    }

    fun getCosts(node: BomNodeDto): Double {
        return getField(node, "costPerPart") as Double
    }

    fun getField(
        node: BomNodeDto,
        field: String,
    ): Any? {
        return node.getFieldValue(field)
    }

    private fun createRootManufacturing(name: String = "test"): BomNodeDto {
        return wizardBuilder
            .createStandard {
                calculationTitle = Text("title-$name")
                lifeTime = TimeInYears(BigDecimal(10), TimeInYearsUnit.YEAR)
                location = Text("tset.ref.classification.germany")
                peakUsableProductionVolumePerYear = QuantityUnit(150000.toBigDecimal())
                averageUsableProductionVolumePerYear = QuantityUnit(75_000.toBigDecimal())
            }.bomNode
    }

    fun addStep(
        node: BomNodeDto,
        name: String = "Step1",
        initialCycleTime: Time = Time(0.0, TimeUnits.SECOND),
    ): BomNodeDto {
        return addStep(node.id, node.manufacturing!!.id, node.branch.id, name, initialCycleTime)
    }

    fun addStep(
        bomNodeId: String,
        manufacturing: String,
        branchId: String,
        name: String = "Step1",
        initialCycleTime: Time = Time(0.0, TimeUnits.SECOND),
    ): BomNodeDto {
        val stepCreationResult =
            manufacturingCrudTestClient.addStep(
                bomNodeId = bomNodeId,
                branchId = branchId,
                parentId = manufacturing,
                name = name,
                initialCycleTime = initialCycleTime,
            )

        // this call was moved out here from manufacturingCrudTestClient.addStep since this field update is only relevant
        // in fulfilling the history assertions in BomImporterIT
        return if (initialCycleTime.res > BigDecimal.ZERO) {
            val step =
                stepCreationResult.findInTree {
                    it.name == name
                }!!
            manufacturingCrudTestClient.updateField(
                bomNodeId = stepCreationResult.id,
                branch = stepCreationResult.branch.id,
                field =
                    ManufacturingUpdateController.InputParameterApi(
                        name = "internalCycleTime",
                        entityId = step.id,
                        type = "CycleTime",
                        unit = "SECONDS",
                        value = initialCycleTime.res,
                    ),
            )
        } else {
            stepCreationResult
        }
    }

    fun addLabor(node: BomNodeDto): BomNodeDto {
        val subStepId = node.manufacturing!!.children.find { it.type == Entities.MANUFACTURING_STEP }!!.id
        return addLabor(node.id, subStepId, node.branch.id)
    }

    private fun addChild(
        bomNodeId: String,
        parentEntityId: String,
        childNodeId: String,
    ): BomNodeDto {
        val fields =
            listOf(
                FieldParameterDto(name = "quantity", type = "Pieces", value = 1.0, denominatorUnit = null),
                FieldParameterDto(
                    name = CommercialCalculationCostMaterialUsage::customProcurementType.name,
                    type = CustomProcurementType::class.simpleName!!,
                    value = TsetProcurementType.INHOUSE.customProcurementType.value,
                    denominatorUnit = null,
                ),
            )

        return manufacturingCrudTestClient.addEntity(
            bomNodeId = bomNodeId,
            branchId = MAIN_BRANCH,
            name = "Child-Node-$bomNodeId - $childNodeId",
            entityType = Entities.BOM_ENTRY,
            parentId = parentEntityId,
            childBomNodeId = childNodeId,
            fields = fields,
        )
    }

    fun addLabor(
        bomNodeId: String,
        stepId: String,
        branchId: String,
    ): BomNodeDto {
        val fields =
            listOf(
                FieldParameterDto(
                    name = "requiredLabor",
                    type = "Num",
                    value = 10.0,
                    denominatorUnit = null,
                ),
            )

        return manufacturingCrudTestClient.addLabor(
            bomNodeId,
            branchId,
            stepId,
            "Labor-Step1",
            skillType = TsetDefaultSkillType.SKILLED_WORKER.mdKeyAsText(),
            fields,
        )
    }

    fun addSubManufacturing(
        parentNodeId: String,
        parentStepId: String,
        branch: String?,
    ): BomNodeDto {
        return wizardBuilder
            .asSubManufacturing(
                bomNodeId = parentNodeId,
                stepId = parentStepId,
                branchId = branch,
                quantity = QuantityUnit(1.0),
            )
            .createStandard()
            .bomNode
    }
}
