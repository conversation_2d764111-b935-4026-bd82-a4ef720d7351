package com.nu.bom.core

import com.nu.bom.core.api.ManufacturingUpdateController
import com.nu.bom.core.api.dtos.BomNodeDto
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.createBranchId
import com.nu.bom.core.service.BomNodeService
import com.nu.bom.core.service.MasterDataService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.MasterDataMockHelper
import com.nu.bom.core.utils.NbkClient
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Import
import java.math.BigDecimal
import java.math.RoundingMode

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Import(MasterDataMockHelper::class)
@AutoConfigureWebTestClient
class ManufacturingCrudBase : NbkClient.With {
    @Autowired
    protected lateinit var bomNodeService: BomNodeService

    protected var projectId: ProjectId = ProjectId()
    protected lateinit var accessCheck: AccessCheck

    @Autowired
    protected lateinit var masterDataService: MasterDataService

    @Autowired
    override lateinit var nbkClient: NbkClient

    @BeforeEach
    fun setup() {
        val setup = nbkClient.setupWithProject(name = "Test Project", key = "TPR")

        val project = setup.second
        accessCheck = setup.first
        projectId = project.mongoProjectId()
    }

    @AfterEach
    fun teardown() {
        nbkClient.cleanup()
    }

    fun updateField(
        bomNodeId: String,
        branch: String,
        field: ManufacturingUpdateController.InputParameterApi,
    ): BomNodeSnapshot =
        getBomNode(
            manufacturingCrudTestClient.updateField(bomNodeId = bomNodeId, branch = branch, field = field),
        )

    fun getBomNode(dto: BomNodeDto): BomNodeSnapshot {
        val branch = dto.branch.id
        return bomNodeService.getBomNode(accessCheck, BomNodeId(dto.id), branch = createBranchId(branch)).block()
            ?: error("Node not found with id=${dto.id} and branch=$branch")
    }

    fun assertField(
        entity: ManufacturingEntity,
        field: Pair<String, FieldResult<*, *>>,
    ) {
        entity.getFieldResult(field.first)!!.let {
            when (field.second.res) {
                is BigDecimal -> {
                    val scale = (field.second.res as BigDecimal).scale()
                    assertEquals(
                        0,
                        (field.second.res as BigDecimal).compareTo((it.res as BigDecimal).setScale(scale, RoundingMode.HALF_UP)),
                    )
                }
                else -> assertEquals(field.second.res, it.res)
            }
        }
    }
}
