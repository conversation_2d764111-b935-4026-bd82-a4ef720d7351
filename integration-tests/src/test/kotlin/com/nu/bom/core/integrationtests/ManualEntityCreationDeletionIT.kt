package com.nu.bom.core.integrationtests

import com.nu.bom.core.manufacturing.entities.BaseMaterial
import com.nu.bom.core.manufacturing.entities.Consumable
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.ElectronicComponent
import com.nu.bom.core.manufacturing.entities.Labor
import com.nu.bom.core.manufacturing.entities.Machine
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.entities.ManualManufacturingStep
import com.nu.bom.core.manufacturing.entities.ManualMaterialV2
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.Setup
import com.nu.bom.core.manufacturing.entities.Tool
import com.nu.bom.core.manufacturing.entities.masterdata.material.MaterialConsumerExtension
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.AreaUnits
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeStepMachiningType
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeUnit
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.EmissionUnits
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.MountingType
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.NumericFieldResultWithUnit
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.Power
import com.nu.bom.core.manufacturing.fieldTypes.PowerUnits
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SetupType
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYearsUnit
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.TsetDefaultSkillType
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.nu.bom.core.manufacturing.fieldTypes.mdKeyAsText
import com.nu.bom.core.manufacturing.utils.ManufacturingTreeUtils
import com.nu.bom.core.manufacturing.utils.RequiredFields
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.NET_WEIGHT_PER_PART
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.mongoProjectId
import com.nu.bom.core.service.BomNodeService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.NbkClient
import com.tset.bom.clients.nuledge.FieldParameterDto
import org.bson.types.ObjectId
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import reactor.test.StepVerifier

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@AutoConfigureWebTestClient
class ManualEntityCreationDeletionIT : NbkClient.With {
    @Autowired
    private lateinit var bomNodeService: BomNodeService

    @Autowired
    override lateinit var nbkClient: NbkClient

    private var projectId: ProjectId = ProjectId()

    private var bomNodeId: BomNodeId = BomNodeId()
    private lateinit var manufacturingId: ObjectId
    private lateinit var step1Id: ObjectId
    private lateinit var createdEntityId: ObjectId
    private lateinit var branch: String
    private lateinit var accessCheck: AccessCheck

    @BeforeEach
    fun setup() {
        val (ac, project) = nbkClient.setupWithProject(name = "Test Project", key = "MECD")

        accessCheck = ac
        projectId = project.mongoProjectId()

        // initialize ManualManufacturing
        createManualManufacturing {
            calculationTitle = Text("ManualManufacturing1")
            peakUsableProductionVolumePerYear = QuantityUnit(100_000.toBigDecimal())
            averageUsableProductionVolumePerYear = QuantityUnit(100_000.toBigDecimal())
            lifeTime = TimeInYears(10.toBigDecimal(), TimeInYearsUnit.YEAR)
            location = Text("tset.ref.classification.germany")
            dimension = Dimension(Dimension.Selection.NUMBER)
        }
    }

    @AfterEach
    fun teardown() {
        nbkClient.cleanup()
    }

    @Test
    fun createDeleteManualManufacturingStep() {
        assertAddRemoveEntity(
            "Step2",
            Entities.MANUFACTURING_STEP,
            fields =
                mapOf(
                    "initialCycleTime" to CycleTime(0.0, CycleTimeUnit.SECOND),
                    "partsPerCycle" to QuantityUnit(1.0),
                    "utilizationRate" to Rate(0.8),
                ),
        ) { entity ->
            assertTrue(entity is ManualManufacturingStep)
        }
    }

    @Test
    fun createManualManufacturingStep() {
        assertAddRemoveEntity(
            entityName = "Step2",
            entityType = Entities.MANUFACTURING_STEP,
            fields =
                mapOf(
                    "displayDesignation" to Text("step"),
                    "utilizationRate" to Rate(0.8),
                    "partsPerCycle" to QuantityUnit(1.0),
                ),
        ) { entity ->
            assertTrue(entity is ManualManufacturingStep)

            assertEquals(Text("step"), entity.getFieldResult("displayDesignation"))
        }
    }

    @Test
    fun createDeleteMachine() {
        assertAddRemoveEntity(
            "Machine1",
            Entities.MACHINE,
            fields =
                mapOf(
                    "designation" to Text("Machine1"),
                    "quantity" to Pieces(1.0),
                    "manufacturer" to Text("Test Manufacturer"),
                    "type" to Text("Test Type"),
                    "depreciationTime" to Time(10.0, TimeUnits.YEAR),
                    "investBase" to Money(20_000.toBigDecimal()),
                    "investFundament" to Money(20_000.toBigDecimal()),
                    "investSetup" to Money(20_000.toBigDecimal()),
                    "investMisc" to Money(20_000.toBigDecimal()),
                    "maintenanceRate" to Rate(1.0),
                    "powerOnTimeRate" to Rate(1.0),
                    "requiredSpaceGross" to Area(1.0, AreaUnits.QM),
                    "requiredSpaceNet" to Area(0.8, AreaUnits.QM),
                    "consumableRate" to Rate(1.0),
                    "connectedLoad" to Power(10.0, PowerUnits.KILOWATT),
                    "machineWeight" to Weight(1500.0, WeightUnits.KILOGRAM),
                    "masterdataBaseCurrency" to
                        com.nu.bom.core.manufacturing.fieldTypes
                            .Currency("EUR"),
                ),
        ) { entity ->
            assertTrue(entity is Machine)
        }
    }

    @Test
    fun createDeleteLabor() {
        assertAddRemoveEntity(
            "Labor1",
            Entities.LABOR,
            fields =
                mapOf(
                    "skillType" to TsetDefaultSkillType.SKILLED_WORKER.mdKeyAsText(),
                    "requiredLabor" to Num(1.0),
                ),
        ) { entity ->
            assertTrue(entity is Labor)
        }
    }

    @Test
    fun createDeleteSetup() {
        assertAddRemoveEntity(
            "Setup1",
            Entities.SETUP,
            fields =
                mapOf(
                    "skillType" to TsetDefaultSkillType.SKILLED_WORKER.mdKeyAsText(),
                    "requiredLabor" to Num(1.0),
                    // FIXME: NEUM-1182 divideByZero exception @ManufacturingStep/systemDownTime
//                "setupType" to SetupType.EXTERNAL_SETUP,
                    "setupType" to SetupType.INTERNAL_SETUP,
                    "setupTime" to Time(3600.0, TimeUnits.SECOND),
                ),
        ) { entity ->
            assertTrue(entity is Setup)
        }
    }

    @Test
    fun createDeleteTool() {
        assertAddRemoveEntity(
            "Tool1",
            Entities.TOOL,
            fields =
                mapOf(
                    "designation" to Text("Tool1"),
                    "investPerTool" to Money(1.0),
                    "serviceLifeInCycles" to Num(1.0),
                    "proportionalInvest" to Rate(1.0),
                    "maintenanceRate" to Rate(1.0),
                    "conceptCost" to Money(1.0),
                    "masterdataBaseCurrency" to
                        com.nu.bom.core.manufacturing.fieldTypes
                            .Currency("EUR"),
                ),
        ) { entity ->
            assertTrue(entity is Tool)
        }
    }

    @Test
    fun createDeleteCycleTimeStep() {
        assertAddRemoveEntity(
            "CycleTimeStep1",
            Entities.CYCLETIME_STEP,
            fields =
                mapOf(
                    "time" to CycleTime(1.0, CycleTimeUnit.SECOND),
                    "machiningType" to CycleTimeStepMachiningType.Others,
                    "cycleTimeStep" to Text(CycleTimeStep::class.simpleName!!),
                ),
        ) { entity ->
            assertTrue(entity is CycleTimeStep)
        }
    }

    @Test
    fun createDeleteConsumable() {
        assertAddRemoveEntity(
            "Consumable1",
            Entities.CONSUMABLE,
            fields =
                mapOf(
                    Consumable::quantity.name to Num(1.0),
                    MaterialConsumerExtension::materialBasePrice.name to Money(0.2),
                    BaseMaterial::cO2PerUnit.name to Emission(0.2, EmissionUnits.KILOGRAM_CO2E),
                    BaseMaterial::baseCurrency.name to
                        com.nu.bom.core.manufacturing.fieldTypes
                            .Currency("EUR"),
                    BaseMaterial::reuseOfScrap.name to Bool(false),
                ),
        ) { entity ->
            assertTrue(entity is Consumable)
        }
    }

    @Test
    fun createDeleteMaterial() {
        assertAddRemoveEntity(
            "Material1",
            Entities.MATERIAL,
            fields =
                mapOf(
                    NET_WEIGHT_PER_PART to QuantityUnit(100.0),
                    "scrapWeightPerPart" to QuantityUnit(10.0),
                    "lossRate" to Rate(0.1),
                    "materialBasePrice" to Money(10.0),
                    "masterdataBaseCurrency" to
                        com.nu.bom.core.manufacturing.fieldTypes
                            .Currency("EUR"),
                    "dimension" to Dimension(Dimension.Selection.MASS),
                    "materialRecyclingPrice" to Money(1.0),
                ),
        ) { entity ->
            assertTrue(entity is ManualMaterialV2)
        }
    }

    @Test
    fun createDeleteElectronicComponent() {
        assertAddRemoveEntity(
            "Material1",
            Entities.C_PART,
            entityClass = ElectronicComponent::class.simpleName,
            fields =
                mapOf(
                    "quantity" to Pieces(2.0),
                    "pricePerUnit" to Money(10.0),
                    "masterdataBaseCurrency" to
                        com.nu.bom.core.manufacturing.fieldTypes
                            .Currency("EUR"),
                    "mpn" to Text("123456"),
                    "mountingType" to MountingType.SMD,
                    "manufacturer" to Text("MyMan"),
                    "cO2PerUnit" to Emission(1.0, EmissionUnits.KILOGRAM_CO2E),
                ),
        ) { entity ->
            assertTrue(entity is ElectronicComponent)
        }
    }

    private fun createManualManufacturing(customizer: RequiredFields.() -> Unit) {
        val result =
            wizardBuilder
                .withStandardFields(customizer)
                .execute()
                .getOrThrow()

        branch = manufacturingCrudTestClient.checkoutBranch(result.bomNode.id)

        val newResult =
            manufacturingCrudTestClient.addStep(result.bomNode, branch, "Step1", Time(0.0, TimeUnits.SECOND))

        bomNodeId = BomNodeId(newResult.id)

        manufacturingCrudTestClient.publish(bomNodeId.toHexString(), branch)

        StepVerifier
            .create(loadNode(bomNodeId))
            .assertNext {
                val manualManufacturing = it.manufacturing!!
                assertEquals(result.bomNode.title, it.title)
                assertTrue(manualManufacturing is ManualManufacturing)

                manufacturingId = manualManufacturing._id

                assertTrue(manualManufacturing.children.isNotEmpty())

                val step1 = manualManufacturing.findByEntityName("Step1")
                assertEquals(Entities.MANUFACTURING_STEP, step1?.getEntityTypeAnnotation())
                assertEquals("Step1", step1?.name)
                assertTrue(step1 is ManualManufacturingStep)

                // assert that Manufacturing/costPerPart can be calculated
                assertCostPerPartExists(it.manufacturing!!)

                step1Id = step1!!._id
            }.verifyComplete()
    }

    private fun assertAddRemoveEntity(
        entityName: String,
        entityType: Entities,
        fields: Map<String, FieldResult<*, *>> = emptyMap(),
        entityClass: String? = null,
        assertionConsumer: (ManufacturingEntity) -> Unit = {},
    ) = assertAddRemoveEntity(
        entityType = entityType,
        entityClass = entityClass,
        fields =
            mapOf(
                "displayDesignation" to Text(entityName),
            ) + fields,
        assertionConsumer = assertionConsumer,
    )

    private fun assertAddRemoveEntity(
        entityType: Entities,
        fields: Map<String, FieldResult<*, *>>,
        entityClass: String? = null,
        assertionConsumer: (ManufacturingEntity) -> Unit = {},
    ) {
        // extract entity name
        val entityName =
            fields["displayDesignation"]?.let { it as Text }?.res.let {
                assertNotNull(it) { "Missing field: displayDesignation" }
                it!!
            }

        // create entity
        val newBomNode =
            manufacturingCrudTestClient.addEntity(
                bomNodeId.toHexString(),
                branch,
                entityName,
                entityType,
                step1Id.toHexString(),
                fields =
                    fields.map { (fieldKey, fieldResult) ->
                        toFieldInputDto(fieldKey, fieldResult)
                    },
                entityClass = entityClass,
            )
        branch = newBomNode.branch.id

        // assert entity creation
        StepVerifier
            .create(loadNode(newBomNode.id))
            .assertNext {
                val createdEntity =
                    it.manufacturing?.findByEntityName(entityName)
                        ?: throw NullPointerException("Missing $entityName in ${it.manufacturing?.treeStructure()}")

                assertEquals(entityType, createdEntity.getEntityTypeAnnotation())

                // apply user assertions
                assertionConsumer(createdEntity)

                // assert that Manufacturing/costPerPart can be calculated
                assertCostPerPartExists(it.manufacturing!!)

                createdEntityId = createdEntity._id
            }.verifyComplete()

        // delete entity
        manufacturingCrudTestClient.deleteEntity(
            bomNodeId = newBomNode.id,
            entityId = createdEntityId.toString(),
            branch = branch,
        )

        // assert entity deletion
        StepVerifier
            .create(loadNode(newBomNode.id))
            .assertNext {
                assertNull(ManufacturingTreeUtils.findEntityById(it.manufacturing!!, createdEntityId))

                // assert that Manufacturing/costPerPart can be calculated
                assertCostPerPartExists(it.manufacturing!!)
            }.verifyComplete()
    }

    private fun loadNode(
        id: String,
        branchId: String? = null,
    ) = loadNode(BomNodeId(id), branchId)

    private fun loadNode(
        id: BomNodeId,
        branchId: String? = null,
    ) = bomNodeService.getBomNode(
        accessCheck = accessCheck,
        nodeId = id,
        branch = BranchId(branchId ?: branch),
    )

    private fun toFieldInputDto(
        fieldKey: String,
        fieldResult: FieldResult<*, *>,
    ): FieldParameterDto =
        FieldParameterDto(
            name = fieldKey,
            type = fieldResult.javaClass.simpleName,
            unit =
                when (fieldResult) {
                    is NumericFieldResultWithUnit<*, *> -> (fieldResult.unit as Enum<*>).name
                    else -> null
                },
            value = fieldResult.res!!.toString(),
            denominatorUnit = null,
        )

    private fun assertCostPerPartExists(entity: ManufacturingEntity) {
        assertNotNull(entity.getFieldResult("costPerPart"))
    }
}
