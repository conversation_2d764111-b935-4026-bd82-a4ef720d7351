package com.nu.bom.core.integrationtests.md

import com.nu.bom.core.api.ManufacturingQueryController
import com.nu.bom.core.api.ManufacturingUpdateController
import com.nu.bom.core.api.dtos.BomNodeDto
import com.nu.bom.core.config.defaultMasterDataMocksAnswer
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostMaterialUsage
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.overheadLookup.DefaultOverheadType
import com.nu.bom.core.manufacturing.entities.BaseManufacturingFields
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.masterdata.tsetdefaultconfiguration.TsetOverheadMethod
import com.nu.bom.core.model.BomEntryRelation
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.MergeSourceType
import com.nu.bom.core.service.BomPublishService
import com.nu.bom.core.service.MasterDataService
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService
import com.nu.bom.core.service.dto.DirtyStateHandling
import com.nu.bom.core.service.masterdata.MdDetailCrudService
import com.nu.bom.core.service.masterdata.MdLookupService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.AccountTestUtil
import com.nu.bom.core.utils.CalculationBuilderService
import com.nu.bom.core.utils.CompositeCalculationBuilder
import com.nu.bom.core.utils.assertBigDecimalsEquals
import com.nu.bom.core.utils.assertField
import com.nu.bom.core.utils.getDefaultInputs
import com.nu.bom.tests.docker.MasterdataUpdateTest
import com.nu.bomrads.dto.ProjectCreationDTO
import com.nu.masterdata.dto.v1.detail.DetailBulkResponseSuccessDto
import com.nu.masterdata.dto.v1.detail.DetailDto
import com.nu.masterdata.dto.v1.detail.LovValueDto
import com.nu.masterdata.dto.v1.detail.NumericDetailValueDto
import com.nu.masterdata.dto.v1.detail.NumericValueDto
import com.nu.masterdata.dto.v1.detail.UnitMeasurementDto
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import com.nu.masterdata.dto.v1.lookup.request.Effectivity
import com.nu.masterdata.dto.v1.lookup.request.MasterdataLookupRequest
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.kotlin.any
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import java.math.BigDecimal
import java.time.Instant

private val logger = org.slf4j.LoggerFactory.getLogger(MasterdataOverheadUpdateIT::class.java)

/**
 * the tests here make sure the following flow works:
 *
 * * create new manufacturing (that uses overheads from masterdata service)
 * * update overheads in masterdata serivce (postDetails)
 * * check available openMerges where masterdata updates should appear
 * * execute the updateMasterdata flow that uses the new overhead rate
 */
@SpringBootTest
@MasterdataUpdateTest
class MasterdataOverheadUpdateIT
    @Autowired
    constructor(
        private val accountUtil: AccountTestUtil,
        private val mdDetailCrudService: MdDetailCrudService,
        private val manufacturingQueryController: ManufacturingQueryController,
        private val manufacturingUpdateController: ManufacturingUpdateController,
        private val bomPublishService: BomPublishService,
        private val builderService: CalculationBuilderService,
        private val masterDataService: MasterDataService,
        private val mdLookupService: MdLookupService,
    ) {
        private lateinit var headerTypeKey: SimpleKeyDto

        private val expectedInitialRate = 0.15.toBigDecimal()

        private lateinit var accessCheck: AccessCheck
        private lateinit var projectCreationDTO: ProjectCreationDTO
        private lateinit var rootSnapshot: BomNodeSnapshot
        private lateinit var child1BomEntry: BomEntryRelation
        private lateinit var childEntity: ManufacturingEntity

        @BeforeEach
        fun setup() {
            val (ac, project) = accountUtil.setupWithProject(name = "MasterdataOverheadUpdateIT", key = "MOUIT")
            accessCheck = ac
            projectCreationDTO = project
            masterDataService.apply {
                Mockito
                    .doAnswer(::defaultMasterDataMocksAnswer)
                    .`when`(this)
                    .getLatestMasterDataByCompositeKey(accessCheck = any(), selector = any(), mode = any())
            }
            val child =
                CompositeCalculationBuilder
                    .create(CompositeCalculationBuilder.TreeType.MANUAL)
                    .withCustomizer { root ->
                        val fields = getDefaultInputs(ManualManufacturing::class).toMutableMap()
                        fields[CommercialCalculationCostMaterialUsage::procurementType.name] =
                            ManufacturingType(
                                ManufacturingType.Type.PURCHASE,
                            )
                        fields[BaseManufacturingFields::peakUsableProductionVolumePerYear.name] = QuantityUnit(25000.0)
                        fields[Manufacturing::overheadMethod.name] = TsetOverheadMethod.BUILD_TO_PRINT_AUTO.fieldType
                        root
                            .withTitle("Child1-Of-Root1")
                            .withInput(fields)
                    }

            val tree =
                CompositeCalculationBuilder
                    .create(CompositeCalculationBuilder.TreeType.MANUAL)
                    .withDefaultProject(projectCreationDTO)
                    .withCustomizer { root ->
                        val fields = getDefaultInputs(ManualManufacturing::class).toMutableMap()
                        fields[CommercialCalculationCostMaterialUsage::procurementType.name] =
                            ManufacturingType(
                                ManufacturingType.Type.PURCHASE,
                            )
                        fields[BaseManufacturingFields::peakUsableProductionVolumePerYear.name] = QuantityUnit(30000.0)
                        fields[Manufacturing::overheadMethod.name] = TsetOverheadMethod.BUILD_TO_PRINT_AUTO.fieldType
                        root
                            .withTitle("Root1")
                            .withInput(fields)
                    }.withSub(child)
                    .build()

            rootSnapshot = builderService.build(tree, accessCheck).block()!!.snapshot

            child1BomEntry = rootSnapshot.subNodes.first()
            childEntity =
                rootSnapshot.manufacturing!!.findChild {
                    it.bomNodeId != rootSnapshot.bomNodeId && it.getEntityTypeAnnotation() == Entities.MANUFACTURING
                }!!

            headerTypeKey = SimpleKeyDto(MasterdataTsetConfigurationService.getTsetMasterdataConfig().overheadsConfiguration.headerTypeKey)
        }

        @AfterEach
        fun teardown() {
            accountUtil.cleanup()
        }

        @Test
        fun testMasterdataDetailUpdateNotRecursive() {
            validateInitialRates()

            val updatedRate = 0.42.toBigDecimal()
            updateOverheadRateInMasterdata(updatedRate)

            val nodeAfterUpdate = validateOpenMergesAndDoUpdate(rootSnapshot.bomNodeId(), 1, 1, false)!!
            validateRate(updatedRate, rootSnapshot.bomNodeId(), BranchId(nodeAfterUpdate.branch.id))
            val publishedState =
                bomPublishService
                    .publish(
                        accessCheck = accessCheck,
                        nodeId = rootSnapshot.bomNodeId(),
                        branchId = BranchId(nodeAfterUpdate.branch.id),
                        dirtyStateHandling = DirtyStateHandling.FAIL_ON_DIRTY,
                    ).block()
            val childBomNodeDto = validateRate(expectedInitialRate, childEntity.bomNodeId!!, publishedState!!.branch.id())
            Assertions.assertNotEquals(
                nodeAfterUpdate.getField(Manufacturing::masterdataTimestamp.name)!!.value,
                childBomNodeDto.getField(Manufacturing::masterdataTimestamp.name)?.value,
            )

            val childAfterUpdate = validateOpenMergesAndDoUpdate(childEntity.bomNodeId!!, 1, 0, false)!!
            validateRate(updatedRate, childEntity.bomNodeId!!, BranchId(childAfterUpdate.branch.id))
        }

        @Test
        fun testMasterdataDetailUpdateRecursive() {
            validateInitialRates()

            val childTimestamp = childEntity.getFieldResult(Manufacturing::masterdataTimestamp.name)!!.res as BigDecimal
            rootSnapshot.manufacturing!!.assertField(Manufacturing::masterdataTimestamp.name, childTimestamp)
            logger.info("timestamp is $childTimestamp, epochMillis=${Num(Instant.now().toEpochMilli())}")

            validateOpenMergesAndDoUpdate(rootSnapshot.bomNodeId(), 0, 0, false)

            val updatedRate = 0.42.toBigDecimal()
            updateOverheadRateInMasterdata(updatedRate)

            val nodeAfterUpdate = validateOpenMergesAndDoUpdate(rootSnapshot.bomNodeId(), 1, 1, true)!!
            val publishedState =
                bomPublishService
                    .publish(
                        accessCheck = accessCheck,
                        nodeId = rootSnapshot.bomNodeId(),
                        branchId = BranchId(nodeAfterUpdate.branch.id),
                        dirtyStateHandling = DirtyStateHandling.FAIL_ON_DIRTY,
                    ).block()
            validateRate(updatedRate, rootSnapshot.bomNodeId(), publishedState!!.branch.id())

            val childBomNodeDto = validateRate(updatedRate, childEntity.bomNodeId!!, publishedState.branch.id())
            Assertions.assertEquals(
                nodeAfterUpdate.getField(Manufacturing::masterdataTimestamp.name)!!.value,
                childBomNodeDto.getField(Manufacturing::masterdataTimestamp.name)?.value,
            )

            validateOpenMergesAndDoUpdate(rootSnapshot.bomNodeId(), 0, 0, false)
        }

        private fun validateInitialRates() {
            rootSnapshot.manufacturing!!.assertField("#Cost_SalesAndGeneralAdministrationCosts-Mfg_Rate", expectedInitialRate)
            childEntity.assertField("#Cost_SalesAndGeneralAdministrationCosts-Mfg_Rate", expectedInitialRate)
        }

        private fun validateOpenMergesAndDoUpdate(
            bomNodeId: BomNodeId,
            expectedMd: Int,
            expectedMdChildren: Int,
            recursiveUpdate: Boolean,
        ): BomNodeDto? {
            logger.info("validateOpenMergesAndDoUpdate, epochMilli=${Num(Instant.now().toEpochMilli())}")

            val nodeWithOpenMerges =
                manufacturingQueryController
                    .getManufacturing(
                        accessCheck = accessCheck,
                        bomNodeId = bomNodeId,
                        branch = null,
                        depth = null,
                        changesetId = null,
                        showOpenMerges = true,
                        ccy = null,
                    ).block()

            Assertions.assertEquals(expectedMdChildren + expectedMd, nodeWithOpenMerges!!.openMergesAvailable.size)
            Assertions.assertEquals(
                expectedMdChildren,
                nodeWithOpenMerges.openMergesAvailable.count {
                    it.name == MergeSourceType.MASTERDATA_CHILDREN.name
                },
                "expected that openMerges contains one entry of type MASTERDATA_CHILDREN",
            )
            Assertions.assertEquals(
                expectedMd,
                nodeWithOpenMerges.openMergesAvailable.count {
                    it.name == MergeSourceType.MASTERDATA.name
                },
                "expected that openMerges contains one entry of type MASTERDATA",
            )

            return if (expectedMd + expectedMdChildren > 0) {
                logger.info("updateMasterData, epochMilli=${Num(Instant.now().toEpochMilli())}")
                manufacturingUpdateController
                    .updateMasterData(
                        accessCheck = accessCheck,
                        bomNodeId = bomNodeId,
                        branch = nodeWithOpenMerges.branch.id,
                        recursiveUpdate = recursiveUpdate,
                    ).block()!!
            } else {
                null
            }
        }

        private fun updateOverheadRateInMasterdata(updatedRate: BigDecimal) {
            logger.info("updateOverheadRateInMasterdata, epochMilli=${Num(Instant.now().toEpochMilli())}")
            val newDetailEntry =
                DetailDto(
                    headerKey = SimpleKeyDto("Tset.${DefaultOverheadType.SalesAndGeneralAdministrationCosts.name}.Manufacturing"),
                    effectivities =
                        mapOf(
                            SimpleKeyDto("overheadMethod") to LovValueDto(SimpleKeyDto(TsetOverheadMethod.BUILD_TO_PRINT_AUTO.name)),
                            SimpleKeyDto("validFromVolume") to
                                NumericValueDto(25000.0, UnitMeasurementDto(SimpleKeyDto("tset.unit.piece.piece"))),
                        ),
                    value =
                        NumericDetailValueDto(
                            value = updatedRate.toDouble() * 100,
                            numerator = UnitMeasurementDto(SimpleKeyDto("tset.unit.rate.percentage")),
                        ),
                    active = true,
                )
            val resp = mdDetailCrudService.postDetailEntries(accessCheck, headerTypeKey, listOf(newDetailEntry)).block()
            Assertions.assertNotNull(resp)
            Assertions.assertEquals(1, resp!!.size)
            Assertions.assertInstanceOf(DetailBulkResponseSuccessDto::class.java, resp[0])
        }

        private fun validateRate(
            expectedRate: BigDecimal,
            bomNodeId: BomNodeId,
            branchId: BranchId?,
        ): BomNodeDto {
            val bomNodeDto =
                manufacturingQueryController
                    .getManufacturing(
                        accessCheck = accessCheck,
                        bomNodeId = bomNodeId,
                        branch = branchId?.toHexString(),
                        depth = null,
                        changesetId = null,
                        showOpenMerges = true,
                        ccy = null,
                    ).block()

            val rate = bomNodeDto!!.manufacturing?.getField("#Cost_SalesAndGeneralAdministrationCosts-Mfg_Rate")?.value as BigDecimal?
            Assertions.assertNotNull(rate)
            if (rate!!.compareTo(expectedRate) != 0) {
                debugWrongOverheadRate(bomNodeDto)
            }

            assertBigDecimalsEquals(expectedRate, rate, "new rate expected to be $expectedRate")
            return bomNodeDto
        }

        /**
         * this function exists to print more helpful output if test fails.
         */
        private fun debugWrongOverheadRate(bomNodeDto: BomNodeDto) {
            val ts = bomNodeDto.manufacturing!!.getField(Manufacturing::masterdataTimestamp.name).value as BigDecimal
            logger.info("not equal, timestamp to request lookup {}", ts)
            val resp =
                mdLookupService
                    .lookup(
                        accessCheck,
                        MasterdataLookupRequest(
                            strategyKey = SimpleKeyDto("defaultStrategy"),
                            headerTypeKey = headerTypeKey,
                            headerKeys =
                                listOf(
                                    SimpleKeyDto("Tset.${DefaultOverheadType.SalesAndGeneralAdministrationCosts.name}.Manufacturing"),
                                ),
                            effectivities =
                                listOf(
                                    Effectivity(
                                        SimpleKeyDto("overheadMethod"),
                                        LovValueDto(SimpleKeyDto(TsetOverheadMethod.BUILD_TO_PRINT_AUTO.name)),
                                    ),
                                    Effectivity(
                                        SimpleKeyDto("validFromVolume"),
                                        NumericValueDto(25000.0, UnitMeasurementDto(SimpleKeyDto("pcs"))),
                                    ),
                                ),
                            timestampEpochMillis = ts.toLong(),
                        ),
                    ).block()
            logger.info("resp in test {} with ts {}", resp, ts)

            val ts2 = ts.toLong() + 10
            val resp2 =
                mdLookupService
                    .lookup(
                        accessCheck,
                        MasterdataLookupRequest(
                            strategyKey = SimpleKeyDto("defaultStrategy"),
                            headerTypeKey = headerTypeKey,
                            headerKeys =
                                listOf(
                                    SimpleKeyDto("Tset.${DefaultOverheadType.SalesAndGeneralAdministrationCosts.name}.Manufacturing"),
                                ),
                            effectivities =
                                listOf(
                                    Effectivity(
                                        SimpleKeyDto("overheadMethod"),
                                        LovValueDto(SimpleKeyDto(TsetOverheadMethod.BUILD_TO_PRINT_AUTO.name)),
                                    ),
                                    Effectivity(
                                        SimpleKeyDto("validFromVolume"),
                                        NumericValueDto(25000.0, UnitMeasurementDto(SimpleKeyDto("pcs"))),
                                    ),
                                ),
                            timestampEpochMillis = ts2,
                        ),
                    ).block()
            logger.info("resp in test {} with ts {}", resp2, ts2)
        }
    }
