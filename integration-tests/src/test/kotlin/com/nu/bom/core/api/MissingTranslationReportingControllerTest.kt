package com.nu.bom.core.api

import com.nu.bom.core.api.dtos.MissingTranslationDto
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.reactive.server.WebTestClient

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@AutoConfigureWebTestClient
class MissingTranslationReportingControllerTest {
    @Autowired
    private lateinit var webTestClient: WebTestClient

    @BeforeEach
    fun setUp() {
        deleteMissingTranslations()
    }

    @Test
    fun `test report and return missing translations`() {
        reportMissingTranslation("my_key")
        reportMissingTranslation("my_key_2")
        // Duplicate keys will be ignored and not inserted.
        reportMissingTranslation("my_key_2")

        val result = getMissingTranslations()

        assertThat(result).hasSize(2)
        assertMissingTranslation(result, "my_key")
        assertMissingTranslation(result, "my_key_2")
    }

    @Test
    fun `test delete all reported missing translations`() {
        reportMissingTranslation("my_key")
        reportMissingTranslation("my_key_2")

        val result = getMissingTranslations()
        assertThat(result).hasSize(2)

        deleteMissingTranslations()

        val resultAfterDelete = getMissingTranslations()
        assertThat(resultAfterDelete).isEmpty()
    }

    @Test
    fun `test delete specific missing translation`() {
        reportMissingTranslation("my_key")
        reportMissingTranslation("my_key_2")

        val result = getMissingTranslations()
        assertThat(result).hasSize(2)

        deleteMissingTranslation("my_key_2")

        val resultAfterDelete = getMissingTranslations()
        assertThat(resultAfterDelete).hasSize(1)
        assertMissingTranslation(resultAfterDelete, "my_key")
    }

    @Test
    fun `test delete keys that exist in JSON on refresh`() {
        reportMissingTranslation("my_key")
        // This key exists in "en.json". On refresh, it will be removed from the missing translations collection in the DB.
        reportMissingTranslation("year")

        val result = getMissingTranslations()
        assertThat(result).hasSize(2)
        assertMissingTranslation(result, "my_key")
        assertMissingTranslation(result, "year")

        refreshMissingTranslations()

        val resultAfterRefresh = getMissingTranslations()
        assertThat(resultAfterRefresh).hasSize(1)
        assertMissingTranslation(resultAfterRefresh, "my_key")
    }

    private fun getMissingTranslations(): List<MissingTranslationDto> {
        return webTestClient.get()
            .uri("/api/internal/missingTranslations")
            .accept(MediaType.APPLICATION_JSON)
            .exchange()
            .expectStatus().isOk
            .expectBodyList(MissingTranslationDto::class.java)
            .returnResult()
            .responseBody!!
    }

    private fun reportMissingTranslation(key: String) {
        webTestClient.post()
            .uri("/api/internal/missingTranslations")
            .accept(MediaType.APPLICATION_JSON)
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(
                MissingTranslationDto(
                    key = key,
                    timestamp = TIMESTAMP_MOCK,
                ),
            )
            .exchange()
            .expectStatus().isOk
    }

    private fun assertMissingTranslation(
        result: List<MissingTranslationDto>,
        containsKey: String,
    ) {
        assertThat(result)
            .usingRecursiveFieldByFieldElementComparatorIgnoringFields(MissingTranslationDto::timestamp.name)
            .contains(
                MissingTranslationDto(
                    key = containsKey,
                    timestamp = TIMESTAMP_MOCK,
                ),
            )
    }

    private fun deleteMissingTranslations() {
        webTestClient.delete()
            .uri("/api/internal/missingTranslations")
            .accept(MediaType.APPLICATION_JSON)
            .exchange()
            .expectStatus().isOk
    }

    private fun deleteMissingTranslation(key: String) {
        webTestClient.delete()
            .uri("/api/internal/missingTranslations/$key")
            .accept(MediaType.APPLICATION_JSON)
            .exchange()
            .expectStatus().isOk
    }

    private fun refreshMissingTranslations() {
        webTestClient.post()
            .uri("/api/internal/missingTranslations/refresh")
            .accept(MediaType.APPLICATION_JSON)
            .exchange()
            .expectStatus().isOk
    }

    companion object {
        private const val TIMESTAMP_MOCK = "timestamp"
    }
}
