package com.nu.bom.core.wizard

import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.mongoProjectId
import com.nu.bom.core.repository.WizardRepository
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.NbkClient
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test", "masterdata-init", "lookup-init", "template-init", "shape-init", "config-init")
// TODO: Investigate why the default timeout is not enough
@AutoConfigureWebTestClient(timeout = "PT20S")
abstract class RoughPartWizardITBase : NbkClient.With {
    @Autowired
    private lateinit var wizardRepository: WizardRepository

    protected var projectId: ProjectId = ProjectId()

    @Autowired
    override lateinit var nbkClient: NbkClient
    private lateinit var accessCheck: AccessCheck

    @BeforeEach
    fun setup() {
        val (ac, project) = nbkClient.setupWithProject(name = "Test Project", key = "RPWIT")

        accessCheck = ac
        projectId = project.mongoProjectId()
    }

    @AfterEach
    fun teardown() {
        wizardRepository.deleteAll().block()
        nbkClient.cleanup()
    }
}
