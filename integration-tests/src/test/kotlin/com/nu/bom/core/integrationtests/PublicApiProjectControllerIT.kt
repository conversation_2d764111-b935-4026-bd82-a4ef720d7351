package com.nu.bom.core.integrationtests

import com.nu.bom.core.manufacturing.utils.ManufacturingCrudTestClient
import com.nu.bom.core.manufacturing.utils.PublicApiProjectCrudTestClient
import com.nu.bom.core.model.toMongoProjectId
import com.nu.bom.core.publicapi.dtos.ProjectCreation
import com.nu.bom.core.utils.AccountTestUtil
import com.nu.bomrads.id.FolderId
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@AutoConfigureWebTestClient
class PublicApiProjectControllerIT {
    @Autowired
    lateinit var accountTestUtil: AccountTestUtil

    @Autowired
    lateinit var manufacturingCrudTestClient: ManufacturingCrudTestClient

    @Autowired
    lateinit var publicApiProjectCrudTestClient: PublicApiProjectCrudTestClient

    companion object {
        const val PROJECT_KEY = "PAPICIT"
    }

    lateinit var folderId: FolderId

    @BeforeEach
    fun setup() {
        val (accessCheck, project) = accountTestUtil.setupWithProject("Public API Project Controller Initial", key = PROJECT_KEY)
        val projectId = project.project!!.id.toMongoProjectId()
        folderId = project.project!!.folderId!!
        manufacturingCrudTestClient.configure(projectId, jwtToken = accessCheck.token)
        publicApiProjectCrudTestClient.configure(jwtToken = accessCheck.token)
    }

    @AfterEach
    fun teardown() {
        manufacturingCrudTestClient.reset()
        publicApiProjectCrudTestClient.reset()
    }

    @Test
    fun getProjectByKey() {
        val projects = manufacturingCrudTestClient.getProjects()
        Assertions.assertThat(projects.currentPage.size).isEqualTo(1)

        val proj = publicApiProjectCrudTestClient.getByKey(PROJECT_KEY)
        Assertions.assertThat(proj.key).isEqualTo(PROJECT_KEY)
        Assertions.assertThat(proj.name).isEqualTo("Public API Project Controller Initial")
    }

    @Test
    fun getProjectByFolder() {
        val projects = manufacturingCrudTestClient.getProjects()
        Assertions.assertThat(projects.currentPage.size).isEqualTo(1)

        val foundProjects = publicApiProjectCrudTestClient.getByFolderId(folderId)
        Assertions.assertThat(foundProjects.currentPage.size).isEqualTo(1)
        val proj = foundProjects.currentPage.first()
        Assertions.assertThat(proj.key).isEqualTo(PROJECT_KEY)
        Assertions.assertThat(proj.name).isEqualTo("Public API Project Controller Initial")
    }

    @Test
    fun getProjectByFolderNotFound() {
        val foundProjects = publicApiProjectCrudTestClient.getByFolderId(FolderId())
        Assertions.assertThat(foundProjects.currentPage).isEmpty()
    }

    @Test
    fun `create project with empty key is possible`() {
        val dto =
            ProjectCreation(
                name = "without key",
                key = null,
                folderId = folderId.idToString(),
            )
        val proj = publicApiProjectCrudTestClient.create(dto)
        Assertions.assertThat(proj.key).isNotNull()
        Assertions.assertThat(proj.key.length).isEqualTo(15)
        Assertions.assertThat(proj.name).isEqualTo("without key")
    }
}
