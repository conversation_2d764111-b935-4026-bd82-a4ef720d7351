package com.nu.bom.core.integrationtests

import com.nu.bom.core.api.COST_PER_PART
import com.nu.bom.core.api.CalculationEntityCopyController
import com.nu.bom.core.api.dtos.CompositeTriggerDtoData
import com.nu.bom.core.api.dtos.CrossProjectEntityCopyTriggerDtoData
import com.nu.bom.core.api.dtos.DuplicateEntityTriggerDtoData
import com.nu.bom.core.api.dtos.TriggerDto
import com.nu.bom.core.api.dtos.TriggerDtoData
import com.nu.bom.core.config.defaultMasterDataMocksAnswer
import com.nu.bom.core.controller.CalculationResultWithSnapshot
import com.nu.bom.core.exception.readable.ErrorCode
import com.nu.bom.core.manufacturing.entities.BaseManufacturingFields
import com.nu.bom.core.manufacturing.entities.BomEntry
import com.nu.bom.core.manufacturing.entities.Consumable
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.ElectronicComponent
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.entities.ManualManufacturingStep
import com.nu.bom.core.manufacturing.entities.ManualMaterialV2
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ManufacturingStep
import com.nu.bom.core.manufacturing.entities.ManufacturingStepLine
import com.nu.bom.core.manufacturing.entities.ProcessedMaterial
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.AreaUnits
import com.nu.bom.core.manufacturing.fieldTypes.ConfigIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.DensityUnits
import com.nu.bom.core.manufacturing.fieldTypes.Diffusivity
import com.nu.bom.core.manufacturing.fieldTypes.DiffusivityUnits
import com.nu.bom.core.manufacturing.fieldTypes.EntityRef
import com.nu.bom.core.manufacturing.fieldTypes.ExchangeRatesField
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.FoamingType
import com.nu.bom.core.manufacturing.fieldTypes.InjectionType
import com.nu.bom.core.manufacturing.fieldTypes.InternalRef
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Null
import com.nu.bom.core.manufacturing.fieldTypes.ObjectIdField
import com.nu.bom.core.manufacturing.fieldTypes.PartQuality
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.Pressure
import com.nu.bom.core.manufacturing.fieldTypes.PressureUnits
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.RunnerSystems
import com.nu.bom.core.manufacturing.fieldTypes.ShrinkageBehavior
import com.nu.bom.core.manufacturing.fieldTypes.Speed
import com.nu.bom.core.manufacturing.fieldTypes.SpeedUnits
import com.nu.bom.core.manufacturing.fieldTypes.Temperature
import com.nu.bom.core.manufacturing.fieldTypes.TemperatureUnits
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYearsUnit
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.nu.bom.core.manufacturing.service.FieldIdKey
import com.nu.bom.core.manufacturing.testentities.entityprovider.StepCreatorMaterial
import com.nu.bom.core.manufacturing.testentities.entityprovider.TestAssembly
import com.nu.bom.core.manufacturing.testentities.entityprovider.TestEntityProviderMultiLayer
import com.nu.bom.core.manufacturing.testentities.entityprovider.TestStepWithImportantParameter
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.CompositeTriggerAction
import com.nu.bom.core.model.CrossProjectEntityCopy
import com.nu.bom.core.model.DuplicateEntity
import com.nu.bom.core.model.FieldTrigger
import com.nu.bom.core.model.configurations.SemanticVersion
import com.nu.bom.core.model.toMongoProjectId
import com.nu.bom.core.service.CalculationEntityCopyService
import com.nu.bom.core.service.HistoryService
import com.nu.bom.core.service.ManufacturingCalculationService
import com.nu.bom.core.service.MasterDataService
import com.nu.bom.core.service.PartService
import com.nu.bom.core.service.change.HISTORY_TYPE
import com.nu.bom.core.service.configurations.ConfigurationService
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService
import com.nu.bom.core.service.dto.EntitySelector
import com.nu.bom.core.service.dto.ProjectSelector
import com.nu.bom.core.service.file.SecureFileService
import com.nu.bom.core.service.file.UploadType
import com.nu.bom.core.service.file.UploadablePayload
import com.nu.bom.core.service.masterdata.MdCurrencyService
import com.nu.bom.core.service.masterdata.MdDetailCrudService
import com.nu.bom.core.service.masterdata.MdHeaderCrudService
import com.nu.bom.core.technologies.manufacturings.inj.material.MaterialPlastic2
import com.nu.bom.core.technologies.steps.plasticinj.ManufacturingStepPlasticInjection2
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.user.withAccessCheck
import com.nu.bom.core.utils.AccountTestUtil
import com.nu.bom.core.utils.CalculationBuilder
import com.nu.bom.core.utils.CalculationBuilder.Companion.calculationBuilder
import com.nu.bom.core.utils.CalculationBuilderService
import com.nu.bom.core.utils.CompositeCalculationBuilder
import com.nu.bom.core.utils.CompositeCalculationBuilder.Companion.createTree
import com.nu.bom.core.utils.CompositeCalculationBuilder.TreeType
import com.nu.bom.core.utils.EntityBuilder
import com.nu.bom.core.utils.EntityBuilder.Companion.entityBuilder
import com.nu.bom.core.utils.LoadTestEntityClasses
import com.nu.bom.core.utils.ManufacturingServiceHelper
import com.nu.bom.core.utils.assertBigDecimalsEquals
import com.nu.bom.core.utils.assertDefaultManualStructure
import com.nu.bom.core.utils.assertErrorThat
import com.nu.bom.core.utils.assertSameStructure
import com.nu.bom.core.utils.getDefaultInputs
import com.nu.bom.core.utils.then
import com.nu.bom.tests.docker.MasterdataUpdateTest
import com.nu.bomrads.dto.ProjectCreationDTO
import com.nu.masterdata.dto.v1.basicdata.CurrencyDto
import com.nu.masterdata.dto.v1.detail.CurrencyMeasurementDto
import com.nu.masterdata.dto.v1.detail.DetailDto
import com.nu.masterdata.dto.v1.detail.NumericDetailValueDto
import com.nu.masterdata.dto.v1.header.HeaderDto
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import com.nu.masterdata.dto.v1.schema.CurrencyTypeDto
import com.nu.masterdata.dto.v1.schema.NumericDetailValueSchemaDto
import com.nu.masterdata.dto.v1.schema.NumericFieldSchemaDto
import com.nu.masterdata.dto.v1.schema.UnitOfMeasurementTypeDto
import com.tset.common.util.UserException
import com.tset.core.service.domain.Currency
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.SoftAssertions
import org.bson.types.ObjectId
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.io.TempDir
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.Mockito.doAnswer
import org.mockito.kotlin.any
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import reactor.core.publisher.Mono
import reactor.test.StepVerifier
import java.io.File
import java.math.BigDecimal

@SpringBootTest
@MasterdataUpdateTest
@LoadTestEntityClasses(
    packages = [
        "com.nu.bom.core.manufacturing.testentities.entityprovider",
        "com.nu.bom.core.manufacturing.testentities.multicurrency",
    ],
)
class CopyPasteIT {
    @Autowired
    private lateinit var calculationUpdateModule: ManufacturingCalculationService

    @Autowired
    private lateinit var builderService: CalculationBuilderService

    @Autowired
    private lateinit var copyPasteService: CalculationEntityCopyService

    @Autowired
    private lateinit var historyService: HistoryService

    @Autowired
    private lateinit var manufacturingServiceHelper: ManufacturingServiceHelper

    @Autowired
    private lateinit var accountUtil: AccountTestUtil

    @Autowired
    private lateinit var masterDataService: MasterDataService

    @Autowired
    private lateinit var fileService: SecureFileService

    @Autowired
    private lateinit var partService: PartService

    @Autowired
    private lateinit var currencyService: MdCurrencyService

    @Autowired
    private lateinit var headerService: MdHeaderCrudService

    @Autowired
    private lateinit var detailCrudService: MdDetailCrudService

    @TempDir
    lateinit var clientTempDir: File

    private lateinit var accessCheck: AccessCheck
    private lateinit var projectCreationDTO: ProjectCreationDTO

    companion object {
        private val DEFAULT_COPY_RULES =
            CalculationEntityCopyService.CopyRules(
                operation = CalculationEntityCopyService.Operation.COPY_PASTE,
            )

        private val OPERATION_DUPLICATE =
            CalculationEntityCopyService.CopyRules(
                operation = CalculationEntityCopyService.Operation.DUPLICATE,
            )
    }

    @BeforeEach
    fun setup() {
        val (ac, project) = accountUtil.setupWithProject(name = "CopyPasteIT", key = "CPIT")
        accessCheck = ac
        projectCreationDTO = project
        masterDataService.apply {
            doAnswer(::defaultMasterDataMocksAnswer)
                .`when`(this)
                .getLatestMasterDataByCompositeKey(accessCheck = any(), selector = any(), mode = any())
        }
    }

    @AfterEach
    fun teardown() {
        accountUtil.cleanup()
    }

    @ParameterizedTest
    @ValueSource(booleans = [false, true])
    fun `clone MANUAL root`(checkHistory: Boolean) {
        // manual root with manual sub
        val (rootSnapshot, _, accessCheck) =
            buildTree {
                withType(TreeType.MANUAL)
                withDefaultProject(projectCreationDTO)
                withSub(CompositeCalculationBuilder.create(TreeType.MANUAL))
            }

        val subSnapshot = rootSnapshot.subNodes[0].snapshot!!

        val source =
            EntitySelector(
                rootSnapshot.projectId(),
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branchId(),
                entityId = rootSnapshot.manufacturing?._id!!,
            )
        val target = ProjectSelector(rootSnapshot.projectId())

        StepVerifier
            .create(
                copyBomTree(source, target),
            ).assertNext { result ->
                assertThat(result.result.manufacturing!!.getFieldResult(COST_PER_PART)).isNotNull

                val copiedRoot = result.newRootSnapshot

                // same title, same part
                assertThat(copiedRoot.title).isEqualTo(rootSnapshot.title)
                assertThat(copiedRoot.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(rootSnapshot.getBaseManufacturing()?.partId)

                // same root structure
                assertThat(copiedRoot.manufacturing).isNotNull
                assertSameStructure(rootSnapshot.manufacturing!!, copiedRoot.manufacturing!!, ignoreBomEntries = true)

                assertThat(copiedRoot.subNodes).singleElement().satisfies({ copiedSubRelation ->
                    val copiedSub = copiedSubRelation.snapshot
                    assertThat(copiedSub).isNotNull

                    // same title, same part
                    assertThat(copiedSub!!.title).isEqualTo(subSnapshot.title)
                    assertThat(copiedSub.getBaseManufacturing()?.partId)
                        .isNotNull
                        .isEqualTo(subSnapshot.getBaseManufacturing()?.partId)

                    // same sub structure
                    assertThat(copiedSub.manufacturing).isNotNull
                    assertSameStructure(subSnapshot.manufacturing!!, copiedSub.manufacturing!!, ignoreBomEntries = true)
                })

                if (checkHistory) {
                    // assert history entry
                    assertThat(copiedRoot).isNotNull

                    assertHistory(
                        accessCheck,
                        copiedRoot,
                        expectedTriggers =
                            listOf(
                                CopyPasteTriggerAssertion(
                                    sourceType = Entities.MANUFACTURING.name,
                                    sourceDisplayName = "DefaultPart",
                                    targetParentType = null,
                                    targetParentDisplayName = null,
                                ),
                            ),
                    )
                }
            }.verifyComplete()
    }

    @ParameterizedTest
    @ValueSource(booleans = [false, true])
    fun `clone MANUAL sub`(checkHistory: Boolean) {
        // manual calc - 3 levels
        val (rootSnapshot, _, accessCheck) =
            buildTree {
                withType(TreeType.MANUAL)
                withDefaultProject(projectCreationDTO)
                withSub(
                    createTree {
                        withType(TreeType.MANUAL)
                        withCustomizer { it.withTitle("Sub") }
                        withSub(
                            createTree {
                                withType(TreeType.MANUAL)
                                withCustomizer { it.withTitle("SubSub") }
                            },
                        )
                    },
                )
            }

        val subSnapshot = rootSnapshot.subNodes[0].snapshot!!
        val subSubSnapshot = subSnapshot.subNodes[0].snapshot!!

        val source =
            EntitySelector(
                subSnapshot.projectId(),
                bomNodeId = subSnapshot.bomNodeId(),
                branchId = subSnapshot.branchId(),
                entityId = subSnapshot.manufacturing?._id!!,
            )
        val target = ProjectSelector(rootSnapshot.projectId())

        StepVerifier
            .create(
                copyBomTree(source, target),
            ).assertNext { result ->
                assertThat(result.result.manufacturing!!.getFieldResult(COST_PER_PART)).isNotNull

                val copiedSub = result.newRootSnapshot

                // same title, same part
                assertThat(copiedSub.title).isEqualTo("Sub")
                assertThat(copiedSub.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(subSnapshot.getBaseManufacturing()?.partId)

                // same structure
                assertThat(copiedSub.manufacturing).isNotNull
                assertSameStructure(subSnapshot.manufacturing!!, copiedSub.manufacturing!!, ignoreBomEntries = true)

                assertThat(copiedSub.subNodes).singleElement().satisfies({ copiedSubSubRelation ->
                    val copiedSubSub = copiedSubSubRelation.snapshot
                    assertThat(copiedSubSub).isNotNull

                    // same title, same part
                    assertThat(copiedSubSub!!.title).isEqualTo("SubSub")
                    assertThat(copiedSubSub.getBaseManufacturing()?.partId)
                        .isNotNull
                        .isEqualTo(subSubSnapshot.getBaseManufacturing()?.partId)

                    // same sub sub structure
                    assertThat(copiedSubSub.manufacturing).isNotNull
                    assertSameStructure(
                        subSubSnapshot.manufacturing!!,
                        copiedSubSub.manufacturing!!,
                        ignoreBomEntries = true,
                    )
                })

                if (checkHistory) {
                    // assert history entry
                    assertThat(copiedSub).isNotNull

                    assertHistory(
                        accessCheck,
                        copiedSub,
                        expectedTriggers =
                            listOf(
                                CopyPasteTriggerAssertion(
                                    sourceType = Entities.MANUFACTURING.name,
                                    sourceDisplayName = "DefaultPart",
                                    targetParentType = null,
                                    targetParentDisplayName = null,
                                ),
                            ),
                    )
                }
            }.verifyComplete()
    }

    @ParameterizedTest
    @ValueSource(booleans = [false, true])
    fun `copy MANUAL sub`(checkHistory: Boolean) {
        // manual calc - 2 levels
        val (rootSnapshot, _, accessCheck) = createManualTreeWithSub()

        val subSnapshot = rootSnapshot.subNodes[0].snapshot!!

        val source =
            EntitySelector(
                subSnapshot.projectId(),
                bomNodeId = subSnapshot.bomNodeId(),
                branchId = subSnapshot.branchId(),
                entityId = subSnapshot.manufacturing?._id!!,
            )

        val target =
            EntitySelector(
                subSnapshot.projectId(),
                bomNodeId = subSnapshot.bomNodeId(),
                branchId = subSnapshot.branchId(),
                entityId = subSnapshot.manufacturing!!._id,
            )

        StepVerifier
            .create(
                copyPasteService.copyEntity(accessCheck, source, target, DEFAULT_COPY_RULES),
            ).assertNext { result ->
                assertThat(result.result.manufacturing!!.getFieldResult(COST_PER_PART)).isNotNull

                val updatedSub = result.newRootSnapshot

                // same title, part for sub
                assertThat(updatedSub.title).isEqualTo("Sub")
                assertThat(updatedSub.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(subSnapshot.getBaseManufacturing()?.partId)

                // same structure on sub
                assertThat(updatedSub.manufacturing).isNotNull
                assertSameStructure(subSnapshot.manufacturing!!, updatedSub.manufacturing!!, ignoreBomEntries = true)

                assertThat(updatedSub.subNodes).singleElement().satisfies({ copiedSubRel ->
                    assertThat(copiedSubRel.snapshot).isNotNull
                    val copiedSub = copiedSubRel.snapshot!!

                    // same title, part for copied sub
                    assertThat(copiedSub.title).isEqualTo("Sub")
                    assertThat(copiedSub.getBaseManufacturing()?.partId)
                        .isNotNull
                        .isEqualTo(subSnapshot.getBaseManufacturing()?.partId)

                    // same structure on copied sub
                    assertThat(copiedSub.manufacturing).isNotNull
                    assertSameStructure(subSnapshot.manufacturing!!, copiedSub.manufacturing!!, ignoreBomEntries = true)
                })

                if (checkHistory) {
                    // assert history entry
                    assertThat(updatedSub).isNotNull

                    assertHistory(
                        accessCheck,
                        updatedSub,
                        expectedTriggers =
                            listOf(
                                CopyPasteTriggerAssertion(
                                    sourceType = Entities.MANUFACTURING.name,
                                    sourceDisplayName = "DefaultPart",
                                    targetParentType = Entities.MANUFACTURING.name,
                                    targetParentDisplayName = "DefaultPart",
                                ),
                            ),
                    )
                }
            }.verifyComplete()
    }

    @ParameterizedTest
    @ValueSource(booleans = [false, true])
    fun `copy MANUAL root`(checkHistory: Boolean) {
        // manual calc - 2 levels
        val (rootSnapshot, _, accessCheck) = createManualTreeWithSub()

        val subSnapshot = rootSnapshot.subNodes[0].snapshot!!

        val source =
            EntitySelector(
                rootSnapshot.projectId(),
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branchId(),
                entityId = rootSnapshot.manufacturing?._id!!,
            )

        val target =
            EntitySelector(
                subSnapshot.projectId(),
                bomNodeId = subSnapshot.bomNodeId(),
                branchId = subSnapshot.branchId(),
                entityId = subSnapshot.manufacturing?._id!!,
            )

        StepVerifier
            .create(
                copyPasteService.copyEntity(accessCheck, source, target, DEFAULT_COPY_RULES),
            ).assertNext { result ->
                assertThat(result.result.manufacturing!!.getFieldResult(COST_PER_PART)).isNotNull

                val updatedSub = result.newRootSnapshot

                // same title, part, refkey for sub
                assertThat(updatedSub.title).isEqualTo("Sub")
                assertThat(updatedSub.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(subSnapshot.getBaseManufacturing()?.partId)

                // same structure on sub
                assertThat(updatedSub.manufacturing).isNotNull
                assertSameStructure(subSnapshot.manufacturing!!, updatedSub.manufacturing!!, ignoreBomEntries = true)

                // assert copied root under original sub
                assertThat(updatedSub.subNodes).singleElement().satisfies({ copiedRootRel ->
                    assertThat(copiedRootRel.snapshot).isNotNull
                    val copiedRoot = copiedRootRel.snapshot!!

                    // same title, part, but new refkey for copied root
                    assertThat(copiedRoot.title).isEqualTo("Root")
                    assertThat(copiedRoot.getBaseManufacturing()?.partId)
                        .isNotNull
                        .isEqualTo(subSnapshot.getBaseManufacturing()?.partId)

                    // same structure on copied root
                    assertThat(copiedRoot.manufacturing).isNotNull
                    assertSameStructure(
                        rootSnapshot.manufacturing!!,
                        copiedRoot.manufacturing!!,
                        ignoreBomEntries = true,
                    )

                    // assert recursively copied sub
                    assertThat(copiedRoot.subNodes).singleElement().satisfies({ copiedSubRel ->
                        assertThat(copiedSubRel.snapshot).isNotNull
                        val copiedSub = copiedSubRel.snapshot!!

                        // same title, part, but new refkey for recursively copied sub
                        assertThat(copiedSub.title).isEqualTo("Sub")
                        assertThat(copiedSub.getBaseManufacturing()?.partId)
                            .isNotNull
                            .isEqualTo(subSnapshot.getBaseManufacturing()?.partId)

                        // same structure on copied sub
                        assertThat(copiedSub.manufacturing).isNotNull
                        assertSameStructure(
                            subSnapshot.manufacturing!!,
                            copiedSub.manufacturing!!,
                            ignoreBomEntries = true,
                        )
                    })
                })

                if (checkHistory) {
                    // assert history entry
                    assertThat(updatedSub).isNotNull

                    assertHistory(
                        accessCheck,
                        updatedSub,
                        expectedTriggers =
                            listOf(
                                CopyPasteTriggerAssertion(
                                    sourceType = Entities.MANUFACTURING.name,
                                    sourceDisplayName = "DefaultPart",
                                    targetParentType = Entities.MANUFACTURING.name,
                                    targetParentDisplayName = "DefaultPart",
                                ),
                            ),
                    )
                }
            }.verifyComplete()
    }

    private fun createManualTreeWithSub() =
        buildTree {
            withType(TreeType.MANUAL)
            withDefaultProject(projectCreationDTO)
            withCustomizer { it.withTitle("Root") }
            withSub(
                createTree {
                    withType(TreeType.MANUAL)
                    withCustomizer { it.withTitle("Sub") }
                },
            )
        }

    @ParameterizedTest
    @ValueSource(booleans = [false, true])
    fun `copy MANUAL root - check fields`(withOverrides: Boolean) {
        val (preSourceRoot, _, accessCheck) =
            buildTree {
                withType(TreeType.MANUAL)
                withDefaultProject(projectCreationDTO)
                withCustomizer { root ->
                    root
                        .withTitle("SourceRoot")
                        .withInput(
                            mapOf(
                                "lifeTime" to TimeInYears(3.0, TimeInYearsUnit.YEAR),
                                "location" to Text("tset.ref.classification.hungary"),
                                "averageUsableProductionVolumePerYear" to QuantityUnit(555.0),
                                "peakUsableProductionVolumePerYear" to QuantityUnit(666.0),
                            ),
                        )
                }
            }

        val (targetRoot, _, _) =
            buildTree {
                withType(TreeType.MANUAL)
                withDefaultProject(projectCreationDTO)
                withCustomizer {
                    it
                        .withTitle("TargetRoot")
                        .withInput(
                            mapOf(
                                "lifeTime" to TimeInYears(10.0, TimeInYearsUnit.YEAR),
                                "location" to Text("tset.ref.classification.austria"),
                                "averageUsableProductionVolumePerYear" to QuantityUnit(1000.0),
                                "peakUsableProductionVolumePerYear" to QuantityUnit(1200.0),
                            ),
                        )
                }
            }

        val (sourceRoot, _, _) =
            when (withOverrides) {
                true ->
                    manufacturingServiceHelper.setFieldAndPublish(
                        accessCheck,
                        preSourceRoot,
                        "averageUsableProductionVolumePerYear" to QuantityUnit(558.0),
                    )

                false -> Triple(preSourceRoot, null, null)
            }

        val source =
            EntitySelector(
                sourceRoot.projectId(),
                bomNodeId = sourceRoot.bomNodeId(),
                branchId = sourceRoot.branchId(),
                entityId = sourceRoot.manufacturing?._id!!,
            )

        val target =
            EntitySelector(
                targetRoot.projectId(),
                bomNodeId = targetRoot.bomNodeId(),
                branchId = targetRoot.branchId(),
                entityId = targetRoot.manufacturing?._id!!,
            )

        StepVerifier
            .create(
                copyPasteService.copyEntity(accessCheck, source, target, DEFAULT_COPY_RULES),
            ).assertNext { result ->
                assertThat(result.result.manufacturing!!.getFieldResult(COST_PER_PART)).isNotNull

                val updatedTargetRoot = result.newRootSnapshot

                // same title, part, refkey for sub
                assertThat(updatedTargetRoot.title).isEqualTo("TargetRoot")
                assertThat(updatedTargetRoot.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(targetRoot.getBaseManufacturing()?.partId)

                // same structure on root
                assertThat(updatedTargetRoot.manufacturing).isNotNull
                assertSameStructure(
                    targetRoot.manufacturing!!,
                    updatedTargetRoot.manufacturing!!,
                    ignoreBomEntries = true,
                )

                // assert copied root under original root
                assertThat(updatedTargetRoot.subNodes).singleElement().satisfies({ copiedRootRel ->
                    assertThat(copiedRootRel.snapshot).isNotNull
                    val copiedRoot = copiedRootRel.snapshot!!

                    // same title, part, but new refkey for copied root
                    assertThat(copiedRoot.title).isEqualTo("SourceRoot")
                    assertThat(copiedRoot.getBaseManufacturing()?.partId)
                        .isNotNull
                        .isEqualTo(targetRoot.getBaseManufacturing()?.partId)

                    // same structure on copied root
                    assertThat(copiedRoot.manufacturing).isNotNull
                    assertSameStructure(sourceRoot.manufacturing!!, copiedRoot.manufacturing!!, ignoreBomEntries = true)

                    // assert field changes due to root -> sub transformation
                    val copiedManufacturing = copiedRoot.manufacturing!!

                    val lifeTime = copiedManufacturing.getFieldResult("lifeTime")!!.res as BigDecimal
                    val location = copiedManufacturing.getFieldResult("locationName")!!.res as String
                    val peakUsableProductionVolumePerYear =
                        copiedManufacturing.getFieldResult("peakUsableProductionVolumePerYear")!!.res as BigDecimal
                    val averageUsableProductionVolumePerYear =
                        copiedManufacturing.getFieldResult("averageUsableProductionVolumePerYear")!!.res as BigDecimal

                    // assert that lifeTime and volumes are recalculated based on parent, and the override is intact
                    assertBigDecimalsEquals(lifeTime, 10.0.toBigDecimal(), "")
                    assertThat(location).isEqualTo("Hungary")
                    assertBigDecimalsEquals(peakUsableProductionVolumePerYear, 1200.0.toBigDecimal(), "")
                    when (withOverrides) {
                        true -> assertBigDecimalsEquals(averageUsableProductionVolumePerYear, 558.0.toBigDecimal(), "")
                        false ->
                            assertBigDecimalsEquals(
                                averageUsableProductionVolumePerYear,
                                1000.0.toBigDecimal(),
                                "",
                            )
                    }
                })
            }.verifyComplete()
    }

    @ParameterizedTest
    @ValueSource(booleans = [false, true])
    fun `clone MANUAL sub - check fields`(withOverrides: Boolean) {
        // manual calc - 3 levels
        val (preRootSnapshot, _, accessCheck) =
            buildTree {
                withType(TreeType.MANUAL)
                withDefaultProject(projectCreationDTO)
                withCustomizer { root ->
                    root
                        .withTitle("Root")
                        .withInput(
                            mapOf(
                                "lifeTime" to TimeInYears(10.0, TimeInYearsUnit.YEAR),
                                "location" to Text("tset.ref.classification.austria"),
                                "averageUsableProductionVolumePerYear" to QuantityUnit(1000.0),
                                "peakUsableProductionVolumePerYear" to QuantityUnit(1200.0),
                            ),
                        )
                }
                withSub(
                    CompositeCalculationBuilder
                        .create(TreeType.MANUAL)
                        .withCustomizer { sub ->
                            sub
                                .withTitle("Sub")
                                .withInput(
                                    mapOf(
                                        "location" to Text("tset.ref.classification.hungary"),
                                    ),
                                )
                        },
                )
            }

        val preSubSnapshot = preRootSnapshot.subNodes[0].snapshot!!

        val (subSnapshot, _, _) =
            when (withOverrides) {
                true ->
                    manufacturingServiceHelper.setFieldAndPublish(
                        accessCheck,
                        preSubSnapshot,
                        "averageUsableProductionVolumePerYear" to QuantityUnit(558.0),
                    )

                false -> Triple(preSubSnapshot, null, null)
            }

        val manufacturingBeforeCopy = subSnapshot.manufacturing!!

        val lifeTimeBc = manufacturingBeforeCopy.getFieldResult("lifeTime")!!.res as BigDecimal
        val locationBc = manufacturingBeforeCopy.getFieldResult("locationName")!!.res as String
        val productionVolumePerYearBc =
            manufacturingBeforeCopy.getFieldResult("peakUsableProductionVolumePerYear")!!.res as BigDecimal
        val averageProductionVolumePerYearBc =
            manufacturingBeforeCopy.getFieldResult("averageUsableProductionVolumePerYear")!!.res as BigDecimal

        // assert that - before copy - subman fields are inheriting parent values (except for the override)
        assertBigDecimalsEquals(lifeTimeBc, 10.0.toBigDecimal(), "")
        assertThat(locationBc).isEqualTo("Hungary")
        assertBigDecimalsEquals(productionVolumePerYearBc, 1200.0.toBigDecimal(), "")
        when (withOverrides) {
            true -> assertBigDecimalsEquals(averageProductionVolumePerYearBc, 558.0.toBigDecimal(), "")
            false -> assertBigDecimalsEquals(averageProductionVolumePerYearBc, 1000.0.toBigDecimal(), "")
        }

        val source =
            EntitySelector(
                subSnapshot.projectId(),
                bomNodeId = subSnapshot.bomNodeId(),
                branchId = subSnapshot.branchId(),
                entityId = subSnapshot.manufacturing!!._id,
            )
        val target = ProjectSelector(subSnapshot.projectId())

        StepVerifier
            .create(
                copyBomTree(source, target),
            ).assertNext { result ->
                assertThat(result.result.manufacturing!!.getFieldResult(COST_PER_PART)).isNotNull

                val copiedSub = result.newRootSnapshot

                // same title, same part
                assertThat(copiedSub.title).isEqualTo("Sub")
                assertThat(copiedSub.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(subSnapshot.getBaseManufacturing()?.partId)

                // same structure
                assertThat(copiedSub.manufacturing).isNotNull
                assertSameStructure(subSnapshot.manufacturing!!, copiedSub.manufacturing!!, ignoreBomEntries = true)

                // assert field changes due to sub -> root transformation
                val copiedManufacturing = copiedSub.manufacturing!!

                val lifeTime = copiedManufacturing.getFieldResult("lifeTime")!!.res as BigDecimal
                val location = copiedManufacturing.getFieldResult("locationName")!!.res as String
                val peakUsableProductionVolumePerYear =
                    copiedManufacturing.getFieldResult("peakUsableProductionVolumePerYear")!!.res as BigDecimal
                val averageUsableProductionVolumePerYear =
                    copiedManufacturing.getFieldResult("averageUsableProductionVolumePerYear")!!.res as BigDecimal

                // assert that fields of the now root calculation are kept as initial fields, and the override is intact
                assertBigDecimalsEquals(lifeTime, 10.0.toBigDecimal(), "")
                assertThat(location).isEqualTo("Hungary")
                assertBigDecimalsEquals(peakUsableProductionVolumePerYear, 1200.0.toBigDecimal(), "")
                when (withOverrides) {
                    true -> assertBigDecimalsEquals(averageUsableProductionVolumePerYear, 558.0.toBigDecimal(), "")
                    false -> assertBigDecimalsEquals(averageUsableProductionVolumePerYear, 1000.0.toBigDecimal(), "")
                }
            }.verifyComplete()
    }

    @ParameterizedTest
    @ValueSource(booleans = [false, true])
    fun `copy MANUAL step1`(checkHistory: Boolean) {
        // manual calc
        val (rootSnapshot, rootEntity, accessCheck) =
            buildTree {
                withType(TreeType.MANUAL)
                withDefaultProject(projectCreationDTO)
            }
        assertDefaultManualStructure(rootEntity)

        val step1 = rootEntity.findByEntityName("Step1")!!

        val source =
            EntitySelector(
                rootSnapshot.projectId(),
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branchId(),
                entityId = step1._id,
            )

        val target =
            EntitySelector(
                rootSnapshot.projectId(),
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branchId(),
                entityId = rootSnapshot.manufacturing!!._id,
            )

        StepVerifier
            .create(
                copyPasteService.copyEntity(accessCheck, source, target, DEFAULT_COPY_RULES),
            ).assertNext { result ->
                assertThat(result.result.manufacturing!!.getFieldResult(COST_PER_PART)).isNotNull

                val updatedRoot = result.newRootSnapshot

                // same title, part, refkey for root
                assertThat(updatedRoot.title).isEqualTo(rootSnapshot.title)
                assertThat(updatedRoot.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(rootSnapshot.getBaseManufacturing()?.partId)

                // assert copied step1 on top [children Material1, CPart1 are not taken]
                assertThat(
                    updatedRoot.manufacturing!!
                        .children
                        .filter { it.getEntityTypeAnnotation() == Entities.MANUFACTURING_STEP },
                ).singleElement().satisfies({ copiedStep1 ->

                    assertThat(copiedStep1.displayName).isEqualTo(step1.displayName)

                    // assert that Material1, CPart1 are excluded when Step1 is copied
                    assertThat(step1.children.find { it.displayName == "Material1" }).isNotNull
                    assertThat(step1.children.find { it.displayName == "CPart1" }).isNotNull
                    assertThat(copiedStep1.children.find { it.displayName == "Material1" }).isNull()
                    assertThat(copiedStep1.children.find { it.displayName == "CPart1" }).isNull()

                    // assert original reinserted step2 below
                    assertThat(
                        copiedStep1.children.filter { it.getEntityTypeAnnotation() == Entities.MANUFACTURING_STEP },
                    ).singleElement()
                        .satisfies({ reinsertedStep2 ->
                            val step2 = rootEntity.findByEntityName("Step2")!!
                            assertSameStructure(step2, reinsertedStep2)
                        })
                })

                if (checkHistory) {
                    // assert history entry
                    assertThat(updatedRoot).isNotNull

                    assertHistory(
                        accessCheck,
                        updatedRoot,
                        expectedTriggers =
                            listOf(
                                CopyPasteTriggerAssertion(
                                    sourceType = Entities.MANUFACTURING_STEP.name,
                                    sourceDisplayName = "Step1",
                                    targetParentType = Entities.MANUFACTURING.name,
                                    targetParentDisplayName = "DefaultPart",
                                ),
                            ),
                    )
                }
            }.verifyComplete()
    }

    private fun buildTree(builder: CompositeCalculationBuilder.() -> Unit): CalculationBuilderService.Result =
        builderService
            .build(
                CompositeCalculationBuilder
                    .buildTree(builder),
                accessCheck,
            ).withAccessCheck(accessCheck)
            .block()!!

    private fun buildTree(calculationBuilder: CalculationBuilder): CalculationBuilderService.Result =
        builderService.build(calculationBuilder, accessCheck).block()!!

    @Test
    fun `copy sub with cost module and step to calculation`() {
        // manual calc with sub calc fake cost module
        val (rootSnapshot, rootEntity, accessCheck) =
            buildTree(
                calculationBuilder {
                    withDefaultProject(projectCreationDTO)
                    withClass(ManualManufacturing::class)
                    withChildren(
                        listOf(
                            entityBuilder {
                                withClass(ManufacturingStep::class)
                                withName("step1")
                            },
                            entityBuilder {
                                withClass(ManufacturingStep::class)
                                withName("step2")
                                withSubs(
                                    listOf(
                                        calculationBuilder {
                                            withClass(ManualManufacturing::class)
                                            withTitle("Sub calculation")
                                            withBomEntryClass(BomEntry::class)
                                            withChild(
                                                entityBuilder {
                                                    withClass(ProcessedMaterial::class)
                                                    withName("Source Processed material")
                                                },
                                            )
                                        },
                                    ),
                                )
                            },
                        ),
                    )
                },
            )

        // target calc
        val (targetSnapshot, targetEntity, _) =
            buildTree(
                calculationBuilder {
                    withDefaultProject(projectCreationDTO)
                    withClass(ManualManufacturing::class)
                    withChildren(
                        listOf(
                            entityBuilder {
                                withClass(ManufacturingStep::class)
                                withName("step2")
                            },
                        ),
                    )
                },
            )
        val step1 = rootEntity.findByEntityName("step1")!!
        val sub =
            rootEntity
                .findByEntityName("step2")!!
                .findByEntityName("ManualManufacturing")!!

        val source =
            listOf(
                EntitySelector(
                    rootSnapshot.projectId(),
                    bomNodeId = rootSnapshot.bomNodeId(),
                    branchId = rootSnapshot.branchId(),
                    entityId = sub._id,
                ),
                EntitySelector(
                    rootSnapshot.projectId(),
                    bomNodeId = rootSnapshot.bomNodeId(),
                    branchId = rootSnapshot.branchId(),
                    entityId = step1._id,
                ),
            )

        val target =
            EntitySelector(
                targetSnapshot.projectId(),
                bomNodeId = targetSnapshot.bomNodeId(),
                branchId = targetSnapshot.branchId(),
                entityId = targetSnapshot.manufacturing!!._id,
            )

        StepVerifier
            .create(
                copyPasteService.copyEntity(accessCheck, source, target, DEFAULT_COPY_RULES),
            ).assertNext { result ->
                assertThat(result.result.manufacturing!!.getFieldResult(COST_PER_PART)).isNotNull

                val updatedRoot = result.newRootSnapshot

                // same title, part, refkey for root
                assertThat(updatedRoot.title).isEqualTo(targetSnapshot.title)
                assertThat(updatedRoot.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(targetSnapshot.getBaseManufacturing()?.partId)

                // assert copied step1 on top
                assertThat(
                    updatedRoot.manufacturing!!
                        .children
                        .filter { it.getEntityTypeAnnotation() == Entities.MANUFACTURING_STEP },
                ).singleElement().satisfies({ copiedStep1 ->

                    assertThat(copiedStep1.displayName).isEqualTo(step1.displayName)
                    // assert original reinserted step2 below
                    assertThat(
                        copiedStep1.children.filter { it.getEntityTypeAnnotation() == Entities.MANUFACTURING_STEP },
                    ).singleElement()
                        .satisfies({ reinsertedStep2 ->
                            val step2 = targetEntity.findByEntityName("step2")!!
                            assertSameStructure(step2, reinsertedStep2)
                        })
                })
                // assert copied sub
                assertThat(
                    updatedRoot.manufacturing!!
                        .children
                        .filter { it.getEntityTypeAnnotation() == Entities.BOM_ENTRY },
                ).isNotNull()
            }.verifyComplete()
    }

    @ParameterizedTest
    @ValueSource(booleans = [false, true])
    fun `copy MANUAL step1 modularized`(checkHistory: Boolean) {
        val stepFields =
            mapOf(
                "shotWeightPerCycle" to QuantityUnit(3.0),
                "adjustedThermalDiffusivityForFoaming" to Diffusivity(0.5, DiffusivityUnits.QCM_PER_SECONDS),
                "averageInjectionVelocity" to Speed(5.0, SpeedUnits.M_PER_MIN),
                "density" to Density(0.1, DensityUnits.KILOGRAM_PER_CM),
                "deployedWeightPerPart" to Pieces(5.0),
                "injectingTemperature" to Temperature(270.0, TemperatureUnits.CELSIUS),
                "materialFactor" to Rate(3.0),
                "maxInternalMoldPressure" to Pressure(30.0, PressureUnits.BAR),
                "maxMoldTemperature" to Temperature(50.0, TemperatureUnits.CELSIUS),
                "minInternalMoldPressure" to Pressure(2.0, PressureUnits.BAR),
                "minMoldTemperature" to Temperature(15.0, TemperatureUnits.CELSIUS),
                "moldSeparationTemperature" to Temperature(76.0, TemperatureUnits.CELSIUS),
                "runnerSystems" to RunnerSystems(RunnerSystems.Selection.HOT_COLD_RUNNER),
                "shrinkageBehavior" to ShrinkageBehavior(ShrinkageBehavior.Selection.AVERAGE_VOLUME_SHRINKAGE),
                "sprueRate" to Rate(3.0),
                "sprueRateForInjectionTime" to Rate(3.0),
                "thermalDiffusivity" to Diffusivity(0.5, DiffusivityUnits.QCM_PER_SECONDS),
                "technologyModel" to Text("ManufacturingInjection2"),
            )
        val partFields =
            mapOf(
                "maxWallThickness" to Length(1.2, LengthUnits.CENTIMETER),
                "netWeightPerPart" to QuantityUnit(Weight(1.0, WeightUnits.KILOGRAM)),
                "shapeId" to Text("S_386"),
                "foaming" to FoamingType.CHEMICAL,
                "foamingDensity" to Density(5.0, DensityUnits.KILOGRAM_PER_CM),
                "injectionType" to InjectionType.NORMAL_INJECTION,
                "mainWallThickness" to Length(1.0, LengthUnits.CENTIMETER),
                "partHeight" to Length(50.0, LengthUnits.MILLIMETER),
                "partLength" to Length(50.0, LengthUnits.MILLIMETER),
                "partOuterDiameter" to Length(10.0, LengthUnits.MILLIMETER),
                "partQuality" to PartQuality.STANDARD,
                "partWidth" to Length(50.0, LengthUnits.MILLIMETER),
                "projectedAreaPerPart" to Area(1.5, AreaUnits.QDM),
            )

        // manual calc
        val (rootSnapshot, rootEntity, accessCheck) =
            buildTree {
                withDefaultProject(projectCreationDTO)
                withType(TreeType.CUSTOM)
                withCustomBuild(
                    calculationBuilder {
                        withClass(ManualManufacturing::class)
                        withTitle("MANUAL")
                        withInput(partFields)
                        withChild(
                            entityBuilder {
                                withClass(ManufacturingStepPlasticInjection2::class)
                                withName("ManufacturingStepPlasticInjection2")
                                withIsolated()
                                withInput(stepFields)
                            },
                        )
                    },
                )
            }

        val step1 = rootEntity.findByEntityName("ManufacturingStepPlasticInjection2")!!

        val source =
            EntitySelector(
                rootSnapshot.projectId!!,
                bomNodeId = rootSnapshot.bomNodeId!!,
                branchId = rootSnapshot.branchId(),
                entityId = step1._id,
            )

        val target =
            EntitySelector(
                rootSnapshot.projectId!!,
                bomNodeId = rootSnapshot.bomNodeId!!,
                branchId = rootSnapshot.branchId(),
                entityId = rootSnapshot.manufacturing!!._id,
            )

        StepVerifier
            .create(
                copyPasteService.copyEntity(accessCheck, source, target, DEFAULT_COPY_RULES),
            ).assertNext { (result, updatedRoot) ->
                assertThat(result.manufacturing?.getFieldResult("foaming")).isNotNull
                assertThat(result.manufacturing?.getFieldResult("shapeId")).isNotNull

                // same title, part, refkey for root
                assertThat(updatedRoot.title).isEqualTo(rootSnapshot.title)
                assertThat(updatedRoot.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(rootSnapshot.getBaseManufacturing()?.partId)

                if (checkHistory) {
                    // assert history entry
                    assertThat(updatedRoot).isNotNull

                    assertHistory(
                        accessCheck,
                        updatedRoot,
                        expectedTriggers =
                            listOf(
                                CopyPasteTriggerAssertion(
                                    sourceType = Entities.MANUFACTURING_STEP.name,
                                    sourceDisplayName = "ManufacturingStepPlasticInjection2",
                                    targetParentType = Entities.MANUFACTURING.name,
                                    targetParentDisplayName = "DefaultPart",
                                ),
                            ),
                    )
                }
            }.verifyComplete()
    }

    @ParameterizedTest
    @ValueSource(booleans = [false, true])
    fun `copy MANUAL step1 + material modularized`(checkHistory: Boolean) {
        val partFields =
            mapOf(
                "maxWallThickness" to Length(1.2, LengthUnits.CENTIMETER),
                "netWeightPerPart" to QuantityUnit(Weight(1.0, WeightUnits.KILOGRAM)),
                "shapeId" to Text("S_386"),
                "foaming" to FoamingType.CHEMICAL,
                "foamingDensity" to Density(5.0, DensityUnits.KILOGRAM_PER_CM),
                "injectionType" to InjectionType.NORMAL_INJECTION,
                "mainWallThickness" to Length(1.0, LengthUnits.CENTIMETER),
                "partHeight" to Length(50.0, LengthUnits.MILLIMETER),
                "partLength" to Length(50.0, LengthUnits.MILLIMETER),
                "partOuterDiameter" to Length(10.0, LengthUnits.MILLIMETER),
                "partQuality" to PartQuality.STANDARD,
                "partWidth" to Length(50.0, LengthUnits.MILLIMETER),
                "projectedAreaPerPart" to Area(1.5, AreaUnits.QDM),
                "lotSize" to Pieces(8333.toBigDecimal()),
                "technologyModel" to Text("ManufacturingInjection2"),
                "costModuleConfigurationIdentifier" to
                    ConfigIdentifier(
                        ConfigurationIdentifier.tset("tset", SemanticVersion(2, 0)),
                    ),
            )

        // manual calc
        fun customBuild(
            stepInput: Map<String, EntityRef>,
            plasticInput: Map<String, EntityRef>,
        ) = calculationBuilder {
            withClass(ManualManufacturing::class)
            withTitle("MANUAL")
            withInput(partFields)
            withChild(
                entityBuilder {
                    withClass(ManufacturingStepPlasticInjection2::class)
                    withIsolated()
                    withName("ManufacturingStepPlasticInjection2")
                    withInput(stepInput)
                    withIsolated()
                },
            )
            withChild(
                entityBuilder {
                    withClass(MaterialPlastic2::class)
                    withName("MaterialPlastic2")
                    withInput(plasticInput)
                    withIsolated()
                },
            )
        }

        val (rootSnapshot, rootEntity, accessCheck) =
            buildTree {
                withType(TreeType.CUSTOM)
                withCustomBuild(customBuild(mapOf(), mapOf()))
                withDefaultProject(projectCreationDTO)
            }
        assertConfigurationIdentifier(rootSnapshot.manufacturing, "tset")

        val (targetRootSnapshot, _, _) =
            buildTree {
                withType(TreeType.CUSTOM)
                withDefaultProject(projectCreationDTO)
                withCustomBuild(
                    calculationBuilder {
                        withClass(ManualManufacturing::class)
                        withTitle("TARGET")
                    },
                )
            }
        assertEmptyConfigurationIdentifier(targetRootSnapshot.manufacturing)

        val step1 = rootEntity.findByEntityName("ManufacturingStepPlasticInjection2")!!
        val material = rootEntity.findByEntityName("MaterialPlastic2")!!

        val (_, updatedBomNode) =
            calculationUpdateModule
                .transformAndCalculate(
                    accessCheck,
                    rootSnapshot.bomNodeId(),
                    rootSnapshot.branchId(),
                    triggerAction = FieldTrigger(rootSnapshot.bomNodeId(), step1._id, "linkedMaterial"),
                    customInputs = { _, _ ->

                        mapOf(
                            FieldIdKey("linkedMaterial", step1.entityId) to EntityRef(material.entityId),
                            FieldIdKey("linkedStep", material.entityId) to EntityRef(step1.entityId),
                        )
                    },
                    transformManufacturing = { base, _, _ ->
                        Mono.just(base)
                    },
                ).block()!!

        val sourceStep =
            EntitySelector.fromEntitySelectorDto(
                CalculationEntityCopyController.EntitySelectorDto(
                    rootSnapshot.projectId().toHexString(),
                    bomNodeId = updatedBomNode.bomNodeId().toHexString(),
                    branchId = updatedBomNode.branchIdStr(),
                    entityId = step1._id.toHexString(),
                ),
            )
        val sourceMaterial =
            EntitySelector.fromEntitySelectorDto(
                CalculationEntityCopyController.EntitySelectorDto(
                    rootSnapshot.projectId().toHexString(),
                    bomNodeId = updatedBomNode.bomNodeId().toHexString(),
                    branchId = updatedBomNode.branchIdStr(),
                    entityId = material._id.toHexString(),
                ),
            )

        val target =
            EntitySelector(
                targetRootSnapshot.projectId(),
                bomNodeId = targetRootSnapshot.bomNodeId!!,
                branchId = targetRootSnapshot.branchId(),
                entityId = targetRootSnapshot.manufacturing!!._id,
            )

        StepVerifier
            .create(
                copyPasteService.copyEntity(
                    accessCheck,
                    listOf(sourceMaterial, sourceStep),
                    target,
                    DEFAULT_COPY_RULES,
                ),
            ).assertNext { (result, updatedRoot) ->
                assertThat(result.manufacturing?.getFieldResult("foaming")).isNotNull
                assertThat(result.manufacturing?.getFieldResult("shapeId")).isNotNull
                assertConfigurationIdentifier(result.manufacturing, "tset")

                val stepResult =
                    result.manufacturing?.findChild { manufacturingEntity ->
                        manufacturingEntity.name == "ManufacturingStepPlasticInjection2" && manufacturingEntity._id != step1._id
                    }!!

                val materialResult =
                    result.manufacturing?.findChild { manufacturingEntity ->
                        (manufacturingEntity.name == "MaterialPlastic2" && manufacturingEntity._id != material._id)
                    }!!

                val soft = SoftAssertions()

                val linkedMaterial = stepResult.getFieldResult("linkedMaterial")?.res
                soft
                    .assertThat(linkedMaterial)
                    .isEqualTo(InternalRef(materialResult._id))

                // same title, part, refkey for root
                soft
                    .assertThat(updatedRoot.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(rootSnapshot.getBaseManufacturing()?.partId)
                soft.assertAll()

                if (checkHistory) {
                    // assert history entry
                    assertThat(updatedRoot).isNotNull

                    assertHistory(
                        accessCheck,
                        updatedRoot,
                        expectedTriggers =
                            listOf(
                                CopyPasteTriggerAssertion(
                                    sourceType = Entities.MANUFACTURING_STEP.name,
                                    sourceDisplayName = "ManufacturingStepPlasticInjection2",
                                    targetParentType = Entities.MANUFACTURING.name,
                                    targetParentDisplayName = "DefaultPart",
                                ),
                            ),
                    )
                }
            }.verifyComplete()
    }

    @ParameterizedTest
    @ValueSource(booleans = [false, true])
    fun `copy MANUAL material modularized`(checkHistory: Boolean) {
        val partFields =
            mapOf(
                "maxWallThickness" to Length(1.2, LengthUnits.CENTIMETER),
                "netWeightPerPart" to QuantityUnit(Weight(1.0, WeightUnits.KILOGRAM)),
                "shapeId" to Text("S_386"),
                "foaming" to FoamingType.CHEMICAL,
                "foamingDensity" to Density(5.0, DensityUnits.KILOGRAM_PER_CM),
                "injectionType" to InjectionType.NORMAL_INJECTION,
                "mainWallThickness" to Length(1.0, LengthUnits.CENTIMETER),
                "partHeight" to Length(50.0, LengthUnits.MILLIMETER),
                "partLength" to Length(50.0, LengthUnits.MILLIMETER),
                "partOuterDiameter" to Length(10.0, LengthUnits.MILLIMETER),
                "partQuality" to PartQuality.STANDARD,
                "partWidth" to Length(50.0, LengthUnits.MILLIMETER),
                "projectedAreaPerPart" to Area(1.5, AreaUnits.QDM),
                "technologyModel" to Text("ManufacturingInjection2"),
            )

        // manual calc
        val (rootSnapshot, rootEntity, accessCheck) =
            buildTree {
                withDefaultProject(projectCreationDTO)
                withType(TreeType.CUSTOM)
                withCustomBuild(
                    calculationBuilder {
                        withClass(ManualManufacturing::class)
                        withTitle("MANUAL")
                        withInput(partFields)
                        withChild(
                            entityBuilder {
                                withClass(ManufacturingStepPlasticInjection2::class)
                                withIsolated()
                                withName("ManufacturingStepPlasticInjection2")
                            },
                        )
                        withChild(
                            entityBuilder {
                                withClass(MaterialPlastic2::class)
                                withName("MaterialPlastic2")
                                withIsolated()
                            },
                        )
                    },
                )
            }

        val step1 = rootEntity.findByEntityName("ManufacturingStepPlasticInjection2")!!
        val material = rootEntity.findByEntityName("MaterialPlastic2")!!

        step1.replaceOrAddFieldResult("linkedMaterial") { EntityRef(material.entityId) }

        val sourceMaterial =
            EntitySelector(
                rootSnapshot.projectId!!,
                bomNodeId = rootSnapshot.bomNodeId!!,
                branchId = rootSnapshot.branchId(),
                entityId = material._id,
            )

        val target =
            EntitySelector(
                rootSnapshot.projectId!!,
                bomNodeId = rootSnapshot.bomNodeId!!,
                branchId = rootSnapshot.branchId(),
                entityId = rootSnapshot.manufacturing!!._id,
            )

        StepVerifier
            .create(
                copyPasteService.copyEntity(accessCheck, sourceMaterial, target, DEFAULT_COPY_RULES),
            ).assertNext { (result, updatedRoot) ->
                assertThat(result.manufacturing?.getFieldResult("foaming")).isNotNull
                assertThat(result.manufacturing?.getFieldResult("shapeId")).isNotNull

                val materialResult =
                    result.manufacturing?.findChild { manufacturingEntity ->
                        (manufacturingEntity.name == "MaterialPlastic2" && manufacturingEntity._id != material._id)
                    }!!

                assertThat(materialResult.isolated).isTrue()
                assertThat(materialResult.name).isEqualTo(material.name)

                // same title, part, refkey for root
                assertThat(updatedRoot.title).isEqualTo(rootSnapshot.title)
                assertThat(updatedRoot.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(rootSnapshot.getBaseManufacturing()?.partId)

                if (checkHistory) {
                    // assert history entry
                    assertThat(updatedRoot).isNotNull

                    assertHistory(
                        accessCheck,
                        updatedRoot,
                        expectedTriggers =
                            listOf(
                                CopyPasteTriggerAssertion(
                                    sourceType = Entities.MANUFACTURING_STEP.name,
                                    sourceDisplayName = "ManufacturingStepPlasticInjection2",
                                    targetParentType = Entities.MANUFACTURING.name,
                                    targetParentDisplayName = "DefaultPart",
                                ),
                            ),
                    )
                }
            }.verifyComplete()
    }

    @Test
    fun `copy MANUAL material keep the existing config identifier of target`() {
        val sourcePartFields = createPartFields("tset1")
        val targetPartFields = createPartFields("tset2")

        // manual calc
        val (rootSnapshot, rootEntity, accessCheck) = buildPlasticInjectionCalculationTree(sourcePartFields, "MANUAL")
        assertConfigurationIdentifier(rootSnapshot.manufacturing, "tset1")

        val (targetRootSnapshot, _, _) = buildPlasticInjectionCalculationTree(targetPartFields, "TARGET")
        assertConfigurationIdentifier(targetRootSnapshot.manufacturing, "tset2")

        val material = rootEntity.findByEntityName("MaterialPlastic2")!!

        val sourceMaterial =
            EntitySelector(
                rootSnapshot.projectId!!,
                bomNodeId = rootSnapshot.bomNodeId!!,
                branchId = rootSnapshot.branchId(),
                entityId = material._id,
            )

        val target =
            EntitySelector(
                targetRootSnapshot.projectId!!,
                bomNodeId = targetRootSnapshot.bomNodeId!!,
                branchId = targetRootSnapshot.branchId(),
                entityId = targetRootSnapshot.manufacturing!!._id,
            )

        StepVerifier
            .create(
                copyPasteService.copyEntity(accessCheck, listOf(sourceMaterial), target, DEFAULT_COPY_RULES),
            ).assertNext { (result) ->
                assertConfigurationIdentifier(result.manufacturing, "tset2")
            }.verifyComplete()
    }

    private fun assertConfigurationIdentifier(
        entity: ManufacturingEntity?,
        key: String,
    ) {
        val tsetKey = ConfigurationService.ensureTsetPrefix(key)
        assertThat(entity?.getFieldResult(BaseManufacturingFields::costModuleConfigurationIdentifier.name)?.res.toString()).isEqualTo(
            "Jsondata(data=ConfigurationIdentifier(key=$tsetKey, version=SemanticVersion(major=2, minor=0)))",
        )
    }

    private fun assertEmptyConfigurationIdentifier(entity: ManufacturingEntity?) {
        assertThat(entity?.getFieldResult(BaseManufacturingFields::costModuleConfigurationIdentifier.name)?.res.toString()).isEqualTo(
            "Jsondata(data=ConfigurationIdentifier(key=, version=SemanticVersion(major=1, minor=0)))",
        )
    }

    private fun buildPlasticInjectionCalculationTree(
        fields: Map<String, FieldResult<out Any, FieldResult<out Any, *>>>,
        title: String,
    ) = buildTree {
        withDefaultProject(projectCreationDTO)
        withType(TreeType.CUSTOM)
        withCustomBuild(
            calculationBuilder {
                withClass(ManualManufacturing::class)
                withTitle(title)
                withInput(fields)
                withChild(
                    entityBuilder {
                        withClass(ManufacturingStepPlasticInjection2::class)
                        withIsolated()
                        withName("ManufacturingStepPlasticInjection2")
                    },
                )
                withChild(
                    entityBuilder {
                        withClass(MaterialPlastic2::class)
                        withName("MaterialPlastic2")
                        withIsolated()
                    },
                )
            },
        )
    }

    @ParameterizedTest
    @ValueSource(booleans = [false, true])
    fun `copy MANUAL step2`(checkHistory: Boolean) {
        // manual calc
        val (rootSnapshot, rootEntity, accessCheck) =
            buildTree {
                withType(TreeType.MANUAL)
                withDefaultProject(projectCreationDTO)
            }
        assertDefaultManualStructure(rootEntity)

        val step2 = rootEntity.findByEntityName("Step2")!!

        val source =
            EntitySelector(
                rootSnapshot.projectId(),
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branchId(),
                entityId = step2._id,
            )

        val target =
            EntitySelector(
                rootSnapshot.projectId(),
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branchId(),
                entityId = rootSnapshot.manufacturing!!._id,
            )

        StepVerifier
            .create(
                copyPasteService.copyEntity(accessCheck, source, target, DEFAULT_COPY_RULES),
            ).assertNext { result ->
                assertThat(result.result.manufacturing!!.getFieldResult(COST_PER_PART)).isNotNull

                val updatedRoot = result.newRootSnapshot

                // same title, part, refkey for root
                assertThat(updatedRoot.title).isEqualTo(rootSnapshot.title)
                assertThat(updatedRoot.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(rootSnapshot.getBaseManufacturing()?.partId)

                // assert copied step2 on top [child step1 is not taken]
                assertThat(
                    updatedRoot.manufacturing!!
                        .children
                        .filter { it.getEntityTypeAnnotation() == Entities.MANUFACTURING_STEP },
                ).singleElement().satisfies({ copiedStep2 ->

                    assertThat(copiedStep2.displayName).isEqualTo(step2.displayName)

                    // assert original reinserted step2 below
                    assertThat(
                        copiedStep2.children.filter { it.getEntityTypeAnnotation() == Entities.MANUFACTURING_STEP },
                    ).singleElement()
                        .satisfies({ reinsertedStep2 ->
                            assertSameStructure(step2, reinsertedStep2)
                        })
                })

                if (checkHistory) {
                    // assert history entry
                    assertThat(updatedRoot).isNotNull

                    assertHistory(
                        accessCheck,
                        updatedRoot,
                        expectedTriggers =
                            listOf(
                                CopyPasteTriggerAssertion(
                                    sourceType = Entities.MANUFACTURING_STEP.name,
                                    sourceDisplayName = "Step2",
                                    targetParentType = Entities.MANUFACTURING.name,
                                    targetParentDisplayName = "DefaultPart",
                                ),
                            ),
                    )
                }
            }.verifyComplete()
    }

    @ParameterizedTest
    @ValueSource(booleans = [false, true])
    fun `copy MANUAL material1`(checkHistory: Boolean) {
        // manual calc
        val (rootSnapshot, rootEntity, accessCheck) =
            buildTree {
                withType(TreeType.MANUAL)
                withDefaultProject(projectCreationDTO)
            }
        assertDefaultManualStructure(rootEntity)

        val step2 = rootEntity.findByEntityName("Step2")!!
        val material1 = rootEntity.findByEntityName("Material1")!!

        val source =
            EntitySelector(
                rootSnapshot.projectId(),
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branchId(),
                entityId = material1._id,
            )

        val target =
            EntitySelector(
                rootSnapshot.projectId(),
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branchId(),
                entityId = step2._id,
            )

        StepVerifier
            .create(
                copyPasteService.copyEntity(accessCheck, source, target, DEFAULT_COPY_RULES),
            ).assertNext { result ->
                assertThat(result.result.manufacturing!!.getFieldResult(COST_PER_PART)).isNotNull

                val updatedRoot = result.newRootSnapshot

                // same title, part, refkey for root
                assertThat(updatedRoot.title).isEqualTo(rootSnapshot.title)
                assertThat(updatedRoot.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(rootSnapshot.getBaseManufacturing()?.partId)

                // assert copied material1 on step2
                assertThat(
                    updatedRoot.manufacturing!!
                        .children
                        .filter { it.getEntityTypeAnnotation() == Entities.MANUFACTURING_STEP },
                ).singleElement().satisfies({ updatedStep2 ->

                    assertThat(updatedStep2.children.find { it.displayName == material1.displayName }).isNotNull

                    // assert untouched step1
                    assertThat(
                        updatedStep2.children.filter { it.getEntityTypeAnnotation() == Entities.MANUFACTURING_STEP },
                    ).singleElement()
                        .satisfies({ untouchedStep1 ->
                            val step1 = rootEntity.findByEntityName("Step1")!!
                            assertSameStructure(step1, untouchedStep1)
                        })
                })

                if (checkHistory) {
                    // assert history entry
                    assertThat(updatedRoot).isNotNull

                    assertHistory(
                        accessCheck,
                        updatedRoot,
                        expectedTriggers =
                            listOf(
                                CopyPasteTriggerAssertion(
                                    sourceType = Entities.MATERIAL.name,
                                    sourceDisplayName = "Material1",
                                    targetParentType = Entities.MANUFACTURING_STEP.name,
                                    targetParentDisplayName = "Step2",
                                ),
                            ),
                    )
                }
            }.verifyComplete()
    }

    @Test
    fun `copy CONSUMABLE to step less`() {
        val (sourceRootSnapshot, _, accessCheck) =
            buildTree(
                calculationBuilder {
                    withClass(ManualManufacturing::class)
                    withDefaultProject(projectCreationDTO)
                    withInput(getDefaultInputs(ManualManufacturing::class))
                    withTitle("Root1")
                    withChild(
                        entityBuilder {
                            withClass(Consumable::class)
                            withName("Consumable1")
                        },
                    )
                },
            )

        val (targetRootSnapshot, _, _) =
            buildTree(
                calculationBuilder {
                    withClass(ManualManufacturing::class)
                    withDefaultProject(projectCreationDTO)
                    withInput(getDefaultInputs(ManualManufacturing::class))
                    withTitle("Root2")
                },
            )

        Assertions.assertNull(getConsumableEntity(targetRootSnapshot))
        val target =
            EntitySelector(
                targetRootSnapshot.projectId(),
                bomNodeId = targetRootSnapshot.bomNodeId(),
                branchId = targetRootSnapshot.branchId(),
                entityId = targetRootSnapshot.manufacturing?._id!!,
            )

        val source =
            EntitySelector(
                sourceRootSnapshot.projectId(),
                bomNodeId = sourceRootSnapshot.bomNodeId(),
                branchId = sourceRootSnapshot.branchId(),
                entityId = sourceRootSnapshot.manufacturing?.findByEntityType(Entities.CONSUMABLE)!!._id,
            )

        val copied =
            copyPasteService
                .copyEntity(
                    accessCheck,
                    source,
                    target,
                    DEFAULT_COPY_RULES,
                ).block()
        val copiedConsumable = getConsumableEntity(copied!!.newRootSnapshot)
        Assertions.assertNotNull(copiedConsumable)
        Assertions.assertEquals(targetRootSnapshot.bomNodeId(), copied.newRootSnapshot.bomNodeId())
        Assertions.assertEquals(targetRootSnapshot.bomNodeId(), copiedConsumable!!.parents.first().bomNodeId)
        Assertions.assertEquals("Consumable1", copiedConsumable.name)
    }

    @ParameterizedTest
    @ValueSource(booleans = [false, true])
    fun `copy MANUAL cPart1`(checkHistory: Boolean) {
        // manual calc
        val (rootSnapshot, rootEntity, accessCheck) =
            buildTree {
                withType(TreeType.MANUAL)
                withDefaultProject(projectCreationDTO)
            }

        assertDefaultManualStructure(rootEntity)

        val step2 = rootEntity.findByEntityName("Step2")!!
        val cPart1 = rootEntity.findByEntityName("CPart1")!!

        val source =
            EntitySelector(
                rootSnapshot.projectId(),
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branchId(),
                entityId = cPart1._id,
            )

        val target =
            EntitySelector(
                rootSnapshot.projectId(),
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branchId(),
                entityId = step2._id,
            )

        StepVerifier
            .create(
                copyPasteService.copyEntity(accessCheck, source, target, DEFAULT_COPY_RULES),
            ).assertNext { result ->
                assertThat(result.result.manufacturing!!.getFieldResult(COST_PER_PART)).isNotNull

                val updatedRoot = result.newRootSnapshot

                // same title, part, refkey for root
                assertThat(updatedRoot.title).isEqualTo(rootSnapshot.title)
                assertThat(updatedRoot.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(rootSnapshot.getBaseManufacturing()?.partId)

                // assert copied cPart1 on step2
                assertThat(
                    updatedRoot.manufacturing!!
                        .children
                        .filter { it.getEntityTypeAnnotation() == Entities.MANUFACTURING_STEP },
                ).singleElement().satisfies({ updatedStep2 ->

                    assertThat(updatedStep2.children.find { it.displayName == cPart1.displayName }).isNotNull

                    // assert untouched step1
                    assertThat(
                        updatedStep2.children.filter { it.getEntityTypeAnnotation() == Entities.MANUFACTURING_STEP },
                    ).singleElement()
                        .satisfies({ untouchedStep1 ->
                            val step1 = rootEntity.findByEntityName("Step1")!!
                            assertSameStructure(step1, untouchedStep1)
                        })
                })

                if (checkHistory) {
                    // assert history entry
                    assertThat(updatedRoot).isNotNull

                    assertHistory(
                        accessCheck,
                        updatedRoot,
                        expectedTriggers =
                            listOf(
                                CopyPasteTriggerAssertion(
                                    sourceType = Entities.C_PART.name,
                                    sourceDisplayName = "CPart1",
                                    targetParentType = Entities.MANUFACTURING_STEP.name,
                                    targetParentDisplayName = "Step2",
                                ),
                            ),
                    )
                }
            }.verifyComplete()
    }

    @ParameterizedTest
    @ValueSource(booleans = [false, true])
    fun `copy step to GENERATED`(checkHistory: Boolean) {
        val (sourceSnapshot, sourceRoot, accessCheck) =
            buildTree {
                withType(TreeType.GENERATED_TEST)
                withDefaultProject(projectCreationDTO)
            }

        val (targetSnapshot, targetRoot, _) =
            buildTree {
                withType(TreeType.GENERATED_TEST)
                withDefaultProject(projectCreationDTO)
            }

        val sourceStep = sourceRoot.findByEntityName("step")!!
        val targetMaterial = targetRoot.findByEntityName("material")!!

        val source =
            EntitySelector(
                sourceSnapshot.projectId(),
                bomNodeId = sourceSnapshot.bomNodeId(),
                branchId = sourceSnapshot.branchId(),
                entityId = sourceStep._id,
            )

        val target =
            EntitySelector(
                targetSnapshot.projectId(),
                bomNodeId = targetSnapshot.bomNodeId(),
                branchId = targetSnapshot.branchId(),
                entityId = targetMaterial._id,
            )

        StepVerifier
            .create(
                copyPasteService.copyEntity(accessCheck, source, target, DEFAULT_COPY_RULES),
            ).assertNext { result ->
                val root = result.newRootSnapshot.manufacturing!!
                val updatedSnapshot = result.newRootSnapshot

                // assert entity structure
                assertThat(root::class).isEqualTo(TestEntityProviderMultiLayer::class)
                assertThat(root.getFieldResult("importantParameterAggregation")).isNotNull

                assertThat(root.children.find { it::class == StepCreatorMaterial::class })
                    .isNotNull
                    .satisfies({ processedMaterial ->
                        assertThat(processedMaterial!!.name).isEqualTo("material")

                        assertThat(
                            processedMaterial.children.find { it::class == TestStepWithImportantParameter::class },
                        ).isNotNull
                            .satisfies({ copiedStep ->
                                assertThat(copiedStep!!.name).isEqualTo("step")

                                assertThat(copiedStep.children.find { it::class == TestAssembly::class })
                                    .isNotNull
                                    .satisfies({ assemblyStep ->
                                        assertThat(assemblyStep!!.name).isEqualTo("assembly-stepparam-1")

                                        assertThat(
                                            assemblyStep.children.find { it::class == TestStepWithImportantParameter::class },
                                        ).isNotNull
                                            .satisfies({ step ->
                                                assertThat(step!!.name).isEqualTo("step")

                                                assertThat(
                                                    step.children.find { it::class == TestStepWithImportantParameter::class },
                                                ).isNotNull
                                                    .satisfies({ cleaningStep ->
                                                        assertThat(cleaningStep!!.name).isEqualTo("cleaning-step")
                                                    })
                                            })
                                    })
                            })
                    })

                if (checkHistory) {
                    // assert history entry
                    assertThat(updatedSnapshot).isNotNull

                    assertHistory(
                        accessCheck,
                        updatedSnapshot,
                        expectedTriggers =
                            listOf(
                                CopyPasteTriggerAssertion(
                                    sourceType = Entities.MANUFACTURING_STEP.name,
                                    sourceDisplayName = "step",
                                    targetParentType = Entities.PROCESSED_MATERIAL.name,
                                    targetParentDisplayName = null,
                                ),
                            ),
                    )
                }
            }.verifyComplete()
    }

    @ParameterizedTest
    @ValueSource(booleans = [false, true])
    fun `multi select clone MANUAL root & MANUAL sub`(checkHistory: Boolean) {
        // manual root with manual sub
        val (rootSnapshot, _, accessCheck) =
            buildTree {
                withType(TreeType.MANUAL)
                withDefaultProject(projectCreationDTO)
                withSub(CompositeCalculationBuilder.create(TreeType.MANUAL))
            }

        val subSnapshot = rootSnapshot.subNodes[0].snapshot!!

        val source1 =
            EntitySelector(
                rootSnapshot.projectId(),
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branchId(),
                entityId = rootSnapshot.manufacturing?._id!!,
            )
        val source2 =
            EntitySelector(
                subSnapshot.projectId(),
                bomNodeId = subSnapshot.bomNodeId(),
                branchId = subSnapshot.branchId(),
                entityId = subSnapshot.manufacturing?._id!!,
            )
        val target = ProjectSelector(rootSnapshot.projectId())

        StepVerifier
            .create(
                copyPasteService.copyBomTree(accessCheck, listOf(source1, source2), target),
            ).assertNext { result ->
                assertThat(result.result.manufacturing!!.getFieldResult(COST_PER_PART)).isNotNull

                val copiedRoot = result.newRootSnapshot

                // same title, same part
                assertThat(copiedRoot.title).isEqualTo(rootSnapshot.title)
                assertThat(copiedRoot.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(rootSnapshot.getBaseManufacturing()?.partId)

                // same root structure
                assertThat(copiedRoot.manufacturing).isNotNull
                assertSameStructure(rootSnapshot.manufacturing!!, copiedRoot.manufacturing!!, ignoreBomEntries = true)

                assertThat(copiedRoot.subNodes).singleElement().satisfies({ copiedSubRelation ->
                    val copiedSub = copiedSubRelation.snapshot
                    assertThat(copiedSub).isNotNull

                    // same title, same part
                    assertThat(copiedSub!!.title).isEqualTo(subSnapshot.title)
                    assertThat(copiedSub.getBaseManufacturing()?.partId)
                        .isNotNull
                        .isEqualTo(subSnapshot.getBaseManufacturing()?.partId)

                    // same sub structure
                    assertThat(copiedSub.manufacturing).isNotNull
                    assertSameStructure(subSnapshot.manufacturing!!, copiedSub.manufacturing!!, ignoreBomEntries = true)
                })

                if (checkHistory) {
                    // assert copied root history entry
                    assertThat(copiedRoot).isNotNull

                    assertHistory(
                        accessCheck,
                        copiedRoot,
                        expectedTriggers =
                            listOf(
                                CopyPasteTriggerAssertion(
                                    sourceType = Entities.MANUFACTURING.name,
                                    sourceDisplayName = "DefaultPart",
                                    targetParentType = null,
                                    targetParentDisplayName = null,
                                ),
                            ),
                    )
                }
            }.assertNext { result ->
                assertThat(result.result.manufacturing!!.getFieldResult(COST_PER_PART)).isNotNull

                val copiedSub = result.newRootSnapshot

                // same title, same part
                assertThat(copiedSub.title).isEqualTo(subSnapshot.title)
                assertThat(copiedSub.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(subSnapshot.getBaseManufacturing()?.partId)

                // same structure
                assertThat(copiedSub.manufacturing).isNotNull
                assertSameStructure(subSnapshot.manufacturing!!, copiedSub.manufacturing!!, ignoreBomEntries = true)

                assertThat(copiedSub.subNodes).isEmpty()

                if (checkHistory) {
                    // assert copied sub history entry
                    assertThat(copiedSub).isNotNull

                    assertHistory(
                        accessCheck,
                        copiedSub,
                        expectedTriggers =
                            listOf(
                                CopyPasteTriggerAssertion(
                                    sourceType = Entities.MANUFACTURING.name,
                                    sourceDisplayName = "DefaultPart",
                                    targetParentType = null,
                                    targetParentDisplayName = null,
                                ),
                            ),
                    )
                }
            }.verifyComplete()
    }

    @Test
    fun `multi select copy MANUAL Step1,Material1,CPart1 to StepB`() {
        val tree1 =
            CalculationBuilder
                .create(ManualManufacturing::class)
                .withDefaultProject(projectCreationDTO)
                .withInput(getDefaultInputs(ManualManufacturing::class))
                .withTitle("MANUAL-1")
                .withChild(
                    EntityBuilder
                        .create(ManualManufacturingStep::class)
                        .withName("Step2")
                        .withInput(getDefaultInputs(ManualManufacturingStep::class))
                        .withChild(
                            EntityBuilder
                                .create(ManualManufacturingStep::class)
                                .withName("Step1")
                                .withInput(
                                    getDefaultInputs(ManualManufacturingStep::class),
                                ).withChildren(
                                    listOf(
                                        EntityBuilder
                                            .create(ManualMaterialV2::class)
                                            .withName("Material1")
                                            .withInput(getDefaultInputs(ManualMaterialV2::class)),
                                        EntityBuilder
                                            .create(ElectronicComponent::class)
                                            .withName("CPart1")
                                            .withInput(getDefaultInputs(ElectronicComponent::class)),
                                    ),
                                ),
                        ),
                )

        val tree2 =
            CalculationBuilder
                .create(ManualManufacturing::class)
                .withDefaultProject(projectCreationDTO)
                .withInput(getDefaultInputs(ManualManufacturing::class))
                .withTitle("MANUAL-2")
                .withChild(
                    EntityBuilder
                        .create(ManualManufacturingStep::class)
                        .withName("StepB")
                        .withInput(getDefaultInputs(ManualManufacturingStep::class))
                        .withChild(
                            EntityBuilder
                                .create(ManualManufacturingStep::class)
                                .withName("StepA")
                                .withInput(getDefaultInputs(ManualManufacturingStep::class))
                                .withChild(
                                    EntityBuilder
                                        .create(ManualMaterialV2::class)
                                        .withName("MaterialX")
                                        .withInput(getDefaultInputs(ManualMaterialV2::class)),
                                ),
                        ),
                )

        val (sourceSnapshot, _, accessCheck) = builderService.build(tree1, accessCheck).block()!!

        val material1 = sourceSnapshot.manufacturing!!.findChild { it.name == "Material1" }!!

        val cPart1 = sourceSnapshot.manufacturing!!.findChild { it.name == "CPart1" }!!

        val step1 = sourceSnapshot.manufacturing!!.findChild { it.name == "Step1" }!!

        val (targetSnapshot, _, _) = builderService.build(tree2, accessCheck).block()!!

        val stepB = targetSnapshot.manufacturing!!.findChild { it.name == "StepB" }!!

        val source1 =
            EntitySelector(
                sourceSnapshot.projectId(),
                bomNodeId = sourceSnapshot.bomNodeId(),
                branchId = sourceSnapshot.branchId(),
                entityId = material1._id,
            )
        val source2 =
            EntitySelector(
                sourceSnapshot.projectId(),
                bomNodeId = sourceSnapshot.bomNodeId(),
                branchId = sourceSnapshot.branchId(),
                entityId = cPart1._id,
            )
        val source3 =
            EntitySelector(
                sourceSnapshot.projectId(),
                bomNodeId = sourceSnapshot.bomNodeId(),
                branchId = sourceSnapshot.branchId(),
                entityId = step1._id,
            )

        val target =
            EntitySelector(
                targetSnapshot.projectId(),
                bomNodeId = targetSnapshot.bomNodeId(),
                branchId = targetSnapshot.branchId(),
                entityId = stepB._id,
            )

        StepVerifier
            .create(
                copyPasteService.copyEntity(
                    accessCheck,
                    listOf(source1, source2, source3),
                    target,
                    DEFAULT_COPY_RULES,
                ),
            ).assertNext { result ->
                assertThat(result.result.manufacturing!!.getFieldResult(COST_PER_PART)).isNotNull

                val updatedTarget = result.newRootSnapshot

                // same title, same part, same refKey
                assertThat(updatedTarget.title).isEqualTo(targetSnapshot.title)
                assertThat(updatedTarget.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(targetSnapshot.getBaseManufacturing()?.partId)

                // updated target structure (Step2/Step1/StepB(/(Material1,CPart1))/StepA(/MaterialX)))
                // [i.e. MaterialX, Material1,CPart1, StepA,StepB,Step1,Step2 in BomExplorer]
                assertThat(updatedTarget.manufacturing).isNotNull

                val targetStep1 = updatedTarget.manufacturing!!.children.single { it.displayName == "Step1" }
                val targetStepB = targetStep1.children.single { it.displayName == "StepB" }

                val groupIdValue = targetStep1.getFieldOrInitialFieldResult(ManufacturingStepLine::groupId.name)?.res
                assertThat(groupIdValue == null || Null.isNull(groupIdValue))
                    .withFailMessage { "copying a step should remove groupId field but is $groupIdValue" }
                    .isTrue

                targetStep1.children.single { it.displayName == "Material1" }
                targetStep1.children.single { it.displayName == "CPart1" }

                val targetStepA = targetStepB.children.single { it.displayName == "StepA" }
                targetStepA.children.single { it.displayName == "MaterialX" }

                // assert history entry
                assertThat(updatedTarget).isNotNull

                assertHistory(
                    accessCheck,
                    updatedTarget,
                    expectedTriggers =
                        listOf(
                            CopyPasteTriggerAssertion(
                                sourceType = Entities.MATERIAL.name,
                                sourceDisplayName = "Material1",
                                targetParentType = Entities.MANUFACTURING_STEP.name,
                                targetParentDisplayName = "StepB",
                            ),
                            CopyPasteTriggerAssertion(
                                sourceType = Entities.C_PART.name,
                                sourceDisplayName = "CPart1",
                                targetParentType = Entities.MANUFACTURING_STEP.name,
                                targetParentDisplayName = "StepB",
                            ),
                            CopyPasteTriggerAssertion(
                                sourceType = Entities.MANUFACTURING_STEP.name,
                                sourceDisplayName = "Step1",
                                targetParentType = Entities.MANUFACTURING.name,
                                targetParentDisplayName = "DefaultPart",
                            ),
                            CopyPasteTriggerAssertion(
                                sourceType = Entities.MANUFACTURING_STEP.name,
                                sourceDisplayName = "Step2",
                                targetParentType = Entities.MANUFACTURING.name,
                                targetParentDisplayName = "DefaultPart",
                            ),
                        ),
                )
            }.verifyComplete()
    }

    @ParameterizedTest
    @ValueSource(booleans = [false, true])
    fun `duplicate MANUAL cycleTypeStepGroup1`(checkHistory: Boolean) {
        // manual calc
        val (rootSnapshot, rootEntity, accessCheck) =
            buildTree {
                withType(TreeType.MANUAL)
                withDefaultProject(projectCreationDTO)
                withCustomizer {
                    it.children.find { child -> child.name == "Step2" }!!.withChild(
                        entityBuilder {
                            withType(Entities.CYCLETIME_STEP_GROUP)
                            withName("CycleTimeStepGroup1")
                            withChild(
                                entityBuilder {
                                    withType(Entities.CYCLETIME_STEP)
                                    withClass(CycleTimeStep::class)
                                    withName("CycleTimeStep1")
                                },
                            )
                        },
                    )
                    it
                }
            }

        val step2 = rootEntity.findByEntityName("Step2")!!
        val group1 = rootEntity.findByEntityName("CycleTimeStepGroup1")!!

        val source =
            EntitySelector(
                rootSnapshot.projectId(),
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branchId(),
                entityId = group1._id,
            )

        val target =
            EntitySelector(
                rootSnapshot.projectId(),
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branchId(),
                entityId = step2._id,
            )

        StepVerifier
            .create(
                copyPasteService.copyEntity(accessCheck, source, target, OPERATION_DUPLICATE),
            ).assertNext { result ->
                assertThat(result.result.manufacturing!!.getFieldResult(COST_PER_PART)).isNotNull

                val updatedRoot = result.newRootSnapshot

                // same title, part, refkey for root
                assertThat(updatedRoot.title).isEqualTo(rootSnapshot.title)
                assertThat(updatedRoot.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(rootSnapshot.getBaseManufacturing()?.partId)

                // assert copied group1 on step2
                assertThat(
                    updatedRoot.manufacturing!!
                        .children
                        .filter { it.getEntityTypeAnnotation() == Entities.MANUFACTURING_STEP },
                ).singleElement().satisfies({ updatedStep2 ->

                    // assert original group1 is intact
                    assertThat(
                        updatedStep2.children.filter {
                            it.displayName == group1.displayName && it.entityId == group1.entityId
                        },
                    ).singleElement()

                    // assert duplicate group1 has new id, and has its children
                    assertThat(
                        updatedStep2.children.filter {
                            it.displayName == group1.displayName && it.entityId != group1.entityId
                        },
                    ).singleElement()
                        .satisfies({ duplicatedGroup1 ->
                            assertThat(duplicatedGroup1.children.filter { it.displayName == "CycleTimeStep1" })
                                .singleElement()
                        })

                    // assert untouched step1
                    assertThat(
                        updatedStep2.children.filter { it.getEntityTypeAnnotation() == Entities.MANUFACTURING_STEP },
                    ).singleElement()
                        .satisfies({ untouchedStep1 ->
                            val step1 = rootEntity.findByEntityName("Step1")!!
                            assertSameStructure(step1, untouchedStep1)
                        })
                })

                if (checkHistory) {
                    // assert history entry
                    assertThat(updatedRoot).isNotNull

                    assertHistory(
                        accessCheck,
                        updatedRoot,
                        expectedTriggers =
                            listOf(
                                DuplicateTriggerAssertion(
                                    sourceType = Entities.CYCLETIME_STEP_GROUP.name,
                                    sourceDisplayName = "CycleTimeStepGroup1",
                                    targetParentType = Entities.MANUFACTURING_STEP.name,
                                    targetParentDisplayName = "Step2",
                                ),
                            ),
                    )
                }
            }.verifyComplete()
    }

    @ParameterizedTest
    @ValueSource(booleans = [false, true])
    fun `duplicate MANUAL cycleTypeStep1`(checkHistory: Boolean) {
        // manual calc
        val (rootSnapshot, rootEntity, accessCheck) =
            buildTree {
                withType(TreeType.MANUAL)
                withDefaultProject(projectCreationDTO)
                withCustomizer {
                    it.children.find { child -> child.name == "Step2" }!!.withChild(
                        entityBuilder {
                            withType(Entities.CYCLETIME_STEP_GROUP)
                            withName("CycleTimeStepGroup1")
                            withChild(
                                entityBuilder {
                                    withType(Entities.CYCLETIME_STEP)
                                    withClass(CycleTimeStep::class)
                                    withName("CycleTimeStep1")
                                },
                            )
                        },
                    )
                    it
                }
            }

        val group1 = rootEntity.findByEntityName("CycleTimeStepGroup1")!!
        val cycleTimeStep1 = rootEntity.findByEntityName("CycleTimeStep1")!!

        val source =
            EntitySelector(
                rootSnapshot.projectId(),
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branchId(),
                entityId = cycleTimeStep1._id,
            )

        val target =
            EntitySelector(
                rootSnapshot.projectId(),
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branchId(),
                entityId = group1._id,
            )

        StepVerifier
            .create(
                copyPasteService.copyEntity(accessCheck, source, target, OPERATION_DUPLICATE),
            ).assertNext { result ->
                assertThat(result.result.manufacturing!!.getFieldResult(COST_PER_PART)).isNotNull

                val updatedRoot = result.newRootSnapshot

                // same title, part, refkey for root
                assertThat(updatedRoot.title).isEqualTo(rootSnapshot.title)
                assertThat(updatedRoot.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(rootSnapshot.getBaseManufacturing()?.partId)

                assertThat(
                    updatedRoot.manufacturing!!
                        .children
                        .filter { it.getEntityTypeAnnotation() == Entities.MANUFACTURING_STEP },
                ).singleElement().satisfies({ step2 ->

                    // assert cycleTimeStep1 is duplicated
                    assertThat(step2.children.filter { it.displayName == group1.displayName })
                        .singleElement()
                        .satisfies({ updatedGroup1 ->

                            // assert original cycleTimeStep1 is intact
                            assertThat(
                                updatedGroup1.children.filter {
                                    it.displayName == cycleTimeStep1.displayName && it.entityId == cycleTimeStep1.entityId
                                },
                            ).singleElement()

                            // assert duplicate cycleTimeStep1 has new id
                            assertThat(
                                updatedGroup1.children.filter {
                                    it.displayName == cycleTimeStep1.displayName && it.entityId != cycleTimeStep1.entityId
                                },
                            ).singleElement()
                        })

                    // assert untouched step1
                    assertThat(step2.children.filter { it.getEntityTypeAnnotation() == Entities.MANUFACTURING_STEP })
                        .singleElement()
                        .satisfies({ untouchedStep1 ->
                            val step1 = rootEntity.findByEntityName("Step1")!!
                            assertSameStructure(step1, untouchedStep1)
                        })
                })

                if (checkHistory) {
                    // assert history entry
                    assertThat(updatedRoot).isNotNull

                    assertHistory(
                        accessCheck,
                        updatedRoot,
                        expectedTriggers =
                            listOf(
                                DuplicateTriggerAssertion(
                                    sourceType = Entities.CYCLETIME_STEP.name,
                                    sourceDisplayName = "CycleTimeStep1",
                                    targetParentType = Entities.CYCLETIME_STEP_GROUP.name,
                                    targetParentDisplayName = "CycleTimeStepGroup1",
                                ),
                            ),
                    )
                }
            }.verifyComplete()
    }

    @Test
    fun `copy MANUAL material with currencies`() {
        // 1 - manual calc with base currencies
        val (baseCurrenciesRootSnapshot, _, accessCheck) =
            buildTree(
                calculationBuilder {
                    withClass(ManualManufacturing::class)
                    withDefaultProject(projectCreationDTO)
                    withInput(getDefaultInputs(ManualManufacturing::class))
                    withTitle("Base Currencies Root")
                    withChild(
                        entityBuilder {
                            withClass(ManualManufacturingStep::class)
                            withName("Base Currencies Step1")
                            withInput(
                                getDefaultInputs(ManualManufacturingStep::class) +
                                    (ManufacturingStepLine::groupId.name to ObjectIdField()),
                            )
                            withChild(
                                entityBuilder {
                                    withClass(ManualMaterialV2::class)
                                    withName("Base Currencies Material1")
                                    withInput(getDefaultInputs(ManualMaterialV2::class))
                                },
                            )
                        },
                    )
                },
            )

        // 2 - add new currency and exchange rate
        addEth().block()

        // 3 - manual calc with extended currencies
        val (extendedCurrenciesRootSnapshot, _, _) =
            buildTree(
                calculationBuilder {
                    withClass(ManualManufacturing::class)
                    withDefaultProject(projectCreationDTO)
                    withInput(getDefaultInputs(ManualManufacturing::class))
                    withTitle("Extended Currencies Root")
                    withChild(
                        entityBuilder {
                            withClass(ManualManufacturingStep::class)
                            withName("Extended Currencies Step1")
                            withInput(getDefaultInputs(ManualManufacturingStep::class))
                            withChild(
                                entityBuilder {
                                    withClass(ManualMaterialV2::class)
                                    withName("Extended Currencies Material1")
                                    withInput(
                                        getDefaultInputs(ManualMaterialV2::class) +
                                            mapOf(
                                                "baseCurrency" to
                                                    com.nu.bom.core.manufacturing.fieldTypes.Currency(
                                                        "ETH",
                                                    ),
                                            ),
                                    )
                                },
                            )
                        },
                    )
                },
            )

        val source =
            EntitySelector(
                extendedCurrenciesRootSnapshot.projectId(),
                bomNodeId = extendedCurrenciesRootSnapshot.bomNodeId(),
                branchId = extendedCurrenciesRootSnapshot.branchId(),
                entityId = extendedCurrenciesRootSnapshot.manufacturing?._id!!,
            )

        val target =
            EntitySelector(
                baseCurrenciesRootSnapshot.projectId(),
                bomNodeId = baseCurrenciesRootSnapshot.bomNodeId(),
                branchId = baseCurrenciesRootSnapshot.branchId(),
                entityId = baseCurrenciesRootSnapshot.manufacturing?._id!!,
            )

        StepVerifier
            .create(
                copyPasteService.copyEntity(accessCheck, source, target, DEFAULT_COPY_RULES),
            ).assertNext { result ->
                assertThat(result.result.manufacturing!!.getFieldResult(COST_PER_PART)).isNotNull

                val updatedRoot = result.newRootSnapshot

                // same title, part, refkey for sub
                assertThat(updatedRoot.title).isEqualTo("Base Currencies Root")
                assertThat(updatedRoot.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(baseCurrenciesRootSnapshot.getBaseManufacturing()?.partId)

                // same structure on sub
                assertThat(updatedRoot.manufacturing).isNotNull
                assertSameStructure(
                    baseCurrenciesRootSnapshot.manufacturing!!,
                    updatedRoot.manufacturing!!,
                    ignoreBomEntries = true,
                )

                assertThat(updatedRoot.subNodes).singleElement().satisfies({ copiedSubRel ->
                    assertThat(copiedSubRel.snapshot).isNotNull
                    val copiedSub = copiedSubRel.snapshot!!

                    // same title, part, but new refkey for copied sub
                    assertThat(copiedSub.title).isEqualTo(extendedCurrenciesRootSnapshot.title)
                    assertThat(copiedSub.getBaseManufacturing()?.partId)
                        .isNotNull
                        .isEqualTo(extendedCurrenciesRootSnapshot.getBaseManufacturing()?.partId)

                    // extended exchange rate from parent
                    val copiedSubExchangeRates = copiedSub.manufacturing!!.getExchangeRates()!!
                    assertThat(copiedSubExchangeRates.children.size)
                        .isGreaterThan(
                            updatedRoot.manufacturing!!
                                .getExchangeRates()!!
                                .children.size,
                        )
                    assertThat(copiedSubExchangeRates.children.size)
                        .isGreaterThan(
                            updatedRoot.manufacturing!!
                                .getExchangeRates()!!
                                .children.size,
                        )
                    assertThat(
                        (copiedSubExchangeRates.getFieldResult("exchangeRates")!! as ExchangeRatesField).res.keys,
                    ).contains(Currency("ETH"))

                    // same structure on copied sub
                    assertThat(copiedSub.manufacturing).isNotNull
                    assertSameStructure(
                        extendedCurrenciesRootSnapshot.manufacturing!!,
                        copiedSub.manufacturing!!,
                        ignoreBomEntries = true,
                        ignoreExchangeRates = true,
                    )
                })
            }.verifyComplete()
    }

    @Test
    fun `copy MANUAL with subcurrencies`() {
        // 1 - manual calc with base currencies
        val (baseCurrenciesRootSnapshot, _, accessCheck) =
            buildTree(
                calculationBuilder {
                    withClass(ManualManufacturing::class)
                    withDefaultProject(projectCreationDTO)
                    withInput(getDefaultInputs(ManualManufacturing::class))
                    withTitle("Base Currencies Root")
                },
            )

        // 2 - add new currency and exchange rate
        addEth().block()

        // 3 - manual calc with extended currencies
        val (extendedCurrenciesRootSnapshot, _, _) =
            buildTree(
                calculationBuilder {
                    withClass(ManualManufacturing::class)
                    withDefaultProject(projectCreationDTO)
                    withInput(
                        getDefaultInputs(ManualManufacturing::class) +
                            mapOf(
                                "baseCurrency" to
                                    com.nu.bom.core.manufacturing.fieldTypes.Currency(
                                        "ETH",
                                    ),
                            ),
                    )
                    withTitle("Extended Currencies Root")
                },
            )

        val source =
            EntitySelector(
                extendedCurrenciesRootSnapshot.projectId(),
                bomNodeId = extendedCurrenciesRootSnapshot.bomNodeId(),
                branchId = extendedCurrenciesRootSnapshot.branchId(),
                entityId = extendedCurrenciesRootSnapshot.manufacturing?._id!!,
            )

        val target =
            EntitySelector(
                baseCurrenciesRootSnapshot.projectId(),
                bomNodeId = baseCurrenciesRootSnapshot.bomNodeId(),
                branchId = baseCurrenciesRootSnapshot.branchId(),
                entityId = baseCurrenciesRootSnapshot.manufacturing?._id!!,
            )

        assertErrorThat(
            copyPasteService.copyEntity(accessCheck, source, target, DEFAULT_COPY_RULES),
        ).satisfies({ e ->
            e is UserException && e.userErrorCode == ErrorCode.COPY_TARGET_IS_MISSING_CURRENCY.name
        })
    }

    @Test
    fun `copy subcalc with step line`() {
        val (targetSnap, _, _) =
            buildTree(
                calculationBuilder {
                    withClass(ManualManufacturing::class)
                    withDefaultProject(projectCreationDTO)
                },
            )
        val groupId = ObjectId()
        val (sourceSnap, _, _) =
            buildTree(
                calculationBuilder {
                    withClass(ManualManufacturing::class)
                    withDefaultProject(projectCreationDTO)
                    withChildren(
                        listOf(
                            entityBuilder {
                                withClass(ManualManufacturingStep::class)
                                withInput(mapOf(ManufacturingStepLine::groupId.name to ObjectIdField(groupId)))
                            },
                            entityBuilder {
                                withClass(ManualManufacturingStep::class)
                                withInput(mapOf(ManufacturingStepLine::groupId.name to ObjectIdField(groupId)))
                            },
                        ),
                    )
                },
            )

        val source =
            EntitySelector(
                sourceSnap.projectId(),
                bomNodeId = sourceSnap.bomNodeId(),
                branchId = sourceSnap.branchId(),
                entityId = sourceSnap.manufacturing?._id!!,
            )

        val target =
            EntitySelector(
                targetSnap.projectId(),
                bomNodeId = targetSnap.bomNodeId(),
                branchId = targetSnap.branchId(),
                entityId = targetSnap.manufacturing?._id!!,
            )

        Assertions.assertDoesNotThrow {
            copyPasteService.copyEntity(accessCheck, source, target, DEFAULT_COPY_RULES).block()
        }
    }

    @ParameterizedTest
    @ValueSource(booleans = [false, true])
    fun `clone MANUAL root and sub with attachments`(deleteFiles: Boolean) {
        // upload files
        val txtFile = File(clientTempDir, "test01.txt").apply { writeText("simple text") }
        val txtUpload = UploadablePayload.fromByteArray(txtFile.name, txtFile.readBytes())
        val jsonFile = File(clientTempDir, "test02.json").apply { writeText("{ \"mykey\":\"myval\" }") }
        val jsonUpload = UploadablePayload.fromByteArray(jsonFile.name, jsonFile.readBytes())

        val files =
            listOf(
                fileService
                    .upload(accessCheck, UploadType.BOM_NODE_ATTACHMENT, null, txtUpload)
                    .block()!!,
                fileService
                    .upload(accessCheck, UploadType.BOM_NODE_ATTACHMENT, null, jsonUpload)
                    .block()!!,
            )

        val partId =
            partService
                .createPart(
                    designation = "TestPart",
                    number = "1234",
                    accessCheck = accessCheck,
                    projectId = projectCreationDTO.project!!.id.toMongoProjectId(),
                    images = files.map { it.id },
                    projectKey = projectCreationDTO.project!!.key,
                    attachmentToVersionedPart = emptyMap(),
                ).withAccessCheck(accessCheck)
                .mapNotNull { it._id }
                .block()!!
        // manual root with manual sub
        val (rootSnapshot, _, accessCheck) =
            buildTree {
                withType(TreeType.MANUAL)
                withCustomizer {
                    it.withPart(partId)
                }
                withDefaultProject(projectCreationDTO)
                withSub(CompositeCalculationBuilder.create(TreeType.MANUAL))
            }

        val subSnapshot = rootSnapshot.subNodes[0].snapshot!!

        val source =
            EntitySelector(
                rootSnapshot.projectId(),
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branchId(),
                entityId = rootSnapshot.manufacturing?._id!!,
            )
        val target = ProjectSelector(rootSnapshot.projectId())

        StepVerifier
            .create(
                fileService
                    .list(
                        accessCheck,
                        UploadType.BOM_NODE_ATTACHMENT,
                        rootSnapshot.bomNodeId().toHexString(),
                    ).collectList(),
            ).assertNext {
                assertThat(it.size).isEqualTo(2)
            }

        if (deleteFiles) {
            files.forEach {
                fileService.delete(accessCheck, it.id).block()
            }
        }

        StepVerifier
            .create(
                copyBomTree(source, target),
            ).assertNext { result ->
                assertThat(result.result.manufacturing!!.getFieldResult(COST_PER_PART)).isNotNull

                val copiedRoot = result.newRootSnapshot

                // same title, same part
                assertThat(copiedRoot.title).isEqualTo(rootSnapshot.title)
                assertThat(copiedRoot.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(rootSnapshot.getBaseManufacturing()?.partId)

                // same root structure
                assertThat(copiedRoot.manufacturing).isNotNull
                assertSameStructure(rootSnapshot.manufacturing!!, copiedRoot.manufacturing!!, ignoreBomEntries = true)

                assertThat(copiedRoot.subNodes).singleElement().satisfies({ copiedSubRelation ->
                    val copiedSub = copiedSubRelation.snapshot
                    assertThat(copiedSub).isNotNull

                    // same title, same part
                    assertThat(copiedSub!!.title).isEqualTo(subSnapshot.title)
                    assertThat(copiedSub.getBaseManufacturing()?.partId)
                        .isNotNull
                        .isEqualTo(subSnapshot.getBaseManufacturing()?.partId)

                    // same sub structure
                    assertThat(copiedSub.manufacturing).isNotNull
                    assertSameStructure(subSnapshot.manufacturing!!, copiedSub.manufacturing!!, ignoreBomEntries = true)
                })

                StepVerifier
                    .create(
                        fileService
                            .list(
                                accessCheck,
                                UploadType.BOM_NODE_ATTACHMENT,
                                result.newRootSnapshot.bomNodeId().toHexString(),
                            ).collectList(),
                    ).assertNext {
                        if (deleteFiles) assertThat(it.size).isEqualTo(0) else assertThat(it.size).isEqualTo(2)
                    }
            }.verifyComplete()
    }

    data class CopyPasteTriggerAssertion(
        val sourceType: String?,
        val sourceDisplayName: String?,
        val targetParentType: String?,
        val targetParentDisplayName: String?,
    ) : TriggerAssertion

    data class DuplicateTriggerAssertion(
        val sourceType: String,
        val sourceDisplayName: String,
        val targetParentType: String,
        val targetParentDisplayName: String,
    ) : TriggerAssertion

    interface TriggerAssertion

    private fun assertHistory(
        accessCheck: AccessCheck,
        snapshot: BomNodeSnapshot,
        expectedTriggers: List<TriggerAssertion>,
    ) {
        StepVerifier
            .create(
                historyService.getNodeHistoryForNode(
                    accessCheck,
                    projectId = snapshot.projectId().toHexString(),
                    nodeId = snapshot.bomNodeId().toHexString(),
                    branch = snapshot.branch?.toHexString(),
                    null,
                    // snapshot.changeset?.toHexString(),
                    null,
                    HISTORY_TYPE.ALL,
                    true,
                    includeParents = true,
                    includePublish = true,
                ),
            ).assertNext { historyListDto ->

                val trigger =
                    historyListDto
                        .first()
                        .nodeChanges
                        .first()
                        .trigger
                val copyTriggers = extractCopyTriggers(trigger).toMutableList()

                // assert CrossProjectEntityCopyTriggerDtoData
                compareTriggers(
                    expectedTriggers.filterIsInstance<CopyPasteTriggerAssertion>(),
                    copyTriggers.filterIsInstance<CrossProjectEntityCopyTriggerDtoData>(),
                ) { expected, actual ->
                    actual.sourceType == expected.sourceType &&
                        actual.sourceDisplayName == expected.sourceDisplayName &&
                        actual.targetParentType == expected.targetParentType &&
                        actual.targetParentDisplayName == expected.targetParentDisplayName
                }

                // assert DuplicateEntityTriggerDtoData (if operation was 'DUPLICATE')
                compareTriggers(
                    expectedTriggers.filterIsInstance<DuplicateTriggerAssertion>(),
                    copyTriggers.filterIsInstance<DuplicateEntityTriggerDtoData>(),
                ) { expected, actual ->
                    actual.sourceType == expected.sourceType &&
                        actual.sourceDisplayName == expected.sourceDisplayName &&
                        actual.targetParentType == expected.targetParentType &&
                        actual.targetParentDisplayName == expected.targetParentDisplayName
                }
            }
    }

    private fun <E : TriggerAssertion, A : TriggerDtoData> compareTriggers(
        expectedTriggers: List<E>,
        actualTriggers: List<A>,
        predicate: (E, A) -> Boolean,
    ) {
        assertThat(actualTriggers.size)
            .withFailMessage {
                "Expected:${expectedTriggers.size} actual:${actualTriggers.size}" +
                    printComparison(expectedTriggers, actualTriggers)
            }.isEqualTo(expectedTriggers.size)

        val actualTriggersList = actualTriggers.toMutableList()

        val expectedUnmatched =
            expectedTriggers.mapNotNull { expected ->
                val match =
                    actualTriggersList.find { actual ->
                        predicate(expected, actual)
                    }
                if (match != null) {
                    actualTriggersList.remove(match)
                    null
                } else {
                    expected
                }
            }

        if (expectedUnmatched.isNotEmpty() || actualTriggersList.isNotEmpty()) {
            throw AssertionError("unmatched" + printComparison(expectedUnmatched, actualTriggersList))
        }
    }

    private fun printComparison(
        expectedList: List<TriggerAssertion>,
        actualList: List<TriggerDtoData>,
    ): String {
        val expectedBuilder =
            expectedList.fold(StringBuilder("\n----\nEXPECTED: ${expectedList.size}\n----\n")) { builder, expected ->
                builder.appendLine()
                when (expected) {
                    is CopyPasteTriggerAssertion -> {
                        builder.appendLine("sourceType=${expected.sourceType}")
                        builder.appendLine("sourceDisplayName=${expected.sourceDisplayName}")
                        builder.appendLine("targetParentDisplayName=${expected.targetParentDisplayName}")
                    }

                    is DuplicateTriggerAssertion -> {
                        builder.appendLine("sourceType=${expected.sourceType}")
                        builder.appendLine("sourceDisplayName=${expected.sourceDisplayName}")
                        builder.appendLine("targetParentDisplayName=${expected.targetParentDisplayName}")
                    }

                    else -> error("unexpected trigger type ${expected::class.java.name}")
                }
            }

        val actualBuilder =
            actualList.fold(StringBuilder("\n---\nACTUAL: ${actualList.size}\n---\n")) { builder, actual ->
                builder.appendLine()
                when (actual) {
                    is CrossProjectEntityCopyTriggerDtoData -> {
                        builder.appendLine("sourceType=${actual.sourceType}")
                        builder.appendLine("sourceDisplayName=${actual.sourceDisplayName}")
                        builder.appendLine("targetParentDisplayName=${actual.targetParentDisplayName}")
                    }

                    is DuplicateEntityTriggerDtoData -> {
                        builder.appendLine("sourceType=${actual.sourceType}")
                        builder.appendLine("sourceDisplayName=${actual.sourceDisplayName}")
                        builder.appendLine("targetParentDisplayName=${actual.targetParentDisplayName}")
                    }

                    else -> error("unexpected trigger type ${actual::class.java.name}")
                }
                builder.appendLine()
            }

        return expectedBuilder.append(actualBuilder).toString()
    }

    private fun extractCopyTriggers(trigger: TriggerDto): List<TriggerDtoData> =
        when (trigger.triggerType) {
            CompositeTriggerAction::class.simpleName -> {
                assertThat(trigger.data).isInstanceOf(CompositeTriggerDtoData::class.java)
                (trigger.data as CompositeTriggerDtoData).triggers.map { triggerElement ->
                    assertThat(triggerElement.triggerType).isIn(
                        CrossProjectEntityCopy::class.simpleName,
                        DuplicateEntity::class.simpleName,
                    )
                    triggerElement.data
                }
            }

            CrossProjectEntityCopy::class.simpleName -> {
                assertThat(trigger.data).isInstanceOf(CrossProjectEntityCopyTriggerDtoData::class.java)
                listOf(trigger.data as CrossProjectEntityCopyTriggerDtoData)
            }

            DuplicateEntity::class.simpleName -> {
                assertThat(trigger.data).isInstanceOf(DuplicateEntityTriggerDtoData::class.java)
                listOf(trigger.data as DuplicateEntityTriggerDtoData)
            }

            else -> error("unexpected trigger $trigger")
        }

    private fun copyBomTree(
        source: EntitySelector,
        target: ProjectSelector,
    ): Mono<CalculationResultWithSnapshot> = copyPasteService.copyBomTree(accessCheck, listOf(source), target).last()

    private fun addEth(): Mono<Void> {
        val ccy = CurrencyDto(SimpleKeyDto("ETH"), "Ethereum", "ETH")
        val headerType = SimpleKeyDto(MasterdataTsetConfigurationService.EXCHANGE_RATE_HEADER_TYPE_KEY)
        val ethRate = 1.0 / 4000
        return currencyService
            .createCurrency(accessCheck, ccy)
            .then {
                headerService.createHeader(
                    accessCheck,
                    HeaderDto(
                        ccy.key,
                        ccy.name,
                        headerType,
                        true,
                        NumericDetailValueSchemaDto(
                            NumericFieldSchemaDto(
                                UnitOfMeasurementTypeDto(
                                    CurrencyTypeDto(ccy.key),
                                    CurrencyTypeDto(SimpleKeyDto("EUR")),
                                ),
                            ),
                        ),
                    ),
                )
            }.then {
                val value =
                    NumericDetailValueDto(
                        ethRate,
                        CurrencyMeasurementDto(ccy.key),
                        CurrencyMeasurementDto(SimpleKeyDto("EUR")),
                    )
                detailCrudService.postDetailEntries(
                    accessCheck,
                    headerType,
                    listOf(DetailDto(emptyMap(), ccy.key, value, true)),
                )
            }.then()
    }
}

private fun createPartFields(configIdentifier: String): Map<String, FieldResult<out Any, FieldResult<out Any, *>>> =
    mapOf(
        "maxWallThickness" to Length(1.2, LengthUnits.CENTIMETER),
        "netWeightPerPart" to QuantityUnit(Weight(1.0, WeightUnits.KILOGRAM)),
        "shapeId" to Text("S_386"),
        "foaming" to FoamingType.CHEMICAL,
        "foamingDensity" to Density(5.0, DensityUnits.KILOGRAM_PER_CM),
        "injectionType" to InjectionType.NORMAL_INJECTION,
        "mainWallThickness" to Length(1.0, LengthUnits.CENTIMETER),
        "partHeight" to Length(50.0, LengthUnits.MILLIMETER),
        "partLength" to Length(50.0, LengthUnits.MILLIMETER),
        "partOuterDiameter" to Length(10.0, LengthUnits.MILLIMETER),
        "partQuality" to PartQuality.STANDARD,
        "partWidth" to Length(50.0, LengthUnits.MILLIMETER),
        "projectedAreaPerPart" to Area(1.5, AreaUnits.QDM),
        "costModuleConfigurationIdentifier" to
            ConfigIdentifier(
                ConfigurationIdentifier.tset(configIdentifier, SemanticVersion(2, 0)),
            ),
    )

private fun getConsumableEntity(targetRootSnapshot: BomNodeSnapshot) =
    targetRootSnapshot.manufacturing!!.findByEntityType(Entities.CONSUMABLE)
