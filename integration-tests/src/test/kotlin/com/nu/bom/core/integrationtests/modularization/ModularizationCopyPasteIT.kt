package com.nu.bom.core.integrationtests.modularization

import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.AreaUnits
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.DensityUnits
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.FoamingType
import com.nu.bom.core.manufacturing.fieldTypes.InjectionType
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.PartQuality
import com.nu.bom.core.manufacturing.fieldTypes.PlasticCoatingType
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYearsUnit
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.NET_WEIGHT_PER_PART_WIZARD
import com.nu.bom.core.mongoProjectId
import com.nu.bom.core.prediction.PredictionServiceMock
import com.nu.bom.core.service.CalculationEntityCopyService
import com.nu.bom.core.service.PartService
import com.nu.bom.core.service.dto.EntitySelector
import com.nu.bom.core.technologies.steps.plasticinj.ManufacturingStepPlasticInjection2
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.user.withAccessCheck
import com.nu.bom.core.utils.CalculationBuilder
import com.nu.bom.core.utils.CalculationBuilderService
import com.nu.bom.core.utils.CompositeCalculationBuilder
import com.nu.bom.core.utils.EntityBuilder
import com.nu.bom.core.utils.NbkClient
import com.nu.bomrads.dto.ProjectCreationDTO
import org.assertj.core.api.Assertions.assertThat
import org.bson.types.ObjectId
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import reactor.test.StepVerifier

val DEFAULT_COPY_RULES =
    CalculationEntityCopyService.CopyRules(
        operation = CalculationEntityCopyService.Operation.COPY_PASTE,
    )

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test", "lookup-init", "shape-init", "masterdata-init", "config-init")
@AutoConfigureWebTestClient
class ModularizationCopyPasteIT : NbkClient.With {
    @Autowired
    private lateinit var builderService: CalculationBuilderService

    @Autowired
    private lateinit var copyPasteService: CalculationEntityCopyService

    @Autowired
    override lateinit var nbkClient: NbkClient

    @Autowired
    private lateinit var partService: PartService

    @Autowired
    lateinit var predictionService: PredictionServiceMock

    private lateinit var accessCheck: AccessCheck
    private lateinit var project: ProjectCreationDTO
    private lateinit var partId: ObjectId

    @BeforeEach
    fun init() {
        predictionService
            .clear()
            .withPrediction("inj", "UtilizationRate", 18.0)

        val (ac, project) = nbkClient.setupWithProject(name = "Test Project", key = "RPWIT")

        this.accessCheck = ac
        this.project = project
        this.partId =
            partService
                .newPart(
                    designation = "WizardTestPart",
                    number = "1234",
                    accessCheck = ac,
                    projectId = project.mongoProjectId(),
                ).mapNotNull { it._id }
                .block()!!

        predictionService
            .withPrediction("inj", "ToolComplexity", "T3")
            .withPrediction("inj", "GateValveType", 1)
            .withPrediction("inj", "SprueRate", 0.18936210640608034)
            .withPrediction("inj", "InvestPerTool", 44358.6860612057)
            .withPrediction("inj", "UtilizationRate", 0.858262381567614)
            .withPrediction("inj", "UnloadingType", 1)
            .withPrediction("inj", "MaintenanceRate", 0.190027615)
    }

    @Test
    fun `copy step with cost module`() {
        val tree =
            CompositeCalculationBuilder
                .create(CompositeCalculationBuilder.TreeType.MANUAL)
                .withCustomizer {
                    it.withPart(partId)
                }.withDefaultProject(project)
                .withSub(
                    CompositeCalculationBuilder.create(CompositeCalculationBuilder.TreeType.MANUAL),
                ).build()

        val (rootSnap, rootEntity, _) =
            builderService
                .build(tree, accessCheck)
                .withAccessCheck(accessCheck)
                .block()!!

        val projectId = project.mongoProjectId()

        val req = createPlasticInjectionViaWizard()

        val bomNode = req.bomNode
        val stepAfterCreation =
            bomNode.manufacturing!!
                .children
                .find { it.type == Entities.PROCESSED_MATERIAL }!!
                .children
                .first()

        val source =
            EntitySelector(
                projectId,
                bomNodeId = BomNodeId(bomNode.id),
                branchId = BranchId(bomNode.branch.id),
                entityId = ObjectId(stepAfterCreation.id),
            )

        val step = rootEntity.findByEntityName("Step2")!!

        val target =
            EntitySelector(
                rootSnap.projectId!!,
                bomNodeId = rootSnap.bomNodeId!!,
                branchId = rootSnap.branchId(),
                entityId = step._id,
            )

        StepVerifier
            .create(
                copyPasteService.copyEntity(accessCheck, source, target, DEFAULT_COPY_RULES),
            ).assertNext { result ->

                val resultManufacturing = result.result.manufacturing
                val rootManufacturing = rootSnap.manufacturing

                assertThat(resultManufacturing).isNotNull()
                assertThat(result.newRootSnapshot.title).isEqualTo(rootSnap.title)
                assertThat(result.newRootSnapshot.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(rootSnap.getBaseManufacturing()?.partId)

                // before and after differ because we copied the manufacturing step into it
                assertThat(rootManufacturing!!.findByEntityName("ManufacturingStepInjection2")).isNull()
                assertThat(resultManufacturing!!.findByEntityName("ManufacturingStepInjection2")).isNotNull()

                // the parent manufacturing will have dynamicFields and the step will now be considered an isolated step
                assertThat(rootManufacturing.dynamicFields).isEmpty()
                assertThat(resultManufacturing.dynamicFields).isNotEmpty()
                assertThat(resultManufacturing.findByEntityName("ManufacturingStepInjection2")!!.isolated).isTrue()
            }.verifyComplete()
    }

    @Test
    fun `copy material with cost module`() {
        val partFields =
            mapOf(
                "maxWallThickness" to Length(1.2, LengthUnits.CENTIMETER),
                "netWeightPerPart" to QuantityUnit(Weight(1.0, WeightUnits.KILOGRAM)),
                "shapeId" to Text("S_386"),
                "foaming" to FoamingType.CHEMICAL,
                "foamingDensity" to Density(5.0, DensityUnits.KILOGRAM_PER_CM),
                "injectionType" to InjectionType.NORMAL_INJECTION,
                "mainWallThickness" to Length(1.0, LengthUnits.CENTIMETER),
                "partHeight" to Length(50.0, LengthUnits.MILLIMETER),
                "partLength" to Length(50.0, LengthUnits.MILLIMETER),
                "partOuterDiameter" to Length(10.0, LengthUnits.MILLIMETER),
                "partQuality" to PartQuality.STANDARD,
                "partWidth" to Length(50.0, LengthUnits.MILLIMETER),
                "projectedAreaPerPart" to Area(1.5, AreaUnits.QDM),
                "technologyModel" to Text("ManufacturingInjection2"),
            )

        val tree =
            CompositeCalculationBuilder
                .create(
                    CalculationBuilder
                        .create(ManualManufacturing::class)
                        .withTitle("MANUAL")
                        .withInput(partFields)
                        .withChild(
                            EntityBuilder
                                .create(ManufacturingStepPlasticInjection2::class)
                                .withName("ManufacturingStepPlasticInjection2"),
                        ),
                ).withDefaultProject(project)
                .build()

        val (rootSnapshot, rootEntity, accessCheck) = builderService.build(tree, accessCheck).block()!!

        val projectId = project.mongoProjectId()

        val req = createPlasticInjectionViaWizard()

        val step = rootEntity.findByEntityName("ManufacturingStepPlasticInjection2")!!

        val bomNode = req.bomNode
        val materialPlastic2 =
            bomNode.manufacturing!!
                .findEntity("InjectedMaterial")
                .first()
                .findEntity("ManufacturingStepInjection2")
                .first()
                .findEntity("MaterialPlastic2")
                .first()

        val source =
            EntitySelector(
                projectId,
                bomNodeId = BomNodeId(bomNode.id),
                branchId = BranchId(bomNode.branch.id),
                entityId = ObjectId(materialPlastic2.id),
            )

        val target =
            EntitySelector(
                rootSnapshot.projectId!!,
                bomNodeId = rootSnapshot.bomNodeId!!,
                branchId = rootSnapshot.branchId(),
                entityId = step._id,
            )

        StepVerifier
            .create(
                copyPasteService.copyEntity(accessCheck, source, target, DEFAULT_COPY_RULES),
            ).assertNext { result ->

                val resultManufacturing = result.result.manufacturing
                val rootManufacturing = rootSnapshot.manufacturing

                assertThat(resultManufacturing).isNotNull()
                assertThat(result.newRootSnapshot.title).isEqualTo(rootSnapshot.title)
                assertThat(result.newRootSnapshot.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(rootSnapshot.getBaseManufacturing()?.partId)

                // before and after differ because we copied the material into it
                assertThat(rootManufacturing!!.findByEntityName("ManufacturingStepPlasticInjection2")!!.children).isNotEmpty()
                assertThat(rootManufacturing.findByEntityName("MaterialPlastic2")).isNull()
                // we copied a material over, so there is a new child
                assertThat(resultManufacturing!!.findByEntityName("ManufacturingStepPlasticInjection2")!!.children.size).isEqualTo(
                    rootManufacturing.findByEntityName("ManufacturingStepPlasticInjection2")!!.children.size + 1,
                )

                assertThat(resultManufacturing.findByEntityName("MaterialPlastic2")).isNotNull()
                assertThat(resultManufacturing.findByEntityName("MaterialPlastic2")!!.isolated).isTrue()

                // the parent manufacturing will have dynamicFields and the material will now be considered an isolated step
                assertThat(rootManufacturing.dynamicFields).isEmpty()
                assertThat(resultManufacturing.dynamicFields).isNotEmpty()
            }.verifyComplete()
    }

    @Test
    fun `copy material and step with cost module`() {
        val tree =
            CompositeCalculationBuilder
                .create(CompositeCalculationBuilder.TreeType.MANUAL)
                .withCustomizer {
                    it.withPart(partId)
                }.withDefaultProject(project)
                .withSub(
                    CompositeCalculationBuilder.create(CompositeCalculationBuilder.TreeType.MANUAL),
                ).build()

        val (rootSnap, rootEntity, _) =
            builderService
                .build(tree, accessCheck)
                .withAccessCheck(accessCheck)
                .block()!!

        val projectId = project.mongoProjectId()

        val req = createPlasticInjectionViaWizard()

        val bomNode = req.bomNode
        val stepAfterCreation =
            bomNode.manufacturing!!
                .findEntity("InjectedMaterial")
                .first()
                .findEntity("ManufacturingStepInjection2")
                .first()

        val materialPlastic2 =
            bomNode.manufacturing!!
                .findEntity("InjectedMaterial")
                .first()
                .findEntity("ManufacturingStepInjection2")
                .first()
                .findEntity("MaterialPlastic2")
                .first()

        val sourceStep =
            EntitySelector(
                projectId,
                bomNodeId = BomNodeId(bomNode.id),
                branchId = BranchId(bomNode.branch.id),
                entityId = ObjectId(stepAfterCreation.id),
            )

        val sourceMaterial =
            EntitySelector(
                projectId,
                bomNodeId = BomNodeId(bomNode.id),
                branchId = BranchId(bomNode.branch.id),
                entityId = ObjectId(materialPlastic2.id),
            )

        val step = rootEntity.findByEntityName("Step2")!!

        val target =
            EntitySelector(
                rootSnap.projectId!!,
                bomNodeId = rootSnap.bomNodeId!!,
                branchId = rootSnap.branchId(),
                entityId = step._id,
            )

        StepVerifier
            .create(
                copyPasteService.copyEntity(
                    accessCheck,
                    listOf(sourceMaterial, sourceStep),
                    target,
                    DEFAULT_COPY_RULES,
                ),
            ).assertNext { result ->

                val resultManufacturing = result.result.manufacturing
                val rootSourceManufacturing = bomNode.manufacturing
                val rootDestinationManufacturing = rootSnap.manufacturing

                assertThat(resultManufacturing).isNotNull()
                assertThat(result.newRootSnapshot.title).isEqualTo(rootSnap.title)
                assertThat(result.newRootSnapshot.getBaseManufacturing()?.partId)
                    .isNotNull
                    .isEqualTo(rootSnap.getBaseManufacturing()?.partId)

                // before and after differ because we copied the manufacturing step into it
                assertThat(rootDestinationManufacturing!!.findByEntityName("ManufacturingStepInjection2")).isNull()
                assertThat(resultManufacturing!!.findByEntityName("ManufacturingStepInjection2")).isNotNull()

                val resultManufacturingStep = resultManufacturing.findByEntityName("ManufacturingStepInjection2")!!

                assertThat(resultManufacturing.findByEntityName("MaterialPlastic2")).isNotNull
                assertThat(resultManufacturing.findByEntityName("MaterialPlastic2")!!.isolated).isTrue()

                val linkedMaterial = resultManufacturingStep.getFieldResult("linkedMaterial")!!.res
                assertThat(linkedMaterial.toString()).isEqualTo(resultManufacturing.findByEntityName("MaterialPlastic2")!!._id.toString())

                assertThat(
                    rootSourceManufacturing!!
                        .findEntity("ManufacturingStepInjection2")
                        .first()
                        .findEntity("Injection mold"),
                ).isNotEmpty
                assertThat(
                    rootSourceManufacturing
                        .findEntity("ManufacturingStepInjection2")
                        .first()
                        .children
                        .map { it.type },
                ).contains(Entities.TOOL)
                assertThat(resultManufacturingStep.children.map { it.getEntityTypeAnnotation() }).doesNotContain(
                    Entities.TOOL,
                )

                assertThat(resultManufacturing.getField("technologyModel")!!.result.res).isEqualTo("ManufacturingInjection2")
                assertThat(resultManufacturingStep.getField("technologyModel")!!.result.res).isEqualTo("ManufacturingInjection2")
                assertThat(
                    resultManufacturing
                        .findByEntityName("MaterialPlastic2")!!
                        .getField("technologyModel")!!
                        .result.res,
                ).isEqualTo("ManufacturingInjection2")

                assertThat(resultManufacturing.getField("netWeightPerPart")?.result?.res).isNotNull

                // the parent manufacturing will have dynamicFields and the step will now be considered an isolated step
                assertThat(rootDestinationManufacturing.dynamicFields).isEmpty()
                assertThat(resultManufacturing.dynamicFields).isNotEmpty()
                assertThat(resultManufacturingStep.isolated).isTrue()
            }.verifyComplete()
    }

    private fun createPlasticInjectionViaWizard() =
        wizardBuilder
            .withStandardFields(partId.toHexString()) {
                location = Text("tset.ref.classification.germany")
                lifeTime = TimeInYears(7.toBigDecimal(), TimeInYearsUnit.YEAR)
                peakUsableProductionVolumePerYear = QuantityUnit(8558.toBigDecimal())
                averageUsableProductionVolumePerYear = QuantityUnit(70000.toBigDecimal())
                dimension = Dimension(Dimension.Selection.NUMBER)
            }.withTechStep("Plastic Injection")
            .withWamStep(
                mapOf(
                    NET_WEIGHT_PER_PART_WIZARD to Weight(1.20476, WeightUnits.KILOGRAM),
                    "materialName" to Text("PBT-RAW_MATERIAL_PLASTIC_GRANULATE"),
                ),
            ).withShapeStep("S_400")
            .withSpecStep(
                mapOf(
                    "projectedAreaPerPart" to Area(0.2133, AreaUnits.QM),
                    "partLength" to Length(1.01, LengthUnits.METER),
                    "partHeight" to Length(0.185, LengthUnits.METER),
                    "partWidth" to Length(0.314, LengthUnits.METER),
                    "mainWallThickness" to Length(0.0025, LengthUnits.METER),
                    "maxWallThickness" to Length(0.004, LengthUnits.METER),
                    "partQuality" to PartQuality.STANDARD,
                    "plasticCoatingType" to PlasticCoatingType.NO_TYPE,
                    "cleaningNeeded" to SelectableBoolean.FALSE,
                ),
            ) {}
            .execute()
            .getOrThrow()
}
