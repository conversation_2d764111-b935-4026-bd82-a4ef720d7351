package com.nu.bom.core.service

import com.nu.bom.core.api.dtos.CreateManufacturingResult
import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.config.LookupServiceMock
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostManufacturedMaterial
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostMaterialUsage
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetProcurementType
import com.nu.bom.core.manufacturing.entities.ElectronicComponent
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.UnitOverrideContext
import com.nu.bom.core.manufacturing.fieldTypes.BASE_CURRENCY
import com.nu.bom.core.manufacturing.fieldTypes.CustomProcurementType
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Part
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.Wizard
import com.nu.bom.core.model.WizardData
import com.nu.bom.core.model.createBranchId
import com.nu.bom.core.model.toMongoProjectId
import com.nu.bom.core.repository.WizardRepository
import com.nu.bom.core.service.bomimporter.BomImporterFinishData
import com.nu.bom.core.service.bomimporter.BomImporterRootFinishData
import com.nu.bom.core.service.bomimporter.BomImporterService
import com.nu.bom.core.service.bomimporter.EntityCreationBomImporterService
import com.nu.bom.core.service.bomimporter.ManufacturingCreationBomImporterService
import com.nu.bom.core.service.bomimporter.SUB_REQ_FIELDS
import com.nu.bom.core.service.bomimporter.enrichment.EnrichmentSourceType
import com.nu.bom.core.service.bomrads.BomradsBomNodeService
import com.nu.bom.core.service.nexar.NexarQueryService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.AccountTestUtil
import com.nu.bom.core.utils.CalculationBuilderService
import com.nu.bom.core.utils.CompositeCalculationBuilder
import com.nu.bom.core.utils.assertDefaultManualStructure
import com.nu.bom.core.utils.findInTree
import com.nu.bomrads.dto.ProjectCreationDTO
import com.nu.bomrads.dto.admin.ProjectDTO
import com.tset.bom.clients.bomimporter.DataFieldValueDTO
import com.tset.bom.clients.bomimporter.DataRowDto
import com.tset.bom.clients.bomimporter.ImportState
import com.tset.bom.clients.bomimporter.ImportedDataDto
import com.tset.bom.clients.bomimporter.QuantityType
import com.tset.core.api.calculation.dto.CalculationCreationModalMode
import com.tset.core.api.calculation.dto.CalculationPosition
import com.tset.core.api.calculation.dto.CalculationUpdateContextDto
import com.tset.core.api.calculation.dto.CalculationUpdateData
import com.tset.core.api.calculation.dto.CalculationUpdateInputDto
import com.tset.core.api.calculation.dto.CalculationUpdatePayloadDto
import com.tset.core.service.domain.Currency
import com.tset.core.service.domain.calculation.CalculationType
import org.assertj.core.api.Assertions.assertThat
import org.bson.types.ObjectId
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.any
import org.mockito.kotlin.argThat
import org.mockito.kotlin.eq
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.boot.test.mock.mockito.SpyBean
import org.springframework.test.context.ActiveProfiles
import reactor.core.publisher.Mono
import java.math.BigDecimal

@SpringBootTest
@ActiveProfiles("test")
class BomImporterIT {
    @SpyBean
    private lateinit var bomradsBomNodeService: BomradsBomNodeService

    @MockBean
    private lateinit var bomImporterClientMock: BomImporterService

    @MockBean
    private lateinit var nexarQueryService: NexarQueryService

    @Autowired
    private lateinit var bomImporterService: EntityCreationBomImporterService

    @Autowired
    private lateinit var masterDataService: MasterDataService

    @Autowired
    private lateinit var builderService: CalculationBuilderService

    @Autowired
    private lateinit var bomNodeService: BomNodeService

    @Autowired
    private lateinit var wizardRepository: WizardRepository

    @Autowired
    private lateinit var manufacturingCreationBomImporterService: ManufacturingCreationBomImporterService

    @Autowired
    private lateinit var accountUtil: AccountTestUtil

    private lateinit var lookupService: LookupServiceMock

    private lateinit var accessCheck: AccessCheck

    private lateinit var project: ProjectDTO
    private lateinit var projectCreationDTO: ProjectCreationDTO

    private lateinit var wizardId: ObjectId

    @BeforeEach
    fun setupMocks() {
        val (ac, p) = accountUtil.setupWithProject(name = "projectName", key = "projectKey")

        accessCheck = ac
        project = p.project!!
        projectCreationDTO = p

        lookupService = LookupServiceMock()

        whenever(bomImporterClientMock.finishImport(eq(accessCheck), eq("someImportId"))).thenReturn(Mono.empty())
    }

    @Test
    fun testFinishImportBasic() {
        // setup calculation - manual calc
        val tree =
            CompositeCalculationBuilder
                .create(CompositeCalculationBuilder.TreeType.MANUAL)
                .withDefaultProject(projectCreationDTO)
                .build()

        val (rootSnapshot, rootEntity, accessCheck) = builderService.build(tree, accessCheck).block()!!

        assertDefaultManualStructure(rootEntity)

        val step2 = rootEntity.findByEntityName("Step2")!!

        // setup octopart
        val octopart =
            Part(
                id = "*********",
                slug = "",
                short_description = "Elco O",
                sellers = emptyList(),
                descriptions = emptyList(),
                mpn = "789",
                manufacturer = "",
                categoryId = null,
                medianPrice = null,
            )
        whenever(nexarQueryService.getPartsByMpn(listOf("123", "456", "789")))
            .thenReturn(Mono.just(mapOf("789" to listOf(octopart))))

        whenever(nexarQueryService.getPartById(octopart.id))
            .thenReturn(Mono.just(octopart))

        whenever(nexarQueryService.getPreferredVendorPrice(octopart))
            .thenReturn(Money(333.0))

        // setup import
        val importId = ObjectId.get()
        val importData =
            ImportedDataDto(
                state = ImportState.FINISHED,
                items =
                    listOf(
                        DataRowDto(
                            values =
                                listOf(
                                    DataFieldValueDTO("displayDesignation", "manual elco"),
                                    DataFieldValueDTO("quantity", "2"),
                                    DataFieldValueDTO("mpn", "123"),
                                    DataFieldValueDTO("pricePerUnit", "5.0"),
                                ),
                        ),
                        DataRowDto(
                            values =
                                listOf(
                                    DataFieldValueDTO("displayDesignation", "octopart elco", "Elco O"),
                                    DataFieldValueDTO("quantity", "4"),
                                    DataFieldValueDTO("mpn", "789", "789"),
                                    DataFieldValueDTO("pricePerUnit", "20.0", "333.0"),
                                ),
                            sourceType = EnrichmentSourceType.NEXAR.name,
                            sourceKey = octopart.id,
                        ),
                    ),
            )

        whenever(bomImporterClientMock.getImport(accessCheck, importId.toHexString()))
            .thenReturn(Mono.just(importData).cache())

        whenever(bomImporterClientMock.finishImport(accessCheck, importId.toHexString()))
            .thenReturn(Mono.empty())

        // execute
        val updatedNode =
            bomImporterService.finishImport(
                accessCheck,
                importId.toHexString(),
                data =
                    BomImporterFinishData(
                        projectId = rootSnapshot.projectId(),
                        bomNodeId = rootSnapshot.bomNodeId(),
                        branchId = rootSnapshot.branchId(),
                        parentId = step2._id,
                        entityType = Entities.C_PART,
                        entityClass = ElectronicComponent::class,
                        replace = true,
                    ),
            ).flatMap { bomNodeDto ->
                bomNodeService.getBomNode(
                    accessCheck,
                    BomNodeId(bomNodeDto.id),
                    createBranchId(bomNodeDto.branch.id),
                )
            }.block()!!

        // assert created elcos
        val elcos =
            updatedNode.manufacturing!!.visitChildren { child, _ ->
                child.takeIf { child.getEntityClass() == ElectronicComponent::class.simpleName!! }
            }

        assertThat(elcos.size).isEqualTo(2)

        // assert manual elco fields are correct
        val elcoManual = elcos.find { it.displayName == "manual elco" }?.getFieldResultMap()

        assertThat(elcoManual).isNotNull

        assertThat(elcoManual!!["quantity"]).isEqualTo(QuantityUnit(2.0))
        assertThat(elcoManual["mpn"]).isEqualTo(Text("123"))
        assertThat(elcoManual["pricePerUnit"]).isEqualTo(Money(5.0)) // mo discount for manual elcos

        assertThat(elcoManual["costPerPart"]).satisfies({ result ->
            assertThat(result).isNotNull
            assertThat(result!!.res).isNotNull
            assertThat(result.res).isInstanceOf(BigDecimal::class.java)
        })

        // assert Nexar elco fallback correctness with default discount
        val elcoOctopart = elcos.find { it.displayName == "octopart elco" }?.getFieldResultMap()

        assertThat(elcoOctopart).isNotNull

        assertThat(elcoOctopart!!["quantity"]).isEqualTo(QuantityUnit(4.0))
        assertThat(elcoOctopart["mpn"]).isEqualTo(Text("789"))
        assertThat(elcoOctopart["pricePerUnit"]).isEqualTo(Money(20.0))
        assertThat(elcoOctopart["externalPartId"]).isEqualTo(Text(octopart.id))

        assertThat(elcoOctopart["costPerPart"]).satisfies({ result ->
            assertThat(result).isNotNull
            assertThat(result!!.res).isNotNull
            assertThat(result.res).isInstanceOf(BigDecimal::class.java)
        })

        assertThat(elcoOctopart["displayDesignation"]?.systemValue).isEqualTo(Text("Elco O").res)
        assertThat(
            (elcoOctopart["pricePerUnit"]?.systemValue as BigDecimal).compareTo(Money("99.9").res) == 0,
        ) // default discount applied

        // assert calculation
        assertThat(updatedNode.manufacturing!!.getFieldResult("costPerPart")).satisfies({ result ->
            assertThat(result).isNotNull
            assertThat(result!!.res).isNotNull
            assertThat(result.res).isInstanceOf(BigDecimal::class.java)
        })
    }

    @ParameterizedTest
    @MethodSource("fieldsWizard")
    fun `should import a list of root manufacturing and only call bomrads once`(fieldsWizard: List<FieldParameter>) {
        // given
        setupMocksAndData(fieldsWizard)
        // and
        val importId = "someImportId"
        willReturnImportData(
            bomImporterClientMock,
            importId = importId,
            // data is returned in reverse order, because it is sorted by creation date in descending order
            importedData = (1..3).map { dataRowDto("title-$it", "import-$it") }.reversed(),
        )

        // when
        val result = manufacturingCreationBomImporterService.finishRootImport(importId)

        // then
        createNodesWasCalled(
            numberOfTimes = 1,
            nodeNames = listOf("title-1", "title-2", "title-3"),
        )
        // and
        Assertions.assertTrue(
            result!!.all { it.bomNode.kpi.costPerPart!!.newValue!!.getEurValue()!!.compareTo(BigDecimal.ZERO) == 0 },
        )
        Assertions.assertTrue(
            allInitialValuesAreCorrect(result, fieldsWizard),
        )
    }

    @ParameterizedTest
    @MethodSource("fieldsWizard")
    fun `should import a root manufacturing and use default values`(fieldsWizard: List<FieldParameter>) {
        // given
        setupMocksAndData(fieldsWizard)
        // and
        val importId = "someImportId"
        val overwrites =
            listOf(
                DataFieldValueDTO(BASE_CURRENCY, "AUD"),
                DataFieldValueDTO("location", "tset.ref.classification.spain"),
            )
        willReturnImportData(
            bomImporterClientMock,
            importId = importId,
            importedData =
                listOf(
                    dataRowDto(
                        "title",
                        "import",
                        overwrites,
                    ),
                ),
        )

        // when
        val result = manufacturingCreationBomImporterService.finishRootImport(importId)

        // then
        createNodesWasCalled(
            numberOfTimes = 1,
            nodeNames = listOf("title"),
        )
        // and
        Assertions.assertTrue(
            allInitialValuesAreCorrect(
                result!!,
                getInitialWithOverwrites(fieldsWizard, overwrites),
            ),
        )
    }

    @ParameterizedTest
    @MethodSource("fieldsWizard")
    fun `import a root manufacturing and overwrite dimension`(fieldsWizard: List<FieldParameter>) {
        // given
        setupMocksAndData(fieldsWizard)
        // and
        val importId = "someImportId"
        willReturnImportData(
            bomImporterClientMock,
            importId = importId,
            importedData =
                listOf(
                    dataRowDto(
                        "title",
                        "import",
                        listOf(DataFieldValueDTO("dimension", Dimension.Selection.VOLUME.name)),
                    ),
                ),
        )

        // when
        val result = manufacturingCreationBomImporterService.finishRootImport(importId)

        // then
        createNodesWasCalled(
            numberOfTimes = 1,
            nodeNames = listOf("title"),
        )
        // and
        val resultsFields =
            result?.map {
                it.bomNode.manufacturing!!.fields.filter { fieldParameter ->
                    fieldParameter.name == "dimension"
                }
            }?.flatten()

        assertNotNull(resultsFields)
        assertThat(resultsFields?.size).isEqualTo(1)

        resultsFields?.all { field ->
            field.value == Dimension.Selection.VOLUME
        }
    }

    @ParameterizedTest
    @MethodSource("fieldsWizard")
    fun `import a root-sub manufacturing tree sub doesn't inherit dimension`(fieldsWizard: List<FieldParameter>) {
        // given
        setupMocksAndData(fieldsWizard)
        // and
        val importId = "someImportId"
        val overwrites = listOf(DataFieldValueDTO("dimension", Dimension.Selection.VOLUME.name))
        willReturnImportData(
            bomImporterClientMock,
            importId = importId,
            importedData =
                listOf(
                    dataRowDto(
                        "title",
                        "import",
                        overwrites,
                    ),
                    dataRowDto(
                        "title",
                        "import",
                        emptyList(),
                        1,
                    ),
                ),
        )

        // when
        val result = manufacturingCreationBomImporterService.finishRootImport(importId)

        // then
        createNodesWasCalled(
            numberOfTimes = 1,
            nodeNames = listOf("title"),
        )
        // and
        verifyRootAndSub(result, "dimension", Dimension.Selection.VOLUME.name, Dimension.Selection.NUMBER.name)
    }

    private fun verifyRootAndSub(
        result: List<CreateManufacturingResult>?,
        fieldName: String,
        rootValue: String,
        subValue: String,
    ) {
        assertNotNull(result)
        assertThat(result!!.size).isEqualTo(1)
        val field = result[0].bomNode.manufacturing!!.getField(fieldName)
        assertThat(field.value).isEqualTo(rootValue)

        val childEntity =
            result[0].bomNode.manufacturing.findInTree({ it?.type == Entities.BOM_ENTRY }) {
                it?.children ?: emptyList()
            }
        val childField = childEntity!!.getField(fieldName)
        assertThat(childField.value).isEqualTo(subValue)
    }

    @ParameterizedTest
    @MethodSource("fieldsWizard")
    fun `import a root and sub calcs and take procurement type from import`(fieldsWizard: List<FieldParameter>) {
        // given
        setupMocksAndData(fieldsWizard)
        // and
        val importId = "someImportId"
        willReturnImportData(
            bomImporterClientMock,
            importId = importId,
            importedData =
                listOf(
                    dataRowDto(
                        "title",
                        "import",
                        listOf(
                            DataFieldValueDTO(
                                CommercialCalculationCostManufacturedMaterial::customProcurementType.name,
                                CustomProcurementType.fromCustomProcurementTypeWrapper(
                                    TsetProcurementType.PURCHASE.customProcurementType,
                                ).res,
                            ),
                        ),
                    ),
                    dataRowDto(
                        "title",
                        "import",
                        listOf(
                            DataFieldValueDTO(
                                CommercialCalculationCostManufacturedMaterial::customProcurementType.name,
                                CustomProcurementType.fromCustomProcurementTypeWrapper(
                                    TsetProcurementType.INHOUSE.customProcurementType,
                                ).res,
                            ),
                        ),
                        1,
                    ),
                ),
        )

        // when
        val result = manufacturingCreationBomImporterService.finishRootImport(importId)

        // then
        createNodesWasCalled(
            numberOfTimes = 1,
            nodeNames = listOf("title"),
        )
        // and
        verifyRootAndSub(
            result,
            CommercialCalculationCostManufacturedMaterial::customProcurementType.name,
            TsetProcurementType.PURCHASE.customProcurementType.value,
            TsetProcurementType.INHOUSE.customProcurementType.value,
        )
    }

    private fun getInitialWithOverwrites(
        fieldsWizard: List<FieldParameter>,
        overwrites: List<DataFieldValueDTO>,
    ) = fieldsWizard.map { field ->
        when (field.name) {
            BASE_CURRENCY ->
                FieldParameter(
                    name = "baseCurrency",
                    type = "Currency",
                    value = overwrites.first { it.columnId == field.name }.value ?: field.value,
                    source = "I",
                    denominatorUnit = null,
                )

            "location" ->
                FieldParameter(
                    name = "location",
                    type = "Text",
                    value = overwrites.first { it.columnId == field.name }.value ?: field.value,
                    source = "I",
                    denominatorUnit = null,
                )

            else -> field
        }
    }

    private fun allInitialValuesAreCorrect(
        result: List<CreateManufacturingResult>,
        fieldsWizard: List<FieldParameter>,
    ): Boolean {
        val resultsFields =
            result.map {
                it.bomNode.manufacturing!!.fields.filter { fieldParameter ->
                    (SUB_REQ_FIELDS + BASE_CURRENCY - "calculationTitle").contains(
                        fieldParameter.name,
                    )
                }
            }.flatten()

        return resultsFields.all { field ->
            when (val value = field.value) {
                is BigDecimal -> value.compareTo(BigDecimal(fieldsWizard.find { it.name == field.name }?.value as Int)) == 0
                else -> value == fieldsWizard.find { it.name == field.name }?.value
            }
        }
    }

    private fun dataRowDto(
        calculationTitle: String,
        partDesignation: String,
        extraFields: List<DataFieldValueDTO> = emptyList(),
        level: Int = 0,
    ) = DataRowDto(
        listOf(
            DataFieldValueDTO("calculationTitle", calculationTitle),
            DataFieldValueDTO("partDesignation", partDesignation),
        ) + extraFields,
        level = level,
    )

    private fun willReturnImportData(
        importService: BomImporterService,
        importId: String,
        importedData: List<DataRowDto>,
    ) = whenever(importService.getImport(eq(accessCheck), eq(importId))).thenReturn(
        Mono.just(
            ImportedDataDto(
                state = ImportState.FINISHED,
                quantityType = QuantityType.TOTAL,
                items = importedData,
            ),
        ),
    )

    private fun ManufacturingCreationBomImporterService.finishRootImport(importId: String) =
        finishRootImport(
            accessCheck = accessCheck,
            importId = importId,
            data =
                BomImporterRootFinishData(
                    projectId = project.id.toMongoProjectId(),
                    entityType = Entities.MANUFACTURING,
                    entityClass = ManualManufacturing::class,
                    wizardId = wizardId,
                ),
        ).block()

    private fun createNodesWasCalled(
        numberOfTimes: Int,
        nodeNames: List<String>,
    ) {
        verify(bomradsBomNodeService, times(numberOfTimes)).createNodes(
            accessCheck = eq(accessCheck),
            projectId = any(),
            multiNodeChanges =
                argThat { multiNodeChanges ->
                    multiNodeChanges.nodeTrees.size == nodeNames.size
                },
        )
    }

    private fun setupMocksAndData(fieldParameters: List<FieldParameter>) {
        wizardId = testWizard(fieldParameters)._id!!
    }

    private fun testWizard(fields: List<FieldParameter>): Wizard {
        val calculationType = CalculationType.EXCEL_FILE_IMPORT
        return wizardRepository.save(
            Wizard(
                projectId = project.id.toMongoProjectId(),
                standardCalculationData =
                    WizardData(
                        calculationType = calculationType,
                        partId = null,
                        partName = null,
                        calculationTitle = "Main",
                        fields = fields,
                        bomEntry = null,
                        calculationUpdateData =
                            CalculationUpdatePayloadDto(
                                data = CalculationUpdateData(fields = fields, parentBomData = null),
                                input =
                                    CalculationUpdateInputDto(
                                        mode = CalculationCreationModalMode.CALCULATION_MODE_NEW,
                                        position = CalculationPosition.ROOT,
                                        originalType = calculationType,
                                        context =
                                            CalculationUpdateContextDto(
                                                bomNodeId = null,
                                                branchId = null,
                                                stepId = null,
                                            ),
                                        currency = Currency.EUR,
                                    ),
                                selectedType = calculationType,
                            ),
                        unitOverrideContext = UnitOverrideContext.mockWithDefaults,
                    ),
                accountId = accessCheck.asAccountId(),
            ),
        ).block()!!
    }

    @AfterEach
    fun cleanUp() {
        accountUtil.cleanup()
    }

    companion object {
        @JvmStatic
        fun fieldsWizard() =
            listOf(
                listOf(
                    FieldParameter(
                        name = "dimension",
                        type = "Dimension",
                        value = Dimension.Selection.NUMBER.name,
                        source = "I",
                        denominatorUnit = null,
                    ),
                    FieldParameter(
                        name = "location",
                        type = "Text",
                        value = "tset.ref.classification.austria",
                        source = "I",
                        denominatorUnit = null,
                    ),
                    FieldParameter(
                        name = "lifeTime",
                        type = "TimeInYears",
                        value = 1,
                        source = "I",
                        denominatorUnit = null,
                        unit = "YEAR",
                    ),
                    FieldParameter(
                        name = "baseCurrency",
                        type = "Currency",
                        value = "EUR",
                        source = "I",
                        denominatorUnit = null,
                    ),
                    FieldParameter(
                        name = CommercialCalculationCostMaterialUsage::customProcurementType.name,
                        type = CustomProcurementType::class.simpleName!!,
                        value = TsetProcurementType.PURCHASE.customProcurementType.value,
                        denominatorUnit = null,
                        source = "I",
                    ),
                    FieldParameter(
                        name = "peakUsableProductionVolumePerYear",
                        type = "Pieces",
                        value = 1,
                        source = "I",
                        denominatorUnit = null,
                    ),
                    FieldParameter(
                        name = "averageUsableProductionVolumePerYear",
                        type = "Pieces",
                        value = 1,
                        source = "I",
                        denominatorUnit = null,
                    ),
                ),
            )
    }
}
