package com.nu.bom.core.integrationtests

import com.nu.bom.core.publicapi.dtos.InputRange
import com.nu.bom.core.publicapi.dtos.MetaInfoMaterials
import com.nu.bom.core.publicapi.dtos.MetaInfoShapes
import com.nu.bom.core.publicapi.dtos.MetaInfoTechnologies
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.AccountTestUtil
import com.nu.bom.core.utils.AccountTestUtil.Companion.authHeader
import org.assertj.core.api.AssertionsForInterfaceTypes.assertThat
import org.assertj.core.api.SoftAssertions
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.reactive.server.WebTestClient

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test", "lookup-init", "shape-init", "masterdata-init")
class PublicApiMetaInfoControllerIntegrationTest {
    companion object {
        @JvmStatic
        private fun getShapeInfoParameters(): List<Arguments> =
            listOf(
                Arguments.of("cube", 115, InputRange.LowerBounded::class.java),
                Arguments.of("rrol", 6, InputRange.Bounded::class.java),
                Arguments.of("pcb", 0, InputRange.Bounded::class.java),
            )
    }

    @Autowired
    lateinit var client: WebTestClient

    @Autowired
    private lateinit var accountUtil: AccountTestUtil

    var accessCheck: AccessCheck? = null

    @BeforeEach
    fun setup() {
        val (accCheck, jwt) = accountUtil.setupWithJwt()
        accessCheck = accCheck
    }

    @AfterEach
    fun teardown() {
        accountUtil.cleanup()
    }

    @Test
    fun getAllTechnologies() {
        client
            .get()
            .uri("/v1/metainfo/technologies")
            .authHeader(accessCheck!!.token)
            .exchange()
            .expectStatus()
            .isOk()
            .expectBody(MetaInfoTechnologies::class.java)
            .value {
                val expectedNumberTechnologies = 28
                val softAssertions = SoftAssertions()
                softAssertions
                    .assertThat(it.res.size)
                    .isEqualTo(expectedNumberTechnologies)
                softAssertions
                    .assertThat(it.res.map { it.identifier })
                    .`as`("List of technologies should be distinct")
                    .doesNotHaveDuplicates()
                softAssertions
                    .assertThat(it.res.map { it.humanReadableName }.sorted())
                    .`as`("List of technologies is expected to be sorted.")
                    .isSorted
                softAssertions.assertAll()
            }
    }

    @ParameterizedTest
    @MethodSource("getShapeInfoParameters")
    fun getShapeInfo(
        technology: String,
        expectedNumberOfShapes: Int,
        rangeType: Class<InputRange>,
    ) {
        client
            .get()
            .uri("/v1/metainfo/{technology}/shapes", technology)
            .authHeader(accessCheck!!.token)
            .exchange()
            .expectStatus()
            .isOk
            .expectBody(MetaInfoShapes::class.java)
            .value { shapes ->
                val softAssertions = SoftAssertions()
                softAssertions.assertThat(shapes.technology.identifier).isEqualTo(technology)
                softAssertions.assertThat(shapes.res.size).isEqualTo(expectedNumberOfShapes)
                shapes.res.forEach {
                    softAssertions.assertThat(it.volumeRangeInCubicMetre).isInstanceOf(rangeType)
                    if (it.volumeRangeInCubicMetre is InputRange.Bounded) {
                        val bounded = it.volumeRangeInCubicMetre as InputRange.Bounded
                        softAssertions
                            .assertThat(bounded.min)
                            .`as`("Min volume should be less or equal to max volume")
                            .isLessThanOrEqualTo(bounded.max)
                    }
                }
                softAssertions
                    .assertThat(shapes.res)
                    .`as`("Response should not contain duplicate shapes")
                    .doesNotHaveDuplicates()
                softAssertions.assertAll()
            }
    }

    @Test
    fun getMaterialInfo() {
        val technology = "rrol"
        val expectedNumMaterials = 24
        client
            .get()
            .uri("/v1/metainfo/{technology}/materials", technology)
            .authHeader(accessCheck!!.token)
            .exchange()
            .expectStatus()
            .isOk
            .expectBody(MetaInfoMaterials::class.java)
            .value {
                assertThat(it.groups.size).isEqualTo(1)
                val group = it.groups[0]
                val softAssertions = SoftAssertions()
                softAssertions.assertThat(it.technology.identifier).isEqualTo(technology)
                softAssertions.assertThat(group.materials.size).isEqualTo(expectedNumMaterials)
                softAssertions.assertThat(group.materials.map { material -> material.identifier }).doesNotHaveDuplicates()
                group.materials.forEach { material ->
                    softAssertions.assertThat(material.densityInKilogramPerCubicMetre).isPositive
                }
                softAssertions.assertAll()
            }
    }

    @Test
    fun `COST-49743 - getMaterialInfo for SAND does not contain RawMaterialSand`() {
        val technology = "sand"
        client
            .get()
            .uri("/v1/metainfo/{technology}/materials", technology)
            .authHeader(accessCheck!!.token)
            .exchange()
            .expectStatus()
            .isOk
            .expectBody(MetaInfoMaterials::class.java)
            .value {
                assertThat(it.groups).hasSize(1)
                val group = it.groups[0]
                val softAssertions = SoftAssertions()
                softAssertions.assertThat(it.technology.identifier).isEqualTo(technology)
                softAssertions
                    .assertThat(group.materials.map { material -> material.identifier })
                    .doesNotHaveDuplicates()
                    .contains("AlMg3")
                    .doesNotContain("Furan resin binder")
                softAssertions.assertAll()
            }
    }
}
