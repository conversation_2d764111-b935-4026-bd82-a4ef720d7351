package com.nu.bom.core.manufacturing.utils

import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetProcurementType
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.CalculationQualityConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.ConfigIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.Currency
import com.nu.bom.core.manufacturing.fieldTypes.CustomProcurementType
import com.nu.bom.core.manufacturing.fieldTypes.Date
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.NumericFieldResultWithUnit
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYearsUnit
import com.nu.bom.core.model.configurations.SemanticVersion
import com.nu.bom.core.service.configurations.CalculationQualityTsetConfigurationService
import kotlin.reflect.full.memberProperties

data class RequiredFields(
    var calculationTitle: Text,
    var location: Text,
    var baseCurrency: Currency,
    var lifeTime: TimeInYears,
    var peakUsableProductionVolumePerYear: QuantityUnit,
    var averageUsableProductionVolumePerYear: QuantityUnit,
    var customProcurementType: CustomProcurementType,
    var dimension: Dimension,
    var costUnit: Text,
    var quantityUnit: Text,
    var calculationQualityConfigurationKey: CalculationQualityConfigurationKey,
    var calculationDate: Date,
    var costModuleConfigurationIdentifier: ConfigIdentifier,
) {
    companion object {
        fun default() =
            Dimension.getDefault(Entities.MANUFACTURING)!!.let { dim ->
                RequiredFields(
                    calculationTitle = Text("Main"),
                    location = Text("tset.ref.classification.austria"),
                    baseCurrency = Currency("EUR"),
                    lifeTime = TimeInYears(10.toBigDecimal(), TimeInYearsUnit.YEAR),
                    peakUsableProductionVolumePerYear = QuantityUnit(100.toBigDecimal()),
                    averageUsableProductionVolumePerYear = QuantityUnit(100.toBigDecimal()),
                    customProcurementType =
                        CustomProcurementType
                            .fromCustomProcurementTypeWrapper(TsetProcurementType.PURCHASE.customProcurementType),
                    dimension = dim,
                    costUnit = Text(dim.res.getDefaultCostUnit()),
                    quantityUnit = Text(dim.res.getDefaultQuantityUnit()),
                    calculationQualityConfigurationKey =
                        CalculationQualityConfigurationKey(
                            ConfigurationIdentifier.tset(
                                CalculationQualityTsetConfigurationService.DEFAULT_CALC_QUALITY_KEY,
                                SemanticVersion.initialVersion(),
                            ),
                        ),
                    calculationDate = Date.of(2024, 1, 1),
                    costModuleConfigurationIdentifier = ConfigIdentifier(ConfigurationIdentifier.empty()),
                )
            }
    }

    fun toMap() = RequiredFields::class.memberProperties.associate { prop -> prop.name to prop.get(this)!! }

    fun toFieldParameters() =
        RequiredFields::class.memberProperties.map {
            it.name to it.get(this)!!
        }.map { (name, field) ->
            FieldParameter(
                name = name,
                type = field.javaClass.simpleName,
                value = (field as FieldResultStar).res,
                unit = (field as? NumericFieldResultWithUnit<*, *>)?.unitName,
                denominatorUnit = null,
            )
        }
}
