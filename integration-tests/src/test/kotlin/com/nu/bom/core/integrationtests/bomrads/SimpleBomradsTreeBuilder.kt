package com.nu.bom.core.integrationtests.bomrads

import com.nu.bom.core.api.CalculationEntityCopyController
import com.nu.bom.core.api.dtos.BomExpBaseDto
import com.nu.bom.core.api.dtos.BomExpNodeQueryDto
import com.nu.bom.core.api.dtos.BomExpRequestDto
import com.nu.bom.core.exception.ErrorCodedException
import com.nu.bom.core.exception.userException.BomNodeNotFoundException
import com.nu.bom.core.integrationtests.bomrads.EntityCreationHelper.subnodeBomEntry
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.MergeSourceType
import com.nu.bom.core.model.toBomEntryId
import com.nu.bom.core.model.toMongoBomNodeId
import com.nu.bom.core.model.toMongoID
import com.nu.bom.core.publicapi.dtos.BomNodeCreationResponse
import com.nu.bom.core.publicapi.dtos.FieldParameter
import com.nu.bom.core.publicapi.dtos.ObjectUpdate
import com.nu.bom.core.publicapi.dtos.PubApiManufacturing
import com.nu.bom.core.publicapi.service.PublicAPICalculationServiceImpl
import com.nu.bom.core.service.BomExplorerServiceV2
import com.nu.bom.core.service.BomNodeService
import com.nu.bom.core.service.BomPublishService
import com.nu.bom.core.service.BomPublishService.PublishResult
import com.nu.bom.core.service.BomTreeService
import com.nu.bom.core.service.CalculationNoReCalcCopyService
import com.nu.bom.core.service.ManufacturingCalculationService
import com.nu.bom.core.service.bomnode.SaveToSourceBranchService
import com.nu.bom.core.service.dto.DirtyStateHandling
import com.nu.bom.core.user.AccessCheck
import org.assertj.core.api.Assertions.assertThat
import org.bson.types.ObjectId
import org.junit.jupiter.api.assertThrows
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import java.math.BigDecimal

@Service
class SimpleBomradsTreeBuilder(
    private val calculationCreationService: PublicAPICalculationServiceImpl,
    private val manufacturingCalculationService: ManufacturingCalculationService,
    private val copyCalculationService: CalculationNoReCalcCopyService,
    private val bomExplorerServiceV2: BomExplorerServiceV2,
    private val bomNodeService: BomNodeService,
    private val bomTreeService: BomTreeService,
    private val bomPublishService: BomPublishService,
    private var saveToSourceBranchService: SaveToSourceBranchService,
) {
    private lateinit var accessCheck: AccessCheck
    private lateinit var projectKey: String

    fun buildTree(
        accessCheck: AccessCheck,
        projectKey: String,
        rootNode: CreateNodeData,
    ): BomradsTree {
        this.accessCheck = accessCheck
        this.projectKey = projectKey
        return BomradsTree(
            rootNode,
            Context(
                accessCheck,
                projectKey,
                calculationCreationService,
                manufacturingCalculationService,
                copyCalculationService,
                bomExplorerServiceV2,
                bomNodeService,
                bomTreeService,
                bomPublishService,
                saveToSourceBranchService,
            ),
        )
    }
}

/**
 * Class that hold data necessary to create a bomNode.
 * You can add part name or number there if you want.
 */
class CreateNodeData(
    val name: String,
    vararg val children: CreateNodeData,
)

/**
 * Provides methods to modify/change node.
 */
class Node(
    private val bomNodeId: String,
    private val manufacturingEntityId: String,
    private val context: Context,
) {
    fun getCostPerPart(): BigDecimal = getField("costPerPart") as BigDecimal

    fun getManufacturing(): Mono<PubApiManufacturing> =
        context.calculationCreationService.getCalc(context.accessCheck, context.projectKey, bomNodeId, null, true)

    fun addMaterialToManufacturingAndDontSave(netWeight: Int): Mono<BomNodeCreationResponse> =
        addMaterial(bomNodeId, manufacturingEntityId, netWeight, false)

    fun addMaterialToManufacturingAndSave(netWeight: Int): Mono<BomNodeCreationResponse> =
        addMaterial(bomNodeId, manufacturingEntityId, netWeight, true)

    fun addSubNodeToManufacturingAndSave(partName: String): Mono<BomNodeCreationResponse> =
        addSubNode(bomNodeId, manufacturingEntityId, partName, true)

    fun assertConflictOnSave(branchId: String) {
        val exception =
            assertThrows<ErrorCodedException> {
                context.calculationCreationService
                    .publishToMain(context.accessCheck, context.projectKey, bomNodeId, branchId)
                    .block()
            }
        assertThat(exception.message)
            .isEqualTo("500 INTERNAL_SERVER_ERROR \"Cannot save variant, the source calculations changed. Please update first.\"")
    }

    fun updateAndPublish(branchId: String): Mono<PublishResult> {
        return context.manufacturingCalculationService.mergeAndRemoveOpenMerges(
            context.accessCheck,
            BranchId(branchId),
            MergeSourceType.MASTER,
            BomNodeId(bomNodeId),
        )
            .flatMap { result ->
                context.bomPublishService
                    .publish(context.accessCheck, BomNodeId(bomNodeId), result.branch!!, DirtyStateHandling.NONE)
            }
    }

    fun deleteBomNodeAndDontSave(
        nodeNameToDelete: String,
        branchId: String?,
    ): Mono<BomNodeCreationResponse> {
        return getManufacturing().flatMap {
            val childEntry = getChildBomEntryByName(it.children, nodeNameToDelete)
            context.calculationCreationService
                .deleteObject(context.accessCheck, context.projectKey, bomNodeId, childEntry.id!!, branchId, false)
        }
    }

    fun deleteBomNodeAndSave(
        nodeNameToDelete: String,
        branchId: String?,
    ): Mono<PublishResult> {
        return getManufacturing().flatMap {
            val childEntry = getChildBomEntryByName(it.children, nodeNameToDelete)
            context.calculationCreationService
                .deleteObject(context.accessCheck, context.projectKey, bomNodeId, childEntry.id!!, branchId, false)
        }.flatMap { result ->
            context.bomPublishService.publish(
                context.accessCheck,
                BomNodeId(bomNodeId),
                result.branchId.toMongoID(),
                DirtyStateHandling.NONE,
            )
        }
    }

    fun saveAsVariant(
        branchId: String,
        variantName: String,
    ): Mono<BomNodeCreationResponse> {
        return context.calculationCreationService.saveVariant(context.accessCheck, bomNodeId, branchId, variantName)
    }

    fun assertConflictOnSetAsMain(branchId: String) {
        val exception =
            assertThrows<ErrorCodedException> {
                setVariantAsMain(branchId).block()
            }
        assertThat(
            exception.message,
        ).isEqualTo("500 INTERNAL_SERVER_ERROR \"Cannot save variant, the source calculations changed. Please update first.\"")
    }

    fun updateAndUseAsMain(branchId: String): Mono<PublishResult> {
        return context.manufacturingCalculationService.mergeAndRemoveOpenMerges(
            context.accessCheck,
            BranchId(branchId),
            MergeSourceType.MASTER,
            BomNodeId(bomNodeId),
        )
            .flatMap { result ->
                context.saveToSourceBranchService.saveToSourceBranch(
                    context.accessCheck,
                    BomNodeId(result.result.id),
                    result.branch!!,
                    DirtyStateHandling.NONE,
                ).flatMap {
                    context.bomPublishService.publish(context.accessCheck, BomNodeId(bomNodeId), BranchId(it.id), DirtyStateHandling.NONE)
                }
            }
    }

    fun copyCalculation(
        branchId: String,
        sourceProjectId: String,
        targetProjectId: String,
        manufacturingEntityId: String,
    ): List<Node> {
        val source =
            CalculationEntityCopyController.EntitySelectorDto(
                sourceProjectId,
                bomNodeId = bomNodeId,
                branchId = branchId,
                entityId = manufacturingEntityId,
            )
        val target = CalculationEntityCopyController.EntitySelectorDto(targetProjectId)
        val copiedNodes =
            context.noReCalcCopyService.copyCalculation(
                context.accessCheck,
                CalculationEntityCopyController.SourceAndTargetEntitySelectorDto(
                    listOf(source),
                    target,
                    CalculationEntityCopyController.CopyContextDto(),
                ),
            ).mapNotNull { it.id }.collectList().block()!!.map { it!!.toMongoBomNodeId().toHexString() }

        val copiedBomExpResult =
            fetchCopiedNode(targetProjectId).filter { node ->
                node.bomExplorerUniqueId in copiedNodes
            }
        return copiedBomExpResult.map {
            Node(
                it.bomExplorerUniqueId,
                it.entityId,
                context,
            )
        }
    }

    fun fetchVariantBranch(): String =
        context.bomNodeService.getManufacturingBranches(context.accessCheck, bomNodeId)
            .mapNotNull { it }.collectList().block()!!.first { !it.master }.id

    fun fetchVariantBranchbyName(branchName: String): String =
        context.bomNodeService.getManufacturingBranches(context.accessCheck, bomNodeId)
            .mapNotNull { it }.collectList().block()!!.first { !it.master && it.name == branchName }.id

    private fun fetchCopiedNode(targetProjectId: String): List<BomExpBaseDto> {
        val rootNodes = context.bomExplorerServiceV2.getBomNodes(context.accessCheck, targetProjectId, null).block()!!
        val req1 =
            BomExpRequestDto(
                listOf(
                    BomExpNodeQueryDto(rootNodes[0].id, true, null),
                ),
                null,
            )
        val rootWithSubNodes = context.bomExplorerServiceV2.getBomNodes(context.accessCheck, targetProjectId, req1).block()!!
        return rootWithSubNodes
    }

    fun saveAsVariantWithOutBranch(
        variantName: String,
        projectId: String,
    ) {
        context.bomTreeService.copyMain(
            context.accessCheck,
            bomNodeId.toMongoID(),
            variantName,
            projectId.toMongoID(),
        ).block()!!
    }

    fun setPriceToMaterialAndSave(
        variantId: String?,
        newPrice: String,
    ): Mono<BomNodeCreationResponse> {
        val updatedMaterialObject =
            ObjectUpdate(
                "materialBasePrice",
                newPrice,
                "Money",
                null,
                null,
            )

        return getManufacturing().map {
            getManufacturingByType(it.children, "MATERIAL").id!!
        }.flatMap { materialEntityId ->
            context.calculationCreationService.modifyObject(
                context.accessCheck,
                context.projectKey,
                bomNodeId,
                materialEntityId,
                variantId,
                listOf(updatedMaterialObject),
                true,
            )
        }
    }

    fun assertThatBomNodeDoesNotExists() {
        val exception =
            assertThrows<BomNodeNotFoundException> {
                context.calculationCreationService.getCalc(context.accessCheck, context.projectKey, bomNodeId, null, true).block()
            }
        assertThat(exception.message)
            .isEqualTo("404 NOT_FOUND \"Could not find bom node ${BomNodeId(bomNodeId).toBomEntryId().id} branchId=null\"")
    }

    fun getManufacturingByType(
        children: List<PubApiManufacturing>,
        type: String,
    ): PubApiManufacturing {
        return children.first { child ->
            child.type == type
        }
    }

    fun setPeakUsableProductionVolumePerYearAndSave(newValue: String): Mono<BomNodeCreationResponse> {
        val updatePartNumberObject =
            ObjectUpdate(
                "peakUsableProductionVolumePerYear",
                newValue,
                "Pieces",
                "PIECE",
                null,
            )
        return context.calculationCreationService.modifyObject(
            context.accessCheck,
            context.projectKey,
            bomNodeId,
            manufacturingEntityId,
            null,
            listOf(updatePartNumberObject),
            true,
        )
    }

    fun getPeakUsableProductionVolumePerYear(): BigDecimal = getField("peakUsableProductionVolumePerYear") as BigDecimal

    fun getPartNumber(): String = getField("partNumber") as String

    fun getBomNodeId(): String = bomNodeId

    fun getManufacturingEntityId(): String = manufacturingEntityId

    fun updatePartNumberWithoutSave(
        variantId: String?,
        newValue: String,
    ): Mono<BomNodeCreationResponse> {
        return updatePartNumber(variantId, newValue, false)
    }

    fun updatePartNumberAndSave(
        variantId: String?,
        newValue: String,
    ): Mono<BomNodeCreationResponse> {
        return updatePartNumber(variantId, newValue, true)
    }

    fun setVariantAsMain(branchId: String): Mono<BomNodeCreationResponse> {
        return context.calculationCreationService.publishToMain(context.accessCheck, context.projectKey, bomNodeId, branchId)
    }

    private fun updatePartNumber(
        variantId: String?,
        newValue: String,
        save: Boolean,
    ): Mono<BomNodeCreationResponse> {
        val updatePartNumberObject =
            ObjectUpdate(
                "partNumber",
                newValue,
                "Text",
                null,
                null,
            )
        return context.calculationCreationService.modifyObject(
            context.accessCheck,
            context.projectKey,
            bomNodeId,
            manufacturingEntityId,
            variantId,
            listOf(updatePartNumberObject),
            save,
        )
    }

    private fun getField(name: String): Any {
        return getManufacturing().map {
            it.outputs.first { it.name == name }.value
        }.block()!!
    }

    private fun getChildBomEntryByName(
        children: List<PubApiManufacturing>,
        name: String,
    ): PubApiManufacturing {
        return children.first { child ->
            child.type == "BOM_ENTRY" && child.name == name
        }
    }

    private fun addSubNode(
        parentBomNodeId: String,
        parentManufacturingId: String,
        partName: String,
        save: Boolean,
    ): Mono<BomNodeCreationResponse> {
        val bomEntry = subnodeBomEntry(partName, partName)

        return context.calculationCreationService.addObjectTree(
            accessCheck = context.accessCheck,
            projectKey = context.projectKey,
            parentBomNodeId = parentBomNodeId,
            objectTree = bomEntry,
            parentManufacturingEntityId = ObjectId(parentManufacturingId),
            save = save,
            variantId = null,
        )
    }

    private fun addMaterial(
        parentBomNodeId: String,
        parentManufacturingId: String,
        netWeight: Int,
        save: Boolean,
    ): Mono<BomNodeCreationResponse> {
        val material =
            PubApiManufacturing(
                id = null,
                bomNodeId = null,
                name = "MaterialBar",
                type = "MATERIAL",
                manufacturingType = "ManualMaterialV2",
                masterdataKey = null,
                model = null,
                part = null,
                inputs =
                    listOf(
                        designation("MaterialBar"),
                        materialBasePrice(1000),
                        materialDensity("3"),
                        netWeightPerPart(netWeight),
                        scrapWeightPerPart(3),
                    ),
            )

        return context.calculationCreationService.addObjectTree(
            accessCheck = context.accessCheck,
            projectKey = context.projectKey,
            parentBomNodeId = parentBomNodeId,
            objectTree = material,
            parentManufacturingEntityId = ObjectId(parentManufacturingId),
            save = save,
            variantId = null,
        )
    }

    private fun netWeightPerPart(value: Int): FieldParameter {
        return FieldParameter(
            "netWeightPerPart",
            value,
            "Weight",
            "KILOGRAM",
            null,
        )
    }

    private fun scrapWeightPerPart(value: Int): FieldParameter {
        return FieldParameter(
            "scrapWeightPerPart",
            value,
            "Weight",
            "KILOGRAM",
            null,
        )
    }

    private fun materialDensity(value: String): FieldParameter {
        return FieldParameter(
            "density",
            value,
            "Density",
            "GRAM_PER_CCM",
            null,
        )
    }

    private fun materialBasePrice(value: Int): FieldParameter {
        return FieldParameter(
            "materialBasePrice",
            value,
            "Money",
            null,
            null,
        )
    }

    fun designation(value: String): FieldParameter {
        return FieldParameter(
            "displayDesignation",
            value,
            "Text",
            null,
            null,
        )
    }
}

/**
 * Used to pass in services, projectKey and accessCheck to a Node. Should not be used in tests.
 */
data class Context(
    val accessCheck: AccessCheck,
    val projectKey: String,
    val calculationCreationService: PublicAPICalculationServiceImpl,
    val manufacturingCalculationService: ManufacturingCalculationService,
    val noReCalcCopyService: CalculationNoReCalcCopyService,
    val bomExplorerServiceV2: BomExplorerServiceV2,
    val bomNodeService: BomNodeService,
    val bomTreeService: BomTreeService,
    val bomPublishService: BomPublishService,
    val saveToSourceBranchService: SaveToSourceBranchService,
)
