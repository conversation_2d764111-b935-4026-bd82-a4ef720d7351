package com.nu.bom.core.integrationtests

import com.nu.bom.core.api.COST_PER_PART
import com.nu.bom.core.api.CalculationEntityCopyController
import com.nu.bom.core.api.dtos.BomNodeDto
import com.nu.bom.core.config.defaultMasterDataMocksAnswer
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.toMongoID
import com.nu.bom.core.model.toMongoProjectId
import com.nu.bom.core.model.toUUID
import com.nu.bom.core.service.BomNodeService
import com.nu.bom.core.service.BomTreeService
import com.nu.bom.core.service.FolderService
import com.nu.bom.core.service.MasterDataService
import com.nu.bom.core.service.PartService
import com.nu.bom.core.service.ProjectService
import com.nu.bom.core.service.WorkspaceService
import com.nu.bom.core.service.bomrads.AllChildren
import com.nu.bom.core.service.bomrads.BomradsFileUploadService
import com.nu.bom.core.service.file.MediaTypeDetectionService
import com.nu.bom.core.service.file.MultiPartUpload
import com.nu.bom.core.service.file.S3CloudAwareFileService
import com.nu.bom.core.service.file.SecureFileService
import com.nu.bom.core.service.file.SecureFileServiceImpl
import com.nu.bom.core.service.file.UploadType
import com.nu.bom.core.service.file.UploadablePayload
import com.nu.bom.core.service.file.module.UpdateMode
import com.nu.bom.core.service.file.module.UploadModule
import com.nu.bom.core.threedb.ThreeDbServiceMock
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.user.UserService
import com.nu.bom.core.user.withAccessCheck
import com.nu.bom.core.utils.AccountTestUtil
import com.nu.bom.core.utils.CalculationBuilderService
import com.nu.bom.core.utils.CharUtils
import com.nu.bom.core.utils.CompositeCalculationBuilder
import com.nu.bom.core.utils.NbkClient
import com.nu.bomrads.dto.FileUploadDto
import com.nu.bomrads.dto.OwnerType
import com.nu.bomrads.dto.ProjectCreationDTO
import com.nu.bomrads.dto.admin.FolderUpdateDTO
import com.nu.bomrads.dto.admin.ProjectDTO
import com.nu.bomrads.enumeration.Roles
import com.nu.bomrads.nuxt.RolesHelper
import com.tset.bom.clients.VirusScanner
import org.assertj.core.api.Assertions.assertThat
import org.bson.types.ObjectId
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.Mockito
import org.mockito.kotlin.any
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import reactor.core.publisher.Mono
import reactor.test.StepVerifier

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@AutoConfigureWebTestClient
class CopyPasteControllerIT : NbkClient.With {
    companion object {
        val logger: Logger = LoggerFactory.getLogger(CopyPasteControllerIT::class.java)
    }

    @Autowired
    private lateinit var threeDbServiceMock: ThreeDbServiceMock

    @Autowired
    private lateinit var builderService: CalculationBuilderService

    @Autowired
    private lateinit var masterDataService: MasterDataService

    @Autowired
    private lateinit var accountUtil: AccountTestUtil

    @Autowired
    private lateinit var bomTreeService: BomTreeService

    @Autowired
    private lateinit var bomNodeService: BomNodeService

    @Autowired
    private lateinit var projectService: ProjectService

    @Autowired
    private lateinit var folderService: FolderService

    @Autowired
    private lateinit var workspaceService: WorkspaceService

    @Autowired
    override lateinit var nbkClient: NbkClient

    @Autowired
    lateinit var partService: PartService

    @Autowired
    private lateinit var bomradsFileUploadService: BomradsFileUploadService

    private val s3CloudFileService =
        object : S3CloudAwareFileService {
            override fun upload(
                payload: ByteArray,
                accessCheck: AccessCheck,
            ) = Mono.just("")

            override fun upload(
                payload: ByteArray,
                fileName: String,
                accessCheck: AccessCheck?,
            ) = Mono.just("")

            override fun startMultiPartUpload(
                fileName: String,
                accessCheck: AccessCheck?,
            ) = Mono.just(
                MultiPartUpload(
                    "key",
                    "bucket",
                    "uploadId",
                    1,
                    emptyList(),
                ),
            )

            override fun uploadPart(
                multiPartUpload: MultiPartUpload,
                payload: ByteArray,
                accessCheck: AccessCheck?,
            ): Mono<MultiPartUpload> {
                TODO("Not yet implemented")
            }

            override fun completeMultiPartUpload(
                multiPartUpload: MultiPartUpload,
                accessCheck: AccessCheck?,
            ) = Mono.just("")

            override fun uploadAndContinueIfExists(
                payload: ByteArray,
                fileName: String,
                accessCheck: AccessCheck?,
            ) = Mono.just("")

            override fun uploadAndContinueIfExists(
                payload: ByteArray,
                fileName: String,
            ) = Mono.just("")

            override fun download(
                uploadId: String,
                accessCheck: AccessCheck,
            ): Mono<ByteArray> {
                TODO("Not yet implemented")
            }

            override fun delete(
                uploadId: String,
                folder: String,
            ): Mono<Boolean> {
                TODO("Not yet implemented")
            }
        }

    @Autowired
    private lateinit var virusScanner: VirusScanner

    @Autowired
    private lateinit var mediaTypeDetectionService: MediaTypeDetectionService

    @Autowired
    private lateinit var userService: UserService

    @Autowired
    private lateinit var bomradsFileService: BomradsFileUploadService

    private lateinit var projectCreationDTO: ProjectCreationDTO

    private val uploadModule =
        object : UploadModule {
            override fun canAccessOwner(
                accessCheck: AccessCheck,
                ownerIds: List<String>,
            ) = Mono.just(true)

            override fun updateOwner(
                accessCheck: AccessCheck,
                fileUploadDto: FileUploadDto,
                updateMode: UpdateMode,
            ): Mono<Void> = Mono.empty()

            override fun isMimeTypeAllowed(
                mimeType: String,
                filename: String,
            ) = Mono.just(true)

            override fun getOwnerType() = OwnerType.valueOf("BOM_NODE")

            override fun getUploadType() = UploadType.BOM_NODE_ATTACHMENT

            override fun isValidOwnerId(ownerId: String) = true

            override fun getAccountIdForOwner(
                accessCheck: AccessCheck,
                ownerId: String,
            ) = Mono.just("true")
        }

    private lateinit var fileService: SecureFileService

    private lateinit var accessCheck: AccessCheck

    private var variantId: String = ""

    @BeforeEach
    fun setup() {
        val (ac, project) = nbkClient.setupWithProject(name = "CopyPastControllerIT", key = "CPCIT")
        accessCheck = ac
        projectCreationDTO = project
        masterDataService.apply {
            Mockito
                .doAnswer(::defaultMasterDataMocksAnswer)
                .`when`(this)
                .getLatestMasterDataByCompositeKey(accessCheck = any(), selector = any(), mode = any())
        }
        fileService =
            SecureFileServiceImpl(
                s3FileService = s3CloudFileService,
                mediaTypeDetector = mediaTypeDetectionService,
                virusScanner = virusScanner,
                userService = userService,
                bomradsFileUploadService = bomradsFileUploadService,
                uploadModules = listOf(uploadModule),
                threeDbService = threeDbServiceMock,
            )
    }

    @AfterEach
    fun teardown() {
        nbkClient.cleanup()
        accountUtil.cleanup()
    }

    @ParameterizedTest
    @ValueSource(booleans = [false, true])
    fun `copy calculation with attachment`(differentProject: Boolean) {
        val sourceProject = createTestProject(accessCheck, CharUtils.randomAlphanumericString(6))
        val partId = createPartWithFiles(sourceProject.project!!)
        // manual root with files
        val tree =
            CompositeCalculationBuilder
                .create(CompositeCalculationBuilder.TreeType.MANUAL)
                .withDefaultProject(sourceProject)
                .withCustomizer {
                    it.withPart(partId)
                }.build()

        val (rootSnapshot, _, _) = builderService.build(tree, accessCheck).withAccessCheck(accessCheck).block()!!

        val source =
            CalculationEntityCopyController.EntitySelectorDto(
                rootSnapshot.projectId().toHexString(),
                bomNodeId = rootSnapshot.bomNodeId().toHexString(),
                branchId = rootSnapshot.branchId().toHexString(),
                entityId = rootSnapshot.manufacturing?._id!!.toHexString(),
            )
        val target = getTargetProject(accessCheck, rootSnapshot, differentProject)

        StepVerifier
            .create(copyCalculation(source, target, CalculationEntityCopyController.CopyContextDto()))
            .assertNext { result ->
                assertCopyResultCorrect(result, rootSnapshot)
                StepVerifier
                    .create(
                        bomradsFileService
                            .findByOwnerIdAndUploadType(
                                accessCheck,
                                result.id.toUUID().toString(),
                                UploadType.BOM_NODE_ATTACHMENT,
                            ).collectList(),
                    ).assertNext {
                        assertThat(it.size).isEqualTo(2)
                    }.verifyComplete()
            }.verifyComplete()
    }

    private fun createPartWithFiles(project: ProjectDTO): ObjectId {
        // upload files
        val txtUpload = UploadablePayload.fromBase64("test01.txt", "c2ltcGxlIHRleHQ=")
        val jsonUpload = UploadablePayload.fromBase64("test02.json", "eyAibXlrZXkiOiJteXZhbCIgfQ==")

        val files =
            listOf(
                fileService
                    .upload(accessCheck, UploadType.BOM_NODE_ATTACHMENT, null, txtUpload)
                    .withAccessCheck(accessCheck)
                    .block()!!,
                fileService
                    .upload(accessCheck, UploadType.BOM_NODE_ATTACHMENT, null, jsonUpload)
                    .withAccessCheck(accessCheck)
                    .block()!!,
            )
        return partService
            .createPart(
                designation = "TestPart",
                number = "1234",
                accessCheck = accessCheck,
                projectId = project.id.toMongoProjectId(),
                images = files.map { it.id },
                projectKey = project.key,
                attachmentToVersionedPart = mapOf(),
            ).withAccessCheck(accessCheck)
            .mapNotNull { it._id }
            .block()!!
    }

    private fun createTestProject(
        accessCheck: AccessCheck,
        projectName: String,
    ): ProjectCreationDTO {
        val workspace =
            workspaceService
                .createWorkspace(
                    accessCheck,
                    CharUtils.randomAlphanumericString(6).uppercase(),
                    listOf(
                        RolesHelper.NewRoleDto(Roles.WORKSPACE_OWNER.name, null),
                    ),
                ).block()!!
        val folderName = CharUtils.randomAlphanumericString(6).uppercase()
        val folder = folderService.createOrGet(accessCheck, FolderUpdateDTO(folderName, null, workspace.id)).block()!!

        val projectKey = CharUtils.randomAlphanumericString(6).uppercase()

        return projectService
            .createProject(
                accessCheck,
                projectName,
                projectKey,
                folderId = folder.id,
            ).block()!!
    }

    private fun assertCopyResultCorrect(
        result: BomNodeDto,
        rootSnapshot: BomNodeSnapshot,
        subSnapshot: BomNodeSnapshot? = null,
    ) {
        assertThat(result.manufacturing!!.getField(COST_PER_PART)).isNotNull

        // same title, same part
        assertThat(result.title).isEqualTo(rootSnapshot.title)
        assertThat(result.manufacturing?.part?.designation)
            .isNotNull
            .isEqualTo(rootSnapshot.getBaseManufacturing()?.part?.designation)
        assertThat(result.manufacturing?.part?.number)
            .isNotNull
            .isEqualTo(rootSnapshot.getBaseManufacturing()?.part?.number)

        if (subSnapshot != null) {
            assertThat(result.subNodes).singleElement().satisfies({ copiedSubRelation ->
                StepVerifier
                    .create(manufacturingCrudTestClient.getBomNodeMono(copiedSubRelation.bomNodeId))
                    .assertNext { sub ->
                        // same title, same part
                        assertThat(sub.title).isEqualTo(subSnapshot.title)
                        assertThat(sub.manufacturing?.part?.designation)
                            .isNotNull
                            .isEqualTo(subSnapshot.getBaseManufacturing()?.part?.designation)
                        assertThat(sub.manufacturing?.part?.number)
                            .isNotNull
                            .isEqualTo(subSnapshot.getBaseManufacturing()?.part?.number)
                    }
            })
        } else {
            assertThat(result.subNodes).isEmpty()
        }
    }

    private fun assertVariants(
        accessCheck: AccessCheck,
        bomNodeId: String,
        variantBomNodes: List<BomNodeSnapshot>,
    ) {
        StepVerifier
            .create(bomNodeService.getManufacturingBranches(accessCheck, bomNodeId).collectList())
            .assertNext { branches ->
                assertThat(branches).hasSize(1)
                // main branch
                assertThat(branches.filter { it.master }).hasSize(1)
                val mainBranch = branches.first { it.master }
                assertThat(mainBranch.global).isTrue()
                assertThat(mainBranch.master).isTrue()
                // variants
                val variants = branches.filter { !it.master }.map { it.name }
                assertThat(variantBomNodes.filter { variants.contains(it.title) }).hasSize(variants.size)
            }.verifyComplete()
    }

    @ParameterizedTest
    @ValueSource(booleans = [false, true])
    fun `clone MANUAL root with variant on sub`(differentProject: Boolean) {
        val sourceProject = createTestProject(accessCheck, CharUtils.randomAlphanumericString(6))
        // manual root with manual sub and sub variant
        val tree =
            CompositeCalculationBuilder
                .create(CompositeCalculationBuilder.TreeType.MANUAL)
                .withDefaultProject(sourceProject)
                .withSub(
                    CompositeCalculationBuilder.create(CompositeCalculationBuilder.TreeType.MANUAL),
                ).build()

        val (rootSnapshot, _, accessCheck) = builderService.build(tree, accessCheck).block()!!
        val subSnapshot = rootSnapshot.subNodes[0].snapshot!!

        // sub variant
        val variantBomNode = createVariant(accessCheck, subSnapshot)

        val source =
            CalculationEntityCopyController.EntitySelectorDto(
                rootSnapshot.projectId().toHexString(),
                bomNodeId = rootSnapshot.bomNodeId().toHexString(),
                branchId = rootSnapshot.branchId().toHexString(),
                entityId = rootSnapshot.manufacturing?._id!!.toHexString(),
            )
        val target = getTargetProject(accessCheck, rootSnapshot, differentProject)

        StepVerifier
            .create(copyCalculation(source, target, CalculationEntityCopyController.CopyContextDto()))
            .assertNext { result ->
                assertCopyResultCorrect(result, rootSnapshot, subSnapshot)
                assertVariants(accessCheck, result.subNodes.first().bomNodeId, listOf(variantBomNode))
            }.verifyComplete()
    }

    private fun createVariant(
        accessCheck: AccessCheck,
        rootSnapshot: BomNodeSnapshot? = null,
        bomNodeId: BomNodeId? = null,
        branchId: BranchId? = null,
    ): BomNodeSnapshot {
        val variantName = CharUtils.randomAlphanumericString(6).uppercase()
        val sourceNodeId = requireNotNull(rootSnapshot?.bomNodeId() ?: bomNodeId)

        val variantBranchDto =
            bomTreeService
                .copyMain(
                    accessCheck,
                    sourceNodeId,
                    variantName,
                    rootSnapshot?.branchId() ?: branchId,
                ).block()!!

        return bomNodeService
            .getBomNode(
                accessCheck,
                sourceNodeId,
                variantBranchDto.id.toMongoID(),
                loadingMode = AllChildren(sourceNodeId),
            ).block()!!
    }

    @Test
    fun `clone MANUAL sub`() {
        val sourceProject = createTestProject(accessCheck, CharUtils.randomAlphanumericString(6))
        // manual calc - 3 levels
        val tree =
            CompositeCalculationBuilder
                .create(CompositeCalculationBuilder.TreeType.MANUAL)
                .withDefaultProject(sourceProject)
                .withSub(
                    CompositeCalculationBuilder
                        .create(CompositeCalculationBuilder.TreeType.MANUAL)
                        .withCustomizer { it.withTitle("Sub") }
                        .withSub(
                            CompositeCalculationBuilder
                                .create(CompositeCalculationBuilder.TreeType.MANUAL)
                                .withCustomizer { it.withTitle("SubSub") },
                        ),
                ).build()

        val (rootSnapshot, _, _) = builderService.build(tree, accessCheck).block()!!

        val subSnapshot = rootSnapshot.subNodes[0].snapshot!!
        val subSubSnapshot = subSnapshot.subNodes[0].snapshot!!

        val source =
            CalculationEntityCopyController.EntitySelectorDto(
                subSnapshot.projectId().toHexString(),
                bomNodeId = subSnapshot.bomNodeId().toHexString(),
                branchId = subSnapshot.branchId().toHexString(),
                entityId = subSnapshot.manufacturing?._id!!.toHexString(),
                parentId = rootSnapshot.bomNodeId().toHexString(),
            )
        val target = CalculationEntityCopyController.EntitySelectorDto(rootSnapshot.projectId().toHexString())

        StepVerifier
            .create(
                copyCalculation(source, target, CalculationEntityCopyController.CopyContextDto()),
            ).assertNext { copyResult ->
                assertThat(copyResult.manufacturing!!.getField(COST_PER_PART)).isNotNull

                // same title, same part
                assertThat(copyResult.title).isEqualTo(subSnapshot.title)
                assertThat(copyResult.manufacturing?.part?.designation)
                    .isNotNull
                    .isEqualTo(subSnapshot.getBaseManufacturing()?.part?.designation)
                assertThat(copyResult.manufacturing?.part?.number)
                    .isNotNull
                    .isEqualTo(subSnapshot.getBaseManufacturing()?.part?.number)

                assertThat(copyResult.subNodes).singleElement().satisfies({ copiedSubRelation ->
                    StepVerifier
                        .create(
                            manufacturingCrudTestClient.getBomNodeMono(copiedSubRelation.bomNodeId),
                        ).assertNext { sub ->
                            // same title, same part
                            assertThat(sub.title).isEqualTo(subSubSnapshot.title)
                            assertThat(sub.manufacturing?.part?.designation)
                                .isNotNull
                                .isEqualTo(subSubSnapshot.getBaseManufacturing()?.part?.designation)
                            assertThat(sub.manufacturing?.part?.number)
                                .isNotNull
                                .isEqualTo(subSubSnapshot.getBaseManufacturing()?.part?.number)
                        }
                })
            }.verifyComplete()
    }

    @Test
    fun `copy MANUAL sub`() {
        val sourceProject = createTestProject(accessCheck, CharUtils.randomAlphanumericString(6))
        // manual calc - 2 levels
        val tree =
            CompositeCalculationBuilder
                .create(CompositeCalculationBuilder.TreeType.MANUAL)
                .withDefaultProject(sourceProject)
                .withCustomizer { it.withTitle("Root") }
                .withSub(
                    CompositeCalculationBuilder
                        .create(CompositeCalculationBuilder.TreeType.MANUAL)
                        .withCustomizer { it.withTitle("Sub") },
                ).build()

        val (rootSnapshot, _, _) = builderService.build(tree, accessCheck).block()!!

        val subSnapshot = rootSnapshot.subNodes[0].snapshot!!

        val source =
            CalculationEntityCopyController.EntitySelectorDto(
                subSnapshot.projectId().toHexString(),
                bomNodeId = subSnapshot.bomNodeId().toHexString(),
                branchId = subSnapshot.branchId().toHexString(),
                entityId = subSnapshot.manufacturing?._id!!.toHexString(),
            )

        val target =
            CalculationEntityCopyController.EntitySelectorDto(
                subSnapshot.projectId().toHexString(),
                bomNodeId = subSnapshot.bomNodeId().toHexString(),
                branchId = subSnapshot.branchId().toHexString(),
                entityId = subSnapshot.manufacturing!!._id.toHexString(),
            )

        StepVerifier
            .create(
                copyCalculation(source, target, CalculationEntityCopyController.CopyContextDto()),
            ).assertNext { result ->
                assertThat(result.manufacturing!!.getField(COST_PER_PART)).isNotNull

                // same title, part for sub
                assertThat(result.title).isEqualTo(subSnapshot.title)
                assertThat(result.manufacturing?.part?.designation)
                    .isNotNull
                    .isEqualTo(subSnapshot.getBaseManufacturing()?.part?.designation)
                assertThat(result.manufacturing?.part?.number)
                    .isNotNull
                    .isEqualTo(subSnapshot.getBaseManufacturing()?.part?.number)

                assertThat(result.subNodes).singleElement().satisfies({ copiedSubRel ->
                    StepVerifier
                        .create(
                            bomNodeService.getBomNode(
                                accessCheck,
                                BomNodeId(copiedSubRel.bomNodeId),
                                BranchId(result.branch.id),
                            ),
                        ).assertNext { sub ->
                            // same title, same part
                            assertThat(sub.title).isEqualTo(subSnapshot.title)
                            assertThat(sub.getBaseManufacturing()?.part?.designation)
                                .isNotNull
                                .isEqualTo(subSnapshot.getBaseManufacturing()?.part?.designation)
                            assertThat(sub.getBaseManufacturing()?.part?.number)
                                .isNotNull
                                .isEqualTo(subSnapshot.getBaseManufacturing()?.part?.number)
                        }.verifyComplete()
                })
            }.verifyComplete()
    }

    private fun getTargetProject(
        accessCheck: AccessCheck,
        rootSnapshot: BomNodeSnapshot,
        differentProject: Boolean,
    ): CalculationEntityCopyController.EntitySelectorDto {
        val targetProjectId =
            if (differentProject) {
                createTestProject(accessCheck, CharUtils.randomAlphanumericString(6)).project!!.id.idToString()
            } else {
                rootSnapshot.projectId().toHexString()
            }
        return CalculationEntityCopyController.EntitySelectorDto(targetProjectId)
    }

    private fun copyCalculation(
        source: CalculationEntityCopyController.EntitySelectorDto,
        target: CalculationEntityCopyController.EntitySelectorDto,
        copyContextDto: CalculationEntityCopyController.CopyContextDto,
    ): Mono<BomNodeDto> = manufacturingCrudTestClient.copyBomTree(listOf(source), target, copyContextDto)
}
