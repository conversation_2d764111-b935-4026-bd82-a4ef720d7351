package com.nu.bom.core.api

import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.api.dtos.ManufacturingDto
import com.nu.bom.core.manufacturing.entities.Tool
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.AccountTestUtil
import com.nu.bom.core.utils.AccountTestUtil.Companion.authHeader
import com.nu.bom.core.utils.CalculationBuilderService
import com.nu.bom.core.utils.CompositeCalculationBuilder
import com.nu.bom.core.utils.EntityBuilder
import com.nu.bomrads.dto.ProjectCreationDTO
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType.APPLICATION_JSON
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.reactive.server.WebTestClient

@SpringBootTest
@ActiveProfiles("test")
@AutoConfigureWebTestClient
class BomQueryControllerIT {
    @Autowired
    private lateinit var webTestClient: WebTestClient

    @Autowired
    private lateinit var builderService: CalculationBuilderService

    @Autowired
    private lateinit var accountUtil: AccountTestUtil

    private lateinit var accessCheck: AccessCheck

    private lateinit var project: ProjectCreationDTO

    @BeforeEach
    fun setup() {
        val setup = accountUtil.setupWithProject("BomQueryControllerIT", "BQC")
        accessCheck = setup.first
        project = setup.second
    }

    @Test
    fun `given a tree with two manual calculation when filtering for manufacturing we should return one entry`() {
        val tree =
            CompositeCalculationBuilder
                .create(CompositeCalculationBuilder.TreeType.MANUAL)
                .withDefaultProject(project)
                .withSub(
                    CompositeCalculationBuilder.create(CompositeCalculationBuilder.TreeType.MANUAL),
                ).build()
        val (snapshot, _, _) = builderService.build(tree, accessCheck).block()!!
        val bomNodeId = snapshot.bomNodeId
        val branchId = snapshot.branchId()

        val entityType = Entities.MANUFACTURING.toString()
        val response =
            webTestClient
                .get()
                .uri("/api/bom/filter/man/${bomNodeId?.toHexString()}?entityType=$entityType&branch=${branchId.toHexString()}")
                .accept(APPLICATION_JSON)
                // For some reason accessCheck token is different during creation
                .authHeader(accessCheck.token)
                .exchange()

        response
            .expectStatus()
            .isOk
            .expectBodyList(ManufacturingDto::class.java)
            .hasSize(1)
    }

    @Test
    fun `given a tree with two manual calculation when filtering for tools should enrich data`() {
        val tree =
            CompositeCalculationBuilder
                .create(CompositeCalculationBuilder.TreeType.MANUAL)
                .withSub(
                    CompositeCalculationBuilder
                        .create(CompositeCalculationBuilder.TreeType.MANUAL)
                        .withCustomizer {
                            it.withChild(
                                EntityBuilder.create(
                                    Tool::class,
                                ),
                            )
                            it.withPart(
                                partDesignation = "DefaultPart",
                                partNumber = "1234",
                            )
                        },
                ).withDefaultProject(project)
                .build()
        val (snapshot, _, _) = builderService.build(tree, accessCheck).block()!!
        val bomNodeId = snapshot.bomNodeId
        val branchId = snapshot.branchId()

        val entityType = Entities.TOOL.toString()
        val response =
            webTestClient
                .get()
                .uri("/api/bom/filter/man/${bomNodeId?.toHexString()}?entityType=$entityType&branch=${branchId.toHexString()}")
                .accept(APPLICATION_JSON)
                // For some reason accessCheck token is different during creation
                .authHeader(accessCheck.token)
                .exchange()

        val body =
            response
                .expectStatus()
                .isOk
                .expectBodyList(ManufacturingDto::class.java)
                .returnResult()
                .responseBody

        assertThat(body)
            .first()
            .satisfies({
                assertThat(it)
                    .extracting { dto ->
                        dto.getField("parentBomDesignation")
                    }.isEqualTo(FieldParameter("parentBomDesignation", "Text", value = "DefaultPart", source = "C", metaInfo = emptyMap()))
            }, {
                assertThat(it)
                    .extracting { dto ->
                        dto.getField("parentStep")
                    }.isEqualTo(FieldParameter("parentStep", "Text", value = "Standard", source = "C", metaInfo = emptyMap()))
            })
    }
}
