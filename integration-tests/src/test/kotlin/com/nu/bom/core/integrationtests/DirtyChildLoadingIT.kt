package com.nu.bom.core.integrationtests

import com.nu.bom.core.api.COST_PER_PART
import com.nu.bom.core.api.dtos.BomNodeDto
import com.nu.bom.core.api.dtos.BranchDto
import com.nu.bom.core.api.dtos.FieldConversionService
import com.nu.bom.core.controller.CalculationResultWithSnapshot
import com.nu.bom.core.controller.InputParameter
import com.nu.bom.core.exception.ErrorCodedException
import com.nu.bom.core.exception.readable.ErrorCode
import com.nu.bom.core.manufacturing.entities.BomEntry
import com.nu.bom.core.manufacturing.entities.BomNodeReference
import com.nu.bom.core.manufacturing.entities.ElectronicComponent
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.entities.ManualManufacturingStep
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.UnitOverrideContext
import com.nu.bom.core.model.BomEntryCreationData
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.createBranchId
import com.nu.bom.core.service.BomNodeService
import com.nu.bom.core.service.BomPublishService
import com.nu.bom.core.service.BomTreeService
import com.nu.bom.core.service.CalculationEntityCopyService
import com.nu.bom.core.service.DirtyChildLoadingService
import com.nu.bom.core.service.EntityCreationDataConversionService
import com.nu.bom.core.service.EntityCreationService
import com.nu.bom.core.service.HistoryService
import com.nu.bom.core.service.ManufacturingCalculationService
import com.nu.bom.core.service.ManufacturingEntityDeletionService
import com.nu.bom.core.service.ManufacturingEntityRearrangementService
import com.nu.bom.core.service.ManufacturingFieldLockService
import com.nu.bom.core.service.bomimporter.BomImporterFinishData
import com.nu.bom.core.service.bomimporter.BomImporterService
import com.nu.bom.core.service.bomimporter.EntityCreationBomImporterService
import com.nu.bom.core.service.bomnode.SaveToSourceBranchService
import com.nu.bom.core.service.change.HISTORY_TYPE
import com.nu.bom.core.service.dto.DirtyStateHandling
import com.nu.bom.core.service.dto.EntitySelector
import com.nu.bom.core.service.exports.TsetFileExportService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.user.withAccessCheck
import com.nu.bom.core.utils.AccountTestUtil
import com.nu.bom.core.utils.CalculationBuilderService
import com.nu.bom.core.utils.CompositeCalculationBuilder
import com.nu.bom.core.utils.assertErrorThat
import com.nu.bom.core.utils.getDefaultInputs
import com.nu.bomrads.dto.ProjectCreationDTO
import com.tset.bom.clients.bomimporter.DataFieldValueDTO
import com.tset.bom.clients.bomimporter.DataRowDto
import com.tset.bom.clients.bomimporter.ImportState
import com.tset.bom.clients.bomimporter.ImportedDataDto
import com.tset.common.testing.Step
import com.tset.common.testing.TestScenario
import com.tset.core.module.bom.CalculationUpdateModule
import com.tset.core.module.bom.calculation.CalculationCreateSubInput
import com.tset.core.module.bom.exchangerates.ExchangeRateMap
import com.tset.core.service.domain.Currency
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.TestFactory
import org.mockito.Mockito.doAnswer
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import reactor.kotlin.core.publisher.toMono

@SpringBootTest
@ActiveProfiles("test")
class DirtyChildLoadingIT {
    @Autowired
    private lateinit var builderService: CalculationBuilderService

    @Autowired
    private lateinit var bomNodeService: BomNodeService

    @Autowired
    private lateinit var bomPublishService: BomPublishService

    @Autowired
    private lateinit var bomTreeService: BomTreeService

    @Autowired
    private lateinit var historyService: HistoryService

    @Autowired
    private lateinit var manufacturingCalculationService: ManufacturingCalculationService

    @Autowired
    private lateinit var manufacturingFieldLockService: ManufacturingFieldLockService

    @Autowired
    private lateinit var entityCreationService: EntityCreationService

    @Autowired
    private lateinit var entityDeletionService: ManufacturingEntityDeletionService

    @Autowired
    private lateinit var entityRearrangementService: ManufacturingEntityRearrangementService

    @Autowired
    private lateinit var calculationUpdateModule: CalculationUpdateModule

    @Autowired
    private lateinit var fieldConversionService: FieldConversionService

    @Autowired
    private lateinit var entityCreationBomImporterService: EntityCreationBomImporterService

    @Autowired
    private lateinit var entityCopyService: CalculationEntityCopyService

    @Autowired
    // this bean is configured as a spy in tests
    private lateinit var bomImporterService: BomImporterService

    @Autowired
    private lateinit var tsetFileExportService: TsetFileExportService

    @Autowired
    private lateinit var dirtyChildLoadingService: DirtyChildLoadingService

    @Autowired
    private lateinit var saveToSourceBranchService: SaveToSourceBranchService

    @Autowired
    private lateinit var accountUtil: AccountTestUtil

    private lateinit var result: Pair<AccessCheck, ProjectCreationDTO>
    private lateinit var currentState: CalculationBuilderService.Result

    @BeforeEach
    fun setup() {
        result =
            accountUtil.setupWithProject(
                name = "DirtyChild Project",
                key = "DCP",
            )
    }

    @AfterEach
    fun cleanup() {
        accountUtil.cleanup()
    }

    private fun recoverImplicitly() {
        val (rootSnapshot, rootEntity, accessCheck) = this.currentState
        val step2 = rootEntity.findByEntityName("Step2")!!
        this.currentState =
            manufacturingCalculationService.setField(
                accessCheck,
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branch,
                field =
                    InputParameter(
                        name = "scrapRate",
                        entityId = step2.entityId,
                        type = "Rate",
                        value = 0.71.toBigDecimal(),
                    ),
                dirtyChildLoading = false,
            ).block()!!.getState()
    }

    @TestFactory
    fun `update field with dirty child loading and recover implicitly`(): TestScenario =
        TestScenario(
            Step("start with dirty calculation") { createManualRootAndSub(createDirty = true) },
            Step("in the history, it is dirty") { checkHistory(expectDirty = true) },
            Step("recover implicitly", this::recoverImplicitly),
            Step("not dirty anymore") { assertDirtyOfTheCurrent(false) },
            Step("in the history, it is not dirty after") { checkHistory(expectDirty = false) },
        )

    private fun recalculateDirty() {
        val (rootSnapshot, _, accessCheck) = this.currentState
        this.currentState =
            dirtyChildLoadingService.recalculateDirty(
                accessCheck,
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branch,
            ).block()!!.getState()
    }

    @TestFactory
    fun `update field with dirty child loading`(): TestScenario =
        TestScenario(
            Step("start with dirty calculation") { createManualRootAndSub(createDirty = true) },
            Step("in the history, it is dirty") { checkHistory(expectDirty = true) },
            Step("recalculate dirty", this::recalculateDirty),
            Step("not dirty anymore") { assertDirtyOfTheCurrent(false) },
            Step("in the history, it is not dirty after") { checkHistory(expectDirty = false) },
        )

    private fun createCommonDirtyScenario(innerStep: Step): TestScenario =
        TestScenario(
            Step("start with dirty calculation") { createManualRootAndSub(createDirty = true) },
            Step("in the history, it is dirty") { checkHistory(expectDirty = true) },
            innerStep,
            Step("still dirty after") { assertDirtyOfTheCurrent(true) },
            Step("in the history, it is still dirty") { checkHistory(expectDirty = true) },
            Step("recalculate dirty", this::recalculateDirty),
            Step("not dirty anymore") { assertDirtyOfTheCurrent(false) },
            Step("in the history, it is not dirty after") { checkHistory(expectDirty = false) },
        )

    @TestFactory
    fun `unlock field with dirty child loading`(): TestScenario =
        createCommonDirtyScenario(
            Step("unlocking a field must preserve dirty state", this::unlockAndCalculate),
        )

    private fun unlockAndCalculate() {
        // unlocking a field must preserve dirty state
        val (rootSnapshot, rootEntity, accessCheck) = this.currentState
        val step2 = rootEntity.findByEntityName("Step2")!!

        // unlocking a field must preserve dirty state
        this.currentState =
            manufacturingFieldLockService.unlockAndCalculate(
                accessCheck,
                bomNodeId = rootSnapshot.bomNodeId().toHexString(),
                branchId = rootSnapshot.branch,
                field =
                    InputParameter(
                        name = "scrapRate",
                        entityId = step2.entityId,
                        type = "Rate",
                        value = 0.88.toBigDecimal(),
                    ),
                dirtyChildLoading = true,
            ).block()!!.getState()
    }

    @TestFactory
    fun `create entity with dirty child loading`(): TestScenario =
        createCommonDirtyScenario(
            Step("creating new entity must preserve dirty state", this::createEntityWithDirtyChildLoading),
        )

    private fun createEntityWithDirtyChildLoading() {
        val (rootSnapshot, rootEntity, accessCheck) = this.currentState
        // creating new entity must preserve dirty state
        this.currentState =
            entityCreationService.createAndCalculate(
                accessCheck,
                creationData =
                    EntityCreationDataConversionService.EntityCreationData(
                        bomNodeId = rootSnapshot.bomNodeId(),
                        branchId = rootSnapshot.branch,
                        parentId = rootEntity._id,
                        projectId = rootSnapshot.projectId(),
                        childBomNodeId = null,
                        items =
                            listOf(
                                EntityCreationDataConversionService.EntityCreationData.ItemData(
                                    entityType = Entities.MANUFACTURING_STEP,
                                    entityClass = ManualManufacturingStep::class,
                                    masterDataSelector = null,
                                    fields = getDefaultInputs(ManualManufacturingStep::class),
                                    overwrites = emptyMap(),
                                    isolated = false,
                                ),
                            ),
                    ),
                dirtyChildLoading = true,
            ).block()!!.getState()
    }

    @TestFactory
    fun `delete entity with dirty child loading`(): TestScenario =
        createCommonDirtyScenario(
            Step("deleting an entity must preserve dirty state", this::deleteEntityWithDirtyChildLoading),
        )

    private fun deleteEntityWithDirtyChildLoading() {
        val (rootSnapshot, rootEntity, accessCheck) = this.currentState

        val material1 = rootEntity.findByEntityName("Material1")!!

        // deleting an entity must preserve dirty state
        this.currentState =
            entityDeletionService.deleteEntity(
                accessCheck,
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branch,
                entityId = material1._id,
                dirtyChildLoading = true,
            ).block()!!.getState()
    }

    @TestFactory
    fun `reorder entity with dirty child loading`(): TestScenario =
        createCommonDirtyScenario(
            Step("reordering entity must preserve dirty state", this::reorderEntityWithDirtyChildLoading),
        )

    private fun reorderEntityWithDirtyChildLoading() {
        val (rootSnapshot, rootEntity, accessCheck) = this.currentState

        val material1 = rootEntity.findByEntityName("Material1")!!
        val cPart1 = rootEntity.findByEntityName("CPart1")!!

        // reordering entity must preserve dirty state
        this.currentState =
            entityRearrangementService.reorder(
                accessCheck,
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branch,
                sourceEntityId = material1._id,
                insertAfterTargetEntityId = cPart1._id,
                insertBeforeTargetEntityId = null,
                dirtyChildLoading = true,
            ).block()!!.getState()
    }

    @TestFactory
    fun `rearrange entity with dirty child loading`(): TestScenario =
        createCommonDirtyScenario(
            Step("rearranging entity must preserve dirty state", this::rearrangeEntityWithDirtyChildLoading),
        )

    private fun rearrangeEntityWithDirtyChildLoading() {
        val (rootSnapshot, rootEntity, accessCheck) = this.currentState

        val step1 = rootEntity.findByEntityName("Step1")!!
        val step2 = rootEntity.findByEntityName("Step2")!!

        // rearranging entity must preserve dirty state
        entityRearrangementService.rearrange(
            accessCheck,
            bomNodeId = rootSnapshot.bomNodeId(),
            branchId = rootSnapshot.branch,
            sourceEntityId = step2._id,
            targetParentId = step1._id,
            dirtyChildLoading = true,
        ).block()!!.getState()
    }

    @TestFactory
    fun `import entity with dirty child loading`(): TestScenario =
        createCommonDirtyScenario(
            Step("importing bom-importer entities must preserve dirty state", this::importEntityWithDirtyChildLoading),
        )

    private fun importEntityWithDirtyChildLoading() {
        val (rootSnapshot, rootEntity, accessCheck) = this.currentState

        doAnswer {
            ImportedDataDto(
                state = ImportState.FINISHED,
                items = createElcoImportDto(3),
            ).toMono()
        }.whenever(bomImporterService).getImport(any(), eq("mock-import-id"))

        // importing bom-importer entities must preserve dirty state
        this.currentState =
            entityCreationBomImporterService.finishImport(
                accessCheck,
                data =
                    BomImporterFinishData(
                        projectId = rootSnapshot.projectId(),
                        bomNodeId = rootSnapshot.bomNodeId(),
                        branchId = rootSnapshot.branch,
                        parentId = rootEntity._id,
                        entityType = Entities.C_PART,
                        entityClass = ElectronicComponent::class,
                        replace = false,
                    ),
                importId = "mock-import-id",
                dirtyChildLoading = true,
            ).block()!!.getState()
    }

    @TestFactory
    fun `paste entity with dirty child loading`(): TestScenario =
        createCommonDirtyScenario(
            Step("copy-paste tree under root (next to its sub) must preserve dirty state", this::pasteEntityWithDirtyChildLoading),
        )

    private fun pasteEntityWithDirtyChildLoading() {
        val (rootSnapshot, _, accessCheck) = this.currentState

        val source =
            EntitySelector(
                rootSnapshot.projectId(),
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branchId(),
                entityId = rootSnapshot.manufacturing?._id!!,
            )

        val target =
            EntitySelector(
                rootSnapshot.projectId(),
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branchId(),
                entityId = rootSnapshot.manufacturing?._id!!,
            )

        val copyRules =
            CalculationEntityCopyService.CopyRules(
                operation = CalculationEntityCopyService.Operation.COPY_PASTE,
            )

        // copy-paste tree under root (next to its sub)
        this.currentState =
            entityCopyService.copyEntity(accessCheck, listOf(source), target, copyRules, dirtyChildLoading = true)
                .withAccessCheck(accessCheck).block()!!.getState()
    }

    @TestFactory
    fun `create sub with dirty child loading`(): TestScenario =
        createCommonDirtyScenario(
            Step("adding a sub must preserve dirty state", this::createSubWithDirtyChildLoading),
        )

    private fun createSubWithDirtyChildLoading() {
        val (rootSnapshot, rootEntity, accessCheck) = this.currentState

        // use Step1 which does not have any subs yet
        val step1 = rootEntity.findByEntityName("Step1")!!
        assertThat(step1.findChild { it is BomNodeReference }).isNull()

        // adding a sub must preserve dirty state
        this.currentState =
            calculationUpdateModule.dispatch(
                accessCheck,
                CalculationCreateSubInput(
                    projectId = rootSnapshot.projectId(),
                    title = "New Sub",
                    fields =
                        getDefaultInputs(ManualManufacturing::class).map { (name, result) ->
                            fieldConversionService.fieldWithResultToFieldParameter(
                                name = name,
                                resultPreConversion = result,
                                entityClass = ManualManufacturing::class.simpleName!!,
                                entity = null,
                                exchangeRateMap = ExchangeRateMap.empty(),
                                unitOverrideContext = UnitOverrideContext.defaultManufacturing,
                                baseCurrency = Currency.EUR,
                                entityForDynamicMetaData = null,
                            )
                        },
                    clazz = ManualManufacturing::class,
                    parentBomData =
                        BomEntryCreationData(
                            bomNodeId = rootSnapshot.bomNodeId(),
                            branchId = rootSnapshot.branch,
                            stepId = step1._id,
                            fields =
                                getDefaultInputs(BomEntry::class).map { (name, result) ->
                                    fieldConversionService.fieldWithResultToFieldParameter(
                                        name = name,
                                        resultPreConversion = result,
                                        entityClass = ManualManufacturing::class.simpleName!!,
                                        exchangeRateMap = ExchangeRateMap.empty(),
                                        unitOverrideContext = UnitOverrideContext.defaultManufacturing,
                                        baseCurrency = Currency.EUR,
                                        entityForDynamicMetaData = null,
                                    )
                                },
                            path = emptyList(),
                        ),
                    partName = "New Sub Part",
                    displayCurrency = Currency.EUR,
                ),
                dirtyChildLoading = true,
            ).block()!!.bomNode.getState()
    }

    @TestFactory
    fun `publish fails with dirty child loading`(): TestScenario =
        TestScenario(
            Step("start with dirty calculation") { createManualRootAndSub(createDirty = true) },
            Step("in the history, it is dirty") { checkHistory(expectDirty = true) },
            Step("publish needs RECALCULATE_ON_DIRTY", this::publishFailsWithDirtyChildLoading),
            Step("not dirty anymore") { assertDirtyOfTheCurrent(false) },
            Step("in the history, it is not dirty after") { checkHistory(expectDirty = false) },
        )

    private fun publishFailsWithDirtyChildLoading() {
        val (rootSnapshot, _, accessCheck) = this.currentState

        assertErrorThat(
            bomPublishService.publish(
                accessCheck,
                rootSnapshot.bomNodeId(),
                rootSnapshot.branchId(),
                DirtyStateHandling.FAIL_ON_DIRTY,
            ),
        ).isInstanceOfSatisfying(ErrorCodedException::class.java) { error ->
            assertThat(error.errorCode).isEqualTo(ErrorCode.FAIL_ON_DIRTY)
        }

        this.currentState =
            bomPublishService.publish(
                accessCheck,
                rootSnapshot.bomNodeId(),
                rootSnapshot.branchId(),
                DirtyStateHandling.RECALCULATE_ON_DIRTY,
            ).block()!!.let { publishResult ->
                getState(publishResult.branch.id().toHexString())
            }
    }

    @TestFactory
    fun `save as public variant fails with dirty child loading`(): TestScenario =
        TestScenario(
            Step("start with dirty calculation") { createManualRootAndSub(createDirty = true) },
            Step("in the history, it is dirty") { checkHistory(expectDirty = true) },
            Step("save as public variant needs RECALCULATE_ON_DIRTY", this::saveAsPublicVariantNeedsRecalculateOnDirty),
            Step("not dirty anymore") { assertDirtyOfTheCurrent(false) },
            Step("in the history, it is not dirty after") { checkHistory(expectDirty = false) },
        )

    private fun saveAsPublicVariantNeedsRecalculateOnDirty() {
        val (rootSnapshot, _, accessCheck) = this.currentState

        assertErrorThat(
            bomTreeService.saveAsPublicVariant(
                accessCheck,
                rootSnapshot.branchId(),
                rootSnapshot.title,
                rootSnapshot.bomNodeId(),
                dirtyStateHandling = DirtyStateHandling.FAIL_ON_DIRTY,
            ),
        ).isInstanceOfSatisfying(ErrorCodedException::class.java) { error ->
            assertThat(error.errorCode).isEqualTo(ErrorCode.FAIL_ON_DIRTY)
        }

        this.currentState =
            bomTreeService.saveAsPublicVariant(
                accessCheck,
                rootSnapshot.branchId(),
                rootSnapshot.title,
                rootSnapshot.bomNodeId(),
                dirtyStateHandling = DirtyStateHandling.RECALCULATE_ON_DIRTY,
            ).block()!!.getState()
    }

    @TestFactory
    fun `save to source branch fails with dirty child loading`(): TestScenario =
        TestScenario(
            Step("start with dirty calculation") { createManualRootAndSub(createDirty = true) },
            Step("in the history, it is dirty") { checkHistory(expectDirty = true) },
            Step("save to source branch ", this::saveToSourceBranchFailsWithDirtyChildLoading),
            Step("not dirty anymore") { assertDirtyOfTheCurrent(false) },
            Step("in the history, it is not dirty after") { checkHistory(expectDirty = false) },
        )

    private fun saveToSourceBranchFailsWithDirtyChildLoading() {
        val (rootSnapshot, _, accessCheck) = this.currentState

        // save state to a variant
        this.currentState =
            bomTreeService.saveAsPublicVariant(
                accessCheck,
                rootSnapshot.branchId(),
                rootSnapshot.title,
                rootSnapshot.bomNodeId(),
                dirtyStateHandling = DirtyStateHandling.NONE,
            ).block()!!.getState()

        // set calculation dirty on a public variant
        setDirty(true)
        // assert dirty handling
        assertErrorThat(
            saveToSourceBranchService.saveToSourceBranch(
                accessCheck,
                currentState.snapshot.bomNodeId(),
                currentState.snapshot.branchId(),
                DirtyStateHandling.FAIL_ON_DIRTY,
            ),
        ).isInstanceOfSatisfying(ErrorCodedException::class.java) { error ->
            assertThat(error.errorCode).isEqualTo(ErrorCode.FAIL_ON_DIRTY)
        }
        this.currentState =
            saveToSourceBranchService.saveToSourceBranch(
                accessCheck,
                currentState.snapshot.bomNodeId(),
                currentState.snapshot.branchId(),
                DirtyStateHandling.RECALCULATE_ON_DIRTY,
            ).block()!!.getState()
    }

    @TestFactory
    fun `export tset file fails with dirty child loading`(): TestScenario =
        TestScenario(
            Step("start with dirty calculation") { createManualRootAndSub(createDirty = true) },
            Step("in the history, it is dirty") { checkHistory(expectDirty = true) },
            Step("export Tset file fails", this::exportTsetFileFails),
        )

    private fun exportTsetFileFails() {
        val (rootSnapshot, _, accessCheck) = this.currentState

        assertErrorThat(
            tsetFileExportService.exportTsetFile(
                accessCheck,
                projectId = rootSnapshot.projectId(),
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branch,
            ),
        ).isInstanceOfSatisfying(ErrorCodedException::class.java) { error ->
            assertThat(error.errorCode).isEqualTo(ErrorCode.FAIL_ON_DIRTY)
        }
    }

    private fun createManualRootAndSub(createDirty: Boolean): CalculationBuilderService.Result {
        // manual root with sub
        val tree =
            CompositeCalculationBuilder.create(CompositeCalculationBuilder.TreeType.MANUAL)
                .withDefaultProject(result.second).withSub(
                    CompositeCalculationBuilder.create(CompositeCalculationBuilder.TreeType.MANUAL),
                ).build()

        this.currentState = builderService.build(tree, result.first).block()!!
        setDirty(createDirty)
        return currentState
    }

    private fun setDirty(dirty: Boolean) {
        val (rootSnapshot, rootEntity, accessCheck) = this.currentState

        val bomEntry = rootEntity.findByEntityType(Entities.BOM_ENTRY)!!
        // use field update as initial action to control initial dirty state
        val updateResult =
            manufacturingCalculationService.setField(
                accessCheck,
                bomNodeId = rootSnapshot.bomNodeId(),
                branchId = rootSnapshot.branch,
                field =
                    InputParameter(
                        name = "quantity",
                        entityId = bomEntry.entityId,
                        type = "QuantityUnit",
                        value = 4.toBigDecimal(),
                    ),
                dirtyChildLoading = dirty,
            ).block()!!

        // FIXME: udpateResult should already contain the flag, but Mongo callback does not apply for the immediately returned object, so we need to fetch the node once more...
        val updatedSnapshot =
            bomNodeService.getBomNode(
                accessCheck,
                updateResult.newRootSnapshot.bomNodeId(),
                updateResult.newRootSnapshot.branch,
            ).block()!!

        assertThat(updatedSnapshot.manufacturing!!.getFieldResult(COST_PER_PART)).isNotNull
        assertThat(updatedSnapshot.dirtyChildLoading).isEqualTo(dirty)

        this.currentState =
            CalculationBuilderService.Result(
                updatedSnapshot,
                updatedSnapshot.manufacturing!!,
                accessCheck,
            )
    }

    private fun checkHistory(expectDirty: Boolean) {
        // assert history entry dirtyness
        assertHistory(
            currentState.accessCheck,
            snapshot = currentState.snapshot,
            expectDirty = expectDirty,
        )
    }

    fun assertDirtyOfTheCurrent(expectDirtyResult: Boolean) {
        assertThat(this.currentState.entity.getFieldResult(COST_PER_PART)).isNotNull
        assertThat(this.currentState.snapshot.dirtyChildLoading).isEqualTo(expectDirtyResult)
    }

    private fun CalculationResultWithSnapshot.getState(): CalculationBuilderService.Result = getState(this.result.bomNode.branch.id)

    private fun BomNodeDto.getState(): CalculationBuilderService.Result = getState(this.branch.id)

    private fun BranchDto.getState(): CalculationBuilderService.Result = getState(this.id)

    private fun getState(branchId: String): CalculationBuilderService.Result {
        val accessCheck = this.currentState.accessCheck
        val bomNodeId = this.currentState.snapshot.bomNodeId()
        val updatedSnapshot =
            bomNodeService.getBomNode(accessCheck, bomNodeId, createBranchId(branchId)).block()!!

        return CalculationBuilderService.Result(updatedSnapshot, updatedSnapshot.manufacturing!!, accessCheck)
    }

    private fun assertHistory(
        accessCheck: AccessCheck,
        snapshot: BomNodeSnapshot,
        expectDirty: Boolean,
    ) {
        val historyDto =
            historyService.getNodeHistoryForNode(
                accessCheck,
                projectId = snapshot.projectId().toHexString(),
                nodeId = snapshot.bomNodeId().toHexString(),
                branch = snapshot.branch?.toHexString(),
                null,
                null, // snapshot.changeset?.toHexString(),
                HISTORY_TYPE.ALL,
                includeChildren = false,
                includeParents = false,
                includePublish = false,
            ).block()!!.first()

        assertThat(historyDto.nodeChanges.first().dirtyChildLoading).isEqualTo(expectDirty)
    }

    private fun createElcoImportDto(count: Int): List<DataRowDto> {
        return IntRange(0, count - 1).map {
            DataRowDto(
                listOf(
                    DataFieldValueDTO("displayDesignation", "elco-$it"),
                    DataFieldValueDTO("quantity", "1"),
                    DataFieldValueDTO("pricePerUnit", "$it"),
                ),
            )
        }
    }
}
