package com.nu.bom.core.manufacturing.technologies.sand

import com.nu.bom.core.SpringCalculationServiceTestBase
import com.nu.bom.core.controller.InputParameter
import com.nu.bom.core.controller.ManufacturingParameters
import com.nu.bom.core.manufacturing.entities.BaseManufacturingFields
import com.nu.bom.core.manufacturing.fieldTypes.ConfigIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.CoreToPartRatio
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYearsUnit
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.NET_WEIGHT_PER_PART_WIZARD
import com.nu.bom.core.model.configurations.SemanticVersion
import com.nu.bom.core.model.toProjectId
import com.nu.bom.core.prediction.PredictionServiceMock
import com.nu.bom.core.technologies.manufacturings.sand.ManufacturingSandCasting
import com.nu.bom.core.utils.assertField
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class SandCastingExample5WithChildRecalculationTest : SpringCalculationServiceTestBase() {
    @Autowired
    lateinit var predictionService: PredictionServiceMock

    @BeforeEach
    fun init() {
        predictionService
            .clear()
            .withPrediction("sand", "SprueAndOverflowWeightPerCycle", 18.2542256641091)
    }

    @Test
    fun calculate() {
        val initialFields =
            mapOf<String, FieldResult<*, *>>(
                "lifeTime" to TimeInYears(5.toBigDecimal(), TimeInYearsUnit.YEAR),
                "location" to Text("tset.ref.classification.germany"),
                "peakUsableProductionVolumePerYear" to QuantityUnit(100000.toBigDecimal()),
                "averageUsableProductionVolumePerYear" to QuantityUnit(100000.toBigDecimal()),
                "materialName" to Text("GJS-500-RAW_MATERIAL_CASTING_ALLOY"),
                NET_WEIGHT_PER_PART_WIZARD to Weight(27.4.toBigDecimal(), WeightUnits.KILOGRAM),
                "partLength" to Length(0.6494.toBigDecimal(), LengthUnits.METER),
                "partWidth" to Length(0.456.toBigDecimal(), LengthUnits.METER),
                "partHeight" to Length(0.1765.toBigDecimal(), LengthUnits.METER),
                "hasCore" to SelectableBoolean.TRUE,
                "relativeCoreSize" to Rate(0.05.toBigDecimal()),
                "coreToPartRatio" to CoreToPartRatio(CoreToPartRatio.Selection.NORMAL_PACKAGE),
                "coreAssembly" to SelectableBoolean.FALSE,
                "shapeId" to Text("S_206"),
                "shapeTechnologyGroup" to Text("SAND_S3"),
                "burrFree" to SelectableBoolean.TRUE,
                "cleaningNeeded" to SelectableBoolean.FALSE,
                BaseManufacturingFields::costModuleConfigurationIdentifier.name to
                    ConfigIdentifier(
                        ConfigurationIdentifier.tset("SAND_CONFIGURATION_KEY", SemanticVersion.initialVersion()),
                    ),
            ).map {
                it.value.source = FieldResult.SOURCE.I
                it
            }.associateBy(
                { it.key },
                { it.value },
            )

        val creationResult =
            manufacturingCreationService
                .create(
                    accessCheck = accessCheck,
                    type = ManufacturingSandCasting::class,
                    name = ManufacturingSandCasting::class.simpleName!!,
                    args =
                        hashMapOf(
                            "partId" to partId.toString(),
                            "key" to "DE-1",
                            "number" to "1",
                            "isPart" to true,
                        ),
                    title = "TestTitle",
                    fields = initialFields,
                    projectId = projectId.toProjectId(),
                    year = 2019,
                ).block()!!
                .result
        val childId = BomNodeId(creationResult.bomNode.subNodes[0].bomNodeId)

        val childNode = bomNodeService.getBomNode(accessCheck, childId, branch = null).block()!!

        val calculationResult =
            manufacturingCalculationService
                .updateAndCalculate(
                    ManufacturingParameters(
                        bomNodeId = childId,
                        parameters =
                            listOf(
                                InputParameter(
                                    name = "location",
                                    type = "Text",
                                    entityId = childNode.manufacturing!!._id.toString(),
                                    value = "tset.ref.classification.hungary",
                                ),
                            ),
                    ),
                    accessCheck = accessCheck,
                    branchId = childNode.bomradBranchId(),
                ).block()!!
                .result
        assertResult(calculationResult.result, "ManufacturingCoreShooting", "productionCosts", 0.7803)

        childNode.manufacturing!!
            .findByEntityName("ManufacturingCoreShooting")!!
            .assertField("productionCosts", 0.9709.toBigDecimal(), roundingScale = 3)
    }
}
