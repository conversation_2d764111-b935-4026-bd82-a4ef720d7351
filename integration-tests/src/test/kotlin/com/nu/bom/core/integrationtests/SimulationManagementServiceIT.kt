package com.nu.bom.core.integrationtests

import com.nu.bom.core.CalculationTestBase
import com.nu.bom.core.api.dtos.AutocompleteResponse
import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.api.dtos.FieldsDto
import com.nu.bom.core.api.dtos.NodeSelectionCreationDto
import com.nu.bom.core.api.dtos.PaginatedResponse
import com.nu.bom.core.api.dtos.SimulationCreationDto
import com.nu.bom.core.api.dtos.SimulationDto
import com.nu.bom.core.api.dtos.SimulationListDto
import com.nu.bom.core.api.dtos.SimulationUpdateDto
import com.nu.bom.core.exception.userException.SimulationNotFoundException
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.testentities.TestManufacturing
import com.nu.bom.core.model.BomNode
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.SimulationId
import com.nu.bom.core.model.SnapshotId
import com.nu.bom.core.model.simulation.Status
import com.nu.bom.core.model.simulation.Type
import com.nu.bom.core.model.toBomNodeId
import com.nu.bom.core.mongoProjectId
import com.nu.bom.core.repository.SecureSimulationRepository
import com.nu.bom.core.repository.insecure.SimulationRepository
import com.nu.bom.core.service.BomNodeService
import com.nu.bom.core.service.PartService
import com.nu.bom.core.service.bomnode.BomNodeStrippedService
import com.nu.bom.core.service.simulation.PerformanceProfilerService
import com.nu.bom.core.service.simulation.SecureSimulationEntitySaver
import com.nu.bom.core.service.simulation.SimulationLoaderService
import com.nu.bom.core.service.simulation.SimulationManagementService
import com.nu.bom.core.service.simulation.insecure.SimulationEntitySaver
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.AccountTestUtil
import com.nu.bom.core.utils.AccountUtil
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.containsAny
import com.tset.core.service.RefKeyService
import org.bson.types.ObjectId
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.Mockito.anyCollection
import org.mockito.Mockito.anyList
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.PageRequest
import org.springframework.test.context.ActiveProfiles
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toFlux
import reactor.test.StepVerifier

@ActiveProfiles("test")
@SpringBootTest
class SimulationManagementServiceIT : CalculationTestBase() {
    @Autowired
    private lateinit var partService: PartService

    @Autowired
    private lateinit var accountUtil: AccountTestUtil

    @Autowired
    private lateinit var simulationRepository: SimulationRepository

    @Autowired
    private lateinit var autowiredEntityManager: EntityManager

    @Autowired
    private lateinit var simulationEntitySaver: SimulationEntitySaver

    @Autowired
    private lateinit var refKeyService: RefKeyService

    private lateinit var simulationLoaderService: SimulationLoaderService

    private lateinit var simulationManagementService: SimulationManagementService

    private lateinit var partId: ObjectId
    private var projectId: ProjectId = ProjectId()
    private var simulationId: SimulationId? = null

    private val accessibleBomNodeIds = listOf(BomNodeId(), BomNodeId(), BomNodeId())
    private val inaccessibleBomNodeIds = listOf(BomNodeId(), BomNodeId(), BomNodeId())

    private lateinit var accessCheck: AccessCheck

    @BeforeEach
    fun setup() {
        val (ac, project) = accountUtil.setupWithProject(name = "Test Project", key = "SMS")

        accessCheck = ac
        projectId = project.mongoProjectId()

        partId =
            partService.newPart(
                designation = "TestPart",
                number = "1234",
                accessCheck = accessCheck,
                projectId = projectId,
            )
                .mapNotNull { it._id }
                .block()!!

        simulationRepository.deleteAll().block()

        simulationLoaderService = mock(SimulationLoaderService::class.java)

        val bomNodeStrippedService = mock(BomNodeStrippedService::class.java)

        `when`(
            bomNodeStrippedService.getBranchState(
                any(),
                any(),
                anyOrNull(),
                anyOrNull(),
            ),
        ).thenAnswer { invocation ->
            val nodeId = invocation.arguments[1] as BomNodeId
            val branchId = invocation.arguments[2] as BranchId?
            val snapshotId = invocation.arguments[3] as SnapshotId?
            Mono.just(
                BomNodeStrippedService.BranchState(
                    nodeId = nodeId,
                    branchId = branchId,
                    snapshotId = snapshotId,
                    exists = true,
                    main = false,
                    latest = true,
                    partName = "PartyPart",
                    snapshotTitle = "Title XYZ",
                ),
            )
        }

        val bomNodeService = mock(BomNodeService::class.java)

        `when`(bomNodeService.getBomNode(any(), any(), anyOrNull(), anyOrNull(), any())).thenAnswer {
            val bomNode = BomNode(name = "test", year = 2000, projectId = ObjectId.get())
            bomNode._id = ObjectId.get()

            Mono.just(
                bomNode.createSnapshot(
                    title = "test title",
                    manufacturing =
                        addObject(
                            name = "Test Manufacturing",
                            clazz = TestManufacturing::class.java,
                            overrides =
                                mapOf(
                                    "dimension" to Dimension.getDefault(Entities.MANUFACTURING)!!,
                                ),
                        ),
                    accountId = accessCheck.asAccountId(),
                ),
            )
        }

        `when`(bomNodeService.nodeExists(any(), anyList(), any())).thenAnswer {
            val bomNodes = it.arguments[1] as List<BomNodeId>
            if (inaccessibleBomNodeIds.containsAny(bomNodes)) {
                Mono.just(false)
            } else {
                Mono.just(true)
            }
        }

        `when`(bomNodeService.filterAccessibleBomNodeIds(any(), anyCollection())).thenAnswer {
            val bomNodeIds = it.arguments[1] as Collection<BomNodeId>
            bomNodeIds.filterNot { id -> inaccessibleBomNodeIds.contains(id) }.map { id -> id.toBomNodeId() }.toFlux()
        }

        `when`(
            simulationLoaderService.calculate(
                any(),
                any(),
                any(),
                any(),
                any(),
            ),
        ).thenAnswer { _ ->
            createMono()
        }

        val secureSimulationRepository = SecureSimulationRepository(simulationRepository, bomNodeService)
        val secureSimulationEntitySaver = SecureSimulationEntitySaver(simulationEntitySaver, bomNodeService)

        simulationManagementService =
            SimulationManagementService(
                secureSimulationRepository,
                simulationLoaderService,
                secureSimulationEntitySaver,
                bomNodeStrippedService,
                bomNodeService,
                userService,
                autowiredEntityManager,
                refKeyService,
            )
    }

    @AfterEach
    fun cleanup() {
        accountUtil.cleanup()
    }

    private fun createMono(): Mono<PerformanceProfilerService.AggregatedResult> {
        return Mono.just(
            PerformanceProfilerService.AggregatedResult(
                partName = "Part 123",
                snapshotTitle = "SnapshotTitle",
                snapshotId = SnapshotId(),
                elapsedMs = 100,
                successCount = 10,
                errorCount = 0,
                results =
                    listOf(
                        PerformanceProfilerService.CalculationResultExtract(
                            value = 5.toBigDecimal(),
                            elapsedMs = 50,
                            costPerPart = FieldParameter(),
                        ),
                        PerformanceProfilerService.CalculationResultExtract(
                            value = 10.toBigDecimal(),
                            elapsedMs = 50,
                            costPerPart = FieldParameter(),
                        ),
                    ),
            ),
        )
    }

    @ParameterizedTest
    @MethodSource("simulationParameters")
    fun testSimulationCreation(
        fieldsDto: FieldsDto,
        success: Boolean,
    ) {
        val bomNodeId = BomNodeId()
        val bomNodeId2 = BomNodeId()
        val branchId = BranchId()
        val branchId2 = BranchId()

        val simulation =
            try {
                val simulation = createSimulationMono(fieldsDto, bomNodeId, branchId, bomNodeId2, branchId2).block()
                if (success) {
                    Assertions.assertNotNull(simulation, "simulation exists")
                } else {
                    fail("Expected to fail with $fieldsDto")
                }
                simulation!!
            } catch (e: Exception) {
                if (!success) {
                    Assertions.assertNotNull(e, "expected to fail with $fieldsDto")
                    return
                } else {
                    throw e
                }
            }

        Assertions.assertEquals(Status.CREATED, simulation.status, "simulation exists")
        Assertions.assertEquals(SIMULATION_NAME, simulation.name, "simulation name is correct")
        Assertions.assertEquals(2, simulation.nodeSelection.size, "node selection is 2")
        Assertions.assertEquals(bomNodeId.toHexString(), simulation.nodeSelection[0].bomNodeId, "bomNodeId 1")
        Assertions.assertEquals(bomNodeId2.toHexString(), simulation.nodeSelection[1].bomNodeId, "bomNodeId 2")
        Assertions.assertEquals(branchId.toHexString(), simulation.nodeSelection[0].branchId, "branchId 1")
        Assertions.assertEquals(branchId2.toHexString(), simulation.nodeSelection[1].branchId, "branchId 2")
        Assertions.assertEquals("PartyPart", simulation.nodeSelection[0].partName, "partName 1")
        Assertions.assertEquals("PartyPart", simulation.nodeSelection[1].partName, "partName 2")
        Assertions.assertEquals(true, simulation.nodeSelection[0].latest, "latest 1")
        Assertions.assertEquals(true, simulation.nodeSelection[1].latest, "latest 2")
        Assertions.assertEquals(false, simulation.nodeSelection[0].main, "main 1")
        Assertions.assertEquals(false, simulation.nodeSelection[1].main, "main 2")
        Assertions.assertEquals("Title XYZ", simulation.nodeSelection[0].title, "branchName 1")
        Assertions.assertEquals("Title XYZ", simulation.nodeSelection[1].title, "branchName 2")
        Assertions.assertNotNull(simulation.id, "simulation.id exists")
        val savedSimulation = simulationRepository.findById(SimulationId(simulation.id)).block()

        Assertions.assertNotNull(savedSimulation, "simulation exists")
        Assertions.assertEquals(SIMULATION_NAME, savedSimulation!!.name, "simulation exists")
        Assertions.assertEquals(false, savedSimulation.deleted, "simulation not deleted")
        Assertions.assertEquals(2, savedSimulation.nodes.size, "node selection is 2")
        val actualBomNodeIds = savedSimulation.nodes.map { it.bomNodeId }.toSet()
        val actualBranchIds = savedSimulation.nodes.map { it.branchId }.toSet()
        Assertions.assertEquals(setOf(bomNodeId, bomNodeId2), actualBomNodeIds, "savedSimulation bomNodeIds")
        Assertions.assertEquals(setOf(branchId, branchId2), actualBranchIds, "savedSimulation branchId")

        Assertions.assertEquals(
            branchId,
            savedSimulation.nodes.find { it.bomNodeId == bomNodeId }?.branchId,
            "savedSimulation branchId 1",
        )
        Assertions.assertEquals(
            branchId2,
            savedSimulation.nodes.find { it.bomNodeId == bomNodeId2 }?.branchId,
            "savedSimulation branchId 2",
        )
        simulationId = savedSimulation._id!!
    }

    @Test
    fun updateName() {
        val simulationId = createSimulation()

        val result =
            simulationManagementService.update(accessCheck, simulationId, SimulationUpdateDto(name = "new name"))
                .block()

        Assertions.assertNotNull(result, "simulationDto is not null")
        Assertions.assertEquals(simulationId.toHexString(), result!!.id, "simulationDto.id")
        Assertions.assertEquals("new name", result.name, "simulationDto.name")

        val savedSimulation = simulationRepository.findById(simulationId).block()
        Assertions.assertNotNull(savedSimulation, "savedSimulation is not null")
        Assertions.assertEquals(simulationId, savedSimulation!!._id, "savedSimulation.id")
        Assertions.assertEquals("new name", savedSimulation.name, "savedSimulation.id")
    }

    @Test
    fun testCopy() {
        val simulationId = createSimulation()

        val copied = simulationManagementService.copy(accessCheck, simulationId).block()
        Assertions.assertNotNull(copied, "simulationCreationDto")
        Assertions.assertEquals(false, copied!!.calculationRemoved, "simulationCreationDto.calculationRemoved")
        Assertions.assertEquals(SIMULATION_NAME, copied.name, "simulationCreationDto.calculationRemoved")
    }

    @Test
    fun testSearchByName() {
        simulationId = createOnePublicAndTwoPrivateSimulations()

        run {
            val updatedList = simulationManagementService.list(accessCheck, PageRequest.of(0, 1), "first").block()
            checkSimulationList(
                "list where simulationName contains 'first'",
                updatedList,
                1,
                id = simulationId,
                simulationName = SIMULATION_NAME,
            )
        }

        run {
            val updatedList = simulationManagementService.list(accessCheck, PageRequest.of(0, 1), "xxxx").block()
            checkSimulationList("list where simulationName contains 'xxxx'", updatedList, 0)
        }

        run {
            val updatedList = simulationManagementService.list(accessCheck, PageRequest.of(0, 1), "FIRST").block()
            checkSimulationList(
                "list where simulationName contains 'FIRST'",
                updatedList,
                1,
                id = simulationId,
                simulationName = SIMULATION_NAME,
            )
        }

        run {
            val updatedList = simulationManagementService.list(accessCheck, PageRequest.of(0, 1), "    ").block()
            checkSimulationList(
                "list when name filter is just whitespace '    '",
                updatedList,
                1,
                id = simulationId,
                simulationName = SIMULATION_NAME,
            )
        }

        run {
            val updatedList = simulationManagementService.list(accessCheck, PageRequest.of(0, 1), "st SIM").block()
            checkSimulationList(
                "list where simulationName contains 'st sim'",
                updatedList,
                1,
                id = simulationId,
                simulationName = SIMULATION_NAME,
            )
        }

        run {
            val updatedList =
                simulationManagementService.list(accessCheck, PageRequest.of(0, 1), "simulation first").block()
            checkSimulationList("list where simulationName contains 'simulation first'", updatedList, 0)
        }
    }

    private fun createOnePublicAndTwoPrivateSimulations(): SimulationId {
        val publicSim =
            createSimulationMono(
                FieldsDto(
                    name = "peakUsableProductionVolumePerYear",
                    type = Type.NUMERIC,
                    minimum = 10.toBigDecimal(),
                    maximum = 100.toBigDecimal(),
                ),
                accessibleBomNodeIds[0],
                BranchId(),
                accessibleBomNodeIds[1],
                BranchId(),
            ).block()

        createSimulationMono(
            FieldsDto(
                name = "peakUsableProductionVolumePerYear",
                type = Type.NUMERIC,
                minimum = 10.toBigDecimal(),
                maximum = 100.toBigDecimal(),
            ),
            inaccessibleBomNodeIds[0],
            BranchId(),
            inaccessibleBomNodeIds[1],
            BranchId(),
        ).block()

        createSimulationMono(
            FieldsDto(
                name = "peakUsableProductionVolumePerYear",
                type = Type.NUMERIC,
                minimum = 10.toBigDecimal(),
                maximum = 100.toBigDecimal(),
            ),
            inaccessibleBomNodeIds[0],
            BranchId(),
            accessibleBomNodeIds[1],
            BranchId(),
        ).block()
        return SimulationId(publicSim?.id)
    }

    @Test
    fun testSimulationListings() {
        // check that there is no simulation
        checkNoSimulationExists()

        val simulationId = createOnePublicAndTwoPrivateSimulations()

        run {
            val result = simulationManagementService.list(accessCheck, page = PageRequest.of(0, 1)).block()
            checkSimulationList("find-one", result, 1, id = simulationId, simulationName = SIMULATION_NAME)
        }

        run {
            // check that paging works
            val result = simulationManagementService.list(accessCheck, page = PageRequest.of(1, 1)).block()
            checkSimulationList("find-nothing-on-page-one", result, 0, totalCount = 1)
        }

        // check that other accounts can't see it
        run {
            val otherAccountResult =
                simulationManagementService.list(AccountUtil.dummyAccessCheck(), page = PageRequest.of(0, 1))
                    .block()
            checkSimulationList("otherAccountResult", otherAccountResult, 0)
        }
    }

    private fun checkNoSimulationExists() {
        val initial = simulationManagementService.list(accessCheck, page = PageRequest.of(0, 1)).block()
        checkSimulationList("initial", initial, 0)
    }

    private fun checkSimulationList(
        name: String,
        result: PaginatedResponse<SimulationListDto>?,
        numberOfResults: Int,
        totalCount: Long = numberOfResults.toLong(),
        id: SimulationId? = null,
        simulationName: String? = null,
    ) {
        Assertions.assertNotNull(result, "paginatedResponse[$name]")
        if (numberOfResults == 0) {
            Assertions.assertEquals(totalCount, result!!.totalCount, "paginatedResponse[$name].totalCount")
            Assertions.assertEquals(0, result.currentPage.size, "paginatedResponse[$name].currentPage.size ( $result )")
        } else {
            Assertions.assertEquals(
                totalCount,
                result!!.totalCount,
                "paginatedResponse[$name].totalCount",
            )
            Assertions.assertEquals(
                numberOfResults,
                result.currentPage.size,
                "paginatedResponse[$name].currentPage.size",
            )
            Assertions.assertEquals(
                id?.toHexString(),
                result.currentPage[0].id,
                "paginatedResponse[$name].currentPage[0].id",
            )
            Assertions.assertEquals(
                simulationName,
                result.currentPage[0].name,
                "paginatedResponse[$name].currentPage[0].name",
            )
        }
    }

    @Test
    fun testSimulationDeletion() {
        checkNoSimulationExists()

        val simulationId = createSimulation()

        run {
            StepVerifier.create(simulationManagementService.delete(AccountUtil.dummyAccessCheck(), simulationId))
                .expectErrorMatches { it is SimulationNotFoundException }
                .verify()
        }
        run {
            val deletion = simulationManagementService.delete(accessCheck, simulationId).block()
            Assertions.assertNotNull(deletion, "found with correct account")
        }
        run {
            // check that the db still has the entity, but in a deleted state
            val simulation = simulationRepository.findById(simulationId).block()
            Assertions.assertNotNull(simulation, "entity exists")
            Assertions.assertTrue(simulation!!.deleted, "entity.deleted")
        }
        run {
            // check that the API doesn't know about it
            StepVerifier.create(simulationManagementService.delete(accessCheck, simulationId))
                .expectErrorMatches { it is SimulationNotFoundException }
                .verify()
        }
        run {
            val result = simulationManagementService.list(accessCheck, page = PageRequest.of(0, 1)).block()
            checkSimulationList("deletedList", result, 0)
        }
    }

    private fun createSimulation(): SimulationId {
        testSimulationCreation(
            FieldsDto(
                name = "peakUsableProductionVolumePerYear",
                type = Type.NUMERIC,
                minimum = 10.toBigDecimal(),
                maximum = 100.toBigDecimal(),
            ),
            true,
        )
        return simulationId!!
    }

    private fun createSimulationMono(
        fieldsDto: FieldsDto,
        bomNodeId: BomNodeId,
        branchId: BranchId,
        bomNodeId2: BomNodeId,
        branchId2: BranchId,
    ): Mono<SimulationDto> {
        return simulationManagementService.create(
            accessCheck,
            SimulationCreationDto(
                name = SIMULATION_NAME,
                fields = fieldsDto,
                calculationRemoved = false,
                nodeSelection =
                    listOf(
                        NodeSelectionCreationDto(
                            bomNodeId = bomNodeId.toHexString(),
                            branchId = branchId.toHexString(),
                        ),
                        NodeSelectionCreationDto(
                            bomNodeId = bomNodeId2.toHexString(),
                            branchId = branchId2.toHexString(),
                        ),
                    ),
            ),
        )
    }

    companion object {
        const val SIMULATION_NAME = "first simulation"

        @JvmStatic
        fun simulationParameters() =
            listOf(
                arrayOf(
                    FieldsDto(
                        name = "peakUsableProductionVolumePerYear",
                        type = Type.NUMERIC,
                        minimum = 10.toBigDecimal(),
                        maximum = 100.toBigDecimal(),
                    ),
                    true,
                ),
                arrayOf(
                    FieldsDto(
                        name = "notExistingField",
                        type = Type.NUMERIC,
                        minimum = 10.toBigDecimal(),
                        maximum = 100.toBigDecimal(),
                    ),
                    false,
                ),
                arrayOf(
                    FieldsDto(name = "peakUsableProductionVolumePerYear", type = Type.NUMERIC, maximum = 100.toBigDecimal()),
                    false,
                ),
                arrayOf(
                    FieldsDto(name = "peakUsableProductionVolumePerYear", type = Type.NUMERIC, minimum = 10.toBigDecimal()),
                    false,
                ),
                arrayOf(FieldsDto(name = "peakUsableProductionVolumePerYear", type = Type.SELECTION), false),
                arrayOf(FieldsDto(name = "location", type = Type.SELECTION, values = emptyList()), false),
                arrayOf(
                    FieldsDto(
                        name = "location",
                        type = Type.SELECTION,
                        values = listOf(AutocompleteResponse(key = "nowhere", name = "Nowhere", section = null)),
                    ),
                    true,
                ),
            )
    }
}
