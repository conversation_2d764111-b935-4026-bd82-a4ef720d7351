package com.nu.bom.core.integrationtests.entitycreation

import com.nu.bom.core.api.ManufacturingUpdateController
import com.nu.bom.core.api.dtos.BomNodeDto
import com.nu.bom.core.api.dtos.EntityCreationDto
import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.api.dtos.ManufacturingDto
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.model.MasterData
import com.nu.bom.core.model.MasterDataSelector
import com.nu.bom.core.model.MergeSourceType
import com.nu.bom.core.model.PartId
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.mongoProjectId
import com.nu.bom.core.service.MasterDataService
import com.nu.bom.core.service.PartService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.NbkClient
import com.tset.bom.clients.nuledge.FieldParameterDto
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

private const val SELECTED_TOOL = "selectedTool"
private const val TOOL_DIAMETER = "toolDiameter"
private const val FEED_RATE_PER_TOOTH = "feedRatePerTooth"
private const val NUMBER_OF_TEETH = "numberOfTeeth"
private const val NUMBER_OF_BLADES = "numberOfBlades"
private const val MAX_CUTTING_DEPTH = "maxCuttingDepth"
private const val CUTTING_SPEED = "cuttingSpeed"
private const val TIME = "time"
private const val MACHINING_TYPE = "machiningType"
private const val TYPE = "type"
private const val MATERIAL_NAME = "materialName"
private const val DEPTH = "depth"
private const val LENGTH = "length"
private const val WIDTH = "width"
private const val GROUP = "group"
private const val MATERIAL_GROUP = "materialGroup"

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test", "masterdata-init", "config-init")
@AutoConfigureWebTestClient
class CycleTimeStepCreationIT : NbkClient.With {
    private lateinit var partId: PartId
    private lateinit var access: AccessCheck
    private lateinit var projectId: ProjectId

    @Autowired
    private lateinit var partService: PartService

    @Autowired
    override lateinit var nbkClient: NbkClient

    @Autowired
    private lateinit var masterDataService: MasterDataService

    @BeforeEach
    fun setup() {
        val result = nbkClient.setupWithProject("CycleTimeStepCreationIT", "CTSCIT")
        access = result.first
        projectId = result.second.mongoProjectId()

        partId =
            partService.newPart(
                designation = "TestPart",
                number = "1234",
                accessCheck = access,
                projectId = projectId,
            ).block()!!._id!!
    }

    @AfterEach
    fun cleanup() {
        nbkClient.cleanup()
    }

    @Test
    fun `create simple line milling step following the modal flow without overriden fields`() {
        createAndPublishCalcWithGroup().let { (node, group) ->
            val fields = doModalInteraction(node, group, actions = simpleLineMillingActions())
            addCycleTimeStepAndPublish(
                node,
                group,
                fields,
            )
        }.also { (_, cycleTimeStep) ->
            cycleTimeStep.validateFields(
                listOf(
                    FieldToValidate(SELECTED_TOOL, "shouldermill_1628"),
                    FieldToValidate(TOOL_DIAMETER, 0.012),
                    FieldToValidate(FEED_RATE_PER_TOOTH, 0.1, isDefaultUnits = true),
                    FieldToValidate(NUMBER_OF_TEETH, 4),
                    FieldToValidate(MAX_CUTTING_DEPTH, 0.019),
                    FieldToValidate(CUTTING_SPEED, 222.0),
                    FieldToValidate(TIME, 1.000254724),
                ),
            )
        }
    }

    @Test
    fun `create simple line milling change selected tool with defaults`() {
        val (node, group) = createAndPublishCalcWithGroup()
        doModalInteraction(
            node,
            group,
            actions = simpleLineMillingActions() + getAction(SELECTED_TOOL, "shouldermill_1612"),
        ).let { fields ->
            addCycleTimeStepAndPublish(node, group, fields)
        }.also { (_, cycleTimeStep) ->
            cycleTimeStep.validateFields(
                listOf(
                    FieldToValidate(SELECTED_TOOL, "shouldermill_1612"),
                    FieldToValidate(TOOL_DIAMETER, 0.08),
                    FieldToValidate(FEED_RATE_PER_TOOTH, 0.25, isDefaultUnits = true),
                    FieldToValidate(NUMBER_OF_TEETH, 7),
                    FieldToValidate(MAX_CUTTING_DEPTH, 0.013),
                    FieldToValidate(CUTTING_SPEED, 290.0),
                    FieldToValidate(TIME, 1.000297138),
                ),
            )
        }
    }

    @Test
    fun `create simple line milling change defaults`() {
        val (node, group) = createAndPublishCalcWithGroup()
        val fields =
            doModalInteraction(
                node,
                group,
                actions = simpleLineMillingActions() + getAction(TOOL_DIAMETER, 0.021) + getAction(NUMBER_OF_TEETH, 12),
            ).also {
                it.validateFields(
                    listOf(
                        FieldToValidate(TOOL_DIAMETER, 0.021, 0.012),
                        FieldToValidate(NUMBER_OF_TEETH, 12, 4),
                    ),
                )
            }
        addCycleTimeStepAndPublish(node, group, fields).also { (_, cycleTimeStep) ->
            cycleTimeStep.validateFields(
                listOf(
                    FieldToValidate(SELECTED_TOOL, "shouldermill_1628"),
                    FieldToValidate(TOOL_DIAMETER, 0.021, 0.012),
                    FieldToValidate(FEED_RATE_PER_TOOTH, 0.1, isDefaultUnits = true),
                    FieldToValidate(NUMBER_OF_TEETH, 12, 4),
                    FieldToValidate(MAX_CUTTING_DEPTH, 0.019),
                    FieldToValidate(CUTTING_SPEED, 222.0),
                    FieldToValidate(TIME, 1.00014859),
                ),
            )
        }
    }

    @Test
    fun `create face milling change fields that trigger tool update`() {
        val (node, group) = createAndPublishCalcWithGroup()
        val fields =
            doModalInteraction(
                node,
                group,
                actions = faceMillingActions(),
            ).also {
                it.validateFields(
                    listOf(
                        FieldToValidate(SELECTED_TOOL, "facemill_76"),
                        FieldToValidate(TOOL_DIAMETER, 3.0),
                        FieldToValidate(NUMBER_OF_TEETH, 4),
                        FieldToValidate(CUTTING_SPEED, 222.0),
                    ),
                )
            }
        applyActions(node, group, Entities.CYCLETIME_STEP, fields, listOf(getAction(WIDTH, 5.5))).also {
            it.validateFields(
                listOf(
                    FieldToValidate(SELECTED_TOOL, "facemill_80"),
                    FieldToValidate(TOOL_DIAMETER, 8.0),
                    FieldToValidate(NUMBER_OF_TEETH, 4),
                    FieldToValidate(CUTTING_SPEED, 222.0),
                ),
            )
        }.let { newFields ->
            addCycleTimeStepAndPublish(node, group, newFields)
        }.also { (_, cycleTimeStep) ->
            cycleTimeStep.validateFields(
                listOf(
                    FieldToValidate(SELECTED_TOOL, "facemill_80"),
                    FieldToValidate(TOOL_DIAMETER, 0.008),
                    FieldToValidate(FEED_RATE_PER_TOOTH, 0.07, isDefaultUnits = true),
                    FieldToValidate(MAX_CUTTING_DEPTH, 6.0, isDefaultUnits = true),
                    FieldToValidate(NUMBER_OF_TEETH, 4),
                    FieldToValidate(CUTTING_SPEED, 222.0),
                    FieldToValidate(TIME, 1.000242592),
                ),
            )
        }
    }

    @Test
    fun `create face milling change tool check masterdata selector updated`() {
        val (node, group) = createAndPublishCalcWithGroup()
        val fields =
            doModalInteraction(
                node,
                group,
                actions = faceMillingActions(),
            )
        val (updateNode, cycleTimeStep) = addCycleTimeStepAndPublish(node, group, fields)
        assertEquals(MasterDataType.TOOL_FACE_MILLING, cycleTimeStep.masterDataKey?.type)
        assertEquals("facemill_76", cycleTimeStep.masterDataKey?.key)
        cycleTimeStep.validateFields(
            listOf(
                FieldToValidate(SELECTED_TOOL, "facemill_76"),
                FieldToValidate(TOOL_DIAMETER, 3.0, isDefaultUnits = true),
                FieldToValidate(NUMBER_OF_TEETH, 4),
                FieldToValidate(CUTTING_SPEED, 222.0),
            ),
        )
        val updateFieldRequest = getUpdatedField(cycleTimeStep, SELECTED_TOOL, "facemill_80")
        manufacturingCrudTestClient.updateField(
            bomNodeId = updateNode.id,
            branch = updateNode.branch.id,
            field = updateFieldRequest,
        ).let {
            it.findInTree { entity -> entity.name == "cycleTimeStep" }!!
        }.let {
            assertEquals(MasterDataType.TOOL_FACE_MILLING, it.masterDataKey?.type)
            assertEquals("facemill_80", it.masterDataKey?.key)
        }
    }

    @Test
    fun `face milling change width updates tool`() {
        val (node, group) = createAndPublishCalcWithGroup()
        val fields =
            doModalInteraction(
                node,
                group,
                actions = faceMillingActions() + getAction(NUMBER_OF_TEETH, 12),
            )
        val (updateNode, cycleTimeStep) = addCycleTimeStepAndPublish(node, group, fields)
        cycleTimeStep.validateFields(
            listOf(
                FieldToValidate(SELECTED_TOOL, "facemill_76"),
                FieldToValidate(TOOL_DIAMETER, 3.0, isDefaultUnits = true),
                FieldToValidate(NUMBER_OF_TEETH, 12, 4),
                FieldToValidate(CUTTING_SPEED, 222.0),
            ),
        )
        val updateFieldRequest = getUpdatedField(cycleTimeStep, WIDTH, 99.9)
        manufacturingCrudTestClient.updateField(
            bomNodeId = updateNode.id,
            branch = updateNode.branch.id,
            field = updateFieldRequest,
        ).let {
            it.findInTree { entity -> entity.name == "cycleTimeStep" }!!
        }.validateFields(
            listOf(
                FieldToValidate(SELECTED_TOOL, "facemill_61"),
                FieldToValidate(NUMBER_OF_TEETH, 12, 16),
                FieldToValidate(CUTTING_SPEED, 290.0),
            ),
        )
    }

    @Test
    fun `face milling masterdata update re-evaluates fields`() {
        val (node, group) = createAndPublishCalcWithGroup()
        val fields =
            doModalInteraction(
                node,
                group,
                actions = faceMillingActions() + getAction(NUMBER_OF_TEETH, 12),
            )
        val (updateNode, cycleTimeStep) = addCycleTimeStepAndPublish(node, group, fields)
        cycleTimeStep.validateFields(
            listOf(
                FieldToValidate(SELECTED_TOOL, "facemill_76"),
                FieldToValidate(TOOL_DIAMETER, 3.0, isDefaultUnits = true),
                FieldToValidate(NUMBER_OF_TEETH, 12, 4),
            ),
        )
        // 2 - update tool information
        val (toolMasterData, updateToolMasterdata) = updateToolMasterdata()
        // assert pending masterdata updates
        var bomNode = assertPendingUpdates(updateNode)
        // update masterdata and get latest changes
        bomNode = manufacturingCrudTestClient.updateMasterData(bomNode)
        bomNode.findInTree { entity -> entity.name == "cycleTimeStep" }!!.also {
            it.validateFields(
                listOf(
                    FieldToValidate(SELECTED_TOOL, "facemill_76"),
                    FieldToValidate(NUMBER_OF_TEETH, 12, 24),
                    FieldToValidate(TOOL_DIAMETER, 8.0, isDefaultUnits = true),
                ),
            )
        }
        // revert changes
        masterDataService.saveGlobalMasterData(toolMasterData.copy(version = updateToolMasterdata.version + 1))
            .block()!!
        // assert pending masterdata updates
        bomNode = assertPendingUpdates(bomNode)
        // update masterdata and get latest changes
        bomNode = manufacturingCrudTestClient.updateMasterData(bomNode)
        bomNode.findInTree { entity -> entity.name == "cycleTimeStep" }!!.also {
            it.validateFields(
                listOf(
                    FieldToValidate(SELECTED_TOOL, "facemill_76"),
                    FieldToValidate(NUMBER_OF_TEETH, 12, 4),
                    FieldToValidate(TOOL_DIAMETER, 3.0, isDefaultUnits = true),
                ),
            )
        }
    }

    private fun updateToolMasterdata(): Pair<MasterData, MasterData> {
        val toolMasterData =
            masterDataService.getLatestMasterDataByCompositeKey(
                access,
                selector = MasterDataSelector(MasterDataType.TOOL_FACE_MILLING, "facemill_76"),
            ).block()!!
        val newValues =
            mapOf(
                NUMBER_OF_BLADES to Num(24),
                TOOL_DIAMETER to Length(8.0, LengthUnits.MILLIMETER),
            )
        val updateToolMasterdata =
            toolMasterData.copy(
                data = toolMasterData.data.filter { it.key !in listOf(NUMBER_OF_BLADES, TOOL_DIAMETER) } + newValues,
                version = toolMasterData.version + 1,
            )
        masterDataService.saveGlobalMasterData(updateToolMasterdata).block()!!
        return Pair(toolMasterData, updateToolMasterdata)
    }

    private fun assertPendingUpdates(bomNode: BomNodeDto): BomNodeDto {
        val updateNode =
            manufacturingCrudTestClient.getBomNode(
                bomNode.id,
                bomNode.branch.id,
                showOpenMerges = true,
            )!!
        assertThat(updateNode.openMergesAvailable).singleElement().satisfies({ mergeSourceType ->
            assertThat(mergeSourceType).isEqualTo(MergeSourceType.MASTERDATA)
        })
        return updateNode
    }

    @Test
    fun `verify group creation flow`() {
        val (node, step) = createCalcWithStep()
        var fields =
            doModalInteraction(
                node,
                step,
                parentType = Entities.MANUFACTURING_STEP.name,
                entityType = Entities.CYCLETIME_STEP_GROUP,
                entityClass = "CycleTimeStepGroup",
                actions = emptyList(),
            ).also {
                // Validate initial values
                it.validateFields(
                    listOf(
                        FieldToValidate("grouping", "SEQUENTIAL"),
                        FieldToValidate(TYPE, "PROCESSING"),
                    ),
                )
            }
        // Mock interactions with modal
        fields =
            applyActions(
                node,
                step,
                Entities.CYCLETIME_STEP_GROUP,
                fields,
                listOf(getAction(TYPE, "MACHINING")),
            ).also { newFields ->
                newFields.validateFields(listOf(FieldToValidate(TYPE, "MACHINING")))
                assertNull(newFields.find { it.name == MATERIAL_NAME }!!.value)
            }
        applyActions(
            node,
            step,
            Entities.CYCLETIME_STEP_GROUP,
            fields,
            listOf(getAction(MATERIAL_NAME, "null")),
        ).let { newFields ->
            // Push changes and validate final result
            manufacturingCrudTestClient.addCycleTimeStepGroup(
                bomNodeId = node.id,
                branchId = node.branch.id,
                parentId = step.id,
                name = GROUP,
                fields =
                    newFields.map {
                        if (it.name == MATERIAL_GROUP) {
                            it.copy(value = "P7")
                        } else {
                            it
                        }.toFieldParameterDto()
                    },
            )
        }.let {
            it.findInTree { entity -> entity.name == GROUP }!!
        }.validateFields(
            listOf(
                FieldToValidate(TYPE, "MACHINING"),
                FieldToValidate(MATERIAL_GROUP, "P7"),
            ),
        )
    }

    @Test
    fun `verify cycle time creation flow`() {
        val (node, group) = createAndPublishCalcWithGroup()
        var fields =
            doModalInteraction(
                node,
                group,
                actions = emptyList(),
            ).also { newFields ->
                // Validate initial values
                assertEquals(4, newFields.size, "Initial values without user interaction")
            }
        // Interact with modal
        fields =
            applyActions(
                node,
                group,
                Entities.CYCLETIME_STEP,
                fields,
                listOf(getAction(MACHINING_TYPE, "MILLING"), getAction("cycleTimeStep", "ManualFaceMilling")),
            ).also { newFields ->
                assertEquals(21, newFields.size, "Manual face milling fields once machining type is selected")
                assertNull(newFields.find { it.name == LENGTH }!!.value)
                assertNull(newFields.find { it.name == WIDTH }!!.value)
                assertNull(newFields.find { it.name == SELECTED_TOOL }!!.value)
            }
        // Providing the width would trigger loading of tools
        fields =
            applyActions(
                node,
                group,
                Entities.CYCLETIME_STEP,
                fields,
                listOf(getAction(WIDTH, 14.0)),
            ).also { newFields ->
                newFields.validateFields(
                    listOf(
                        FieldToValidate(TOOL_DIAMETER, 18.0),
                        FieldToValidate(SELECTED_TOOL, "facemill_85"),
                        FieldToValidate(NUMBER_OF_TEETH, 4),
                        FieldToValidate(CUTTING_SPEED, 222.0),
                    ),
                )
                assertNull(newFields.find { it.name == DEPTH }!!.value)
                assertNull(newFields.find { it.name == TIME }!!.value)
            }
        // Set with would trigger finding a new tool and populating fields
        fields =
            applyActions(
                node,
                group,
                Entities.CYCLETIME_STEP,
                fields,
                listOf(getAction(WIDTH, 28.0)),
            ).also { newFields ->
                newFields.validateFields(
                    listOf(
                        FieldToValidate(TOOL_DIAMETER, 40.0),
                        FieldToValidate(SELECTED_TOOL, "facemill_1"),
                        FieldToValidate(NUMBER_OF_TEETH, 4),
                        FieldToValidate(CUTTING_SPEED, 290.0),
                    ),
                )
                assertNull(newFields.find { it.name == TIME }!!.value)
            }
        // Populate all missing fields to get time calculated
        applyActions(
            node,
            group,
            Entities.CYCLETIME_STEP,
            fields,
            listOf(getAction(LENGTH, 12.0), getAction(DEPTH, 12.0)),
        ).validateFields(
            listOf(
                FieldToValidate(TIME, 2.07555257324),
            ),
        )
    }

    private fun ManufacturingDto.validateFields(fields: List<FieldToValidate>) {
        fields.forEach { fieldToValidate ->
            getField(fieldToValidate.fieldName).also {
                validateField(it, fieldToValidate)
            }
        }
    }

    private fun List<FieldParameter>.validateFields(fieldsToValidate: List<FieldToValidate>) {
        fieldsToValidate.forEach { fieldToValidate ->
            find { it.name == fieldToValidate.fieldName }!!.also {
                validateField(it, fieldToValidate)
            }
        }
    }

    private fun validateField(
        field: FieldParameter,
        fieldToValidate: FieldToValidate,
    ) {
        fieldToValidate.fieldValue?.let { value ->
            if (fieldToValidate.isDefaultUnits) {
                assertEquals(value, field.valueInDefaultUnit)
            } else {
                assertEquals(value, field.value)
            }
        }
        fieldToValidate.fieldSystemValue?.let { assertEquals(it, field.systemValue) }
    }

    private fun getUpdatedField(
        manufacturingDto: ManufacturingDto,
        fieldName: String,
        fieldValue: Any,
    ): ManufacturingUpdateController.InputParameterApi {
        val modifiedField = manufacturingDto.fields.find { it.name == fieldName }!!.copy(value = fieldValue)
        return ManufacturingUpdateController.InputParameterApi(
            name = modifiedField.name,
            entityId = manufacturingDto.id,
            type = modifiedField.type,
            unit = modifiedField.unit,
            value = modifiedField.value,
        )
    }

    private fun addCycleTimeStepAndPublish(
        node: BomNodeDto,
        group: ManufacturingDto,
        fields: List<FieldParameter>,
    ): Pair<BomNodeDto, ManufacturingDto> {
        return manufacturingCrudTestClient.addCycleTimeStep(
            bomNodeId = node.id,
            branchId = node.branch.id,
            parentId = group.id,
            name = "cycleTimeStep",
            fields = fields.map { it.toFieldParameterDto() },
        ).let {
            manufacturingCrudTestClient.publish(it)
        }.let {
            it to it.findInTree { entity -> entity.name == "cycleTimeStep" }!!
        }
    }

    private fun createAndPublishCalcWithGroup(): Pair<BomNodeDto, ManufacturingDto> {
        return createCalcWithStep().let { (node, step) ->
            manufacturingCrudTestClient.addCycleTimeStepGroup(
                bomNodeId = node.id,
                branchId = node.branch.id,
                parentId = step.id,
                name = GROUP,
                fields = groupFields(),
            )
        }.let {
            manufacturingCrudTestClient.publish(it)
        }.let { node ->
            val group = node.findInTree { entity -> entity.name == GROUP }!!
            node to group
        }
    }

    private fun createCalcWithStep(): Pair<BomNodeDto, ManufacturingDto> {
        return wizardBuilder.createStandard(partId = partId.toHexString()).bomNode
            .let {
                manufacturingCrudTestClient.addStep(it, name = "step", initialCycleTime = Time(1.0, TimeUnits.SECOND))
            }.let {
                val step = it.findInTree { entity -> entity.name == "step" }!!
                it to step
            }
    }

    private fun simpleLineMillingActions() =
        listOf(
            getAction(MACHINING_TYPE, "MILLING"),
            getAction("cycleTimeStep", "ManualSimpleLineMilling"),
            getAction(LENGTH, 0.01),
            getAction(DEPTH, 0.02),
        )

    private fun faceMillingActions() =
        listOf(
            getAction(MACHINING_TYPE, "MILLING"),
            getAction("cycleTimeStep", "ManualFaceMilling"),
            getAction(LENGTH, 0.01),
            getAction(DEPTH, 0.02),
            getAction(WIDTH, 0.023),
        )

    private fun getAction(
        fieldName: String,
        value: Any,
    ): (List<FieldParameter>) -> List<FieldParameter> {
        return { fields: List<FieldParameter> ->
            fields.map {
                if (it.name == fieldName) {
                    it.copy(value = value, source = "I")
                } else {
                    it
                }
            }
        }
    }

    private fun doModalInteraction(
        node: BomNodeDto,
        parentEntity: ManufacturingDto,
        parentType: String = Entities.CYCLETIME_STEP_GROUP.name,
        entityClass: String = "CycleTimeStep",
        entityType: Entities = Entities.CYCLETIME_STEP,
        actions: List<(fields: List<FieldParameter>) -> List<FieldParameter>>,
    ): List<FieldParameter> {
        val fields =
            masterDataClient.getMandatoryEntityFields(
                projectId,
                node.id,
                node.branch.id,
                entityType,
                composite = null,
                parentType = parentType,
                entityClass = entityClass,
                parentId = parentEntity.id,
            )
        return applyActions(node, parentEntity, entityType, fields, actions)
    }

    private fun applyActions(
        node: BomNodeDto,
        parentEntity: ManufacturingDto,
        entityType: Entities,
        inputFields: List<FieldParameter>,
        actions: List<(fields: List<FieldParameter>) -> List<FieldParameter>>,
    ): List<FieldParameter> {
        var fields = inputFields
        actions.forEach {
            fields =
                masterDataClient.refreshModal(
                    projectId,
                    node.id,
                    node.branch.id,
                    EntityCreationDto(parentId = parentEntity.id, entityType = entityType, fields = it(fields)),
                ).fields
        }
        return fields
    }

    private fun groupFields() =
        listOf(
            FieldParameterDto(
                name = MATERIAL_NAME,
                type = "Text",
                value = "null",
                denominatorUnit = null,
            ),
            FieldParameterDto(
                name = MATERIAL_GROUP,
                type = "Text",
                value = "P1",
                denominatorUnit = null,
            ),
            FieldParameterDto(
                name = TYPE,
                type = "CycleTimeGroupType",
                value = "MACHINING",
                denominatorUnit = null,
            ),
            FieldParameterDto(
                name = "displayDesignation",
                type = "Text",
                value = GROUP,
                denominatorUnit = null,
            ),
            FieldParameterDto(
                name = "processingType",
                type = "CycleTimeStepProcessingType",
                value = "OTHERS",
                denominatorUnit = null,
            ),
        )

    data class FieldToValidate(
        val fieldName: String,
        val fieldValue: Any?,
        val fieldSystemValue: Any? = null,
        val isDefaultUnits: Boolean = false,
    )
}
