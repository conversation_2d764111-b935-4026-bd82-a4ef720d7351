package com.nu.bom.core.general

import com.nu.bom.core.api.ManufacturingUpdateController
import com.nu.bom.core.api.dtos.BomNodeDto
import com.nu.bom.core.api.dtos.CreateManufacturingResult
import com.nu.bom.core.api.dtos.ManufacturingDto
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.fieldTypes.CalculationQualityConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeCleannessExtension
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeHeatTreatmentAlu
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYearsUnit
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.NET_WEIGHT_PER_PART_WIZARD
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.configurations.SemanticVersion
import com.nu.bom.core.mongoProjectId
import com.nu.bom.core.prediction.PredictionServiceMock
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.NbkClient
import com.nu.bom.core.utils.toObjectId
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.reactive.server.WebTestClient

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test", "masterdata-init", "lookup-init", "template-init", "shape-init", "mock-predictions", "config-init")
// TODO: Investigate why the default timeout is not enough
@AutoConfigureWebTestClient(timeout = "PT20S")
class ManufacturingUpdateIT : NbkClient.With {
    private lateinit var client: WebTestClient

    @Autowired
    override lateinit var nbkClient: NbkClient

    lateinit var accessCheck: AccessCheck

    private var projectId: ProjectId = ProjectId()
    private var branchId: BranchId = BranchId()

    private lateinit var createManufacturingResult: CreateManufacturingResult
    private lateinit var bomNodeId: String

    @Autowired
    lateinit var predictionService: PredictionServiceMock

    @BeforeEach
    fun init() {
        predictionService
            .clear()
            .withPrediction("dca", "CastingPressure_DieCasting", 7.44300396825397E7)
            .withPrediction("dca", "ToolComplexity_DieCasting", "T3")
            .withPrediction("dca", "UseSlider_DieCasting", "true")
            .withPrediction("dca", "AreaSurchargeFraction_DieCasting", 1.5112093434343434)
            .withPrediction("dca", "SprueRate_DieCasting", 0.480675764244256)
            .withPrediction("dca", "StepSubType_Deburring", "Trowal")
            .withPrediction("dca", "InvestPerToolTrim_DieCasting", 18582.339615781402)
            .withPrediction("dca", "InvestPerToolInsert_DieCasting", 55872.12872428789)
            .withPrediction("dca", "UtilizationRate_DieCasting", 0.8066819219884981)
            .withPrediction("dca", "SolidificationTime_DieCasting", 5.792651370576466)
            .withPrediction("dca", "ScrapRate_DieCasting", 0.02367486811049392)
            .withPrediction("dca", "ToolMaintenanceRateFrame_DieCasting", 0.2885698733600229)
            .withPrediction("dca", "InvestPerToolFrame_DieCasting", 18635.970881736677)
            .withPrediction("dca", "ToolMaintenanceRateInsert_DieCasting", 0.3468404703330697)
            .withPrediction("dca", "ToolMaintenanceRateTrim_DieCasting", 0.3263494625728716)
            .withPrediction("dca", "StepTimeWithRelevance1033_DieCasting", 3.6436081595047503)
            .withPrediction("dca", "StepTimeWithRelevance1032_DieCasting", 1.9684284128925773)
            .withPrediction("dca", "StepTimeWithRelevance1028_DieCasting", 1.0197853114975546)
            .withPrediction("dca", "StepTimeWithRelevance2028_DieCasting", 1.0386936396936395)
            .withPrediction("dca", "StepTimeWithRelevance1029_DieCasting", 4.642981948472613)
            .withPrediction("dca", "StepTimeWithRelevance1031_DieCasting", 2.4462712670170097)
            .withPrediction("dca", "StepTimeWithRelevance1026_DieCasting", 7.7576927222588195)
            .withPrediction("dca", "StepTimeWithRelevance1036_DieCasting", 1.0)
            .withPrediction("dca", "StepTimeWithRelevance1035_DieCasting", 1.768281196499764)
            .withPrediction("dca", "StepTimeWithRelevance1034_DieCasting", 2.360795525769118)
            .withPrediction("dca", "StepTimeWithRelevance1030_DieCasting", 6.323210688215004)
            .withPrediction("dca", "StepTimeWithRelevance1037_DieCasting", 6.631352931067313)
            .withPrediction("dca", "ManufacturingCostsPerKg_Deburring", 0.2038789017373176)
    }

    @BeforeEach
    fun setup() {
        val (ac, project) = nbkClient.setupWithProject(name = "Test Project", key = "MUIT")

        accessCheck = ac
        projectId = project.mongoProjectId()

        client = manufacturingCrudTestClient.getConfiguredClient()

        createManufacturingResult =
            wizardBuilder
                .withStandardFields {
                    location = Text("tset.ref.classification.germany")
                    lifeTime = TimeInYears(5.toBigDecimal(), TimeInYearsUnit.YEAR)
                    peakUsableProductionVolumePerYear = QuantityUnit(55_000.toBigDecimal())
                    averageUsableProductionVolumePerYear = QuantityUnit(70000.toBigDecimal())
                    dimension = Dimension(Dimension.Selection.NUMBER)
                    calculationQualityConfigurationKey =
                        CalculationQualityConfigurationKey(
                            ConfigurationIdentifier.tset("BROWNFIELD", SemanticVersion.initialVersion()),
                        )
                }.withTechStep("High Pressure Die Casting")
                .withWamStep(
                    mapOf(
                        NET_WEIGHT_PER_PART_WIZARD to Weight(4.0.toBigDecimal(), WeightUnits.KILOGRAM),
                        "materialName" to Text("AlSi10Mg (Fe)"),
                    ),
                ).withShapeStep("S_224")
                .withSpecStep(
                    mapOf(
                        "maxWallThickness" to Length(0.003.toBigDecimal(), LengthUnits.METER),
                        "stepSubTypeHeatTreatmentAlu" to StepSubTypeHeatTreatmentAlu.T5,
                        "cleaningNeeded" to SelectableBoolean.TRUE,
                        "cleanness" to StepSubTypeCleannessExtension.NORMAL,
                        "leakageTest" to SelectableBoolean.TRUE,
                        "impregnationNeeded" to SelectableBoolean.TRUE,
                    ),
                ) {}
                .execute()
                .getOrThrow()

        bomNodeId = createManufacturingResult.bomNode.id

        val callsPerYear = createManufacturingResult.bomNode.getField("callsPerYear")
        assertNotNull(callsPerYear, "callsPerYear exists")

        branchId =
            manufacturingCrudTestClient
                .checkout(bomNodeId)
                .branch.id
                .toObjectId()!!
    }

    @AfterEach
    fun teardown() {
        nbkClient.cleanup()
    }

    @Test
    fun updateAndOverwrite() {
        val manufacturing = createManufacturingResult.bomNode.manufacturing!!

        val modifiedField =
            manufacturing.fields
                .find {
                    it.name == "lifeTime"
                }!!
                .let {
                    it.copy(value = (it.value as Double) * 2.0)
                }

        val updateFieldRequest =
            ManufacturingUpdateController.InputParameterApi(
                name = modifiedField.name,
                entityId = manufacturing.id,
                type = modifiedField.type,
                unit = modifiedField.unit,
                value = modifiedField.value,
            )

        val updatedBomNode =
            manufacturingCrudTestClient.updateField(
                bomNodeId = bomNodeId,
                branch = branchId.toHexString(),
                field = updateFieldRequest,
            )

        // same bom node, same manufacturing
        assertEquals(bomNodeId, updatedBomNode.id)
        assertEquals(createManufacturingResult.bomNode.manufacturing!!.id, updatedBomNode.manufacturing!!.id)

        // updated version
        assertTrue(createManufacturingResult.bomNode.manufacturing!!.version < updatedBomNode.manufacturing!!.version)

        val updatedField = updatedBomNode.manufacturing!!.fields.find { it.name == modifiedField.name }!!

        // assert new field value is persisted
        assertEquals(modifiedField.value, updatedField.value)
        assertEquals(modifiedField.type, updatedField.type)
        assertEquals(modifiedField.unit, updatedField.unit)
        // TODO: enable
        // assertEquals(modifiedField.source, updatedField.source)
    }

    @Test
    fun updateCalculationQuality() {
        val manufacturing = createManufacturingResult.bomNode.manufacturing!!

        val curDisplayName = (
            manufacturing.fields
                .find {
                    it.name == "calculationQualityDisplayName"
                }!!
                .value as String
        )
        assertEquals("Brownfield", curDisplayName)

        val modifiedField =
            manufacturing.fields
                .find {
                    it.name == Manufacturing::calculationQualityConfigurationKey.name
                }!!
                .copy(
                    value = ConfigurationIdentifier.tset("QUOTE", SemanticVersion.initialVersion()),
                )

        val updateFieldRequest =
            ManufacturingUpdateController.InputParameterApi(
                name = modifiedField.name,
                entityId = manufacturing.id,
                type = modifiedField.type,
                unit = modifiedField.unit,
                value = modifiedField.value,
            )

        val updatedBomNode =
            manufacturingCrudTestClient.updateField(
                bomNodeId = bomNodeId,
                branch = branchId.toHexString(),
                field = updateFieldRequest,
            )

        // same bom node, same manufacturing
        assertEquals(bomNodeId, updatedBomNode.id)
        assertEquals(createManufacturingResult.bomNode.manufacturing!!.id, updatedBomNode.manufacturing!!.id)

        // updated version
        assertTrue(createManufacturingResult.bomNode.manufacturing!!.version < updatedBomNode.manufacturing!!.version)

        val modifiedDisplayName = (
            updatedBomNode.manufacturing!!
                .fields
                .find {
                    it.name == "calculationQualityDisplayName"
                }!!
                .value as String
        )
        assertEquals("Quote", modifiedDisplayName)
    }

    @Test
    fun lock() {
        val manufacturing = createManufacturingResult.bomNode.manufacturing!!

        val fieldToLock = manufacturing.getField("callsPerYear")

        // assert default source
        assertEquals("C", fieldToLock.source!!)

        val lockFieldRequest =
            ManufacturingUpdateController.InputParameterApi(
                name = fieldToLock.name,
                entityId = manufacturing.id,
                type = fieldToLock.type,
                unit = fieldToLock.unit,
                value = fieldToLock.value,
            )

        val updatedBomNode =
            manufacturingCrudTestClient.lock(
                bomNodeId = bomNodeId,
                branchId = branchId,
                lockFieldRequest = lockFieldRequest,
            )

        // same bom node
        assertEquals(bomNodeId, updatedBomNode.id)
        // same manufacturing
        assertEquals(createManufacturingResult.bomNode.manufacturing!!.id, updatedBomNode.manufacturing!!.id)
        // same version
        assertEquals(createManufacturingResult.bomNode.manufacturing!!.version, updatedBomNode.manufacturing!!.version)

        val lockedField = updatedBomNode.manufacturing!!.fields.find { it.name == fieldToLock.name }!!

        // assert that field is equal to the original except the source, which has now been changed to "I"
        assertEquals(fieldToLock.copy(source = "I"), lockedField)
    }

    @Test
    fun unlockAndUpdate() {
        val initialManufacturing = createManufacturingResult.bomNode.manufacturing!!
        val nodeWithLockedField = lockField(initialManufacturing, "callsPerYear")

        val entityWithLockedField = nodeWithLockedField.manufacturing!!
        val lockedField = entityWithLockedField.getField("callsPerYear")

        // assert that field is locked: i.e. source has been changed to "I"
        assertEquals("I", lockedField.source)

        val unlockFieldRequest =
            ManufacturingUpdateController.InputParameterApi(
                name = lockedField.name,
                entityId = entityWithLockedField.id,
                type = lockedField.type,
                unit = lockedField.unit,
                value = lockedField.value,
            )

        val updatedBomNode =
            manufacturingCrudTestClient.unlock(
                bomNodeId = bomNodeId,
                branchId = branchId,
                unlockFieldRequest = unlockFieldRequest,
            )

        // same bom node, same manufacturing
        assertEquals(bomNodeId, updatedBomNode.id)
        assertEquals(createManufacturingResult.bomNode.manufacturing!!.id, updatedBomNode.manufacturing!!.id)

        // updated version
        assertTrue(createManufacturingResult.bomNode.manufacturing!!.version < updatedBomNode.manufacturing!!.version)

        val unlockedField = updatedBomNode.manufacturing!!.getField(lockedField.name)

        // assert that field is now unlocked, i.e. its source has now been changed to "C"
        assertEquals("C", unlockedField.source)
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun renameNode(userBranch: Boolean) {
        val renamedNode =
            manufacturingCrudTestClient.renameNode(
                bomNodeId = bomNodeId,
                title = "New Title - &\\\"%%\\\"%%\\\"\\\"\$!",
                branch = if (userBranch) branchId.toHexString() else null,
            )

        // assert title change
        assertThat(renamedNode.title).isEqualTo("New Title - &\\\"%%\\\"%%\\\"\\\"\$!")

        // assert checkout (stay on existing user branch, or new checkout from main)
        if (userBranch) {
            assertThat(renamedNode.branch.id).isEqualTo(branchId.toHexString())
        } else {
            assertThat(renamedNode.branch.id).isNotEqualTo(branchId.toHexString())
        }
    }

    private fun lockField(
        manufacturing: ManufacturingDto,
        name: String,
    ): BomNodeDto {
        val fieldToLock = manufacturing.getField(name)

        val updateFieldRequest =
            ManufacturingUpdateController.InputParameterApi(
                name = fieldToLock.name,
                entityId = manufacturing.id,
                type = fieldToLock.type,
                unit = fieldToLock.unit,
                value = fieldToLock.value,
            )

        return manufacturingCrudTestClient.lock(bomNodeId = bomNodeId, branchId = branchId, lockFieldRequest = updateFieldRequest)
    }
}
