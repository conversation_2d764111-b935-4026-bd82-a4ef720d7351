package com.nu.bom.core.integrationtests

import com.nu.bom.core.manufacturing.utils.ManufacturingCrudTestClient
import com.nu.bom.core.manufacturing.utils.ManufacturingWizardTestBuilder
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.toMongoProjectId
import com.nu.bom.core.service.FolderService
import com.nu.bom.core.service.ProjectService
import com.nu.bom.core.service.WorkspaceService
import com.nu.bom.core.utils.AccountTestUtil
import com.nu.bomrads.dto.admin.FolderUpdateDTO
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import reactor.test.StepVerifier

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test", "lookup-init", "config-init")
@AutoConfigureWebTestClient
class MultitenancyIT {
    @Autowired
    private lateinit var workspaceService: WorkspaceService

    @Autowired
    private lateinit var folderService: FolderService

    @Autowired
    private lateinit var projectService: ProjectService

    @Autowired
    private lateinit var accountUtil: AccountTestUtil

    @Autowired
    private lateinit var crudUtil: ManufacturingCrudTestClient

    @Autowired
    private lateinit var wizardUtil: ManufacturingWizardTestBuilder

    private lateinit var account1: AccountTestUtil.TestAccount
    private lateinit var account2: AccountTestUtil.TestAccount

    @BeforeEach
    fun setup() {
        account1 = accountUtil.createTestAccount("accountA")
        account2 = accountUtil.createTestAccount("accountB")
    }

    @AfterEach
    fun teardown() {
        accountUtil.cleanup()
    }

    @Test
    fun createGetProjectAndPart() {
        /** FOLDER */
        val folderAccount1 =
            accountUtil.doAs(account1) {
                workspaceService.createWorkspace(it, "workspace", emptyList())
                    .flatMap { workspace ->
                        folderService.createOrGet(accessCheck = it, FolderUpdateDTO("FolderAccount1", null, workspace.id))
                    }
            }.block()!!

        /** PROJECT */

        val projectName = "ProjectAccount1"

        val createProjectAsAccount1 =
            accountUtil.doAs(account1) {
                projectService.createProject(
                    accessCheck = it,
                    name = projectName,
                    folderId = folderAccount1.id,
                )
            }

        val getProjectAsAccount1 =
            accountUtil.doAs(account1) {
                projectService.getProjectByName(accessCheck = it, name = projectName)
            }

        val getProjectAsAccount2 =
            accountUtil.doAs(account2) {
                projectService.getProjectByName(accessCheck = it, name = projectName)
            }

        StepVerifier.create(createProjectAsAccount1)
            .expectNextCount(1)
            .verifyComplete()

        StepVerifier.create(getProjectAsAccount1)
            .expectNextMatches { it.name == projectName }
            .verifyComplete()

        StepVerifier.create(getProjectAsAccount2)
            .verifyComplete()
    }

    private fun createProject(
        projectName: String,
        projectKey: String,
        account: AccountTestUtil.TestAccount,
    ): ProjectId {
        return accountUtil.doAs(account) {
            workspaceService.createWorkspace(it, "workspace $projectKey", emptyList())
                .flatMap { workspace ->
                    folderService.createOrGet(accessCheck = it, FolderUpdateDTO("Folder $projectKey", null, workspace.id))
                        .flatMap { folder ->
                            projectService.createProject(
                                accessCheck = it,
                                name = projectName,
                                key = projectKey,
                                folderId = folder.id,
                            ).map { it.project!!.id.toMongoProjectId() }
                        }
                }
        }.block()!!
    }

    @Test
    fun createGetManufacturing() {
        val projectName = "MyProject"
        val projectKey = "MYP1"

        val project1 =
            createProject(
                projectName = projectName,
                projectKey = projectKey,
                account = account1,
            )

        val project2 =
            createProject(
                projectKey = projectKey,
                projectName = projectName,
                account = account2,
            )

        wizardUtil.configure(project1, account1.jwt)

        val bomNodeId1 =
            wizardUtil.createStandard()
                .bomNode
                .id

        wizardUtil.configure(project2, account2.jwt)

        val bomNodeId2 =
            wizardUtil.createStandard()
                .bomNode
                .id

        crudUtil.configure(project1, account1.jwt)

        val node1AsAccount1 = crudUtil.getBomNode(bomNodeId1)
        assertNotNull(node1AsAccount1)
        val node2AsAccount1 = crudUtil.getBomNodeNotFound(bomNodeId2)
        assertNull(node2AsAccount1)

        crudUtil.configure(project2, account2.jwt)

        val node1AsAccount2 = crudUtil.getBomNodeNotFound(bomNodeId1)
        val node2AsAccount2 = crudUtil.getBomNode(bomNodeId2)

        assertNull(node1AsAccount2)
        assertNotNull(node2AsAccount2)
    }
}
