package com.nu.bom.core.integrationtests

import com.nimbusds.jose.JWSAlgorithm
import com.nimbusds.jose.JWSHeader
import com.nimbusds.jwt.JWTClaimsSet
import com.nimbusds.jwt.SignedJWT
import com.nu.bom.core.service.bomrads.BomradsAccountService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.user.UserAccountInfoDto
import com.nu.bom.core.user.UserClient
import com.nu.bom.core.utils.TestSecurityConfig
import com.nu.security.BomradsService
import com.nu.security.config.NuHeadersConfiguration
import com.nu.user.UserInfoDto
import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.aspectj.lang.annotation.Pointcut
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.context.annotation.Profile
import org.springframework.security.oauth2.jose.jws.MacAlgorithm
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.security.oauth2.jwt.NimbusReactiveJwtDecoder
import org.springframework.stereotype.Component
import org.springframework.test.context.ActiveProfiles
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.time.Instant
import java.util.Date
import java.util.Optional

private const val ENV_NAME = "integration-test-env"

@SpringBootTest
@ActiveProfiles("test", "test-account-service")
class AccountIT {
    @Autowired
    private lateinit var bomradsAccountService: BomradsAccountService

    @Test
    fun createNewAccountWithDefaultWorkspace() {
        val account = bomradsAccountService.createAccount("newlycreated", ENV_NAME).block()
        assert(account != null)
    }
}

@Configuration
@Profile("test-account-service")
class UserClientTestConfiguration {
    @Bean
    @Primary
    fun client(): UserClient =
        object : UserClient {
            @Autowired
            private lateinit var testSecurity: TestSecurityConfig

            override fun getUsers(
                accessCheck: AccessCheck,
                filterTerm: String?,
            ): Flux<UserInfoDto> = TODO()

            override fun getUserById(
                accessCheck: AccessCheck,
                id: String?,
            ): Mono<Optional<UserInfoDto>> = TODO()

            override fun getUsersByIds(
                accessCheck: AccessCheck,
                ids: List<String>,
            ): Mono<List<UserInfoDto>> = TODO()

            override fun getCurrentUser(jwt: Jwt): Mono<String> = TODO()

            override fun getCurrentUserAndAccount(accessCheck: AccessCheck): Mono<UserAccountInfoDto> = TODO()

            override fun getServiceToken(): String {
                val header = JWSHeader(JWSAlgorithm.HS512)
                val claimsSet =
                    JWTClaimsSet
                        .Builder()
                        .claim(BomradsService.CLAIM_ACCOUNT, "account_default")
                        .claim(BomradsService.CLAIM_ROLES, "tset-platform-develop")
                        .claim(BomradsService.CLAIM_SERVICE_TOKEN, true)
                        .issueTime(Date.from(Instant.now()))
                        .expirationTime(Date.from(Instant.now().plusSeconds(600)))

                val jwt =
                    SignedJWT(header, claimsSet.build())
                        .apply { sign(testSecurity.signer) }
                        .toJwt()

                return jwt.tokenValue
            }

            private fun SignedJWT.toJwt(): Jwt =
                NimbusReactiveJwtDecoder
                    .withSecretKey(testSecurity.signer.secretKey)
                    .macAlgorithm(MacAlgorithm.HS512)
                    .build()
                    .decode(this.serialize())
                    .block()!!
        }
}

@Aspect
@Component
@Profile("test-account-service")
class TsetServiceAspect(
    private val nuHeadersConfiguration: NuHeadersConfiguration,
) {
    @Pointcut("execution(* com.nu.http.TsetService.postToMono(..))")
    fun addHeaderPc() {
        // Intentionally empty, only used for AspectJ configuration
    }

    @Around("addHeaderPc()")
    fun addEnvHeader(pjp: ProceedingJoinPoint): Mono<Any> {
        val args =
            pjp.args.withIndex().map { i ->
                when (val m = i.value) {
                    is Map<*, *> ->
                        if (m.keys.contains(BomradsService.NU_ACCOUNT_HEADER)) {
                            m + (nuHeadersConfiguration.envId to ENV_NAME)
                        } else {
                            m
                        }

                    else -> m
                }
            }

        val proceed = pjp.proceed(args.toTypedArray())
        return proceed as Mono<Any>
    }
}
