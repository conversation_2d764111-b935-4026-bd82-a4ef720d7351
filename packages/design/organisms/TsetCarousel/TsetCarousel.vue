<template>
  <section class="flex flex-col overflow-hidden">
    <section
      v-data-test:container
      class="flex flex-1 justify-between overflow-hidden"
      :class="containerClass"
    >
      <!-- #region previous navigation -->
      <div
        v-if="showNavButtons"
        v-data-test:navigate-previous
        class="flex h-full items-center"
      >
        <slot name="prev-navigation" :on-click="prev">
          <TsetButton
            class="mx-10 self-center"
            type="secondary"
            size="small"
            label=""
            @click="prev"
          >
            <template #icon-only>
              <IconChevronLeft class="h-24" />
            </template>
          </TsetButton>
        </slot>
      </div>
      <!-- #endregion previous navigation -->

      <!--  #region main content -->
      <section
        ref="mainContainer"
        class="main-swiper flex h-full min-w-0 flex-1 items-center justify-center overflow-hidden"
      >
        <swiper-container
          :key="currentKey"
          class="h-full w-full min-w-0"
          slides-per-view="1"
          :thumbs-swiper="containerThumb"
        >
          <swiper-slide
            v-if="images.length === 0"
            v-data-test:no-image
            class="flex h-full w-full items-center justify-center"
          >
            <slot name="no-image">
              <!-- default image placeholder -->
              <TsetImage src="h-full w-full p-20" alt="" />
            </slot>
          </swiper-slide>
          <swiper-slide
            v-for="(imageUrl, slideIndex) in images"
            v-else
            :key="imageUrl"
            v-data-test:slide="slideIndex"
            class="flex h-full w-full items-center justify-center"
          >
            <slot name="slide-image" :image-url="imageUrl" :index="slideIndex">
              <TsetImage
                class="mx-auto h-full w-full object-contain"
                :src="imageUrl"
                :alt="`slide ${slideIndex + 1}`"
              />
            </slot>
          </swiper-slide>
        </swiper-container>
      </section>
      <!--  #endregion main content -->

      <!--  #region next navigation -->
      <div
        v-if="showNavButtons"
        v-data-test:navigate-next
        class="flex h-full items-center"
      >
        <slot name="next-navigation" :on-click="next">
          <TsetButton
            type="secondary"
            label=""
            size="small"
            class="mx-10 self-center"
            @click="next"
          >
            <template #icon-only>
              <IconChevronRight class="h-24" />
            </template>
          </TsetButton>
        </slot>
      </div>
      <!--  #endregion next navigation -->
    </section>

    <!--  #region Thumbnail -->
    <section
      v-if="thumbnails"
      ref="thumbContainer"
      v-data-test:thumbnails
      class="thumb-swiper flex-shrink-0"
    >
      <swiper-container
        :id="thumbSwiperId"
        :key="thumbCurrentKey"
        slides-per-view="auto"
        space-between="12"
        :center-insufficient-slides="true"
        :class="thumbnailContainerClass"
      >
        <swiper-slide
          v-for="(imageUrl, thumbIndex) in images"
          :key="imageUrl"
          v-data-test:thumb-slide="thumbIndex"
          class="flex items-center justify-center"
          :class="[
            {
              'opacity-50': index !== thumbIndex,
            },
            thumbnailSlideSize,
          ]"
        >
          <slot name="thumb-image" :image-url="imageUrl" :index="thumbIndex">
            <TsetImage
              class="h-full w-full rounded object-cover"
              :src="imageUrl"
              :alt="`thumbnail ${thumbIndex + 1}`"
            />
          </slot>
        </swiper-slide>
      </swiper-container>
    </section>
    <!--  #endregion Thumbnail -->

    <!--  #region Counter -->
    <div
      v-if="showCounter && images.length > 1"
      v-data-test:counter
      class="text-caption-semibold absolute bottom-4 left-4 z-40 inline-flex h-16 max-w-40 items-center rounded-md bg-gray-light/70 px-6 text-center"
      :class="[counterClass]"
    >
      {{ index + 1 }}/{{ images.length }}
    </div>
    <!--  #endregion Counter-->
  </section>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, ref, toRef, watch } from 'vue'
import type { Swiper as SwiperType } from 'swiper/types'
import { useEventListener, useThrottleFn } from '@vueuse/core'
import TsetButton from '@tset/design/atoms/TsetButton'
import { generateUuid } from '@tset/shared-utils/helpers/general'
import TsetImage from '@tset/design/atoms/TsetImage/TsetImage.vue'

//#region PROPS
const props = withDefaults(
  defineProps<{
    images: string[]
    index: number
    allowKeyboardNavigation?: boolean
    containerClass?: string
    showCounter?: boolean
    counterClass?: string
    hideNavigation?: boolean
    thumbnails?: boolean
    thumbnailContainerClass?: string
    thumbnailSlideSize?: `h-[${number}px] w-[${number}px]`
  }>(),
  {
    thumbnailSlideSize: 'h-[48px] w-[48px]',
    containerClass: '',
    counterClass: '',
    thumbnailContainerClass: '',
  }
)
//#endregion PROPS

//#region EMITS
const emit = defineEmits<{
  'update:index': [index: number]
}>()
//#endregion EMITS

const mainContainer = ref<HTMLDivElement>()
const thumbContainer = ref<HTMLDivElement>()
const swiper = ref<SwiperType | undefined>(undefined)
const thumbSwiper = ref<SwiperType | undefined>(undefined)

// #region Swiper Id
const swiperId = generateUuid()
const thumbSwiperId = 'thumb_' + swiperId
const currentKey = ref(generateUuid())
const thumbCurrentKey = computed(() => 'thumb_' + currentKey.value)
const containerThumb = computed(() => {
  if (props.thumbnails) {
    return `#${thumbSwiperId}`
  }
  return undefined
})
// #endregion Swiper Id

onMounted(async () => {
  await initializeSwiper()
})

function getMainSwiperEl() {
  return mainContainer.value?.querySelector('swiper-container')
}

function getThumbSwiperEl() {
  return thumbContainer.value?.querySelector('swiper-container')
}

async function initializeSwiper() {
  thumbSwiper.value = getThumbSwiperEl()?.swiper
  swiper.value = getMainSwiperEl()?.swiper

  swiper.value?.on('realIndexChange', (arg) => {
    emit('update:index', arg.realIndex)
  })
  swiper.value?.on('slidesUpdated', () => {
    startLoop()
  })

  slideTo(props.index)
  await nextTick(() => {
    startLoop()
  })
}

function startLoop() {
  const swiperEl = getMainSwiperEl()
  const slideCount = swiperEl?.swiper?.slides?.length ?? 0
  const params = swiperEl?.swiper?.params
  if (swiperEl && slideCount > 1 && !params?.loop) {
    Object.assign(swiperEl, {
      loop: true,
    })
    swiperEl.initialize()
  }
}

watch(toRef(props, 'index'), () => {
  slideTo(props.index)
})

watch(toRef(props, 'images'), async () => {
  currentKey.value = generateUuid()
  emit('update:index', 0)
  await nextTick(() => initializeSwiper())
})

// #region Carousel Navigation
const showNavButtons = computed(() => {
  return !props.hideNavigation && props.images.length > 1
})

function slideTo(value: number) {
  if (swiper.value?.realIndex !== value && swiper.value?.params) {
    swiper.value?.slideToLoop(value)
  }
}
function next() {
  swiper.value?.slideNext()
}

function prev() {
  swiper.value?.slidePrev()
}
// #endregion Carousel Navigation

// #region Keyboard navigation
const throttledFn = useThrottleFn((event: KeyboardEvent) => {
  if (event.ctrlKey || event.altKey) {
    return
  }

  const actions = {
    ArrowRight: next,
    ArrowLeft: prev,
  } as Record<string, () => void>

  actions[event.key]?.()

  if (actions[event.key]) {
    event.preventDefault()
    event.stopPropagation()
  }
}, 110)
useEventListener(document, ['keydown'], async (event) => {
  if (props.allowKeyboardNavigation) {
    await throttledFn(event)
  }
})
// #endregion keyboard navigation
</script>

<script lang="ts">
export default {
  name: 'TsetCarousel',
}
</script>
