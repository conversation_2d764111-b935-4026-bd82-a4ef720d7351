<template>
  <div
    :id="componentId"
    ref="tsetSelectContainer"
    data-test="tsetselect-container"
    class="relative h-full w-full"
  >
    <TsetSelectHeader
      :id="id"
      ref="tsetSelectHeader"
      :mode="isOpened ? 'edit' : 'normal'"
      :items="items"
      :class="{ 'z-20': isOpened }"
      :maximize-content="maximizeContent"
      :disabled="disabled"
      :hide-outline="hideOutline"
      :with-tset-input="withTsetInput"
      :tab-index="tabIndex"
      :hide-hover-outline="hideHoverOutline"
      :tset-table-editor="isTsetTableEditor"
      :has-error="hasError"
      :has-warning="hasWarning"
      @input="$emit('input', $event)"
      @caret-click="toggle"
      @click="open"
      @down="emit('down')"
      @up="emit('up')"
      @esc="close()"
      @tab="handleTab()"
      @enter="emit('select', $event)"
    >
      <template #header-list-item="{ item }">
        <slot name="header-list-item" :item="item">
          <TsetSelectListItem
            v-if="item"
            :item="item"
            :maximize-content="maximizeContent"
            :is-header-item="true"
            :max-item-width-px="maxItemWidthPx"
            hide-icon
            tabindex="-1"
          >
            <template v-if="item?.selectable.iconName" #left-icon>
              <component
                :is="item?.selectable.iconName"
                v-data-test:header-left-icon
                class="mr-8 shrink-0 grow-0 basis-16"
              />
            </template>
          </TsetSelectListItem>
          <span
            v-else-if="placeholder"
            v-data-test:placeholder
            class="px-8 text-gray-default"
          >
            {{ placeholder }}
          </span>
        </slot>
      </template>
    </TsetSelectHeader>
    <TsetPopup
      :opened="isOpened"
      :animation="'none'"
      :open-on-trigger-click="false"
      :close-on-click-outside="true"
      :close-on-self-click="false"
      :placements="[
        'bottom',
        'top',
        'bottom-start',
        'top-start',
        'bottom-end',
        'top-end',
      ]"
      @closed="close"
    >
      <div
        v-data-test:dropdown="dataTestSuffix"
        class="tset-select-dropdown"
        :style="{ minWidth: listMinWidth + 'px' }"
      >
        <div v-if="isLoading" class="loading-wrapper">
          <TsetWave size="small" />
        </div>
        <TsetSelectList
          v-if="!isLoading"
          :id="listId"
          key="tsetselect-list"
          class="min-w-100 max-w-full"
          :items="items"
          :current="current"
          :max-height="listMaxHeight"
          :maximize-content="maximizeContent"
          @item-click="emit('select', $event)"
          @item-hover="emit('itemHover', $event)"
        >
          <template #list-item="{ item }">
            <slot name="list-item" :item="item">
              <TsetSelectListItem :item="item">
                <template v-if="item?.selectable.iconName" #left-icon>
                  <component
                    :is="item?.selectable.iconName"
                    v-data-test:list-item-left-icon
                    class="mr-8 shrink-0 grow-0 basis-16"
                  />
                </template>
              </TsetSelectListItem>
            </slot>
          </template>
          <template #list-footer>
            <slot name="list-footer" />
          </template>
        </TsetSelectList>
      </div>
    </TsetPopup>
  </div>
</template>

<script setup lang="ts">
import TsetPopup from '@tset/design/atoms/TsetPopup/TsetPopup.vue'
import TsetWave from '@tset/design/atoms/TsetWave/TsetWave.vue'
import { generateUuid } from '@tset/shared-utils/helpers/general'
import { computed, onMounted, ref, toRefs, watch } from 'vue'
import type TsetSelect from '.'
import type { SelectItem } from './TsetSelect.types'
import TsetSelectHeader from './TsetSelectHeader.vue'
import TsetSelectList from './TsetSelectList.vue'
import TsetSelectListItem from './TsetSelectListItem.vue'
import { useTsetSelectOpen } from './useTsetSelectOpen'

const props = withDefaults(
  defineProps<{
    items: SelectItem[]
    isOpened: boolean
    current: number
    id: string
    maximizeContent?: boolean
    disabled?: boolean
    noPortal?: boolean
    hideOutline?: boolean
    withTsetInput?: boolean
    isLoading?: boolean
    tabIndex?: number
    hideHoverOutline?: boolean
    isTsetTableEditor?: boolean
    placeholder?: string
    hasWarning?: boolean
    hasError?: boolean
    dataTestSuffix?: string
    maxItemWidthPx?: number
  }>(),
  {
    withTsetInput: true,
    tabIndex: 0,
    placeholder: '',
    hasWarning: false,
    hasError: false,
  }
)

const emit = defineEmits<{
  (e: 'select', payload?: SelectItem): void
  (e: 'close'): void
  (e: 'open'): void
  (e: 'itemHover', payload: number): void
  (e: 'up'): void
  (e: 'down'): void
  (e: 'input', event: string): void
}>()

const tsetSelectContainer = ref<typeof TsetSelect>()
const tsetSelectHeader = ref<typeof TsetSelectHeader>()
const listMinWidth = ref(50)

const uniqueId = generateUuid()
const isOpened = toRefs(props).isOpened

onMounted(() => {
  useTsetSelectOpen(isOpened, close)
})

const listMaxHeight = computed<number>(() => 200)
const componentId = computed<string>(() => `${props.id}-${uniqueId}`)
const listId = computed<string>(() => componentId.value + '-list')

function toggle(): void {
  props.isOpened ? close() : open()
}

function open(): void {
  if (!props.isOpened && !props.disabled) {
    emit('open')
  }
}

async function handleTab() {
  setTimeout(() => {
    close()
  }, 1)
}

function close(): void {
  emit('close')
}

function updateMinWidth(): void {
  const rootEl = document.getElementById(componentId.value)

  if (!rootEl) {
    return
  }

  const button =
    rootEl?.querySelector('div > .growth-container') ??
    rootEl?.querySelector('div > .header-main')

  const buttonWidth = button?.getBoundingClientRect().width ?? 50
  listMinWidth.value = buttonWidth ?? 50
}

async function clickTsetSelectHeader(e?: FocusEvent) {
  await tsetSelectHeader.value?.headerClicked(e)
}
//#endregion METHODS

watch(isOpened, (isOpened: boolean) => {
  if (isOpened) {
    updateMinWidth()
  }
})

//#region EXPOSE
defineExpose({
  clickTsetSelectHeader,
})
//#endregion EXPOSE
</script>

<script lang="ts">
export default {
  name: 'TsetSelect',
}
</script>

<style lang="postcss" scoped>
.tset-select-dropdown {
  @apply box-border;
}

.loading-wrapper {
  @apply flex items-center justify-center px-16 py-16;
}
</style>
