import type {
  NumericValueDto,
  UnitOfMeasurementTypeDto,
} from '@domain/masterdata/model/openapi'

import { UnitToString } from '@domain/masterdata/utils/Unit/toString'
import { THIN_SPACE } from '@tset/shared-utils/helpers/THIN_SPACE'

type PossibleUnitInput =
  | string
  | NumericValueDto['numerator']
  | UnitOfMeasurementTypeDto['numerator']

export function UnitToFractionString(input: {
  numerator?: PossibleUnitInput
  denominator?: PossibleUnitInput
}): string {
  return [input.numerator, input.denominator]
    .map((measurement) => {
      if (!measurement) {
        return undefined
      }
      if (typeof measurement === 'string') {
        return measurement
      }
      return UnitToString(measurement)
    })
    .filter((measurementString) => measurementString !== undefined)
    .join(`${THIN_SPACE}/${THIN_SPACE}`)
}
