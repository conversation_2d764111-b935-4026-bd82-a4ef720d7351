/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
import type { FieldValueDto } from './field-value-dto';
// May contain unused imports in some cases
import type { HeaderNaturalKeyDto } from './header-natural-key-dto';

/**
 * 
 * @export
 * @interface HeaderWithoutSchemaDto
 */
export interface HeaderWithoutSchemaDto {
    /**
     * 
     * @type {HeaderNaturalKeyDto}
     * @memberof HeaderWithoutSchemaDto
     */
    'key': HeaderNaturalKeyDto;
    /**
     * 
     * @type {string}
     * @memberof HeaderWithoutSchemaDto
     */
    'name': string;
    /**
     * A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter
     * @type {string}
     * @memberof HeaderWithoutSchemaDto
     */
    'headerTypeKey': string;
    /**
     * 
     * @type {boolean}
     * @memberof HeaderWithoutSchemaDto
     */
    'active': boolean;
    /**
     * this is a map where, the key is a key of a ClassificationTypeDto, and the value is a list of ClassificationDto keys
     * @type {{ [key: string]: Array<string>; }}
     * @memberof HeaderWithoutSchemaDto
     */
    'classifications'?: { [key: string]: Array<string>; };
    /**
     * this is a map where, the key is a key of a FieldDefinitionDto, and the value is the value for this classificationField
     * @type {{ [key: string]: FieldValueDto; }}
     * @memberof HeaderWithoutSchemaDto
     */
    'classificationFieldValues'?: { [key: string]: FieldValueDto; };
}
