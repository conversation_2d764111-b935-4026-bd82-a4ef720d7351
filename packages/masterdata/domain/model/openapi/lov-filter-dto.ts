/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface LovFilterDto
 */
export interface LovFilterDto {
    /**
     * 
     * @type {string}
     * @memberof LovFilterDto
     */
    'type': string;
    /**
     * A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter
     * @type {string}
     * @memberof LovFilterDto
     */
    'equals': string;
}
