/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig, AxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ClassificationDto } from '@domain/masterdata/model/openapi';
// @ts-ignore
import type { ClassificationFieldRequestDto } from '@domain/masterdata/model/openapi';
// @ts-ignore
import type { ClassificationFieldResponseDto } from '@domain/masterdata/model/openapi';
// @ts-ignore
import type { ErrorResponse } from '@domain/masterdata/model/openapi';
/**
 * ClassificationApi - axios parameter creator
 * @export
 */
export const ClassificationApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary Create a classification
         * @param {string} key 
         * @param {ClassificationDto} classificationDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createClassification: async (key: string, classificationDto: ClassificationDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'key' is not null or undefined
            assertParamExists('createClassification', 'key', key)
            // verify required parameter 'classificationDto' is not null or undefined
            assertParamExists('createClassification', 'classificationDto', classificationDto)
            const localVarPath = `/api/md/v1/classifications/{key}`
                .replace(`{${"key"}}`, encodeURIComponent(String(key)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(classificationDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Delete a classification
         * @param {string} key 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteClassification: async (key: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'key' is not null or undefined
            assertParamExists('deleteClassification', 'key', key)
            const localVarPath = `/api/md/v1/classifications/{key}`
                .replace(`{${"key"}}`, encodeURIComponent(String(key)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Find classifications
         * @param {string} [name] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        findAllClassifications: async (name?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/md/v1/classifications`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get direct child classifications of a node
         * @param {string} key 
         * @param {string} [searchStr] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getChildClassifications: async (key: string, searchStr?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'key' is not null or undefined
            assertParamExists('getChildClassifications', 'key', key)
            const localVarPath = `/api/md/v1/classifications/{key}/children`
                .replace(`{${"key"}}`, encodeURIComponent(String(key)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (searchStr !== undefined) {
                localVarQueryParameter['searchStr'] = searchStr;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get a classification
         * @param {string} key 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getClassification: async (key: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'key' is not null or undefined
            assertParamExists('getClassification', 'key', key)
            const localVarPath = `/api/md/v1/classifications/{key}`
                .replace(`{${"key"}}`, encodeURIComponent(String(key)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get fields of classification
         * @param {string} key 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getClassificationFields: async (key: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'key' is not null or undefined
            assertParamExists('getClassificationFields', 'key', key)
            const localVarPath = `/api/md/v1/classifications/{key}/fields`
                .replace(`{${"key"}}`, encodeURIComponent(String(key)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Set fields of classification
         * @param {string} key 
         * @param {{ [key: string]: ClassificationFieldRequestDto; }} requestBody 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        setClassificationFields: async (key: string, requestBody: { [key: string]: ClassificationFieldRequestDto; }, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'key' is not null or undefined
            assertParamExists('setClassificationFields', 'key', key)
            // verify required parameter 'requestBody' is not null or undefined
            assertParamExists('setClassificationFields', 'requestBody', requestBody)
            const localVarPath = `/api/md/v1/classifications/{key}/fields`
                .replace(`{${"key"}}`, encodeURIComponent(String(key)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(requestBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Update or create a classification
         * @param {string} key 
         * @param {ClassificationDto} classificationDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateClassification: async (key: string, classificationDto: ClassificationDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'key' is not null or undefined
            assertParamExists('updateClassification', 'key', key)
            // verify required parameter 'classificationDto' is not null or undefined
            assertParamExists('updateClassification', 'classificationDto', classificationDto)
            const localVarPath = `/api/md/v1/classifications/{key}`
                .replace(`{${"key"}}`, encodeURIComponent(String(key)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(classificationDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ClassificationApi - functional programming interface
 * @export
 */
export const ClassificationApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ClassificationApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary Create a classification
         * @param {string} key 
         * @param {ClassificationDto} classificationDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createClassification(key: string, classificationDto: ClassificationDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ClassificationDto>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createClassification(key, classificationDto, options);
            const index = configuration?.serverIndex ?? 0;
            const operationBasePath = operationServerMap['ClassificationApi.createClassification']?.[index]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, operationBasePath || basePath);
        },
        /**
         * 
         * @summary Delete a classification
         * @param {string} key 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteClassification(key: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteClassification(key, options);
            const index = configuration?.serverIndex ?? 0;
            const operationBasePath = operationServerMap['ClassificationApi.deleteClassification']?.[index]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, operationBasePath || basePath);
        },
        /**
         * 
         * @summary Find classifications
         * @param {string} [name] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async findAllClassifications(name?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<ClassificationDto>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.findAllClassifications(name, options);
            const index = configuration?.serverIndex ?? 0;
            const operationBasePath = operationServerMap['ClassificationApi.findAllClassifications']?.[index]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, operationBasePath || basePath);
        },
        /**
         * 
         * @summary Get direct child classifications of a node
         * @param {string} key 
         * @param {string} [searchStr] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getChildClassifications(key: string, searchStr?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<ClassificationDto>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getChildClassifications(key, searchStr, options);
            const index = configuration?.serverIndex ?? 0;
            const operationBasePath = operationServerMap['ClassificationApi.getChildClassifications']?.[index]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, operationBasePath || basePath);
        },
        /**
         * 
         * @summary Get a classification
         * @param {string} key 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getClassification(key: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ClassificationDto>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getClassification(key, options);
            const index = configuration?.serverIndex ?? 0;
            const operationBasePath = operationServerMap['ClassificationApi.getClassification']?.[index]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, operationBasePath || basePath);
        },
        /**
         * 
         * @summary Get fields of classification
         * @param {string} key 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getClassificationFields(key: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<{ [key: string]: ClassificationFieldResponseDto; }>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getClassificationFields(key, options);
            const index = configuration?.serverIndex ?? 0;
            const operationBasePath = operationServerMap['ClassificationApi.getClassificationFields']?.[index]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, operationBasePath || basePath);
        },
        /**
         * 
         * @summary Set fields of classification
         * @param {string} key 
         * @param {{ [key: string]: ClassificationFieldRequestDto; }} requestBody 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async setClassificationFields(key: string, requestBody: { [key: string]: ClassificationFieldRequestDto; }, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<{ [key: string]: ClassificationFieldResponseDto; }>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.setClassificationFields(key, requestBody, options);
            const index = configuration?.serverIndex ?? 0;
            const operationBasePath = operationServerMap['ClassificationApi.setClassificationFields']?.[index]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, operationBasePath || basePath);
        },
        /**
         * 
         * @summary Update or create a classification
         * @param {string} key 
         * @param {ClassificationDto} classificationDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateClassification(key: string, classificationDto: ClassificationDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ClassificationDto>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateClassification(key, classificationDto, options);
            const index = configuration?.serverIndex ?? 0;
            const operationBasePath = operationServerMap['ClassificationApi.updateClassification']?.[index]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, operationBasePath || basePath);
        },
    }
};

/**
 * ClassificationApi - factory interface
 * @export
 */
export const ClassificationApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ClassificationApiFp(configuration)
    return {
        /**
         * 
         * @summary Create a classification
         * @param {ClassificationApiCreateClassificationRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createClassification(requestParameters: ClassificationApiCreateClassificationRequest, options?: AxiosRequestConfig): AxiosPromise<ClassificationDto> {
            return localVarFp.createClassification(requestParameters.key, requestParameters.classificationDto, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Delete a classification
         * @param {ClassificationApiDeleteClassificationRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteClassification(requestParameters: ClassificationApiDeleteClassificationRequest, options?: AxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteClassification(requestParameters.key, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Find classifications
         * @param {ClassificationApiFindAllClassificationsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        findAllClassifications(requestParameters: ClassificationApiFindAllClassificationsRequest = {}, options?: AxiosRequestConfig): AxiosPromise<Array<ClassificationDto>> {
            return localVarFp.findAllClassifications(requestParameters.name, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get direct child classifications of a node
         * @param {ClassificationApiGetChildClassificationsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getChildClassifications(requestParameters: ClassificationApiGetChildClassificationsRequest, options?: AxiosRequestConfig): AxiosPromise<Array<ClassificationDto>> {
            return localVarFp.getChildClassifications(requestParameters.key, requestParameters.searchStr, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get a classification
         * @param {ClassificationApiGetClassificationRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getClassification(requestParameters: ClassificationApiGetClassificationRequest, options?: AxiosRequestConfig): AxiosPromise<ClassificationDto> {
            return localVarFp.getClassification(requestParameters.key, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get fields of classification
         * @param {ClassificationApiGetClassificationFieldsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getClassificationFields(requestParameters: ClassificationApiGetClassificationFieldsRequest, options?: AxiosRequestConfig): AxiosPromise<{ [key: string]: ClassificationFieldResponseDto; }> {
            return localVarFp.getClassificationFields(requestParameters.key, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Set fields of classification
         * @param {ClassificationApiSetClassificationFieldsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        setClassificationFields(requestParameters: ClassificationApiSetClassificationFieldsRequest, options?: AxiosRequestConfig): AxiosPromise<{ [key: string]: ClassificationFieldResponseDto; }> {
            return localVarFp.setClassificationFields(requestParameters.key, requestParameters.requestBody, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Update or create a classification
         * @param {ClassificationApiUpdateClassificationRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateClassification(requestParameters: ClassificationApiUpdateClassificationRequest, options?: AxiosRequestConfig): AxiosPromise<ClassificationDto> {
            return localVarFp.updateClassification(requestParameters.key, requestParameters.classificationDto, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for createClassification operation in ClassificationApi.
 * @export
 * @interface ClassificationApiCreateClassificationRequest
 */
export interface ClassificationApiCreateClassificationRequest {
    /**
     * 
     * @type {string}
     * @memberof ClassificationApiCreateClassification
     */
    readonly key: string

    /**
     * 
     * @type {ClassificationDto}
     * @memberof ClassificationApiCreateClassification
     */
    readonly classificationDto: ClassificationDto
}

/**
 * Request parameters for deleteClassification operation in ClassificationApi.
 * @export
 * @interface ClassificationApiDeleteClassificationRequest
 */
export interface ClassificationApiDeleteClassificationRequest {
    /**
     * 
     * @type {string}
     * @memberof ClassificationApiDeleteClassification
     */
    readonly key: string
}

/**
 * Request parameters for findAllClassifications operation in ClassificationApi.
 * @export
 * @interface ClassificationApiFindAllClassificationsRequest
 */
export interface ClassificationApiFindAllClassificationsRequest {
    /**
     * 
     * @type {string}
     * @memberof ClassificationApiFindAllClassifications
     */
    readonly name?: string
}

/**
 * Request parameters for getChildClassifications operation in ClassificationApi.
 * @export
 * @interface ClassificationApiGetChildClassificationsRequest
 */
export interface ClassificationApiGetChildClassificationsRequest {
    /**
     * 
     * @type {string}
     * @memberof ClassificationApiGetChildClassifications
     */
    readonly key: string

    /**
     * 
     * @type {string}
     * @memberof ClassificationApiGetChildClassifications
     */
    readonly searchStr?: string
}

/**
 * Request parameters for getClassification operation in ClassificationApi.
 * @export
 * @interface ClassificationApiGetClassificationRequest
 */
export interface ClassificationApiGetClassificationRequest {
    /**
     * 
     * @type {string}
     * @memberof ClassificationApiGetClassification
     */
    readonly key: string
}

/**
 * Request parameters for getClassificationFields operation in ClassificationApi.
 * @export
 * @interface ClassificationApiGetClassificationFieldsRequest
 */
export interface ClassificationApiGetClassificationFieldsRequest {
    /**
     * 
     * @type {string}
     * @memberof ClassificationApiGetClassificationFields
     */
    readonly key: string
}

/**
 * Request parameters for setClassificationFields operation in ClassificationApi.
 * @export
 * @interface ClassificationApiSetClassificationFieldsRequest
 */
export interface ClassificationApiSetClassificationFieldsRequest {
    /**
     * 
     * @type {string}
     * @memberof ClassificationApiSetClassificationFields
     */
    readonly key: string

    /**
     * 
     * @type {{ [key: string]: ClassificationFieldRequestDto; }}
     * @memberof ClassificationApiSetClassificationFields
     */
    readonly requestBody: { [key: string]: ClassificationFieldRequestDto; }
}

/**
 * Request parameters for updateClassification operation in ClassificationApi.
 * @export
 * @interface ClassificationApiUpdateClassificationRequest
 */
export interface ClassificationApiUpdateClassificationRequest {
    /**
     * 
     * @type {string}
     * @memberof ClassificationApiUpdateClassification
     */
    readonly key: string

    /**
     * 
     * @type {ClassificationDto}
     * @memberof ClassificationApiUpdateClassification
     */
    readonly classificationDto: ClassificationDto
}

/**
 * ClassificationApi - object-oriented interface
 * @export
 * @class ClassificationApi
 * @extends {BaseAPI}
 */
export class ClassificationApi extends BaseAPI {
    /**
     * 
     * @summary Create a classification
     * @param {ClassificationApiCreateClassificationRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ClassificationApi
     */
    public createClassification(requestParameters: ClassificationApiCreateClassificationRequest, options?: RawAxiosRequestConfig) {
        return ClassificationApiFp(this.configuration).createClassification(requestParameters.key, requestParameters.classificationDto, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Delete a classification
     * @param {ClassificationApiDeleteClassificationRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ClassificationApi
     */
    public deleteClassification(requestParameters: ClassificationApiDeleteClassificationRequest, options?: RawAxiosRequestConfig) {
        return ClassificationApiFp(this.configuration).deleteClassification(requestParameters.key, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Find classifications
     * @param {ClassificationApiFindAllClassificationsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ClassificationApi
     */
    public findAllClassifications(requestParameters: ClassificationApiFindAllClassificationsRequest = {}, options?: RawAxiosRequestConfig) {
        return ClassificationApiFp(this.configuration).findAllClassifications(requestParameters.name, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get direct child classifications of a node
     * @param {ClassificationApiGetChildClassificationsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ClassificationApi
     */
    public getChildClassifications(requestParameters: ClassificationApiGetChildClassificationsRequest, options?: RawAxiosRequestConfig) {
        return ClassificationApiFp(this.configuration).getChildClassifications(requestParameters.key, requestParameters.searchStr, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get a classification
     * @param {ClassificationApiGetClassificationRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ClassificationApi
     */
    public getClassification(requestParameters: ClassificationApiGetClassificationRequest, options?: RawAxiosRequestConfig) {
        return ClassificationApiFp(this.configuration).getClassification(requestParameters.key, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get fields of classification
     * @param {ClassificationApiGetClassificationFieldsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ClassificationApi
     */
    public getClassificationFields(requestParameters: ClassificationApiGetClassificationFieldsRequest, options?: RawAxiosRequestConfig) {
        return ClassificationApiFp(this.configuration).getClassificationFields(requestParameters.key, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Set fields of classification
     * @param {ClassificationApiSetClassificationFieldsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ClassificationApi
     */
    public setClassificationFields(requestParameters: ClassificationApiSetClassificationFieldsRequest, options?: RawAxiosRequestConfig) {
        return ClassificationApiFp(this.configuration).setClassificationFields(requestParameters.key, requestParameters.requestBody, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Update or create a classification
     * @param {ClassificationApiUpdateClassificationRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ClassificationApi
     */
    public updateClassification(requestParameters: ClassificationApiUpdateClassificationRequest, options?: RawAxiosRequestConfig) {
        return ClassificationApiFp(this.configuration).updateClassification(requestParameters.key, requestParameters.classificationDto, options).then((request) => request(this.axios, this.basePath));
    }
}

