/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig, AxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { CurrencyDto } from '@domain/masterdata/model/openapi';
// @ts-ignore
import type { ErrorResponse } from '@domain/masterdata/model/openapi';
/**
 * CurrencyApi - axios parameter creator
 * @export
 */
export const CurrencyApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary Create a currency
         * @param {string} key 
         * @param {CurrencyDto} currencyDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createCurrency: async (key: string, currencyDto: CurrencyDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'key' is not null or undefined
            assertParamExists('createCurrency', 'key', key)
            // verify required parameter 'currencyDto' is not null or undefined
            assertParamExists('createCurrency', 'currencyDto', currencyDto)
            const localVarPath = `/api/md/v1/currencies/{key}`
                .replace(`{${"key"}}`, encodeURIComponent(String(key)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(currencyDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Delete a currency
         * @param {string} key 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteCurrency: async (key: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'key' is not null or undefined
            assertParamExists('deleteCurrency', 'key', key)
            const localVarPath = `/api/md/v1/currencies/{key}`
                .replace(`{${"key"}}`, encodeURIComponent(String(key)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Find currencies
         * @param {string} [name] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        findAllCurrencies: async (name?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/md/v1/currencies`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (name !== undefined) {
                localVarQueryParameter['name'] = name;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get a currency
         * @param {string} key 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCurrency: async (key: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'key' is not null or undefined
            assertParamExists('getCurrency', 'key', key)
            const localVarPath = `/api/md/v1/currencies/{key}`
                .replace(`{${"key"}}`, encodeURIComponent(String(key)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Update or create a currency
         * @param {string} key 
         * @param {CurrencyDto} currencyDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateCurrency: async (key: string, currencyDto: CurrencyDto, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'key' is not null or undefined
            assertParamExists('updateCurrency', 'key', key)
            // verify required parameter 'currencyDto' is not null or undefined
            assertParamExists('updateCurrency', 'currencyDto', currencyDto)
            const localVarPath = `/api/md/v1/currencies/{key}`
                .replace(`{${"key"}}`, encodeURIComponent(String(key)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(currencyDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CurrencyApi - functional programming interface
 * @export
 */
export const CurrencyApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = CurrencyApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary Create a currency
         * @param {string} key 
         * @param {CurrencyDto} currencyDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createCurrency(key: string, currencyDto: CurrencyDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CurrencyDto>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createCurrency(key, currencyDto, options);
            const index = configuration?.serverIndex ?? 0;
            const operationBasePath = operationServerMap['CurrencyApi.createCurrency']?.[index]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, operationBasePath || basePath);
        },
        /**
         * 
         * @summary Delete a currency
         * @param {string} key 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteCurrency(key: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteCurrency(key, options);
            const index = configuration?.serverIndex ?? 0;
            const operationBasePath = operationServerMap['CurrencyApi.deleteCurrency']?.[index]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, operationBasePath || basePath);
        },
        /**
         * 
         * @summary Find currencies
         * @param {string} [name] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async findAllCurrencies(name?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<CurrencyDto>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.findAllCurrencies(name, options);
            const index = configuration?.serverIndex ?? 0;
            const operationBasePath = operationServerMap['CurrencyApi.findAllCurrencies']?.[index]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, operationBasePath || basePath);
        },
        /**
         * 
         * @summary Get a currency
         * @param {string} key 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getCurrency(key: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CurrencyDto>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getCurrency(key, options);
            const index = configuration?.serverIndex ?? 0;
            const operationBasePath = operationServerMap['CurrencyApi.getCurrency']?.[index]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, operationBasePath || basePath);
        },
        /**
         * 
         * @summary Update or create a currency
         * @param {string} key 
         * @param {CurrencyDto} currencyDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateCurrency(key: string, currencyDto: CurrencyDto, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CurrencyDto>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateCurrency(key, currencyDto, options);
            const index = configuration?.serverIndex ?? 0;
            const operationBasePath = operationServerMap['CurrencyApi.updateCurrency']?.[index]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, operationBasePath || basePath);
        },
    }
};

/**
 * CurrencyApi - factory interface
 * @export
 */
export const CurrencyApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = CurrencyApiFp(configuration)
    return {
        /**
         * 
         * @summary Create a currency
         * @param {CurrencyApiCreateCurrencyRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createCurrency(requestParameters: CurrencyApiCreateCurrencyRequest, options?: AxiosRequestConfig): AxiosPromise<CurrencyDto> {
            return localVarFp.createCurrency(requestParameters.key, requestParameters.currencyDto, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Delete a currency
         * @param {CurrencyApiDeleteCurrencyRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteCurrency(requestParameters: CurrencyApiDeleteCurrencyRequest, options?: AxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteCurrency(requestParameters.key, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Find currencies
         * @param {CurrencyApiFindAllCurrenciesRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        findAllCurrencies(requestParameters: CurrencyApiFindAllCurrenciesRequest = {}, options?: AxiosRequestConfig): AxiosPromise<Array<CurrencyDto>> {
            return localVarFp.findAllCurrencies(requestParameters.name, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get a currency
         * @param {CurrencyApiGetCurrencyRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCurrency(requestParameters: CurrencyApiGetCurrencyRequest, options?: AxiosRequestConfig): AxiosPromise<CurrencyDto> {
            return localVarFp.getCurrency(requestParameters.key, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Update or create a currency
         * @param {CurrencyApiUpdateCurrencyRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateCurrency(requestParameters: CurrencyApiUpdateCurrencyRequest, options?: AxiosRequestConfig): AxiosPromise<CurrencyDto> {
            return localVarFp.updateCurrency(requestParameters.key, requestParameters.currencyDto, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for createCurrency operation in CurrencyApi.
 * @export
 * @interface CurrencyApiCreateCurrencyRequest
 */
export interface CurrencyApiCreateCurrencyRequest {
    /**
     * 
     * @type {string}
     * @memberof CurrencyApiCreateCurrency
     */
    readonly key: string

    /**
     * 
     * @type {CurrencyDto}
     * @memberof CurrencyApiCreateCurrency
     */
    readonly currencyDto: CurrencyDto
}

/**
 * Request parameters for deleteCurrency operation in CurrencyApi.
 * @export
 * @interface CurrencyApiDeleteCurrencyRequest
 */
export interface CurrencyApiDeleteCurrencyRequest {
    /**
     * 
     * @type {string}
     * @memberof CurrencyApiDeleteCurrency
     */
    readonly key: string
}

/**
 * Request parameters for findAllCurrencies operation in CurrencyApi.
 * @export
 * @interface CurrencyApiFindAllCurrenciesRequest
 */
export interface CurrencyApiFindAllCurrenciesRequest {
    /**
     * 
     * @type {string}
     * @memberof CurrencyApiFindAllCurrencies
     */
    readonly name?: string
}

/**
 * Request parameters for getCurrency operation in CurrencyApi.
 * @export
 * @interface CurrencyApiGetCurrencyRequest
 */
export interface CurrencyApiGetCurrencyRequest {
    /**
     * 
     * @type {string}
     * @memberof CurrencyApiGetCurrency
     */
    readonly key: string
}

/**
 * Request parameters for updateCurrency operation in CurrencyApi.
 * @export
 * @interface CurrencyApiUpdateCurrencyRequest
 */
export interface CurrencyApiUpdateCurrencyRequest {
    /**
     * 
     * @type {string}
     * @memberof CurrencyApiUpdateCurrency
     */
    readonly key: string

    /**
     * 
     * @type {CurrencyDto}
     * @memberof CurrencyApiUpdateCurrency
     */
    readonly currencyDto: CurrencyDto
}

/**
 * CurrencyApi - object-oriented interface
 * @export
 * @class CurrencyApi
 * @extends {BaseAPI}
 */
export class CurrencyApi extends BaseAPI {
    /**
     * 
     * @summary Create a currency
     * @param {CurrencyApiCreateCurrencyRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CurrencyApi
     */
    public createCurrency(requestParameters: CurrencyApiCreateCurrencyRequest, options?: RawAxiosRequestConfig) {
        return CurrencyApiFp(this.configuration).createCurrency(requestParameters.key, requestParameters.currencyDto, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Delete a currency
     * @param {CurrencyApiDeleteCurrencyRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CurrencyApi
     */
    public deleteCurrency(requestParameters: CurrencyApiDeleteCurrencyRequest, options?: RawAxiosRequestConfig) {
        return CurrencyApiFp(this.configuration).deleteCurrency(requestParameters.key, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Find currencies
     * @param {CurrencyApiFindAllCurrenciesRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CurrencyApi
     */
    public findAllCurrencies(requestParameters: CurrencyApiFindAllCurrenciesRequest = {}, options?: RawAxiosRequestConfig) {
        return CurrencyApiFp(this.configuration).findAllCurrencies(requestParameters.name, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get a currency
     * @param {CurrencyApiGetCurrencyRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CurrencyApi
     */
    public getCurrency(requestParameters: CurrencyApiGetCurrencyRequest, options?: RawAxiosRequestConfig) {
        return CurrencyApiFp(this.configuration).getCurrency(requestParameters.key, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Update or create a currency
     * @param {CurrencyApiUpdateCurrencyRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CurrencyApi
     */
    public updateCurrency(requestParameters: CurrencyApiUpdateCurrencyRequest, options?: RawAxiosRequestConfig) {
        return CurrencyApiFp(this.configuration).updateCurrency(requestParameters.key, requestParameters.currencyDto, options).then((request) => request(this.axios, this.basePath));
    }
}

