import { gBasePoint } from '@tset/shared-utils/tests/generators/basePoint'
import { gTsetSketcherBoundaries } from '@tset/shared-utils/tests/generators/tsetSketcherBoundaries'

describe('TsetSketcherBoundaries generator', () => {
  it('should generate a fake TsetSketcherBoundaries', () => {
    const actual = gTsetSketcherBoundaries()
    expect(actual.max.x).toBeDefined()
    expect(actual.max.y).toBeDefined()
    expect(actual.min.x).toBeDefined()
    expect(actual.min.y).toBeDefined()
  })
  it('should use the input values to generate the TsetSketcherBoundaries', () => {
    const min = gBasePoint()
    const max = gBasePoint()
    const actual = gTsetSketcherBoundaries({
      min,
      max,
    })
    expect(actual.min).toBe(min)
    expect(actual.max).toBe(max)
  })
})
