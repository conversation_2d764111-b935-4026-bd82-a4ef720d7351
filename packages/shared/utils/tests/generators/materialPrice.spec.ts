import { gCurrencyInfo } from '@tset/shared-utils/tests/generators/currencyInfo'
import { gMaterialPrice } from '@tset/shared-utils/tests/generators/materialPrice'

describe('MaterialPrice Generator', () => {
  it('should generate a fake MaterialPrice', () => {
    const actual = gMaterialPrice()
    expect(actual.componentName).not.toBe('')
    expect(actual.price).toBe(1)
    expect(actual.currencyInfo).toStrictEqual({})
  })

  it('should use the input values to customize the generated MaterialPrice', () => {
    const actual = gMaterialPrice({
      componentName: 'the-component-name',
      currencyInfo: gCurrencyInfo({ EUR: 123, JPY: 234, CHF: 456 }),
      price: 11,
      id: 'the-id',
      quantity: 34,
    })

    expect(actual.componentName).toBe('the-component-name')
    expect(actual.currencyInfo).toStrictEqual({ EUR: 123, JPY: 234, CHF: 456 })
    expect(actual.price).toBe(11)
    expect(actual.id).toBe('the-id')
    expect(actual.quantity).toBe(34)
  })
})
