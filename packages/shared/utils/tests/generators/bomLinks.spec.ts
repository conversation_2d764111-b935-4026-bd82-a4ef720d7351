import { gBomLink } from '@tset/shared-utils/tests/generators/bomLink'

describe('BomLink generator', () => {
  it('should generate a fake BomLink', () => {
    const actual = gBomLink()
    expect(actual.bomEntryId).toBeDefined()
    expect(actual.bomNodeId).toBeDefined()
  })

  it('should use the input values to generate the BomLink', () => {
    const actual = gBomLink({
      bomEntryId: 'i-am-a-noble-steed',
      bomNodeId: 'i-am-a-green-oger',
    })
    expect(actual.bomEntryId).toBe('i-am-a-noble-steed')
    expect(actual.bomNodeId).toBe('i-am-a-green-oger')
  })
})
