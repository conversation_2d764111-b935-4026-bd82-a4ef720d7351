import faker, { datatype } from 'faker'
import { gResultField } from './resultField'

faker.seed(123)
const { uuid } = datatype

export function gCalculationPreviewTechSpecific(): CalculationPreviewTechSpecific
export function gCalculationPreviewTechSpecific<
  C extends Partial<CalculationPreviewTechSpecific>
>(custom: C): CalculationPreviewTechSpecific & C
/**
 * CalculationPreviewTechSpecific test util generator
 *
 * @returns fake and customized CalculationPreviewTechSpecific
 */
export function gCalculationPreviewTechSpecific(
  custom?: Partial<CalculationPreviewTechSpecific>
) {
  const base = {
    shapeId: uuid(),
    kpis: [gResultField()],
    materialDisplayDesignation: gResultField(),
    accountIdentifier: 'ts',
  }
  return { ...base, ...custom }
}
