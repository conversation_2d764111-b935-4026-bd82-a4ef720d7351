import {
  includes,
  shouldNeverHappen,
} from '@tset/shared-utils/helpers/typescript'

/**
 * TypeScript helpers
 *
 * @group mandatory
 */
describe('TypeScript helpers', () => {
  describe('shouldNeverHappen | TS util to help and prevent default cases which should not happen', () => {
    //Note: I know this is a stupid test!
    test('the Error message should start with `This should never happen:`', () => {
      //Note: we hijack TS for the culprit
      const actual = shouldNeverHappen('whatever message', 'never' as never)
      expect(actual.message.startsWith('This should never happen:')).toBe(true)
    })

    test('the Error message should contain the passed message', () => {
      const expected = 'this message will be in the Error message'
      //Note: we hijack TS for the culprit
      const actual = shouldNeverHappen(expected, 'never' as never)
      expect(actual.message).toContain(expected)
    })

    test('the Error message should end with "`culprit` should not exist."', () => {
      const expected = 'culprit'
      //Note: we hijack TS for the culprit
      const actual = shouldNeverHappen('whatever message', expected as never)
      expect(actual.message.endsWith(`${expected} is not valid.`)).toBe(true)
    })
  })

  describe('includes | checks a value is part of list of values adding TS support', () => {
    it('should return `true` if `needle` is in `haystack`', () => {
      expect(
        includes(['i', 'contain', 'needle', 'inside', 'me'], 'inside')
      ).toBe(true)
    })

    it('should return `false` if `needle` is NOT in `haystack`', () => {
      expect(
        includes(['maybe there', 'not there', 'are you there?'], 'there')
      ).toBe(false)
    })
  })
})
