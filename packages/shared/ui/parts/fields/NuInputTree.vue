<template>
  <TsetHierarchicalSelect
    ref="tsetHierarchicalSelect"
    :values="data"
    :model-value="String(props.field.value)"
    :is-loading="isPending"
    :placeholder="getPlaceholderForHierarchicalField(field)"
    :tset-table-editor="tsetTableEditor"
    :has-error="hasError"
    @update:model-value="emit('change', { ...props.field, value: $event })"
    @closed="emit('focusout')"
  >
    <template #system-value="{ close }">
      <TsetHierarchicalSelectEntry
        v-data-test:systemValueEntry
        v-if="systemValue"
        :node="systemValue"
        is-system-value
        :disabled="systemValue?.key === notAvailable"
        @toggle="
          () => {
            close()
            emit('restore')
          }
        "
      >
        <template #system-value-badge>
          <TsetBadge
            v-data-test:systemValueBadge
            v-if="systemValue"
            :label="
              isBulkActions
                ? $t('statics.systemValue')
                : capitalized($t('statics.fromSystem'))
            "
            size="small"
            type="gray"
            class="rounded-full"
            :class="{
              'mr-auto': isBulkActions,
            }"
          />
        </template>
      </TsetHierarchicalSelectEntry>
    </template>
  </TsetHierarchicalSelect>
</template>

<script setup lang="ts">
import { $t } from '@shared/translation/nuTranslation'
import TsetBadge from '@tset/design/atoms/TsetBadge'
import type { TsetHierarchicalSelectItem } from '@tset/design/molecules/TsetHierarchicalSelectNavigator/TsetHierarchicalSelect.types'
import TsetHierarchicalSelectEntry from '@tset/design/molecules/TsetHierarchicalSelectNavigator/TsetHierarchicalSelectEntry.vue'
import TsetHierarchicalSelect from '@tset/design/organisms/TsetHierarchicalSelect/TsetHierarchicalSelect.vue'
import { capitalized } from '@tset/design/tokens/Typography/capitalized'
import { useTreeQuery } from '@tset/shared-api/tree.api'
import { getPlaceholderForHierarchicalField } from '@tset/shared-utils/helpers/field'
import { isFieldManuallyOverridden } from '@tset/shared-utils/helpers/overriden'
import { computed, ref } from 'vue'

//#region PROPS
const props = defineProps<{
  field: ResultField
  tsetTableEditor?: boolean
  hasError?: boolean
}>()
//#endregion PROPS

//#region EMIT
const emit = defineEmits<{
  change: [ResultField]
  restore: []
  focusout: []
}>()
//#endregion EMIT

//#region REFS
const tsetHierarchicalSelect =
  ref<Nullable<InstanceType<typeof TsetHierarchicalSelect>>>(null)
//#endregion REFS

//#region Data
const { isPending, data } = useTreeQuery(props.field)
//#endregion Data

//#region SYSTEM VALUE
const notAvailable = 'notAvailable'

const isBulkActions = computed(
  () => props.field.metaInfo?.section === 'bulkActions'
)

const systemValue = computed<TsetHierarchicalSelectItem | undefined>(() => {
  if (isBulkActions.value) {
    return {
      key: '-',
      name: props.field.systemValue as string,
    }
  }

  if (!isFieldManuallyOverridden(props.field)) return

  return (
    data.value?.find(({ key }) => key === props.field.systemValue) ?? {
      key: notAvailable,
      name: $t('selectables.optionNotAvailable'),
    }
  )
})
//#endregion SYSTEM VALUE
</script>

<script lang="ts">
export default {
  name: 'NuInputTree',
}
</script>
