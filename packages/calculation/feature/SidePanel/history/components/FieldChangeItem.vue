<template>
  <div
    class="flex w-full items-center gap-5 overflow-hidden rounded-[4px] px-8 py-4"
  >
    <span
      v-tooltip="label"
      class="text-caption-light min-w-50 truncate text-gray-dark"
    >
      {{ label }}
    </span>
    <span
      v-tooltip="tooltip"
      v-data-test:value
      class="text-body-semibold ml-auto truncate text-right"
    >
      {{ value }}
    </span>
    <span v-if="unit" v-data-test:unit class="text-body-light shrink-0">
      {{ unit }}
    </span>
  </div>
</template>

<script setup lang="ts">
//#region PROPS
defineProps<{
  label: string
  tooltip: string
  value: string
  unit?: string
}>()
//#endregion PROPS
</script>
<script lang="ts">
export default {
  name: 'FieldChangeItem',
}
</script>
