import type { MockedFunction } from 'vitest'
import { UserTsetDataStore } from '../data/store'
import {
  buildErrorSection,
  errorLabel,
  errorMaxLength,
  formatErrorSection,
  labelSet,
  prepareErrorReport,
} from './usertset'

//#region MOCKS
vi.mock('../data/store')
//#endregion MOCKS

//#region HELPERS
function generateErrorMessage() {
  let message = ''
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let length = 0

  while (length <= errorMaxLength) {
    message += characters.charAt(Math.floor(Math.random() * characters.length))
    length++
  }

  return message
}
//#endregion HELPERS
describe('usertset helpers', () => {
  it('all labels should start with a "-" to enable JIRA formatting properly', () => {
    Object.values(labelSet).forEach((label) => {
      expect(label.startsWith('- ')).toBe(true)
    })
  })

  describe('formatErrorSection', () => {
    it(`should have "${errorLabel}" as title`, () => {
      const error = {
        message: 'Nulla laboris nulla magna labore.',
      }
      const actual = buildErrorSection(error)
      expect(actual.title).toBe(errorLabel)
    })

    it('should have the original message as an item', () => {
      const error = {
        message:
          'Consectetur voluptate sit culpa cupidatat quis eiusmod ad consequat eiusmod non quis culpa incididunt non.',
      }
      const actual = buildErrorSection(error)
      expect(actual.items.length).toBe(1)
      expect(actual.items[0].text).toBe(error.message)
    })

    it('should display "null" as message when message is null in the error provided', () => {
      const error = {
        message: null,
      }
      const actual = buildErrorSection(error)
      expect(actual.items.length).toBe(1)
      expect(actual.items[0].text).toBe('null')
    })

    it('should have the error as an item when available in error', () => {
      const error = {
        error:
          'Try to parse the USB panel, maybe it will synthesize the 1080p sensor!',
        message:
          'If we input the matrix, we can get to the SDD capacitor through the primary SAS port!',
      }
      const { items } = buildErrorSection(error)
      expect(items).toContainEqual({
        label: labelSet.error,
        text: error.error,
      })
    })

    it('should have the error code as an item when available in error', () => {
      const error = {
        errorCode:
          'Try to parse the USB panel, maybe it will synthesize the 1080p sensor!',
        message:
          'If we input the matrix, we can get to the SDD capacitor through the primary SAS port!',
      }
      const { items } = buildErrorSection(error)
      expect(items).toContainEqual({
        label: labelSet.errorCode,
        text: error.errorCode,
      })
    })

    it('should have the exception as an item when available in error', () => {
      const error = {
        exception: 'optical',
        message:
          'If we input the matrix, we can get to the SDD capacitor through the primary SAS port!',
      }
      const { items } = buildErrorSection(error)
      expect(items).toContainEqual({
        label: labelSet.exception,
        text: error.exception,
      })
    })

    it('should have the request id as an item when available in error', () => {
      const error = {
        requestId: 'ec773b6d-30bc-4443-a22f-6a7dc43de06e',
        message:
          'If we input the matrix, we can get to the SDD capacitor through the primary SAS port!',
      }
      const { items } = buildErrorSection(error)
      expect(items).toContainEqual({
        label: labelSet.requestId,
        text: error.requestId,
      })
    })

    it('should have the path as an item when available in error', () => {
      const error = {
        path: '/opt/share/ability_venezuela.ott',
        message:
          'If we input the matrix, we can get to the SDD capacitor through the primary SAS port!',
      }
      const { items } = buildErrorSection(error)
      expect(items).toContainEqual({
        label: labelSet.path,
        text: error.path,
      })
    })

    it('should have the timestamp as an item when available in error', () => {
      const error = {
        timestamp: '2021-05-31T06:43:17.663Z',
        message:
          'If we input the matrix, we can get to the SDD capacitor through the primary SAS port!',
      }
      const { items } = buildErrorSection(error)
      expect(items).toContainEqual({
        label: labelSet.timestamp,
        text: error.timestamp,
      })
    })

    it('should NOT have the trace as an item when available in error', () => {
      const error = {
        trace: 'Enim culpa ad duis culpa exercitation occaecat.',
        message:
          'If we input the matrix, we can get to the SDD capacitor through the primary SAS port!',
      }
      const { items } = buildErrorSection(error)
      expect(items).not.toContainEqual({
        label: labelSet.trace,
        text: error.trace,
      })
    })

    it('should have the message in the last position', () => {
      const error = {
        message: 'Deserunt dolor consectetur minim pariatur veniam.',
        errorCode: 'Regional Sudanese Manat',
        requestId: '811e4c89-583e-4d75-a52f-44ea6895c18e',
      }
      const { items } = buildErrorSection(error)
      expect(items[items.length - 1]).toStrictEqual({
        label: labelSet.message,
        text: error.message,
      })
    })

    it('should truncate the message if too long', () => {
      const error = {
        message: 'a'.repeat(errorMaxLength + 200),
      }
      const { items } = buildErrorSection(error)
      expect(items[0].text.length).toBe(errorMaxLength)
    })

    it.skip('should truncate the message a cleverer way', () => {
      // I think as we potentially have an object here the trace can also be very long and is probably the original problem when the error was cast to a string
      // we should probably manage length a cleverer way
    })

    it('should adapt the error section title when message is truncated', () => {
      const error = {
        message: 'a'.repeat(errorMaxLength + 200),
      }
      const { items } = buildErrorSection(error)
      expect(items[0].label).toBe(labelSet.truncatedMessage)
    })
  })

  describe('formatting the error section', () => {
    it('should format the section as a string', () => {
      const errorSection = {
        title: errorLabel,
        items: [
          {
            label: labelSet.requestId,
            text: '93c75373-e584-4a32-87a3-80b136f0e851',
          },
          {
            label: labelSet.message,
            text: 'Laboris aute nulla labore velit officia exercitation eiusmod mollit dolor id laborum labore ea.',
          },
        ],
      }

      const actual = formatErrorSection(errorSection)
      const expected = `\n${labelSet.requestId}: ${errorSection.items[0].text}\n${labelSet.message}: ${errorSection.items[1].text}`
      expect(actual).toBe(expected)
    })
  })

  describe('prepareErrorReport', () => {
    it(`- adds attachment to data store if error message is provided and is over ${errorMaxLength} characters long`, () => {
      const errorMessage = generateErrorMessage()
      const addAttachmentMock =
        UserTsetDataStore.addAttachment as MockedFunction<
          typeof UserTsetDataStore.addAttachment
        >

      prepareErrorReport(errorMessage)

      expect(addAttachmentMock).toHaveBeenCalled()
    })
    it('- sets error in data store if error is provided', () => {
      const errorMessage = 'foo'
      const setErrorMock = UserTsetDataStore.setError as MockedFunction<
        typeof UserTsetDataStore.setError
      >

      prepareErrorReport(errorMessage, {
        message: errorMessage,
      })

      expect(setErrorMock).toHaveBeenCalled()
    })
  })
})
