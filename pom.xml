<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <!--Keep in sync with spring-boot.version property!-->
        <version>3.2.8</version>
        <relativePath/>
        <!-- lookup parent from repository -->
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.nu.bom</groupId>
    <artifactId>neumann-build-all</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>neumann-build-all</name>
    <description>Core Service for BoM Management</description>

    <packaging>pom</packaging>

    <properties>
        <java.version>17</java.version>
        <kotlin.version>1.9.23</kotlin.version>
        <kotlin.jvm.target>17</kotlin.jvm.target>
        <!--Keep in sync with spring-boot-starter-parent version above!-->
        <spring-boot.version>3.2.8</spring-boot.version>
        <spring-cloud.version>2023.0.0</spring-cloud.version>
        <testcontainers.version>1.19.7</testcontainers.version>
        <modernizer-maven-plugin.version>2.8.0</modernizer-maven-plugin.version>
        <querydsl.version>5.1.0</querydsl.version>
        <mongock.version>5.4.0</mongock.version>
        <resilience4j.version>2.2.0</resilience4j.version>
        <nulib.version>0.1.264</nulib.version>
        <quantitytTypes.version>0.1.3</quantitytTypes.version>
        <surefire.version>3.2.5</surefire.version>
        <archunit-junit5.version>1.2.1</archunit-junit5.version>
        <konsist.version>0.17.3</konsist.version>
        <sonar.cpd.exclusions>**/neumann-core/src/main/kotlin/com/nu/bom/core/manufacturing/commercialcalculation/operationConfiguration/tsetDefaultConfiguration/legacy/**</sonar.cpd.exclusions>
        <sonar.exclusions>${project.basedir}/test-support/**</sonar.exclusions>
        <sonar.coverage.exclusions>**/neumann-clients/**,**/technologies/lookups/**</sonar.coverage.exclusions>
        <sonar.kotlin.detekt.reportPaths>${project.build.directory}/detekt.xml</sonar.kotlin.detekt.reportPaths>
        <sonar.coverage.jacoco.xmlReportPaths>
            ${project.basedir}/../coverage-report-aggregate/target/site/jacoco-aggregate/jacoco.xml
        </sonar.coverage.jacoco.xmlReportPaths>
        <argLine/> <!-- needed for proper jacoco argline overwriting with @{argLine}, see https://stackoverflow.com/questions/46489455/append-the-value-of-argline-param-in-maven-surefire-plugin -->
    </properties>

    <modules>
        <module>neumann-clients</module>
        <module>nexar-api</module>
        <module>neumann-core</module>
        <module>rough-part-technologies</module>
        <module>neumann-app-bundle</module>
        <module>test-support</module>
        <module>integration-tests</module>
        <module>readable-exceptions</module>
        <module>coverage-report-aggregate</module>
    </modules>

    <profiles>
        <profile>
            <id>mac-m1</id>
            <activation>
                <os>
                    <family>mac</family>
                    <arch>aarch64</arch>
                </os>
            </activation>
            <dependencies>
                <!-- fix for M1 Macs -->
                <dependency>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-resolver-dns-native-macos</artifactId>
                    <version>${netty.version}</version>
                    <classifier>osx-aarch_64</classifier>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>ci</id>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.owasp</groupId>
                            <artifactId>dependency-check-maven</artifactId>
                            <version>9.0.9</version>
                            <dependencies>
                                <dependency>
                                    <groupId>org.postgresql</groupId>
                                    <artifactId>postgresql</artifactId>
                                    <version>42.5.4</version>
                                </dependency>
                            </dependencies>
                            <configuration>
                                <databaseDriverName>org.postgresql.Driver</databaseDriverName>
                                <connectionString>${env.VULNERABILITYDB_URL}</connectionString>
                                <databaseUser>${env.VULNERABILITYDB_USER}</databaseUser>
                                <databasePassword>${env.VULNERABILITYDB_PASSWORD}</databasePassword>
                                <knownExploitedUrl>${env.VULNERABILITYDB_EXPLOIT_URL}</knownExploitedUrl>
                                <suppressionFiles>
                                    <supressionFile>./supressions-critical.xml</supressionFile>
                                    <supressionFile>./supressions-high.xml</supressionFile>
                                </suppressionFiles>
                            </configuration>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
    </profiles>

    <dependencies>
        <dependency>
            <groupId>org.springframework.plugin</groupId>
            <artifactId>spring-plugin-core</artifactId>
            <version>3.0.0</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>4.4.0</version>
            </dependency>
            <dependency>
                <groupId>com.nu.lib</groupId>
                <artifactId>quantitytypes</artifactId>
                <version>${quantitytTypes.version}</version>
            </dependency>
            <dependency>
                <groupId>com.nu.lib</groupId>
                <artifactId>quantitytypes-annotations</artifactId>
                <version>${quantitytTypes.version}</version>
            </dependency>
            <dependency>
                <groupId>com.nu.bom</groupId>
                <artifactId>core</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.nu.bom</groupId>
                <artifactId>neumann-clients</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.nu.bom</groupId>
                <artifactId>nexar-api</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.nu.bom</groupId>
                <artifactId>core</artifactId>
                <classifier>tests</classifier>
                <type>test-jar</type>
                <scope>test</scope>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.nu.bom</groupId>
                <artifactId>rough-part-technologies</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.nu.bom</groupId>
                <artifactId>test-support</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.nu.bom</groupId>
                <artifactId>lit</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.nu.bom</groupId>
                <artifactId>app-bundle</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.nu.bom</groupId>
                <artifactId>readable-exceptions</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.nu.bom</groupId>
                <artifactId>integration-tests</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.nu.lib</groupId>
                <artifactId>kubernetes-client</artifactId>
                <version>${nulib.version}</version>
            </dependency>
            <dependency>
                <groupId>com.nu.lib</groupId>
                <artifactId>bomrad-common</artifactId>
                <version>${nulib.version}</version>
            </dependency>
            <dependency>
                <groupId>com.nu.lib</groupId>
                <artifactId>security-common</artifactId>
                <version>${nulib.version}</version>
            </dependency>
            <dependency>
                <groupId>com.nu.lib</groupId>
                <artifactId>http-service</artifactId>
                <version>${nulib.version}</version>
            </dependency>
            <dependency>
                <groupId>com.nu.lib</groupId>
                <artifactId>utils</artifactId>
                <version>${nulib.version}</version>
            </dependency>
            <dependency>
                <groupId>com.nu.lib</groupId>
                <artifactId>reactive-utils</artifactId>
                <version>${nulib.version}</version>
            </dependency>
            <dependency>
                <groupId>com.nu.lib</groupId>
                <artifactId>test-helper</artifactId>
                <version>${nulib.version}</version>
            </dependency>
            <dependency>
                <groupId>com.nu.lib</groupId>
                <artifactId>engine-common</artifactId>
                <version>${nulib.version}</version>
            </dependency>
            <dependency>
                <groupId>com.nu.lib</groupId>
                <artifactId>public-api</artifactId>
                <version>${nulib.version}</version>
            </dependency>
            <dependency>
                <groupId>com.nu.lib</groupId>
                <artifactId>authentication-filter</artifactId>
                <version>${nulib.version}</version>
            </dependency>
            <dependency>
                <groupId>com.nu.lib</groupId>
                <artifactId>masterdata-common</artifactId>
                <version>${nulib.version}</version>
            </dependency>
            <!-- kotlin version -->
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib-jdk8</artifactId>
                <version>${kotlin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-reflect</artifactId>
                <version>${kotlin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-test</artifactId>
                <version>${kotlin.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.awaitility</groupId>
                <artifactId>awaitility-kotlin</artifactId>
                <version>4.2.0</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.wiremock</groupId>
                <artifactId>wiremock-standalone</artifactId>
                <version>3.4.2</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>io.mongock</groupId>
                <artifactId>mongock-bom</artifactId>
                <version>${mongock.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>2.2</version>
            </dependency>

            <dependency>
                <groupId>com.datadoghq</groupId>
                <artifactId>dd-trace-api</artifactId>
                <version>LATEST</version>
            </dependency>

            <!-- Test containers support -->
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>testcontainers-bom</artifactId>
                <version>${testcontainers.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.mock-server</groupId>
                <artifactId>mockserver-netty-no-dependencies</artifactId>
                <version>5.15.0</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.mockito.kotlin/mockito-kotlin -->
            <dependency>
                <groupId>org.mockito.kotlin</groupId>
                <artifactId>mockito-kotlin</artifactId>
                <version>5.2.1</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>io.mockk</groupId>
                <artifactId>mockk</artifactId>
                <version>1.10.2</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.8.13</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>prepare-agent</goal>
                                <goal>report</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                 <plugin>
                    <groupId>com.github.gantsign.maven</groupId>
                    <artifactId>ktlint-maven-plugin</artifactId>
                    <version>1.12.1</version>
                </plugin>
                <plugin>
                  <groupId>org.gaul</groupId>
                  <artifactId>modernizer-maven-plugin</artifactId>
                  <version>${modernizer-maven-plugin.version}</version>
                  <executions>
                    <execution>
                      <id>modernizer</id>
                      <phase>package</phase>
                      <goals>
                        <goal>modernizer</goal>
                      </goals>
                    </execution>
                  </executions>
                  <configuration>
                    <javaVersion>11</javaVersion>
                  </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>3.1.0</version>
                    <executions>
                        <execution>
                            <!-- This can be run separately with mvn antrun:run@detekt -->
                            <id>detekt</id>
                            <phase>verify</phase>
                            <configuration>
                                <skip>${detekt.skip}</skip>
                                <target name="detekt">
                                    <java taskname="detekt" dir="${basedir}"
                                          fork="true"
                                          failonerror="false"
                                          classname="io.gitlab.arturbosch.detekt.cli.Main"
                                          classpathref="maven.plugin.classpath">
                                        <arg value="--input"/>
                                        <arg value="${basedir}/src/main/kotlin"/>
                                        <arg value="--report"/>
                                        <arg value="xml:${project.build.directory}/detekt.xml"/>
                                        <arg value="--config"/>
                                        <arg value="${basedir}/detekt.yml"/>
                                    </java>
                                </target>
                            </configuration>
                            <goals>
                                <goal>run</goal>
                            </goals>
                        </execution>
                    </executions>
                    <dependencies>
                        <dependency>
                            <groupId>io.gitlab.arturbosch.detekt</groupId>
                            <artifactId>detekt-cli</artifactId>
                            <version>1.22.0-RC2</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${surefire.version}</version>
                    <configuration>
                        <includes>
                            <include>**/*.class</include>
                        </includes>
                        <groups>${nu.test.included.groups}</groups>
                        <excludedGroups>${nu.test.excluded.groups}</excludedGroups>
                        <useSystemClassLoader>false</useSystemClassLoader>
                        <forkCount>3</forkCount>
                        <reuseForks>true</reuseForks>
                        <argLine>@{argLine} -Xmx8G</argLine>
                        <parallel>classesAndMethods</parallel>
                        <threadCount>2</threadCount>
                        <perCoreThreadCount>true</perCoreThreadCount>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
                <version>${kotlin.version}</version>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <jvmTarget>${kotlin.jvm.target}</jvmTarget>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.owasp</groupId>
                <artifactId>dependency-check-maven</artifactId>
                <version>9.0.9</version>
                <configuration>
                    <suppressionFiles>
                        <supressionFile>./supressions-critical.xml</supressionFile>
                        <supressionFile>./supressions-high.xml</supressionFile>
                    </suppressionFiles>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>tset-public</id>
            <name>tset-public</name>
            <url>https://nexus.tset.cloud/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>tset-public</id>
            <name>tset-public</name>
            <url>https://nexus.tset.cloud/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>
