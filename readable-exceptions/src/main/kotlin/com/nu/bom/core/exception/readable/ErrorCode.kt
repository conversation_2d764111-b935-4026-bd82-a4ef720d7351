package com.nu.bom.core.exception.readable

/**
 * Shared error code enum library
 * */
enum class ErrorCode {
    PROJECT_KEY_ALREADY_EXISTS,
    PROJECT_KEY_INVALID,
    PROJECT_NAME_INVALID,
    PROJECT_NOT_FOUND,
    PROJECT_KEY_OR_NAME_NOT_FOUND,
    PROJECT_KEY_FOUND_NAME_MISMATCH,
    PROJECT_KEY_OR_NAME_MUST_BE_PRESENT,
    BOM_NODE_NOT_FOUND,
    BRANCH_NOT_FOUND,
    <PERSON><PERSON>CH_STATE_UNEXPECTED,
    MANUFACTURING_ENTITY_NOT_FOUND,
    UNSUPPORTED_OPERATION,
    ACCESS_DENIED,
    INVALID_INPUT,
    PART_NOT_FOUND,
    LOOKUP_NOT_FOUND,
    SHAPE_NOT_FOUND,
    SHAPE_NOT_SUPPORTED,
    RESOURCE_NOT_FOUND,
    LOOKUP_ENTRY_NOT_FOUND,
    MU<PERSON><PERSON>LE_LOOKUP_ENTRIES_FOUND,
    CONTAMINATED_FILE,
    MASTER_DATA_TYPE_INVALID,
    MASTER_DATA_REQUIRED_FIELD_MISSING,
    MASTER_DATA_NOT_FOUND,
    MASTER_DATA_DELETE_RESTORE_INSTEAD,
    LINKED_GLOBAL_MASTER_DATA_NOT_FOUND,
    UNSUPPORTED_CURRENCY,
    FAIL_ON_DIRTY,
    MERGE_NEEDED,
    TEMPLATE_NOT_FOUND,
    NUMERIC_INPUT_EXCEEDS_LIMIT,
    SERVICE_UNAVAILABLE,
    INVALID_NUMBER_VALUE_CONVERSION,
    INVALID_QUANTITY_VALUE,
    LAZY_MIGRATION_ERROR,
    SERVICE_TIMEOUT,
    SERVICE_FAILED_DUE_TO_UNSUPPORTED_INPUT,
    NESTING_FAILED_DUE_TO_UNSUPPORTED_INPUT,
    MANDATORY_FIELDS_MISSING,
    MANDATORY_TECHNOLOGY_FIELD_MISSING,
    ACCOUNT_NAME_NOT_FOUND,
    ACCOUNT_ID_NOT_FOUND,
    ACCOUNT_CREATION_FAILED,
    COPY_PASTE_FAILED_REORDER,
    COPY_PASTE_FAILED,
    BOM_NODE_SNAPSHOT_NOT_FOUND,
    SIMULATION_NOT_FOUND,
    DELTA_COMPARISON_NOT_FOUND,
    PAYLOAD_TOO_LARGE,
    MONGO_DOCUMENT_TOO_LARGE,
    CREATION_INVALID_SUBENTITY,
    DELETE_ENTITY_ROOT_FORBIDDEN,
    ATTACHMENT_NOT_FOUND,
    TURNING_INDUCTIVE_HARDENING_NO_LINES,
    TURNING_HARD_TURNING_NO_HARDENING,
    TURNING_SKETCH_SCALING_FAILURE,
    TURNING_SKETCH_INVALID_INPUTS,
    UNSUPPORTED_MEDIA_TYPE,
    COPY_TARGET_IS_MISSING_CURRENCY,
    COPY_TARGET_TECHNOLOGY_MODEL_INCOMPATIBLE,
    CALCULATION_MODULE_ERROR,
    COUNTER_NOT_AVAILABLE,
    FIELD_CALCULATION_ERROR,
    MERGE_ERROR,
    IMPORT_ERROR,
    CLAIM_NOT_FOUND,
    REFKEY_PARSE_EXCEPTION,
    ENTITY_TYPE_ERROR,
    VERSION_ERROR,
    COUNTER_GROUP_PARSE_EXCEPTION,
    NOT_ADMIN_USER,
    DATA_ITEM_NOT_FOUND,
    EXPORT_FORMAT_UNKNOWN,
    NON_EXHAUSTIVE_MATCH_ERROR,
    MASTERDATA_NOT_FOUND, // TODO migrate lookup table exceptions
    NODE_IN_WRONG_STATE, // legacy non-bomrads
    FIELD_CONVERSION_ERROR,
    INCOMPATIBLE_USER_INPUTS,
    TURNING_HARDENED_RAW_PART,
    TURNING_PART_TOO_BIG_FOR_MACHINE,
    TRANSPORT_PART_TOO_BIG,
    TRANSPORT_PART_TOO_HEAVY,
    GROUP_ID_OUTSIDE_GROUP_CONTEXT,
    STEP_ALREADY_ADDED_TO_LINE,
    COST_MODULE_GENERATED_LINE,
    EXTERNAL_STEP_IN_LINE,
    STEPS_MUST_BE_CONSECUTIVE_LINE,
    COST_MODULE_GENERATED_ENTITY_NOT_REORDERABLE,
    FOLDER_NAME_ALREADY_EXISTS,
    FOLDER_NOT_FOUND,
    WORKSPACE_NAME_ALREADY_EXISTS,
    ERR_DEFAULT_WORKSPACE_CREATION,
    WORKSPACE_NOT_FOUND,
    ROLE_ALREADY_EXISTS,
    ROLE_INVALID,
    ROLE_SELF_OWNER_REMOVED,
    ROLE_ALL_OWNER_REMOVED,
    INCOMPATIBLE_TECHNOLOGIES,
    ZENDESK_ERROR,
    FTI_RESPONSE_EXCEPTION,
    BULK_ACTION_NOT_FOUND,
    WIZARD_INTERNAL_EXCEPTION,
    PUBLIC_API_BAD_REQUEST,
    CONFIGURATION_NOT_FOUND,
    NODE_IS_PROTECTED,
    NODE_IS_NOT_ROOT_NODE,
    NODE_IS_ALREADY_PROTECTED,
    DUPLICATED_EFFECTIVITY_MAPPING,
    CLASSIFICATION_NOT_SUPPORTED,
    INVALID_MATERIAL_TECH_COMBINATION,
}
