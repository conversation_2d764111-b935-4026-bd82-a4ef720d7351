### create detail in reference data
POST {{baseUrl}}/api/md/v1/details
Content-Type: application/json
Authorization: Bearer {{auth_token}}

[
  {
    "active": "true",
    "effectivities": {
      "validFromVolume": {
          "type": "numeric",
          "numeric": 200005,
          "unit": "pcs"
      },
      "overheadMethod": {
          "type": "lov",
          "lovEntryKey": "BUILD_TO_PRINT_AUTO"
      }
    },
    "headerKey": "Tset.SalesAndGeneralAdministrationCosts.Manufacturing",
    "value": {
      "numeric": 31,
      "type": "numeric",
      "unit": "percentage"
    }
  }
]


### create detail simple
POST {{baseUrl}}/api/md/v1/details
Content-Type: application/json
Authorization: Bearer {{auth_token}}

[
  {
    "effectivities": {
      "LH-FD1": {
        "numeric": "100.3",
        "unit": "unit1"
      },
      "LH-FD2": {
        "lovEntryKey": "LH-LE2"
      }
    },
    "headerKey": "LH-HEADER1",
    "value":  {
      "numeric": 333.0
    },
    "active": "true"
  }
]

### create detail complex
POST {{baseUrl}}/api/md/v1/details
Content-Type: application/json
Authorization: Bearer {{auth_token}}

[
{
  "effectivities": {
    "valid from": {
      "date": "2023-07-19"
    },
    "turnover (meur)": {
      "numeric": "23.3",
      "unit": "pcs"
    },
    "overhead method": {
      "lovEntryKey": "NO_OVERHEADS"
    },
    "sector": {
      "lovEntryKey": "Food industry"
    },
    "location": {
      "treeNodeKey": "Italy"
    }
  },
  "headerKey": "Profit",
  "value":  {
    "numeric": 123
  },
  "active": "true"
}
]
