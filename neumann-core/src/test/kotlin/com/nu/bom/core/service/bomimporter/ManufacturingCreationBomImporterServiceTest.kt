package com.nu.bom.core.service.bomimporter

import com.nu.bom.core.CalculationTestBase
import com.nu.bom.core.api.dtos.FieldConversionService
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostManufacturedMaterial
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetProcurementType
import com.nu.bom.core.manufacturing.entities.BomEntry
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Currency
import com.nu.bom.core.manufacturing.fieldTypes.CustomProcurementType
import com.nu.bom.core.manufacturing.fieldTypes.Date
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYearsUnit
import com.nu.bom.core.manufacturing.service.asEntityClass
import com.nu.bom.core.repository.WizardRepository
import com.nu.bom.core.service.BomNodeService
import com.nu.bom.core.service.ManufacturingCalculationService
import com.nu.bom.core.service.ManufacturingCreationService
import com.nu.bom.core.service.ManufacturingEntityFactoryService
import com.nu.bom.core.service.ProjectService
import com.nu.bom.core.utils.AccountUtil
import com.nu.bom.core.utils.assertEqualObjectsInCollection
import com.nu.bomrads.dto.admin.ClientAccountDTO
import com.nu.bomrads.dto.admin.ProjectDTO
import com.nu.bomrads.id.AccountId
import com.nu.bomrads.id.ProjectId
import com.tset.bom.clients.bomimporter.QuantityType
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.ArgumentCaptor
import org.mockito.Captor
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import reactor.core.publisher.Flux
import reactor.util.function.Tuples
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate

class ManufacturingCreationBomImporterServiceTest : CalculationTestBase() {
    private lateinit var importService: ManufacturingCreationBomImporterService
    private lateinit var manufacturingEntityFactoryService: ManufacturingEntityFactoryService

    @Captor
    private lateinit var initialCaptor: ArgumentCaptor<Map<String, FieldResultStar>>

    @Captor
    private lateinit var overwriteCaptor: ArgumentCaptor<Map<String, FieldResultStar>>

    @BeforeEach
    fun setup() {
        MockitoAnnotations.openMocks(this)
        val bomNodeService: BomNodeService = mock()
        val projectService: ProjectService = mock()
        val bomImporterService: BomImporterService = mock()
        val manufacturingCreationService: ManufacturingCreationService = mock()
        val wizardRepository: WizardRepository = mock()
        manufacturingEntityFactoryService = mock()
        whenever(
            manufacturingEntityFactoryService.createEntity(
                any(),
                any(),
                anyOrNull(),
                any(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                any(),
                any(),
                anyOrNull(),
                anyOrNull(),
                any(),
                any(),
                any(),
                any(),
            ),
        ).thenReturn(
            addObject(
                name = "name",
                clazz = BomEntry::class.java,
                fields =
                    mapOf(
                        "quantity" to Pieces.ONE,
                        "stepId" to Text("stepId"),
                    ),
            ),
        )
        val manufacturingCalculationService: ManufacturingCalculationService = mock()
        val fieldConversionService: FieldConversionService = mock()
        importService =
            ManufacturingCreationBomImporterService(
                bomNodeService,
                entityManager,
                fieldFactoryService,
                projectService,
                bomImporterService,
                manufacturingCreationService,
                wizardRepository,
                manufacturingEntityFactoryService,
                manufacturingCalculationService,
                fieldConversionService,
                ccyService,
                mdBasicDataService,
                operationConfigService,
            )
    }

    @ParameterizedTest
    @ValueSource(strings = ["partDescription", "userNotes"])
    fun `field should not be used as a input but as an overwrite`(fieldName: String) {
        val accessCheck = AccountUtil.dummyAccessCheck()
        val clazz = ManufacturingEntity::class.java.kotlin
        val project =
            ProjectDTO(
                ProjectId(),
                name = "name",
                key = "key",
                account = ClientAccountDTO(AccountId(), version = 1, key = "acc_key", environment = "env"),
                currency = "EUR",
                createdBy = "Created By",
                lastModifiedBy = "Last Modified By",
                created = Instant.now(),
                lastModified = Instant.now(),
            )
        importService
            .createManu(
                accessCheck,
                project,
                clazz,
                mapOf(
                    "partDescription" to Text("description"),
                    "location" to Text("Germany (with EEG Apportionment)"),
                    "lifeTime" to TimeInYears(5.toBigDecimal(), TimeInYearsUnit.YEAR),
                    "peakUsableProductionVolumePerYear" to QuantityUnit(55_000.toBigDecimal()),
                    "averageUsableProductionVolumePerYear" to QuantityUnit(70000.toBigDecimal()),
                    "dimension" to Dimension(Dimension.Selection.NUMBER),
                    "userNotes" to Text("comment"),
                ),
            )

        verify(manufacturingEntityFactoryService).createEntity(
            eq(clazz.simpleName!!),
            eq(Entities.MANUFACTURING),
            eq(clazz.asEntityClass()),
            eq(
                hashMapOf(
                    "isPart" to true,
                    "isPartModelConverted" to true,
                    "partDesignation" to "",
                    "partNumber" to "",
                    "key" to project.key,
                    "user" to accessCheck.userId,
                ),
            ),
            org.mockito.kotlin.isNull(),
            org.mockito.kotlin.isNull(),
            org.mockito.kotlin.isNull(),
            capture(initialCaptor),
            eq(0),
            org.mockito.kotlin.isNull(),
            org.mockito.kotlin.isNull(),
            capture(overwriteCaptor),
            eq(emptyList()),
            eq(emptyMap()),
            eq(false),
        )

        assertFalse(initialCaptor.value.containsKey(fieldName))
        assertTrue(overwriteCaptor.value.containsKey(fieldName))
    }

    private fun <T> capture(argumentCaptor: ArgumentCaptor<T>): T = argumentCaptor.capture()

    @Test
    fun `create two calculations parent-child with a root already existing and different currencies`() {
        val rootFields =
            mapOf(
                CommercialCalculationCostManufacturedMaterial::customProcurementType.name to
                    CustomProcurementType.fromCustomProcurementTypeWrapper(TsetProcurementType.PURCHASE.customProcurementType),
                "dimension" to Dimension(Dimension.Selection.AREA),
                "calculationTitle" to Text("1"),
                "baseCurrency" to Currency("EUR"),
            )
        val quantityType = QuantityType.TOTAL
        val isRoot = false
        val rootLevel = 0

        val manufacturing =
            addObject(
                "Manufacturing",
                ManualManufacturing::class.java,
                fields = mapOf("baseCurrency" to Currency("EUR")),
            )
        val child =
            addObject(
                "Child",
                ManualManufacturing::class.java,
                fields = mapOf("baseCurrency" to Currency("USD")),
            )
        val result =
            importService
                .buildSingleRoot(
                    rootLevel,
                    Flux.fromIterable(listOf(Tuples.of(0, manufacturing), Tuples.of(1, child))),
                    rootFields,
                    quantityType,
                    isRoot,
                ).block()!!

        assertNull(result.fieldWithResults.find { it.name.name == "baseCurrency" })
        // root currency of EUR is unchanged so should not be there
        assertNull(result.initialFieldsWithResults.find { it.name.name == "baseCurrency" })
        val childManu =
            result.children
                .first()
                .children
                .first()
        // same here
        assertNull(childManu.initialFieldsWithResults.find { it.name.name == "baseCurrency" })
        // USD should be in changed fields list
        assertEquals(
            "USD",
            childManu.fieldWithResults
                .find { it.name.name == "baseCurrency" }
                ?.result
                ?.res,
        )
    }

    @Test
    fun `create two calculations parent-child with a root already existing and different overwrite fields`() {
        val rootFields =
            mapOf(
                CommercialCalculationCostManufacturedMaterial::customProcurementType.name to
                    CustomProcurementType.fromCustomProcurementTypeWrapper(TsetProcurementType.PURCHASE.customProcurementType),
                "dimension" to Dimension(Dimension.Selection.AREA),
                "calculationTitle" to Text("1"),
                "baseCurrency" to Currency("EUR"),
            )
        val quantityType = QuantityType.TOTAL
        val isRoot = false
        val rootLevel = 0

        // please update the tested fields if this list changes
        assertEqualObjectsInCollection(
            OVERWRITE_FIELDS,
            setOf("partDescription", "userNotes", "calculationDate"),
            "asserted fields have been refactored",
        )

        val manufacturing =
            addObject(
                "Manufacturing",
                ManualManufacturing::class.java,
                overrides =
                    mapOf(
                        "partDescription" to Text("rootPartDescription"),
                        "userNotes" to Text("rootUserNotes"),
                        "calculationDate" to Date(LocalDate.of(2024, 4, 4)),
                    ),
            )
        val child =
            addObject(
                "Child",
                ManualManufacturing::class.java,
                overrides =
                    mapOf(
                        "partDescription" to Text("subPartDescription"),
                        "userNotes" to Text("subUserNotes"),
                        "calculationDate" to Date(LocalDate.of(2025, 4, 4)),
                    ),
            )
        val result =
            importService
                .buildSingleRoot(
                    rootLevel,
                    Flux.fromIterable(listOf(Tuples.of(0, manufacturing), Tuples.of(1, child))),
                    rootFields,
                    quantityType,
                    isRoot,
                ).block()!!

        assertEquals(
            "rootPartDescription",
            result.fieldWithResults
                .find { it.name.name == "partDescription" }
                ?.result
                ?.res,
        )
        assertEquals(
            "rootUserNotes",
            result.fieldWithResults
                .find { it.name.name == "userNotes" }
                ?.result
                ?.res,
        )
        assertEquals(
            LocalDate.of(2024, 4, 4),
            result.fieldWithResults
                .find { it.name.name == "calculationDate" }
                ?.result
                ?.res,
        )
        val childManu =
            result.children
                .first()
                .children
                .first()
        assertEquals(
            "subPartDescription",
            childManu.fieldWithResults
                .find { it.name.name == "partDescription" }
                ?.result
                ?.res,
        )
        assertEquals(
            "subUserNotes",
            childManu.fieldWithResults
                .find { it.name.name == "userNotes" }
                ?.result
                ?.res,
        )
        assertEquals(
            LocalDate.of(2025, 4, 4),
            childManu.fieldWithResults
                .find { it.name.name == "calculationDate" }
                ?.result
                ?.res,
        )
    }

    @Test
    fun `create two calculations parent-child with a root already existing and different volumes`() {
        val rootFields =
            mapOf(
                CommercialCalculationCostManufacturedMaterial::customProcurementType.name to
                    CustomProcurementType.fromCustomProcurementTypeWrapper(TsetProcurementType.PURCHASE.customProcurementType),
                "dimension" to Dimension(Dimension.Selection.AREA),
                "calculationTitle" to Text("1"),
                "baseCurrency" to Currency("EUR"),
                "lifeTime" to TimeInYears(12.0, TimeInYearsUnit.YEAR),
                "peakUsableProductionVolumePerYear" to QuantityUnit(123.0.toBigDecimal()),
                "averageUsableProductionVolumePerYear" to QuantityUnit(234.0.toBigDecimal()),
            )
        val quantityType = QuantityType.TOTAL
        val isRoot = false
        val rootLevel = 0

        val manufacturing =
            addObject(
                "Manufacturing",
                ManualManufacturing::class.java,
                fields =
                    mapOf(
                        "lifeTime" to TimeInYears(25.0, TimeInYearsUnit.YEAR),
                        "peakUsableProductionVolumePerYear" to QuantityUnit(432.0.toBigDecimal()),
                        "averageUsableProductionVolumePerYear" to QuantityUnit(234.0.toBigDecimal()),
                    ),
            )
        val child =
            addObject(
                "Child",
                ManualManufacturing::class.java,
                fields =
                    mapOf(
                        "lifeTime" to TimeInYears(25.0, TimeInYearsUnit.YEAR),
                        "peakUsableProductionVolumePerYear" to QuantityUnit(123.0.toBigDecimal()),
                        "averageUsableProductionVolumePerYear" to QuantityUnit(888.0.toBigDecimal()),
                    ),
            )
        val result =
            importService
                .buildSingleRoot(
                    rootLevel,
                    Flux.fromIterable(listOf(Tuples.of(0, manufacturing), Tuples.of(1, child))),
                    rootFields,
                    quantityType,
                    isRoot,
                ).block()!!

        assertNull(
            result.fieldWithResults
                .find { it.name.name == "averageUsableProductionVolumePerYear" }
                ?.result
                ?.res,
        )
        assertEquals(25.0.toBigDecimal(), getFieldScaled(result, "lifeTime"))
        assertEquals(432.0.toBigDecimal(), getFieldScaled(result, "peakUsableProductionVolumePerYear"))
        assertNull(result.initialFieldsWithResults.find { it.name.name == "lifeTime" })
        assertNull(result.initialFieldsWithResults.find { it.name.name == "peakUsableProductionVolumePerYear" })
        assertNull(result.initialFieldsWithResults.find { it.name.name == "averageUsableProductionVolumePerYear" })
        val childManu =
            result.children
                .first()
                .children
                .first()
        assertNull(
            childManu.fieldWithResults
                .find { it.name.name == "lifeTime" }
                ?.result
                ?.res,
        )
        assertEquals(888.0.toBigDecimal(), getFieldScaled(childManu, "averageUsableProductionVolumePerYear"))
        assertEquals(123.0.toBigDecimal(), getFieldScaled(childManu, "peakUsableProductionVolumePerYear"))
        assertNull(childManu.initialFieldsWithResults.find { it.name.name == "lifeTime" })
        assertNull(childManu.initialFieldsWithResults.find { it.name.name == "peakUsableProductionVolumePerYear" })
        assertNull(childManu.initialFieldsWithResults.find { it.name.name == "averageUsableProductionVolumePerYear" })
    }

    @Test
    fun `create two calculations parent-child with a new root and different volumes`() {
        val rootFields =
            mapOf(
                CommercialCalculationCostManufacturedMaterial::customProcurementType.name to
                    CustomProcurementType.fromCustomProcurementTypeWrapper(TsetProcurementType.PURCHASE.customProcurementType),
                "dimension" to Dimension(Dimension.Selection.AREA),
                "calculationTitle" to Text("1"),
                "baseCurrency" to Currency("EUR"),
                "lifeTime" to TimeInYears(12.0, TimeInYearsUnit.YEAR),
                "peakUsableProductionVolumePerYear" to QuantityUnit(123.0.toBigDecimal()),
                "averageUsableProductionVolumePerYear" to QuantityUnit(234.0.toBigDecimal()),
            )
        val quantityType = QuantityType.TOTAL
        val isRoot = true
        val rootLevel = 0

        val manufacturing =
            addObject(
                "Manufacturing",
                ManualManufacturing::class.java,
                fields =
                    mapOf(
                        "lifeTime" to TimeInYears(25.0, TimeInYearsUnit.YEAR),
                        "peakUsableProductionVolumePerYear" to QuantityUnit(432.0.toBigDecimal()),
                    ),
            )
        val child =
            addObject(
                "Child",
                ManualManufacturing::class.java,
                fields =
                    mapOf(
                        "lifeTime" to TimeInYears(25.0, TimeInYearsUnit.YEAR),
                        "peakUsableProductionVolumePerYear" to QuantityUnit(123.0.toBigDecimal()),
                        "averageUsableProductionVolumePerYear" to QuantityUnit(888.0.toBigDecimal()),
                    ),
            )
        val secondChild =
            addObject(
                "SubChild",
                ManualManufacturing::class.java,
                fields =
                    mapOf(
                        "lifeTime" to TimeInYears(12.0, TimeInYearsUnit.YEAR),
                        "baseCurrency" to Currency("USD"),
                        "peakUsableProductionVolumePerYear" to QuantityUnit(123.0.toBigDecimal()),
                        "averageUsableProductionVolumePerYear" to QuantityUnit(111.0.toBigDecimal()),
                    ),
            )
        val result =
            importService
                .buildSingleRoot(
                    rootLevel,
                    Flux.fromIterable(listOf(Tuples.of(0, manufacturing), Tuples.of(1, child), Tuples.of(2, secondChild))),
                    rootFields,
                    quantityType,
                    isRoot,
                ).block()!!

        assertNull(result.fieldWithResults.find { it.name.name == "averageUsableProductionVolumePerYear" })
        assertEquals(25.0.toBigDecimal(), getInitialFieldScaled(result, "lifeTime"))
        assertEquals(432.0.toBigDecimal(), getInitialFieldScaled(result, "peakUsableProductionVolumePerYear"))
        assertEquals(234.0.toBigDecimal(), getInitialFieldScaled(result, "averageUsableProductionVolumePerYear"))
        assertEquals(
            "EUR",
            result.initialFieldsWithResults
                .find { it.name.name == "baseCurrency" }
                ?.result
                ?.res,
        )
        assertNull(result.fieldWithResults.find { it.name.name == "baseCurrency" })
        val childManu =
            result.children
                .first()
                .children
                .first()
        assertNull(childManu.fieldWithResults.find { it.name.name == "lifeTime" })
        assertEquals(888.0.toBigDecimal(), getFieldScaled(childManu, "averageUsableProductionVolumePerYear"))
        assertEquals(123.0.toBigDecimal(), getFieldScaled(childManu, "peakUsableProductionVolumePerYear"))
        assertNull(childManu.initialFieldsWithResults.find { it.name.name == "lifeTime" })
        assertNull(childManu.initialFieldsWithResults.find { it.name.name == "peakUsableProductionVolumePerYear" })
        assertNull(childManu.initialFieldsWithResults.find { it.name.name == "averageUsableProductionVolumePerYear" })
        assertNull(childManu.initialFieldsWithResults.find { it.name.name == "baseCurrency" })
        assertNull(result.fieldWithResults.find { it.name.name == "baseCurrency" })
        val childChildManu =
            childManu.children
                .first()
                .children
                .last()
        assertEquals(12.0.toBigDecimal(), getFieldScaled(childChildManu, "lifeTime"))
        assertNull(childChildManu.initialFieldsWithResults.find { it.name.name == "lifeTime" })
        assertNull(childChildManu.initialFieldsWithResults.find { it.name.name == "peakUsableProductionVolumePerYear" })
        assertNull(childChildManu.initialFieldsWithResults.find { it.name.name == "averageUsableProductionVolumePerYear" })
        assertEquals(111.0.toBigDecimal(), getFieldScaled(childChildManu, "averageUsableProductionVolumePerYear"))
        assertNull(childChildManu.initialFieldsWithResults.find { it.name.name == "baseCurrency" })
        assertEquals(
            "USD",
            childChildManu.fieldWithResults
                .find { it.name.name == "baseCurrency" }
                ?.result
                ?.res,
        )
    }

    @Test
    fun `create two calculations parent-child with a new root and different overwrite fields`() {
        val rootFields =
            mapOf(
                CommercialCalculationCostManufacturedMaterial::customProcurementType.name to
                    CustomProcurementType.fromCustomProcurementTypeWrapper(TsetProcurementType.PURCHASE.customProcurementType),
                "dimension" to Dimension(Dimension.Selection.AREA),
                "calculationTitle" to Text("1"),
                "baseCurrency" to Currency("EUR"),
                "lifeTime" to TimeInYears(12.0, TimeInYearsUnit.YEAR),
                "peakUsableProductionVolumePerYear" to QuantityUnit(123.0.toBigDecimal()),
                "averageUsableProductionVolumePerYear" to QuantityUnit(234.0.toBigDecimal()),
            )
        val quantityType = QuantityType.TOTAL
        val isRoot = true
        val rootLevel = 0

        // please update the tested fields if this list changes
        assertEqualObjectsInCollection(
            OVERWRITE_FIELDS,
            setOf("partDescription", "userNotes", "calculationDate"),
            "asserted fields have been refactored",
        )

        val manufacturing =
            addObject(
                "Manufacturing",
                ManualManufacturing::class.java,
                overrides =
                    mapOf(
                        "partDescription" to Text("rootPartDescription"),
                        "userNotes" to Text("rootUserNotes"),
                        "calculationDate" to Date(LocalDate.of(2024, 7, 7)),
                    ),
            )
        val child =
            addObject(
                "Child",
                ManualManufacturing::class.java,
                overrides =
                    mapOf(
                        "partDescription" to Text("sub1PartDescription"),
                        "userNotes" to Text("sub1UserNotes"),
                        "calculationDate" to Date(LocalDate.of(2024, 10, 10)),
                    ),
            )
        val secondChild =
            addObject(
                "SubChild",
                ManualManufacturing::class.java,
                overrides =
                    mapOf(
                        "partDescription" to Text("sub2PartDescription"),
                        "userNotes" to Text("sub2UserNotes"),
                        "calculationDate" to Date(LocalDate.of(2024, 11, 11)),
                    ),
            )
        val result =
            importService
                .buildSingleRoot(
                    rootLevel,
                    Flux.fromIterable(listOf(Tuples.of(0, manufacturing), Tuples.of(1, child), Tuples.of(2, secondChild))),
                    rootFields,
                    quantityType,
                    isRoot,
                ).block()!!

        assertEquals(
            "rootPartDescription",
            result.fieldWithResults
                .find { it.name.name == "partDescription" }
                ?.result
                ?.res,
        )
        assertEquals(
            "rootUserNotes",
            result.fieldWithResults
                .find { it.name.name == "userNotes" }
                ?.result
                ?.res,
        )
        assertEquals(
            LocalDate.of(2024, 7, 7),
            result.fieldWithResults
                .find { it.name.name == "calculationDate" }
                ?.result
                ?.res,
        )

        val childManu =
            result.children
                .first()
                .children
                .first()
        assertEquals(
            "sub1PartDescription",
            childManu.fieldWithResults
                .find { it.name.name == "partDescription" }
                ?.result
                ?.res,
        )
        assertEquals(
            "sub1UserNotes",
            childManu.fieldWithResults
                .find { it.name.name == "userNotes" }
                ?.result
                ?.res,
        )
        assertEquals(
            LocalDate.of(2024, 10, 10),
            childManu.fieldWithResults
                .find { it.name.name == "calculationDate" }
                ?.result
                ?.res,
        )

        val childChildManu =
            childManu.children
                .first()
                .children
                .last()
        assertEquals(
            "sub2PartDescription",
            childChildManu.fieldWithResults
                .find { it.name.name == "partDescription" }
                ?.result
                ?.res,
        )
        assertEquals(
            "sub2UserNotes",
            childChildManu.fieldWithResults
                .find { it.name.name == "userNotes" }
                ?.result
                ?.res,
        )
        assertEquals(
            LocalDate.of(2024, 11, 11),
            childChildManu.fieldWithResults
                .find { it.name.name == "calculationDate" }
                ?.result
                ?.res,
        )
    }

    @Test
    fun `create a calculation and a sub calc via bom importer and check volumes`() {
        val rootFields =
            mapOf(
                CommercialCalculationCostManufacturedMaterial::customProcurementType.name to
                    CustomProcurementType.fromCustomProcurementTypeWrapper(TsetProcurementType.PURCHASE.customProcurementType),
                "dimension" to Dimension(Dimension.Selection.AREA),
                "lifeTime" to TimeInYears(10.0, TimeInYearsUnit.YEAR),
                "calculationTitle" to Text("1"),
                "baseCurrency" to Currency("EUR"),
                "averageUsableProductionVolumePerYear" to QuantityUnit(10.0),
                "peakUsableProductionVolumePerYear" to QuantityUnit(10.0),
            )
        val quantityType = QuantityType.PER_PARENT
        val isRoot = false
        val rootLevel = 0

        val manufacturing =
            addObject(
                "Manufacturing",
                ManualManufacturing::class.java,
                fields =
                    mapOf(
                        "lifeTime" to TimeInYears(13.0, TimeInYearsUnit.YEAR),
                        "averageUsableProductionVolumePerYear" to QuantityUnit(30.0),
                        "peakUsableProductionVolumePerYear" to QuantityUnit(20.0),
                        "quantity" to
                            QuantityUnit(
                                2.0.toBigDecimal(),
                            ),
                    ),
            )

        val child =
            addObject(
                "Child",
                ManualManufacturing::class.java,
                fields =
                    mapOf(
                        "peakUsableProductionVolumePerYear" to QuantityUnit(40.0),
                        "quantity" to
                            QuantityUnit(
                                4.0.toBigDecimal(),
                            ),
                    ),
            )
        val result =
            importService
                .buildSingleRoot(
                    rootLevel,
                    Flux.fromIterable(listOf(Tuples.of(0, manufacturing), Tuples.of(1, child))),
                    rootFields,
                    quantityType,
                    isRoot,
                ).block()!!

        assertEquals(
            30.0.toBigDecimal(),
            getFieldScaled(result, "averageUsableProductionVolumePerYear"),
        )
        assertNull(result.initialFieldsWithResults.find { it.name.name == "peakUsableProductionVolumePerYear" })
        assertNull(result.fieldWithResults.find { it.name.name == "peakUsableProductionVolumePerYear" })

        val childManu =
            result.children
                .first()
                .children
                .first()

        assertEquals(
            40.0.toBigDecimal(),
            getFieldScaled(childManu, "peakUsableProductionVolumePerYear"),
        )
        assertNull(childManu.initialFieldsWithResults.find { it.name.name == "averageUsableProductionVolumePerYear" })
        assertNull(childManu.fieldWithResults.find { it.name.name == "averageUsableProductionVolumePerYear" })
    }

    @Test
    fun `create a calculation and a sub calc via bom importer and check volumes with TOTAL`() {
        val rootFields =
            mapOf(
                CommercialCalculationCostManufacturedMaterial::customProcurementType.name to
                    CustomProcurementType.fromCustomProcurementTypeWrapper(TsetProcurementType.PURCHASE.customProcurementType),
                "dimension" to Dimension(Dimension.Selection.AREA),
                "lifeTime" to TimeInYears(10.0, TimeInYearsUnit.YEAR),
                "calculationTitle" to Text("1"),
                "baseCurrency" to Currency("EUR"),
                "averageUsableProductionVolumePerYear" to QuantityUnit(10.0),
                "peakUsableProductionVolumePerYear" to QuantityUnit(10.0),
            )
        val quantityType = QuantityType.TOTAL
        val isRoot = false
        val rootLevel = 0

        val manufacturing =
            addObject(
                "Manufacturing",
                ManualManufacturing::class.java,
                fields =
                    mapOf(
                        "lifeTime" to TimeInYears(13.0, TimeInYearsUnit.YEAR),
                        "averageUsableProductionVolumePerYear" to QuantityUnit(30.0),
                        "peakUsableProductionVolumePerYear" to QuantityUnit(20.0),
                        "quantity" to
                            QuantityUnit(
                                2.0.toBigDecimal(),
                            ),
                    ),
            )

        val child =
            addObject(
                "Child",
                ManualManufacturing::class.java,
                fields =
                    mapOf(
                        "peakUsableProductionVolumePerYear" to QuantityUnit(40.0),
                        "quantity" to
                            QuantityUnit(
                                4.0.toBigDecimal(),
                            ),
                    ),
            )
        val result =
            importService
                .buildSingleRoot(
                    rootLevel,
                    Flux.fromIterable(listOf(Tuples.of(0, manufacturing), Tuples.of(1, child))),
                    rootFields,
                    quantityType,
                    isRoot,
                ).block()!!

        assertEquals(
            30.0.toBigDecimal(),
            getFieldScaled(result, "averageUsableProductionVolumePerYear"),
        )
        assertNull(result.initialFieldsWithResults.find { it.name.name == "peakUsableProductionVolumePerYear" })
        assertNull(result.fieldWithResults.find { it.name.name == "peakUsableProductionVolumePerYear" })

        val childManu =
            result.children
                .first()
                .children
                .first()
        assertNull(childManu.initialFieldsWithResults.find { it.name.name == "peakUsableProductionVolumePerYear" })
        assertNull(childManu.fieldWithResults.find { it.name.name == "peakUsableProductionVolumePerYear" })
        assertNull(childManu.initialFieldsWithResults.find { it.name.name == "averageUsableProductionVolumePerYear" })
        assertNull(childManu.fieldWithResults.find { it.name.name == "averageUsableProductionVolumePerYear" })
        assertEquals(
            2.0.toBigDecimal(),
            getInitialFieldScaled(childManu, "quantity"),
        )
    }

    @Test
    fun `create a root calculation and two calcs as sub calc via bom importer and check volumes with TOTAL`() {
        val rootFields =
            mapOf(
                CommercialCalculationCostManufacturedMaterial::customProcurementType.name to
                    CustomProcurementType.fromCustomProcurementTypeWrapper(TsetProcurementType.PURCHASE.customProcurementType),
                "dimension" to Dimension(Dimension.Selection.AREA),
                "lifeTime" to TimeInYears(10.0, TimeInYearsUnit.YEAR),
                "calculationTitle" to Text("1"),
                "baseCurrency" to Currency("EUR"),
                "averageUsableProductionVolumePerYear" to QuantityUnit(10.0),
                "peakUsableProductionVolumePerYear" to QuantityUnit(10.0),
            )
        val quantityType = QuantityType.TOTAL
        val isRoot = true
        val rootLevel = 0

        val manufacturing =
            addObject(
                "Manufacturing",
                ManualManufacturing::class.java,
                fields =
                    mapOf(
                        "lifeTime" to TimeInYears(13.0, TimeInYearsUnit.YEAR),
                        "averageUsableProductionVolumePerYear" to QuantityUnit(30.0),
                        "peakUsableProductionVolumePerYear" to QuantityUnit(20.0),
                    ),
            )

        val child =
            addObject(
                "Child",
                ManualManufacturing::class.java,
                fields =
                    mapOf(
                        "peakUsableProductionVolumePerYear" to QuantityUnit(20.0),
                        "quantity" to
                            QuantityUnit(
                                2.0.toBigDecimal(),
                            ),
                    ),
            )
        val grandChild =
            addObject(
                "GrandChild",
                ManualManufacturing::class.java,
                fields =
                    mapOf(
                        "peakUsableProductionVolumePerYear" to QuantityUnit(60.0),
                        "averageUsableProductionVolumePerYear" to QuantityUnit(20.0),
                        "quantity" to
                            QuantityUnit(
                                6.0.toBigDecimal(),
                            ),
                    ),
            )

        val result =
            importService
                .buildSingleRoot(
                    rootLevel,
                    Flux.fromIterable(listOf(Tuples.of(0, manufacturing), Tuples.of(1, child), Tuples.of(2, grandChild))),
                    rootFields,
                    quantityType,
                    isRoot,
                ).block()!!

        assertEquals(
            30.0.toBigDecimal(),
            getInitialFieldScaled(result, "averageUsableProductionVolumePerYear"),
        )
        assertNull(result.fieldWithResults.find { it.name.name == "averageUsableProductionVolumePerYear" })
        assertEquals(
            20.0.toBigDecimal(),
            getInitialFieldScaled(result, "peakUsableProductionVolumePerYear"),
        )
        assertNull(result.fieldWithResults.find { it.name.name == "peakUsableProductionVolumePerYear" })

        val childManu =
            result.children
                .first()
                .children
                .first()
        assertNull(childManu.initialFieldsWithResults.find { it.name.name == "peakUsableProductionVolumePerYear" })
        assertEquals(
            20.0.toBigDecimal(),
            getFieldScaled(childManu, "peakUsableProductionVolumePerYear"),
        )
        assertNull(childManu.initialFieldsWithResults.find { it.name.name == "averageUsableProductionVolumePerYear" })
        assertNull(childManu.fieldWithResults.find { it.name.name == "averageUsableProductionVolumePerYear" })
        assertEquals(
            2.0.toBigDecimal(),
            getInitialFieldScaled(childManu, "quantity"),
        )

        val grandChildResult =
            childManu.children
                .first()
                .children
                .last()
        assertEquals(
            20.0.toBigDecimal(),
            getFieldScaled(grandChildResult, "averageUsableProductionVolumePerYear"),
        )
        assertNull(grandChildResult.initialFieldsWithResults.find { it.name.name == "peakUsableProductionVolumePerYear" })
        assertNull(grandChildResult.fieldWithResults.find { it.name.name == "peakUsableProductionVolumePerYear" })
        assertEquals(
            3.0.toBigDecimal(),
            getInitialFieldScaled(grandChildResult, "quantity"),
        )
    }

    @Test
    fun `create a root calculation and two calcs as sub calc via bom importer and check volumes with PARENT`() {
        val rootFields =
            mapOf(
                CommercialCalculationCostManufacturedMaterial::customProcurementType.name to
                    CustomProcurementType.fromCustomProcurementTypeWrapper(TsetProcurementType.PURCHASE.customProcurementType),
                "dimension" to Dimension(Dimension.Selection.AREA),
                "lifeTime" to TimeInYears(10.0, TimeInYearsUnit.YEAR),
                "calculationTitle" to Text("1"),
                "baseCurrency" to Currency("EUR"),
                "averageUsableProductionVolumePerYear" to QuantityUnit(10.0),
                "peakUsableProductionVolumePerYear" to QuantityUnit(10.0),
            )
        val quantityType = QuantityType.PER_PARENT
        val isRoot = true
        val rootLevel = 0

        val manufacturing =
            addObject(
                "Manufacturing",
                ManualManufacturing::class.java,
                fields =
                    mapOf(
                        "lifeTime" to TimeInYears(13.0, TimeInYearsUnit.YEAR),
                        "averageUsableProductionVolumePerYear" to QuantityUnit(30.0),
                        "peakUsableProductionVolumePerYear" to QuantityUnit(20.0),
                    ),
            )

        val child =
            addObject(
                "Child",
                ManualManufacturing::class.java,
                fields =
                    mapOf(
                        "peakUsableProductionVolumePerYear" to QuantityUnit(20.0),
                        "averageUsableProductionVolumePerYear" to QuantityUnit(60.0),
                        "quantity" to
                            QuantityUnit(
                                2.0.toBigDecimal(),
                            ),
                    ),
            )
        val grandChild =
            addObject(
                "GrandChild",
                ManualManufacturing::class.java,
                fields =
                    mapOf(
                        "peakUsableProductionVolumePerYear" to QuantityUnit(60.0),
                        "averageUsableProductionVolumePerYear" to QuantityUnit(20.0),
                        "quantity" to
                            QuantityUnit(
                                3.0.toBigDecimal(),
                            ),
                    ),
            )

        val result =
            importService
                .buildSingleRoot(
                    rootLevel,
                    Flux.fromIterable(listOf(Tuples.of(0, manufacturing), Tuples.of(1, child), Tuples.of(2, grandChild))),
                    rootFields,
                    quantityType,
                    isRoot,
                ).block()!!

        assertEquals(
            30.0.toBigDecimal(),
            getInitialFieldScaled(result, "averageUsableProductionVolumePerYear"),
        )
        assertNull(result.fieldWithResults.find { it.name.name == "averageUsableProductionVolumePerYear" })
        assertEquals(
            20.0.toBigDecimal(),
            getInitialFieldScaled(result, "peakUsableProductionVolumePerYear"),
        )
        assertNull(result.fieldWithResults.find { it.name.name == "peakUsableProductionVolumePerYear" })

        val childManu =
            result.children
                .first()
                .children
                .first()
        assertNull(childManu.initialFieldsWithResults.find { it.name.name == "peakUsableProductionVolumePerYear" })
        assertEquals(
            20.0.toBigDecimal(),
            getFieldScaled(childManu, "peakUsableProductionVolumePerYear"),
        )
        assertNull(childManu.initialFieldsWithResults.find { it.name.name == "averageUsableProductionVolumePerYear" })
        assertNull(childManu.fieldWithResults.find { it.name.name == "averageUsableProductionVolumePerYear" })
        assertEquals(
            2.0.toBigDecimal(),
            getInitialFieldScaled(childManu, "quantity"),
        )

        val grandChildResult =
            childManu.children
                .first()
                .children
                .last()
        assertEquals(
            20.0.toBigDecimal(),
            getFieldScaled(grandChildResult, "averageUsableProductionVolumePerYear"),
        )
        assertNull(grandChildResult.initialFieldsWithResults.find { it.name.name == "peakUsableProductionVolumePerYear" })
        assertNull(grandChildResult.fieldWithResults.find { it.name.name == "peakUsableProductionVolumePerYear" })
        assertEquals(
            3.0.toBigDecimal(),
            getInitialFieldScaled(grandChildResult, "quantity"),
        )
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `create two calculations parent-child with a different dimension`(isTotal: Boolean) {
        val rootFields =
            mapOf(
                CommercialCalculationCostManufacturedMaterial::customProcurementType.name to
                    CustomProcurementType.fromCustomProcurementTypeWrapper(TsetProcurementType.PURCHASE.customProcurementType),
                "dimension" to Dimension(Dimension.Selection.NUMBER),
                "calculationTitle" to Text("1"),
                "baseCurrency" to Currency("EUR"),
                "lifeTime" to TimeInYears(12.0, TimeInYearsUnit.YEAR),
                "peakUsableProductionVolumePerYear" to QuantityUnit(123.0.toBigDecimal()),
                "averageUsableProductionVolumePerYear" to QuantityUnit(234.0.toBigDecimal()),
            )
        val quantityType = if (isTotal) QuantityType.TOTAL else QuantityType.PER_PARENT
        val isRoot = true
        val rootLevel = 0

        val manufacturing =
            addObject(
                "Manufacturing",
                ManualManufacturing::class.java,
                fields =
                    mapOf(
                        "dimension" to Dimension(Dimension.Selection.MASS),
                    ),
            )
        val child =
            addObject(
                "Child",
                ManualManufacturing::class.java,
                fields =
                    mapOf(
                        "dimension" to Dimension(Dimension.Selection.VOLUME),
                    ),
            )
        val secondChild =
            addObject(
                "SubChild",
                ManualManufacturing::class.java,
            )

        val result =
            importService
                .buildSingleRoot(
                    rootLevel,
                    Flux.fromIterable(listOf(Tuples.of(0, manufacturing), Tuples.of(1, child), Tuples.of(2, secondChild))),
                    rootFields,
                    quantityType,
                    isRoot,
                ).block()!!

        assertEquals(
            Dimension.Selection.MASS,
            result.initialFieldsWithResults
                .find { it.name.name == "dimension" }
                ?.result
                ?.res,
        )
        val childManu =
            result.children
                .first()
                .children
                .first()
        assertEquals(
            Dimension.Selection.VOLUME,
            childManu.initialFieldsWithResults
                .find { it.name.name == "dimension" }
                ?.result
                ?.res,
        )
        val childChildManu =
            childManu.children
                .first()
                .children
                .last()
        assertEquals(
            Dimension.Selection.NUMBER,
            childChildManu.initialFieldsWithResults
                .find { it.name.name == "dimension" }
                ?.result
                ?.res,
        )
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `create two calculations parent-child with a different procurement type`(isTotal: Boolean) {
        val rootFields =
            mapOf(
                CommercialCalculationCostManufacturedMaterial::customProcurementType.name to
                    CustomProcurementType.fromCustomProcurementTypeWrapper(TsetProcurementType.PURCHASE.customProcurementType),
                "dimension" to Dimension(Dimension.Selection.NUMBER),
                "calculationTitle" to Text("1"),
                "baseCurrency" to Currency("EUR"),
                "lifeTime" to TimeInYears(12.0, TimeInYearsUnit.YEAR),
                "peakUsableProductionVolumePerYear" to QuantityUnit(123.0.toBigDecimal()),
                "averageUsableProductionVolumePerYear" to QuantityUnit(234.0.toBigDecimal()),
            )
        val quantityType = if (isTotal) QuantityType.TOTAL else QuantityType.PER_PARENT
        val isRoot = true
        val rootLevel = 0

        val manufacturing =
            addObject(
                "Manufacturing",
                ManualManufacturing::class.java,
            )
        val child =
            addObject(
                "Child",
                ManualManufacturing::class.java,
                fields =
                    mapOf(
                        CommercialCalculationCostManufacturedMaterial::customProcurementType.name to
                            CustomProcurementType.fromCustomProcurementTypeWrapper(TsetProcurementType.INHOUSE.customProcurementType),
                    ),
            )
        val secondChild =
            addObject(
                "SubChild",
                ManualManufacturing::class.java,
            )

        val result =
            importService
                .buildSingleRoot(
                    rootLevel,
                    Flux.fromIterable(listOf(Tuples.of(0, manufacturing), Tuples.of(1, child), Tuples.of(2, secondChild))),
                    rootFields,
                    quantityType,
                    isRoot,
                ).block()!!

        assertEquals(
            TsetProcurementType.PURCHASE.customProcurementType.value,
            result.initialFieldsWithResults
                .find { it.name.name == CommercialCalculationCostManufacturedMaterial::customProcurementType.name }
                ?.result
                ?.res,
        )
        val childManu =
            result.children
                .first()
                .children
                .first()
        assertEquals(
            TsetProcurementType.INHOUSE.customProcurementType.value,
            childManu.initialFieldsWithResults
                .find { it.name.name == CommercialCalculationCostManufacturedMaterial::customProcurementType.name }
                ?.result
                ?.res,
        )
        val childChildManu =
            childManu.children
                .first()
                .children
                .last()
        assertEquals(
            TsetProcurementType.INHOUSE.customProcurementType.value,
            childChildManu.initialFieldsWithResults
                .find { it.name.name == CommercialCalculationCostManufacturedMaterial::customProcurementType.name }
                ?.result
                ?.res,
        )
    }

    private fun getFieldScaled(
        result: ManufacturingEntity,
        fieldName: String,
    ) = (
        result.fieldWithResults
            .find { it.name.name == fieldName }
            ?.result
            ?.res as BigDecimal
    ).setScale(1)

    private fun getInitialFieldScaled(
        result: ManufacturingEntity,
        fieldName: String,
    ) = (
        result.initialFieldsWithResults
            .find { it.name.name == fieldName }
            ?.result
            ?.res as BigDecimal
    ).setScale(1)
}
