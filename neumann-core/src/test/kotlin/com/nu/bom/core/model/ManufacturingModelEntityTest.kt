package com.nu.bom.core.model

import com.nu.bom.core.CalculationTestBase
import com.nu.bom.core.manufacturing.behaviours.NetWeightPerPartDirectCalculation
import com.nu.bom.core.manufacturing.behaviours.ScrapWeightBehaviour
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostChildEntities
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostMaterialUsage
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostPurchasedMaterial
import com.nu.bom.core.manufacturing.entities.BaseEntityFields
import com.nu.bom.core.manufacturing.entities.BaseManufacturingFields
import com.nu.bom.core.manufacturing.entities.BaseMaterial
import com.nu.bom.core.manufacturing.entities.ManualBaseMaterial
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.entities.ManualMaterialV2
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ShapedMaterial
import com.nu.bom.core.manufacturing.entities.masterdata.material.MaterialConsumerExtension
import com.nu.bom.core.manufacturing.extension.CO2_EXTENSION_PACKAGE
import com.nu.bom.core.manufacturing.extension.ManualMaterialCO2Extension
import com.nu.bom.core.manufacturing.extension.MaterialDimensionExtension
import com.nu.bom.core.manufacturing.extension.volumeandscrap.VolumeAndScrapCalculationMaterialUsage
import com.nu.bom.core.manufacturing.fieldTypes.ConfigIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.DensityUnits
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYearsUnit
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.WeightCalculationMode
import com.nu.bom.core.manufacturing.masterdata.tsetdefaultconfiguration.TsetOverheadMethod
import com.nu.bom.core.model.manufacturing.ManufacturingEntityReadConverter
import com.nu.bom.core.model.manufacturing.ManufacturingEntityWriteConverter
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.model.manufacturing.persistentModel.PersistedDynamicFieldModel
import com.nu.bom.core.model.manufacturing.schema.persistence.PersistedBomNodeSchemaService
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import com.nu.bom.core.service.migration.lazy.MigrationManager
import com.nu.bom.core.service.migration.lazy.MigrationMapperService
import com.nu.bom.core.service.migration.lazy.MigrationMapperServiceImpl
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.mockito.kotlin.mock
import org.mockito.kotlin.spy
import org.mockito.kotlin.verify
import org.mockito.kotlin.verifyNoMoreInteractions
import org.springframework.data.mongodb.core.convert.MappingMongoConverter

class ManufacturingModelEntityTest : CalculationTestBase() {
    private lateinit var writeConverter: ManufacturingEntityWriteConverter
    private lateinit var readConverter: ManufacturingEntityReadConverter
    private lateinit var mappingMongoConverter: MappingMongoConverter
    private lateinit var migrationManager: MigrationManager
    private lateinit var migrationMapperService: MigrationMapperService
    private lateinit var persistedBomNodeSchemaService: PersistedBomNodeSchemaService

    @BeforeEach
    fun setup() {
        val mappers: Map<MigrationChangeSetId, ManufacturingModelEntityMapper> =
            mapOf(
                MigrationChangeSetId("latest-changeset-id") to
                    object : ManufacturingModelEntityMapper {
                        override val changeSetId: MigrationChangeSetId = MigrationChangeSetId("latest-changeset-id")

                        override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity = entity
                    },
            )
        mappingMongoConverter = mock<MappingMongoConverter>()
        migrationManager = spy(MigrationManager())
        migrationMapperService = spy(MigrationMapperServiceImpl(migrationManager, mappers))
        persistedBomNodeSchemaService = mock<PersistedBomNodeSchemaService>()
        writeConverter =
            ManufacturingEntityWriteConverter(
                mappingMongoConverter,
                migrationManager,
                persistedBomNodeSchemaService,
            )
        readConverter =
            ManufacturingEntityReadConverter(
                mappingMongoConverter,
                factory,
                fieldFactoryService,
                migrationMapperService,
                persistedBomNodeSchemaService,
                entityManager,
            )
    }

    @Test
    fun testToModelConversion() {
        val entity = ManualManufacturing("entityName")
        val entityClass = ShapedMaterial::class.simpleName!!
        entity.dynamicFields =
            mutableMapOf(
                "shapeIdentifier" to ManufacturingEntity.DynamicField(entityClass, "shapeId"),
                "partHeight" to ManufacturingEntity.DynamicField(entityClass, "partHeight"),
            )
        val result =
            assertDoesNotThrow {
                writeConverter.toManufacturingModelEntity(entity)
            }

        Assertions.assertNotNull(result, "modelEntity")
        Assertions.assertEquals(
            listOf(
                PersistedDynamicFieldModel("shapeIdentifier", "ShapedMaterial", "shapeId"),
                PersistedDynamicFieldModel("partHeight", "ShapedMaterial", null),
            ),
            result.dynamicFields,
            "dynamicFields",
        )
        verify(migrationManager).getLatestChangeSetId("ManualManufacturing", emptySet(), emptySet())
        val readEntity = readConverter.toManufacturingEntity(result, null).block()!!
        Assertions.assertNotNull(readEntity, "readEntity")

        Assertions.assertEquals(
            ManufacturingEntity.DynamicField(entityClass, "shapeId"),
            readEntity.dynamicFields["shapeIdentifier"],
            "entity.dynamicFields[shapeIdentifier]",
        )
        Assertions.assertEquals(
            ManufacturingEntity.DynamicField(entityClass, "partHeight"),
            readEntity.dynamicFields["partHeight"],
            "entity.dynamicFields[partHeight]",
        )

        verify(migrationMapperService).applyMigrationChangeSets(result, null)
        verifyNoMoreInteractions(mappingMongoConverter, migrationMapperService)
    }

    @Test
    fun testModelEntityClasses() {
        extensionManager.enablePackage(CO2_EXTENSION_PACKAGE)
        val manufacturing = createManufacturing()
        calculate(manufacturing)

        val material = manufacturing.children.single { ManualMaterialV2::class.isInstance(it) }
        this.builder.addInputs(
            material,
            parameters = emptyMap(),
            overrides =
                mapOf(
                    ManualBaseMaterial::weightMode.name to WeightCalculationMode.SCRAP_WEIGHT,
                ),
            useId = true,
        )
        calculate(manufacturing)

        val result =
            assertDoesNotThrow {
                writeConverter.toManufacturingModelEntity(material)
            }
        val dynamicClasses = result.extractDynamicClasses()
        val baseClasses = result.extractBaseClasses()
        val service = MigrationMapperServiceImpl(mock<MigrationManager>(), emptyMap())

        Assertions.assertTrue(service.isHierarchyIntact(result, result.clazz, result.baseClasses, result.dynamicClasses))
        Assertions.assertNotNull(dynamicClasses)
        Assertions.assertEquals(ScrapWeightBehaviour::class.simpleName!!, dynamicClasses.first())
        Assertions.assertTrue(dynamicClasses.contains(ScrapWeightBehaviour::class.simpleName!!))
        Assertions.assertEquals(
            setOf(
                BaseEntityFields::class.simpleName!!,
                ManualBaseMaterial::class.simpleName!!,
                CommercialCalculationCostChildEntities::class.simpleName!!,
                CommercialCalculationCostMaterialUsage::class.simpleName!!,
                CommercialCalculationCostPurchasedMaterial::class.simpleName!!,
                ManualMaterialCO2Extension::class.simpleName!!,
                VolumeAndScrapCalculationMaterialUsage::class.simpleName!!,
                MaterialDimensionExtension::class.simpleName,
                BaseMaterial::class.simpleName,
                MaterialConsumerExtension::class.simpleName,
            ),
            baseClasses,
        )
        Assertions.assertEquals(ManualMaterialV2::class.simpleName!!, result.clazz)
    }

    private fun createManufacturing(
        name: String = "Test Manufacturing",
        parent: ManufacturingEntity? = null,
        procurementType: ManufacturingType = ManufacturingType.PURCHASE,
    ): Manufacturing {
        val rootParameters =
            if (parent == null) {
                mapOf(
                    Manufacturing::location.name to Text("Germany"),
                    BaseManufacturingFields::peakUsableProductionVolumePerYear.name to QuantityUnit(10.000),
                    BaseManufacturingFields::averageUsableProductionVolumePerYear.name to QuantityUnit(10.000),
                )
            } else {
                mapOf()
            }

        val commonParameters =
            mapOf(
                Manufacturing::callsPerYear.name to Num(10.0),
                Manufacturing::shiftsPerDay.name to Num(3.0.toBigDecimal()),
                CommercialCalculationCostMaterialUsage::procurementType.name to procurementType,
                Manufacturing::lifeTime.name to TimeInYears(8.0.toBigDecimal(), TimeInYearsUnit.YEAR),
                Manufacturing::productionHoursPerYear.name to Time(6800.0.toBigDecimal(), TimeUnits.HOUR),
                Manufacturing::overheadMethod.name to TsetOverheadMethod.BUILD_TO_PRINT_AUTO.fieldType,
                BaseManufacturingFields::costModuleConfigurationIdentifier.name to
                    ConfigIdentifier(ConfigurationIdentifier.empty()),
            )

        val root =
            addObject(
                name = name,
                clazz = Manufacturing::class.java,
                parent = parent,
                parameters = rootParameters + commonParameters,
            ) as Manufacturing

        addObject(
            name = "Material",
            clazz = ManualMaterialV2::class.java,
            parent = root,
            fields =
                mapOf(
                    BaseEntityFields::displayDesignation.name to Text("The Amazing Manual Material"),
                    NetWeightPerPartDirectCalculation::netWeightPerPart.name to QuantityUnit(100.0),
                    ScrapWeightBehaviour::scrapWeightPerPart.name to QuantityUnit(10.0),
                    ManualMaterialV2::density.name to Density(1000.0, DensityUnits.GRAM_PER_CCM),
                    ManualBaseMaterial::lossRate.name to Rate(0.1),
                    MaterialConsumerExtension::materialBasePrice.name to Money(10.0),
                    ManualBaseMaterial::materialRecyclingPrice.name to Money(5.0),
                ),
        )

        return root
    }
}
