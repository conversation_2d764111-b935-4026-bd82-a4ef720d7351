package com.nu.bom.core.calculation

import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.AreaUnits
import com.nu.bom.core.manufacturing.fieldTypes.CurrentDensity
import com.nu.bom.core.manufacturing.fieldTypes.CurrentDensityUnits
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeUnit
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.DensityUnits
import com.nu.bom.core.manufacturing.fieldTypes.DepositionRate
import com.nu.bom.core.manufacturing.fieldTypes.DepositionRateUnits
import com.nu.bom.core.manufacturing.fieldTypes.Diffusivity
import com.nu.bom.core.manufacturing.fieldTypes.DiffusivityUnits
import com.nu.bom.core.manufacturing.fieldTypes.ElectricCurrent
import com.nu.bom.core.manufacturing.fieldTypes.ElectricCurrentUnits
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.EmissionUnits
import com.nu.bom.core.manufacturing.fieldTypes.Energy
import com.nu.bom.core.manufacturing.fieldTypes.EnergyUnits
import com.nu.bom.core.manufacturing.fieldTypes.Force
import com.nu.bom.core.manufacturing.fieldTypes.ForceUnits
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.PaintSpreadingRate
import com.nu.bom.core.manufacturing.fieldTypes.PaintSpreadingRateUnits
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.PiecesUnits
import com.nu.bom.core.manufacturing.fieldTypes.Power
import com.nu.bom.core.manufacturing.fieldTypes.PowerUnits
import com.nu.bom.core.manufacturing.fieldTypes.Pressure
import com.nu.bom.core.manufacturing.fieldTypes.PressureUnits
import com.nu.bom.core.manufacturing.fieldTypes.Revolution
import com.nu.bom.core.manufacturing.fieldTypes.RevolutionUnits
import com.nu.bom.core.manufacturing.fieldTypes.SpecificHeatCapacity
import com.nu.bom.core.manufacturing.fieldTypes.SpecificHeatCapacityUnits
import com.nu.bom.core.manufacturing.fieldTypes.Speed
import com.nu.bom.core.manufacturing.fieldTypes.SpeedUnits
import com.nu.bom.core.manufacturing.fieldTypes.SurfaceDensity
import com.nu.bom.core.manufacturing.fieldTypes.SurfaceDensityUnits
import com.nu.bom.core.manufacturing.fieldTypes.Temperature
import com.nu.bom.core.manufacturing.fieldTypes.TemperatureUnits
import com.nu.bom.core.manufacturing.fieldTypes.ThermalConductivity
import com.nu.bom.core.manufacturing.fieldTypes.ThermalConductivityUnits
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeInHours
import com.nu.bom.core.manufacturing.fieldTypes.TimeInHoursUnit
import com.nu.bom.core.manufacturing.fieldTypes.TimeInMinutes
import com.nu.bom.core.manufacturing.fieldTypes.TimeInMinutesUnits
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYearsUnit
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.Torque
import com.nu.bom.core.manufacturing.fieldTypes.TorqueUnits
import com.nu.bom.core.manufacturing.fieldTypes.Volume
import com.nu.bom.core.manufacturing.fieldTypes.VolumeFlowRate
import com.nu.bom.core.manufacturing.fieldTypes.VolumeFlowRateUnits
import com.nu.bom.core.manufacturing.fieldTypes.VolumeUnits
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.tset.core.module.bom.DECIMAL_SCALE
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.math.MathContext

class TypeUnitTest {
    private val res = BigDecimal("7.2").setScale(DECIMAL_SCALE)

    @Test
    fun areaTime() {
        // this test is a just a hull for completeness' sake, since there is no AreaTime, just the Units.
    }

    @Test
    fun area() {
        val value = Area(res, AreaUnits.QM)

        assertEquals(res.compareTo(value.to(AreaUnits.QM)), 0)
        assertEquals(toSquareDeci(res).compareTo(value.to(AreaUnits.QDM)), 0)
        assertEquals(toSquareCenti(res).compareTo(value.to(AreaUnits.QCM)), 0)
        assertEquals(toSquareMilli(res).compareTo(value.to(AreaUnits.QMM)), 0)
    }

    @Test
    fun currentDensity() {
        val value = CurrentDensity(res, CurrentDensityUnits.AMP_PER_QM)

        assertEquals(res.compareTo(value.to(CurrentDensityUnits.AMP_PER_QM)), 0)
        assertEquals(toPerSquareDeci(res).compareTo(value.to(CurrentDensityUnits.AMP_PER_QDM)), 0)
        assertEquals(toPerSquareCenti(res).compareTo(value.to(CurrentDensityUnits.AMP_PER_QCM)), 0)
    }

    @Suppress("DEPRECATION")
    @Test
    fun cycleTime() {
        val value = CycleTime(res, CycleTimeUnit.SECOND)

        assertEquals(res.compareTo(value.to(CycleTimeUnit.SECOND)), 0)
        assertEquals(res.compareTo(value.to(CycleTimeUnit.SECONDS)), 0)
        assertEquals(res.compareTo(value.to(CycleTimeUnit.STROKES_PER_SECONDS)), 0)

        assertEquals(secToMin(res).compareTo(value.to(CycleTimeUnit.MINUTE)), 0)
        assertEquals(secToMin(res).compareTo(value.to(CycleTimeUnit.MINUTES)), 0)
        assertEquals(secToMin(res).compareTo(value.to(CycleTimeUnit.STROKES_PER_MINUTES)), 0)

        assertEquals(secToHour(res).compareTo(value.to(CycleTimeUnit.HOUR)), 0)
        assertEquals(secToHour(res).compareTo(value.to(CycleTimeUnit.HOURS)), 0)
        assertEquals(secToHour(res).compareTo(value.to(CycleTimeUnit.STROKES_PER_HOURS)), 0)
    }

    @Test
    fun density() {
        val value = Density(res, DensityUnits.KILOGRAM_PER_CM)
        assertEquals(res.compareTo(value.to(DensityUnits.KILOGRAM_PER_CM)), 0)
        assertEquals((kgToG(toPerCubicCenti(res))).compareTo(value.to(DensityUnits.GRAM_PER_CCM)), 0)
    }

    @Test
    fun depositionRate() {
        val value = DepositionRate(res, DepositionRateUnits.MICROMETER_PER_SECOND)

        assertEquals(res.compareTo(value.to(DepositionRateUnits.MICROMETER_PER_SECOND)), 0)
        assertEquals(perSecToPerMin(res).compareTo(value.to(DepositionRateUnits.MICROMETER_PER_MIN)), 0)
    }

    @Test
    fun diffusivity() {
        val value = Diffusivity(res, DiffusivityUnits.QM_PER_SECONDS)

        assertEquals(res.compareTo(value.to(DiffusivityUnits.QM_PER_SECONDS)), 0)
        assertEquals(toSquareCenti(res).compareTo(value.to(DiffusivityUnits.QCM_PER_SECONDS)), 0)
        assertEquals(toSquareMilli(res).compareTo(value.to(DiffusivityUnits.QMM_PER_SECONDS)), 0)
    }

    @Test
    fun electricCurrent() {
        val value = ElectricCurrent(res, ElectricCurrentUnits.AMP)

        assertEquals(toMicro(res).compareTo(value.to(ElectricCurrentUnits.MICROAMP)), 0)
        assertEquals(toMilli(res).compareTo(value.to(ElectricCurrentUnits.MILLIAMP)), 0)
        assertEquals(res.compareTo(value.to(ElectricCurrentUnits.AMP)), 0)
        assertEquals(toKilo(res).compareTo(value.to(ElectricCurrentUnits.KILOAMP)), 0)
        assertEquals(toMega(res).compareTo(value.to(ElectricCurrentUnits.MEGAAMP)), 0)
    }

    @Test
    fun emission() {
        val value = Emission(res, EmissionUnits.GRAM_CO2E)

        assertEquals(toMilli(res).compareTo(value.to(EmissionUnits.MILLIGRAM_CO2E)), 0)
        assertEquals(res.compareTo(value.to(EmissionUnits.GRAM_CO2E)), 0)
        assertEquals(toKilo(res).compareTo(value.to(EmissionUnits.KILOGRAM_CO2E)), 0)
        assertEquals(toMega(res).compareTo(value.to(EmissionUnits.TON_CO2E)), 0)
    }

    @Test
    fun energy() {
        val value = Energy(res, EnergyUnits.JOULE) // equals WATTSECOND

        assertEquals(res.compareTo(value.to(EnergyUnits.JOULE)), 0)
        assertEquals(secToHour(res).compareTo(value.to(EnergyUnits.WATTHOUR)), 0)
        assertEquals(toKilo(secToHour(res)).compareTo(value.to(EnergyUnits.KILOWATTHOUR)), 0)
    }

    @Test
    fun force() {
        val value = Force(res, ForceUnits.NEWTON)

        assertEquals(res.compareTo(value.to(ForceUnits.NEWTON)), 0)
        assertEquals(toKilo(res).compareTo(value.to(ForceUnits.KILONEWTON)), 0)
        assertEquals(toTonForce(res).compareTo(value.to(ForceUnits.TON_FORCE).round(MathContext.DECIMAL32)), 0)
    }

    @Test
    fun length() {
        val value = Length(res, LengthUnits.METER)

        assertEquals(toMicro(res).compareTo(value.to(LengthUnits.MICROMETER)), 0)
        assertEquals(toMilli(res).compareTo(value.to(LengthUnits.MILLIMETER)), 0)
        assertEquals(toCenti(res).compareTo(value.to(LengthUnits.CENTIMETER)), 0)
        assertEquals(toDeci(res).compareTo(value.to(LengthUnits.DECIMETER)), 0)
        assertEquals(res.compareTo(value.to(LengthUnits.METER)), 0)
        assertEquals(toKilo(res).compareTo(value.to(LengthUnits.KILOMETER)), 0)
    }

    @Test
    fun paintSpreadingRate() {
        val value = PaintSpreadingRate(res, PaintSpreadingRateUnits.QM_PER_LITER)

        assertEquals(res.compareTo(value.to(PaintSpreadingRateUnits.QM_PER_LITER)), 0)
    }

    @Test
    fun pieces() {
        val value = Pieces(res, PiecesUnits.PIECE)

        assertEquals(res.compareTo(value.to(PiecesUnits.PIECE)), 0)
        assertEquals((res.div(BigDecimal(10))).compareTo(value.to(PiecesUnits.TEN_PIECES)), 0)
        assertEquals((res.div(BigDecimal(100))).compareTo(value.to(PiecesUnits.HUNDRED_PIECES)), 0)
        assertEquals((res.div(BigDecimal(1000))).compareTo(value.to(PiecesUnits.THOUSAND_PIECES)), 0)
    }

    @Test
    fun power() {
        val value = Power(res, PowerUnits.WATT)

        assertEquals(res.compareTo(value.to(PowerUnits.WATT)), 0)
        assertEquals(toKilo(res).compareTo(value.to(PowerUnits.KILOWATT)), 0)
        assertEquals(toMega(res).compareTo(value.to(PowerUnits.MEGAWATT)), 0)
    }

    @Test
    fun pressure() {
        val value = Pressure(res, PressureUnits.PASCAL)

        assertEquals(res.compareTo(value.to(PressureUnits.PASCAL)), 0)
        assertEquals(toMega(res).compareTo(value.to(PressureUnits.MEGAPASCAL)), 0)
        assertEquals(pascalToBar(res).compareTo(value.to(PressureUnits.BAR)), 0)
    }

    @Test
    fun revolution() {
        val value = Revolution(res, RevolutionUnits.PER_SEC)

        assertEquals(res.compareTo(value.to(RevolutionUnits.PER_SEC)), 0)
        assertEquals(perSecToPerMin(res).compareTo(value.to(RevolutionUnits.PER_MIN)), 0)
    }

    @Test
    fun specificHeatCapacity() {
        val value = SpecificHeatCapacity(res, SpecificHeatCapacityUnits.JOULE_PER_KELVIN_PER_KILOGRAM)

        assertEquals(res.compareTo(value.to(SpecificHeatCapacityUnits.JOULE_PER_KELVIN_PER_KILOGRAM)), 0)
        assertEquals(perKgToPerG(res).compareTo(value.to(SpecificHeatCapacityUnits.JOULE_PER_KELVIN_PER_GRAM)), 0)
    }

    @Test
    fun speed() {
        val value = Speed(res, SpeedUnits.M_PER_SEC)

        assertEquals(res.compareTo(value.to(SpeedUnits.M_PER_SEC)), 0)
        assertEquals(perSecToPerMin(res).compareTo(value.to(SpeedUnits.M_PER_MIN)), 0)
        assertEquals(toMilli(res).compareTo(value.to(SpeedUnits.MM_PER_SEC)), 0)
        assertEquals(toMilli(perSecToPerMin(res)).compareTo(value.to(SpeedUnits.MM_PER_MIN)), 0)
    }

    fun surfaceDensity() {
        val value = SurfaceDensity(res, SurfaceDensityUnits.KILOGRAM_PER_QM)

        assertEquals(res.compareTo(value.to(SurfaceDensityUnits.KILOGRAM_PER_QM)), 0)
        assertEquals(perKgToPerG(res).compareTo(value.to(SurfaceDensityUnits.GRAM_PER_QM)), 0)
    }

    @Test
    fun temperature() {
        val value = Temperature(res, TemperatureUnits.CELSIUS)

        assertEquals(res.compareTo(value.to(TemperatureUnits.CELSIUS)), 0)
    }

    @Test
    fun thermalConductivity() {
        val value = ThermalConductivity(res, ThermalConductivityUnits.WATTS_PER_METER_KELVIN)

        assertEquals(res.compareTo(value.to(ThermalConductivityUnits.WATTS_PER_METER_KELVIN)), 0)
    }

    @Test
    fun time() {
        val value = Time(res, TimeUnits.YEAR)

        assertEquals(res.compareTo(value.to(TimeUnits.YEAR)), 0)
        assertEquals(yearToDay(res).compareTo(value.to(TimeUnits.DAY)), 0)
        assertEquals(yearToHour(res).compareTo(value.to(TimeUnits.HOUR)), 0)
        assertEquals(yearToMinute(res).compareTo(value.to(TimeUnits.MINUTE)), 0)
        assertEquals(yearToSecond(res).compareTo(value.to(TimeUnits.SECOND)), 0)
    }

    @Test
    fun timeInMinutes() {
        val value = TimeInMinutes(res, TimeInMinutesUnits.YEAR)

        assertEquals(res.compareTo(value.to(TimeInMinutesUnits.YEAR)), 0)
        assertEquals(yearToDay(res).compareTo(value.to(TimeInMinutesUnits.DAY)), 0)
        assertEquals(yearToHour(res).compareTo(value.to(TimeInMinutesUnits.HOUR)), 0)
        assertEquals(yearToMinute(res).compareTo(value.to(TimeInMinutesUnits.MINUTE)), 0)
        assertEquals(yearToSecond(res).compareTo(value.to(TimeInMinutesUnits.SECOND)), 0)
    }

    @Test
    fun timeInHours() {
        val value = TimeInHours(res, TimeInHoursUnit.YEAR)

        assertEquals(res.compareTo(value.to(TimeInHoursUnit.YEAR)), 0)
        assertEquals(yearToDay(res).compareTo(value.to(TimeInHoursUnit.DAY)), 0)
        assertEquals(yearToHour(res).compareTo(value.to(TimeInHoursUnit.HOUR)), 0)
        assertEquals(yearToMinute(res).compareTo(value.to(TimeInHoursUnit.MINUTE)), 0)
        assertEquals(yearToSecond(res).compareTo(value.to(TimeInHoursUnit.SECOND)), 0)
    }

    @Test
    fun timeInYears() {
        val value = TimeInYears(res, TimeInYearsUnit.YEAR)

        assertEquals(res.compareTo(value.to(TimeInYearsUnit.YEAR)), 0)
        assertEquals(yearToDay(res).compareTo(value.to(TimeInYearsUnit.DAY)), 0)
        assertEquals(yearToHour(res).compareTo(value.to(TimeInYearsUnit.HOUR)), 0)
        assertEquals(yearToMinute(res).compareTo(value.to(TimeInYearsUnit.MINUTE)), 0)
        assertEquals(yearToSecond(res).compareTo(value.to(TimeInYearsUnit.SECOND)), 0)
    }

    @Test
    fun torque() {
        val value = Torque(res, TorqueUnits.NEWTONMETER)

        assertEquals(res.compareTo(value.to(TorqueUnits.NEWTONMETER)), 0)
    }

    @Test
    fun volumeFlowRate() {
        val value = VolumeFlowRate(res, VolumeFlowRateUnits.CM_PER_SECONDS)

        assertEquals(res.compareTo(value.to(VolumeFlowRateUnits.CM_PER_SECONDS)), 0)
        assertEquals(toCubicCenti(res).compareTo(value.to(VolumeFlowRateUnits.CCM_PER_SECONDS)), 0)
        assertEquals(toCubicMilli(res).compareTo(value.to(VolumeFlowRateUnits.CMM_PER_SECONDS)), 0)
    }

    @Test
    fun volume() {
        val mc = MathContext(DECIMAL_SCALE)
        val value = Volume(res, VolumeUnits.CM)

        assertEquals(res.compareTo(value.to(VolumeUnits.CM)), 0)
        assertEquals(toCubicDeci(res).compareTo(value.to(VolumeUnits.CDM)), 0)
        assertEquals(toCubicCenti(res).compareTo(value.to(VolumeUnits.CCM)), 0)
        assertEquals(toCubicMilli(res).compareTo(value.to(VolumeUnits.CMM).round(mc)), 0)

        assertEquals(toCubicDeci(res).compareTo(value.to(VolumeUnits.LITER)), 0)
        assertEquals(toMilli(toCubicDeci(res)).compareTo(value.to(VolumeUnits.MILLILITER)), 0)
    }

    @Test
    fun weight() {
        val value = Weight(res, WeightUnits.GRAM)

        assertEquals(toMilli(res).compareTo(value.to(WeightUnits.MILLIGRAM)), 0)
        assertEquals(res.compareTo(value.to(WeightUnits.GRAM)), 0)
        assertEquals(toKilo(res).compareTo(value.to(WeightUnits.KILOGRAM)), 0)
        assertEquals(toMega(res).compareTo(value.to(WeightUnits.TON)), 0)
    }

    private fun kgToG(res: BigDecimal) = toMilli(res)

    private fun toMicro(res: BigDecimal) = res.times(BigDecimal(1000 * 1000))

    private fun toMilli(res: BigDecimal) = res.times(BigDecimal(1000))

    private fun toCenti(res: BigDecimal) = res.times(BigDecimal(100))

    private fun toDeci(res: BigDecimal) = res.times(BigDecimal(10))

    private fun toKilo(res: BigDecimal) = res.div(BigDecimal(1000))

    private fun toMega(res: BigDecimal) = res.div(BigDecimal(1000 * 1000))

    private fun toSquareDeci(res: BigDecimal) = res.times(BigDecimal(10 * 10))

    private fun toSquareCenti(res: BigDecimal) = res.times(BigDecimal(100 * 100))

    private fun toSquareMilli(res: BigDecimal) = res.times(BigDecimal(1000 * 1000))

    private fun toPerSquareDeci(res: BigDecimal) = res.div(BigDecimal(10 * 10))

    private fun toPerSquareCenti(res: BigDecimal) = res.div(BigDecimal(100 * 100))

    private fun toPerCubicCenti(res: BigDecimal) = res.div(BigDecimal(100 * 100 * 100))

    private fun toCubicDeci(res: BigDecimal) = res.times(BigDecimal(10 * 10 * 10))

    private fun toCubicCenti(res: BigDecimal) = res.times(BigDecimal(100 * 100 * 100))

    private fun toCubicMilli(res: BigDecimal) = res.times(BigDecimal(1000 * 1000 * 1000))

    private fun secToMin(res: BigDecimal) = res.div(BigDecimal(60))

    private fun secToHour(res: BigDecimal) = res.div(BigDecimal(60 * 60))

    private fun yearToDay(res: BigDecimal) = res.times(BigDecimal(365))

    private fun yearToHour(res: BigDecimal) = res.times(BigDecimal(365 * 24))

    private fun yearToMinute(res: BigDecimal) = res.times(BigDecimal(365 * 24 * 60))

    private fun yearToSecond(res: BigDecimal) = res.times(BigDecimal(365 * 24 * 60 * 60))

    private fun perSecToPerMin(res: BigDecimal) = res.times(BigDecimal(60))

    private fun perKgToPerG(res: BigDecimal) = toKilo(res)

    private fun pascalToBar(res: BigDecimal) = res.div(BigDecimal(100000))

    private fun toTonForce(res: BigDecimal) = res.div(BigDecimal(9806.65))
}
