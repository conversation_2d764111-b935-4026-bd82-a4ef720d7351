package com.nu.bom.core.service

import com.nu.bom.core.CalculationTestBase
import com.nu.bom.core.controller.CalculationResultDto
import com.nu.bom.core.controller.CalculationResultWithSnapshot
import com.nu.bom.core.exception.userException.IncompatibleTechnologiesException
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.entities.BaseEntityFields
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.BaseManufacturingFields
import com.nu.bom.core.manufacturing.entities.BaseModelManufacturing
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ManufacturingStep
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.ConfigIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.TriggerAction
import com.nu.bom.core.model.configurations.SemanticVersion
import com.nu.bom.core.service.change.ChangedEntities
import com.nu.bom.core.service.configurations.InjCostModuleTsetConfigurationService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.AccountUtil
import org.bson.types.ObjectId
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.doAnswer
import org.mockito.kotlin.eq
import org.mockito.kotlin.isNull
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import reactor.core.publisher.Mono
import reactor.test.StepVerifier
import kotlin.reflect.KClass

class EntityCreationServiceVersioningTest : CalculationTestBase() {
    private lateinit var service: EntityCreationService

    private val manufacturingCalculationService: ManufacturingCalculationService = mock()
    private val bomNodeService: BomNodeService = mock()
    private val bomNodeConversionService: BomNodeConversionService = mock()

    private val bomNodeId: BomNodeId = BomNodeId()
    private val branchId: BranchId = BranchId()
    private val parentId: ObjectId = ObjectId()
    private lateinit var manufacturing: ManualManufacturing

    private val configurationIdentifier =
        ConfigurationIdentifier.tset(
            InjCostModuleTsetConfigurationService.TSET_CONFIGURATION_KEY,
            SemanticVersion(1, 0),
        )

    private val access: AccessCheck = AccountUtil.dummyAccessCheck()

    @BeforeEach
    fun setup() {
        service =
            EntityCreationService(
                entityManager(),
                manufacturingCalculationService,
                factory,
                bomNodeConversionService,
                bomNodeService,
                mockConfigurationManagementService,
            )
        whenever(
            manufacturingCalculationService.transformAndCalculate(
                accessCheck = eq(access),
                bomNodeId = eq(bomNodeId),
                branchId = any<BranchId>(),
                triggerAction = any<TriggerAction>(),
                context = any(),
                forceRecalculate = any(),
                dirtyChildLoading = any(),
                extraBomNodeId = anyOrNull(),
                loadingMode = any(),
                customInputs = isNull(),
                transformRoot = anyOrNull(),
                transformManufacturing = any(),
            ),
        ).doAnswer {
            val func =
                it.arguments[11] as ((BaseManufacturing, BomNodeSnapshot, ChangedEntities) -> Mono<BaseManufacturing>)

            val baseManufacturing = func(manufacturing, mock(), mock()).block()

            calculate(baseManufacturing!!)

            Mono.just(
                CalculationResultWithSnapshot(
                    CalculationResultDto(
                        manufacturing = baseManufacturing,
                        bomNode = mock(),
                        missingInputs = emptyList(),
                        open = emptyList(),
                        openCalculations = emptyList(),
                        result = mock(),
                    ),
                    mock(),
                ),
            )
        }
        whenever(
            mockConfigurationManagementService.getDefaultCostModuleConfigurationIdentifier(
                any(),
                any(),
            ),
        ).thenReturn(
            Mono.just(configurationIdentifier),
        )
    }

    private fun createEntityDto(
        entityType: Entities,
        fields: Map<String, FieldResultStar>,
        entityClass: KClass<out ManufacturingEntity>? = null,
        technology: Model,
    ): EntityCreationDataConversionService.EntityCreationData =
        EntityCreationDataConversionService.EntityCreationData(
            bomNodeId = bomNodeId,
            branchId = branchId,
            parentId = parentId,
            projectId = null,
            childBomNodeId = null,
            items =
                listOf(
                    EntityCreationDataConversionService.EntityCreationData.ItemData(
                        entityType = entityType,
                        entityClass = entityClass,
                        masterDataSelector = null,
                        fields = fields,
                        overwrites = emptyMap(),
                        isolated = false,
                    ),
                ),
            technology = technology,
        )

    @Test
    fun `creating modularized entity makes root modularized`() {
        createManualManufacturingAndVerifyCostModule()
        addModularizedStepAndVerifyCostModule(Model.INJ2)
        verify(
            manufacturingCalculationService,
        ).transformAndCalculate(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            anyOrNull(),
            anyOrNull(),
            isNull(),
            anyOrNull(),
            any(),
        )
    }

    @Test
    fun `adding different modularized step fails`() {
        createManualManufacturingAndVerifyCostModule()
        addModularizedStepAndVerifyCostModule(Model.INJ2)
        addModularizedStepAndVerifyError(Model.CHILL)
    }

    @Test
    fun `removing modularized entity makes root unmodularized`() {
        createManualManufacturingAndVerifyCostModule()
        addModularizedStepAndVerifyCostModule(Model.INJ2)
        removeStepAndVerifyManual()
        verify(
            manufacturingCalculationService,
        ).transformAndCalculate(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            anyOrNull(),
            anyOrNull(),
            isNull(),
            anyOrNull(),
            any(),
        )
    }

    @Test
    fun `removing modularized entity allows to modularize again`() {
        createManualManufacturingAndVerifyCostModule()
        addModularizedStepAndVerifyCostModule(Model.INJ2)
        removeStepAndVerifyManual()
        addModularizedStepAndVerifyCostModule(Model.CHILL)
        verify(
            manufacturingCalculationService,
            times(2),
        ).transformAndCalculate(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            anyOrNull(),
            anyOrNull(),
            isNull(),
            anyOrNull(),
            any(),
        )
    }

    private fun addModularizedStepAndVerifyCostModule(model: Model) {
        val dto =
            createEntityDto(
                Entities.MANUFACTURING_STEP,
                mapOf(
                    BaseEntityFields::displayDesignation.name to Text("Hello").withSource(FieldResult.SOURCE.I),
                    BaseManufacturingFields::quantity.name to Num(1.0).withSource(FieldResult.SOURCE.I),
                    ManufacturingStep::technologyModel.name to
                        Text(model.entity).withSource(FieldResult.SOURCE.I),
                ),
                technology = model,
            )

        val rootManufacturing = service.createAndCalculate(access, dto).block()
        assertNotNull(rootManufacturing, "entity")
        // Verify modularized manual manufacturing cost module properties
        verifyCostModuleInfo(model.entity, configurationIdentifier, model.name)
    }

    private fun addModularizedStepAndVerifyError(model: Model) {
        val dto =
            createEntityDto(
                Entities.MANUFACTURING_STEP,
                mapOf(
                    BaseEntityFields::displayDesignation.name to Text("Hello").withSource(FieldResult.SOURCE.I),
                    BaseManufacturingFields::quantity.name to Num(1.0).withSource(FieldResult.SOURCE.I),
                    ManufacturingStep::technologyModel.name to
                        Text(model.entity).withSource(FieldResult.SOURCE.I),
                ),
                technology = model,
            )

        StepVerifier.create(service.createAndCalculate(access, dto))
            .expectErrorSatisfies {
                it.suppressed[0] is IncompatibleTechnologiesException
            }
            .verify()
    }

    private fun createManualManufacturingAndVerifyCostModule() {
        manufacturing =
            createManualManufacturing(
                "testEntity",
            ).apply { _id = parentId }
        calculate(manufacturing)
        // Verify empty manual manufacturing cost module properties
        verifyCostModuleInfo(Model.MANUAL.entity, ConfigurationIdentifier.empty(), Model.MANUAL.name)
    }

    private fun removeStepAndVerifyManual() {
        manufacturing.children.removeIf {
            it.getEntityType() == Entities.MANUFACTURING_STEP.name
        }

        calculate(manufacturing)
        assertNotNull(manufacturing, "entity")
        // Verify modularized manual manufacturing cost module properties
        verifyCostModuleInfo(Model.MANUAL.entity, configurationIdentifier, Model.MANUAL.name)
    }

    private fun verifyCostModuleInfo(
        model: String,
        config: ConfigurationIdentifier,
        technologyName: String,
    ) {
        assertEquals(
            Text(model),
            manufacturing.getFieldResult(BaseModelManufacturing::technologyModel.name),
            "root.technologyModel",
        )
        assertEquals(
            ConfigIdentifier(config),
            manufacturing.getFieldResult(BaseManufacturingFields::costModuleConfigurationIdentifier.name),
            "root.costModuleConfigurationIdentifier",
        )
        assertEquals(
            Text(technologyName),
            manufacturing.getFieldResult(BaseManufacturingFields::configurationTechnology.name),
            "root.configurationTechnology",
        )
    }
}
