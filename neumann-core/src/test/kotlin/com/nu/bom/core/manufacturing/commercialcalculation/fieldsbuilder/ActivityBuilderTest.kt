package com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder

import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationRole
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.fieldCalculation.CommercialCalculationTestBase
import com.nu.bom.core.manufacturing.commercialcalculation.fieldnamebuilders.ActivityAndInvestFieldNameBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.fieldnamebuilders.ValueFieldNameBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.executioncontext.CommercialCalculationContext
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.ActivityInputOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.InputSubElement
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.SubElementInputOperation
import com.nu.bom.core.manufacturing.commercialcalculation.rollupconfiguration.RollUpConfiguration
import com.nu.bom.core.manufacturing.configurablefields.config.FieldConfig
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

class ActivityBuilderTest : CommercialCalculationTestBase() {
    private val rollUpConfiguration = RollUpConfiguration()

    enum class ElementType {
        Cost1,
        Cost2,
        Cost3,
        Activity1,
        Activity2,
        Activity3,
    }

    @Test
    fun activityBuilderTest() {
        val subElementOperations =
            listOf(
                SubElementInputOperation(
                    ElementType.Cost1.name,
                    AggregationLevel.MACHINE,
                ),
                SubElementInputOperation(
                    ElementType.Cost2.name,
                    AggregationLevel.MACHINE,
                ),
                SubElementInputOperation(
                    ElementType.Cost3.name,
                    AggregationLevel.MACHINE,
                ),
            )
        val activityOperations =
            listOf(
                ActivityInputOperation(
                    ElementType.Activity1.name,
                    AggregationLevel.MACHINE,
                    mapOf(
                        ElementType.Cost1.name to
                            InputSubElement(
                                "Activity1_Cost1",
                            ),
                    ),
                ),
                ActivityInputOperation(
                    ElementType.Activity2.name,
                    AggregationLevel.MACHINE,
                    mapOf(
                        ElementType.Cost1.name to
                            InputSubElement(
                                "Activity2_Cost1",
                            ),
                        ElementType.Cost2.name to
                            InputSubElement(
                                "Activity2_Cost2",
                            ),
                    ),
                ),
                ActivityInputOperation(
                    ElementType.Activity3.name,
                    AggregationLevel.MACHINE,
                    mapOf(
                        ElementType.Cost1.name to
                            InputSubElement(
                                "Activity3_Cost1",
                            ),
                        ElementType.Cost2.name to
                            InputSubElement(
                                "Activity3_Cost2",
                            ),
                        ElementType.Cost3.name to
                            InputSubElement(
                                "Activity3_Cost3",
                            ),
                    ),
                ),
            )

        val config =
            TestConfigurationFactory.createInternalCostConfig(
                subElementOperations + activityOperations,
                listOf(),
            )

        val context = rollUpConfiguration.getAggregationContexts(AggregationLevel.MACHINE, AggregationRole.THIS).single()
        val fieldConfigs = getFieldConfigsForContext(ValueType.COST, context, config, rollUpConfiguration)

        printFieldConfigs(fieldConfigs)

        assertFieldsAreUnique(fieldConfigs)

        // verify sub elements
        val activityElementSubElement =
            listOf(
                Pair(ElementType.Activity1, ElementType.Cost1),
                Pair(ElementType.Activity2, ElementType.Cost1),
                Pair(ElementType.Activity2, ElementType.Cost2),
                Pair(ElementType.Activity3, ElementType.Cost1),
                Pair(ElementType.Activity3, ElementType.Cost2),
                Pair(ElementType.Activity3, ElementType.Cost3),
            )
        activityElementSubElement.forEach {
            assertActivitySubElement(fieldConfigs, it.first, it.second)
        }

        // verify activity sums
        val activitySum =
            listOf(
                Pair(ElementType.Activity1, setOf(ElementType.Cost1)),
                Pair(ElementType.Activity2, setOf(ElementType.Cost1, ElementType.Cost2)),
                Pair(ElementType.Activity3, setOf(ElementType.Cost1, ElementType.Cost2, ElementType.Cost3)),
            )
        activitySum.forEach {
            assertActivitySum(fieldConfigs, it.first, it.second)
        }

        // verify cost sums
        val costSum =
            listOf(
                Pair(ElementType.Cost1, setOf(ElementType.Activity1, ElementType.Activity2, ElementType.Activity3)),
                Pair(ElementType.Cost2, setOf(ElementType.Activity2, ElementType.Activity3)),
                Pair(ElementType.Cost3, setOf(ElementType.Activity3)),
            )
        costSum.forEach {
            assertCostSum(fieldConfigs, it.second, it.first)
        }
    }

    private fun assertFieldsAreUnique(fieldConfigs: List<FieldConfig>) {
        val visited = mutableSetOf<String>()
        fieldConfigs.forEach { fieldConfig ->
            Assertions.assertFalse(visited.contains(fieldConfig.fieldName), "Field name ${fieldConfig.fieldName} is not unique!")
            visited.add(fieldConfig.fieldName)
        }
    }

    private fun assertActivitySubElement(
        fieldConfigs: List<FieldConfig>,
        activityType: ElementType,
        elementType: ElementType,
    ) {
        val aggregationLevel = AggregationLevel.MACHINE
        val aggregationRole = AggregationRole.THIS

        println("\nChecking activity sub element field: $aggregationLevel, $aggregationRole, $activityType, $elementType")
        val fieldName =
            ActivityAndInvestFieldNameBuilder(
                ValueType.COST,
                aggregationLevel,
                aggregationRole,
                activityType.name,
                elementType.name,
            ).fieldName

        // check field
        val fieldConfig = fieldConfigs.singleOrNull { it.fieldName == fieldName }
        Assertions.assertNotNull(fieldConfig, "Field $fieldName does not exist in field config!")

        // check expected function
        Assertions.assertEquals(CommercialCalculationContext::assignMoneyField, fieldConfig!!.kFunction, "Invalid function!")
    }

    private fun assertActivitySum(
        fieldConfigs: List<FieldConfig>,
        activityType: ElementType,
        elementTypes: Set<ElementType>,
    ) {
        val aggregationLevel = AggregationLevel.MACHINE
        val aggregationRole = AggregationRole.THIS

        println("\nChecking activity sum field: $aggregationLevel, $aggregationRole, $activityType")
        val fieldName =
            ActivityAndInvestFieldNameBuilder(
                ValueType.COST,
                aggregationLevel,
                aggregationRole,
                activityType.name,
                null,
            ).fieldName

        // check field
        val fieldConfig = fieldConfigs.singleOrNull { it.fieldName == fieldName }
        Assertions.assertNotNull(fieldConfig, "Field $fieldName does not exist in field config!")

        // check expected function
        Assertions.assertEquals(CommercialCalculationContext::sumOfMoney, fieldConfig!!.kFunction, "Invalid function!")

        // check summands
        val expectedFieldNames =
            elementTypes.map {
                ActivityAndInvestFieldNameBuilder(ValueType.COST, aggregationLevel, aggregationRole, activityType.name, it.name).fieldName
            }.toSet()
        val actualFieldNames = fieldConfig.parameters.values.first().map { it.fieldName }.toSet()
        Assertions.assertEquals(expectedFieldNames, actualFieldNames, "Summands do not meet expectation!")
    }

    private fun assertCostSum(
        fieldConfigs: List<FieldConfig>,
        activityTypes: Set<ElementType>,
        elementType: ElementType,
    ) {
        val aggregationLevel = AggregationLevel.MACHINE
        val aggregationRole = AggregationRole.THIS

        println("\nChecking cost sum field: $aggregationLevel, $aggregationRole, $elementType")
        val fieldName = ValueFieldNameBuilder(ValueType.COST, aggregationLevel, aggregationRole, elementType.name).fieldName

        // check field
        val fieldConfig = fieldConfigs.singleOrNull { it.fieldName == fieldName }
        Assertions.assertNotNull(fieldConfig, "Field $fieldName does not exist in field config!")

        // check expected function
        Assertions.assertEquals(CommercialCalculationContext::sumOfMoney, fieldConfig!!.kFunction, "Invalid function!")

        // check summands
        val expectedFieldNames =
            activityTypes.map {
                ActivityAndInvestFieldNameBuilder(ValueType.COST, aggregationLevel, aggregationRole, it.name, elementType.name).fieldName
            }.toSet()
        val actualFieldNames = fieldConfig.parameters.values.first().map { it.fieldName }.toSet()
        Assertions.assertEquals(expectedFieldNames, actualFieldNames, "Summands do not meet expectation!")
    }

    private fun printFieldConfigs(fieldConfigs: List<FieldConfig>) {
        println("Field Configs:")
        fieldConfigs.forEach { fieldConfig ->
            println(fieldConfig.toString())
        }
    }
}
