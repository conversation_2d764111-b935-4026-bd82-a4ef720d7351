package com.nu.bom.core

import com.nu.bom.core.CalculationTestBase.Companion.logger
import com.nu.bom.core.manufacturing.commercialcalculation.rollupconfiguration.RollUpConfiguration
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataCategory
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.extension.classificationcalculator.ClassificationCalculatorOutputs
import com.nu.bom.core.manufacturing.extension.classificationcalculator.ClassificationCalculatorRestriction
import com.nu.bom.core.manufacturing.extension.lookups.CO2SteelLookup
import com.nu.bom.core.manufacturing.extension.lookups.CO2_Country
import com.nu.bom.core.manufacturing.extension.lookups.LocationPrefills
import com.nu.bom.core.manufacturing.factories.FieldFactoryService
import com.nu.bom.core.manufacturing.fieldTypes.CO2CalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.ClassificationResponse
import com.nu.bom.core.manufacturing.fieldTypes.CostCalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.CurrenciesField
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.EmissionUnits
import com.nu.bom.core.manufacturing.fieldTypes.Energy
import com.nu.bom.core.manufacturing.fieldTypes.EnergyUnits
import com.nu.bom.core.manufacturing.fieldTypes.MaterialFurnaceType
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.service.CalculationContextService
import com.nu.bom.core.manufacturing.service.ConfigurationManagementService
import com.nu.bom.core.manufacturing.service.EntityClassOrName
import com.nu.bom.core.model.MasterData
import com.nu.bom.core.model.MasterDataSelector
import com.nu.bom.core.model.configurations.ConfigurationValue
import com.nu.bom.core.model.configurations.CostModulesConfiguration
import com.nu.bom.core.service.ManufacturingEntityFactoryService
import com.nu.bom.core.service.configurations.ConfigType
import com.nu.bom.core.turn.TurningApiMock.Companion.mockCycleTimeResponse
import com.nu.bom.core.turn.model.GearHobbingOperationInfo
import com.nu.bom.core.user.AccessCheck
import com.tset.bom.clients.tsetdel.model.TsetDelFileLocation
import com.tset.core.service.currency.Currencies
import com.tset.core.service.currency.CurrencyDescription
import com.tset.core.service.domain.Currency
import org.mockito.ArgumentMatchers.anyInt
import org.mockito.ArgumentMatchers.anyMap
import org.mockito.ArgumentMatchers.anyString
import org.mockito.Mockito
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.whenever
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono
import java.math.BigDecimal

class CalculationContextServiceMock {
    companion object {
        fun create(
            mockConfigurationManagementService: ConfigurationManagementService,
            factory: ManufacturingEntityFactoryService,
            fieldFactoryService: FieldFactoryService,
        ): CalculationContextService =
            Mockito.mock(CalculationContextService::class.java).also { calculationContextService ->
                whenever(
                    calculationContextService.getMasterData(
                        any(),
                        any<MasterDataType>(),
                        anyString(),
                        anyInt(),
                        anyString(),
                    ),
                ).then {
                    Mono.just(
                        createMockMasterData(
                            type = it.arguments[1] as MasterDataType,
                            key = it.arguments[2] as String,
                            location = it.arguments[4] as String,
                            year = it.arguments[3] as Int,
                        ),
                    )
                }

                whenever(
                    calculationContextService.getMasterData(
                        any(),
                        any<MasterDataCategory>(),
                        anyString(),
                        anyInt(),
                        anyString(),
                    ),
                ).then { mock ->
                    Mono.just(
                        createMockMasterData(
                            type =
                                MasterDataType.entries
                                    .find { value -> value.category == (mock.arguments[1] as MasterDataCategory) }!!,
                            key = mock.arguments[2] as String,
                            location = mock.arguments[4] as String,
                            year = mock.arguments[3] as Int,
                        ),
                    )
                }

                whenever(
                    calculationContextService.getLatestLookupTable(
                        key = anyString(),
                        accessCheck = any(),
                        rowParser =
                            any<
                                (
                                    List<String>,
                                ) -> Class<Any>,
                            >(),
                    ),
                ).then {
                    Flux.just(
                        createMockLookup(it.getArgument(0)),
                    )
                }

                whenever(
                    calculationContextService.getLatestLookupTable(key = anyString(), accessCheck = any()),
                ).then {
                    Mono.just(
                        createMockLookup(it.getArgument(0)),
                    )
                }

                whenever(
                    calculationContextService.createEntity(
                        any(),
                        any(),
                        anyOrNull(),
                        any(),
                        any(),
                        any(),
                        anyOrNull(),
                        any(),
                        anyOrNull(),
                    ),
                ).then {
                    factory.createEntity(
                        name = it.getArgument(0) as String,
                        entityType = it.getArgument(1) as Entities,
                        clazz = it.getArgument<EntityClassOrName?>(2),
                        args = it.getArgument(3),
                        fields = it.getArgument(4),
                        overwrites = it.getArgument(5),
                        entityRef = it.getArgument(6) as String?,
                        version = it.getArgument(7) as Int,
                    )
                }

                whenever(
                    calculationContextService.createEntityWithMasterdata(
                        accessCheck = any(),
                        name = anyString(),
                        entityType = any(),
                        clazz = anyOrNull(),
                        masterDataSelector = any(),
                        version = anyInt(),
                        args = anyMap(),
                        overwrites = anyMap(),
                        fields = anyMap(),
                        entityRef = anyOrNull(),
                        entityId = anyOrNull(),
                    ),
                ).then { invocation ->
                    val selector = invocation.arguments[4] as MasterDataSelector
                    logger.info(
                        "createEntityWithMasterdata: name=${invocation.arguments[1]} overwrites = ${invocation.arguments[7]}",
                    )

                    val masterData = createMockMasterData(selector = selector)

                    factory
                        .createEntity(
                            name = invocation.getArgument(1),
                            entityType = invocation.getArgument(2),
                            clazz = invocation.getArgument(3),
                            args = invocation.getArgument(6),
                            fields = masterData.data,
                            overwrites = invocation.getArgument(7),
                            version = invocation.getArgument(5),
                            entityRef = invocation.getArgument(9),
                            masterDataSelector = selector,
                        ).toMono()
                }

                whenever(
                    calculationContextService.fieldFactoryService(),
                ).thenReturn(fieldFactoryService)

                whenever(
                    calculationContextService.getPart(any(), any()),
                ).thenReturn(Mono.empty())

                val mockClassificationResponse =
                    ClassificationResponse(
                        ClassificationResponse.ClassificationCalculatorResponse(
                            0.0,
                            listOf(0.0),
                            listOf(ClassificationCalculatorOutputs(0.0, "")),
                        ),
                    )

                whenever(
                    calculationContextService.getClassificationCalculator(
                        any(),
                        any<CO2SteelLookup>(),
                        any(),
                    ),
                ).thenReturn(Mono.just(mockClassificationResponse))

                whenever(
                    calculationContextService.getClassificationCalculator(
                        any(),
                        any<List<Pair<String, ClassificationCalculatorRestriction>>>(),
                        any(),
                    ),
                ).thenReturn(Mono.just(mockClassificationResponse))

                whenever(
                    calculationContextService.createEntitiesFromTemplate(
                        any(),
                        any(),
                        any(),
                        any(),
                        any(),
                        any(),
                    ),
                ).thenReturn(Flux.empty())

                whenever(
                    calculationContextService.findMachiningTool(
                        any(),
                        any(),
                        anyOrNull(),
                        any(),
                        anyOrNull(),
                        anyOrNull(),
                        any(),
                    ),
                ).thenReturn(
                    Mono.just(
                        listOf(
                            MasterData(
                                MasterDataSelector(
                                    MasterDataType.TOOL,
                                    "key",
                                    null,
                                    null,
                                ),
                                mapOf("toolId" to Text("milling")),
                            ),
                        ),
                    ),
                )

                whenever(
                    calculationContextService.getDefaultConfigurationKey(any(), any<ConfigType<*>>()),
                ).then { mock ->
                    mockConfigurationManagementService.getDefaultConfigurationKey(
                        mock.arguments[0] as AccessCheck,
                        mock.arguments[1] as ConfigType<*>,
                    )
                }

                whenever(
                    calculationContextService.getConfiguration(any(), any<ConfigType<ConfigurationValue>>(), any()),
                ).then { mock ->
                    mockConfigurationManagementService.getDefaultConfiguration(
                        mock.arguments[0] as AccessCheck,
                        mock.arguments[1] as ConfigType<*>,
                    )
                }

                whenever(calculationContextService.getCostCalculationConfiguration(any(), any(), any())).then {
                    val key = it.arguments[1] as CostCalculationOperationsConfigurationKey
                    val rollUpConfig = it.arguments[2] as RollUpConfiguration
                    mockConfigurationManagementService.getInternalCostCalculationConfiguration(
                        accessCheck = it.arguments[0] as AccessCheck,
                        key = key,
                        rollUpConfiguration = rollUpConfig,
                    )
                }

                whenever(calculationContextService.getCO2CalculationConfiguration(any(), any(), any())).then {
                    val key = it.arguments[1] as CO2CalculationOperationsConfigurationKey
                    val rollUpConfig = it.arguments[2] as RollUpConfiguration
                    mockConfigurationManagementService.getInternalCO2CalculationConfiguration(
                        accessCheck = it.arguments[0] as AccessCheck,
                        key = key,
                        rollUpConfiguration = rollUpConfig,
                    )
                }

                whenever(
                    calculationContextService.getCostModuleConfiguration(
                        any(),
                        any(),
                        any(),
                        any(),
                    ),
                ).thenReturn(
                    Mono.just(
                        CostModulesConfiguration(),
                    ),
                )
                whenever(calculationContextService.getTurningOperationCycleTime(any()))
                    .thenAnswer { invocation ->
                        val request = invocation.arguments[0] as GearHobbingOperationInfo
                        Mono.just(mockCycleTimeResponse(request))
                    }

                whenever(calculationContextService.getTsetDelFileLocation(any(), anyString(), anyString()))
                    .thenReturn(Mono.just(TsetDelFileLocation("mockBucket", "mockFileKeyEnv", "mockFileKeyAllOther")))
            }

        private fun createMockMasterData(selector: MasterDataSelector): MasterData =
            createMockMasterData(
                type = selector.type,
                key = selector.key,
                location = selector.location,
                year = selector.year,
            )

        private fun createMockMasterData(
            type: MasterDataType,
            key: String,
            location: String,
            year: Int,
        ): MasterData =
            MasterData(
                type = type,
                key = key,
                version = 0,
                location = location,
                year = year,
                active = true,
                data =
                    mapOf(
                        "displayDesignation" to Text("masterData for $key"),
                        "designation" to Text("masterData for $key"),
                        "energyCost" to Money(0.1611.toBigDecimal()),
                        "countryId" to Text("1"),
                        "country" to Text("Germany"),
                        "interestRate" to Rate(0.04444.toBigDecimal()),
                        "leasingFeeProduction" to Money(5.478.toBigDecimal()),
                        "laborBurdenShift1" to Rate(5.478.toBigDecimal()),
                        "laborBurdenShift2" to Rate(5.478.toBigDecimal()),
                        "laborBurdenShift3" to Rate(5.478.toBigDecimal()),
                        "laborBurdenShift4" to Rate(5.478.toBigDecimal()),
                        "ccy" to
                            com.nu.bom.core.manufacturing.fieldTypes
                                .Currency("EUR"),
                        "currencies" to CurrenciesField(mockCurrencies()),
                        "exchangeRate" to Rate(BigDecimal.ONE),
                    ),
                refKey = null,
            )

        private fun mockCurrencies() =
            Currencies(
                mapOf(
                    Currency("EUR") to
                        CurrencyDescription(
                            "Euros",
                            "#,##0.00",
                        ),
                ),
            )

        private fun createMockLookup(table: String): Any =
            when (table) {
                "countryId" -> mapOf("1" to "Germany")
                "CO2_Location" ->
                    LocationPrefills(
                        Text("1"),
                        Emission(BigDecimal.ONE, EmissionUnits.GRAM_CO2E),
                        Emission(BigDecimal.ONE, EmissionUnits.GRAM_CO2E),
                        Energy(BigDecimal.ONE, EnergyUnits.KILOWATTHOUR),
                        Emission(BigDecimal.ONE, EmissionUnits.GRAM_CO2E),
                        Emission(BigDecimal.ONE, EmissionUnits.GRAM_CO2E),
                        Rate(BigDecimal.ONE),
                        Rate(BigDecimal.ONE),
                        Emission(BigDecimal.ONE, EmissionUnits.GRAM_CO2E),
                        Emission(BigDecimal.ONE, EmissionUnits.GRAM_CO2E),
                        Num(1),
                        Num(1),
                    )

                "CO2_Steel" ->
                    CO2SteelLookup(
                        legClass = Num(1),
                        furnaceType = MaterialFurnaceType.BLAST_FURNACE,
                        pigIron = Pair(Num(0), Num(0)),
                        ownScrap = Pair(Num(0), Num(0)),
                        purchasedScrap = Pair(Num(0), Num(0)),
                        quantityElectricityMelting = QuantityUnit(0.0),
                        quantityErdgasHeatSteelPipeBar = QuantityUnit(0.0),
                        quantityErdgasHeatSteelSheetWire = QuantityUnit(0.0),
                        quantityErdgasFirstRolling = QuantityUnit(0.0),
                        quantityElectricityFirstRolling = QuantityUnit(0.0),
                        quantityErdgasSecondRolling = QuantityUnit(0.0),
                        quantityElectricitySecondRolling = QuantityUnit(0.0),
                        quantityHeatRolled = QuantityUnit(0.0),
                        quantityBlockForBarForged = QuantityUnit(0.0),
                        quantityBlockForHZ = QuantityUnit(0.0),
                        quantityHZForBarRolled = QuantityUnit(0.0),
                        quantityHZForSheetRolled = QuantityUnit(0.0),
                        quantityForAluExtruded = QuantityUnit(0.0),
                    )

                "CO2_Country" ->
                    CO2_Country(
                        primaryShare = BigDecimal.ONE,
                        secondaryShare = BigDecimal.ONE,
                        primaryTCo2_tAl = BigDecimal.ONE,
                        gCO2_kWh = BigDecimal.ONE,
                        averageTCo2_tAl = BigDecimal.ONE,
                        countryId = "1",
                    )
                else -> any()
            }
    }
}
