package com.nu.bom.core.service

import com.nu.bom.core.CalculationTestBase
import com.nu.bom.core.exception.ErrorCodedException
import com.nu.bom.core.exception.readable.ErrorCode
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure.CurrentCostOperationConfiguration
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.CostCalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.service.ConfigurationManagementService
import com.nu.bom.core.manufacturing.testentities.TestManufacturing
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.configurations.SemanticVersion
import com.nu.bom.core.service.configurations.ConfigType
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.assertThatMono
import org.assertj.core.api.Assertions.assertThat
import org.bson.types.ObjectId
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import reactor.core.publisher.Mono

@ExtendWith(MockitoExtension::class)
class OperationConfigServiceTest : CalculationTestBase() {
    @Mock
    lateinit var bomNodeService: BomNodeService

    @Mock
    lateinit var configurationManagementService: ConfigurationManagementService

    @Test
    fun `gets configuration when key is found`() {
        val service = OperationConfigService(bomNodeService, configurationManagementService, mock())
        val accessCheck = mock<AccessCheck>()
        val bomNodeId = ObjectId()
        val branchId = ObjectId()

        val costCalculationKey =
            CostCalculationOperationsConfigurationKey(
                ConfigurationIdentifier("mock", SemanticVersion.initialVersion()),
            )
        val manufacturing =
            addObject(
                "Test Manufacturing",
                TestManufacturing::class.java,
                overrides =
                    mapOf(
                        "costCalculationOperationKey" to costCalculationKey,
                    ),
            )
        val snapshot =
            BomNodeSnapshot(
                name = "root",
                year = 2023,
                title = "title",
                manufacturing = manufacturing,
                accountId = null,
            ).apply {
                projectId = ObjectId()
            }
        whenever(
            bomNodeService.getBomNode(
                accessCheck,
                bomNodeId,
                branchId,
            ),
        ).thenReturn(Mono.just(snapshot))

        val mockedConfiguration = mock<CurrentCostOperationConfiguration>()
        whenever(
            configurationManagementService.getConfigurationById(
                accessCheck,
                ConfigType.CommercialCostOperations,
                costCalculationKey.res,
            ),
        ).thenReturn(Mono.just(mockedConfiguration))

        val result = service.getOperationConfig(accessCheck, bomNodeId, branchId)

        assertThatMono(result).satisfies({
            assertThat(it).isEqualTo(mockedConfiguration)
        })
    }

    @Test
    fun `returns error when mandatory configuration key field is not found`() {
        val service = OperationConfigService(bomNodeService, configurationManagementService, mock())
        val accessCheck = mock<AccessCheck>()
        val bomNodeId = ObjectId()
        val branchId = ObjectId()

        val costCalculationKey =
            CostCalculationOperationsConfigurationKey(
                ConfigurationIdentifier("mock", SemanticVersion.initialVersion()),
            )
        val manufacturing =
            addObject(
                "Test Manufacturing",
                TestManufacturing::class.java,
            )
        val snapshot =
            BomNodeSnapshot(
                name = "root",
                year = 2023,
                title = "title",
                manufacturing = manufacturing,
                accountId = null,
            ).apply {
                projectId = ObjectId()
            }
        whenever(
            bomNodeService.getBomNode(
                accessCheck,
                bomNodeId,
                branchId,
            ),
        ).thenReturn(Mono.just(snapshot))

        val result = service.getOperationConfig(accessCheck, bomNodeId, branchId)
        assertThrows<ErrorCodedException> {
            result.block()
        }.let { exception ->
            assertThat(exception.errorCode)
                .isEqualTo(ErrorCode.MANDATORY_FIELDS_MISSING)
        }
    }
}
