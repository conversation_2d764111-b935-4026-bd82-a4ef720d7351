package com.nu.bom.core.manufacturing.masterdata

import com.nu.bom.core.CalculationTestBase
import com.nu.bom.core.MdLookupServiceMock
import com.nu.bom.core.manufacturing.entities.BaseManufacturingFields
import com.nu.bom.core.manufacturing.entities.BaseMaterial
import com.nu.bom.core.manufacturing.entities.Consumable
import com.nu.bom.core.manufacturing.entities.ElectronicComponent
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.extension.COMMERCIAL_CALCULATION_COST_FIELD_PACKAGE
import com.nu.bom.core.manufacturing.fieldTypes.Date
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.MountingType
import com.nu.bom.core.manufacturing.fieldTypes.PiecesUnits
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.masterdata.operationsAndExecutionContext.MasterdataContextOperationConfigBuilders
import com.nu.bom.core.model.configurations.CurrentEffectivityDefinition
import com.nu.bom.core.model.configurations.CustomCostField
import com.nu.bom.core.model.configurations.CustomFieldsConfiguration
import com.nu.bom.core.model.configurations.CustomMasterdataLovField
import com.nu.bom.core.model.configurations.masterdata.CustomFieldInfo
import com.nu.bom.core.model.configurations.masterdata.EffectivityReference
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.EffectivityType
import com.nu.bom.core.service.configurations.ConfigType
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService
import com.nu.masterdata.dto.v1.basicdata.ClassificationBaseInfoDto
import com.nu.masterdata.dto.v1.basicdata.ClassificationFieldsInfoDto
import com.nu.masterdata.dto.v1.basicdata.FieldWithVisibilityDto
import com.nu.masterdata.dto.v1.detail.DateValueDto
import com.nu.masterdata.dto.v1.detail.LovValueDto
import com.nu.masterdata.dto.v1.detail.NumericValueDto
import com.nu.masterdata.dto.v1.detail.UnitMeasurementDto
import com.nu.masterdata.dto.v1.detail.table.DetailQueryResponseDto
import com.nu.masterdata.dto.v1.detail.table.HeaderAndDetailDto
import com.nu.masterdata.dto.v1.fields.LovFieldDefinitionDto
import com.nu.masterdata.dto.v1.header.HeaderDetailQueryResponseDto
import com.nu.masterdata.dto.v1.header.HeaderDto
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import com.nu.masterdata.dto.v1.schema.LovFieldSchemaDto
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.data.Offset
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.whenever
import reactor.core.publisher.Mono
import java.math.BigDecimal
import java.time.LocalDate

class MaterialLookupTest : CalculationTestBase() {
    companion object {
        private const val CUSTOM_LOV = CustomFieldsConfiguration.CUSTOM_FIELD_PREFIX + "lov"
        private const val CUSTOM_MINS = CustomFieldsConfiguration.CUSTOM_FIELD_PREFIX + "minutes"

        private val effResponse =
            mapOf(
                K("pieces") to NumericValueDto(1.0, UnitMeasurementDto(K("pcs"))),
                K("seconds") to NumericValueDto(60.0, UnitMeasurementDto(K(TimeUnits.SECOND.name.lowercase()))),
                K("minutes") to NumericValueDto(1.0, UnitMeasurementDto(K(TimeUnits.MINUTE.name.lowercase()))),
                K("lov.field") to LovValueDto(K("entry"), "Entry Name"),
                K("date") to DateValueDto(LocalDate.of(2024, 1, 1)),
                K("custom.lov") to LovValueDto(K("entry"), "Entry Name"),
                K("custom-minutes") to NumericValueDto(1.0, UnitMeasurementDto(K(TimeUnits.MINUTE.name.lowercase()))),
            )

        private val effDefs =
            // We have to use source fields that are part of [Manufacturing]
            listOf(
                CurrentEffectivityDefinition(
                    "pieces",
                    EffectivityType.NUMERIC,
                    BaseManufacturingFields::peakUsableProductionVolumePerYear.name,
                ),
                CurrentEffectivityDefinition("seconds", EffectivityType.NUMERIC, "timePerShift"),
                CurrentEffectivityDefinition("minutes", EffectivityType.NUMERIC, "timePerShift"),
                CurrentEffectivityDefinition("lov.field", EffectivityType.LOV, "overheadMethod"),
                CurrentEffectivityDefinition("date", EffectivityType.DATE, "calculationDate"),
                CurrentEffectivityDefinition(
                    "custom.lov",
                    EffectivityType.LOV,
                    CUSTOM_LOV,
                    customFieldInfo = CustomFieldInfo.Lov("custom lov"),
                ),
                CurrentEffectivityDefinition(
                    "custom-minutes",
                    EffectivityType.NUMERIC,
                    CUSTOM_MINS,
                    customFieldInfo =
                        CustomFieldInfo.Cost(
                            "custom minutes",
                            Time::class.simpleName!!,
                            TimeUnits.MINUTE.name,
                        ),
                ),
            )

        private val masterdataConfig =
            MasterdataTsetConfigurationService
                .getTsetMasterdataConfig()
                .let { default ->
                    default.copy(
                        effectivityDefinitions = default.effectivityDefinitions + effDefs,
                        overheadsConfiguration =
                            default
                                .overheadsConfiguration
                                .copy(effectivities = effDefs.map { EffectivityReference(it.fieldDefinitionKey) }),
                    )
                }

        private val customFieldsConfig =
            CustomFieldsConfiguration(
                listOf(
                    CustomMasterdataLovField(CUSTOM_LOV, "custom lov", Text("default"), lovTypeKey = "something"),
                    CustomCostField(
                        CUSTOM_MINS,
                        "custom minutes",
                        null,
                        fieldType = Time::class.simpleName!!,
                        displayUnit = TimeUnits.MINUTE.name,
                    ),
                ),
            )

        private val timePerShift = Time(8.0, TimeUnits.HOUR)
        private val expectedRequested =
            mapOf(
                "minutes" to timePerShift,
                "seconds" to timePerShift,
                "lov-field" to Text(masterdataConfig.overheadMethodConfiguration.defaultOverheadMethod),
                "lov-field" + MasterdataContextOperationConfigBuilders.EFFECTIVITY_DISPLAY_NAME_SUFFIX to
                    Text(
                        "Build to print (automotive)",
                    ),
                "date" to Date.utcNow(),
                "custom-lov" to Text("default"),
                "custom-lov" + MasterdataContextOperationConfigBuilders.EFFECTIVITY_DISPLAY_NAME_SUFFIX to Text("defaultName"),
            )

        private val expectedActual =
            mapOf(
                "pieces" to QuantityUnit(1.0),
                "seconds" to Time(1.0, TimeUnits.MINUTE),
                "minutes" to Time(1.0, TimeUnits.MINUTE),
                "lov-field" to Text("entry"),
                "lov-field" + MasterdataContextOperationConfigBuilders.EFFECTIVITY_DISPLAY_NAME_SUFFIX to Text("Entry Name"),
                "date" to Date.of(2024, 1, 1),
                "custom-lov" to Text("entry"),
                "custom-lov" + MasterdataContextOperationConfigBuilders.EFFECTIVITY_DISPLAY_NAME_SUFFIX to Text("Entry Name"),
                "custom-minutes" to Time(1.0, TimeUnits.MINUTE),
            )
    }

    @BeforeEach
    fun setup() {
        extensionManager.enablePackage(COMMERCIAL_CALCULATION_COST_FIELD_PACKAGE)
        MdLookupServiceMock.mockLookup(lookupService, effResponse)
        mockConfiguration(ConfigType.Masterdata, masterdataConfig)
        mockConfiguration(ConfigType.CustomFields, customFieldsConfig)

        whenever(mdNbkUnitMapperService.mapToNbkUnit(UnitMeasurementDto(K("tset.unit.piece.piece")))).thenReturn(PiecesUnits.PIECE)
    }

    @Test
    fun `Consumable masterdata lookup test`() {
        val manu =
            createManufacturing(
                name = "manu",
                parameters =
                    mapOf(
                        Manufacturing::calculationDate.name to Date.of(2024, 1, 1),
                        Manufacturing::location.name to Text("tset.ref.classification.belgium"),
                    ),
            )
        val consumable = Consumable("b1")
        builder.addInputs(
            consumable,
            parameters = emptyMap(),
            fields =
                mapOf(
                    BaseMaterial::headerKey.name to Text("4711"),
                ),
        )
        manu.addChild(consumable)
        calculate(manu)
        // printResults()

        val pricePerUnit =
            consumable.fieldWithResults
                .single { it.name.name == BaseMaterial::pricePerUnit.name }
                .result.res as BigDecimal

        val cO2PerUnit =
            consumable.fieldWithResults
                .single { it.name.name == BaseMaterial::cO2PerUnit.name }
                .result.res as BigDecimal

        val baseCurrency =
            consumable.fieldWithResults
                .single { it.name.name == BaseMaterial::baseCurrency.name }
                .result.res as String

        val dimension =
            consumable.fieldWithResults
                .single { it.name.name == BaseMaterial::dimension.name }
                .result.res as Dimension.Selection

        val costUnit =
            consumable.fieldWithResults
                .single { it.name.name == BaseMaterial::costUnit.name }
                .result.res as String

        val quantityUnit =
            consumable.fieldWithResults
                .single { it.name.name == BaseMaterial::quantityUnit.name }
                .result.res as String

        assertThat(pricePerUnit).isCloseTo(0.027238678876405038.toBigDecimal(), Offset.offset(1E-9.toBigDecimal()))
        assertThat(cO2PerUnit).isEqualTo(0.0343434343.toBigDecimal())

        assertThat(baseCurrency).isEqualTo("USD")

        assertThat(dimension).isEqualTo(Dimension.Selection.NUMBER)
        assertThat(costUnit).isEqualTo("PIECE")
        assertThat(quantityUnit).isEqualTo("PIECE")
    }

    @Test
    fun `Elco masterdata lookup test`() {
        val mountingTypeFieldKey = "tset.ref.field.mounting-type"
        val manufacturerFieldKey = "tset.ref.field.manufacturer"
        val fields =
            listOf(
                LovFieldDefinitionDto(
                    key = SimpleKeyDto(key = mountingTypeFieldKey),
                    systemManaged = false,
                    defaultValue = null,
                    fieldSchema =
                        LovFieldSchemaDto(
                            lovTypeKey = SimpleKeyDto("tset.ref.lov-type.mounting-type"),
                            displayName = "Mounting type",
                        ),
                ),
                LovFieldDefinitionDto(
                    key = SimpleKeyDto(key = manufacturerFieldKey),
                    systemManaged = false,
                    defaultValue = null,
                    fieldSchema =
                        LovFieldSchemaDto(
                            lovTypeKey = SimpleKeyDto("tset.ref.lov-type.elco.manufacturer"),
                            displayName = "Manufacturer",
                        ),
                ),
            )

        Mockito
            .`when`(
                mdBasicDataService.getClassificationTypeFields(
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                ),
            ).thenReturn(
                Mono.just(
                    ClassificationFieldsInfoDto(
                        classifications = emptyList(),
                        fields = fields.map { FieldWithVisibilityDto(it, columnVisibleByDefault = true, filterVisibleByDefault = true) },
                    ),
                ),
            )

        whenever(mdDetailService.postDetailEntriesSearch(anyOrNull(), anyOrNull(), anyOrNull()))
            .thenAnswer {
                Mono.just(
                    DetailQueryResponseDto(
                        content =
                            listOf(
                                HeaderAndDetailDto(
                                    headerDto =
                                        HeaderDetailQueryResponseDto(
                                            key = SimpleKeyDto("4711"),
                                            name = "4711",
                                            headerTypeKey = SimpleKeyDto("tset.ref.header-type.material"),
                                            active = true,
                                            detailValueSchema = null,
                                            classifications =
                                                mapOf(
                                                    SimpleKeyDto(
                                                        "tset.ref.classification-type.material",
                                                    ) to
                                                        listOf(
                                                            ClassificationBaseInfoDto(
                                                                key = SimpleKeyDto("tset.ref.classification.electronic-component"),
                                                                name = "Elco",
                                                                classificationTypeKey =
                                                                    SimpleKeyDto(
                                                                        "tset.ref.classification-type.material",
                                                                    ),
                                                            ),
                                                        ),
                                                ),
                                            classificationFieldValues =
                                                mapOf(
                                                    SimpleKeyDto(
                                                        mountingTypeFieldKey,
                                                    ) to
                                                        LovValueDto(
                                                            SimpleKeyDto("tset.ref.lov-entry.smd"),
                                                            displayName = "SMD",
                                                        ),
                                                    SimpleKeyDto(manufacturerFieldKey) to
                                                        LovValueDto(
                                                            SimpleKeyDto("bosch-key"),
                                                            displayName = "Bosch",
                                                        ),
                                                ),
                                        ),
                                    detailDto = null,
                                ),
                            ),
                        number = 0,
                        size = 1,
                        totalElements = 1,
                        maxCountTruncated = false,
                    ),
                )
            }

        whenever(mdHeaderService.getHeaderByKey(anyOrNull(), anyOrNull(), anyOrNull()))
            .thenAnswer {
                Mono.just(
                    HeaderDto(
                        key = SimpleKeyDto("4711"),
                        name = "4711",
                        headerTypeKey = SimpleKeyDto("tset.ref.header-type.material"),
                        active = true,
                        detailValueSchema = null,
                        classifications =
                            mapOf(
                                SimpleKeyDto(
                                    "tset.ref.classification-type.material",
                                ) to listOf(SimpleKeyDto("tset.ref.classification.electronic-component")),
                            ),
                        classificationFieldValues =
                            mapOf(
                                SimpleKeyDto(
                                    mountingTypeFieldKey,
                                ) to LovValueDto(SimpleKeyDto("tset.ref.lov-entry.smd"), displayName = "SMD"),
                                SimpleKeyDto(manufacturerFieldKey) to LovValueDto(SimpleKeyDto("bosch-key"), displayName = "Bosch"),
                            ),
                    ),
                )
            }

        val manu =
            createManufacturing(
                name = "manu",
                parameters =
                    mapOf(
                        Manufacturing::calculationDate.name to Date.of(2024, 1, 1),
                        Manufacturing::location.name to Text("tset.ref.classification.belgium"),
                    ),
            )
        val elco = ElectronicComponent("b1")
        builder.addInputs(
            elco,
            parameters = emptyMap(),
            fields =
                mapOf(
                    BaseMaterial::headerKey.name to Text("4711"),
                ),
        )
        manu.addChild(elco)
        calculate(manu)

        printResults()

        val mountingTypeFirstCalculation =
            elco.fieldWithResults
                .single { it.name.name == ElectronicComponent::mountingType.name }
                .result.res as MountingType.Selection
        assertThat(mountingTypeFirstCalculation).isEqualTo(MountingType.Selection.SMD)

        calculate(manu)

        val mountingTypeSecondCalculation =
            elco.fieldWithResults
                .single { it.name.name == ElectronicComponent::mountingType.name }
                .result.res as MountingType.Selection
        assertThat(mountingTypeSecondCalculation).isEqualTo(MountingType.Selection.SMD)

        // todo add validations for manufacturer
    }
}
