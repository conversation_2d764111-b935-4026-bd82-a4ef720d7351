package com.nu.bom.core.service.file

import com.nu.bom.core.exception.ErrorCodedException
import com.nu.bom.core.exception.readable.ErrorCode
import com.nu.bom.core.exception.userException.UnsupportedMediaTypeException
import com.nu.bom.core.model.toFileUploadId
import com.nu.bom.core.model.toUUID
import com.nu.bom.core.service.bomrads.BomradsFileUploadService
import com.nu.bom.core.service.file.module.UploadModule
import com.nu.bom.core.threedb.ThreeDbServiceMock
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.user.UserService
import com.nu.bomrads.dto.FileUploadDto
import com.nu.bomrads.dto.OwnerType
import com.nu.bomrads.enumeration.MigratedEnum
import com.nu.bomrads.id.FileUploadId
import com.nu.user.UserInfoDto
import com.tset.bom.clients.VirusScanner
import org.assertj.core.api.Assertions.assertThat
import org.bson.types.ObjectId
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.doAnswer
import org.mockito.kotlin.eq
import org.mockito.kotlin.whenever
import reactor.core.publisher.Mono
import reactor.test.StepVerifier
import java.time.Instant
import java.util.Date
import java.util.Optional
import java.util.UUID

@ExtendWith(MockitoExtension::class)
class SecureFileServiceImplTests {
    @Mock
    private lateinit var mediaTypeDetectionService: MediaTypeDetectionService

    @Mock
    private lateinit var virusScanner: VirusScanner

    @Mock
    private lateinit var userService: UserService

    @Mock
    private lateinit var bomradsFileUploadService: BomradsFileUploadService

    @Mock
    private lateinit var uploadModule: UploadModule

    @Mock
    private lateinit var s3CloudFileService: S3CloudAwareFileService

    private lateinit var fileService: SecureFileService

    private val eCorp = ObjectId.get()
    private val uCorpFile = OwnerType.valueOf("BOM_NODE")
    private val eCorpFileAttachment = UploadType.NESTING_SVG.name

    private val alice =
        AccessCheck(
            accountId = eCorp.toHexString(),
            accountName = "",
            accountLabel = "",
            token = "",
            userId = UUID.randomUUID().toString(),
            realm = "",
        )

    private val poemTxtFilename = "poem.txt"
    private val poemTxtBytes = "lorem ipsum dolor, green is a nice color".toByteArray()

    private val poemTxt =
        object : UploadablePayload {
            override fun getFilename(): String = poemTxtFilename

            override fun getBytes(): Mono<ByteArray> = Mono.just(poemTxtBytes)
        }

    @BeforeEach
    fun setup() {
        doAnswer { Mono.just(it.getArgument<ByteArray>(0)) }.whenever(virusScanner).scan(any())

        whenever(uploadModule.updateOwner(any(), any(), any())).thenReturn(Mono.empty())
        whenever(uploadModule.getOwnerType()).thenReturn(uCorpFile)
        whenever(uploadModule.getUploadType()).thenReturn(UploadType.NESTING_SVG)
        whenever(uploadModule.isMimeTypeAllowed(any(), any())).thenReturn(Mono.just(true))
        whenever(uploadModule.isValidOwnerId(any())).thenReturn(true)
        whenever(userService.getUserById(any(), any())).thenReturn(Mono.just(Optional.empty<UserInfoDto>()))

        doAnswer { Mono.just(UUID.randomUUID().toString()) }.whenever(s3CloudFileService)
            .upload(any(), any<AccessCheck>())
        fileService =
            SecureFileServiceImpl(
                s3FileService = s3CloudFileService,
                mediaTypeDetector = mediaTypeDetectionService,
                virusScanner = virusScanner,
                userService = userService,
                bomradsFileUploadService = bomradsFileUploadService,
                uploadModules = listOf(uploadModule),
                threeDbService = ThreeDbServiceMock(),
            )
    }

    @AfterEach
    fun teardown() {
        Mockito.reset(
            uploadModule,
            virusScanner,
            s3CloudFileService,
            userService,
            bomradsFileUploadService,
        )
    }

    @Test
    fun `not allowed mime type throws exception on upload`() {
        whenever(mediaTypeDetectionService.detect(poemTxtBytes, poemTxtFilename)).thenReturn("text/plain")

        whenever(uploadModule.isMimeTypeAllowed("text/plain", poemTxt.getFilename()))
            .thenReturn(Mono.just(false))

        StepVerifier.create(
            fileService.upload(alice, UploadType.NESTING_SVG, null, poemTxt),
        ).expectErrorSatisfies {
            assertThat(it).isInstanceOf(UnsupportedMediaTypeException::class.java)
        }.verify()
    }

    @Test
    fun `positive virus scan throws exception on upload`() {
        whenever(mediaTypeDetectionService.detect(any(), any()))
            .thenReturn("text/plain")
        whenever(virusScanner.scan(any()))
            .thenReturn(Mono.error(VirusScanner.VirusFoundException("fsociety")))

        StepVerifier.create(
            fileService.upload(alice, UploadType.NESTING_SVG, null, poemTxt),
        ).expectErrorSatisfies {
            assertThat(it).isInstanceOf(ErrorCodedException::class.java)
            assertThat((it as ErrorCodedException).errorCode)
                .isEqualTo(ErrorCode.CONTAMINATED_FILE)
            assertThat(it).hasMessageContaining("fsociety")
        }.verify()
    }

    @Test
    fun downloadLegacy() {
        val id = ObjectId.get()
        val uploadId = UUID.randomUUID().toString()

        val fileUpload =
            FileUploadDto(
                id = FileUploadId(id.toUUID()),
                uploadType = eCorpFileAttachment,
                uploadId = uploadId,
                ownerType = uCorpFile,
                ownerId = "123",
                filename = poemTxtFilename,
                mimeType = "text/plain",
                createdDate = Date.from(Instant.now()),
                createdBy = alice.userId,
            )

        whenever(bomradsFileUploadService.findByIdAndDeletedIsFalse(alice, FileUploadId(id.toUUID()))).thenReturn(
            Mono.just(
                fileUpload,
            ),
        )

        doAnswer { Mono.just(poemTxtBytes) }.whenever(s3CloudFileService).download(eq(uploadId), any())

        StepVerifier.create(
            fileService.download(alice, id.toHexString()),
        ).assertNext { response ->
            assertThat(response.filename).isEqualTo(poemTxtFilename)
            assertThat(response.payload).isEqualTo(poemTxtBytes)
        }.verifyComplete()
    }

    @Test
    fun downloadS3Account() {
        val id = ObjectId.get()
        val uploadId = UUID.randomUUID().toString()

        val fileUpload =
            FileUploadDto(
                id = FileUploadId(id.toUUID()),
                uploadType = eCorpFileAttachment,
                uploadId = uploadId,
                ownerType = uCorpFile,
                ownerId = "123",
                filename = poemTxtFilename,
                mimeType = "text/plain",
                migrated = MigratedEnum.MIGRATED,
                createdDate = Date.from(Instant.now()),
                createdBy = alice.userId,
            )

        whenever(bomradsFileUploadService.findByIdAndDeletedIsFalse(alice, FileUploadId(id.toUUID()))).thenReturn(
            Mono.just(
                fileUpload,
            ),
        )

        doAnswer { Mono.just(poemTxtBytes) }.whenever(s3CloudFileService).download(uploadId, alice)

        StepVerifier.create(
            fileService.download(alice, id.toHexString()),
        ).assertNext { response ->
            assertThat(response.filename).isEqualTo(poemTxtFilename)
            assertThat(response.payload).isEqualTo(poemTxtBytes)
        }.verifyComplete()
    }

    @Test
    fun downloadS3Feature() {
        val id = ObjectId.get()
        val uploadId = UUID.randomUUID().toString()

        val fileUpload =
            FileUploadDto(
                id = FileUploadId(id.toUUID()),
                uploadType = eCorpFileAttachment,
                uploadId = uploadId,
                ownerType = uCorpFile,
                ownerId = "123",
                filename = poemTxtFilename,
                mimeType = "text/plain",
                migrated = MigratedEnum.MIGRATED,
                createdDate = Date.from(Instant.now()),
                createdBy = alice.userId,
            )

        whenever(bomradsFileUploadService.findByIdAndDeletedIsFalse(alice, id.toFileUploadId())).thenReturn(
            Mono.just(
                fileUpload,
            ),
        )

        doAnswer { Mono.just(poemTxtBytes) }.whenever(s3CloudFileService).download(uploadId, alice)

        StepVerifier.create(
            fileService.download(alice, id.toHexString()),
        ).assertNext { response ->
            assertThat(response.filename).isEqualTo(poemTxtFilename)
            assertThat(response.payload).isEqualTo(poemTxtBytes)
        }.verifyComplete()
    }

    @Test
    fun downloadS3WithMultipleOwners() {
        val id = ObjectId.get()
        val uploadId = UUID.randomUUID().toString()

        val fileUpload =
            FileUploadDto(
                id = FileUploadId(id.toUUID()),
                uploadType = eCorpFileAttachment,
                uploadId = uploadId,
                ownerType = uCorpFile,
                ownerId = "123",
                filename = poemTxtFilename,
                mimeType = "text/plain",
                migrated = MigratedEnum.MIGRATED,
                createdDate = Date.from(Instant.now()),
                createdBy = alice.userId,
            )

        whenever(bomradsFileUploadService.findByIdAndDeletedIsFalse(alice, FileUploadId(id.toUUID()))).thenReturn(
            Mono.just(
                fileUpload,
            ),
        )

        doAnswer { Mono.just(poemTxtBytes) }.whenever(s3CloudFileService).download(uploadId, alice)

        StepVerifier.create(
            fileService.download(alice, id.toHexString()),
        ).assertNext { response ->
            assertThat(response.filename).isEqualTo(poemTxtFilename)
            assertThat(response.payload).isEqualTo(poemTxtBytes)
        }.verifyComplete()
    }
}
