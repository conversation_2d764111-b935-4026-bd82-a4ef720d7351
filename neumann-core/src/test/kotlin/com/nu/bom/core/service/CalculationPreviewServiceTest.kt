package com.nu.bom.core.service

import com.nu.bom.core.CalculationTestBase
import com.nu.bom.core.api.dtos.BomNodeDtoConversionService
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externaltointernalmapper.ExternalConfigToInternalConfigMapper
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCO2ConfigurationFactory.getDefaultCO2ElementTsetConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCO2ConfigurationFactory.getDefaultCO2OperationTsetConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCostConfigurationFactory.getDefaultCostElementTsetConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCostConfigurationFactory.getDefaultCostOperationTsetConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.rollupconfiguration.RollUpConfiguration
import com.nu.bom.core.manufacturing.entities.ManualManufacturingStep
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.CostCalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.service.ConfigurationManagementService
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.configurations.SemanticVersion
import com.nu.bom.core.service.configurations.ConfigType
import com.nu.bom.core.utils.AccountUtil
import com.nu.bomrads.dto.admin.ClientAccountDTO
import com.nu.bomrads.dto.admin.ProjectDTO
import com.nu.bomrads.id.AccountId
import com.nu.bomrads.id.ProjectId
import com.nu.user.UserInfoDto
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import reactor.core.publisher.Mono
import java.time.Instant
import java.util.Date

@ExtendWith(MockitoExtension::class)
class CalculationPreviewServiceTest : CalculationTestBase() {
    @Mock(lenient = true)
    private lateinit var cbdService: CbdService

    @Mock(lenient = true)
    private lateinit var bomNodeDtoConversionService: BomNodeDtoConversionService

    private lateinit var calculationPreviewService: CalculationPreviewService

    private lateinit var project: ProjectDTO

    @Mock(lenient = true)
    private lateinit var configurationManagementService: ConfigurationManagementService

    private val userInfoDto = UserInfoDto("key", "name")

    @BeforeEach
    fun initMocks() {
        cbdService =
            CbdService(
                CbdServiceProperties(),
            )
        calculationPreviewService =
            CalculationPreviewService(
                fieldConversionService,
                userService,
                cbdService,
                entityManager,
                bomNodeDtoConversionService,
            )
        project =
            ProjectDTO(
                ProjectId(),
                name = "name",
                key = "key",
                account = ClientAccountDTO(AccountId(), version = 1, key = "acc_key", environment = "env"),
                currency = "EUR",
                createdBy = "Created By",
                lastModifiedBy = "Last Modified By",
                created = Instant.now(),
                lastModified = Instant.now(),
            )

        `when`(configurationManagementService.getInternalCostCalculationConfiguration(any(), any(), any())).thenReturn(
            Mono.just(
                ExternalConfigToInternalConfigMapper.mapToInternalConfig(
                    ValueType.COST,
                    getDefaultCostOperationTsetConfiguration(),
                    RollUpConfiguration(),
                ),
            ),
        )

        `when`(configurationManagementService.getInternalCO2CalculationConfiguration(any(), any(), any())).thenReturn(
            Mono.just(
                ExternalConfigToInternalConfigMapper.mapToInternalConfig(
                    ValueType.CO2,
                    getDefaultCO2OperationTsetConfiguration(),
                    RollUpConfiguration(),
                ),
            ),
        )

        `when`(configurationManagementService.getDefaultConfiguration(any(), eq(ConfigType.CommercialCostElements)))
            .thenReturn(
                Mono.just(
                    getDefaultCostElementTsetConfiguration(),
                ),
            )

        `when`(configurationManagementService.getDefaultConfiguration(any(), eq(ConfigType.CommercialCo2Elements)))
            .thenReturn(
                Mono.just(
                    getDefaultCO2ElementTsetConfiguration(),
                ),
            )
    }

    @Test
    fun `location comes from country field from entity rather than location field`() {
        val calculation = prepareCalculation()
        val snapshot =
            testSnapshot(project, "snapshotName", calculation).also {
                it.branch = BranchId()
                it.lastModifiedDate = Date()
            }

        val previewDto =
            calculationPreviewService
                .getCalculationPreview(
                    calculation,
                    snapshot,
                    project,
                    AccountUtil.dummyAccessCheck(),
                ).block()!!
        assertEquals("Germany", previewDto.environment.location)
    }

    @Test
    fun `cost drivers for root manufacturing comes from partName and not display designation`() {
        val calculation = prepareCalculation()
        val snapshot =
            testSnapshot(project, "snapshotName", calculation).also {
                it.branch = BranchId()
                it.lastModifiedDate = Date()
            }

        val previewDto =
            calculationPreviewService
                .getCalculationPreview(
                    calculation,
                    snapshot,
                    project,
                    AccountUtil.dummyAccessCheck(),
                ).block()!!
        assertEquals(
            "partDesignation",
            previewDto.costDrivers
                .first()
                .displayDesignation
                ?.value,
        )
    }

    @Test
    fun `preview breakdown exist`() {
        val calculation = prepareCalculation()
        val snapshot =
            testSnapshot(project, "snapshotName", calculation).also {
                it.branch = BranchId()
                it.lastModifiedDate = Date()
            }

        val previewDto =
            calculationPreviewService
                .getCalculationPreview(
                    calculation,
                    snapshot,
                    project,
                    AccountUtil.dummyAccessCheck(),
                ).block()!!

        assertNotNull(previewDto.costPreviewBreakdown)
        assertNotNull(previewDto.co2PreviewBreakdown)

        val costPreview = previewDto.costPreviewBreakdown!!
        val co2Preview = previewDto.co2PreviewBreakdown!!

        assertEquals(0, costPreview.materials.size)
        assertEquals(0, co2Preview.materials.size)

        assertEquals(1, costPreview.steps.size)
        assertEquals(1, co2Preview.steps.size)

        assertEquals(3, costPreview.specialDirectCosts.size)
        assertEquals(3, co2Preview.specialDirectCosts.size)

        assertEquals(4, costPreview.overheads.size)
        assertEquals(4, co2Preview.overheads.size)
    }

    @Test
    fun `cost drivers for non manufacturing comes from display designation`() {
        val calculation = prepareCalculation()
        val snapshot =
            testSnapshot(project, "snapshotName", calculation).also {
                it.branch = BranchId()
                it.lastModifiedDate = Date()
            }

        val nonManufacturingChild = calculation.findChild { it.getEntityType() == Entities.MANUFACTURING_STEP.name }
        assertThat(nonManufacturingChild).isNotNull

        val previewDto =
            calculationPreviewService
                .getCalculationPreview(
                    calculation,
                    snapshot,
                    project,
                    AccountUtil.dummyAccessCheck(),
                ).block()!!
        assertEquals(
            "stepDisplayDesignation",
            previewDto.costDrivers
                .find { it.entityType == Entities.MANUFACTURING_STEP.name }
                ?.displayDesignation
                ?.value,
        )
    }

    @Test
    fun `co2 drivers contain the denominator unit and correct units`() {
        val calculation = prepareCalculation()
        val snapshot =
            testSnapshot(project, "snapshotName", calculation).also {
                it.branch = BranchId()
                it.lastModifiedDate = Date()
            }

        val previewDto =
            calculationPreviewService
                .getCalculationPreview(
                    calculation,
                    snapshot,
                    project,
                    AccountUtil.dummyAccessCheck(),
                ).block()!!
        // We always provide 4 co2 drivers, 3 main plus others
        assertThat(previewDto.co2Drivers).hasSize(4)
        // All the co2 drivers include a denominator and the correct unit
        previewDto.co2Drivers.forEach {
            assertThat(it.cost.denominatorUnit).isNotNull
            assertThat(it.cost.denominatorUnit?.unit).isEqualTo("PIECE")
            assertThat(it.cost.unit).isEqualTo("KILOGRAM_CO2E")
        }
    }

    @Test
    fun `use correct userService function to retrieve the responsible user`() {
        val calculation =
            prepareCalculation(
                inputFields =
                    mapOf(
                        "responsible" to Text("user"),
                    ),
            )
        val snapshot =
            testSnapshot(project, "snapshotName", calculation).also {
                it.branch = BranchId()
                it.lastModifiedDate = Date()
            }

        val previewDto =
            calculationPreviewService
                .getCalculationPreview(
                    calculation,
                    snapshot,
                    project,
                    AccountUtil.dummyAccessCheck(),
                ).block()!!
        assertEquals(userInfoDto, previewDto.calculationMeta.responsible)
    }

    @Test
    fun `baseCurrency is correctly handled`() {
        val calculation = prepareCalculation()
        val snapshot =
            testSnapshot(project, "snapshotName", calculation).also {
                it.branch = BranchId()
                it.lastModifiedDate = Date()
            }

        snapshot.manufacturing?.addOrUpdateField(Manufacturing::baseCurrency.name) {
            com.nu.bom.core.manufacturing.fieldTypes
                .Currency("CHF")
        }
        val previewDto =
            calculationPreviewService
                .getCalculationPreview(
                    calculation,
                    snapshot,
                    project,
                    AccountUtil.dummyAccessCheck(),
                ).block()!!
        assertEquals("CHF", previewDto.baseCurrency.ccy)

        // test fallback to EUR
        snapshot.manufacturing?.fieldWithResults?.removeIf { it.name.name == Manufacturing::baseCurrency.name }
        val previewDto2 =
            calculationPreviewService
                .getCalculationPreview(
                    calculation,
                    snapshot,
                    project,
                    AccountUtil.dummyAccessCheck(),
                ).block()!!
        assertEquals("EUR", previewDto2.baseCurrency.ccy)
    }

    private fun prepareCalculation(inputFields: Map<String, FieldResultStar> = emptyMap()): Manufacturing {
        val calculation =
            createManufacturing(
                name = "Manufacturing",
                parameters =
                    mapOf<String, FieldResultStar>(
                        "location" to Text("Germany"),
                        "partDesignation" to Text("partDesignation"),
                    ) + inputFields,
            )

        addObject(
            name = "ManufacturingStep",
            clazz = ManualManufacturingStep::class.java,
            parent = calculation,
            overrides =
                mapOf<String, FieldResultStar>(
                    "displayDesignation" to Text("stepDisplayDesignation"),
                ),
        )

        calculation.also {
            it.addFieldResult("location") { Text("Germany") }
            it.addFieldResult("peakUsableProductionVolumePerYear") { QuantityUnit(100_000.0.toBigDecimal()) }
            it.addFieldResult("costCalculationOperationKey") {
                CostCalculationOperationsConfigurationKey(ConfigurationIdentifier("mock", SemanticVersion.initialVersion()))
            }
        }
        calculate(calculation)
        return calculation
    }
}
