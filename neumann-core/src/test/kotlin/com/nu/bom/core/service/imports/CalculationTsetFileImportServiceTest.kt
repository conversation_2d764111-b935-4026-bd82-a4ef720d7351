package com.nu.bom.core.service.imports

import com.fasterxml.jackson.databind.ObjectMapper
import com.nu.bom.core.api.dtos.ImportExportDto
import com.nu.bom.core.milldrill.model.MillingDrillingSideIndex
import com.nu.bom.core.milldrill.model.MillingDrillingSketch
import com.nu.bom.core.model.TurningProfile
import com.nu.bom.core.service.exports.ExportManagerService
import com.nu.bom.core.service.exports.ExportTempFileManager
import com.nu.bom.core.service.exports.TsetFileExportService
import com.nu.bom.core.service.exports.tasks.AttachmentsExportTask
import com.nu.bom.core.service.exports.tasks.BomNodesExportTask
import com.nu.bom.core.service.exports.tasks.BranchTreesExportTask
import com.nu.bom.core.service.exports.tasks.FileDocumentsExportTask
import com.nu.bom.core.service.exports.tasks.IdsExportTask
import com.nu.bom.core.service.exports.tasks.MillingProfilesExportTask
import com.nu.bom.core.service.exports.tasks.MongoSnapshotsExportTask
import com.nu.bom.core.service.exports.tasks.NoCalcDataExportTask
import com.nu.bom.core.service.exports.tasks.PartsExportTask
import com.nu.bom.core.service.exports.tasks.ProjectExportTask
import com.nu.bom.core.service.exports.tasks.TurningProfilesExportTask
import com.nu.bom.core.service.exports.tasks.VersionedPartsExportTask
import com.nu.bom.core.utils.JacksonTest
import org.bson.types.ObjectId
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.springframework.beans.factory.annotation.Autowired
import java.io.ByteArrayInputStream

@JacksonTest
class CalculationTsetFileImportServiceTest(
    @Autowired
    private val objectMapper: ObjectMapper,
) {
    private val calculationImportExportService = Mockito.mock(CalculationImportExportService::class.java)
    private val exportStateService = Mockito.mock(ExportManagerService::class.java)
    private val exportTempFileManager = Mockito.mock(ExportTempFileManager::class.java)

    private val exportTasks =
        listOf(
            Mockito.mock(AttachmentsExportTask::class.java),
            Mockito.mock(BomNodesExportTask::class.java),
            Mockito.mock(BranchTreesExportTask::class.java),
            Mockito.mock(FileDocumentsExportTask::class.java),
            Mockito.mock(IdsExportTask::class.java),
            Mockito.mock(MillingProfilesExportTask::class.java),
            Mockito.mock(MongoSnapshotsExportTask::class.java),
            Mockito.mock(NoCalcDataExportTask::class.java),
            Mockito.mock(PartsExportTask::class.java),
            Mockito.mock(ProjectExportTask::class.java),
            Mockito.mock(TurningProfilesExportTask::class.java),
            Mockito.mock(VersionedPartsExportTask::class.java),
        )

    @Test
    fun `import parses external data`() {
        val payload =
            this::class.java.classLoader
                .getResource("importExternalData.json")!!
                .readBytes()
        val dto = objectMapper.readValue(payload, ImportExportDto::class.java)
        Assertions.assertTrue(dto.externalData.isNotEmpty())
        Assertions.assertEquals(
            CalculationImportExportService.FileUpload(
                name = "image.png",
                zipEntryName = "1461606422-image.png",
                base64Content = "",
                pureContent = null,
            ),
            dto.externalData.first().data,
        )
        Assertions.assertEquals(
            TurningProfile.TurningProfileExport(
                sketch =
                    TurningProfile.FrontendSketch(
                        geometry = null,
                        point_attributes = null,
                        line_attributes = null,
                        global_attributes = null,
                        part_properties = null,
                        scalingException = "ScalingException",
                    ),
                rawModel = null,
                partInputGroup = "partInputGroup",
            ),
            dto.externalData[1].data,
        )
        Assertions.assertEquals(
            MillingDrillingSketch(
                sketchId = ObjectId("123123123123123123123123"),
                activeSideIndex = MillingDrillingSideIndex.Left,
            ),
            dto.externalData[2].data,
        )
    }

    @Test
    fun roundTripZipCreation() {
        val payload =
            this::class.java.classLoader
                .getResource("importExternalData.json")!!
                .readBytes()
        val dto = objectMapper.readValue(payload, ImportExportDto::class.java)

        val calculationTsetFileImportService =
            CalculationTsetFileImportService(calculationImportExportService, objectMapper)
        val testFileExportService =
            TsetFileExportService(
                calculationImportExportService,
                exportStateService,
                exportTempFileManager,
                exportTasks,
            )

        val (bytes, _) = testFileExportService.createZipFile(dto)

        val importFromTestZipInputStream =
            calculationTsetFileImportService.importFromTsetZipInputStream(ByteArrayInputStream(bytes))
        Assertions.assertEquals(dto.manufacturingDto, importFromTestZipInputStream.manufacturingDto)
    }
}
