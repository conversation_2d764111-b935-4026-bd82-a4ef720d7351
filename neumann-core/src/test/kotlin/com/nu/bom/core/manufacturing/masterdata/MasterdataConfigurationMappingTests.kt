package com.nu.bom.core.manufacturing.masterdata

import com.nu.bom.core.exception.ConfigurationUserException
import com.nu.bom.core.manufacturing.MetaCache
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.TsetDefaultSkillType
import com.nu.bom.core.model.configurations.CurrentEffectivityDefinition
import com.nu.bom.core.model.configurations.CurrentMasterdataConfiguration
import com.nu.bom.core.model.configurations.CurrentMaterialMasterdataConfiguration
import com.nu.bom.core.model.configurations.masterdata.CostFactorsConfiguration
import com.nu.bom.core.model.configurations.masterdata.CostModuleSkillTypeMapping
import com.nu.bom.core.model.configurations.masterdata.CustomFieldInfo
import com.nu.bom.core.model.configurations.masterdata.EffectivityReference
import com.nu.bom.core.model.configurations.masterdata.LookupConfiguration
import com.nu.bom.core.model.configurations.masterdata.OverheadMethodConfiguration
import com.nu.bom.core.model.configurations.masterdata.RawMaterialClassifications
import com.nu.bom.core.model.configurations.masterdata.RawMaterialConfiguration
import com.nu.bom.core.model.configurations.masterdata.RegionConfiguration
import com.nu.bom.core.model.configurations.masterdata.SkillTypeConfiguration
import com.nu.bom.core.model.configurations.masterdata.TechnologyMapToClassificationKeys
import com.nu.bom.core.publicapi.dtos.configurations.CustomCostFieldTypeInfoDto
import com.nu.bom.core.publicapi.dtos.configurations.CustomMasterdataLovTypeInfoDto
import com.nu.bom.core.publicapi.dtos.configurations.MasterdataConfigurationDto
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.CostFactorsConfigurationDto
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.CostModuleSkillTypeMappingDto
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.EffectivityDefinitionDto
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.EffectivityReferenceDto
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.EffectivityType
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.HeaderTypeConsumer
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.LookupConfigurationDto
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.MaterialConfigurationDto
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.OverheadMethodConfigurationDto
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.RawMaterialClassificationsDto
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.RawMaterialConfigurationDto
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.RegionConfigurationDto
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.SkillTypeConfigurationDto
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.TechnologyMappingDto
import com.nu.bom.core.publicapi.service.configuration.MasterdataConfigurationDtoMappingService
import com.nu.bom.core.utils.assertThatThrownBy
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

class MasterdataConfigurationMappingTests {
    private val mdConfigMapper = MasterdataConfigurationDtoMappingService(MetaCache.entityManager)

    private val effectivityDto1 =
        EffectivityDefinitionDto("fdKey1", EffectivityType.DATE, "calculationDate")
    private val effectivityDto2 =
        EffectivityDefinitionDto("fdKey2", EffectivityType.NUMERIC, "timePerShift")
    private val customCostEffectivityDto =
        EffectivityDefinitionDto(
            "custom-num",
            EffectivityType.NUMERIC,
            "#CUSTOM_num",
            customFieldInfo = CustomCostFieldTypeInfoDto("Custom num", Time::class.simpleName!!, TimeUnits.MINUTE.name),
        )
    private val customLovEffectivityDto =
        EffectivityDefinitionDto(
            "custom-lov",
            EffectivityType.LOV,
            "#CUSTOM_lov",
            customFieldInfo = CustomMasterdataLovTypeInfoDto("Custom lov"),
        )

    private val lookupConfigDto1 =
        LookupConfigurationDto(
            "headerTypeKey1",
            "lookupStrategy1",
            HeaderTypeConsumer.OVERHEAD,
            effectivities = listOf("fdKey1", "custom-num", "custom-lov").map(::EffectivityReferenceDto),
        )
    private val lookupConfigSanitizedSameDto =
        LookupConfigurationDto(
            "headerTypeKey1",
            "lookupStrategy1",
            HeaderTypeConsumer.OVERHEAD,
            effectivities = listOf("fdKey1", "custom-num", "custom.num", "custom-lov").map(::EffectivityReferenceDto),
        )
    private val overheadMethodConfigurationDto =
        OverheadMethodConfigurationDto(
            overheadMethodLovTypeKey = "omlovkey",
            noOverheadMethod = "no-method",
            defaultOverheadMethod = "default-method",
        )
    private val regionConfigurationDto =
        RegionConfigurationDto(
            regionClassificationTypeKey = "location",
        )

    val costFactorsConfigurationDto =
        CostFactorsConfigurationDto(
            costFloorSpace = "TSET_COST_FLOOR_SPACE_KEY",
            costLaborBurden = "TSET_COST_SHIFT_SURCHARGE_KEY",
            costElectricityPrice = "TSET_COST_ELECTRICITY_PRICE_KEY",
            costNaturalGasPrice = "TSET_COST_NATURAL_GAS_PRICE_KEY",
            costElectricityEmissions = "TSET_COST_ELECTRICITY_EMISSIONS_KEY",
            costNaturalGasEmissionsActive = "TSET_COST_NATURAL_GAS_EMISSIONS_ACTIVE_KEY",
            costNaturalGasEmissionsPassive = "TSET_COST_NATURAL_GAS_EMISSIONS_PASSIVE_KEY",
            costFactorInterestRates = "TSET_COST_FACTOR_INTEREST_RATES_KEY",
            costAluminiumSharePrimary = "TSET_COST_ALUMINIUM_SHARE_PRIMARY_KEY",
            costAluminiumShareSecondary = "TSET_COST_ALUMINIUM_SHARE_SECONDARY_KEY",
            costAluminiumEmissionsPrimary = "TSET_COST_ALUMINIUM_EMISSIONS_PRIMARY_KEY",
            costAluminiumEmissionsSecondary = "TSET_COST_ALUMINIUM_EMISSIONS_SECONDARY_KEY",
            costWage = "TSET_COST_WAGE_KEY",
            costOxygenPrice = "TSET_COST_OXYGEN_PRICE_KEY",
            costCastExcipientsPrice = "TSET_COST_CAST_EXCIPIENTS_KEY",
            costCountryInfo = "TSET_COST_COUNTRY_ID_KEY",
        )

    val skillTypeConfigurationDto =
        SkillTypeConfigurationDto(
            skillTypeLovTypeKey = "tset.ref.lov-type.skill-type",
            masterdataDefaultSkillType = TsetDefaultSkillType.UNSKILLED_WORKER.mdKey,
            laborDefaultSkillType = TsetDefaultSkillType.UNSKILLED_WORKER.mdKey,
            setupDefaultSkillType = TsetDefaultSkillType.SETUP_TECHNICIAN.mdKey,
            toolMaintenanceDefaultSkillType = TsetDefaultSkillType.SETUP_TECHNICIAN.mdKey,
            costModuleSkillTypeMapping =
                CostModuleSkillTypeMappingDto(
                    unskilledWorker = TsetDefaultSkillType.UNSKILLED_WORKER.mdKey,
                    skilledWorker = TsetDefaultSkillType.SKILLED_WORKER.mdKey,
                    setupTechnician = TsetDefaultSkillType.SETUP_TECHNICIAN.mdKey,
                    productionSupervisor = TsetDefaultSkillType.PRODUCTION_SUPERVISOR.mdKey,
                    inspector = TsetDefaultSkillType.INSPECTOR.mdKey,
                    productionEngineer = TsetDefaultSkillType.PRODUCTION_ENGINEER.mdKey,
                    financialAccountant = TsetDefaultSkillType.FINANCIAL_ACCOUNTANT.mdKey,
                    administrationProfessional = TsetDefaultSkillType.ADMINISTRATION_PROFESSIONAL.mdKey,
                    middleManagement = TsetDefaultSkillType.MIDDLE_MANAGEMENT.mdKey,
                    technicalManager = TsetDefaultSkillType.TECHNICAL_MANAGER.mdKey,
                    chiefExecutiveOfficer = TsetDefaultSkillType.CHIEF_EXECUTIVE_OFFICER.mdKey,
                ),
        )

    private val materialConfigurationDto =
        MaterialConfigurationDto(
            materialClassificationTypeKey = "tset.ref.classification-type.material",
            consumableClassificationKey = "tset.ref.classification.consumable",
            electronicComponentClassificationKey = "tset.ref.classification.electronic-component",
            rawMaterialConfiguration =
                RawMaterialConfigurationDto(
                    rawMaterialClassificationKeys =
                        "tset.ref.classification.semi-finished-material,tset.ref.classification.raw-material",
                    rawMaterialClassifications =
                        RawMaterialClassificationsDto(
                            coilClassificationKeys = listOf("tset.ref.classification.coil"),
                            sheetClassificationKeys = listOf("tset.ref.classification.sheet"),
                            castingAlloyClassificationKeys = listOf("tset.ref.classification.casting-alloy"),
                            ingotClassificationKeys = listOf("tset.ref.classification.ingot"),
                            lamellaClassificationKeys = listOf("tset.ref.classification.lamella"),
                            plasticGranulateClassificationKeys = listOf("tset.ref.classification.plastic-granulate"),
                            rubberClassificationKeys = listOf("tset.ref.classification.rubber"),
                            sandClassificationKeys = listOf("tset.ref.classification.sand"),
                            pipeClassificationKeys = listOf("tset.ref.classification.pipe"),
                            wireRodClassificationKeys = listOf("tset.ref.classification.wire-rod"),
                            barClassificationKeys = listOf("tset.ref.classification.bar"),
                            powderClassificationKeys = listOf("tset.ref.classification.powder"),
                            waxClassificationKeys = listOf("tset.ref.classification.wax"),
                            rareEarthClassificationKeys = listOf("tset.ref.classification.rare-earth"),
                            paintClassificationKeys = listOf("tset.ref.classification.paint"),
                            metallicCoatingClassificationKeys = listOf("tset.ref.classification.metallic-coating"),
                            coatingPcbaClassificationKeys = listOf("tset.ref.classification.coating-pcba"),
                            inkClassificationKeys = listOf("tset.ref.classification.ink"),
                            paperCoilClassificationKeys = listOf("tset.ref.classification.paper-coil"),
                            paperSheetClassificationKeys = listOf("tset.ref.classification.paper-sheet"),
                            varnishClassificationKeys = listOf("tset.ref.classification.varnish"),
                            otherClassificationKeys = listOf("tset.ref.classification.other"),
                            technologyClassificationTypeKey = "tset.ref.classification-type.technology",
                        ),
                ),
        )

    private val technologyMappingDto =
        TechnologyMappingDto(
            classificationChillKey = "tset.ref.classification.CHILL",
            classificationPrecKey = "tset.ref.classification.PREC",
            classificationVprecKey = "tset.ref.classification.VPREC",
            classificationDcaKey = "tset.ref.classification.DCA",
            classificationInjKey = "tset.ref.classification.INJ",
            classificationInj2Key = "tset.ref.classification.INJ2",
            classificationMinjKey = "tset.ref.classification.MINJ",
            classificationRinjKey = "tset.ref.classification.RINJ",
            classificationWhatKey = "tset.ref.classification.WHAT",
            classificationChatKey = "tset.ref.classification.CHAT",
            classificationRrolKey = "tset.ref.classification.RROL",
            classificationDforKey = "tset.ref.classification.DFOR",
            classificationAforKey = "tset.ref.classification.AFOR",
            classificationDfortKey = "tset.ref.classification.DFORT",
            classificationSintKey = "tset.ref.classification.SINT",
            classificationSandKey = "tset.ref.classification.SAND",
            classificationCrolKey = "tset.ref.classification.CROL",
            classificationCextKey = "tset.ref.classification.CEXT",
            classificationAlexKey = "tset.ref.classification.ALEX",
            classificationCoresKey = "tset.ref.classification.CORES",
            classificationRswaKey = "tset.ref.classification.RSWA",
            classificationBartKey = "tset.ref.classification.BART",
            classificationPcbaKey = "tset.ref.classification.PCBA",
            classificationPcbKey = "tset.ref.classification.PCB",
            classificationCubeKey = "tset.ref.classification.CUBE",
            classificationLastKey = "tset.ref.classification.LAST",
            classificationMagnKey = "tset.ref.classification.MAGN",
            classificationCoreKey = "tset.ref.classification.CORE",
            classificationFtipdsKey = "tset.ref.classification.FTIPDS",
            classificationFtitdsKey = "tset.ref.classification.FTITDS",
            classificationPboxKey = "tset.ref.classification.PBOX",
        )

    private val baseDto =
        MasterdataConfigurationDto(
            effectivityDefinitions = listOf(effectivityDto1, effectivityDto2, customCostEffectivityDto, customLovEffectivityDto),
            overheadMethodConfiguration = overheadMethodConfigurationDto,
            skillTypeConfiguration = skillTypeConfigurationDto,
            lookupConfigurations =
                listOf(
                    lookupConfigDto1,
                    lookupConfigDto1.copy(headerTypeConsumer = HeaderTypeConsumer.INTEREST),
                    lookupConfigDto1.copy(headerTypeConsumer = HeaderTypeConsumer.EXCHANGERATE),
                    lookupConfigDto1.copy(headerTypeConsumer = HeaderTypeConsumer.WAGE),
                    lookupConfigDto1.copy(headerTypeConsumer = HeaderTypeConsumer.LABOR_BURDEN),
                    lookupConfigDto1.copy(headerTypeConsumer = HeaderTypeConsumer.ELECTRICITY_PRICE),
                    lookupConfigDto1.copy(headerTypeConsumer = HeaderTypeConsumer.NATURAL_GAS_PRICE),
                    lookupConfigDto1.copy(headerTypeConsumer = HeaderTypeConsumer.ELECTRICITY_EMISSIONS),
                    lookupConfigDto1.copy(headerTypeConsumer = HeaderTypeConsumer.NATURAL_GAS_EMISSIONS),
                    lookupConfigDto1.copy(headerTypeConsumer = HeaderTypeConsumer.FLOOR_SPACE_PRICE),
                    lookupConfigDto1.copy(headerTypeConsumer = HeaderTypeConsumer.COSTFACTOR_INTEREST),
                    lookupConfigDto1.copy(headerTypeConsumer = HeaderTypeConsumer.ALUMINIUM_SHARE),
                    lookupConfigDto1.copy(headerTypeConsumer = HeaderTypeConsumer.ALUMINIUM_EMISSIONS),
                    lookupConfigDto1.copy(headerTypeConsumer = HeaderTypeConsumer.CAST_EXCIPIENTS),
                    lookupConfigDto1.copy(headerTypeConsumer = HeaderTypeConsumer.OXYGEN_PRICE),
                    lookupConfigDto1.copy(headerTypeConsumer = HeaderTypeConsumer.COUNTRY_INFO),
                    lookupConfigDto1.copy(headerTypeConsumer = HeaderTypeConsumer.MATERIAL_PRICE),
                    lookupConfigDto1.copy(headerTypeConsumer = HeaderTypeConsumer.MATERIAL_CO2),
                    lookupConfigDto1.copy(headerTypeConsumer = HeaderTypeConsumer.MATERIAL_PRICE_COMPOSITION),
                    lookupConfigDto1.copy(headerTypeConsumer = HeaderTypeConsumer.COMPOSITION_PRICE),
                ),
            regionConfiguration = regionConfigurationDto,
            costFactorsConfiguration = costFactorsConfigurationDto,
            materialConfiguration = materialConfigurationDto,
            technologyMapping = technologyMappingDto,
        )

    private val effectivity1 =
        CurrentEffectivityDefinition("fdKey1", EffectivityType.DATE, "calculationDate", valueMapping = emptyList())
    private val effectivity2 =
        CurrentEffectivityDefinition("fdKey2", EffectivityType.NUMERIC, "timePerShift", valueMapping = emptyList())
    private val customCostEffectivity =
        CurrentEffectivityDefinition(
            "custom-num",
            EffectivityType.NUMERIC,
            "#CUSTOM_num",
            customFieldInfo = CustomFieldInfo.Cost("Custom num", Time::class.simpleName!!, TimeUnits.MINUTE.name),
        )
    private val customLovEffectivity =
        CurrentEffectivityDefinition(
            "custom-lov",
            EffectivityType.LOV,
            "#CUSTOM_lov",
            customFieldInfo = CustomFieldInfo.Lov("Custom lov"),
        )
    private val lookupConfig1 =
        LookupConfiguration(
            "headerTypeKey1",
            "lookupStrategy1",
            effectivities = listOf("fdKey1", "custom-num", "custom-lov").map(::EffectivityReference),
        )
    private val overheadMethodConfiguration =
        OverheadMethodConfiguration(
            overheadMethodLovTypeKey = "omlovkey",
            noOverheadMethod = "no-method",
            defaultOverheadMethod = "default-method",
        )
    private val regionConfiguration =
        RegionConfiguration(
            regionClassificationTypeKey = "location",
        )

    private val costFactorsConfiguration =
        CostFactorsConfiguration(
            costFloorSpace = "TSET_COST_FLOOR_SPACE_KEY",
            costLaborBurden = "TSET_COST_SHIFT_SURCHARGE_KEY",
            costElectricityPrice = "TSET_COST_ELECTRICITY_PRICE_KEY",
            costNaturalGasPrice = "TSET_COST_NATURAL_GAS_PRICE_KEY",
            costElectricityEmissions = "TSET_COST_ELECTRICITY_EMISSIONS_KEY",
            costNaturalGasEmissionsActive = "TSET_COST_NATURAL_GAS_EMISSIONS_ACTIVE_KEY",
            costNaturalGasEmissionsPassive = "TSET_COST_NATURAL_GAS_EMISSIONS_PASSIVE_KEY",
            costFactorInterestRates = "TSET_COST_FACTOR_INTEREST_RATES_KEY",
            costAluminiumSharePrimary = "TSET_COST_ALUMINIUM_SHARE_PRIMARY_KEY",
            costAluminiumShareSecondary = "TSET_COST_ALUMINIUM_SHARE_SECONDARY_KEY",
            costAluminiumEmissionsPrimary = "TSET_COST_ALUMINIUM_EMISSIONS_PRIMARY_KEY",
            costAluminiumEmissionsSecondary = "TSET_COST_ALUMINIUM_EMISSIONS_SECONDARY_KEY",
            costWage = "TSET_COST_WAGE_KEY",
            costOxygenPrice = "TSET_COST_OXYGEN_PRICE_KEY",
            costCastExcipientsPrice = "TSET_COST_CAST_EXCIPIENTS_KEY",
            costCountryInfo = "TSET_COST_COUNTRY_ID_KEY",
        )

    val skillTypeConfiguration =
        SkillTypeConfiguration(
            skillTypeLovTypeKey = "tset.ref.lov-type.skill-type",
            masterdataDefaultSkillType = TsetDefaultSkillType.UNSKILLED_WORKER.mdKey,
            laborDefaultSkillType = TsetDefaultSkillType.UNSKILLED_WORKER.mdKey,
            setupDefaultSkillType = TsetDefaultSkillType.SETUP_TECHNICIAN.mdKey,
            toolMaintenanceDefaultSkillType = TsetDefaultSkillType.SETUP_TECHNICIAN.mdKey,
            costModuleSkillTypeMapping =
                CostModuleSkillTypeMapping(
                    unskilledWorker = TsetDefaultSkillType.UNSKILLED_WORKER.mdKey,
                    skilledWorker = TsetDefaultSkillType.SKILLED_WORKER.mdKey,
                    setupTechnician = TsetDefaultSkillType.SETUP_TECHNICIAN.mdKey,
                    productionSupervisor = TsetDefaultSkillType.PRODUCTION_SUPERVISOR.mdKey,
                    inspector = TsetDefaultSkillType.INSPECTOR.mdKey,
                    productionEngineer = TsetDefaultSkillType.PRODUCTION_ENGINEER.mdKey,
                    financialAccountant = TsetDefaultSkillType.FINANCIAL_ACCOUNTANT.mdKey,
                    administrationProfessional = TsetDefaultSkillType.ADMINISTRATION_PROFESSIONAL.mdKey,
                    middleManagement = TsetDefaultSkillType.MIDDLE_MANAGEMENT.mdKey,
                    technicalManager = TsetDefaultSkillType.TECHNICAL_MANAGER.mdKey,
                    chiefExecutiveOfficer = TsetDefaultSkillType.CHIEF_EXECUTIVE_OFFICER.mdKey,
                ),
        )

    private val materialConfiguration =
        CurrentMaterialMasterdataConfiguration(
            materialClassificationTypeKey = "tset.ref.classification-type.material",
            consumableClassificationKey = "tset.ref.classification.consumable",
            electronicComponentClassificationKey = "tset.ref.classification.electronic-component",
            rawMaterialConfiguration =
                RawMaterialConfiguration(
                    rawMaterialClassificationKeys =
                        "tset.ref.classification.semi-finished-material,tset.ref.classification.raw-material",
                    rawMaterialClassifications =
                        RawMaterialClassifications(
                            coilClassificationKeys = listOf("tset.ref.classification.coil"),
                            sheetClassificationKeys = listOf("tset.ref.classification.sheet"),
                            castingAlloyClassificationKeys = listOf("tset.ref.classification.casting-alloy"),
                            ingotClassificationKeys = listOf("tset.ref.classification.ingot"),
                            lamellaClassificationKeys = listOf("tset.ref.classification.lamella"),
                            plasticGranulateClassificationKeys = listOf("tset.ref.classification.plastic-granulate"),
                            rubberClassificationKeys = listOf("tset.ref.classification.rubber"),
                            sandClassificationKeys = listOf("tset.ref.classification.sand"),
                            pipeClassificationKeys = listOf("tset.ref.classification.pipe"),
                            wireRodClassificationKeys = listOf("tset.ref.classification.wire-rod"),
                            barClassificationKeys = listOf("tset.ref.classification.bar"),
                            powderClassificationKeys = listOf("tset.ref.classification.powder"),
                            waxClassificationKeys = listOf("tset.ref.classification.wax"),
                            rareEarthClassificationKeys = listOf("tset.ref.classification.rare-earth"),
                            paintClassificationKeys = listOf("tset.ref.classification.paint"),
                            metallicCoatingClassificationKeys = listOf("tset.ref.classification.metallic-coating"),
                            coatingPcbaClassificationKeys = listOf("tset.ref.classification.coating-pcba"),
                            inkClassificationKeys = listOf("tset.ref.classification.ink"),
                            paperCoilClassificationKeys = listOf("tset.ref.classification.paper-coil"),
                            paperSheetClassificationKeys = listOf("tset.ref.classification.paper-sheet"),
                            varnishClassificationKeys = listOf("tset.ref.classification.varnish"),
                            otherClassificationKeys = listOf("tset.ref.classification.other"),
                        ),
                ),
            technologyClassificationTypeKey = "tset.ref.classification-type.technology",
        )

    private val technologyMapToClassificationKeys =
        TechnologyMapToClassificationKeys(
            classificationChillKey = "tset.ref.classification.CHILL",
            classificationPrecKey = "tset.ref.classification.PREC",
            classificationVprecKey = "tset.ref.classification.VPREC",
            classificationDcaKey = "tset.ref.classification.DCA",
            classificationInjKey = "tset.ref.classification.INJ",
            classificationInj2Key = "tset.ref.classification.INJ2",
            classificationMinjKey = "tset.ref.classification.MINJ",
            classificationRinjKey = "tset.ref.classification.RINJ",
            classificationWhatKey = "tset.ref.classification.WHAT",
            classificationChatKey = "tset.ref.classification.CHAT",
            classificationRrolKey = "tset.ref.classification.RROL",
            classificationDforKey = "tset.ref.classification.DFOR",
            classificationAforKey = "tset.ref.classification.AFOR",
            classificationDfortKey = "tset.ref.classification.DFORT",
            classificationSintKey = "tset.ref.classification.SINT",
            classificationSandKey = "tset.ref.classification.SAND",
            classificationCrolKey = "tset.ref.classification.CROL",
            classificationCextKey = "tset.ref.classification.CEXT",
            classificationAlexKey = "tset.ref.classification.ALEX",
            classificationCoresKey = "tset.ref.classification.CORES",
            classificationRswaKey = "tset.ref.classification.RSWA",
            classificationBartKey = "tset.ref.classification.BART",
            classificationPcbaKey = "tset.ref.classification.PCBA",
            classificationPcbKey = "tset.ref.classification.PCB",
            classificationCubeKey = "tset.ref.classification.CUBE",
            classificationLastKey = "tset.ref.classification.LAST",
            classificationMagnKey = "tset.ref.classification.MAGN",
            classificationCoreKey = "tset.ref.classification.CORE",
            classificationFtipdsKey = "tset.ref.classification.FTIPDS",
            classificationFtitdsKey = "tset.ref.classification.FTITDS",
            classificationPboxKey = "tset.ref.classification.PBOX",
        )

    @Test
    fun `mapping works as expected for a valid config`() {
        val value =
            CurrentMasterdataConfiguration(
                effectivityDefinitions = listOf(effectivity1, effectivity2, customCostEffectivity, customLovEffectivity),
                overheadMethodConfiguration = overheadMethodConfiguration,
                skillTypeConfiguration = skillTypeConfiguration,
                overheadsConfiguration = lookupConfig1,
                interestsConfiguration = lookupConfig1,
                exchangeRatesConfiguration = lookupConfig1,
                wageConfiguration = lookupConfig1,
                laborBurdenConfiguration = lookupConfig1,
                electricityPriceConfiguration = lookupConfig1,
                naturalGasPriceConfiguration = lookupConfig1,
                electricityEmissionsConfiguration = lookupConfig1,
                naturalGasEmissionsConfiguration = lookupConfig1,
                floorSpacePriceConfiguration = lookupConfig1,
                costFactorsInterestConfiguration = lookupConfig1,
                aluminiumShareConfiguration = lookupConfig1,
                aluminiumEmissionConfiguration = lookupConfig1,
                castExcipientsPriceConfiguration = lookupConfig1,
                oxygenPriceConfiguration = lookupConfig1,
                countryIdConfiguration = lookupConfig1,
                regionConfiguration = regionConfiguration,
                costFactorsConfiguration = costFactorsConfiguration,
                materialPriceConfiguration = lookupConfig1,
                materialCo2Configuration = lookupConfig1,
                materialConfiguration = materialConfiguration,
                technologyMapToClassificationKeys = technologyMapToClassificationKeys,
                materialPriceComposition = lookupConfig1,
                compositionPrice = lookupConfig1,
            )
        val mappedValue = mdConfigMapper.fromDto(baseDto)
        val mappedDto = mdConfigMapper.toDto(value)

        Assertions.assertEquals(baseDto, mappedDto)
        Assertions.assertEquals(value, mappedValue)
    }

    @Test
    fun `validation error is thrown when effectivity reference does not exist`() {
        val dto =
            baseDto.copy(
                lookupConfigurations =
                    baseDto.lookupConfigurations.map { lookup ->
                        lookup.copy(effectivities = listOf(EffectivityReferenceDto("not existing")))
                    },
            )
        // We don't have different types to utilize instead....
        assertThatThrownBy<ConfigurationUserException> {
            mdConfigMapper.fromDto(dto)
        }.hasMessageContaining("cannot find one or more referenced effectivities")
    }

    @Test
    fun `validation error is thrown when same lookup consumer is used twice`() {
        val dto = baseDto.copy(lookupConfigurations = baseDto.lookupConfigurations + lookupConfigDto1)

        assertThatThrownBy<ConfigurationUserException> {
            mdConfigMapper.fromDto(dto)
        }.hasMessageContaining("OVERHEAD can only be configured once")
    }

    @Test
    fun `validation error is thrown when field names are same after sanitize`() {
        val dto =
            baseDto.copy(
                effectivityDefinitions =
                    baseDto.effectivityDefinitions +
                        EffectivityDefinitionDto(
                            "custom.num",
                            EffectivityType.NUMERIC,
                            "#CUSTOM_num2",
                            customFieldInfo = CustomCostFieldTypeInfoDto("Custom num2", Time::class.simpleName!!, TimeUnits.MINUTE.name),
                        ),
                lookupConfigurations =
                    baseDto.lookupConfigurations.filter {
                        it.headerTypeConsumer != HeaderTypeConsumer.OVERHEAD
                    } + lookupConfigSanitizedSameDto,
            )

        assertThatThrownBy<ConfigurationUserException> {
            mdConfigMapper.fromDto(dto)
        }.hasMessageContaining("[custom-num, custom.num] cannot be used together in same lookup due to similar field names")
    }

    @Test
    fun `validation error is thrown when consumer is missing`() {
        val dto = baseDto.copy(lookupConfigurations = baseDto.lookupConfigurations.drop(1))

        assertThatThrownBy<ConfigurationUserException> {
            mdConfigMapper.fromDto(dto)
        }.hasMessageContaining("Missing lookup configuration for OVERHEAD")
    }

    @Test
    fun `validation error is thrown when same fieldDefinition is used twice`() {
        val dto =
            baseDto.copy(
                effectivityDefinitions =
                    listOf(effectivityDto1, effectivityDto2.copy(fieldDefinitionKey = effectivityDto1.fieldDefinitionKey)),
            )
        assertThatThrownBy<ConfigurationUserException> {
            mdConfigMapper.fromDto(dto)
        }.hasMessageContaining("fdKey1 can only be configured once")
    }

    @Test
    fun `validation error is thrown when effectivity reference is used twice`() {
        val dto =
            baseDto.copy(
                lookupConfigurations =
                    baseDto.lookupConfigurations.map { lookup ->
                        lookup.copy(effectivities = lookupConfigDto1.effectivities + lookupConfigDto1.effectivities)
                    },
            )
        assertThatThrownBy<ConfigurationUserException> {
            mdConfigMapper.fromDto(dto)
        }.hasMessageContaining("fdKey1 can only be referenced once per lookup")
    }

    @Test
    fun `validation error is thrown when non-standard field has no field info`() {
        val dto =
            baseDto.copy(
                effectivityDefinitions = baseDto.effectivityDefinitions.map { def -> def.copy(customFieldInfo = null) },
            )
        assertThatThrownBy<ConfigurationUserException> {
            mdConfigMapper.fromDto(dto)
        }.hasMessageContaining("Could not find source field")
    }

    @Test
    fun `validation error is thrown when effectivity valueMapping is not bidirectional`() {
        val firstEffectivityConfig = baseDto.effectivityDefinitions.first()
        val effectivityWithInvalidMapping =
            firstEffectivityConfig.copy(
                valueMapping =
                    mapOf(
                        "A" to "X",
                        "B" to "X",
                    ),
            )

        val dto =
            baseDto.copy(
                effectivityDefinitions =
                    baseDto.effectivityDefinitions.map {
                        if (it == firstEffectivityConfig) {
                            effectivityWithInvalidMapping
                        } else {
                            it
                        }
                    },
            )

        assertThatThrownBy<ConfigurationUserException> {
            mdConfigMapper.fromDto(dto)
        }.hasMessageContaining("ValueMapping(from=A, to=X), ValueMapping(from=B, to=X)").hasMessageContaining("must be bidirectional")
    }

    @Test
    fun `validation error is thrown when cost factor lookups have different header type keys`() {
        val wageConfig = baseDto.lookupConfigurations.single { it.headerTypeConsumer == HeaderTypeConsumer.WAGE }

        val dto =
            baseDto.copy(
                lookupConfigurations =
                    baseDto.lookupConfigurations.map {
                        if (it == wageConfig) {
                            wageConfig.copy(headerTypeKey = "UNEXPECTED")
                        } else {
                            it
                        }
                    },
            )

        assertThatThrownBy<ConfigurationUserException> {
            mdConfigMapper.fromDto(dto)
        }.hasMessageContaining("cost factor headerTypeKeys must be set to the same value")
            .hasMessageContaining("headerTypeKey1")
            .hasMessageContaining("UNEXPECTED")
    }
}
