package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.manufacturing.utils.safeDivision
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import java.math.BigDecimal

class NumericFieldResultTests {
    @Test
    fun `Save division with null divisor should return null`() {
        val result = Money.ONE.safeDivision(null)
        Assertions.assertNull(result)
    }

    @Test
    fun `Save division with zero divisor should return null`() {
        val result = Money.ONE.safeDivision(Money.ZERO)
        Assertions.assertNull(result)
    }

    @Test
    fun `Save division with non zero divisor should return division result`() {
        val result = Money.ONE.safeDivision(Money(2.0))
        Assertions.assertTrue(result!! == Money(0.5))
    }

    @Test
    fun `Save division with null divisor and default should return default value`() {
        val result = Money.ONE.safeDivision(null, Money.ONE)
        Assertions.assertEquals(Money.ONE, result)
    }

    @Test
    fun `Save division with zero divisor and default should return default value`() {
        val result = Money.ONE.safeDivision(Money.ZERO, Money.ONE)
        Assertions.assertEquals(Money.ONE, result)
    }

    @Test
    fun `Save division with non zero divisor and default should return division result`() {
        val result = Money.ONE.safeDivision(Money(2.0), Money.ZERO)
        Assertions.assertTrue(result == Money(0.5))
    }

    @Test
    fun `The ceil of 1 dot 5 should return 2`() {
        val result = Money("1.5").ceil()
        Assertions.assertTrue(result == Money("2.0"))
    }

    @Test
    fun `The ceil of -1 dot 5 should return 2`() {
        val result = Money("-1.5").ceil()
        Assertions.assertTrue(result == Money("-1.0"))
    }

    @Test
    fun `The floor of 1 dot 5 should return 1`() {
        val result = Money("1.5").floor()
        Assertions.assertTrue(result == Money("1.0"))
    }

    @Test
    fun `The floor of -1 dot 5 should return 1`() {
        val result = Money("-1.5").floor()
        Assertions.assertTrue(result == Money("-2.0"))
    }

    @Test
    fun `atMost of 1 for the value of 5 should return 1`() {
        val result = Money(5.0).atMost(BigDecimal.ONE)
        Assertions.assertTrue(result == Money("1.0"))
    }

    @Test
    fun `atLeast of 5 for the value of 1 should return 5`() {
        val result = Money.ONE.atLeast(BigDecimal("5.0"))
        Assertions.assertTrue(result == Money("5.0"))
    }

    @Test
    fun `Division of 5 by 2 should return 2 point 5 `() {
        val result = Money("5.0") / 2
        Assertions.assertTrue(result == Money("2.5"))
    }
}
