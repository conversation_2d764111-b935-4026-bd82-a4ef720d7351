package com.nu.bom.core.service.bomrads

import com.atlassian.httpclient.api.HttpStatus
import com.nu.bom.core.api.dtos.FieldConversionService
import com.nu.bom.core.exception.readable.CopyPasteException
import com.nu.bom.core.exception.readable.ErrorCode
import com.nu.bom.core.remote.NuService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.assertErrorThat
import com.nu.bomrads.dto.CalculationCopyEntityDto
import com.nu.bomrads.dto.admin.BomNodeDTO
import com.nu.bomrads.fields.Trigger
import com.nu.bomrads.id.BomNodeId
import com.nu.bomrads.id.ProjectId
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import org.springframework.web.reactive.function.client.WebClientResponseException
import org.springframework.web.util.UriBuilder
import reactor.core.publisher.Flux
import java.net.URI

@ExtendWith(MockitoExtension::class)
class BomradsCalculationCopyServiceTest {
    @Mock
    lateinit var bomrads: BomradsService

    @Mock
    lateinit var nuService: NuService

    private var accessCheck: AccessCheck =
        AccessCheck(
            accountId = "accId",
            accountName = "accountName",
            accountLabel = "label",
            token = "token",
            realm = "default",
        )

    private lateinit var copyService: BomradsCalculationCopyService

    private val copyDto = CalculationCopyEntityDto(listOf(BomNodeId()), ProjectId(), ProjectId(), Trigger("trigger"))

    @BeforeEach
    fun setUp() {
        val mapperService =
            BomradsObjectMapperService(
                mock<FieldConversionService>(),
                Jackson2ObjectMapperBuilder(),
            )
        copyService = BomradsCalculationCopyService(bomrads, mapperService)
    }

    @Test
    fun `on reorder exception, reorder error code is sent`() {
        whenever(bomrads.nuService()).thenReturn(nuService)
        whenever(
            nuService.sendPostFluxAdminRequestWithTsetService(
                body = anyOrNull(),
                uri = any<(UriBuilder) -> URI>(),
                responseType = eq(BomNodeDTO::class),
                accountName = anyOrNull(),
                overrideEnvironment = any(),
                longRunning = any(),
            ),
        ).thenReturn(
            Flux.error(
                WebClientResponseException(
                    HttpStatus.INTERNAL_SERVER_ERROR.code,
                    "",
                    null,
                    "{\"title\":\"error.copy.paste.reorder\"}".toByteArray(),
                    null,
                ),
            ),
        )
        val expectedException =
            CopyPasteException(
                message = "Copy paste operation failed with error type: error.copy.paste.reorder",
                errorCode = ErrorCode.COPY_PASTE_FAILED_REORDER,
            )
        assertErrorThat(copyService.copyCalculationEntity(accessCheck, copyDto))
            .hasMessageContaining(expectedException.message)
    }

    @Test
    fun `on webclient exception, if not reorder, copy paste error code is sent`() {
        whenever(bomrads.nuService()).thenReturn(nuService)
        whenever(
            nuService.sendPostFluxAdminRequestWithTsetService(
                body = anyOrNull(),
                uri = any<(UriBuilder) -> URI>(),
                responseType = eq(BomNodeDTO::class),
                accountName = anyOrNull(),
                overrideEnvironment = any(),
                longRunning = any(),
            ),
        ).thenReturn(
            Flux.error(
                WebClientResponseException(
                    HttpStatus.INTERNAL_SERVER_ERROR.code,
                    "",
                    null,
                    "{\"title\":\"something else\"}".toByteArray(),
                    null,
                ),
            ),
        )
        val expectedException =
            CopyPasteException(
                message = "Copy paste operation failed with error type: something else",
                errorCode = ErrorCode.COPY_PASTE_FAILED,
            )
        assertErrorThat(copyService.copyCalculationEntity(accessCheck, copyDto))
            .hasMessageContaining(expectedException.message)
    }

    @Test
    fun `other exception, generic message is sent`() {
        whenever(bomrads.nuService()).thenReturn(nuService)
        whenever(
            nuService.sendPostFluxAdminRequestWithTsetService(
                body = anyOrNull(),
                uri = any<(UriBuilder) -> URI>(),
                responseType = eq(BomNodeDTO::class),
                accountName = anyOrNull(),
                overrideEnvironment = any(),
                longRunning = any(),
            ),
        ).thenReturn(
            Flux.error(
                Exception("another error"),
            ),
        )
        val expectedException =
            CopyPasteException(
                message = "Generic copy paste failure with message: another error",
                errorCode = ErrorCode.COPY_PASTE_FAILED,
            )
        assertErrorThat(copyService.copyCalculationEntity(accessCheck, copyDto))
            .hasMessageContaining(expectedException.message)
    }
}
