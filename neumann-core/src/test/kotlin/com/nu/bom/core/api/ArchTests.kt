package com.nu.bom.core.api

import com.nu.bom.core.manufacturing.annotations.EngineTransient
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityProvider
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Extends
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MasterDataCalculation
import com.nu.bom.core.manufacturing.annotations.Nocalc
import com.nu.bom.core.manufacturing.annotations.OrderedEntityCreation
import com.nu.bom.core.manufacturing.behaviours.GrossWeightBehaviour
import com.nu.bom.core.manufacturing.behaviours.GrossWeightPerPartDetailedCalculation
import com.nu.bom.core.manufacturing.behaviours.GrossWeightPerPartDirectCalculation
import com.nu.bom.core.manufacturing.behaviours.NetWeightPerPartDetailedCalculation
import com.nu.bom.core.manufacturing.behaviours.NetWeightPerPartDirectCalculation
import com.nu.bom.core.manufacturing.entities.BaseEntityFields
import com.nu.bom.core.manufacturing.entities.BaseMaterial
import com.nu.bom.core.manufacturing.entities.ExchangeRate
import com.nu.bom.core.manufacturing.entities.MachineMasterData
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.RawMaterial
import com.nu.bom.core.manufacturing.entities.RawMaterialManual
import com.nu.bom.core.manufacturing.extension.RawMaterialTurningExtension
import com.nu.bom.core.manufacturing.extension.volumeandscrap.VolumeAndScrapCalculationMaterialUsage
import com.nu.bom.core.manufacturing.fieldTypes.configuration.ConfigurationField
import com.nu.bom.core.model.configurations.ConfigurationValue
import com.nu.bom.core.publicapi.controller.PublicApiMetaInfoController
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.tngtech.archunit.core.domain.JavaClass
import com.tngtech.archunit.core.domain.JavaClass.Predicates.assignableTo
import com.tngtech.archunit.core.domain.JavaClass.Predicates.resideInAnyPackage
import com.tngtech.archunit.core.domain.JavaClasses
import com.tngtech.archunit.core.domain.JavaMember
import com.tngtech.archunit.core.domain.JavaMethod
import com.tngtech.archunit.core.domain.JavaModifier
import com.tngtech.archunit.core.domain.properties.CanBeAnnotated.Predicates.annotatedWith
import com.tngtech.archunit.core.importer.ClassFileImporter
import com.tngtech.archunit.core.importer.ImportOption
import com.tngtech.archunit.lang.ArchCondition
import com.tngtech.archunit.lang.ConditionEvents
import com.tngtech.archunit.lang.SimpleConditionEvent
import com.tngtech.archunit.lang.conditions.ArchConditions.dependOnClassesThat
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.classes
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.fields
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.members
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.methods
import com.tngtech.archunit.lang.syntax.ArchRuleDefinition.noClasses
import com.tngtech.archunit.library.GeneralCodingRules
import com.tngtech.archunit.library.GeneralCodingRules.ACCESS_STANDARD_STREAMS
import io.swagger.v3.oas.models.OpenAPI
import io.swagger.v3.parser.OpenAPIV3Parser
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.springframework.data.annotation.Transient
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

class ArchTests {
    companion object {
        private lateinit var importedClasses: JavaClasses
        private lateinit var importedTestClasses: JavaClasses
        private lateinit var openAPI: OpenAPI

        @BeforeAll
        @JvmStatic
        fun beforeAllTests() {
            importedClasses =
                ClassFileImporter()
                    .withImportOption(ImportOption.Predefined.DO_NOT_INCLUDE_ARCHIVES)
                    .withImportOption(ImportOption.Predefined.DO_NOT_INCLUDE_TESTS)
                    .importPackages("com.nu.bom.core", "com.tset.core")

            importedTestClasses =
                ClassFileImporter()
                    .withImportOption(ImportOption.Predefined.DO_NOT_INCLUDE_ARCHIVES)
                    .withImportOption(ImportOption.Predefined.ONLY_INCLUDE_TESTS)
                    .importPackages("com.nu.bom.core", "com.tset.core")

            val openApiResource = this::class.java.classLoader.getResource("public-api/v1/openapi.yaml")
            requireNotNull(openApiResource)
            openAPI = OpenAPIV3Parser().read(openApiResource.path)
        }

        fun haveMethodsReturningConfigurationFieldsAnnotatedWithTransient(): ArchCondition<JavaMethod> =
            object : ArchCondition<JavaMethod>("return configuration value and be annotated with @EngineTransient") {
                override fun check(
                    method: JavaMethod,
                    events: ConditionEvents,
                ) {
                    val isNotALambda = !method.name.contains("\$lambda\$")
                    val isEngineTransientOrNoCalcField =
                        method.isAnnotatedWith(EngineTransient::class.java) ||
                            method.isAnnotatedWith(Nocalc::class.java)
                    if (isNotALambda &&
                        methodIsNotAGetterSetter(method) &&
                        methodsReturnsClass(method, ConfigurationField::class.java) &&
                        !isEngineTransientOrNoCalcField
                    ) {
                        val message =
                            """
                            ${methodInformation(method)}
                            returning ConfigurationValue is not annotated with @EngineTransient or @NoCalc.
                            """.trimIndent()
                        events.add(SimpleConditionEvent.violated(method, message))
                    }
                }
            }

        fun notHaveMethodsReturningConfigurationValues(): ArchCondition<JavaMethod> =
            object : ArchCondition<JavaMethod>("not return a configuration") {
                override fun check(
                    method: JavaMethod,
                    events: ConditionEvents,
                ) {
                    if (methodIsNotAGetterSetter(method) &&
                        methodsReturnsClass(method, ConfigurationValue::class.java) &&
                        !method.isAnnotatedWith(Nocalc::class.java)
                    ) {
                        val message =
                            """
                            ${methodInformation(method)}
                            returning Configuration is not allowed in manufacturing entities unless annotated with @NoCalc.
                            """.trimIndent()
                        events.add(SimpleConditionEvent.violated(method, message))
                    }
                }
            }

        fun notHaveMethodsThatCreateEntitiesWithConfigurationParameters(): ArchCondition<JavaMethod> =
            object : ArchCondition<JavaMethod>("entity creation fields should not use configurations as parameters") {
                override fun check(
                    method: JavaMethod,
                    events: ConditionEvents,
                ) {
                    val entityCreationMethod =
                        method.isAnnotatedWith(EntityCreation::class.java) ||
                            method.isAnnotatedWith(EntityProvider::class.java) ||
                            method.isAnnotatedWith(OrderedEntityCreation::class.java)
                    if (entityCreationMethod &&
                        methodIsNotAGetterSetter(method) &&
                        methodHasParameterOfType(method, ConfigurationField::class.java)
                    ) {
                        val message =
                            """
                            ${methodInformation(method)}
                            creates entities and is using a configuration as a parameter.
                            """.trimIndent()
                        events.add(SimpleConditionEvent.violated(method, message))
                    }
                }
            }

        private fun getFullRestEndpoint(method: JavaMethod): String {
            val restAnnotation = method.annotations.single { it.rawType.packageName == "org.springframework.web.bind.annotation" }
            return restAnnotation.properties.entries
                .single { it.key == "value" }
                .let { entry ->
                    val partialPath = (entry.value as Array<*>).firstNotNullOf { it?.toString() }
                    method.owner
                        .getAnnotationOfType(RequestMapping::class.java)
                        .value
                        .first() + partialPath
                }
        }

        fun haveMatchingEndpoints(targetPaths: List<String>): ArchCondition<JavaClass> =
            object : ArchCondition<JavaClass>("should have all endpoints documented in openapi.yaml") {
                val allowedMethods =
                    listOf(
                        GetMapping::class.java,
                        PostMapping::class.java,
                        DeleteMapping::class.java,
                        PutMapping::class.java,
                        PatchMapping::class.java,
                    )

                override fun check(
                    clazz: JavaClass,
                    events: ConditionEvents,
                ) {
                    val methods = clazz.methods.filter { method -> allowedMethods.any { method.isAnnotatedWith(it) } }
                    val endpoints = methods.map { Pair(it, getFullRestEndpoint(it)) }
                    endpoints
                        .filter { (_, path) -> path !in targetPaths }
                        .map { (method, path) ->
                            val message =
                                """
                                ${methodInformation(method)}
                                ($path) is part of public API but is not documented.
                                """.trimIndent()
                            events.add(SimpleConditionEvent.violated(clazz, message))
                        }
                    targetPaths
                        .filter { openApiPath -> openApiPath !in endpoints.map { it.second } }
                        .map { openApiPath ->
                            val message =
                                """
                                $openApiPath endpoint is part of public API documentation but it has no implementation at ${clazz.name}.
                                """.trimIndent()
                            events.add(SimpleConditionEvent.violated(clazz, message))
                        }
                }
            }

        private fun methodHasParameterOfType(
            method: JavaMethod,
            returnClass: Class<*>,
        ): Boolean {
            val parameters = method.parameters
            return parameters.any {
                it.type.allInvolvedRawTypes.any { it.isAssignableTo(returnClass) }
            }
        }

        private fun methodInformation(method: JavaMethod) =
            "${method.name} in ${method.sourceCodeLocation.sourceFileName}:${method.sourceCodeLocation.lineNumber}"

        private fun methodIsNotAGetterSetter(method: JavaMethod): Boolean {
            val ownerClassFieldsSettersAndGetters =
                method.owner.fields
                    .map {
                        val lowerCaseMethodName = it.name.lowercase()
                        listOf("get$lowerCaseMethodName", "set$lowerCaseMethodName")
                    }.flatten()
            return !ownerClassFieldsSettersAndGetters.contains(method.name.lowercase())
        }

        private fun methodsReturnsClass(
            method: JavaMethod,
            returnClass: Class<*>,
        ): Boolean {
            val returnType = method.rawReturnType

            return when {
                returnType.isAssignableTo(returnClass) -> true
                returnType.isAssignableTo(Mono::class.java) || returnType.isAssignableTo(Flux::class.java) ->
                    method.returnType.allInvolvedRawTypes.any { it.isAssignableTo(returnClass) }

                else -> false
            }
        }
    }

    @Test
    fun properlyAnnotatedRestController() {
        methods()
            .that()
            .areAnnotatedWith(GetMapping::class.java)
            .or(annotatedWith(PostMapping::class.java))
            .or(annotatedWith(DeleteMapping::class.java))
            .or(annotatedWith(PutMapping::class.java))
            .or(annotatedWith(PatchMapping::class.java))
            .should()
            .bePublic()
            .andShould()
            .beDeclaredInClassesThat()
            .areAnnotatedWith(RestController::class.java)
            .andShould()
            .notHaveRawReturnType(annotatedWith(Document::class.java))
            .check(importedClasses)
    }

    @Test
    fun restControllerShouldBeInApiPackage() {
        classes()
            .that()
            .areAnnotatedWith(RestController::class.java)
            .should()
            .resideInAnyPackage(
                "com.nu.bom.core.api..",
                "com.nu.bom.core.controller..",
                "com.tset.core.api..",
                "com.nu.bom.core.publicapi.controller..",
            ).check(importedClasses)
    }

    @Test
    fun openApiAndPublicApiMetaInfoControllerEndpointsMatch() {
        val url = openAPI.servers.first().url
        val openApiPaths =
            openAPI.paths
                .map { url + it.key }
                .filter { it.startsWith("$url/metainfo") }
        classes()
            .that()
            .haveNameMatching(PublicApiMetaInfoController::class.java.name)
            .should(haveMatchingEndpoints(openApiPaths))
            .check(importedClasses)
    }

    @Test
    fun avoidUsingStandardOut() {
        noClasses()
            .should(ACCESS_STANDARD_STREAMS)
            .check(importedClasses)
        // GeneralCodingRules.NO_CLASSES_SHOULD_ACCESS_STANDARD_STREAMS.check(importedClasses)
    }

    @Test
    fun avoidJavaUtilLogging() {
        GeneralCodingRules.NO_CLASSES_SHOULD_USE_JAVA_UTIL_LOGGING.check(importedClasses)
    }

    @Test
    fun transientAnnotationIsUnneededOnManufacturingEntityClasses() {
        fields()
            .that()
            .areDeclaredInClassesThat(
                assignableTo(ManufacturingEntity::class.java),
            ).should()
            .notBeAnnotatedWith(Transient::class.java)
            .check(importedClasses)
    }

    @Test
    fun transientAnnotationIsUnneededOnFunctions() {
        methods()
            .should()
            .notBeAnnotatedWith(Transient::class.java)
            .check(importedClasses)
    }

    @Test
    fun manufacturingEntityClassesHaveAnnotations() {
        classes()
            .that()
            .areAssignableTo(ManufacturingEntity::class.java)
            .and()
            // everything what are descendants of ManufacturingEntity
            .areNotAssignableFrom(ManufacturingEntity::class.java)
            .and()
            .doNotHaveModifier(JavaModifier.ABSTRACT)
            .should()
            .beAnnotatedWith(EntityType::class.java)
            .orShould()
            .beAnnotatedWith(Extends::class.java)
            .check(importedClasses)
    }

    @Test
    fun avoidShadedCommonPackages() {
        noClasses()
            .should(
                dependOnClassesThat(
                    resideInAnyPackage(
                        "net.logstash.logback.encoder..",
                        "org.testcontainers.shaded..",
                        "wiremock.org..",
                        "wiremock.com..",
                    ),
                ).`as`("use classes from a shaded package"),
            ).check(importedClasses)
    }

    @Test
    fun noMemberShouldBeInputAndMasterdataCalculation() {
        // Check needed which of those might lead to SPs:
        // https://tsetplatform.atlassian.net/browse/COST-60625
        val whiteList =
            listOf(
                BaseEntityFields::baseCurrency.name to BaseEntityFields::class.simpleName!!,
                RawMaterialTurningExtension::caseHardeningPossibleUserInput.name to RawMaterialTurningExtension::class.simpleName!!,
                ExchangeRate::exchangeRate.name to ExchangeRate::class.simpleName!!,
                ExchangeRate::ccy.name to ExchangeRate::class.simpleName!!,
                MachineMasterData::manufacturer.name to MachineMasterData::class.simpleName!!,
                BaseMaterial::purchasePrice.name to RawMaterial::class.simpleName!!,
                BaseMaterial::purchasePrice.name to RawMaterialManual::class.simpleName!!,
                RawMaterialTurningExtension::quenchingPossibleUserInput.name to RawMaterialTurningExtension::class.simpleName!!,
                RawMaterialTurningExtension::materialGroup.name to RawMaterialTurningExtension::class.simpleName!!,
                "getMaterialGroupQuenchedUserInput\$annotations" to RawMaterialTurningExtension::class.simpleName!!,
                MachineMasterData::residualValue.name to MachineMasterData::class.simpleName!!,
                MachineMasterData::technicalDescription.name to MachineMasterData::class.simpleName!!,
                RawMaterialTurningExtension::materialGroup.name to RawMaterialTurningExtension::class.simpleName!!,
            )
        members()
            .that()
            .areDeclaredInClassesThat(
                assignableTo(ManufacturingEntity::class.java),
            ).should(noDoubledAnnotations(whiteList))
            .check(importedClasses)
    }

    private fun noDoubledAnnotations(whiteList: List<Pair<String, String>>): ArchCondition<JavaMember> =
        object : ArchCondition<JavaMember>(
            "",
        ) {
            override fun check(
                member: JavaMember,
                events: ConditionEvents,
            ) {
                val hasInputAnnotation = member.isAnnotatedWith(Input::class.java)
                val isInput =
                    if (hasInputAnnotation) {
                        member.getAnnotationOfType(Input::class.java)?.active == true
                    } else {
                        false
                    }

                val hasMasterDataCalculationAnnotation = member.isAnnotatedWith(MasterDataCalculation::class.java)
                val isMasterDataCalculation =
                    if (hasMasterDataCalculationAnnotation) {
                        member.getAnnotationOfType(MasterDataCalculation::class.java)?.value == true
                    } else {
                        false
                    }

                if (isInput && isMasterDataCalculation) {
                    if (Pair(member.name, member.owner.simpleName) !in whiteList) {
                        val message =
                            "${member.name} in ${member.owner.simpleName} has both Input and MasterDataCalculation"
                        events.add(SimpleConditionEvent.violated(member, message))
                    }
                }
            }
        }

    @Test
    fun `masterdata usage fields fields must be blacklisted for MasterDataCalculation`() {
        // https://tsetplatform.atlassian.net/browse/COST-81342
        val blacklist =
            listOf(
                GrossWeightBehaviour::grossWeightPerPart.name to GrossWeightBehaviour::class.simpleName!!,
                GrossWeightPerPartDetailedCalculation::grossWeightPerPart.name to GrossWeightPerPartDetailedCalculation::class.simpleName!!,
                GrossWeightPerPartDirectCalculation::grossWeightPerPart.name to GrossWeightPerPartDirectCalculation::class.simpleName!!,
                NetWeightPerPartDetailedCalculation::netWeightPerPart.name to NetWeightPerPartDetailedCalculation::class.simpleName!!,
                NetWeightPerPartDirectCalculation::netWeightPerPart.name to NetWeightPerPartDirectCalculation::class.simpleName!!,
                RawMaterial::weightMode.name to RawMaterial::class.simpleName!!,
                RawMaterial::lossRate.name to RawMaterial::class.simpleName!!,
                RawMaterial::materialRecyclingPrice.name to RawMaterial::class.simpleName!!,
                MachineMasterData::gasConsumption.name to MachineMasterData::class.simpleName!!,
                VolumeAndScrapCalculationMaterialUsage::accumulatedMaterialScrapRate.name
                    to VolumeAndScrapCalculationMaterialUsage::class.simpleName!!,
            )
        members()
            .that()
            .areDeclaredInClassesThat(
                assignableTo(ManufacturingEntity::class.java),
            ).should(blacklistedFieldsMustBeAnnotated(blacklist))
            .check(importedClasses)
    }

    private fun blacklistedFieldsMustBeAnnotated(blacklist: List<Pair<String, String>>): ArchCondition<JavaMember> =
        object : ArchCondition<JavaMember>(
            "",
        ) {
            override fun check(
                member: JavaMember,
                events: ConditionEvents,
            ) {
                val hasMasterDataCalculationAnnotation = member.isAnnotatedWith(MasterDataCalculation::class.java)
                val isMasterDataCalculationDisabled =
                    if (hasMasterDataCalculationAnnotation) {
                        member.getAnnotationOfType(MasterDataCalculation::class.java)?.value == false
                    } else {
                        false
                    }

                if (!isMasterDataCalculationDisabled) {
                    if (Pair(member.name, member.owner.simpleName) in blacklist) {
                        val message =
                            "${member.name} in ${member.owner.simpleName} does not have MasterDataCalculation(false)"
                        events.add(SimpleConditionEvent.violated(member, message))
                    }
                }
            }
        }

    // See https://tset.slite.com/app/docs/pX9HSSL7GFJaVF
    @Test
    fun fluxFlatMapCallSitesShouldBeAnnotated() {
        val fluxFlatMapSuppressRule = "tset:reactive:flux-flatmap"
        val className = Flux::class.java
        val methodNames = listOf("flatMap")

        methods()
            .should(createMethodSuppressRule(fluxFlatMapSuppressRule, className, methodNames))
            .check(importedClasses)
    }

    @Test
    fun fluxFlatMapSequentialCallSitesShouldBeAnnotated() {
        val fluxFlatMapSequentialSuppressRule = "tset:reactive:flux-flatmapsequential"
        val className = Flux::class.java
        val methodNames = listOf("flatMapSequential")

        methods()
            .should(createMethodSuppressRule(fluxFlatMapSequentialSuppressRule, className, methodNames))
            .check(importedClasses)
    }

    private fun createMethodSuppressRule(
        ruleName: String,
        nameOfClass: Class<*>,
        methodNames: List<String>,
    ): ArchCondition<JavaMethod> =
        object : ArchCondition<JavaMethod>(
            "methods calling ${methodNames.joinToString()} should be annotated with @TsetSuppress(\"$ruleName\")",
        ) {
            override fun check(
                method: JavaMethod,
                events: ConditionEvents,
            ) {
                method.callsFromSelf
                    .filter { it.target.owner.isAssignableTo(nameOfClass) }
                    .filter { it.target.name in methodNames }
                    .forEach { call ->
                        val annotatedMethod = findAnnotatedEnclosingMethod(method, ruleName)
                        if (annotatedMethod == null) {
                            val sourceCodeLocation = call.sourceCodeLocation
                            val className = method.owner.simpleName.ifEmpty { method.owner.name }
                            val sourceFileName = sourceCodeLocation.sourceFileName
                            val message =
                                """
                                $className.${method.name}($sourceFileName:${sourceCodeLocation.lineNumber}) ${call.target.name} is not annotated with @TsetSuppress("$ruleName").
                                """.trimIndent()
                            events.add(SimpleConditionEvent.violated(method, message))
                        }
                    }
            }
        }

    // traversal is needed to resolve annotations on nested classes and on methods that enclose lambdas
    private fun findAnnotatedEnclosingMethod(
        method: JavaMethod,
        ruleName: String,
    ): JavaMethod? {
        var currentMethod: JavaMethod? = method
        var currentClass = method.owner

        while (currentMethod != null) {
            if (currentMethod.isAnnotatedWith(TsetSuppress::class.java) &&
                currentMethod.getAnnotationOfType(TsetSuppress::class.java).names.contains(ruleName)
            ) {
                return currentMethod
            }

            if (!currentClass.enclosingClass.isPresent) {
                break
            }

            val enclosingCodeUnit = currentClass.enclosingCodeUnit.orElse(null)
            currentMethod = if (enclosingCodeUnit is JavaMethod) enclosingCodeUnit else null
            currentClass = currentClass.enclosingClass.orElse(null) ?: break
        }

        return null
    }

    @Test
    fun `methods in manufacturing entities returning a configuration field should be annotated as engine transient`() {
        methods()
            .that()
            .areDeclaredInClassesThat(
                assignableTo(ManufacturingEntity::class.java),
            ).should(notHaveMethodsThatCreateEntitiesWithConfigurationParameters())
            .check(importedClasses)
    }

    @Test
    fun `methods in manufacturing entities should not return a configuration value`() {
        methods()
            .that()
            .areDeclaredInClassesThat(
                assignableTo(ManufacturingEntity::class.java),
            ).should(notHaveMethodsReturningConfigurationValues())
            .check(importedClasses)
    }

    @Test
    fun `methods with String parameters should not be annotated with @Test`() {
        methods()
            .that()
            .areDeclaredInClassesThat(
                assignableTo(ManufacturingEntity::class.java),
            ).should(notHaveMethodsThatCreateEntitiesWithConfigurationParameters())
            .check(importedClasses)
    }
}
