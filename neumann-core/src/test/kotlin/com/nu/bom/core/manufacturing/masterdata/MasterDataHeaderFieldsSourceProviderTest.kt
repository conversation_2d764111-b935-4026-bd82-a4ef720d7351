package com.nu.bom.core.manufacturing.masterdata

import com.nu.bom.core.manufacturing.fieldTypes.MdHeaderInfoFieldData
import com.nu.bom.core.manufacturing.fieldTypes.MdHeaderTypeAndHeaderKeyFieldData
import com.nu.bom.core.manufacturing.fieldTypes.masterdata.SimpleKey
import com.nu.bom.core.manufacturing.service.virtualfield.sourceproviders.DataSourceProviders
import com.nu.bom.core.manufacturing.service.virtualfield.sourceproviders.MasterDataHeaderFieldsSourceProvider
import com.nu.bom.core.service.masterdata.MdBasicDataService
import com.nu.bom.core.service.masterdata.MdDetailCrudService
import com.nu.bom.core.user.AccessCheck
import com.nu.masterdata.dto.v1.basicdata.ClassificationBaseInfoDto
import com.nu.masterdata.dto.v1.basicdata.ClassificationFieldsInfoDto
import com.nu.masterdata.dto.v1.basicdata.FieldWithVisibilityDto
import com.nu.masterdata.dto.v1.detail.table.DetailQueryResponseDto
import com.nu.masterdata.dto.v1.detail.table.HeaderAndDetailDto
import com.nu.masterdata.dto.v1.fields.DateFieldDefinitionDto
import com.nu.masterdata.dto.v1.fields.NumericFieldDefinitionDto
import com.nu.masterdata.dto.v1.fields.TextFieldDefinitionDto
import com.nu.masterdata.dto.v1.header.HeaderDetailQueryResponseDto
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import com.nu.masterdata.dto.v1.schema.DateFieldSchemaDto
import com.nu.masterdata.dto.v1.schema.NumericFieldSchemaDto
import com.nu.masterdata.dto.v1.schema.TextFieldSchemaDto
import com.nu.masterdata.dto.v1.schema.UnitOfMeasurementTypeDto
import com.nu.masterdata.dto.v1.schema.UnitTypeDto
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.Mockito.mock
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import reactor.core.publisher.Mono
import reactor.test.StepVerifier

class MasterDataHeaderFieldsSourceProviderTest {
    private lateinit var headerFieldsProvider: MasterDataHeaderFieldsSourceProvider

    private lateinit var mdDetailCrudService: MdDetailCrudService
    private lateinit var mdBasicDataService: MdBasicDataService
    private val accessCheck: AccessCheck = mock(AccessCheck::class.java)

    private val numField =
        NumericFieldDefinitionDto(
            SimpleKeyDto("num-field"),
            false,
            NumericFieldSchemaDto(
                UnitOfMeasurementTypeDto(
                    UnitTypeDto(SimpleKeyDto("pcs"), "Pieces"),
                    null,
                ),
                "numeric-field-name",
            ),
        )

    private val dateField =
        DateFieldDefinitionDto(
            SimpleKeyDto("date-field"),
            DateFieldSchemaDto(
                "date-field-name",
            ),
        )

    private val textField =
        TextFieldDefinitionDto(
            SimpleKeyDto("text-field"),
            TextFieldSchemaDto(
                "text-field-name",
            ),
        )

    @BeforeEach
    fun prepareMocks() {
        mdDetailCrudService = mock(MdDetailCrudService::class.java)
        mdBasicDataService = mock(MdBasicDataService::class.java)
        headerFieldsProvider =
            MasterDataHeaderFieldsSourceProvider(
                mdDetailCrudService,
                mdBasicDataService,
            )

        Mockito
            .`when`(mdDetailCrudService.postAllDetailEntries(any(), eq(SimpleKeyDto("ht1")), any()))
            .thenReturn(
                Mono.just(
                    DetailQueryResponseDto(
                        content =
                            listOf(
                                HeaderAndDetailDto(
                                    headerDto =
                                        HeaderDetailQueryResponseDto(
                                            key = SimpleKeyDto("h1"),
                                            headerTypeKey = SimpleKeyDto("ht1"),
                                            name = "HeaderType 1 - Header 1",
                                            active = true,
                                            classifications =
                                                mapOf(
                                                    SimpleKeyDto("ct1") to
                                                        listOf(
                                                            ClassificationBaseInfoDto(
                                                                key = SimpleKeyDto("c1"),
                                                                name = "c1",
                                                                classificationTypeKey = SimpleKeyDto("ct1"),
                                                            ),
                                                            ClassificationBaseInfoDto(
                                                                key = SimpleKeyDto("c2"),
                                                                name = "c2",
                                                                classificationTypeKey = SimpleKeyDto("ct1"),
                                                            ),
                                                        ),
                                                ),
                                            classificationFieldValues = mapOf(),
                                            detailValueSchema = null,
                                        ),
                                    detailDto = null,
                                ),
                                HeaderAndDetailDto(
                                    headerDto =
                                        HeaderDetailQueryResponseDto(
                                            key = SimpleKeyDto("h2"),
                                            headerTypeKey = SimpleKeyDto("ht1"),
                                            name = "HeaderType 1 - Header 2",
                                            active = true,
                                            classifications =
                                                mapOf(
                                                    SimpleKeyDto("ct1") to
                                                        listOf(
                                                            ClassificationBaseInfoDto(
                                                                key = SimpleKeyDto("c3"),
                                                                name = "c3",
                                                                classificationTypeKey = SimpleKeyDto("ct1"),
                                                            ),
                                                        ),
                                                ),
                                            classificationFieldValues = mapOf(),
                                            detailValueSchema = null,
                                        ),
                                    detailDto = null,
                                ),
                                HeaderAndDetailDto(
                                    headerDto =
                                        HeaderDetailQueryResponseDto(
                                            key = SimpleKeyDto("h3"),
                                            headerTypeKey = SimpleKeyDto("ht1"),
                                            name = "HeaderType 1 - Header 3",
                                            active = true,
                                            classifications =
                                                mapOf(
                                                    SimpleKeyDto("ct1") to
                                                        listOf(
                                                            ClassificationBaseInfoDto(
                                                                key = SimpleKeyDto("c1"),
                                                                name = "c1",
                                                                classificationTypeKey = SimpleKeyDto("ct1"),
                                                            ),
                                                            ClassificationBaseInfoDto(
                                                                key = SimpleKeyDto("c2"),
                                                                name = "c2",
                                                                classificationTypeKey = SimpleKeyDto("ct1"),
                                                            ),
                                                        ),
                                                ),
                                            classificationFieldValues = mapOf(),
                                            detailValueSchema = null,
                                        ),
                                    detailDto = null,
                                ),
                            ),
                        number = 0,
                        size = 3,
                        totalElements = 3,
                        maxCountTruncated = false,
                    ),
                ),
            )

        Mockito
            .`when`(mdDetailCrudService.postAllDetailEntries(any(), eq(SimpleKeyDto("ht2")), any()))
            .thenReturn(
                Mono.just(
                    DetailQueryResponseDto(
                        content =
                            listOf(
                                HeaderAndDetailDto(
                                    headerDto =
                                        HeaderDetailQueryResponseDto(
                                            key = SimpleKeyDto("h1"),
                                            headerTypeKey = SimpleKeyDto("ht2"),
                                            name = "HeaderType 2 - Header 1",
                                            active = true,
                                            classifications =
                                                mapOf(
                                                    SimpleKeyDto("ct2") to
                                                        listOf(
                                                            ClassificationBaseInfoDto(
                                                                key = SimpleKeyDto("c4"),
                                                                name = "c4",
                                                                classificationTypeKey = SimpleKeyDto("ct2"),
                                                            ),
                                                        ),
                                                    SimpleKeyDto("ct1") to
                                                        listOf(
                                                            ClassificationBaseInfoDto(
                                                                key = SimpleKeyDto("c3"),
                                                                name = "c3",
                                                                classificationTypeKey = SimpleKeyDto("ct1"),
                                                            ),
                                                        ),
                                                ),
                                            classificationFieldValues = mapOf(),
                                            detailValueSchema = null,
                                        ),
                                    detailDto = null,
                                ),
                            ),
                        number = 0,
                        size = 1,
                        totalElements = 1,
                        maxCountTruncated = false,
                    ),
                ),
            )

        val v = FieldWithVisibilityDto(numField, columnVisibleByDefault = true, filterVisibleByDefault = false)
        Mockito
            .`when`(
                mdBasicDataService.getClassificationTypeFields(
                    accessCheck,
                    SimpleKeyDto("ct1"),
                    listOf(SimpleKeyDto("c1"), SimpleKeyDto("c2")),
                ),
            ).thenReturn(
                Mono.just(
                    ClassificationFieldsInfoDto(
                        classifications = emptyList(),
                        fields = listOf(v.copy(field = numField)),
                    ),
                ),
            )

        Mockito
            .`when`(mdBasicDataService.getClassificationTypeFields(accessCheck, SimpleKeyDto("ct1"), listOf(SimpleKeyDto("c3"))))
            .thenReturn(
                Mono.just(
                    ClassificationFieldsInfoDto(
                        classifications = emptyList(),
                        fields = listOf(v.copy(field = dateField), v.copy(field = numField)),
                    ),
                ),
            )
        Mockito
            .`when`(mdBasicDataService.getClassificationTypeFields(accessCheck, SimpleKeyDto("ct2"), listOf(SimpleKeyDto("c4"))))
            .thenReturn(
                Mono.just(
                    ClassificationFieldsInfoDto(
                        classifications = emptyList(),
                        fields = listOf(v.copy(field = textField)),
                    ),
                ),
            )
    }

    @Test
    fun checkIsSourceMethod() {
        Assertions.assertThat(headerFieldsProvider.isSourceProviderFor(DataSourceProviders.NEW_MASTER_DATA_HEADER_FIELDS)).isTrue()
        Assertions.assertThat(headerFieldsProvider.isSourceProviderFor(DataSourceProviders.NEW_MASTER_DATA_SOURCE)).isFalse()
    }

    @Test
    fun validateFetchingOfHeaderFields() {
        val req =
            listOf(
                MdHeaderTypeAndHeaderKeyFieldData(
                    headerTypeKey = "ht1",
                    headerKey = SimpleKey("h1"),
                ),
                MdHeaderTypeAndHeaderKeyFieldData(
                    headerTypeKey = "ht1",
                    headerKey = SimpleKey("h2"),
                ),
                MdHeaderTypeAndHeaderKeyFieldData(
                    headerTypeKey = "ht1",
                    headerKey = SimpleKey("h3"),
                ),
                MdHeaderTypeAndHeaderKeyFieldData(
                    headerTypeKey = "ht2",
                    headerKey = SimpleKey("h1"),
                ),
            )

        StepVerifier
            .create(headerFieldsProvider.findSourceEntityData(accessCheck, req).collectList())
            .assertNext { results ->
                val ht1h1 = results.firstOrNull { it.index == 0 }
                val fieldInfo1 =
                    ht1h1
                        ?.value
                        ?.fieldInfo
                        ?.get(
                            MasterDataHeaderFieldsSourceProvider.TARGET_FIELD_NAME,
                        )?.res as? MdHeaderInfoFieldData
                Assertions.assertThat(fieldInfo1).isNotNull
                Assertions.assertThat(fieldInfo1?.fieldDefinitions?.map { it.key }).containsExactlyInAnyOrder(numField.key.key)

                val ht1h2 = results.firstOrNull { it.index == 1 }
                val fieldInfo2 =
                    ht1h2
                        ?.value
                        ?.fieldInfo
                        ?.get(
                            MasterDataHeaderFieldsSourceProvider.TARGET_FIELD_NAME,
                        )?.res as? MdHeaderInfoFieldData
                Assertions.assertThat(fieldInfo2).isNotNull
                Assertions
                    .assertThat(
                        fieldInfo2?.fieldDefinitions?.map { it.key },
                    ).containsExactlyInAnyOrder(dateField.key.key, numField.key.key)

                val ht1h3 = results.firstOrNull { it.index == 2 }
                val fieldInfo3 =
                    ht1h3
                        ?.value
                        ?.fieldInfo
                        ?.get(
                            MasterDataHeaderFieldsSourceProvider.TARGET_FIELD_NAME,
                        )?.res as? MdHeaderInfoFieldData
                Assertions.assertThat(fieldInfo3).isNotNull
                Assertions.assertThat(fieldInfo3?.fieldDefinitions?.map { it.key }).containsExactlyInAnyOrder(numField.key.key)

                val h21h1 = results.firstOrNull { it.index == 3 }
                val fieldInfo4 =
                    h21h1
                        ?.value
                        ?.fieldInfo
                        ?.get(
                            MasterDataHeaderFieldsSourceProvider.TARGET_FIELD_NAME,
                        )?.res as? MdHeaderInfoFieldData
                Assertions.assertThat(fieldInfo4).isNotNull
                Assertions
                    .assertThat(
                        fieldInfo4?.fieldDefinitions?.map {
                            it.key
                        },
                    ).containsExactlyInAnyOrder(numField.key.key, textField.key.key, dateField.key.key)
            }.verifyComplete()
    }
}
