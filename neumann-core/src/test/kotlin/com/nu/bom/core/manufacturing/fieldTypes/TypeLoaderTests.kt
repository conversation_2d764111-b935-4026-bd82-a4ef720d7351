package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.fieldmapping.BackMappingFieldsBuilder
import com.nu.bom.core.service.LocalEntityHashFallbackProvider
import com.nu.bom.core.startup.TypeLoader
import com.nu.bom.core.utils.EntitiesEnumEntityLoader
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.ExtensionManager
import com.nu.bom.core.utils.ManualCreationEntityLoader
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class TypeLoaderTests {
    lateinit var entityManager: EntityManager

    @BeforeEach
    fun setup() {
        val extensionManager = ExtensionManager()

        entityManager =
            EntityManager(
                extensionManager,
                LocalEntityHashFallbackProvider(),
                ManualCreationEntityLoader(EntitiesEnumEntityLoader()),
                BackMappingFieldsBuilder(),
            )
        TypeLoader(entityManager).onStartup()
    }

    @Test
    fun testSelectableTypeCanBeLoaded() {
        assertNotNull(entityManager.getMaybeFieldType("CoreToPartRatio"))

        val selectableEnum = entityManager.getSelectableTypes(CoreToPartRatio::class)
        assertNotNull(selectableEnum)

        val enumConstants = selectableEnum!!.enumConstants.map { it.name }

        assertTrue(
            enumConstants.containsAll(
                listOf(
                    CoreToPartRatio.Selection.NORMAL_PACKAGE.name,
                    CoreToPartRatio.Selection.DOUBLE_CORE.name,
                ),
            ),
        )

        assertEquals(2, enumConstants.size)
    }
}
