package com.nu.bom.core.manufacturing.testentities.behaviour

import com.nu.bom.core.manufacturing.annotations.BehaviourCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.MissingInputError
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.HeaderFieldsDynamicBehaviourInfo
import com.nu.bom.core.manufacturing.fieldTypes.MdHeaderInfoField
import com.nu.bom.core.manufacturing.service.behaviour.HeaderClassificationFieldsDynamicBehaviour

@EntityType(Entities.NONE)
class MasterdataHeaderFieldsBehaviourHost(name: String) : BaseManufacturing(name) {
    @Input
    fun mdHeaderInfo(): MdHeaderInfoField = throw MissingInputError()

    @BehaviourCreation
    fun createDynamicFields(mdHeaderInfo: MdHeaderInfoField): HeaderClassificationFieldsDynamicBehaviour {
        return HeaderClassificationFieldsDynamicBehaviour(
            this,
            HeaderFieldsDynamicBehaviourInfo(
                MasterdataHeaderFieldsBehaviourHost::class.qualifiedName!!,
                mdHeaderInfo.res.fieldDefinitions!!,
            ),
        )
    }
}
