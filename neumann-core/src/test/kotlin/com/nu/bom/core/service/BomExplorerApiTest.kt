package com.nu.bom.core.service

import com.nu.bom.core.api.dtos.BomExpBaseDto
import com.nu.bom.core.api.dtos.BomExpEntryDto
import com.nu.bom.core.api.dtos.BomExpEntryType
import com.nu.bom.core.api.dtos.BomExpNodeDto
import com.nu.bom.core.api.dtos.BomExpNodeQueryDto
import com.nu.bom.core.api.dtos.BomExpRequestDto
import com.nu.bom.core.api.dtos.ExtraNodeInfo
import com.nu.bom.core.api.dtos.FieldConversionService
import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.api.dtos.createCacheIdentifier
import com.nu.bom.core.machining.cycletimestep.ToolChangeCycleTimeStep
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostManufacturedMaterial
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.Consumable
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.fieldTypes.ExchangeRatesField
import com.nu.bom.core.manufacturing.fieldTypes.FieldWithResult
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.service.FieldKey
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.SnapshotId
import com.nu.bom.core.model.toBomNodeId
import com.nu.bom.core.model.toBranchId
import com.nu.bom.core.model.toMongoBomNodeId
import com.nu.bom.core.model.toMongoFormatStr
import com.nu.bom.core.model.toMongoSnapshotId
import com.nu.bom.core.model.toProjectId
import com.nu.bom.core.service.bomnode.ExtraNodeInfoHandlerService
import com.nu.bom.core.service.bomnode.SnapshotLoaderService
import com.nu.bom.core.service.bomrads.BomExplorerLoadingMode
import com.nu.bom.core.service.bomrads.BomNodeLoaderService
import com.nu.bom.core.service.bomrads.BomradsBomNodeService
import com.nu.bom.core.service.bomrads.BomradsObjectMapperService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.JacksonTest
import com.nu.bomrads.dto.BomExplorerResponseDTO
import com.nu.bomrads.dto.ExternalParent
import com.nu.bomrads.dto.InternalChild
import com.nu.bomrads.dto.MinimalBranchDTO
import com.nu.bomrads.dto.NodeSnapshotDTO
import com.nu.bomrads.enumeration.BomNodeStatus
import com.nu.bomrads.id.BomEntryId
import com.nu.bomrads.id.ManufacturingTreeId
import com.tset.core.service.domain.Currency
import com.tset.core.service.domain.calculation.CalculationType
import org.bson.types.ObjectId
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.function.Executable
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.eq
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import reactor.core.publisher.Mono
import reactor.test.StepVerifier
import java.math.BigDecimal
import java.math.RoundingMode

@JacksonTest
class BomExplorerApiTest(
    @Autowired
    private val mapperBuilder: Jackson2ObjectMapperBuilder,
) {
    private lateinit var bomExplorerServiceV2: BomExplorerServiceV2

    private lateinit var bomNodeLoaderService: BomNodeLoaderService

    private lateinit var objectMapperService: BomradsObjectMapperService

    @Mock
    private lateinit var bomradsBomNodeService: BomradsBomNodeService

    @Mock
    private lateinit var snapshotLoaderService: SnapshotLoaderService

    @Mock
    private lateinit var extraNodeInfoHandlerService: ExtraNodeInfoHandlerService

    @Mock
    private lateinit var partService: PartService

    @Mock
    private lateinit var fieldConversionService: FieldConversionService

    @Mock
    private lateinit var entityManager: EntityManager

    private var accessCheck: AccessCheck =
        AccessCheck(
            accountId = "accId",
            accountName = "accountName",
            accountLabel = "label",
            token = "token",
            realm = "default",
        )

    private val projectId: ProjectId = ProjectId()
    private val mainBranchId: BranchId = BranchId()
    private val otherBranchId: BranchId = BranchId()

    private lateinit var child1Snapshot: NodeSnapshotDTO
    private lateinit var parent1Snapshot: NodeSnapshotDTO

    private fun setup(withCurrencyInfoFromBomrads: Boolean) {
        MockitoAnnotations.openMocks(this)

        objectMapperService = BomradsObjectMapperService(fieldConversionService, mapperBuilder)

        bomNodeLoaderService =
            BomNodeLoaderService(
                bomradsBomNodeService,
                snapshotLoaderService,
                extraNodeInfoHandlerService,
                objectMapperService,
                partService,
            )

        bomExplorerServiceV2 =
            BomExplorerServiceV2(
                bomNodeLoaderService,
                entityManager,
            )

        val kClasses: List<Class<out ManufacturingEntity>> = listOf(ToolChangeCycleTimeStep::class.java)
        whenever(entityManager.getAllModularizedClasses(anyOrNull())).thenReturn(kClasses)

        whenever(
            fieldConversionService.fieldResultToFieldParameterWithOthers(
                accessCheck = eq(accessCheck),
                fieldName = any(),
                fieldResult = any(),
                entityClass = any(),
                otherFields = any(),
                entity = anyOrNull(),
                exchangeRateMap = any(),
                bomInfo = anyOrNull(),
                unitOverrideContext = any(),
                nullValueHandling = any(),
                entityForDynamicMetaData = any(),
                interpolatedMetaInfo = anyOrNull(),
                interpolatedFieldInfo = anyOrNull(),
                baseCurrency = anyOrNull(),
            ),
        ).then {
            val fieldName: String = it.getArgument(1)
            if (fieldName == "baseCurrency") {
                Mono.just(
                    FieldParameter.create<com.nu.bom.core.manufacturing.fieldTypes.Currency, String>(
                        fieldName,
                        "CHF",
                    ),
                )
            } else {
                Mono.just(FieldParameter.create<Num, BigDecimal>(fieldName, BigDecimal("38.532110092")))
            }
        }

        child1Snapshot =
            nodeSnapshotDTO(
                ObjectId().toBomNodeId(),
                withCurrencyInfoFromBomrads = withCurrencyInfoFromBomrads,
                partName = "child1",
                nodeName = "nn-child1",
                nodeTitle = "nt-child1",
                root = false,
                calculationClass = "ManufacturingWaxCluster",
            )
        parent1Snapshot =
            nodeSnapshotDTO(
                ObjectId().toBomNodeId(),
                withCurrencyInfoFromBomrads = withCurrencyInfoFromBomrads,
                partName = "parent1",
                children =
                    listOf(
                        InternalChild(child1Snapshot.bomNodeId, BomEntryId(), ManufacturingTreeId()),
                    ),
            )

        whenever(
            bomradsBomNodeService.getSnapshotsForBomExplorer(
                accessCheck,
                projectId.toProjectId(),
                null,
            ),
        ).thenReturn(
            toBomExplorerRespose(listOf(parent1Snapshot)),
        )
        whenever(
            snapshotLoaderService.loadSnapshots(
                accessCheck,
                listOf(),
            ),
        ).thenReturn(
            Mono.just(emptyMap()),
        )

        var loadingMode =
            BomExplorerLoadingMode(
                projectId.toProjectId(),
                BomExpRequestDto(
                    listOf(
                        BomExpNodeQueryDto(parent1Snapshot.bomNodeId.toMongoFormatStr(), true, null),
                    ),
                    null,
                ),
            )
        whenever(
            bomradsBomNodeService.getSnapshotsForBomExplorer(
                accessCheck,
                projectId.toProjectId(),
                loadingMode.convertToNodeSnapshotRequest(),
            ),
        ).thenReturn(toBomExplorerRespose(listOf(parent1Snapshot, child1Snapshot)))
        whenever(
            snapshotLoaderService.loadSnapshots(
                accessCheck,
                listOf(parent1Snapshot.treeId()?.toMongoSnapshotId()!!),
            ),
        ).thenReturn(
            Mono.just(
                mapOf(parent1Snapshot.treeId()?.toMongoSnapshotId()!! to bomNodeSnapshot(parent1Snapshot)),
            ),
        )

        loadingMode =
            BomExplorerLoadingMode(
                projectId.toProjectId(),
                BomExpRequestDto(
                    listOf(
                        BomExpNodeQueryDto(parent1Snapshot.bomNodeId.toMongoFormatStr(), true, null),
                    ),
                    otherBranchId.toHexString(),
                ),
            )
        whenever(
            bomradsBomNodeService.getSnapshotsForBomExplorer(
                accessCheck,
                projectId.toProjectId(),
                loadingMode.convertToNodeSnapshotRequest(),
            ),
        ).thenReturn(
            toBomExplorerRespose(
                listOf(
                    parent1Snapshot.copy(
                        previousTreeId = parent1Snapshot.treeId,
                        branchId = otherBranchId.toBranchId(),
                    ),
                    child1Snapshot.copy(branchId = otherBranchId.toBranchId()),
                ),
            ),
        )

        loadingMode =
            BomExplorerLoadingMode(
                projectId.toProjectId(),
                BomExpRequestDto(
                    listOf(
                        BomExpNodeQueryDto(
                            parent1Snapshot.bomNodeId.toMongoFormatStr(),
                            true,
                            parent1Snapshot.treeId?.toMongoFormatStr(),
                        ),
                    ),
                    null,
                ),
            )
        whenever(
            bomradsBomNodeService.getSnapshotsForBomExplorer(
                accessCheck,
                projectId.toProjectId(),
                loadingMode.convertToNodeSnapshotRequest(),
            ),
        ).thenReturn(toBomExplorerRespose(listOf(parent1Snapshot, child1Snapshot)))
    }

    private fun toBomExplorerRespose(snapshots: List<NodeSnapshotDTO>): Mono<BomExplorerResponseDTO> {
        return Mono.just(
            BomExplorerResponseDTO(
                snapshots,
                snapshots.mapNotNull { it.branchId }.distinct().map { toBranchDto(it) },
            ),
        )
    }

    private fun toBranchDto(branchId: com.nu.bomrads.id.BranchId): MinimalBranchDTO {
        val main = branchId == mainBranchId.toBranchId()
        return MinimalBranchDTO(
            branchId,
            main = main,
            global = main,
            published = false,
            creator = "",
            fromMaster = false,
            undoable = false,
            redoable = false,
            changesetId = null,
            sourceId = null,
        )
    }

    private fun bomNodeSnapshot(
        snapshotDto: NodeSnapshotDTO,
        isChild: Boolean = false,
    ): BomNodeSnapshot {
        val manu =
            BaseManufacturing(name = "x").apply {
                children =
                    listOfNotNull(
                        if (isChild) null else Consumable(name = "manufacturing-child-consumable"),
                    ).toMutableList()
                fieldWithResults =
                    mutableListOf(
                        FieldWithResult(
                            ExchangeRatesField(
                                mapOf(
                                    Currency("EUR") to 1.0.toBigDecimal(),
                                    Currency("CHF") to 1.09.toBigDecimal(),
                                ),
                            ),
                            FieldKey("exchangeRates", "x", "x", "x", 1),
                        ),
                        FieldWithResult(
                            Text("IH"),
                            FieldKey(CommercialCalculationCostManufacturedMaterial::customProcurementTypeShortName.name, "x", "x", "x", 1),
                        ),
                        FieldWithResult(
                            Text("parent1"),
                            FieldKey("partDesignation", "x", "x", "x", 1),
                        ),
                        FieldWithResult(
                            com.nu.bom.core.manufacturing.fieldTypes.Currency("CHF"),
                            FieldKey("baseCurrency", "x", "x", "x", 1),
                        ),
                        FieldWithResult(
                            Num(BigDecimal("38.532110092")),
                            FieldKey("costPerPart", "x", "x", "x", 1),
                        ),
                        FieldWithResult(
                            Num(BigDecimal("2.0")),
                            FieldKey("cO2PerPart", "x", "x", "x", 1),
                        ),
                    )
                // setting createdBy to a not-null value, makes the calculation "not copyable"
                createdBy = if (isChild) FieldKey("doesNotMatter", "x", "x", "x", 1) else null
            }

        val s =
            BomNodeSnapshot(
                snapshotDto.title,
                2020,
                snapshotDto.title,
                accountId = null,
                partId = null,
                manufacturing = manu,
            )
        s._id = snapshotDto.treeId?.toMongoSnapshotId()
        s.projectId = projectId
        return s
    }

    private fun nodeSnapshotDTO(
        bomNodeId: com.nu.bomrads.id.BomNodeId,
        withCurrencyInfoFromBomrads: Boolean,
        nodeName: String = "Name",
        nodeTitle: String = "Title",
        treeId: ManufacturingTreeId = ManufacturingTreeId(),
        externalParents: List<ExternalParent> = emptyList(),
        children: List<InternalChild> = emptyList(),
        partName: String = nodeName,
        root: Boolean = true,
        calculationClass: String = ManualManufacturing::class.java.simpleName,
        branchId: BranchId = mainBranchId,
    ): NodeSnapshotDTO {
        val currencyInfo =
            if (withCurrencyInfoFromBomrads) {
                """
                ,
                "costPerPartInBaseCurrency": 42,
                "baseCurrency": "CHF"
                """.trimIndent()
            } else {
                ""
            }
        return NodeSnapshotDTO(
            bomNodeId = bomNodeId,
            name = nodeName,
            title = nodeTitle,
            year = 2021,
            status = BomNodeStatus.TODO,
            root = root,
            treeId = treeId,
            calculationRoot = bomNodeId,
            children = children,
            externalChildren = listOf(),
            externalParents = externalParents,
            responsibleUser = "test-user",
            legacyPreviousId = null,
            noBranches = 0,
            meta =
                """
                {
                "calculationType": "MANUAL_CALCULATION",
                "procurementType": "IH",
                "partName": "$partName",
                "calculationClass": "$calculationClass"
                $currencyInfo
                }
                """.trimIndent(),
            archived = false,
            branchId = branchId.toBranchId(),
        )
    }

    private fun assertCost(
        expected: BigDecimal,
        baseDto: BomExpBaseDto,
        scale: Int = 2,
    ) {
        Assertions.assertEquals(
            expected.setScale(scale),
            ((baseDto as BomExpNodeDto).costPerPartInBaseCurrency.value as BigDecimal).setScale(
                scale,
                RoundingMode.HALF_UP,
            ),
        )
    }

    @Nested
    inner class WithoutCurrencyInfoFromBomrads {
        @BeforeEach
        fun setupWithCurrencyInfoFromBomrads() {
            setup(false)

            Mockito.`when`(
                extraNodeInfoHandlerService.loadAndReturnExtraNodeInfoSnapshotsAndUpdateMeta(
                    eq(accessCheck),
                    eq(projectId.toProjectId()),
                    any(),
                ),
            )
                .then {
                    val snapshotIds: Collection<SnapshotId> = it.getArgument(2)
                    val extraNodeInfo =
                        ExtraNodeInfo(
                            baseCurrency = Currency.toCurrencyOrEur("CHF"),
                            costPerPartInBaseCurrency = BigDecimal("42.0"),
                            calculationType = CalculationType.MANUAL_CALCULATION,
                            procurementType = "IH",
                            calculationClass = ManualManufacturing::class.java.simpleName,
                            technologyModel = "ManufacturingInjection2",
                        )
                    Mono.just(
                        snapshotIds.associateWith { snapshotId ->
                            val isChild = snapshotId == child1Snapshot.treeId?.toMongoSnapshotId()
                            val snapshotDto =
                                if (isChild) {
                                    child1Snapshot
                                } else {
                                    parent1Snapshot
                                }
                            val metaFromDto = objectMapperService.deserializeExtraNodeInfoFromString(snapshotDto.meta)
                            val meta =
                                if (metaFromDto == null && snapshotId == parent1Snapshot.treeId?.toMongoSnapshotId()) {
                                    extraNodeInfo.copy(
                                        partName = getPartName(snapshotId),
                                    )
                                } else if (metaFromDto == null && isChild) {
                                    extraNodeInfo.copy(
                                        partName = getPartName(snapshotId),
                                        calculationClass = "ManufacturingWaxCluster",
                                    )
                                } else {
                                    extraNodeInfo.copy(partName = getPartName(snapshotId))
                                }
                            meta to bomNodeSnapshot(snapshotDto, isChild)
                        },
                    )
                }
        }

        private fun getPartName(id: SnapshotId): String {
            return when (id) {
                parent1Snapshot.treeId()?.toMongoSnapshotId() -> "parent1"
                child1Snapshot.treeId()?.toMongoSnapshotId() -> "child1"
                else -> ""
            }
        }

        @Test
        @DisplayName("root nodes should be returned by bom explorer api")
        fun testBomExlorerApiRootNodes() {
            StepVerifier.create(bomExplorerServiceV2.getBomNodes(accessCheck, projectId.toHexString(), null)).assertNext { nodes ->
                Assertions.assertEquals(1, nodes.size)

                Assertions.assertEquals("parent1", nodes[0].designation)
                Assertions.assertEquals("parent1", nodes[0].designation)
                Assertions.assertEquals("CHF", (nodes[0] as BomExpNodeDto).baseCurrency.ccy)
                Assertions.assertNull((nodes[0] as BomExpNodeDto).costPerPartInBaseCurrency.currencyInfo)
                Assertions.assertNull((nodes[0] as BomExpNodeDto).reportSent)
                Assertions.assertEquals((nodes[0] as BomExpNodeDto).costModule, Model.INJ2)
                assertCost(BigDecimal("42.0"), nodes[0])
                Assertions.assertEquals(null, nodes[0].childrenIds)
                Assertions.assertTrue(nodes[0].copyable, "copyable flag is wrong")
                Assertions.assertFalse(nodes[0].reorderInfo.moveDownEnabled, "move enabled flag is wrong")
                Assertions.assertFalse(nodes[0].reorderInfo.moveUpEnabled, "move enabled flag is wrong")
            }.verifyComplete()
        }

        @Test
        @DisplayName("specific node with children should be returned by bom explorer api")
        fun testBomExplorerGetNodeWithChildren() {
            val req =
                BomExpRequestDto(
                    listOf(
                        BomExpNodeQueryDto(parent1Snapshot.bomNodeId.toMongoFormatStr(), true, null),
                    ),
                    null,
                )
            val nodes = bomExplorerServiceV2.getBomNodes(accessCheck, projectId.toHexString(), req).block()!!
            Assertions.assertEquals(3, nodes.size)
            Assertions.assertEquals("parent1", nodes[0].designation)
            assertCost(BigDecimal("42.0"), nodes[0])
            Assertions.assertEquals("CHF", (nodes[0] as BomExpNodeDto).baseCurrency.ccy)
            Assertions.assertEquals(2, nodes[0].childrenIds?.size)
            Assertions.assertTrue(nodes[0].copyable, "copyable flag is wrong")
            Assertions.assertEquals(
                child1Snapshot.bomNodeId.toMongoBomNodeId().toString(),
                nodes[0].childrenIds?.get(0)?.nodeId,
            )
            val consumable = nodes.find { if (it is BomExpEntryDto) it.type == BomExpEntryType.CONSUMABLE else false }
            Assertions.assertNotNull(consumable)
            Assertions.assertEquals("manufacturing-child-consumable", consumable?.designation)
            val child = nodes.find { it.designation == "child1" }!!
            Assertions.assertFalse(child.copyable, "copyable flag is wrong")
            Assertions.assertTrue(child.reorderInfo.moveDownEnabled, "move enabled flag is wrong")
            Assertions.assertFalse(child.reorderInfo.moveUpEnabled, "move enabled flag is wrong")
            val validCopySourePerNode =
                nodes.filterIsInstance<BomExpNodeDto>().filter { it.type == CalculationType.MANUAL_CALCULATION }.flatMap {
                    listOf(
                        Executable {
                            Assertions.assertTrue {
                                it.validCopySources.isNotEmpty()
                            }
                        },
                        Executable {
                            Assertions.assertTrue {
                                it.co2PerPart.broken
                            }
                        },
                        Executable {
                            Assertions.assertFalse {
                                it.costPerPartInBaseCurrency.broken
                            }
                        },
                    )
                }
            Assertions.assertAll(validCopySourePerNode)
        }
    }

    @Nested
    inner class WithCurrencyInfoFromBomrads {
        @BeforeEach
        fun setupWithCurrencyInfoFromBomrads() {
            setup(true)
        }

        @Test
        @DisplayName("root nodes should be returned by bom explorer api")
        fun testBomExlorerApiRootNodes() {
            val nodes = bomExplorerServiceV2.getBomNodes(accessCheck, projectId.toHexString(), null).block()!!
            Assertions.assertEquals(1, nodes.size)

            Assertions.assertEquals("parent1", nodes[0].designation)
            Assertions.assertEquals("CHF", (nodes[0] as BomExpNodeDto).baseCurrency.ccy)
            assertCost(BigDecimal("42.0"), nodes[0])
            Assertions.assertEquals(null, nodes[0].childrenIds)
            Assertions.assertTrue(nodes[0].copyable, "copyable flag is wrong")
            Assertions.assertFalse(nodes[0].reorderInfo.moveDownEnabled, "move enabled flag is wrong")
            Assertions.assertFalse(nodes[0].reorderInfo.moveUpEnabled, "move enabled flag is wrong")
        }

        @Test
        @DisplayName("specific node with children should be returned by bom explorer api")
        fun testBomExplorerGetNodeWithChildren() {
            val req =
                BomExpRequestDto(
                    listOf(
                        BomExpNodeQueryDto(parent1Snapshot.bomNodeId.toMongoFormatStr(), true, null),
                    ),
                    null,
                )
            val nodes = bomExplorerServiceV2.getBomNodes(accessCheck, projectId.toHexString(), req).block()!!
            Assertions.assertEquals(3, nodes.size)
            Assertions.assertEquals("parent1", nodes[0].designation)
            assertCost(BigDecimal("42.0"), nodes[0])
            Assertions.assertEquals("CHF", (nodes[0] as BomExpNodeDto).baseCurrency.ccy)
            Assertions.assertEquals(2, nodes[0].childrenIds?.size)
            Assertions.assertTrue(nodes[0].copyable, "copyable flag is wrong")
            Assertions.assertEquals(
                child1Snapshot.bomNodeId.toMongoBomNodeId().toString(),
                nodes[0].childrenIds?.get(0)?.nodeId,
            )
            val consumable = nodes.find { if (it is BomExpEntryDto) it.type == BomExpEntryType.CONSUMABLE else false }
            Assertions.assertNotNull(consumable)
            Assertions.assertEquals("manufacturing-child-consumable", consumable?.designation)
            val child = nodes.find { it.designation == "child1" }!!
            Assertions.assertFalse(child.copyable, "copyable flag is wrong")
            Assertions.assertTrue(child.reorderInfo.moveDownEnabled, "move enabled flag is wrong")
            Assertions.assertFalse(child.reorderInfo.moveUpEnabled, "move enabled flag is wrong")
        }

        @Test
        @DisplayName("specific node with children for other branch should be returned by bom explorer api")
        fun testBomExplorerGetNodeForOtherBranchWithChildren() {
            // this test basically ensures that we handle treeId vs previousTreeId in the snapshotdto correctly
            val req =
                BomExpRequestDto(
                    listOf(
                        BomExpNodeQueryDto(parent1Snapshot.bomNodeId.toMongoFormatStr(), true, null),
                    ),
                    otherBranchId.toHexString(),
                )
            val nodes = bomExplorerServiceV2.getBomNodes(accessCheck, projectId.toHexString(), req).block()!!
            Assertions.assertEquals(3, nodes.size)
            Assertions.assertEquals("parent1", nodes[0].designation)
            assertCost(BigDecimal("42.0"), nodes[0])
            Assertions.assertEquals("CHF", (nodes[0] as BomExpNodeDto).baseCurrency.ccy)
            Assertions.assertEquals(2, nodes[0].childrenIds?.size)
            Assertions.assertTrue(nodes[0].copyable, "copyable flag is wrong")
            Assertions.assertEquals(
                child1Snapshot.bomNodeId.toMongoBomNodeId().toString(),
                nodes[0].childrenIds?.get(0)?.nodeId,
            )
            val consumable = nodes.find { if (it is BomExpEntryDto) it.type == BomExpEntryType.CONSUMABLE else false }
            Assertions.assertNotNull(consumable)
            Assertions.assertEquals("manufacturing-child-consumable", consumable!!.designation)
            val child = nodes.find { it.designation == "child1" }!!
            Assertions.assertFalse(child.copyable, "copyable flag is wrong")
            Assertions.assertTrue(child.reorderInfo.moveDownEnabled, "move enabled flag is wrong")
            Assertions.assertFalse(child.reorderInfo.moveUpEnabled, "move enabled flag is wrong")
        }

        @Test
        @DisplayName("specific node with children for other branch and treeId should be returned by bom explorer api")
        fun testBomExplorerGetNodeForOtherBranchWithWithChildrenAndTreeId() {
            // this test basically ensures that we handle treeId vs previousTreeId in the snapshotdto correctly
            val req =
                BomExpRequestDto(
                    listOf(
                        BomExpNodeQueryDto(
                            parent1Snapshot.bomNodeId.toMongoFormatStr(),
                            true,
                            createCacheIdentifier(parent1Snapshot.copy(branchId = otherBranchId.toBranchId())),
                        ),
                    ),
                    otherBranchId.toHexString(),
                )
            val nodes = bomExplorerServiceV2.getBomNodes(accessCheck, projectId.toHexString(), req).block()!!
            Assertions.assertEquals(1, nodes.size)
            Assertions.assertEquals("parent1", nodes[0].designation)
            Assertions.assertEquals("CHF", (nodes[0] as BomExpNodeDto).baseCurrency.ccy)
            assertCost(BigDecimal("42.0"), nodes[0])
            Assertions.assertNull(nodes[0].childrenIds, "no children ids should be set when node did not change")
            Assertions.assertTrue(nodes[0].copyable, "copyable flag is wrong")
        }

        @Test
        @DisplayName("bom explorer api should not return node if tree id did not change")
        fun testBomExplorerGetNodeWithChildrenAndTreeId() {
            val req =
                BomExpRequestDto(
                    listOf(
                        BomExpNodeQueryDto(
                            parent1Snapshot.bomNodeId.toMongoFormatStr(),
                            true,
                            createCacheIdentifier(parent1Snapshot),
                        ),
                    ),
                    null,
                )
            val nodes = bomExplorerServiceV2.getBomNodes(accessCheck, projectId.toHexString(), req).block()!!
            Assertions.assertEquals(1, nodes.size)
            Assertions.assertEquals("parent1", nodes[0].designation)
            Assertions.assertEquals("CHF", (nodes[0] as BomExpNodeDto).baseCurrency.ccy)
            assertCost(BigDecimal("42.0"), nodes[0])
            Assertions.assertNull(nodes[0].childrenIds, "no children ids should be set when node did not change")
            Assertions.assertTrue(nodes[0].copyable, "copyable flag is wrong")
        }
    }
}
