package com.nu.bom.core.manufacturing.exchangeRates

import com.nu.bom.core.CalculationTestBase
import com.nu.bom.core.manufacturing.configFields.behaviourFieldInjection.fieldBuilder.TestDynamicFieldConfigBuilder
import com.nu.bom.core.manufacturing.entities.BaseEntityFields
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.fieldTypes.ExchangeRatesField
import com.nu.bom.core.manufacturing.fieldTypes.PriceComponents
import com.nu.bom.core.utils.assertBigDecimalsEquals
import com.tset.core.service.domain.Currency
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import java.math.BigDecimal
import com.nu.bom.core.manufacturing.fieldTypes.Currency as FTCurrency

class PriceComponentFieldsTest : CalculationTestBase(dynamicEntityFieldConfigBuilders = listOf(TestDynamicFieldConfigBuilder())) {
    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun simpleEntityWithOnePriceComponentFieldInUSDAndBaseCurrencyEUR(priceComponentAsOverride: Boolean) {
        val exchangeRateField =
            ExchangeRatesField(
                mapOf(
                    Currency("EUR") to 1.0.toBigDecimal(),
                    Currency("USD") to 0.8.toBigDecimal(),
                ),
            )

        val priceComponent =
            PriceComponents(
                listOf(
                    PriceComponents.PriceComponent("first", 10.0.toBigDecimal(), Currency("USD")),
                    PriceComponents.PriceComponent("second", 100.0.toBigDecimal(), Currency("USD")),
                ),
            )

        val entity =
            createEntityForPriceComponentField(
                priceComponentAsOverride = priceComponentAsOverride,
                exchangeRateField = exchangeRateField,
                priceComponents = priceComponent,
            )

        calculateAndPrintResult(entity)

        validateResultPriceComponent(
            entity.name,
            TestEntityWithOnePriceComponentField::myPriceComponents.name,
            mapOf(
                "first" to 12.5.toBigDecimal(),
                "second" to 125.0.toBigDecimal(),
            ),
        )

        changeInput(
            entity,
            PriceComponents(
                listOf(
                    PriceComponents.PriceComponent("first", 10.0.toBigDecimal(), Currency("USD")),
                    PriceComponents.PriceComponent("second", 1000.0.toBigDecimal(), Currency("USD")),
                ),
            ),
        )

        calculateAndPrintResult(entity)
        validateResultPriceComponent(
            entity.name,
            TestEntityWithOnePriceComponentField::myPriceComponents.name,
            mapOf(
                "first" to 12.5.toBigDecimal(),
                "second" to 1250.0.toBigDecimal(),
            ),
        )
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun simpleEntityWithOnePriceComponentFieldInUSDAndBaseCurrencyUSD(priceComponentAsOverride: Boolean) {
        val exchangeRateField =
            ExchangeRatesField(
                mapOf(
                    Currency("EUR") to 1.0.toBigDecimal(),
                    Currency("USD") to 0.8.toBigDecimal(),
                ),
            )

        val priceComponent =
            PriceComponents(
                listOf(
                    PriceComponents.PriceComponent("first", 10.0.toBigDecimal(), Currency("USD")),
                    PriceComponents.PriceComponent("second", 100.0.toBigDecimal(), Currency("USD")),
                ),
            )

        val entity =
            createEntityForPriceComponentField(
                priceComponentAsOverride = priceComponentAsOverride,
                exchangeRateField = exchangeRateField,
                priceComponents = priceComponent,
                baseCurrency =
                    FTCurrency("USD"),
            )

        calculateAndPrintResult(entity)

        validateResultPriceComponent(
            entity.name,
            TestEntityWithOnePriceComponentField::myPriceComponents.name,
            mapOf(
                "first" to 12.5.toBigDecimal(),
                "second" to 125.0.toBigDecimal(),
            ),
        )

        changeInput(
            entity,
            PriceComponents(
                listOf(
                    PriceComponents.PriceComponent("first", 10.0.toBigDecimal(), Currency("USD")),
                    PriceComponents.PriceComponent("second", 1000.0.toBigDecimal(), Currency("USD")),
                ),
            ),
        )

        calculateAndPrintResult(entity)
        validateResultPriceComponent(
            entity.name,
            TestEntityWithOnePriceComponentField::myPriceComponents.name,
            mapOf(
                "first" to 12.5.toBigDecimal(),
                "second" to 1250.0.toBigDecimal(),
            ),
        )
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun simpleEntityWithOnePriceComponentFieldInEurAndBaseCurrencyUSD(priceComponentAsOverride: Boolean) {
        val exchangeRateField =
            ExchangeRatesField(
                mapOf(
                    Currency("EUR") to 1.0.toBigDecimal(),
                    Currency("USD") to 0.8.toBigDecimal(),
                ),
            )

        val priceComponent =
            PriceComponents(
                listOf(
                    PriceComponents.PriceComponent("first", 10.0.toBigDecimal(), Currency.EUR),
                    PriceComponents.PriceComponent("second", 100.0.toBigDecimal(), Currency.EUR),
                ),
            )

        val entity =
            createEntityForPriceComponentField(
                priceComponentAsOverride = priceComponentAsOverride,
                exchangeRateField = exchangeRateField,
                priceComponents = priceComponent,
                baseCurrency =
                    FTCurrency("USD"),
            )

        calculateAndPrintResult(entity)

        validateResultPriceComponent(
            entity.name,
            TestEntityWithOnePriceComponentField::myPriceComponents.name,
            mapOf(
                "first" to 10.0.toBigDecimal(),
                "second" to 100.0.toBigDecimal(),
            ),
        )

        changeInput(
            entity,
            PriceComponents(
                listOf(
                    PriceComponents.PriceComponent("first", 10.0.toBigDecimal(), Currency.EUR),
                    PriceComponents.PriceComponent("second", 1000.0.toBigDecimal(), Currency.EUR),
                ),
            ),
        )

        calculateAndPrintResult(entity)
        validateResultPriceComponent(
            entity.name,
            TestEntityWithOnePriceComponentField::myPriceComponents.name,
            mapOf(
                "first" to 10.0.toBigDecimal(),
                "second" to 1000.0.toBigDecimal(),
            ),
        )
    }

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun simpleEntityWithOnePriceComponentFieldInEurAndBaseCurrencyEUR(priceComponentAsOverride: Boolean) {
        val exchangeRateField =
            ExchangeRatesField(
                mapOf(
                    Currency("EUR") to 1.0.toBigDecimal(),
                    Currency("USD") to 0.8.toBigDecimal(),
                ),
            )

        val priceComponent =
            PriceComponents(
                listOf(
                    PriceComponents.PriceComponent("first", 10.0.toBigDecimal(), Currency.EUR),
                    PriceComponents.PriceComponent("second", 100.0.toBigDecimal(), Currency.EUR),
                ),
            )

        val entity =
            createEntityForPriceComponentField(
                priceComponentAsOverride = priceComponentAsOverride,
                exchangeRateField = exchangeRateField,
                priceComponents = priceComponent,
            )

        calculateAndPrintResult(entity)

        validateResultPriceComponent(
            entity.name,
            TestEntityWithOnePriceComponentField::myPriceComponents.name,
            mapOf(
                "first" to 10.0.toBigDecimal(),
                "second" to 100.0.toBigDecimal(),
            ),
        )

        changeInput(
            entity,
            PriceComponents(
                listOf(
                    PriceComponents.PriceComponent("first", 10.0.toBigDecimal(), Currency.EUR),
                    PriceComponents.PriceComponent("second", 1000.0.toBigDecimal(), Currency.EUR),
                ),
            ),
        )

        calculateAndPrintResult(entity)
        validateResultPriceComponent(
            entity.name,
            TestEntityWithOnePriceComponentField::myPriceComponents.name,
            mapOf(
                "first" to 10.0.toBigDecimal(),
                "second" to 1000.0.toBigDecimal(),
            ),
        )
    }

    private fun changeInput(
        entity: TestEntityWithOnePriceComponentField,
        field: PriceComponents,
    ) {
        builder.clear()
        builder.addInputs(
            entity,
            emptyMap(),
            fields = entity.getFieldResultMap(),
            overrides = mapOf(TestEntityWithOnePriceComponentField::myPriceComponents.name to field),
        )
    }

    private fun calculateAndPrintResult(entityForFieldInjection: ManufacturingEntity) {
        calculate(entityForFieldInjection)

        printResults(
            true,
            true,
            hideDependencies = false,
            hideFieldsFromKClasses =
                setOf(
                    ManufacturingEntity::class,
                    BaseEntityFields::class,
                    BaseManufacturing::class,
                ),
            printSpecialFields = true,
        )
    }

    private fun createEntityForPriceComponentField(
        priceComponentAsOverride: Boolean,
        name: String = "Test entity for price component field",
        parent: ManufacturingEntity? = null,
        exchangeRateField: ExchangeRatesField,
        priceComponents: PriceComponents,
        baseCurrency: FTCurrency = FTCurrency("EUR"),
    ): TestEntityWithOnePriceComponentField {
        val (parameters, overrides) =
            if (priceComponentAsOverride) {
                Pair(
                    mapOf(
                        TestEntityWithOnePriceComponentField::exchangeRates.name to exchangeRateField,
                        BaseEntityFields::baseCurrency.name to baseCurrency,
                    ),
                    mapOf(
                        TestEntityWithOnePriceComponentField::myPriceComponents.name to priceComponents,
                    ),
                )
            } else {
                Pair(
                    mapOf(
                        TestEntityWithOnePriceComponentField::exchangeRates.name to exchangeRateField,
                        BaseEntityFields::baseCurrency.name to baseCurrency,
                        TestEntityWithOnePriceComponentField::myPriceComponents.name to priceComponents,
                    ),
                    emptyMap(),
                )
            }

        return addObject(
            name = name,
            clazz = TestEntityWithOnePriceComponentField::class.java,
            parent = parent,
            parameters = parameters,
            overrides = overrides,
        ) as TestEntityWithOnePriceComponentField
    }

    private fun validateResultPriceComponent(
        objectName: String,
        fieldName: String,
        expectedValues: Map<String, BigDecimal>,
    ): PriceComponents =
        validateResultField(objectName, fieldName) { field ->
            assertTrue(field.isMonetaryType, "$objectName.$fieldName.isMonetary")
            val result = field.getCurrentResult()
            assertNotNull(result, "$objectName.$fieldName.currentResult")
            assertEquals("PriceComponents", result!!.javaClass.simpleName, "$objectName.$fieldName.currentResult")
            (result as PriceComponents).res.forEach {
                val expectedValue = expectedValues[it.componentName]
                logger.info("validate $objectName[$fieldName] expected to be '$expectedValue' but actual result is '$result'")
                assertBigDecimalsEquals(expectedValue, it.price, "$objectName.$fieldName.currentResult.res[${it.componentName}]")
            }
        }.getCurrentResult() as PriceComponents
}
