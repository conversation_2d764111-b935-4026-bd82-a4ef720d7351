package com.nu.bom.core.service.migration.lazy

import com.nu.bom.core.utils.FileResourceLoader
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.core.io.DefaultResourceLoader

class MigrationManagerTests {
    @Test
    fun `parsing in xml format preserves expected order`() {
        val manager = MigrationManager()

        val migration1 =
            "" +
                "<migration>" +
                "<changeSet id=\"W\">" +
                "<class name=\"A\" match=\"direct\"/>" +

                "<class name=\"J\" match=\"base\"/>" +

                "<class name=\"M\" match=\"dynamic\"/>" +
                "</changeSet>" +

                "<changeSet id=\"X\">" +
                "<class name=\"A\" match=\"direct\"/>" +
                "<class name=\"B\" match=\"direct\"/>" +

                "<class name=\"J\" match=\"base\"/>" +
                "<class name=\"K\" match=\"base\"/>" +

                "<class name=\"M\" match=\"dynamic\"/>" +
                "<class name=\"N\" match=\"dynamic\"/>" +
                "</changeSet>" +
                "</migration>"

        val migration2 =
            "" +
                "<migration>" +
                "<changeSet id=\"Y\">" +
                "<class name=\"A\" match=\"direct\"/>" +
                "<class name=\"B\" match=\"direct\"/>" +
                "<class name=\"C\" match=\"direct\"/>" +

                "<class name=\"J\" match=\"base\"/>" +
                "<class name=\"K\" match=\"base\"/>" +
                "<class name=\"L\" match=\"base\"/>" +

                "<class name=\"M\" match=\"dynamic\"/>" +
                "<class name=\"N\" match=\"dynamic\"/>" +
                "<class name=\"O\" match=\"dynamic\"/>" +
                "</changeSet>" +

                "<changeSet id=\"Z\">" +
                "<class name=\"A\" match=\"direct\"/>" +
                "<class name=\"B\" match=\"direct\"/>" +
                "<class name=\"C\" match=\"direct\"/>" +
                "<class name=\"D\" match=\"direct\"/>" +

                "<class name=\"J\" match=\"base\"/>" +
                "<class name=\"K\" match=\"base\"/>" +
                "<class name=\"L\" match=\"base\"/>" +
                "<class name=\"M\" match=\"base\"/>" +

                "<class name=\"M\" match=\"dynamic\"/>" +
                "<class name=\"N\" match=\"dynamic\"/>" +
                "<class name=\"O\" match=\"dynamic\"/>" +
                "<class name=\"P\" match=\"dynamic\"/>" +
                "</changeSet>" +
                "</migration>"

        val migration3 =
            "" +
                "<migration>" +
                "<changeSet id=\"ZS\">" +
                "<class name=\"Q\" match=\"direct\"/>" +

                "<class name=\"R\" match=\"base\"/>" +

                "<class name=\"S\" match=\"dynamic\"/>" +
                "</changeSet>" +
                "</migration>"

        // load streams in order
        manager.loadMigration(migration1.byteInputStream())
        manager.loadMigration(migration2.byteInputStream())
        manager.loadMigration(migration3.byteInputStream())

        // assert imported changeset orders
        run {
            val actual = manager.getOrderedChangeSetsAfter("A", emptySet(), emptySet(), null)
            val expected = listOf("W", "X", "Y", "Z").map { MigrationChangeSetId(it) }

            assertThat(actual).isEqualTo(expected)
        }

        run {
            val actual = manager.getOrderedChangeSetsAfter("B", emptySet(), emptySet(), null)
            val expected = listOf("X", "Y", "Z").map { MigrationChangeSetId(it) }

            assertThat(actual).isEqualTo(expected)
        }

        run {
            val actual = manager.getOrderedChangeSetsAfter("C", emptySet(), emptySet(), null)
            val expected = listOf("Y", "Z").map { MigrationChangeSetId(it) }

            assertThat(actual).isEqualTo(expected)
        }

        run {
            val actual = manager.getOrderedChangeSetsAfter("D", emptySet(), emptySet(), null)
            val expected = listOf("Z").map { MigrationChangeSetId(it) }

            assertThat(actual).isEqualTo(expected)
        }

        run {
            val actual = manager.getOrderedChangeSetsAfter("Q", emptySet(), emptySet(), null)
            val expected = listOf("ZS").map { MigrationChangeSetId(it) }

            assertThat(actual).isEqualTo(expected)
        }

        run {
            val actual = manager.getOrderedChangeSetsAfter("", setOf("J"), emptySet(), null)
            val expected = listOf("W", "X", "Y", "Z").map { MigrationChangeSetId(it) }

            assertThat(actual).isEqualTo(expected)
        }

        run {
            val actual = manager.getOrderedChangeSetsAfter("", setOf("K"), emptySet(), null)
            val expected = listOf("X", "Y", "Z").map { MigrationChangeSetId(it) }

            assertThat(actual).isEqualTo(expected)
        }

        run {
            val actual = manager.getOrderedChangeSetsAfter("", setOf("L"), emptySet(), null)
            val expected = listOf("Y", "Z").map { MigrationChangeSetId(it) }

            assertThat(actual).isEqualTo(expected)
        }

        run {
            val actual = manager.getOrderedChangeSetsAfter("", setOf("M"), emptySet(), null)
            val expected = listOf("Z").map { MigrationChangeSetId(it) }

            assertThat(actual).isEqualTo(expected)
        }

        run {
            val actual = manager.getOrderedChangeSetsAfter("", setOf("R"), emptySet(), null)
            val expected = listOf("ZS").map { MigrationChangeSetId(it) }

            assertThat(actual).isEqualTo(expected)
        }

        run {
            val actual = manager.getOrderedChangeSetsAfter("", emptySet(), setOf("M"), null)
            val expected = listOf("W", "X", "Y", "Z").map { MigrationChangeSetId(it) }

            assertThat(actual).isEqualTo(expected)
        }

        run {
            val actual = manager.getOrderedChangeSetsAfter("", emptySet(), setOf("N"), null)
            val expected = listOf("X", "Y", "Z").map { MigrationChangeSetId(it) }

            assertThat(actual).isEqualTo(expected)
        }

        run {
            val actual = manager.getOrderedChangeSetsAfter("", emptySet(), setOf("O"), null)
            val expected = listOf("Y", "Z").map { MigrationChangeSetId(it) }

            assertThat(actual).isEqualTo(expected)
        }

        run {
            val actual = manager.getOrderedChangeSetsAfter("", emptySet(), setOf("P"), null)
            val expected = listOf("Z").map { MigrationChangeSetId(it) }

            assertThat(actual).isEqualTo(expected)
        }

        run {
            val actual = manager.getOrderedChangeSetsAfter("", emptySet(), setOf("S"), null)
            val expected = listOf("ZS").map { MigrationChangeSetId(it) }

            assertThat(actual).isEqualTo(expected)
        }

        // assert requested starting point
        run {
            assertThat(manager.getOrderedChangeSetsAfter("A", emptySet(), emptySet(), MigrationChangeSetId("W")))
                .isEqualTo(listOf("X", "Y", "Z").map { MigrationChangeSetId(it) })

            assertThat(manager.getOrderedChangeSetsAfter("A", emptySet(), emptySet(), MigrationChangeSetId("X")))
                .isEqualTo(listOf("Y", "Z").map { MigrationChangeSetId(it) })

            assertThat(manager.getOrderedChangeSetsAfter("A", emptySet(), emptySet(), MigrationChangeSetId("Y")))
                .isEqualTo(listOf("Z").map { MigrationChangeSetId(it) })

            assertThat(manager.getOrderedChangeSetsAfter("A", emptySet(), emptySet(), MigrationChangeSetId("Z")))
                .isEqualTo(emptyList<MigrationChangeSetId>())
        }

        // assert latest change set ids
        run {
            assertThat(manager.getLatestChangeSetId("A", emptySet(), emptySet())).isEqualTo(MigrationChangeSetId("Z"))
            assertThat(manager.getLatestChangeSetId("B", emptySet(), emptySet())).isEqualTo(MigrationChangeSetId("Z"))
            assertThat(manager.getLatestChangeSetId("C", emptySet(), emptySet())).isEqualTo(MigrationChangeSetId("Z"))
            assertThat(manager.getLatestChangeSetId("D", emptySet(), emptySet())).isEqualTo(MigrationChangeSetId("Z"))

            assertThat(manager.getLatestChangeSetId("", setOf("J"), emptySet())).isEqualTo(MigrationChangeSetId("Z"))
            assertThat(manager.getLatestChangeSetId("", setOf("K"), emptySet())).isEqualTo(MigrationChangeSetId("Z"))
            assertThat(manager.getLatestChangeSetId("", setOf("L"), emptySet())).isEqualTo(MigrationChangeSetId("Z"))
            assertThat(manager.getLatestChangeSetId("", setOf("M"), emptySet())).isEqualTo(MigrationChangeSetId("Z"))

            assertThat(manager.getLatestChangeSetId("Q", emptySet(), emptySet())).isEqualTo(MigrationChangeSetId("ZS"))
            assertThat(manager.getLatestChangeSetId("", setOf("R"), emptySet())).isEqualTo(MigrationChangeSetId("ZS"))
        }
    }

    @Test
    fun `non-unique changeset id throws an error`() {
        val manager = MigrationManager()

        manager.addMigration(migration(changeSet("non-unique-id")))

        assertThrows<IllegalStateException> {
            manager.addMigration(migration(changeSet("non-unique-id")))
        }.let {
            assertThat(it.message).contains("must be unique")
        }
    }

    @Test
    fun `changesets from different matchers are detected in order`() {
        val manager = MigrationManager()

        // assume class hierarchy A -> B

        manager.addMigration(migration(changeSet("001", direct = setOf("A"))))
        manager.addMigration(migration(changeSet("002", base = setOf("B"))))
        manager.addMigration(migration(changeSet("003", base = setOf("C"))))
        manager.addMigration(migration(changeSet("004", hierarchy = setOf("A"))))
        manager.addMigration(migration(changeSet("005", hierarchy = setOf("B"))))
        manager.addMigration(migration(changeSet("006", hierarchy = setOf("C"))))

        assertThat(manager.getLatestChangeSetId("A", setOf("B", "C"), emptySet()))
            .isEqualTo(MigrationChangeSetId("006"))

        assertThat(manager.getOrderedChangeSetsAfter("A", setOf("B", "C"), emptySet(), null))
            .isEqualTo(listOf("001", "002", "003", "004", "005", "006").map { MigrationChangeSetId(it) })

        assertThat(manager.getOrderedChangeSetsAfter("A", setOf("B", "C"), emptySet(), MigrationChangeSetId("001")))
            .isEqualTo(listOf("002", "003", "004", "005", "006").map { MigrationChangeSetId(it) })

        assertThat(manager.getOrderedChangeSetsAfter("A", setOf("B", "C"), emptySet(), MigrationChangeSetId("002")))
            .isEqualTo(listOf("003", "004", "005", "006").map { MigrationChangeSetId(it) })

        assertThat(manager.getOrderedChangeSetsAfter("A", setOf("B", "C"), emptySet(), MigrationChangeSetId("003")))
            .isEqualTo(listOf("004", "005", "006").map { MigrationChangeSetId(it) })

        assertThat(manager.getOrderedChangeSetsAfter("A", setOf("B", "C"), emptySet(), MigrationChangeSetId("004")))
            .isEqualTo(listOf("005", "006").map { MigrationChangeSetId(it) })

        assertThat(manager.getOrderedChangeSetsAfter("A", setOf("B", "C"), emptySet(), MigrationChangeSetId("005")))
            .isEqualTo(listOf("006").map { MigrationChangeSetId(it) })

        assertThat(manager.getOrderedChangeSetsAfter("A", setOf("B", "C"), emptySet(), MigrationChangeSetId("006")))
            .isEmpty()
    }

    @Test
    fun `direct match strategy matches direct class only`() {
        val manager = MigrationManager()

        // assume class hierarchy A -> B -> C

        // add end-class migration for B
        manager.addMigration(migration(changeSet("001", direct = setOf("B"))))

        assertThat(manager.getLatestChangeSetId("B", setOf("C"), emptySet()))
            .isEqualTo(MigrationChangeSetId("001"))

        assertThat(manager.getLatestChangeSetId("A", setOf("B", "C"), emptySet()))
            .isNull()

        assertThat(manager.getOrderedChangeSetsAfter("B", setOf("C"), emptySet(), null))
            .isEqualTo(listOf("001").map { MigrationChangeSetId(it) })

        assertThat(manager.getOrderedChangeSetsAfter("A", setOf("B", "C"), emptySet(), null))
            .isEmpty()
    }

    @Test
    fun `base match strategy matches base classes only`() {
        val manager = MigrationManager()

        // assume class hierarchy A -> B -> C

        // add base match for B
        manager.addMigration(migration(changeSet("001", base = setOf("B"))))

        assertThat(manager.getLatestChangeSetId("A", setOf("B", "C"), emptySet()))
            .isEqualTo(MigrationChangeSetId("001"))

        assertThat(manager.getLatestChangeSetId("B", setOf("C"), emptySet()))
            .isNull()

        assertThat(manager.getOrderedChangeSetsAfter("A", setOf("B", "C"), emptySet(), null))
            .isEqualTo(listOf("001").map { MigrationChangeSetId(it) })

        assertThat(manager.getOrderedChangeSetsAfter("B", setOf("C"), emptySet(), null))
            .isEmpty()
    }

    @Test
    fun `dynamic match strategy matches dynamic classes only`() {
        val manager = MigrationManager()

        // assume class hierarchy A -> B -> C

        // add dynamic match for B
        manager.addMigration(migration(changeSet("001", dynamic = setOf("B"))))

        assertThat(manager.getLatestChangeSetId("A", emptySet(), setOf("B", "C")))
            .isEqualTo(MigrationChangeSetId("001"))

        assertThat(manager.getLatestChangeSetId("B", emptySet(), setOf("C")))
            .isNull()

        assertThat(manager.getOrderedChangeSetsAfter("A", emptySet(), setOf("B", "C"), null))
            .isEqualTo(listOf("001").map { MigrationChangeSetId(it) })

        assertThat(manager.getOrderedChangeSetsAfter("B", emptySet(), setOf("C"), null))
            .isEmpty()
    }

    @Test
    fun `hierarchy match strategy matches both direct class and base classes`() {
        val manager = MigrationManager()

        // assume class hierarchy A -> B -> C

        // add hierarchy match for B
        manager.addMigration(migration(changeSet("001", hierarchy = setOf("B"))))

        assertThat(manager.getLatestChangeSetId("A", setOf("B", "C"), emptySet()))
            .isEqualTo(MigrationChangeSetId("001"))

        assertThat(manager.getLatestChangeSetId("B", setOf("C"), emptySet()))
            .isEqualTo(MigrationChangeSetId("001"))

        assertThat(manager.getOrderedChangeSetsAfter("A", setOf("B", "C"), emptySet(), null))
            .isEqualTo(listOf("001").map { MigrationChangeSetId(it) })

        assertThat(manager.getOrderedChangeSetsAfter("B", setOf("C"), emptySet(), null))
            .isEqualTo(listOf("001").map { MigrationChangeSetId(it) })
    }

    @Test
    fun `latest changeset excludes all older changesets`() {
        val manager = MigrationManager()

        // assume class A and B
        //
        // class A has 2 migrations
        // class B has 1 migration which changing its class to A

        manager.addMigration(migration(changeSet("001", direct = setOf("A"))))
        manager.addMigration(migration(changeSet("002", direct = setOf("A"))))
        manager.addMigration(migration(changeSet("003", direct = setOf("B"))))
        manager.addMigration(migration(changeSet("004", direct = setOf("A"))))

        // only 004 should apply after 003
        assertThat(manager.getOrderedChangeSetsAfter("A", emptySet(), emptySet(), MigrationChangeSetId("003")))
            .isEqualTo(listOf(MigrationChangeSetId("004")))

        assertThat(manager.getLatestChangeSetId("A", emptySet(), emptySet()))
            .isEqualTo(MigrationChangeSetId("004"))
    }

    /**
     * In case this test breaks...
     * a) you have added a new migration that should be treated as newest migration -> adapt the test
     * b) you rebased your branch against master -> you need to adapt the name of a newly added migration in your feature branch,
     *    because it seems like somebody else added another migration in master that conflicts with your migration
     */
    @Test
    fun `make sure no rebase added a more recent migration`() {
        val newestExpectedMigration = "2025-05-15-material-ui-configuration-identifiers"
        val loader = FileResourceLoader(DefaultResourceLoader())
        val lastMigration = loader.loadFiles("/lazy-migrations/*.xml").sortedBy { it.filename }.last()
        assertThat(lastMigration.filename)
            .withFailMessage {
                "the expected newest migration does not match the newest migration in resources/lazy-migrations:\n" +
                    "* if you have added a new migration that should be treated as newest migration -> adapt the test\n" +
                    "* if you rebased your branch against master -> " +
                    "you need to adapt the name of a newly added migration in your feature branch, " +
                    "because it seems like somebody else added another migration in master that conflicts with your migration"
            }.isEqualTo("$newestExpectedMigration.xml")

        val manager = MigrationManager()
        manager.loadMigration(lastMigration.inputStream)
        assertThat(manager.getLatestChangeSetId()?.id)
            .`as`("Changeset ID does not match changeset's file name")
            .isEqualTo(newestExpectedMigration)
    }

    private fun migration(vararg changeSets: ChangeSet) = Migration(changeSets.toList())

    private fun changeSet(
        id: String,
        direct: Set<String> = emptySet(),
        base: Set<String> = emptySet(),
        dynamic: Set<String> = emptySet(),
        hierarchy: Set<String> = emptySet(),
    ) = ChangeSet(
        id,
        direct.map { ClassElement(it, ClassMatchingStrategy.DIRECT) } +
            base.map { ClassElement(it, ClassMatchingStrategy.BASE) } +
            dynamic.map { ClassElement(it, ClassMatchingStrategy.DYNAMIC) } +
            hierarchy.map { ClassElement(it, ClassMatchingStrategy.HIERARCHY) },
    )

    @Test
    fun `test getLatestChangeSetId`() {
        val manager = MigrationManager()

        manager.directClassMigrations.put("ClassName1", MigrationChangeSetId("ChangeSet1"))
        manager.directClassMigrations.put("ClassName2", MigrationChangeSetId("ChangeSet2"))

        manager.baseClassMigrations.put("BaseClassName1", MigrationChangeSetId("ChangeSet3"))
        manager.baseClassMigrations.put("BaseClassName2", MigrationChangeSetId("ChangeSet4"))

        manager.dynamicClassMigrations.put("DynamicClassName1", MigrationChangeSetId("ChangeSet5"))
        manager.dynamicClassMigrations.put("DynamicClassName2", MigrationChangeSetId("ChangeSet6"))

        manager.changeSetIndex[MigrationChangeSetId("ChangeSet1")] = 1
        manager.changeSetIndex[MigrationChangeSetId("ChangeSet2")] = 2
        manager.changeSetIndex[MigrationChangeSetId("ChangeSet3")] = 3
        manager.changeSetIndex[MigrationChangeSetId("ChangeSet4")] = 4
        manager.changeSetIndex[MigrationChangeSetId("ChangeSet5")] = 5
        manager.changeSetIndex[MigrationChangeSetId("ChangeSet6")] = 6

        val className = "ClassName1"
        val baseClassNames = setOf("BaseClassName1")
        val dynamicClassNames = setOf("DynamicClassName1")

        val latestChangeSetId = manager.getLatestChangeSetId(className, baseClassNames, dynamicClassNames)

        Assertions.assertEquals(MigrationChangeSetId(id = "ChangeSet5"), latestChangeSetId)
    }
}
