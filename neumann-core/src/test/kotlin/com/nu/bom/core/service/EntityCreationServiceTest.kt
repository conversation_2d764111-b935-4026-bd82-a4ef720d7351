package com.nu.bom.core.service

import com.nu.bom.core.CalculationTestBase
import com.nu.bom.core.api.DISPLAY_DESIGNATION
import com.nu.bom.core.api.dtos.MasterDataCompositeKey
import com.nu.bom.core.controller.CalculationResultDto
import com.nu.bom.core.controller.CalculationResultWithSnapshot
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.KnowledgeManufacturingStep
import com.nu.bom.core.manufacturing.entities.Machine
import com.nu.bom.core.manufacturing.entities.ManualManufacturingStep
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ManufacturingStep
import com.nu.bom.core.manufacturing.entities.Material
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.ManufacturingStepType
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.service.ConfigurationManagementService
import com.nu.bom.core.manufacturing.utils.ManufacturingTreeUtils
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.CompositeTriggerAction
import com.nu.bom.core.model.EntityCreation
import com.nu.bom.core.model.MasterData
import com.nu.bom.core.model.MasterDataSelector
import com.nu.bom.core.model.TriggerAction
import com.nu.bom.core.service.change.ChangedEntities
import com.nu.bom.core.service.nuledge.NuLedgeService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.AccountUtil
import com.tset.bom.clients.nuledge.FieldParameterDto
import com.tset.bom.clients.nuledge.KnowledgeEntityDto
import org.assertj.core.api.Assertions.assertThat
import org.bson.types.ObjectId
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.doAnswer
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.eq
import org.mockito.kotlin.isNull
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.verifyNoMoreInteractions
import org.mockito.kotlin.whenever
import reactor.core.publisher.Mono
import kotlin.reflect.KClass

class EntityCreationServiceTest : CalculationTestBase() {
    companion object {
        @JvmStatic
        fun createEntityConfigurations(): List<Setup> =
            listOf(
                Setup(false),
                Setup(true),
                Setup(false, MasterDataSelector(key = "myMachine", type = MasterDataType.MACHINE)),
                Setup(true, MasterDataSelector(key = "myMachine", type = MasterDataType.MACHINE)),
                Setup(false, MasterDataSelector(key = "myConsumable", type = MasterDataType.CONSUMABLE)),
                Setup(true, MasterDataSelector(key = "myConsumable", type = MasterDataType.CONSUMABLE)),
                Setup(true, bulkCreation = true),
                Setup(false, bulkCreation = true),
                Setup(true, beforeAddCustomizer = MODIFY_MAN_AND_ADD_TRIGGER),
                Setup(true, bulkCreation = true, beforeAddCustomizer = MODIFY_MAN_AND_ADD_TRIGGER),
            )

        @JvmStatic
        fun createEntityConfigurationsForSteps(): List<Setup> =
            listOf(
                Setup(false),
                Setup(true),
            )

        @JvmStatic
        fun nuledgeTemplateBased(): List<Setup> =
            listOf(
                Setup(
                    false,
                    masterDataSelector = MasterDataSelector(key = "my-nuledge-template", type = MasterDataType.MANUFACTURING_STEP),
                ),
                Setup(true, masterDataSelector = MasterDataSelector(key = "my-nuledge-template", type = MasterDataType.MANUFACTURING_STEP)),
            )
    }

    lateinit var service: EntityCreationService

    private lateinit var manufacturingCalculationService: ManufacturingCalculationService
    private lateinit var nuLedgeService: NuLedgeService
    private lateinit var bomNodeService: BomNodeService
    private lateinit var bomNodeConversionService: BomNodeConversionService
    private lateinit var configurationManagementService: ConfigurationManagementService

    private lateinit var bomNodeId: BomNodeId
    private lateinit var branchId: BranchId
    private lateinit var parentId: ObjectId
    private lateinit var manufacturing: Manufacturing
    private var knowledgeEntityDto: KnowledgeEntityDto? = null

    private lateinit var capturedTrigger: TriggerAction

    lateinit var access: AccessCheck

    data class Setup(
        val insertAtRoot: Boolean,
        val masterDataSelector: MasterDataSelector? = null,
        val bulkCreation: Boolean = false,
        val beforeAddCustomizer: Pair<BeforeAddCustomizer, BeforeAddCustomizerAssertion>? = null,
    )

    @BeforeEach
    fun setup() {
        manufacturingCalculationService = mock()
        nuLedgeService = mock()
        bomNodeService = mock()
        bomNodeConversionService = mock()
        configurationManagementService = mock()
        service =
            EntityCreationService(
                entityManager(),
                manufacturingCalculationService,
                factory,
                bomNodeConversionService,
                bomNodeService,
                configurationManagementService,
            )

        bomNodeId = BomNodeId()
        branchId = BranchId()
        parentId = ObjectId()

        access = AccountUtil.dummyAccessCheck()
        whenever(
            manufacturingCalculationService.transformAndCalculate(
                accessCheck = eq(access),
                bomNodeId = eq(bomNodeId),
                branchId = any<BranchId>(),
                triggerAction = any<TriggerAction>(),
                context = any(),
                forceRecalculate = any(),
                dirtyChildLoading = any(),
                extraBomNodeId = anyOrNull(),
                loadingMode = any(),
                customInputs = isNull(),
                transformRoot = anyOrNull(),
                transformManufacturing = any(),
            ),
        ).doAnswer {
            val func =
                it.arguments[11] as ((BaseManufacturing, BomNodeSnapshot, ChangedEntities) -> Mono<BaseManufacturing>)
            val result =
                func(
                    manufacturing,
                    mock(),
                    mock(),
                )
            val baseManufacturing = result.block()

            capturedTrigger = it.getArgument(3)

            calculate(baseManufacturing!!)

            Mono.just(
                CalculationResultWithSnapshot(
                    CalculationResultDto(
                        manufacturing = baseManufacturing,
                        bomNode = mock(),
                        missingInputs = emptyList(),
                        open = emptyList(),
                        openCalculations = emptyList(),
                        result = mock(),
                    ),
                    mock(),
                ),
            )
        }

        whenever(calculationContextServiceMock.nuLedge()).doReturn(nuLedgeService)

        whenever(nuLedgeService.getConfigurations(any(), any()))
            .thenAnswer {
                val key = it.arguments[1] as String
                Mono.justOrEmpty(knowledgeEntityDto?.copy(key = key))
            }
        mockMasterData()
    }

    private fun mockMasterData() {
        whenever(masterDataServiceMock.getLatestMasterDataByCompositeKey(any(), any(), any()))
            .thenAnswer {
                val selector = it.arguments[1] as MasterDataSelector
                Mono.just(
                    MasterData(
                        selector = selector,
                        data = mapOf("displayDesignation" to Text("masterData for " + selector.key)),
                    ),
                )
            }
    }

    private fun createEntityDto(
        entityType: Entities,
        fields: Map<String, FieldResultStar>,
        entityClass: KClass<out ManufacturingEntity>? = null,
        masterDataSelector: MasterDataSelector? = null,
        insertRoot: Boolean = false,
    ): EntityCreationDataConversionService.EntityCreationData =
        EntityCreationDataConversionService.EntityCreationData(
            bomNodeId = bomNodeId,
            branchId = branchId,
            parentId = if (insertRoot) manufacturing._id else parentId,
            projectId = null,
            childBomNodeId = null,
            items =
                listOf(
                    EntityCreationDataConversionService.EntityCreationData.ItemData(
                        entityType = entityType,
                        entityClass = entityClass,
                        masterDataSelector = masterDataSelector,
                        fields = fields,
                        overwrites = emptyMap(),
                        isolated = false,
                    ),
                ),
        )

    @ParameterizedTest
    @MethodSource("createEntityConfigurations")
    fun createMachine(setup: Setup) {
        manufacturing =
            run {
                val child = Material("material")
                child._id = parentId
                val manufacturing =
                    createManufacturing(
                        "testEntity",
                        parameters =
                            mapOf(
                                "peakUsableProductionVolumePerYear" to QuantityUnit(100_000.0.toBigDecimal()),
                                "averageUsableProductionVolumePerYear" to QuantityUnit(100_000.0.toBigDecimal()),
                            ),
                    )
                manufacturing.addChild(child)
                manufacturing
            }

        val dto =
            createEntityDto(
                Entities.MACHINE,
                mapOf(
                    "displayDesignation" to Text("Hello").withSource(FieldResult.SOURCE.I),
                    "quantity" to Num(1.0).withSource(FieldResult.SOURCE.I),
                ),
                insertRoot = setup.insertAtRoot,
                masterDataSelector = setup.masterDataSelector,
            )

        val beforeAddCustomizer = setup.beforeAddCustomizer?.first

        val entity =
            when (setup.bulkCreation) {
                // duplicate machines in creation data
                true -> service.createAndCalculate(access, dto.copy(items = dto.items + dto.items), beforeAddCustomizer)
                false -> service.createAndCalculate(access, dto, beforeAddCustomizer)
            }.block()
        assertNotNull(entity, "entity")

        val material = ManufacturingTreeUtils.findEntityById(manufacturing, parentId)
        assertNotNull(material, "material")

        val expectedChildCount =
            when (setup.bulkCreation) {
                true -> 2
                false -> 1
            }

        val machine =
            if (setup.insertAtRoot) {
                assertEquals(0, material!!.children.size, "material.children.size")
                manufacturing.children
                    // filter manual additions
                    .filter { (it.createdBy == null || it.createdByMocked) && it._id != parentId }
                    .also { assertEquals(expectedChildCount, it.size) }
                    .first()
            } else {
                assertEquals(expectedChildCount, material!!.children.size, "material.children.size")
                material.children[0]
            }
        assertEquals(0, machine.children.size, "machine.children.size")

        val entityType = machine.getEntityTypeAnnotation()
        if (setup.masterDataSelector != null) {
            assertEquals(setup.masterDataSelector.key, machine.name, "machine.name")
            assertEquals("Hello", machine.getFieldResultSafe("displayDesignation")?.res, "machine.name")
            assertEquals(setup.masterDataSelector.type.toString(), entityType?.name, "entity.entityTypeAnnotation")
        } else {
            assertEquals("Hello", machine.name, "machine.name")
            assertEquals(Machine::class.java, machine.javaClass, "machine.javaClass")
            assertEquals("MACHINE", entityType?.name, "entity.entityTypeAnnotation")
        }

        assertEquals(
            Num(1.0),
            machine.getInitialFieldResult("quantity"),
            "machine.quantity",
        )
        verify(
            manufacturingCalculationService,
        ).transformAndCalculate(any(), any(), any(), any(), any(), any(), any(), anyOrNull(), anyOrNull(), isNull(), anyOrNull(), any())
        if (setup.masterDataSelector != null) {
            verify(masterDataServiceMock).getLatestMasterDataByCompositeKey(
                eq(access),
                eq(setup.masterDataSelector),
                eq(MasterDataService.SearchMode.FALLBACK_GLOBAL),
            )
        }
        verifyNoMoreInteractions(nuLedgeService, manufacturingCalculationService, masterDataServiceMock)

        assertThat(capturedTrigger).isInstanceOf(CompositeTriggerAction::class.java)
            .satisfies({
                val triggers = (it as CompositeTriggerAction).extTriggers
                if (beforeAddCustomizer == null) {
                    assertThat(triggers.filterIsInstance<EntityCreation>().size).isEqualTo(expectedChildCount)
                } else {
                    assertThat(triggers.filterIsInstance<EntityCreation>().size).isGreaterThanOrEqualTo(expectedChildCount)
                }
            })

        // run beforeAddCustomizer assertion
        setup.beforeAddCustomizer?.second?.invoke(manufacturing, capturedTrigger)
    }

    @ParameterizedTest
    @MethodSource("createEntityConfigurationsForSteps")
    fun createManufacturingStep(setup: Setup) {
        verifyManufacturingStep(setup) { newEntity ->
            assertEquals(ManualManufacturingStep::class.java, newEntity.javaClass, "newEntity.javaClass")
            assertEquals("NewStep", newEntity.name, "newEntity.name")
            verifyDisplayDesignation(newEntity, "NewStep")
        }
    }

    @ParameterizedTest
    @MethodSource("nuledgeTemplateBased")
    fun createSimpleTemplateBasedManufacturingStep(setup: Setup) {
        knowledgeEntityDto =
            KnowledgeEntityDto(
                id = "id-123",
                key = "template-key-123",
                type = "MANUFACTURING_STEP",
                fields =
                    listOf(
                        FieldParameterDto(
                            "displayDesignation",
                            "Text",
                            value = "My Template Step",
                            denominatorUnit = null,
                        ),
                        FieldParameterDto(
                            "partsPerCycle",
                            "Pieces",
                            value = 4.toBigDecimal(),
                            denominatorUnit = null,
                        ),
                    ),
                parentFields =
                    listOf(
                        FieldParameterDto(
                            "manufacturingStepType",
                            "Text",
                            value = ManufacturingStepType.ASSEMBLY.name,
                            denominatorUnit = null,
                        ),
                    ),
            )
        val expectedChildren = if (setup.insertAtRoot) 1 else 0

        verifyManufacturingStep(setup) { newEntity ->
            assertEquals(KnowledgeManufacturingStep::class.java, newEntity.javaClass, "newEntity.javaClass")
            assertEquals("NewStep", newEntity.name, "newEntity.name")
            verifyDisplayDesignation(newEntity, "NewStep")
            verifyPpc(newEntity, 4.0)
            assertEquals(expectedChildren, newEntity.children.size, "newEntity.children")

            verify(nuLedgeService).getConfigurations(any(), eq("my-nuledge-template"))
        }
    }

    @ParameterizedTest
    @MethodSource("nuledgeTemplateBased")
    fun createRecursiveTemplateBasedManufacturingStep(setup: Setup) {
        knowledgeEntityDto =
            KnowledgeEntityDto(
                id = "id-123",
                key = "template-key-123",
                type = "MANUFACTURING_STEP",
                fields =
                    listOf(
                        FieldParameterDto(
                            "displayDesignation",
                            "Text",
                            value = "My Template Step",
                            denominatorUnit = null,
                        ),
                        FieldParameterDto(
                            "partsPerCycle",
                            "Pieces",
                            value = 4.toBigDecimal(),
                            denominatorUnit = null,
                        ),
                    ),
                children =
                    listOf(
                        KnowledgeEntityDto(
                            id = "id-123-1",
                            key = "xxx-123",
                            type = "LABOR",
                            fields =
                                listOf(
                                    FieldParameterDto(
                                        "displayDesignation",
                                        "Text",
                                        value = "Good Labor",
                                        denominatorUnit = null,
                                    ),
                                ),
                        ),
                    ),
                parentFields =
                    listOf(
                        FieldParameterDto(
                            "manufacturingStepType",
                            "Text",
                            value = ManufacturingStepType.ASSEMBLY.name,
                            denominatorUnit = null,
                        ),
                    ),
            )
        val expectedChildren = if (setup.insertAtRoot) 2 else 1

        verifyManufacturingStep(setup) { newEntity ->
            assertEquals(KnowledgeManufacturingStep::class.java, newEntity.javaClass, "newEntity.javaClass")
            assertEquals("NewStep", newEntity.name, "newEntity.name")
            verifyDisplayDesignation(newEntity, "NewStep")
            verifyPpc(newEntity, 4.0)
            assertEquals(expectedChildren, newEntity.children.size, "newEntity.children")
            val material = newEntity.children.find { it.name == "xxx-123" }!!
            assertEquals("xxx-123", material.name, "newEntity.labor.name")
            assertEquals(
                Text("Good Labor"),
                material.getFieldResult("displayDesignation"),
                "labor.displayDesignation",
            )

            verify(nuLedgeService).getConfigurations(any(), eq("my-nuledge-template"))
        }
    }

    @ParameterizedTest
    @MethodSource("nuledgeTemplateBased")
    fun createRecursiveTemplateBasedManufacturingStepWithInternalMasterData(setup: Setup) {
        val internalMasterKey = MasterDataCompositeKey(type = "MACHINE", key = "internal-material-key", year = 2021, location = null)
        knowledgeEntityDto =
            KnowledgeEntityDto(
                id = "id-123",
                key = "template-key-123",
                type = "MANUFACTURING_STEP",
                fields =
                    listOf(
                        FieldParameterDto(
                            "displayDesignation",
                            "Text",
                            value = "My Template Step",
                            denominatorUnit = null,
                        ),
                        FieldParameterDto(
                            "partsPerCycle",
                            "Pieces",
                            value = 4.toBigDecimal(),
                            denominatorUnit = null,
                        ),
                    ),
                children =
                    listOf(
                        KnowledgeEntityDto(
                            id = "id-123-1",
                            key = "xxx-123",
                            type = "MACHINE",
                            masterDataKey = internalMasterKey,
                        ),
                    ),
                parentFields =
                    listOf(
                        FieldParameterDto(
                            "manufacturingStepType",
                            "Text",
                            value = ManufacturingStepType.ASSEMBLY.name,
                            denominatorUnit = null,
                        ),
                    ),
            )
        val expectedChildren = if (setup.insertAtRoot) 2 else 1

        verifyManufacturingStep(setup) { newEntity ->
            assertEquals(KnowledgeManufacturingStep::class.java, newEntity.javaClass, "newEntity.javaClass")
            assertEquals("NewStep", newEntity.name, "newEntity.name")
            verifyDisplayDesignation(newEntity, "NewStep")
            verifyPpc(newEntity, 4.0)
            assertEquals(expectedChildren, newEntity.children.size, "newEntity.children")
            val material = newEntity.children.find { it.name == "xxx-123" }!!
            assertEquals("xxx-123", material.name, "newEntity.material.name")
            verifyDisplayDesignation(material, "masterData for internal-material-key")

            verify(nuLedgeService).getConfigurations(any(), eq("my-nuledge-template"))
        }
    }

    private fun verifyPpc(
        entity: ManufacturingEntity,
        rate: Double,
    ) {
        val field = entity.getFieldOrInitialFieldResult("partsPerCycle")
        assertEquals(
            QuantityUnit(rate),
            field,
            "entity[${entity.name}].partsPerCycle should be $rate but found $field",
        )
    }

    private fun verifyDisplayDesignation(
        entity: ManufacturingEntity,
        displayDesignationString: String,
    ) {
        val field = entity.getFieldOrInitialFieldResult("displayDesignation")
        assertEquals(
            Text(displayDesignationString),
            field,
            "entity[${entity.name}].displayDesignation should be $displayDesignationString but found: $field",
        )
    }

    private fun verifyManufacturingStep(
        setup: Setup,
        entityChecker: (ManufacturingEntity) -> Unit,
    ) {
        manufacturing =
            run {
                val child = ManufacturingStep("manuStep")
                child._id = parentId
                val manufacturing =
                    createManufacturing(
                        "testEntity",
                        parameters =
                            mapOf(
                                "peakUsableProductionVolumePerYear" to QuantityUnit(100_000.0.toBigDecimal()),
                                "averageUsableProductionVolumePerYear" to QuantityUnit(100_000.0.toBigDecimal()),
                            ),
                    )
                manufacturing.addChild(child)
                manufacturing
            }

        val dto =
            createEntityDto(
                Entities.MANUFACTURING_STEP,
                mapOf(
                    "displayDesignation" to Text("NewStep"),
                    "templateName" to Text(setup.masterDataSelector?.key ?: "").withSource(FieldResult.SOURCE.I),
                ),
                insertRoot = setup.insertAtRoot,
                masterDataSelector = setup.masterDataSelector,
            )
        val entity = service.createAndCalculate(access, dto).block()
        assertNotNull(entity, "entity")
        val oldStep = ManufacturingTreeUtils.findEntityById(manufacturing, parentId)
        assertNotNull(oldStep, "child[step]")
        assertEquals("manuStep", oldStep?.name, "child[step].name")

        val newEntity =
            if (setup.insertAtRoot) {
                // (root) -> NewStep -> manuStep
                assertEquals(0, oldStep!!.children.size, "child.children.size")
                val newEntity = manufacturing.children[0]
                assertTrue(1 <= newEntity.children.size, "newEntity.children.size is at least 1 (oldStep)")
                assertTrue(newEntity.children.contains(oldStep), "newEntity.children contains oldStep")
                newEntity
            } else {
                // (root) -> manuStep -> NewStep
                assertEquals(1, oldStep!!.children.size, "child.children.size")
                val newEntity = oldStep.children[0]
                assertTrue(!newEntity.children.contains(oldStep), "newEntity.children DOES not contain oldStep")
                newEntity
            }

        entityChecker(newEntity)

        verify(manufacturingCalculationService).transformAndCalculate(
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(),
            isNull(),
            any(),
            isNull(),
            isNull(),
            any(),
        )
        verifyNoMoreInteractions(nuLedgeService, manufacturingCalculationService, masterDataServiceMock)
    }
}

private val MODIFY_MAN_AND_ADD_TRIGGER: Pair<BeforeAddCustomizer, BeforeAddCustomizerAssertion> =
    Pair(
// beforeAddCustomizer that modifies a field and adds a new trigger
        { baseManufacturing, _ ->
            Mono.fromCallable {
                Pair(
                    baseManufacturing.apply {
                        replaceOrAddFieldResult(DISPLAY_DESIGNATION) {
                            Text("customized").apply { source = FieldResult.SOURCE.I }
                        }
                    },
                    listOf(TestTriggerAction("myTestTrigger")),
                )
            }
        },
// assertion for the above beforeAddCustomizer
        { baseManufacturing, triggerAction ->
            assertEquals(Text("customized"), baseManufacturing.getFieldResult(DISPLAY_DESIGNATION))
            assertThat(triggerAction).isInstanceOf(CompositeTriggerAction::class.java).satisfies({
                assertThat((it as CompositeTriggerAction).extTriggers + it.triggers)
                    .containsOnlyOnce(TestTriggerAction("myTestTrigger"))
            })
        },
    )

private class TestTriggerAction(val testId: String) : TriggerAction(ObjectId.get(), null) {
    override fun equals(other: Any?): Boolean {
        return other is TestTriggerAction && other.testId == testId
    }
}

private typealias BeforeAddCustomizerAssertion = (BaseManufacturing, TriggerAction) -> Unit
