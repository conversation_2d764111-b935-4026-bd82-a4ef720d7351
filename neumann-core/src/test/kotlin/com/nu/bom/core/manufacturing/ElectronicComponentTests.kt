package com.nu.bom.core.manufacturing

import com.nu.bom.core.CalculationTestBase
import com.nu.bom.core.manufacturing.entities.BaseMaterial
import com.nu.bom.core.manufacturing.entities.DefaultDiscount
import com.nu.bom.core.manufacturing.entities.ElectronicComponent
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Currency
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.ElcoPurchaseVolume
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Part
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.PiecesUnits
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYearsUnit
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.service.nexar.NexarQueryService
import com.nu.bom.core.utils.EntityCalculationVerifier.initMocks
import com.nu.bom.core.utils.EntityCalculationVerifier.mockLookup
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import reactor.core.publisher.Mono
import java.math.BigDecimal

class ElectronicComponentTests : CalculationTestBase() {
    private lateinit var nexarQueryServiceMock: NexarQueryService

    private val mockPart =
        Part(
            id = "123456",
            "",
            "WiFi IC 998877",
            manufacturer = "ABC",
            sellers = emptyList(),
            descriptions = emptyList(),
            mpn = "IC-998877",
            categoryId = 666555,
            medianPrice = null,
        )

    private val mockPartNoCategory =
        Part(
            id = "654321",
            "",
            "WiFi IC 998877",
            manufacturer = "ABC",
            sellers = emptyList(),
            descriptions = emptyList(),
            mpn = "IC-998877",
            categoryId = 111222,
            medianPrice = null,
        )

    private val mockLookup =
        listOf(
            // ids,low,medium,high
            "666555",
            "0.3",
            "0.25",
            "0.2",
        )

    private val mockLookupNoDiscounts =
        listOf(
            // ids,low,medium,high
            "111222",
            "",
            "",
            "",
        )

    @BeforeEach
    fun before() {
        nexarQueryServiceMock = mock()
        whenever(this.calculationContextServiceMock.octoPartService()).thenReturn(nexarQueryServiceMock)
        whenever(nexarQueryServiceMock.getPartById("123456")).thenReturn(Mono.just(mockPart).cache())
        whenever(nexarQueryServiceMock.getPartById("654321")).thenReturn(Mono.just(mockPartNoCategory).cache())
        whenever(nexarQueryServiceMock.getPreferredVendorPrice(mockPart)).thenReturn(Money(1000.0))
        whenever(nexarQueryServiceMock.getPreferredVendorPrice(mockPartNoCategory)).thenReturn(Money(1000.0))
    }

    @Test
    fun calculateWithManualInput() {
        val step =
            addObject(
                name = "elco",
                clazz = ElectronicComponent::class.java,
                fields =
                    mapOf(
                        ElectronicComponent::quantity.name to Pieces(2.0),
                        ElectronicComponent::pricePerUnit.name to Money(10.0),
                        BaseMaterial::dimension.name to Dimension(Dimension.Selection.NUMBER),
                        BaseMaterial::costUnit.name to Text(PiecesUnits.PIECE.name),
                        BaseMaterial::baseCurrency.name to Currency("EUR"),
                    ),
            )
        calculate(step)
        printResults()
        validateResultField("elco", "costPerPart", 20.0)
    }

    @Test
    fun calculateWithNexar() {
        val elco =
            addObject(
                name = "elco",
                clazz = ElectronicComponent::class.java,
                fields =
                    mapOf(
                        ElectronicComponent::quantity.name to Pieces(2.0),
                        ElectronicComponent::externalPartId.name to Text("123456"),
                        BaseMaterial::dimension.name to Dimension(Dimension.Selection.NUMBER),
                        BaseMaterial::costUnit.name to Text(PiecesUnits.PIECE.name),
                        BaseMaterial::baseCurrency.name to Currency("EUR"),
                    ),
            )

        // mock discount lookup
        elco.initMocks(calculationContextServiceMock)
        elco.mockLookup("ElcoDiscount", mockLookup)

        calculate(elco)

        validateResultField("elco", "externalPartId", "123456")
        validateResultField("elco", "displayDesignation", "WiFi IC 998877")
        validateResultField("elco", "mpn", "IC-998877")
        validateResultField("elco", "manufacturer", "ABC")
        validateResultFieldValue("elco", "elcoVolume") {
            assertThat(it).isEqualTo(ElcoPurchaseVolume.HIGH)
        }
        validateResultField("elco", "pricePerUnit", 1000 * 0.2)
        validateResultField("elco", "costPerPart", 200.0 * 2)
    }

    @Test
    fun calculateWithFallbackDiscount() {
        val elco =
            addObject(
                name = "elco",
                clazz = ElectronicComponent::class.java,
                fields =
                    mapOf(
                        ElectronicComponent::quantity.name to Pieces(2.0),
                        ElectronicComponent::externalPartId.name to Text("654321"),
                        ElectronicComponent::discountFactor.name to Num(0.0),
                        BaseMaterial::dimension.name to Dimension(Dimension.Selection.NUMBER),
                        BaseMaterial::costUnit.name to Text(PiecesUnits.PIECE.name),
                        BaseMaterial::baseCurrency.name to Currency("EUR"),
                    ),
            )

        // mock discount lookup
        elco.initMocks(calculationContextServiceMock)
        elco.mockLookup("ElcoDiscount", mockLookupNoDiscounts)

        calculate(elco)

        validateResultField("elco", "externalPartId", "654321")

        // should have default discounts applied
        validateResultFieldValue("elco", "defaultDiscountApplied") {
            assertThat(it).isEqualTo(Bool(true))
        }
        // should have default discount factor HIGH volume
        validateResultField("elco", "discountFactor", DefaultDiscount.HIGH.factor)

        // prices should be calculated with default discount for HIGH volume
        validateResultField("elco", "pricePerUnit", 1000 * DefaultDiscount.HIGH.factor)
        validateResultField("elco", "costPerPart", 1000 * DefaultDiscount.HIGH.factor * 2)
    }

    @Test
    fun calculateWithNexarAndElcoVolume() {
        val elco =
            addObject(
                name = "elco",
                clazz = ElectronicComponent::class.java,
                fields =
                    mapOf(
                        ElectronicComponent::quantity.name to Pieces(2.0),
                        ElectronicComponent::externalPartId.name to Text("123456"),
                        ElectronicComponent::elcoVolume.name to ElcoPurchaseVolume.LOW,
                        BaseMaterial::dimension.name to Dimension(Dimension.Selection.NUMBER),
                        BaseMaterial::costUnit.name to Text(PiecesUnits.PIECE.name),
                        BaseMaterial::baseCurrency.name to Currency("EUR"),
                    ),
            )

        // mock discount lookup
        elco.initMocks(calculationContextServiceMock)
        elco.mockLookup("ElcoDiscount", mockLookup)

        calculate(elco)

        validateResultField("elco", "externalPartId", "123456")
        validateResultField("elco", "displayDesignation", "WiFi IC 998877")
        validateResultField("elco", "mpn", "IC-998877")
        validateResultField("elco", "manufacturer", "ABC")
        validateResultFieldValue("elco", "elcoVolume") {
            assertThat(it).isEqualTo(ElcoPurchaseVolume.LOW)
        }
        validateResultField("elco", "pricePerUnit", 1000.0 * 0.3)
        validateResultField("elco", "costPerPart", 300.0 * 2)
    }

    @Test
    fun calculateWithNexarAndManufacturing() {
        val calculation =
            createManufacturing(
                name = "Manufacturing",
                parameters =
                    mapOf(
                        "location" to Text("Germany"),
                        "materialInterestDays" to TimeInYears(BigDecimal.ZERO, TimeInYearsUnit.YEAR),
                        "materialOverheadRate" to Rate(BigDecimal.ZERO),
                        "callsPerYear" to Num(12.0.toBigDecimal()),
                        "peakUsableProductionVolumePerYear" to QuantityUnit(100_000.0.toBigDecimal()),
                        "shiftsPerDay" to Num(3.0.toBigDecimal()),
                        "procurementType" to ManufacturingType(ManufacturingType.Type.INHOUSE),
                        "lifeTime" to TimeInYears(8.0.toBigDecimal(), TimeInYearsUnit.YEAR),
                        "productionHoursPerYear" to Time(6800.0.toBigDecimal(), TimeUnits.HOUR),
                        "reuseOfScrap" to Bool(false),
                        "elcoPurchaseVolume" to ElcoPurchaseVolume.MEDIUM,
                    ),
            )

        val elco =
            addObject(
                name = "elco",
                clazz = ElectronicComponent::class.java,
                parent = calculation,
                fields =
                    mapOf(
                        ElectronicComponent::quantity.name to Pieces(2.0),
                        ElectronicComponent::externalPartId.name to Text("123456"),
                        BaseMaterial::dimension.name to Dimension(Dimension.Selection.NUMBER),
                        BaseMaterial::costUnit.name to Text(PiecesUnits.PIECE.name),
                        BaseMaterial::baseCurrency.name to Currency("EUR"),
                    ),
            )

        // mock discount lookup
        elco.initMocks(calculationContextServiceMock)
        elco.mockLookup("ElcoDiscount", mockLookup)

        calculate(calculation)

        validateResultField("elco", "externalPartId", "123456")
        validateResultField("elco", "displayDesignation", "WiFi IC 998877")
        validateResultField("elco", "mpn", "IC-998877")
        validateResultField("elco", "manufacturer", "ABC")
        validateResultFieldValue("elco", "elcoVolume") {
            assertThat(it).isEqualTo(ElcoPurchaseVolume.MEDIUM)
        }
        validateResultField("elco", "pricePerUnit", 1000.0 * 0.25)
        validateResultField("elco", "costPerPart", 250.0 * 2)
    }
}
