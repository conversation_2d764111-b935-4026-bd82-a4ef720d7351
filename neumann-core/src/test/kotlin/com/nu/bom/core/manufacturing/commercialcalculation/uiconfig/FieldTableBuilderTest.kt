package com.nu.bom.core.manufacturing.commercialcalculation.uiconfig

import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.TestConfigurationFactory.createInternalTsetDefaultCostConfig
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.UniqueOperationIdentifier
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.InternalConfigurationOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.SumProdOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCostCalculationElementType
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.helper.ColumnCreator
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.helper.LookupColumnCreator
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.helper.ValueColumnCreator
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.OperationConfigurationInformationExtractor.expandOperationNestedWithRowStructure
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.fieldtable.FieldTableBuilder.createFieldTable
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.fieldtable.FieldTableRowStructure
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.fieldtable.OperationWithAdditionalInfo
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.CellFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityLocator
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EqualCriteria
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableColumnDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableRowDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.LocatorType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.LookupCellFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.TableRowDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.ValueCellFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.ValueCellField
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.options.ColumnOptionsFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.options.TableOptionsFeDto
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.lang.IllegalArgumentException

class FieldTableBuilderTest {
    data class RowTestCase(
        val fieldTableRowStructure: FieldTableRowStructure,
        val expectedAllRows: List<OperationWithAdditionalInfo>,
        val additionalFilterOnOperation: (op: InternalConfigurationOperation) -> Boolean = { true },
    ) {
        override fun toString() = fieldTableRowStructure.toString()
    }

    data class ColumnTestCase(
        val columnCreators: List<ColumnCreator>,
        val expectedCells: List<CellFeDto?>,
        val rowIdentifier: UniqueOperationIdentifier,
    ) {
        override fun toString() = columnCreators.toString()
    }

    @Test
    fun `cannot construct an operation with additional section without children`() {
        assertThrows<IllegalArgumentException> {
            OperationWithAdditionalInfo(
                UniqueOperationIdentifier("TestKey", AggregationLevel.SOLD_MATERIAL),
                emptyList(),
                true,
                10,
                false,
            )
        }.let { exception ->
            assertThat(exception.message).contains(
                "'UniqueOperationIdentifier(elementKey=TestKey, aggregationLevel=SOLD_MATERIAL)'" +
                    " should not have an additional section without children!",
            )
        }
    }

    @ParameterizedTest
    @MethodSource("getRowTestCases")
    fun testDifferentRowDefinitions(testCase: RowTestCase) {
        val allRows = expandOperationNestedWithRowStructure(config, testCase.fieldTableRowStructure, testCase.additionalFilterOnOperation)
        Assertions.assertEquals(testCase.expectedAllRows, allRows)
    }

    @ParameterizedTest
    @MethodSource("getColumnTestCases")
    fun testDifferentColumnDefinitions(testCase: ColumnTestCase) {
        Assertions.assertEquals(
            testCase.expectedCells,
            testCase.columnCreators.map { it.defineCell(testCase.rowIdentifier) },
        )
    }

    @Test
    fun testFullTable() {
        // creation
        val structure =
            getFieldTableRowStructureFromConfig(
                entryPoint =
                    UniqueOperationIdentifier(
                        TsetCostCalculationElementType.SPECIAL_DIRECT_COSTS.fieldName,
                        AggregationLevel.SOLD_MATERIAL,
                    ),
                maxDepth = 1,
                includeTopLevel = true,
            )
        val columns =
            listOf(
                ValueColumnCreator(FieldTableColumnDefinitionFeDto("1")) {
                    ValueCellField(
                        name = it.elementKey,
                        value = "Test${it.elementKey}",
                        type = "Text",
                    )
                },
                LookupColumnCreator(
                    FieldTableColumnDefinitionFeDto("2", options = ColumnOptionsFeDto(hasTotal = true, mainColumn = true)),
                    { "Test${it.toFeKey()}" },
                ),
            )

        val tableOptions = TableOptionsFeDto(true)
        val fieldTable =
            createFieldTable(
                structure,
                columns,
                expandOperationNestedWithRowStructure(config, structure) { it.destinationElementKey.contains("And") },
                {
                    if (it.elementKey.contains("-Pp")) {
                        EntityLocator(
                            criteria = listOf(EqualCriteria("level", it.aggregationLevel)),
                            LocatorType.CHILD,
                        )
                    } else {
                        null
                    }
                },
                tableOptions,
            )

        // expectation
        val expectedTopRows =
            listOf(
                "NewSectionPackagingAndCarrierCosts_SoldMaterial",
                "SpecialDirectCosts_SoldMaterial",
            )
        val expectedRowDefinitions =
            mapOf(
                "PackagingAndCarrierCosts-Rm_SoldMaterial" to
                    FieldTableRowDefinitionFeDto(
                        "PackagingAndCarrierCosts-Rm_SoldMaterial",
                        cells =
                            listOf(
                                ValueCellFeDto(
                                    "1",
                                    ValueCellField(
                                        name = "DevelopmentCosts-PackagingAndCarrierCosts-Rm",
                                        value = "TestPackagingAndCarrierCosts-Rm",
                                        type = "Text",
                                    ),
                                ),
                                LookupCellFeDto("2", EntityLocator(type = LocatorType.SELF), "TestPackagingAndCarrierCosts-Rm"),
                            ),
                        rows = null,
                    ),
                "PackagingAndCarrierCosts-Pp_SoldMaterial" to
                    FieldTableRowDefinitionFeDto(
                        "PackagingAndCarrierCosts-Pp_SoldMaterial",
                        cells =
                            listOf(
                                ValueCellFeDto(
                                    "1",
                                    ValueCellField(
                                        name = "DevelopmentCosts-PackagingAndCarrierCosts-Pp",
                                        value = "TestPackagingAndCarrierCosts-Pp",
                                        type = "Text",
                                    ),
                                ),
                                LookupCellFeDto("2", EntityLocator(type = LocatorType.SELF), "TestPackagingAndCarrierCosts-Pp"),
                            ),
                        rows = null,
                        EntityLocator(
                            criteria = listOf(EqualCriteria("level", AggregationLevel.SOLD_MATERIAL)),
                            LocatorType.CHILD,
                        ),
                    ),
                "PackagingAndCarrierCosts-Mfg_SoldMaterial" to
                    FieldTableRowDefinitionFeDto(
                        "PackagingAndCarrierCosts-Mfg_SoldMaterial",
                        cells =
                            listOf(
                                ValueCellFeDto(
                                    "1",
                                    ValueCellField(
                                        name = "DevelopmentCosts-PackagingAndCarrierCosts-Mfg",
                                        value = "TestPackagingAndCarrierCosts-Mfg",
                                        type = "Text",
                                    ),
                                ),
                                LookupCellFeDto("2", EntityLocator(type = LocatorType.SELF), "TestPackagingAndCarrierCosts-Mfg"),
                            ),
                        rows = null,
                    ),
                "PackagingAndCarrierCosts_SoldMaterial" to
                    FieldTableRowDefinitionFeDto(
                        "PackagingAndCarrierCosts_SoldMaterial",
                        cells =
                            listOf(
                                ValueCellFeDto(
                                    "1",
                                    ValueCellField(
                                        name = "PackagingAndCarrierCosts",
                                        value = "TestPackagingAndCarrierCosts",
                                        type = "Text",
                                    ),
                                ),
                                LookupCellFeDto("2", EntityLocator(type = LocatorType.SELF), "TestPackagingAndCarrierCosts"),
                            ),
                        rows = null,
                    ),
                "NewSectionPackagingAndCarrierCosts_SoldMaterial" to
                    FieldTableRowDefinitionFeDto(
                        "NewSectionPackagingAndCarrierCosts_SoldMaterial",
                        cells =
                            listOf(
                                ValueCellFeDto(
                                    "1",
                                    ValueCellField(
                                        name = "PackagingAndCarrierCosts",
                                        value = "TestPackagingAndCarrierCosts",
                                        type = "Text",
                                    ),
                                ),
                            ),
                        rows =
                            listOf(
                                "PackagingAndCarrierCosts-Rm_SoldMaterial",
                                "PackagingAndCarrierCosts-Pp_SoldMaterial",
                                "PackagingAndCarrierCosts-Mfg_SoldMaterial",
                            ),
                    ),
                "SpecialDirectCosts_SoldMaterial" to
                    FieldTableRowDefinitionFeDto(
                        "SpecialDirectCosts_SoldMaterial",
                        cells =
                            listOf(
                                ValueCellFeDto(
                                    "1",
                                    ValueCellField(
                                        name = "SpecialDirectCosts",
                                        value = "TestSpecialDirectCosts",
                                        type = "Text",
                                    ),
                                ),
                                LookupCellFeDto("2", EntityLocator(type = LocatorType.SELF), "TestSpecialDirectCosts"),
                            ),
                        rows = listOf("PackagingAndCarrierCosts_SoldMaterial"),
                    ),
            )
        val expectedColumnDefinitions = columns.map { it.columnDefinition }

        // verification
        Assertions.assertEquals(expectedTopRows, fieldTable.rows)
        Assertions.assertEquals(expectedRowDefinitions.keys, fieldTable.rowDefinitions.keys)
        expectedRowDefinitions.forEach { (key, expectedTable) ->
            Assertions.assertEquals(expectedTable, fieldTable.rowDefinitions[key])
        }
        Assertions.assertEquals(expectedColumnDefinitions, fieldTable.columns)
        Assertions.assertEquals(tableOptions, fieldTable.options)
    }

    @Test
    fun `If sold material level is not allowed the overhead table is empty`() {
        val structure =
            getFieldTableRowStructureFromConfig(
                entryPoint =
                    UniqueOperationIdentifier(
                        TsetCostCalculationElementType.INDIRECT_COSTS_AFTER_PRODUCTION.fieldName,
                        AggregationLevel.SOLD_MATERIAL,
                    ),
                maxDepth = 1,
            )

        val columns =
            listOf(
                LookupColumnCreator(
                    FieldTableColumnDefinitionFeDto("2", options = ColumnOptionsFeDto(hasTotal = true, mainColumn = true)),
                    { "Test${it.toFeKey()}" },
                ),
            )

        val fieldTable =
            createFieldTable(
                structure,
                columns,
                expandOperationNestedWithRowStructure(config, structure) { it.origin != AggregationLevel.SOLD_MATERIAL },
                { null },
                null,
            )

        Assertions.assertEquals(emptyList<String>(), fieldTable.rows)
        Assertions.assertEquals(emptyMap<String, TableRowDefinitionFeDto>(), fieldTable.rowDefinitions)
    }

    companion object {
        private val config = createInternalTsetDefaultCostConfig()

        private fun getFieldTableRowStructureFromConfig(
            entryPoint: UniqueOperationIdentifier,
            maxDepth: Int,
            includeTopLevel: Boolean = true,
            newSection: (operation: InternalConfigurationOperation) -> Boolean = { false },
            isCollapsed: (operation: SumProdOperation) -> Boolean = { false },
            expandBelowMaxDepth: (operation: InternalConfigurationOperation) -> Boolean = { false },
            swapOperation: (operation: InternalConfigurationOperation) -> InternalConfigurationOperation = { it },
        ): FieldTableRowStructure =
            FieldTableRowStructure(
                isCollapsed = isCollapsed,
                newSection = newSection,
                entryPoint = config.getOperation(entryPoint),
                maxDepth = maxDepth,
                includeTopLevel = includeTopLevel,
                expandBelowMaxDepth = expandBelowMaxDepth,
                swapOperation = swapOperation,
            )

        @JvmStatic
        fun getRowTestCases() =
            listOf(
                RowTestCase(
                    getFieldTableRowStructureFromConfig(
                        entryPoint =
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.PROFIT.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                        maxDepth = 1,
                        isCollapsed = { it.isDifferentiatedOverhead },
                    ),
                    listOf(
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("Profit-Rm", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("Profit-Pp", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("Profit-Mfg", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("Profit", AggregationLevel.SOLD_MATERIAL),
                            listOf(
                                UniqueOperationIdentifier("Profit-Rm", AggregationLevel.SOLD_MATERIAL),
                                UniqueOperationIdentifier("Profit-Pp", AggregationLevel.SOLD_MATERIAL),
                                UniqueOperationIdentifier("Profit-Mfg", AggregationLevel.SOLD_MATERIAL),
                            ),
                            false,
                            0,
                            isCollapsed = true,
                        ),
                    ),
                ),
                RowTestCase(
                    getFieldTableRowStructureFromConfig(
                        entryPoint =
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.SPECIAL_DIRECT_COSTS.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                        maxDepth = 1,
                        isCollapsed = { it.destinationElementKey.contains("And") },
                    ),
                    listOf(
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("DevelopmentCosts-Rm", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("DevelopmentCosts-Pp", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("DevelopmentCosts-Mfg", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("DevelopmentCosts", AggregationLevel.SOLD_MATERIAL),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("DevelopmentCosts-Rm", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("DevelopmentCosts-Pp", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("DevelopmentCosts-Mfg", AggregationLevel.SOLD_MATERIAL),
                                ),
                            true,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("RampUpCosts-Rm", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("RampUpCosts-Pp", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("RampUpCosts-Mfg", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("RampUpCosts", AggregationLevel.SOLD_MATERIAL),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("RampUpCosts-Rm", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("RampUpCosts-Pp", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("RampUpCosts-Mfg", AggregationLevel.SOLD_MATERIAL),
                                ),
                            true,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("PackagingAndCarrierCosts-Rm", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("PackagingAndCarrierCosts-Pp", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("PackagingAndCarrierCosts-Mfg", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("PackagingAndCarrierCosts", AggregationLevel.SOLD_MATERIAL),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("PackagingAndCarrierCosts-Rm", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("PackagingAndCarrierCosts-Pp", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("PackagingAndCarrierCosts-Mfg", AggregationLevel.SOLD_MATERIAL),
                                ),
                            true,
                            1,
                            isCollapsed = true,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("SpecialDirectCosts", AggregationLevel.SOLD_MATERIAL),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("DevelopmentCosts", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("RampUpCosts", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("PackagingAndCarrierCosts", AggregationLevel.SOLD_MATERIAL),
                                ),
                            false,
                            0,
                            isCollapsed = false,
                        ),
                    ),
                ),
                RowTestCase(
                    getFieldTableRowStructureFromConfig(
                        entryPoint =
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.SPECIAL_DIRECT_COSTS.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                        maxDepth = 1,
                        expandBelowMaxDepth = { it is SumProdOperation && it.isDifferentiatedOverhead },
                    ),
                    listOf(
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("DevelopmentCosts-Rm", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("DevelopmentCosts-Pp", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("DevelopmentCosts-Mfg", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("DevelopmentCosts", AggregationLevel.SOLD_MATERIAL),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("DevelopmentCosts-Rm", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("DevelopmentCosts-Pp", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("DevelopmentCosts-Mfg", AggregationLevel.SOLD_MATERIAL),
                                ),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("RampUpCosts-Rm", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("RampUpCosts-Pp", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("RampUpCosts-Mfg", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("RampUpCosts", AggregationLevel.SOLD_MATERIAL),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("RampUpCosts-Rm", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("RampUpCosts-Pp", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("RampUpCosts-Mfg", AggregationLevel.SOLD_MATERIAL),
                                ),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("PackagingAndCarrierCosts-Rm", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("PackagingAndCarrierCosts-Pp", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("PackagingAndCarrierCosts-Mfg", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("PackagingAndCarrierCosts", AggregationLevel.SOLD_MATERIAL),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("PackagingAndCarrierCosts-Rm", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("PackagingAndCarrierCosts-Pp", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("PackagingAndCarrierCosts-Mfg", AggregationLevel.SOLD_MATERIAL),
                                ),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("SpecialDirectCosts", AggregationLevel.SOLD_MATERIAL),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("DevelopmentCosts", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("RampUpCosts", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("PackagingAndCarrierCosts", AggregationLevel.SOLD_MATERIAL),
                                ),
                            false,
                            0,
                            isCollapsed = false,
                        ),
                    ),
                ),
                RowTestCase(
                    getFieldTableRowStructureFromConfig(
                        entryPoint =
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.SPECIAL_DIRECT_COSTS.fieldName,
                                AggregationLevel.SOLD_MATERIAL,
                            ),
                        maxDepth = 5,
                        newSection = { it.destinationElementKey.contains("And") },
                    ),
                    listOf(
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("DevelopmentCosts-Rm", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("DevelopmentCosts-Pp", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("DevelopmentCosts-Mfg", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("DevelopmentCosts", AggregationLevel.SOLD_MATERIAL),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("DevelopmentCosts-Rm", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("DevelopmentCosts-Pp", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("DevelopmentCosts-Mfg", AggregationLevel.SOLD_MATERIAL),
                                ),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("RampUpCosts-Rm", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("RampUpCosts-Pp", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("RampUpCosts-Mfg", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("RampUpCosts", AggregationLevel.SOLD_MATERIAL),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("RampUpCosts-Rm", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("RampUpCosts-Pp", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("RampUpCosts-Mfg", AggregationLevel.SOLD_MATERIAL),
                                ),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("PackagingAndCarrierCosts-Rm", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("PackagingAndCarrierCosts-Pp", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("PackagingAndCarrierCosts-Mfg", AggregationLevel.SOLD_MATERIAL),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("PackagingAndCarrierCosts", AggregationLevel.SOLD_MATERIAL),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("PackagingAndCarrierCosts-Rm", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("PackagingAndCarrierCosts-Pp", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("PackagingAndCarrierCosts-Mfg", AggregationLevel.SOLD_MATERIAL),
                                ),
                            true,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("SpecialDirectCosts", AggregationLevel.SOLD_MATERIAL),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("DevelopmentCosts", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("RampUpCosts", AggregationLevel.SOLD_MATERIAL),
                                    UniqueOperationIdentifier("PackagingAndCarrierCosts", AggregationLevel.SOLD_MATERIAL),
                                ),
                            false,
                            0,
                            isCollapsed = false,
                        ),
                    ),
                ),
                RowTestCase(
                    getFieldTableRowStructureFromConfig(
                        entryPoint =
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.MANUFACTURING_COSTS_2.fieldName,
                                AggregationLevel.MANUFACTURING_STEP,
                            ),
                        maxDepth = 1,
                    ),
                    listOf(
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("LaborCosts", AggregationLevel.LABOR),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineDepreciationCosts", AggregationLevel.MACHINE),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineInterestCosts", AggregationLevel.MACHINE),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineAreaCosts", AggregationLevel.MACHINE),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("RoughMachineCosts", AggregationLevel.MACHINE),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineFixCosts", AggregationLevel.MACHINE),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("MachineDepreciationCosts", AggregationLevel.MACHINE),
                                    UniqueOperationIdentifier("MachineInterestCosts", AggregationLevel.MACHINE),
                                    UniqueOperationIdentifier("MachineAreaCosts", AggregationLevel.MACHINE),
                                    UniqueOperationIdentifier("RoughMachineCosts", AggregationLevel.MACHINE),
                                ),
                            true,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineEnergyCosts", AggregationLevel.MACHINE),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineMaintenanceCosts", AggregationLevel.MACHINE),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineOperationSupplyCosts", AggregationLevel.MACHINE),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineVariableCosts", AggregationLevel.MACHINE),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("MachineEnergyCosts", AggregationLevel.MACHINE),
                                    UniqueOperationIdentifier("MachineMaintenanceCosts", AggregationLevel.MACHINE),
                                    UniqueOperationIdentifier("MachineOperationSupplyCosts", AggregationLevel.MACHINE),
                                ),
                            true,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineCosts", AggregationLevel.MACHINE),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("MachineFixCosts", AggregationLevel.MACHINE),
                                    UniqueOperationIdentifier("MachineVariableCosts", AggregationLevel.MACHINE),
                                ),
                            true,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("ToolMaintenanceCosts", AggregationLevel.TOOL),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("RoughProcessCosts", AggregationLevel.ROUGH_PROCESS),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("DirectManufacturingCosts", AggregationLevel.MANUFACTURING_STEP),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("LaborCosts", AggregationLevel.LABOR),
                                    UniqueOperationIdentifier("MachineCosts", AggregationLevel.MACHINE),
                                    UniqueOperationIdentifier("ToolMaintenanceCosts", AggregationLevel.TOOL),
                                    UniqueOperationIdentifier("RoughProcessCosts", AggregationLevel.ROUGH_PROCESS),
                                ),
                            true,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("ManufacturingScrapCosts", AggregationLevel.MANUFACTURING_STEP),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("ManufacturingScrapCosts", AggregationLevel.MATERIAL_USAGE),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("ManufacturingOverheadCosts", AggregationLevel.MANUFACTURING_STEP),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("ToolAllocationCosts", AggregationLevel.TOOL),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("ToolInterestCosts", AggregationLevel.TOOL),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier(
                                "ManufacturingCostsAfterDirectManufacturingCosts",
                                AggregationLevel.MANUFACTURING_STEP,
                            ),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("ManufacturingScrapCosts", AggregationLevel.MANUFACTURING_STEP),
                                    UniqueOperationIdentifier("ManufacturingScrapCosts", AggregationLevel.MATERIAL_USAGE),
                                    UniqueOperationIdentifier("ManufacturingOverheadCosts", AggregationLevel.MANUFACTURING_STEP),
                                    UniqueOperationIdentifier("ToolAllocationCosts", AggregationLevel.TOOL),
                                    UniqueOperationIdentifier("ToolInterestCosts", AggregationLevel.TOOL),
                                ),
                            true,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("ManufacturingCosts2", AggregationLevel.MANUFACTURING_STEP),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("DirectManufacturingCosts", AggregationLevel.MANUFACTURING_STEP),
                                    UniqueOperationIdentifier(
                                        "ManufacturingCostsAfterDirectManufacturingCosts",
                                        AggregationLevel.MANUFACTURING_STEP,
                                    ),
                                ),
                            false,
                            0,
                            isCollapsed = false,
                        ),
                    ),
                ),
                RowTestCase(
                    getFieldTableRowStructureFromConfig(
                        entryPoint =
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.MANUFACTURING_COSTS_2.fieldName,
                                AggregationLevel.MANUFACTURING_STEP,
                            ),
                        maxDepth = 2,
                    ),
                    listOf(
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("LaborCosts", AggregationLevel.LABOR),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineDepreciationCosts", AggregationLevel.MACHINE),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineInterestCosts", AggregationLevel.MACHINE),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineAreaCosts", AggregationLevel.MACHINE),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("RoughMachineCosts", AggregationLevel.MACHINE),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineFixCosts", AggregationLevel.MACHINE),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("MachineDepreciationCosts", AggregationLevel.MACHINE),
                                    UniqueOperationIdentifier("MachineInterestCosts", AggregationLevel.MACHINE),
                                    UniqueOperationIdentifier("MachineAreaCosts", AggregationLevel.MACHINE),
                                    UniqueOperationIdentifier("RoughMachineCosts", AggregationLevel.MACHINE),
                                ),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineEnergyCosts", AggregationLevel.MACHINE),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineMaintenanceCosts", AggregationLevel.MACHINE),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineOperationSupplyCosts", AggregationLevel.MACHINE),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineVariableCosts", AggregationLevel.MACHINE),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("MachineEnergyCosts", AggregationLevel.MACHINE),
                                    UniqueOperationIdentifier("MachineMaintenanceCosts", AggregationLevel.MACHINE),
                                    UniqueOperationIdentifier("MachineOperationSupplyCosts", AggregationLevel.MACHINE),
                                ),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineCosts", AggregationLevel.MACHINE),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("MachineFixCosts", AggregationLevel.MACHINE),
                                    UniqueOperationIdentifier("MachineVariableCosts", AggregationLevel.MACHINE),
                                ),
                            true,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("ToolMaintenanceCosts", AggregationLevel.TOOL),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("RoughProcessCosts", AggregationLevel.ROUGH_PROCESS),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("DirectManufacturingCosts", AggregationLevel.MANUFACTURING_STEP),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("LaborCosts", AggregationLevel.LABOR),
                                    UniqueOperationIdentifier("MachineCosts", AggregationLevel.MACHINE),
                                    UniqueOperationIdentifier("ToolMaintenanceCosts", AggregationLevel.TOOL),
                                    UniqueOperationIdentifier("RoughProcessCosts", AggregationLevel.ROUGH_PROCESS),
                                ),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("ManufacturingScrapCosts", AggregationLevel.MANUFACTURING_STEP),
                            childrenIds = emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("ManufacturingScrapCosts", AggregationLevel.MATERIAL_USAGE),
                            childrenIds = emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("ManufacturingOverheadCosts", AggregationLevel.MANUFACTURING_STEP),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("ToolAllocationCosts", AggregationLevel.TOOL),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("ToolInterestCosts", AggregationLevel.TOOL),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier(
                                "ManufacturingCostsAfterDirectManufacturingCosts",
                                AggregationLevel.MANUFACTURING_STEP,
                            ),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("ManufacturingScrapCosts", AggregationLevel.MANUFACTURING_STEP),
                                    UniqueOperationIdentifier("ManufacturingScrapCosts", AggregationLevel.MATERIAL_USAGE),
                                    UniqueOperationIdentifier("ManufacturingOverheadCosts", AggregationLevel.MANUFACTURING_STEP),
                                    UniqueOperationIdentifier("ToolAllocationCosts", AggregationLevel.TOOL),
                                    UniqueOperationIdentifier("ToolInterestCosts", AggregationLevel.TOOL),
                                ),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("ManufacturingCosts2", AggregationLevel.MANUFACTURING_STEP),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("DirectManufacturingCosts", AggregationLevel.MANUFACTURING_STEP),
                                    UniqueOperationIdentifier(
                                        "ManufacturingCostsAfterDirectManufacturingCosts",
                                        AggregationLevel.MANUFACTURING_STEP,
                                    ),
                                ),
                            false,
                            0,
                            isCollapsed = false,
                        ),
                    ),
                ),
                RowTestCase(
                    getFieldTableRowStructureFromConfig(
                        entryPoint =
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.MANUFACTURING_COSTS_2.fieldName,
                                AggregationLevel.MANUFACTURING_STEP,
                            ),
                        maxDepth = 3,
                    ),
                    listOf(
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("LaborCosts", AggregationLevel.LABOR),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineDepreciationCosts", AggregationLevel.MACHINE),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineInterestCosts", AggregationLevel.MACHINE),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineAreaCosts", AggregationLevel.MACHINE),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("RoughMachineCosts", AggregationLevel.MACHINE),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineFixCosts", AggregationLevel.MACHINE),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("MachineDepreciationCosts", AggregationLevel.MACHINE),
                                    UniqueOperationIdentifier("MachineInterestCosts", AggregationLevel.MACHINE),
                                    UniqueOperationIdentifier("MachineAreaCosts", AggregationLevel.MACHINE),
                                    UniqueOperationIdentifier("RoughMachineCosts", AggregationLevel.MACHINE),
                                ),
                            true,
                            3,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineEnergyCosts", AggregationLevel.MACHINE),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineMaintenanceCosts", AggregationLevel.MACHINE),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineOperationSupplyCosts", AggregationLevel.MACHINE),
                            emptyList(),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineVariableCosts", AggregationLevel.MACHINE),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("MachineEnergyCosts", AggregationLevel.MACHINE),
                                    UniqueOperationIdentifier("MachineMaintenanceCosts", AggregationLevel.MACHINE),
                                    UniqueOperationIdentifier("MachineOperationSupplyCosts", AggregationLevel.MACHINE),
                                ),
                            true,
                            3,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("MachineCosts", AggregationLevel.MACHINE),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("MachineFixCosts", AggregationLevel.MACHINE),
                                    UniqueOperationIdentifier("MachineVariableCosts", AggregationLevel.MACHINE),
                                ),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("ToolMaintenanceCosts", AggregationLevel.TOOL),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("RoughProcessCosts", AggregationLevel.ROUGH_PROCESS),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("DirectManufacturingCosts", AggregationLevel.MANUFACTURING_STEP),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("LaborCosts", AggregationLevel.LABOR),
                                    UniqueOperationIdentifier("MachineCosts", AggregationLevel.MACHINE),
                                    UniqueOperationIdentifier("ToolMaintenanceCosts", AggregationLevel.TOOL),
                                    UniqueOperationIdentifier("RoughProcessCosts", AggregationLevel.ROUGH_PROCESS),
                                ),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("ManufacturingScrapCosts", AggregationLevel.MANUFACTURING_STEP),
                            childrenIds = emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("ManufacturingScrapCosts", AggregationLevel.MATERIAL_USAGE),
                            childrenIds = emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("ManufacturingOverheadCosts", AggregationLevel.MANUFACTURING_STEP),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("ToolAllocationCosts", AggregationLevel.TOOL),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("ToolInterestCosts", AggregationLevel.TOOL),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier(
                                "ManufacturingCostsAfterDirectManufacturingCosts",
                                AggregationLevel.MANUFACTURING_STEP,
                            ),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("ManufacturingScrapCosts", AggregationLevel.MANUFACTURING_STEP),
                                    UniqueOperationIdentifier("ManufacturingScrapCosts", AggregationLevel.MATERIAL_USAGE),
                                    UniqueOperationIdentifier("ManufacturingOverheadCosts", AggregationLevel.MANUFACTURING_STEP),
                                    UniqueOperationIdentifier("ToolAllocationCosts", AggregationLevel.TOOL),
                                    UniqueOperationIdentifier("ToolInterestCosts", AggregationLevel.TOOL),
                                ),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("ManufacturingCosts2", AggregationLevel.MANUFACTURING_STEP),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("DirectManufacturingCosts", AggregationLevel.MANUFACTURING_STEP),
                                    UniqueOperationIdentifier(
                                        "ManufacturingCostsAfterDirectManufacturingCosts",
                                        AggregationLevel.MANUFACTURING_STEP,
                                    ),
                                ),
                            false,
                            0,
                            isCollapsed = false,
                        ),
                    ),
                ),
                RowTestCase(
                    getFieldTableRowStructureFromConfig(
                        entryPoint =
                            UniqueOperationIdentifier(
                                TsetCostCalculationElementType.MANUFACTURING_COSTS_2.fieldName,
                                AggregationLevel.MANUFACTURING_STEP,
                            ),
                        maxDepth = 3,
                    ),
                    listOf(
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("LaborCosts", AggregationLevel.LABOR),
                            emptyList(),
                            false,
                            2,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("DirectManufacturingCosts", AggregationLevel.MANUFACTURING_STEP),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("LaborCosts", AggregationLevel.LABOR),
                                ),
                            false,
                            1,
                            isCollapsed = false,
                        ),
                        OperationWithAdditionalInfo(
                            UniqueOperationIdentifier("ManufacturingCosts2", AggregationLevel.MANUFACTURING_STEP),
                            childrenIds =
                                listOf(
                                    UniqueOperationIdentifier("DirectManufacturingCosts", AggregationLevel.MANUFACTURING_STEP),
                                ),
                            false,
                            0,
                            isCollapsed = false,
                        ),
                    ),
                ) { it.origin == AggregationLevel.LABOR },
            )

        @JvmStatic
        fun getColumnTestCases() =
            listOf(
                ColumnTestCase(emptyList(), emptyList(), UniqueOperationIdentifier("Any", AggregationLevel.SOLD_MATERIAL)),
                ColumnTestCase(
                    listOf(
                        ValueColumnCreator(FieldTableColumnDefinitionFeDto("1")) {
                            ValueCellField(
                                name = it.elementKey,
                                value = "Test${it.toFeKey()}",
                                type = "Text",
                            )
                        },
                        LookupColumnCreator(FieldTableColumnDefinitionFeDto("2"), { "TestField${it.elementKey}" }),
                        LookupColumnCreator(
                            FieldTableColumnDefinitionFeDto("2"),
                            { "TestField10${it.elementKey}" },
                            {
                                EntityLocator(
                                    type = LocatorType.CHILD,
                                    criteria =
                                        listOf(
                                            EqualCriteria("key", "value"),
                                        ),
                                )
                            },
                        ),
                    ),
                    listOf(
                        ValueCellFeDto(
                            "1",
                            ValueCellField(
                                name = "RowId",
                                value = "TestRowId_SoldMaterial",
                                type = "Text",
                            ),
                        ),
                        LookupCellFeDto("2", EntityLocator(type = LocatorType.SELF), "TestFieldRowId"),
                        LookupCellFeDto(
                            "2",
                            EntityLocator(
                                type = LocatorType.CHILD,
                                criteria =
                                    listOf(
                                        EqualCriteria("key", "value"),
                                    ),
                            ),
                            "TestField10RowId",
                        ),
                    ),
                    UniqueOperationIdentifier("RowId", AggregationLevel.SOLD_MATERIAL),
                ),
                ColumnTestCase(
                    listOf(
                        LookupColumnCreator(FieldTableColumnDefinitionFeDto("2"), { null }),
                    ),
                    listOf(null),
                    UniqueOperationIdentifier("RowId", AggregationLevel.SOLD_MATERIAL),
                ),
            )
    }
}
