package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.entities.BaseMaterial
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.model.MasterDataSelector
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import org.assertj.core.api.Assertions.assertThat
import org.bson.types.ObjectId
import org.junit.jupiter.api.Test

class ElectronicComponentMigrationTest {
    @Test
    fun shouldMigrateHeaderKey() {
        val headerKey = "ic-s-9-s-08-sg-16-e-1-mtg-ssop-16"
        val entity =
            createElcoEntity(
                headerKey = headerKey,
            )
        val migratedEntity = ElectronicComponentMigration().map(entity)
        assertThat(migratedEntity.initialFieldWithResults.size).isEqualTo(3)
        assertThat(migratedEntity.fieldWithResults.size).isEqualTo(3)

        assertMasterDataTypeIsSetToNone(migratedEntity.initialFieldWithResults)
        assertMasterDataTypeIsSetToNone(migratedEntity.fieldWithResults)

        assertHeaderKeyExists(
            fieldWithResults = migratedEntity.fieldWithResults,
            expectedheaderKey = headerKey,
        )
        assertHeaderKeyExists(
            fieldWithResults = migratedEntity.initialFieldWithResults,
            expectedheaderKey = headerKey,
        )
    }

    @Test
    fun shouldMigrateEmptyHeaderKey() {
        val entity =
            createElcoEntity(
                headerKey = null,
            )
        val migratedEntity = ElectronicComponentMigration().map(entity)
        assertThat(migratedEntity.initialFieldWithResults.size).isEqualTo(4)
        assertThat(migratedEntity.fieldWithResults.size).isEqualTo(3)

        assertMasterDataTypeIsSetToNone(migratedEntity.initialFieldWithResults)
        assertMasterDataTypeIsSetToNone(migratedEntity.fieldWithResults)

        assertEmptyHeaderKeyExists(
            fieldWithResults = migratedEntity.fieldWithResults,
        )
        assertEmptyHeaderKeyExists(
            fieldWithResults = migratedEntity.initialFieldWithResults,
        )
        assertEmissionIsSetTo0(
            fieldWithResults = migratedEntity.initialFieldWithResults,
        )
    }

    private fun assertMasterDataTypeIsSetToNone(fieldWithResults: Map<String, FieldResultModel>) {
        val type = fieldWithResults["masterDataType"]
        assertThat(type).isNotNull
        assertThat(type?.type).isEqualTo(Text::class.simpleName)
        assertThat(type?.value).isInstanceOf(Text::class.java)
        assertThat(type?.value).isEqualTo(Text(MasterDataType.NONE.name))

        val internalType = fieldWithResults["masterDataTypeInternal"]
        assertThat(internalType).isNotNull
        assertThat(internalType?.type).isEqualTo(Text::class.simpleName)
        assertThat(internalType?.value).isInstanceOf(Text::class.java)
        assertThat(internalType?.value).isEqualTo(Text(MasterDataType.NONE.name))
    }

    private fun assertHeaderKeyExists(
        fieldWithResults: Map<String, FieldResultModel>,
        expectedheaderKey: String,
    ) {
        val headerKey = fieldWithResults[BaseMaterial::headerKey.name]
        assertThat(headerKey).isNotNull
        assertThat(headerKey?.type).isEqualTo(Text::class.simpleName)
        assertThat(headerKey?.value).isInstanceOf(Text::class.java)
        assertThat(headerKey?.value).isEqualTo(Text(expectedheaderKey))
    }

    private fun assertEmptyHeaderKeyExists(fieldWithResults: Map<String, FieldResultModel>) {
        val headerKey = fieldWithResults[BaseMaterial::headerKey.name]
        assertThat(headerKey).isNotNull
        assertThat(headerKey?.type).isEqualTo(Text::class.simpleName)
        assertThat(headerKey?.value).isNull()
    }

    private fun assertEmissionIsSetTo0(fieldWithResults: Map<String, FieldResultModel>) {
        val cO2PerUnit = fieldWithResults[BaseMaterial::cO2PerUnit.name]
        assertThat(cO2PerUnit).isNotNull
        assertThat(cO2PerUnit?.type).isEqualTo(Emission::class.simpleName)
        assertThat((cO2PerUnit?.value as Weight).res).isEqualByComparingTo(0.0.toBigDecimal())
    }

    private fun createElcoEntity(headerKey: String?): ManufacturingModelEntity {
        val mdTypeField =
            FieldResultModel(
                version = 1,
                newVersion = 1,
                type = Text::class.java.simpleName,
                value = Text("ANY_MASTERDATA_TYPE"),
            )

        val fields =
            mapOf(
                "masterDataType" to mdTypeField,
                "masterDataTypeInternal" to mdTypeField,
            )

        return ManufacturingModelEntity(
            id = ObjectId.get(),
            name = "entity-1",
            type = Entities.C_PART.name,
            clazz = Entities.C_PART.clazz!!.simpleName!!,
            args = emptyMap(),
            fieldWithResults = fields,
            initialFieldWithResults = fields,
            children = emptyList(),
        ).apply {
            headerKey?.let {
                masterDataSelector =
                    MasterDataSelector(
                        type = MasterDataType.NONE,
                        key = it,
                        year = 2025,
                        location = "Austria",
                    )
            }
        }
    }
}
