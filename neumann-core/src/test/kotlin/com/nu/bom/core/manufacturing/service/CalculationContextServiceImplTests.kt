package com.nu.bom.core.manufacturing.service

import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.extension.classificationcalculator.ClassificationCalculatorService
import com.nu.bom.core.manufacturing.factories.FieldFactoryService
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.utils.InjectionUtilsService
import com.nu.bom.core.milldrill.MillingDrillingCalculationService
import com.nu.bom.core.model.MasterData
import com.nu.bom.core.model.MasterDataSelector
import com.nu.bom.core.model.Template
import com.nu.bom.core.model.TemplateEntity
import com.nu.bom.core.prediction.PredictionServiceApi
import com.nu.bom.core.service.LookupService
import com.nu.bom.core.service.ManufacturingEntityFactoryService
import com.nu.bom.core.service.MasterDataService
import com.nu.bom.core.service.MasterDataTypeService
import com.nu.bom.core.service.MaterialService
import com.nu.bom.core.service.PartService
import com.nu.bom.core.service.TemplateService
import com.nu.bom.core.service.bomrads.BomradsFileUploadService
import com.nu.bom.core.service.file.SecureFileService
import com.nu.bom.core.service.fti.FtiService
import com.nu.bom.core.service.nexar.NexarQueryService
import com.nu.bom.core.service.nuledge.NuLedgeService
import com.nu.bom.core.smf.FriedelNestingApiService
import com.nu.bom.core.smf.SmfUnfoldedPartScalingService
import com.nu.bom.core.smf.WsiApiService
import com.nu.bom.core.tsetdel.TsetDelFileLocationService
import com.nu.bom.core.turn.InductiveHardeningService
import com.nu.bom.core.turn.TurningCalculationService
import com.nu.bom.core.turn.TurningService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.ShapesData
import org.assertj.core.api.Assertions.assertThat
import org.bson.types.ObjectId
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.mockito.Mock
import org.mockito.MockitoAnnotations.openMocks
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.eq
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import reactor.kotlin.core.publisher.toMono
import reactor.test.StepVerifier

private const val NEW_MASTERDATA_HEADER_KEY_FIELD_NAME = "headerKey"
private const val DISPLAY_DESIGNATION_FIELD_NAME = "displayDesignation"

class CalculationContextServiceImplTests {
    @Mock
    private lateinit var manufacturingEntityFactoryService: ManufacturingEntityFactoryService

    @Mock
    private lateinit var masterDataService: MasterDataService

    @Mock
    private lateinit var lookupService: LookupService

    @Mock
    private lateinit var predictionService: PredictionServiceApi

    @Mock
    private lateinit var templateService: TemplateService

    @Mock
    private lateinit var nuLedgeService: NuLedgeService

    @Mock
    private lateinit var masterDataTypeService: MasterDataTypeService

    @Mock
    private lateinit var shapesData: ShapesData

    @Mock
    private lateinit var fieldFactoryService: FieldFactoryService

    @Mock
    private lateinit var turningCalculationService: TurningCalculationService

    @Mock
    private lateinit var wsiApiService: WsiApiService

    @Mock
    private lateinit var inductiveHardeningService: InductiveHardeningService

    @Mock
    private lateinit var millingDrillingCalculationService: MillingDrillingCalculationService

    @Mock
    private lateinit var classificationCalculatorServiceImpl: ClassificationCalculatorService

    @Mock
    private lateinit var nexarQueryService: NexarQueryService

    @Mock
    private lateinit var smfWsiUnfolderScalingService: SmfUnfoldedPartScalingService

    @Mock
    private lateinit var fileService: SecureFileService

    @Mock
    private lateinit var partService: PartService

    @Mock
    private lateinit var tsetDelFileLocationService: TsetDelFileLocationService

    @Mock
    private lateinit var injectionUtilsService: InjectionUtilsService

    @Mock
    private lateinit var configurationManagementService: ConfigurationManagementService

    @Mock
    private lateinit var bomradsFileUploadService: BomradsFileUploadService

    @Mock
    private lateinit var friedelNestingService: FriedelNestingApiService

    @Mock
    private lateinit var ftiService: FtiService

    @Mock
    private lateinit var turningService: TurningService

    @Mock
    private lateinit var materialService: MaterialService

    @Mock
    private lateinit var accessCheck: AccessCheck

    @Mock
    private lateinit var manufacturingEntity: ManufacturingEntity

    private lateinit var service: CalculationContextServiceImpl

    private lateinit var mocks: AutoCloseable

    @BeforeEach
    fun setup() {
        mocks = openMocks(this)

        service =
            CalculationContextServiceImpl(
                manufacturingEntityFactoryService = manufacturingEntityFactoryService,
                masterDataService = masterDataService,
                lookupService = lookupService,
                predictionService = predictionService,
                templateService = templateService,
                nuLedgeService = nuLedgeService,
                masterDataTypeService = masterDataTypeService,
                shapesData = shapesData,
                fieldFactoryService = fieldFactoryService,
                turningCalculationService = turningCalculationService,
                wsiApiService = wsiApiService,
                inductiveHardeningService = inductiveHardeningService,
                millingDrillingCalculationService = millingDrillingCalculationService,
                classificationCalculatorServiceImpl = classificationCalculatorServiceImpl,
                nexarQueryService = nexarQueryService,
                smfWsiUnfolderScalingService = smfWsiUnfolderScalingService,
                fileService = fileService,
                partService = partService,
                tsetDelFileLocationService = tsetDelFileLocationService,
                injectionUtilsService = injectionUtilsService,
                configurationManagementService = configurationManagementService,
                bomradsFileUploadService = bomradsFileUploadService,
                friedelNestingService = friedelNestingService,
                ftiService = ftiService,
                turningService = turningService,
                materialService = materialService,
            )

        whenever(
            manufacturingEntityFactoryService.createEntity(
                name = any(),
                entityType = any(),
                clazz = anyOrNull(),
                args = any(),
                masterDataSelector = anyOrNull(),
                masterDataObjectId = anyOrNull(),
                masterDataVersion = anyOrNull(),
                fields = any(),
                version = any(),
                entityRef = anyOrNull(),
                entityId = anyOrNull(),
                overwrites = any(),
                oldFieldWithResults = any(),
                entityRefsToOverride = any(),
                isolated = any(),
            ),
        ).thenReturn(manufacturingEntity)
    }

    @AfterEach
    fun teardown() {
        mocks.close()
    }

    @ParameterizedTest
    @EnumSource(names = ["CONSUMABLE"], mode = EnumSource.Mode.INCLUDE)
    fun `creates new MD template entity for`(entityType: Entities) {
        val templateName = "my-template"
        val entityName = "my-entity"
        val masterDataKey = "my-masterdata-key"

        mockSingleEntityTemplateResponse(
            templateName = templateName,
            entityName = entityName,
            entityType = entityType,
            masterDataKey = masterDataKey,
            masterDataType = null,
        )

        StepVerifier.create(
            service.createEntitiesFromTemplate(
                accessCheck = accessCheck,
                name = templateName,
                location = "",
                year = 0,
                version = 0,
            ),
        ).expectNextCount(1).verifyComplete()

        val fieldCaptor = argumentCaptor<Map<String, FieldResultStar>>()

        verify(manufacturingEntityFactoryService).createEntity(
            name = eq(entityName),
            entityType = eq(entityType),
            clazz = anyOrNull(),
            args = any(),
            masterDataSelector = eq(null),
            masterDataObjectId = eq(null),
            masterDataVersion = eq(null),
            fields = fieldCaptor.capture(),
            version = any(),
            entityRef = anyOrNull(),
            entityId = anyOrNull(),
            overwrites = any(),
            oldFieldWithResults = any(),
            entityRefsToOverride = any(),
            isolated = any(),
        )

        val fields = fieldCaptor.firstValue

        assertFieldExists(fields, NEW_MASTERDATA_HEADER_KEY_FIELD_NAME, Text(masterDataKey), FieldResult.SOURCE.I)
        assertFieldExists(fields, DISPLAY_DESIGNATION_FIELD_NAME, Text(entityName), FieldResult.SOURCE.I)
        assertThat(fields).hasSize(2)
    }

    @ParameterizedTest
    @EnumSource(names = ["MACHINE", "TOOL"], mode = EnumSource.Mode.INCLUDE)
    fun `creates old MD template entity for`(entityType: Entities) {
        val templateName = "my-template"
        val entityName = "my-entity"
        val masterDataKey = "my-masterdata-key"
        val masterDataType = MasterDataType.valueOf(entityType.name)

        mockSingleEntityTemplateResponse(
            templateName = templateName,
            entityName = entityName,
            entityType = entityType,
            masterDataKey = masterDataKey,
            masterDataType = masterDataType,
        )

        val masterDataId = ObjectId.get()
        val expectedMasterDataSelector = MasterDataSelector.fromMdkey(masterDataKey, masterDataType)
        val masterDataFieldKeyToValue = "my-masterdata-field" to Text("my-masterdata-field-value")

        mockMasterDataResponse(
            masterDataId,
            expectedMasterDataSelector,
            mapOf(masterDataFieldKeyToValue),
        )

        StepVerifier.create(
            service.createEntitiesFromTemplate(
                accessCheck = accessCheck,
                name = templateName,
                location = "",
                year = 0,
                version = 0,
            ),
        ).expectNextCount(1).verifyComplete()

        val fieldCaptor = argumentCaptor<Map<String, FieldResultStar>>()

        verify(manufacturingEntityFactoryService).createEntity(
            name = eq(entityName),
            entityType = eq(entityType),
            clazz = anyOrNull(),
            args = any(),
            masterDataSelector = eq(expectedMasterDataSelector),
            masterDataObjectId = eq(masterDataId),
            masterDataVersion = eq(0),
            fields = fieldCaptor.capture(),
            version = any(),
            entityRef = anyOrNull(),
            entityId = anyOrNull(),
            overwrites = any(),
            oldFieldWithResults = any(),
            entityRefsToOverride = any(),
            isolated = any(),
        )

        val fields = fieldCaptor.firstValue
        assertFieldExists(fields, DISPLAY_DESIGNATION_FIELD_NAME, Text(entityName), FieldResult.SOURCE.I)
        assertFieldExists(fields, masterDataFieldKeyToValue.first, masterDataFieldKeyToValue.second, FieldResult.SOURCE.M)

        assertThat(fields).hasSize(2)
    }

    @ParameterizedTest
    @EnumSource(names = ["LABOR", "SETUP"], mode = EnumSource.Mode.INCLUDE)
    fun `creates non-MD template entity for`(entityType: Entities) {
        val templateName = "my-template"
        val entityName = "my-entity"

        mockSingleEntityTemplateResponse(
            templateName = templateName,
            entityName = entityName,
            entityType = entityType,
            masterDataKey = null,
            masterDataType = null,
        )

        StepVerifier.create(
            service.createEntitiesFromTemplate(
                accessCheck = accessCheck,
                name = templateName,
                location = "",
                year = 0,
                version = 0,
            ),
        ).expectNextCount(1).verifyComplete()

        val fieldCaptor = argumentCaptor<Map<String, FieldResultStar>>()

        verify(manufacturingEntityFactoryService).createEntity(
            name = eq(entityName),
            entityType = eq(entityType),
            clazz = anyOrNull(),
            args = any(),
            masterDataSelector = eq(null),
            masterDataObjectId = eq(null),
            masterDataVersion = eq(null),
            fields = fieldCaptor.capture(),
            version = any(),
            entityRef = anyOrNull(),
            entityId = anyOrNull(),
            overwrites = any(),
            oldFieldWithResults = any(),
            entityRefsToOverride = any(),
            isolated = any(),
        )

        val fields = fieldCaptor.firstValue

        assertFieldExists(fields, DISPLAY_DESIGNATION_FIELD_NAME, Text(entityName), FieldResult.SOURCE.I)
        assertThat(fields).hasSize(1)
    }

    private fun assertFieldExists(
        fields: Map<String, FieldResultStar>,
        key: String,
        expectedValue: FieldResultStar,
        source: FieldResult.SOURCE,
    ) {
        val field = fields[key]
        assertThat(field).isEqualTo(expectedValue)
        assertThat(field!!.source).isEqualTo(source)
    }

    private fun mockMasterDataResponse(
        id: ObjectId,
        selector: MasterDataSelector,
        data: Map<String, FieldResultStar> = emptyMap(),
    ) {
        val masterData =
            MasterData(
                selector = selector,
                data = data,
            ).apply { _id = id }

        whenever(masterDataService.getLatestMasterDataByCompositeKey(accessCheck, selector))
            .thenReturn(masterData.toMono())
    }

    private fun mockSingleEntityTemplateResponse(
        templateName: String,
        entityName: String,
        entityType: Entities,
        masterDataKey: String?,
        masterDataType: MasterDataType?,
    ) {
        val template =
            Template(
                name = templateName,
                entities =
                    listOf(
                        TemplateEntity(
                            name = entityName,
                            type = entityType,
                            clazz = null,
                            masterDataKey = masterDataKey,
                            masterDataType = masterDataType,
                        ),
                    ),
            )

        whenever(templateService.getTemplate(templateName))
            .thenReturn(template.toMono())
    }
}
