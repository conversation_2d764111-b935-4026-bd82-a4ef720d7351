package com.nu.bom.core.service

import com.nu.bom.core.api.dtos.BomNodeDto
import com.nu.bom.core.api.dtos.BranchDto
import com.nu.bom.core.api.dtos.FieldConversionService
import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.api.dtos.KeyPerformanceIndicatorApiDto
import com.nu.bom.core.api.dtos.ManufacturingDto
import com.nu.bom.core.controller.CalculationResultDto
import com.nu.bom.core.controller.CalculationResultWithSnapshot
import com.nu.bom.core.exception.userException.AlreadyPartOfManufacturingLineException
import com.nu.bom.core.exception.userException.ExternalManufacturingLineException
import com.nu.bom.core.exception.userException.ManufacturingEntityNotFoundException
import com.nu.bom.core.exception.userException.StepsNotConsecutiveException
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.BomEntry
import com.nu.bom.core.manufacturing.entities.ExternalManufacturingStep
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.entities.ManualManufacturingStep
import com.nu.bom.core.manufacturing.entities.ManualMaterialV2
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.fieldTypes.ObjectIdField
import com.nu.bom.core.manufacturing.utils.dependencies.GroupDependencyProvider
import com.nu.bom.core.model.BomNode
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.SnapshotId
import com.nu.bom.core.model.toBranchId
import com.nu.bom.core.model.toProjectId
import com.nu.bom.core.service.bomrads.BomNodeLoaderService
import com.nu.bom.core.service.bomrads.BomradsBranchService
import com.nu.bom.core.service.bomrads.BomradsObjectMapperService
import com.nu.bom.core.service.bomrads.DirectParents
import com.nu.bom.core.service.bomrads.PushChangesService
import com.nu.bom.core.service.change.ChangeContext
import com.nu.bom.core.service.change.ChangedEntities
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.JacksonTest
import com.nu.bom.core.utils.capture
import com.nu.bomrads.dto.BranchViewDTO
import com.nu.bomrads.dto.MinimalBranchDTO
import com.nu.bomrads.dto.MinimalChangesetDTO
import com.nu.bomrads.enumeration.BomNodeStatus
import com.nu.bomrads.enumeration.ChangeType
import com.nu.bomrads.id.ChangesetId
import com.tset.core.module.bom.EventSourcingModule
import org.bson.types.ObjectId
import org.hamcrest.MatcherAssert
import org.hamcrest.Matchers
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.ArgumentCaptor
import org.mockito.Captor
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import reactor.core.publisher.Mono
import java.time.Instant
import java.util.Optional

@JacksonTest
class ManufacturingStepLineServiceTests(
    @Autowired
    private val mapperBuilder: Jackson2ObjectMapperBuilder,
) {
    private var accessCheck: AccessCheck =
        AccessCheck(accountId = "accId", accountName = "accountName", accountLabel = "label", token = "token", realm = "default")

    private lateinit var manufacturingStepLineService: ManufacturingStepLineService

    private lateinit var bomTreeService: BomTreeService

    private lateinit var objectMapperService: BomradsObjectMapperService

    @Mock
    private lateinit var manufacturingCalculationService: ManufacturingCalculationService

    @Mock
    private lateinit var bomNodeLoaderService: BomNodeLoaderService

    @Mock
    private lateinit var fieldConversionService: FieldConversionService

    @Mock
    private lateinit var changeContextHelper: ChangeContextHelper

    @Mock
    private lateinit var pushChangesService: PushChangesService

    @Captor
    private lateinit var reqCaptor: ArgumentCaptor<ManufacturingCalculationService.Request>

    private val existingGroupId = ObjectId().toHexString()
    private val projectId: ProjectId = ProjectId()
    private val bomNodeId: BomNodeId = BomNodeId()
    private val branchId: BranchId = BranchId()

    private lateinit var bomNodeSnapshot: BomNodeSnapshot
    private lateinit var strStepIds: List<String>
    private lateinit var externalStep: ExternalManufacturingStep

    @BeforeEach
    fun setup() {
        MockitoAnnotations.openMocks(this)

        objectMapperService = BomradsObjectMapperService(fieldConversionService, mapperBuilder)

        whenever(
            changeContextHelper.prepareContext(
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                dto = anyOrNull(),
            ),
        ).thenReturn(Mockito.mock(ChangedEntities::class.java))

        whenever(
            changeContextHelper.createContext(
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
            ),
        ).thenReturn(Mono.just(Mockito.mock(ChangeContext::class.java)))

        bomTreeService =
            BomTreeService(
                Mockito.mock(BomradsBranchService::class.java),
                Mockito.mock(BomNodeService::class.java),
                bomNodeLoaderService,
                objectMapperService,
                changeContextHelper,
                Mockito.mock(EventSourcingModule::class.java),
                pushChangesService,
                Mockito.mock(DirtyChildLoadingService::class.java),
            )

        manufacturingStepLineService = ManufacturingStepLineService(manufacturingCalculationService, bomTreeService)

        bomNodeSnapshot = bomNodeSnapshot()
        val branchViewDTO =
            BranchViewDTO(
                projectId = projectId.toProjectId(),
                branch =
                    MinimalBranchDTO(
                        branchId.toBranchId(),
                        true,
                        true,
                        true,
                        "creator",
                        false,
                        true,
                        true,
                        null,
                        null,
                        null,
                    ),
                changeset =
                    MinimalChangesetDTO(
                        ChangesetId(),
                        ChangeType.CHECKOUT,
                        "creator",
                        Instant.now(),
                        "main",
                        false,
                        null,
                        null,
                        null,
                    ),
                sourceChangeset = null,
                snapshots = listOf(),
            )

        whenever(
            pushChangesService.pushChanges(
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
            ),
        ).thenReturn(Mono.just(branchViewDTO))

        val bomNodeDto = bomNodeDto()

        whenever(
            manufacturingCalculationService.transformLoadedAndCalculate(
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
            ),
        ).thenReturn(
            Mono.just(
                CalculationResultWithSnapshot(
                    CalculationResultDto(
                        listOf(),
                        listOf(),
                        bomNodeDto,
                        Mockito.mock(ManufacturingDto::class.java),
                    ),
                    bomNodeSnapshot,
                ),
            ),
        )

        whenever(
            manufacturingCalculationService.transformAndCalculate(
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                any(),
            ),
        ).thenAnswer { invocation ->
            val f =
                invocation.getArgument<(BaseManufacturing, BomNodeSnapshot, ChangedEntities) -> Mono<BaseManufacturing>>(
                    11,
                )
            f
                .invoke(
                    bomNodeSnapshot.manufacturing as BaseManufacturing,
                    bomNodeSnapshot,
                    Mockito.mock(ChangedEntities::class.java),
                ).map {
                    CalculationResultWithSnapshot(
                        CalculationResultDto(
                            listOf(),
                            listOf(),
                            bomNodeDto,
                            Mockito.mock(ManufacturingDto::class.java),
                            manufacturing = it,
                        ),
                        bomNodeSnapshot,
                    )
                }
        }

        whenever(
            bomNodeLoaderService.getBomNodeTreeForUpdate(
                accessCheck = accessCheck,
                projectId = projectId,
                nodeId = bomNodeId,
                sourceBranch = branchId,
                loadingMode = DirectParents(bomNodeId),
            ),
        ).thenReturn(Mono.just(bomNodeSnapshot to branchViewDTO))
    }

    private fun bomNodeDto(): BomNodeDto =
        BomNodeDto(
            id = bomNodeSnapshot.id().toHexString(),
            parents = emptyList(),
            subNodes = emptyList(),
            manufacturing = null,
            year = 2022,
            status = BomNodeStatus.IN_PROGRESS,
            responsibleUser = Optional.empty(),
            kpi = KeyPerformanceIndicatorApiDto(null, null),
            cbd = null,
            openMergesAvailable = emptyList(),
            waterfall = null,
            title = null,
            calculationType = FieldParameter(),
            internalRevisionId = "",
            lastModifiedDate = null,
            dirtyChildLoading = false,
            branch =
                BranchDto(
                    branchId.toHexString(),
                    "b",
                    true,
                    null,
                    "owner",
                    true,
                    false,
                    null,
                    null,
                    null,
                    false,
                    false,
                    null,
                    null,
                ),
            protectedAt = Instant.now(),
            protectedBy = "test-user",
        )

    private fun bomNodeSnapshot(): BomNodeSnapshot {
        val root = ManualManufacturing(name = "root")
        var parent: ManufacturingEntity = root
        externalStep = ExternalManufacturingStep(name = "step-external")
        parent.addChild(externalStep)
        parent = externalStep
        for (i in 10 downTo 0) {
            val step =
                ManualManufacturingStep(name = "step-$i").apply {
                    children = mutableListOf(ManualMaterialV2(name = "mat-${11 - i}"))
                    if (i < 5) {
                        replaceOrAddInitialFieldResult(GroupDependencyProvider.GROUP_ID_FIELD_NAME) {
                            ObjectIdField(existingGroupId)
                        }
                    }
                }
            parent.addChild(step)
            root.addChild(
                BomEntry(name = "manu-$i").apply { bomNodeId = BomNodeId() },
            )
            parent = step
        }

        strStepIds =
            root
                .visitChildren { entry, _ ->
                    entry.takeIf { it is ManualManufacturingStep }?.entityId
                }.reversed()

        // Create snapshot with proper `subNodes`
        return BomNodeSnapshot(
            "snap",
            2022,
            title = "snap",
            null,
            partId = null,
            manufacturing = root,
        ).apply {
            _id = SnapshotId()
            bomNode = BomNode("node", 2022, projectId)
        }
    }

    @Test
    @DisplayName("two consecutive steps should be added to group")
    fun testCreateGroup() {
        manufacturingStepLineService
            .createManufacturingStepLine(
                accessCheck,
                projectId,
                bomNodeId,
                branchId,
                strStepIds.subList(8, 10),
                false,
            ).block()

        captureTransformAndCalc()

        Assertions.assertNotNull(reqCaptor.value)
        Assertions.assertEquals(2, reqCaptor.value.parameters.size)

        MatcherAssert.assertThat(
            reqCaptor.value.parameters.map { it.entityId },
            Matchers.containsInAnyOrder(strStepIds[8], strStepIds[9]),
        )
        Assertions.assertNotNull(reqCaptor.value.parameters[0].value)
        Assertions.assertTrue(reqCaptor.value.parameters[0].value is ObjectId)
        Assertions.assertEquals(reqCaptor.value.parameters[0].value, reqCaptor.value.parameters[1].value)
    }

    @Test
    @DisplayName("if step not found for add group ManufacturingEntityNotFoundException should be thrown")
    fun testStepNotFoundDuringAdd() {
        assertThrows<ManufacturingEntityNotFoundException> {
            manufacturingStepLineService
                .createManufacturingStepLine(
                    accessCheck,
                    projectId,
                    bomNodeId,
                    branchId,
                    listOf(ObjectId().toHexString()),
                    false,
                ).block()
        }
    }

    @Test
    @DisplayName("none consecutive steps should not be added to group")
    fun testStepsNotConsecutiveNotFound() {
        assertThrows<StepsNotConsecutiveException> {
            manufacturingStepLineService
                .createManufacturingStepLine(
                    accessCheck,
                    projectId,
                    bomNodeId,
                    branchId,
                    listOf(strStepIds[6], strStepIds[8]),
                    false,
                ).block()
        }
    }

    @Test
    @DisplayName("already grouped steps should not be added to group")
    fun testStepsAlreadyGrouped() {
        assertThrows<AlreadyPartOfManufacturingLineException> {
            manufacturingStepLineService
                .createManufacturingStepLine(
                    accessCheck,
                    projectId,
                    bomNodeId,
                    branchId,
                    listOf(strStepIds[0], strStepIds[1]),
                    false,
                ).block()
        }
    }

    @Test
    @DisplayName("if group not found for revert group ManufacturingEntityNotFoundException should be thrown")
    fun testGroupNotFoundDuringRevertGroup() {
        assertThrows<ManufacturingEntityNotFoundException> {
            manufacturingStepLineService
                .revertManufacturingStepLine(
                    accessCheck,
                    bomNodeId,
                    branchId,
                    ObjectId().toHexString(),
                    false,
                ).block()
        }
    }

    @ParameterizedTest
    @MethodSource("sourceAdaptGroupsReorder")
    fun testAdaptGroupsAfterReorder(reorderScenario: ReorderScenario) {
        // indices used by testdata:  0   1   2     3    4   5    6  7    8     9
        val groupIdsOfSteps = listOf(g1, g1, null, null, g2, g2, g2, g2, null, null)
        val steps = buildStepList(groupIdsOfSteps)
        val movedSteps = steps.toMutableList()
        movedSteps.removeAt(reorderScenario.oldIndex)
        movedSteps.add(reorderScenario.newIndex, steps[reorderScenario.oldIndex])
        val oldParent =
            if (reorderScenario.oldIndex < steps.size - 1) {
                steps[reorderScenario.oldIndex + 1]
            } else {
                bomNodeSnapshot.manufacturing!!
            }
        val newParent =
            if (reorderScenario.newIndex < movedSteps.size - 1) {
                movedSteps[reorderScenario.newIndex + 1]
            } else {
                bomNodeSnapshot.manufacturing!!
            }
        val entity = steps[reorderScenario.oldIndex]
        val newChild = if (reorderScenario.newIndex > 0) movedSteps[reorderScenario.newIndex - 1] else null
        ManufacturingStepLineService.adaptGroupsAfterReorder(
            oldParent = oldParent,
            newParent = newParent,
            entity = entity,
            newChild = newChild,
        )

        Assertions.assertEquals(
            reorderScenario.expectedNewGroupId,
            entity.getFieldOrInitialFieldResult(GroupDependencyProvider.GROUP_ID_FIELD_NAME)?.res as? String,
        )
    }

    @Test
    fun `creating line with external step fails`() {
        assertThrows<ExternalManufacturingLineException> {
            manufacturingStepLineService
                .createManufacturingStepLine(
                    accessCheck,
                    projectId,
                    bomNodeId,
                    branchId,
                    strStepIds.subList(8, 11) + externalStep.entityId,
                    false,
                ).block()
        }
    }

    @Test
    fun `reorder-add external step to group fails`() {
        val steps = buildStepList(listOf(g1, g1))

        assertThrows<ExternalManufacturingLineException> {
            ManufacturingStepLineService.adaptGroupsAfterReorder(
                oldParent = null,
                newParent = steps[1],
                entity = externalStep,
                newChild = steps[0],
            )
        }
    }

    private fun buildStepList(groupIds: List<String?>): List<ManufacturingEntity> =
        groupIds.withIndex().map { (idx, groupId) ->
            ManualManufacturingStep(name = "step-$idx").apply {
                if (groupId != null) {
                    replaceOrAddInitialFieldResult(GroupDependencyProvider.GROUP_ID_FIELD_NAME) {
                        ObjectIdField(groupId)
                    }
                    replaceOrAddFieldResult(GroupDependencyProvider.GROUP_ID_FIELD_NAME) {
                        ObjectIdField(groupId)
                    }
                }
            }
        }

    private fun captureTransformAndCalc() {
        Mockito
            .verify(
                manufacturingCalculationService,
            ).transformLoadedAndCalculate(
                capture(reqCaptor),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
            )
    }

    data class ReorderScenario(
        val oldIndex: Int,
        val newIndex: Int,
        val expectedNewGroupId: String?,
    )

    companion object {
        private val g1 = ObjectId().toHexString()
        private val g2 = ObjectId().toHexString()

        @JvmStatic
        private fun sourceAdaptGroupsReorder() =
            // indices used by testdata:    0   1   2     3    4   5    6  7    8     9
            // val groupIdsOfSteps = listOf(g1, g1, null, null, g2, g2, g2, g2, null, null)
            listOf(
                ReorderScenario(0, 1, g1),
                ReorderScenario(0, 2, null),
                ReorderScenario(0, 6, g2),
                ReorderScenario(0, 7, null),
                ReorderScenario(0, 8, null),
                ReorderScenario(1, 0, g1),
                ReorderScenario(1, 2, null),
                ReorderScenario(4, 5, g2),
                ReorderScenario(4, 7, g2),
                ReorderScenario(4, 8, null),
                ReorderScenario(9, 0, null),
            )
    }
}
