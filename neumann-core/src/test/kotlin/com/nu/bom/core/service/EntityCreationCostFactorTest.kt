package com.nu.bom.core.service

import com.nu.bom.core.CalculationTestBase
import com.nu.bom.core.controller.CalculationResultDto
import com.nu.bom.core.controller.CalculationResultWithSnapshot
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.BaseManufacturingFields
import com.nu.bom.core.manufacturing.entities.Labor
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ManufacturingStep
import com.nu.bom.core.manufacturing.entities.Setup
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Date
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.TsetDefaultSkillType
import com.nu.bom.core.manufacturing.fieldTypes.mdKeyAsText
import com.nu.bom.core.manufacturing.utils.ManufacturingTreeUtils
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.MasterData
import com.nu.bom.core.model.MasterDataSelector
import com.nu.bom.core.model.TriggerAction
import com.nu.bom.core.model.configurations.CurrentEffectivityDefinition
import com.nu.bom.core.model.configurations.CustomFieldsConfiguration
import com.nu.bom.core.model.configurations.masterdata.CustomFieldInfo
import com.nu.bom.core.model.configurations.masterdata.EffectivityReference
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.EffectivityType
import com.nu.bom.core.service.change.ChangedEntities
import com.nu.bom.core.service.configurations.ConfigType
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService
import com.nu.bom.core.service.nuledge.NuLedgeService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.AccountUtil
import com.tset.bom.clients.nuledge.KnowledgeEntityDto
import org.assertj.core.api.Assertions.assertThat
import org.bson.types.ObjectId
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.doAnswer
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import reactor.core.publisher.Mono
import java.time.LocalDate
import kotlin.reflect.KClass

class EntityCreationCostFactorTest : CalculationTestBase() {
    private lateinit var manufacturing: Manufacturing

    lateinit var service: EntityCreationService

    lateinit var access: AccessCheck
    private lateinit var manufacturingCalculationService: ManufacturingCalculationService
    private lateinit var nuLedgeService: NuLedgeService
    private lateinit var bomNodeService: BomNodeService
    private lateinit var bomNodeConversionService: BomNodeConversionService
    private lateinit var bomNodeId: BomNodeId
    private lateinit var branchId: BranchId
    private lateinit var parentId: ObjectId
    private var knowledgeEntityDto: KnowledgeEntityDto? = null
    private lateinit var capturedTrigger: TriggerAction

    @BeforeEach
    fun setup() {
        manufacturingCalculationService = mock()
        nuLedgeService = mock()
        bomNodeService = mock()
        bomNodeConversionService = mock()

        access = AccountUtil.dummyAccessCheck()

        bomNodeId = BomNodeId()
        branchId = BranchId()
        parentId = ObjectId()

        service =
            EntityCreationService(
                entityManager(),
                manufacturingCalculationService,
                factory,
                bomNodeConversionService,
                bomNodeService,
                mockConfigurationManagementService,
            )

        val effDefs =
            // We have to use source fields that are part of [Manufacturing]
            listOf(
                CurrentEffectivityDefinition(
                    "pieces",
                    EffectivityType.NUMERIC,
                    BaseManufacturingFields::peakUsableProductionVolumePerYear.name,
                ),
                CurrentEffectivityDefinition("seconds", EffectivityType.NUMERIC, "timePerShift"),
                CurrentEffectivityDefinition("minutes", EffectivityType.NUMERIC, "timePerShift"),
                CurrentEffectivityDefinition("lov.field", EffectivityType.LOV, "overheadMethod"),
                CurrentEffectivityDefinition("date", EffectivityType.DATE, "calculationDate"),
                CurrentEffectivityDefinition(
                    "custom.lov",
                    EffectivityType.LOV,
                    CustomFieldsConfiguration.CUSTOM_FIELD_PREFIX + "lov",
                    customFieldInfo = CustomFieldInfo.Lov("custom lov"),
                ),
                CurrentEffectivityDefinition(
                    "custom-minutes",
                    EffectivityType.NUMERIC,
                    CustomFieldsConfiguration.CUSTOM_FIELD_PREFIX + "minutes",
                    customFieldInfo = CustomFieldInfo.Cost("custom minutes", Time::class.simpleName!!, TimeUnits.MINUTE.name),
                ),
            )

        whenever(
            mockConfigurationManagementService.getDefaultConfiguration(any(), eq(ConfigType.Masterdata)),
        ).then {
            Mono.just(
                MasterdataTsetConfigurationService
                    .getTsetMasterdataConfig()
                    .let { default ->
                        default.copy(
                            effectivityDefinitions = default.effectivityDefinitions + effDefs,
                            overheadsConfiguration =
                                default
                                    .overheadsConfiguration
                                    .copy(effectivities = effDefs.map { EffectivityReference(it.fieldDefinitionKey) }),
                        )
                    },
            )
        }

        whenever(
            manufacturingCalculationService.transformAndCalculate(
                accessCheck = eq(access),
                bomNodeId = eq(bomNodeId),
                branchId = any<BranchId>(),
                triggerAction = any<TriggerAction>(),
                context = any(),
                forceRecalculate = any(),
                dirtyChildLoading = any(),
                extraBomNodeId = anyOrNull(),
                loadingMode = any(),
                customInputs = org.mockito.kotlin.isNull(),
                transformRoot = anyOrNull(),
                transformManufacturing = any(),
            ),
        ).doAnswer {
            val func =
                it.arguments[11] as ((BaseManufacturing, BomNodeSnapshot, ChangedEntities) -> Mono<BaseManufacturing>)
            val result =
                func(
                    manufacturing,
                    mock(),
                    mock(),
                )
            val baseManufacturing = result.block()

            capturedTrigger = it.getArgument(3)

            calculate(baseManufacturing!!)

            Mono.just(
                CalculationResultWithSnapshot(
                    CalculationResultDto(
                        manufacturing = baseManufacturing,
                        bomNode = mock(),
                        missingInputs = emptyList(),
                        open = emptyList(),
                        openCalculations = emptyList(),
                        result = mock(),
                    ),
                    mock(),
                ),
            )
        }

        whenever(calculationContextServiceMock.nuLedge()).doReturn(nuLedgeService)

        whenever(nuLedgeService.getConfigurations(any(), any()))
            .thenAnswer {
                val key = it.arguments[1] as String
                Mono.justOrEmpty(knowledgeEntityDto?.copy(key = key))
            }
        mockMasterData()
    }

    private fun mockMasterData() {
        whenever(masterDataServiceMock.getLatestMasterDataByCompositeKey(any(), any(), any()))
            .thenAnswer {
                val selector = it.arguments[1] as MasterDataSelector
                Mono.just(
                    MasterData(
                        selector = selector,
                        data = mapOf("displayDesignation" to Text("masterData for " + selector.key)),
                    ),
                )
            }
    }

    /*
    Test creates manufacturing with 2 Steps
    Assigns different locations to each Step.
    Creates 2 Labors with different skillTypes and belonging to different steps.
    Performs calculation.
    Check that labor has a correct wage based on skillType + location of a step
     (wages are mocked in CalculationTestBase, near MasterdataTsetConfigurationService.COST_FACTOR_HEADER_TYPE_KEY)
     */
    @Test
    fun `check md cost factors entities are being created and used for Labor`() {
        manufacturing =
            run {
                val stepOne = ManufacturingStep("manuStep1")
                stepOne.replaceOrAddInitialFieldResult("location") {
                    Text("SteplandOne")
                }
                val stepTwo = ManufacturingStep("manuStep2")
                stepTwo.replaceOrAddInitialFieldResult("location") {
                    Text("SteplandTwo")
                }
                val laborInspector = Labor("labor inspector")
                laborInspector.addFieldResult("skillType") {
                    Text(TsetDefaultSkillType.INSPECTOR.mdKey)
                }
                val laborAccountant = Labor("labor accountant")
                laborAccountant.addFieldResult("skillType") {
                    TsetDefaultSkillType.FINANCIAL_ACCOUNTANT.mdKeyAsText()
                }
                val manufacturing =
                    createManufacturing(
                        "testEntity",
                        parameters =
                            mapOf(
                                "date" to Date(LocalDate.of(2024, 1, 1)),
                                "location" to Text("Germany"),
                            ),
                    )
                stepOne.addChild(laborInspector)
                stepTwo.addChild(laborAccountant)
                manufacturing.addChild(stepOne)
                manufacturing.addChild(stepTwo)
                manufacturing
            }

        val dto =
            createEntityDto(
                Entities.MACHINE,
                mapOf(
                    "quantity" to Num(1.0).withSource(FieldResult.SOURCE.I),
                ),
            )

        val result = service.createAndCalculate(access, dto).block()
        Assertions.assertNotNull(result)

        val calculatedStep = ManufacturingTreeUtils.getDescendantsWithType(manufacturing, Entities.MANUFACTURING_STEP)
        // Verify that locations are correct and hasn't been overwritten
        assertThat(calculatedStep)
            .hasSize(2)
            .satisfies({
                assertThat(
                    it
                        .first { it.name == "manuStep1" }
                        .getFieldResultSafe("location")
                        ?.res,
                ).isEqualTo("SteplandOne")
                assertThat(
                    it
                        .first { it.name == "manuStep2" }
                        .getFieldResultSafe("location")
                        ?.res,
                ).isEqualTo("SteplandTwo")
            })

        val costfactorWage = ManufacturingTreeUtils.getDescendantsWithType(manufacturing, Entities.MD_COSTFACTORS_WAGE)
        // we have 1 skill type per 1 location
        assertThat(costfactorWage).hasSize(2)
        val labor = ManufacturingTreeUtils.getDescendantsWithType(manufacturing, Entities.LABOR)
        assertThat(labor)
            .hasSize(2)
            .satisfies({
                assertThat(it.first { it.name == "labor inspector" }.getFieldResultSafe("wage"))
                    .isEqualTo(Money("41.**********"))
                assertThat(it.first { it.name == "labor accountant" }.getFieldResultSafe("wage"))
                    .isEqualTo(Money("41.**********"))
            })
        val costfactorLaborBurden = ManufacturingTreeUtils.getDescendantsWithType(manufacturing, Entities.MD_COSTFACTORS_LABOR_BURDEN)
        val costFactorNaturalGasPriceEntity =
            ManufacturingTreeUtils.getDescendantsWithType(
                manufacturing,
                Entities.MD_COSTFACTORS_NATURAL_GAS_PRICE,
            )
        val costFactorNaturalGasEmissionsEntity =
            ManufacturingTreeUtils.getDescendantsWithType(
                manufacturing,
                Entities.MD_COSTFACTORS_NATURAL_GAS_EMISSIONS,
            )
        val costFactorInterestEntity = ManufacturingTreeUtils.getDescendantsWithType(manufacturing, Entities.MD_COSTFACTORS_INTEREST)
        val costFactorFloorSpacePriceEntity =
            ManufacturingTreeUtils.getDescendantsWithType(
                manufacturing,
                Entities.MD_COSTFACTORS_FLOOR_SPACE_PRICE,
            )
        val costFactorElectricityEmissionsEntity =
            ManufacturingTreeUtils.getDescendantsWithType(
                manufacturing,
                Entities.MD_COSTFACTORS_ELECTRICITY_EMISSIONS,
            )
        val costFactorAluminiumEmissionsEntity =
            ManufacturingTreeUtils.getDescendantsWithType(
                manufacturing,
                Entities.MD_COSTFACTORS_ALUMINIUM_EMISSIONS,
            )
        val costFactorAluminiumShareEntity =
            ManufacturingTreeUtils.getDescendantsWithType(
                manufacturing,
                Entities.MD_COSTFACTORS_ALUMINIUM_SHARE,
            )
        val costFactorElectricityPriceEntity =
            ManufacturingTreeUtils.getDescendantsWithType(
                manufacturing,
                Entities.MD_COSTFACTORS_ELECTRICITY_PRICE,
            )
        Assertions.assertNotNull(costfactorWage)
        Assertions.assertNotNull(costfactorLaborBurden)
        Assertions.assertNotNull(costFactorNaturalGasPriceEntity)
        Assertions.assertNotNull(costFactorNaturalGasEmissionsEntity)
        Assertions.assertNotNull(costFactorInterestEntity)
        Assertions.assertNotNull(costFactorFloorSpacePriceEntity)
        Assertions.assertNotNull(costFactorElectricityEmissionsEntity)
        Assertions.assertNotNull(costFactorAluminiumEmissionsEntity)
        Assertions.assertNotNull(costFactorAluminiumShareEntity)
        Assertions.assertNotNull(costFactorElectricityPriceEntity)
    }

    @Test
    fun `check cost factors are being created and used for Setup`() {
        manufacturing =
            run {
                val stepOne = ManufacturingStep("manuStep1")
                stepOne.replaceOrAddInitialFieldResult("location") {
                    Text("SteplandOne")
                }
                val stepTwo = ManufacturingStep("manuStep2")
                stepTwo.replaceOrAddInitialFieldResult("location") {
                    Text("SteplandTwo")
                }
                val setupInspector = Setup("setup inspector")
                setupInspector.addFieldResult("skillType") {
                    Text(TsetDefaultSkillType.INSPECTOR.mdKey)
                }
                val setupAccountant = Setup("setup accountant")
                setupAccountant.addFieldResult("skillType") {
                    TsetDefaultSkillType.FINANCIAL_ACCOUNTANT.mdKeyAsText()
                }
                val manufacturing =
                    createManufacturing(
                        "testEntity",
                        parameters =
                            mapOf(
                                "date" to Date(LocalDate.of(2024, 1, 1)),
                                "location" to Text("Germany"),
                            ),
                    )
                stepOne.addChild(setupInspector)
                stepTwo.addChild(setupAccountant)
                manufacturing.addChild(stepOne)
                manufacturing.addChild(stepTwo)
                manufacturing
            }

        val dto =
            createEntityDto(
                Entities.MACHINE,
                mapOf(
                    "quantity" to Num(1.0).withSource(FieldResult.SOURCE.I),
                ),
            )

        val result = service.createAndCalculate(access, dto).block()
        Assertions.assertNotNull(result)

        val calculatedStep = ManufacturingTreeUtils.getDescendantsWithType(manufacturing, Entities.MANUFACTURING_STEP)
        // Verify that locations are correct and hasn't been overwritten
        assertThat(calculatedStep)
            .hasSize(2)
            .satisfies({
                assertThat(
                    it
                        .first { it.name == "manuStep1" }
                        .getFieldResultSafe("location")
                        ?.res,
                ).isEqualTo("SteplandOne")
                assertThat(
                    it
                        .first { it.name == "manuStep2" }
                        .getFieldResultSafe("location")
                        ?.res,
                ).isEqualTo("SteplandTwo")
            })

        val costfactorWage = ManufacturingTreeUtils.getDescendantsWithType(manufacturing, Entities.MD_COSTFACTORS_WAGE)
        // we have 2 skill types and 2 different locations (1 per step)
        assertThat(costfactorWage).hasSize(2)
        val setups = ManufacturingTreeUtils.getDescendantsWithType(manufacturing, Entities.SETUP)
        assertThat(setups)
            .hasSize(2)
            .satisfies({
                assertThat(it.first { it.name == "setup inspector" }.getFieldResultSafe("wage"))
                    .isEqualTo(Money("41.**********"))
                assertThat(it.first { it.name == "setup accountant" }.getFieldResultSafe("wage"))
                    .isEqualTo(Money("41.**********"))
            })
    }

    private fun createEntityDto(
        entityType: Entities,
        fields: Map<String, FieldResultStar>,
        entityClass: KClass<out ManufacturingEntity>? = null,
        masterDataSelector: MasterDataSelector? = null,
    ): EntityCreationDataConversionService.EntityCreationData =
        EntityCreationDataConversionService.EntityCreationData(
            bomNodeId = bomNodeId,
            branchId = branchId,
            parentId = manufacturing._id,
            projectId = null,
            childBomNodeId = null,
            items =
                listOf(
                    EntityCreationDataConversionService.EntityCreationData.ItemData(
                        entityType = entityType,
                        entityClass = entityClass,
                        masterDataSelector = masterDataSelector,
                        fields = fields,
                        overwrites = emptyMap(),
                        isolated = false,
                    ),
                ),
        )
}
