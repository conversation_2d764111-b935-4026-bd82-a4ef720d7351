package com.nu.bom.core.manufacturing.fieldTypes

import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.fieldmapping.BackMappingFieldsBuilder
import com.nu.bom.core.service.LocalEntityHashFallbackProvider
import com.nu.bom.core.startup.TypeLoader
import com.nu.bom.core.utils.EntitiesEnumEntityLoader
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.ExtensionManager
import com.nu.bom.core.utils.ManualCreationEntityLoader
import com.nu.bom.core.utils.TypeCategory
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class TypeMappingTests {
    lateinit var entityManager: EntityManager

    @BeforeEach
    fun setup() {
        val extensionManager = ExtensionManager()

        entityManager =
            EntityManager(
                extensionManager,
                LocalEntityHashFallbackProvider(),
                ManualCreationEntityLoader(EntitiesEnumEntityLoader()),
                BackMappingFieldsBuilder(),
            )
        TypeLoader(entityManager).onStartup()
    }

    @Test
    fun primitiveTypeMappingTest() {
        val decimalType = entityManager.getMaybeFieldType("Num")
        Assertions.assertNotNull(decimalType)

        val typeDefinition = entityManager.mapType(decimalType!!)
        Assertions.assertNotNull(typeDefinition)
        Assertions.assertFalse(typeDefinition.deprecated)
        Assertions.assertEquals(Num::class.simpleName, typeDefinition.type)
        Assertions.assertEquals(TypeCategory.Primitive, typeDefinition.typeDetails.category)
        Assertions.assertEquals("Decimal", typeDefinition.typeDetails.valueType)
        Assertions.assertNull(typeDefinition.typeDetails.keyType)
    }

    @Test
    fun listTypeMappingTest() {
        val listType = entityManager.getMaybeFieldType("PriceComponents")
        Assertions.assertNotNull(listType)

        val typeDefinition = entityManager.mapType(listType!!)
        Assertions.assertNotNull(typeDefinition)
        Assertions.assertFalse(typeDefinition.deprecated)
        Assertions.assertEquals(TypeCategory.List, typeDefinition.typeDetails.category)
        Assertions.assertNull(typeDefinition.typeDetails.keyType)
    }

    @Test
    fun mapTypeMappingTest() {
        val mapType = entityManager.getMaybeFieldType("ExchangeRatesField")
        Assertions.assertNotNull(mapType)

        val typeDefinition = entityManager.mapType(mapType!!)
        Assertions.assertNotNull(typeDefinition)
        Assertions.assertFalse(typeDefinition.deprecated)
        Assertions.assertEquals(ExchangeRatesField::class.simpleName, typeDefinition.type)
        Assertions.assertEquals(TypeCategory.Map, typeDefinition.typeDetails.category)
        Assertions.assertEquals("Decimal", typeDefinition.typeDetails.valueType)
        Assertions.assertEquals("Currency", typeDefinition.typeDetails.keyType)
    }

    @Test
    fun complexTypeMappingTest() {
        val mapType = entityManager.getMaybeFieldType("CurrenciesField")
        Assertions.assertNotNull(mapType)

        val typeDefinition = entityManager.mapType(mapType!!)
        Assertions.assertNotNull(typeDefinition)
        Assertions.assertFalse(typeDefinition.deprecated)
        Assertions.assertEquals(CurrenciesField::class.simpleName, typeDefinition.type)
        Assertions.assertEquals(TypeCategory.Complex, typeDefinition.typeDetails.category)
        Assertions.assertEquals("Jsondata<Currencies>", typeDefinition.typeDetails.valueType)
        Assertions.assertNull(typeDefinition.typeDetails.keyType)
    }

    @Test
    fun selectorTypeMappingTest() {
        val mapType = entityManager.getMaybeFieldType("ClassSelector")
        Assertions.assertNotNull(mapType)

        val typeDefinition = entityManager.mapType(mapType!!)
        Assertions.assertNotNull(typeDefinition)
        Assertions.assertFalse(typeDefinition.deprecated)
        Assertions.assertEquals(ClassSelector::class.simpleName, typeDefinition.type)
        Assertions.assertEquals(TypeCategory.Primitive, typeDefinition.typeDetails.category)
        Assertions.assertEquals("String", typeDefinition.typeDetails.valueType)
    }

    @Test
    fun valueTypesTest() {
        entityManager.getTypeDefinitions().map { typeDefinition ->
            typeDefinition.typeDetails
        }.distinct().forEach {
            println("${it.category}: valueType=${it.valueType}")
        }
    }
}
