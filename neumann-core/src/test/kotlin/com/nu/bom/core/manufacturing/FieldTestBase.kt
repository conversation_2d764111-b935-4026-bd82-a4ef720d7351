package com.nu.bom.core.manufacturing

import com.nu.bom.core.CalculationTestBase
import com.nu.bom.core.machining.model.MillingRequest
import com.nu.bom.core.machining.model.MillingResponse
import com.nu.bom.core.machining.model.TurningRequest
import com.nu.bom.core.machining.model.TurningResponse
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.CalculationOperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.rollupconfiguration.RollUpConfiguration
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataCategory
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.extension.classificationcalculator.ClassificationCalculatorRestriction
import com.nu.bom.core.manufacturing.extension.lookups.CO2SteelLookup
import com.nu.bom.core.manufacturing.factories.FieldFactoryService
import com.nu.bom.core.manufacturing.fieldTypes.CO2CalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.ClassificationResponse
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.CostCalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.MaterialFurnaceType
import com.nu.bom.core.manufacturing.fieldTypes.MaterialSubstances
import com.nu.bom.core.manufacturing.fieldTypes.NumericFieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Result
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TurningStep
import com.nu.bom.core.manufacturing.service.CalculationContextService
import com.nu.bom.core.manufacturing.service.EntityClassOrName
import com.nu.bom.core.manufacturing.service.InjectableEngineService
import com.nu.bom.core.manufacturing.utils.CalculationContextServiceImplReach
import com.nu.bom.core.manufacturing.utils.InjectionUtilsService
import com.nu.bom.core.model.IMasterData
import com.nu.bom.core.model.Lookup
import com.nu.bom.core.model.MasterData
import com.nu.bom.core.model.MasterDataSelector
import com.nu.bom.core.model.TurningProfile
import com.nu.bom.core.model.configurations.ConfigurationValue
import com.nu.bom.core.model.configurations.CostModulesConfiguration
import com.nu.bom.core.model.configurations.CurrentMasterdataConfiguration
import com.nu.bom.core.model.configurations.VersionedCostModule
import com.nu.bom.core.service.LookupService
import com.nu.bom.core.service.configurations.ConfigType
import com.nu.bom.core.service.fti.FtiService
import com.nu.bom.core.service.imports.LookupReader
import com.nu.bom.core.service.nexar.NexarQueryService
import com.nu.bom.core.service.nuledge.NuLedgeService
import com.nu.bom.core.smf.SmfUnfoldedPartScalingService
import com.nu.bom.core.smf.model.NestorRequest
import com.nu.bom.core.smf.model.NestorResponse
import com.nu.bom.core.smf.model.WsiUnfolderResponse
import com.nu.bom.core.turn.TurningApiMock.Companion.mockCycleTimeResponse
import com.nu.bom.core.turn.model.InductiveHardeningGroup
import com.nu.bom.core.turn.model.Technology
import com.nu.bom.core.turn.model.TurningOperationCycleTimeRequestData
import com.nu.bom.core.turn.model.TurningOperationResponse
import com.nu.bom.core.turn.model.TurningStepsRequest
import com.nu.bom.core.turn.model.TurningStepsResponse
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.ExtensionManager
import com.nu.bom.core.utils.ShapesData
import com.nu.bomrads.dto.FileUploadDto
import com.opencsv.CSVReader
import com.tset.bom.clients.tsetdel.model.TsetDelFileLocation
import org.bson.types.ObjectId
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.MockitoAnnotations
import org.slf4j.LoggerFactory
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.io.StringReader
import java.math.BigDecimal
import java.math.RoundingMode
import kotlin.reflect.KClass
import kotlin.reflect.full.declaredMembers

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
abstract class FieldTestBase {
    companion object {
        val logger = LoggerFactory.getLogger(CalculationTestBase::class.java)!!

        object NotAThrowable {
            override fun equals(other: Any?): Boolean = other !is Throwable
        }

        private fun getLookup(
            key: String,
            accessCheck: AccessCheck,
        ): Lookup = getLookup(key, accessCheck.accountName)

        private fun getLookup(
            key: String,
            accountIdentifier: String,
        ): Lookup {
            val lookupFile = this::class.java.getResource("/shapedata/$accountIdentifier/lookup/Lookup_$key.csv")!!.readText()
            val lookupReader = CSVReader(StringReader(lookupFile))
            return LookupReader.readLookup(lookupReader, false, key, accountIdentifier)
        }
    }

    protected val entityManager: EntityManager
        get() = MetaCache.entityManager
    protected val extensionManager: ExtensionManager
        get() = MetaCache.extensionManager

    private var accessCheck =
        AccessCheck(accountId = "accId", accountName = "accName", accountLabel = "label", token = "token", realm = "default")

    private fun setAccountIdentifier(accountIdentifier: String) {
        accessCheck =
            accessCheck.copy(
                accountId = accountIdentifier,
                accountName = accountIdentifier,
            )
    }

    abstract fun getEntity(): ManufacturingEntity

    abstract fun getFieldName(): String

    abstract fun testCases(): List<TestCase>

    data class TestCase(
        val inputs: List<Any?>,
        val result: Any?,
        val accountIdentifier: String = "ts",
    ) {
        init {
            require(inputs.all { it is Result || it is InjectableEngineService || it == null }) {
                "Inputs must be a list of either ${FieldResult::class.simpleName} or ${InjectableEngineService::class.simpleName}."
            }
        }
    }

    @ParameterizedTest
    @MethodSource("testCases")
    fun runtTest(testCase: TestCase) {
        setAccountIdentifier(testCase.accountIdentifier)

        val entity = getEntityWithMocks()
        val function = entity::class.declaredMembers.find { it.name == getFieldName() }
        val paramList =
            if (function!!.parameters.any { it.name == "costModuleVersionProvider" }) {
                listOf(entity) + testCase.inputs + CostModulesConfiguration()
            } else {
                listOf(entity) + testCase.inputs
            }
        val expectedResult = testCase.result

        var result: Any?
        try {
            result = function.call(*paramList.toTypedArray())
            if (result is Mono<*>) {
                result = result.block()
            }
        } catch (e: Exception) {
            result =
                if (expectedResult is KClass<*>) {
                    (e.cause ?: e)::class
                } else {
                    e
                }
        }

        if (result is NumericFieldResult<*> && expectedResult is NumericFieldResult<*>) {
            Assertions.assertEquals(
                expectedResult.res,
                result.res.setScale(expectedResult.res.scale(), RoundingMode.HALF_UP),
            )
        } else {
            Assertions.assertEquals(expectedResult, result)
        }
    }

    private fun getEntityWithMocks(): ManufacturingEntity {
        val contextServiceMock = CalculationContextMock()
        val calculationContextServiceImplReach = CalculationContextServiceImplReach(contextServiceMock, accessCheck)
        val entity = getEntity()
        entity.calculationServices = calculationContextServiceImplReach
        return entity
    }

    @BeforeEach
    fun setup() {
        MockitoAnnotations.openMocks(this)
    }

    class LookupServiceMock : LookupService {
        override fun exists(
            key: String,
            accountIdentifier: String,
            predicate: (List<String>) -> Boolean,
        ): Mono<Boolean> {
            TODO("Not yet implemented")
        }

        override fun getLookupTable(
            key: String,
            accountIdentifier: String,
        ): Mono<Map<String, String>> {
            TODO("Not yet implemented")
        }

        override fun <T> getLookupTable(
            key: String,
            accountIdentifier: String,
            rowParser: (List<String>) -> T,
        ): Flux<T> {
            TODO("Not yet implemented")
        }

        override fun <T> getAccountLookupTable(
            key: String,
            accountIdentifier: String,
            rowParser: (List<String>) -> T,
        ): Flux<T> = Flux.fromIterable(getLookup(key, accountIdentifier).rows.map(rowParser))

        override fun <T> getLookupTableWithHeader(
            key: String,
            accountIdentifier: String,
            rowParser: (Pair<List<String>, List<String>>) -> T,
        ): Flux<T> {
            TODO("Not yet implemented")
        }

        override fun <T> getLookupRows(
            key: String,
            accountIdentifier: String,
            rowParser: (Lookup.Row) -> T,
        ): Flux<T> {
            TODO("Not yet implemented")
        }

        override fun getLookupRows(
            key: String,
            accountIdentifier: String,
        ): Flux<Lookup.Row> {
            TODO("Not yet implemented")
        }

        @Deprecated(
            "old version relying on old lookup format (1st column key, 2nd column value) instead of new multicolumn based lookup format",
        )
        override fun lookupValue(
            lookupTableKey: String,
            lookupKey: String,
        ): Mono<String?> {
            TODO("Not yet implemented")
        }

        override fun persistLookup(lookup: Lookup): Mono<Lookup> {
            TODO("Not yet implemented")
        }
    }

    class CalculationContextMock : CalculationContextService {
        override fun getMasterData(
            accessCheck: AccessCheck,
            type: MasterDataType,
            key: String,
            year: Int,
            location: String,
        ): Mono<MasterData> {
            TODO()
        }

        override fun getMasterData(
            accessCheck: AccessCheck,
            category: MasterDataCategory,
            key: String,
            year: Int,
            location: String,
        ): Mono<MasterData> {
            TODO()
        }

        override fun getShapeInfo(
            accessCheck: AccessCheck,
            technology: String,
            shapeId: String,
            onlyActive: Boolean,
        ): ShapesData.ShapeInfo? {
            TODO()
        }

        override fun createEntity(
            name: String,
            entityType: Entities,
            clazz: EntityClassOrName?,
            args: Map<String, Any>,
            fields: Map<String, FieldResult<*, *>>,
            overwrites: Map<String, FieldResult<*, *>>,
            entityRef: String?,
            version: Int,
            entityId: ObjectId?,
        ): ManufacturingEntity {
            TODO()
        }

        override fun createManufacturing(
            name: String,
            clazz: EntityClassOrName,
            args: Map<String, Any?>,
            masterDataSelector: MasterDataSelector?,
            masterDataObjectId: ObjectId?,
            fields: Map<String, FieldResult<*, *>>,
            version: Int,
        ): BaseManufacturing {
            TODO()
        }

        override fun getLatestLookupTable(
            key: String,
            accessCheck: AccessCheck,
        ): Mono<Map<String, String>> {
            val lookup = getLookup(key, accessCheck)
            return Mono.just(lookup.rows.associate { it[0] to it[1] })
        }

        override fun <T> getLatestLookupTable(
            key: String,
            accessCheck: AccessCheck,
            rowParser: (List<String>) -> T,
        ): Flux<T> = Flux.fromIterable(getLookup(key, accessCheck).rows.map(rowParser))

        override fun <T> getLookupRows(
            key: String,
            accessCheck: AccessCheck,
            rowParser: (Lookup.Row) -> T,
        ): Flux<T> = Flux.fromIterable(getLookup(key, accessCheck).rowList().map(rowParser))

        @Deprecated(
            "old version relying on old lookup format (1st column key, 2nd column value) instead of new multicolumn based lookup format",
        )
        override fun lookupValue(
            lookupTableKey: String,
            lookupKey: String,
        ): Mono<String?> {
            TODO()
        }

        override fun predictNum(
            technology: String,
            variable: String,
            inputs: Map<String, Any>,
        ): Mono<BigDecimal> {
            TODO()
        }

        override fun predictString(
            technology: String,
            variable: String,
            inputs: Map<String, Any>,
        ): Mono<String> {
            TODO()
        }

        override fun <T> predict(
            technology: String,
            variable: String,
            inputs: Map<String, Any>,
        ): Mono<T> {
            TODO()
        }

        override fun createEntitiesFromTemplate(
            accessCheck: AccessCheck,
            name: String,
            location: String,
            year: Int,
            additionalArgs: Map<String, Any>,
            version: Int,
        ): Flux<ManufacturingEntity> {
            TODO()
        }

        override fun <T : ManufacturingEntity> createSystemParameter(
            accessCheck: AccessCheck,
            type: Entities,
            masterData: MasterDataType,
            clazz: KClass<out T>,
            system: String,
            year: Int,
            location: String,
            version: Int,
            entityRef: String?,
        ): Mono<T> {
            TODO()
        }

        override fun createEntityWithMasterdata(
            accessCheck: AccessCheck,
            name: String,
            entityType: Entities,
            clazz: EntityClassOrName,
            masterDataSelector: MasterDataSelector,
            version: Int,
            args: Map<String, Any>,
            overwrites: Map<String, FieldResult<*, *>>,
            fields: Map<String, FieldResult<*, *>>,
            entityRef: String?,
            entityId: ObjectId?,
        ): Mono<ManufacturingEntity> {
            TODO()
        }

        override fun getTurningProfile(
            turningProfileId: Text,
            accessCheck: AccessCheck,
        ): Mono<TurningProfile> {
            TODO()
        }

        override fun <T> getTurningSequence(
            step: TurningStep,
            turningProfileId: Text,
            input: CalculationContextService.TurningProfileInputFields<T>,
            accessCheck: AccessCheck,
        ): Mono<Pair<TurningRequest<T>, TurningResponse>> {
            TODO()
        }

        override fun getTurningOperationCycleTime(request: TurningOperationCycleTimeRequestData): Mono<TurningOperationResponse> =
            Mono.just(mockCycleTimeResponse(request))

        override fun getUnfolderResponse(
            wizardId: String,
            fileContent: String,
        ): Mono<WsiUnfolderResponse> {
            TODO()
        }

        override fun getNestingResponse(
            wizardId: String,
            request: NestorRequest,
            accessCheck: AccessCheck,
            uploadNestingImage: Boolean,
        ): Mono<Pair<String?, NestorResponse>> {
            TODO("Not yet implemented")
        }

        override fun getInductiveHardeningGroups(
            turningProfileId: Text,
            accessCheck: AccessCheck,
        ): Mono<List<InductiveHardeningGroup>> {
            TODO()
        }

        override fun <T> getTurningCalculationSteps(
            turningProfileId: Text,
            technology: Technology<T>,
            accessCheck: AccessCheck,
        ): Mono<Pair<TurningStepsRequest<T>, TurningStepsResponse>> {
            TODO()
        }

        override fun getMillingCalculation(
            input: CalculationContextService.MillingDrillingInputFields,
            accessCheck: AccessCheck,
        ): Mono<Pair<MillingRequest, MillingResponse>> {
            TODO()
        }

        override fun getClassificationCalculator(
            materialSubstances: MaterialSubstances,
            steel: CO2SteelLookup,
            materialFurnaceType: MaterialFurnaceType?,
        ): Mono<ClassificationResponse> {
            TODO()
        }

        override fun getClassificationCalculator(
            materialSubstances: MaterialSubstances,
            restriction: List<Pair<String, ClassificationCalculatorRestriction>>,
            materialFurnaceType: MaterialFurnaceType?,
        ): Mono<ClassificationResponse> {
            TODO()
        }

        override fun nuLedge(): NuLedgeService {
            TODO()
        }

        override fun octoPartService(): NexarQueryService {
            TODO()
        }

        override fun fieldFactoryService(): FieldFactoryService {
            TODO()
        }

        override fun getSmfUnfoldedPartScalingService(): SmfUnfoldedPartScalingService {
            TODO()
        }

        override fun getTsetDelFileLocation(
            accessCheck: AccessCheck,
            technologyKey: String,
            shapeId: String,
        ): Mono<TsetDelFileLocation> {
            TODO()
        }

        override fun getFile(
            accessCheck: AccessCheck,
            fileId: String,
        ): Mono<ByteArray> {
            TODO()
        }

        override fun getFileInfo(
            accessCheck: AccessCheck,
            fileId: String,
        ): Mono<FileUploadDto> {
            TODO()
        }

        override fun findMachiningTool(
            accessCheck: AccessCheck,
            type: String,
            materialGroup: String?,
            entityClass: String,
            toolDiameter: BigDecimal?,
            selectedTool: String?,
            allowEqual: Boolean,
        ): Mono<List<IMasterData>> {
            TODO()
        }

        override fun getInjectionUtilsService(): InjectionUtilsService = InjectionUtilsService(LookupServiceMock())

        override fun <T : ConfigurationValue, C : ConfigType<T>> getConfiguration(
            accessCheck: AccessCheck,
            type: C,
            key: ConfigurationKey<C>,
        ): Mono<T> {
            TODO()
        }

        override fun getCostModuleConfiguration(
            accessCheck: AccessCheck,
            group: String,
            type: String,
            id: ConfigurationIdentifier,
        ): Mono<VersionedCostModule> =
            Mono.just(
                CostModulesConfiguration(),
            )

        override fun getDefaultConfigurationKey(
            accessCheck: AccessCheck,
            type: ConfigType<*>,
        ): Mono<ConfigurationIdentifier> {
            TODO()
        }

        override fun getDefaultCostModuleConfigurationKey(
            accessCheck: AccessCheck,
            type: Model,
        ): Mono<ConfigurationIdentifier> {
            TODO()
        }

        override fun getCostCalculationConfiguration(
            accessCheck: AccessCheck,
            costCalculationOperationsConfigurationKey: CostCalculationOperationsConfigurationKey,
            rollUpConfiguration: RollUpConfiguration,
        ): Mono<CalculationOperationConfiguration> {
            TODO()
        }

        override fun getCO2CalculationConfiguration(
            accessCheck: AccessCheck,
            cO2CalculationOperationsConfigurationKey: CO2CalculationOperationsConfigurationKey,
            rollUpConfiguration: RollUpConfiguration,
        ): Mono<CalculationOperationConfiguration> {
            TODO("Not yet implemented")
        }

        override fun getDefaultMasterdataConfiguration(accessCheck: AccessCheck): Mono<CurrentMasterdataConfiguration> {
            TODO("Not yet implemented")
        }

        override fun getFtiService(): FtiService {
            TODO()
        }
    }
}
