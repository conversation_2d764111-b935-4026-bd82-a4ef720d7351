package com.nu.bom.core.basecalculation

import com.nu.bom.core.CalculationTestBase
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.fieldnamebuilders.RateTimeFieldNameBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCostCalculationElementType
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.AreaUnits
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeUnit
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.Power
import com.nu.bom.core.manufacturing.fieldTypes.PowerUnits
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYearsUnit
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.ToolAllocationMode
import org.junit.jupiter.api.Test

class OneStepOneMachineOneMaterialOneLabor : CalculationTestBase() {
    @Test
    fun performCalculation() {
        val calculation =
            createManufacturing(
                name = "Manufacturing",
                parameters =
                    mapOf(
                        RateTimeFieldNameBuilder(
                            ValueType.COST,
                            TsetCostCalculationElementType.RAW_MATERIAL_INTEREST_COSTS.fieldName,
                            RateTimeFieldNameBuilder.ExtensionName.TIME,
                        ).fieldName to TimeInYears.ZERO,
                        RateTimeFieldNameBuilder(
                            ValueType.COST,
                            TsetCostCalculationElementType.PURCHASE_PARTS_INTEREST_COSTS.fieldName,
                            RateTimeFieldNameBuilder.ExtensionName.TIME,
                        ).fieldName to TimeInYears.ZERO,
                        RateTimeFieldNameBuilder(
                            ValueType.COST,
                            TsetCostCalculationElementType.RAW_MATERIAL_OVERHEAD_COSTS.fieldName,
                            RateTimeFieldNameBuilder.ExtensionName.RATE,
                        ).fieldName to Rate.ZERO,
                        RateTimeFieldNameBuilder(
                            ValueType.COST,
                            TsetCostCalculationElementType.PURCHASE_PARTS_OVERHEAD_COSTS.fieldName,
                            RateTimeFieldNameBuilder.ExtensionName.RATE,
                        ).fieldName to Rate.ZERO,
                        "location" to Text("Germany"),
                        "callsPerYear" to Num(12.0.toBigDecimal()),
                        "peakUsableProductionVolumePerYear" to QuantityUnit(100_000.0.toBigDecimal()),
                        "averageUsableProductionVolumePerYear" to QuantityUnit(100_000.0.toBigDecimal()),
                        "shiftsPerDay" to Num(3.0.toBigDecimal()),
                        "procurementType" to ManufacturingType(ManufacturingType.Type.INHOUSE),
                        "lifeTime" to TimeInYears(8.0.toBigDecimal(), TimeInYearsUnit.YEAR),
                        "productionHoursPerYear" to Time(6800.0.toBigDecimal(), TimeUnits.HOUR),
                        "reuseOfScrap" to Bool(false),
                    ),
            )

        val step1 =
            addObject(
                "Step 1",
                Entities.MANUFACTURING_STEP,
                parent = calculation,
                parameters =
                    mapOf(
                        RateTimeFieldNameBuilder(
                            ValueType.COST,
                            TsetCostCalculationElementType.MANUFACTURING_OVERHEAD_COSTS.fieldName,
                            RateTimeFieldNameBuilder.ExtensionName.RATE,
                        ).fieldName to Rate.ZERO,
                        "lotFraction" to Rate(1.0.toBigDecimal()),
                        "scrapRate" to Rate(0.00.toBigDecimal()),
                        "partsPerCycle" to Pieces(1.0.toBigDecimal()),
                        "setupScrapParts" to QuantityUnit(0.0.toBigDecimal()),
                        "toolAllocationMode" to ToolAllocationMode(ToolAllocationMode.Mode.PAY_ONE),
                        "workInProgress" to Money(0.0.toBigDecimal()),
                        "utilizationRate" to Rate(0.8.toBigDecimal()),
                        "cycleTime" to CycleTime(22.14.toBigDecimal(), CycleTimeUnit.SECOND),
                        "setupIncludeOperators" to Bool(true),
                        "interestOnWorkInProgress" to Bool(false),
                    ),
            )

        // MDE 40/60
        addObject(
            "Machine 1",
            Entities.MACHINE,
            parent = step1,
            parameters =
                mapOf(
                    "age" to Time(0.0.toBigDecimal(), TimeUnits.YEAR),
                    "connectedLoad" to Power(2500.0.toBigDecimal(), PowerUnits.WATT),
                    "consumableRate" to Rate(0.01296.toBigDecimal()),
                    "depreciationTime" to Time(15.0.toBigDecimal(), TimeUnits.YEAR),
                    "investBase" to Money(250000.0.toBigDecimal()),
                    "investFundament" to Money(0.0.toBigDecimal()),
                    "investMisc" to Money(0.0.toBigDecimal()),
                    "investSetup" to Money(25000.0.toBigDecimal()),
                    "maintenanceRate" to Rate(0.03.toBigDecimal()),
                    "powerOnTimeRate" to Rate(0.5.toBigDecimal()),
                    "quantity" to Pieces(1.0.toBigDecimal()),
                    "requiredSpaceGross" to Area(32.0.toBigDecimal(), AreaUnits.QM),
                ),
        )

        addObject(
            "Material 1",
            Entities.MATERIAL,
            parent = step1,
            parameters =
                mapOf(
                    "quantity" to Num(0.025.toBigDecimal()),
                    "pricePerUnit" to Money(1.0.toBigDecimal()),
                    "reuseOfScrap" to Bool(false),
                    "scrapWeightPerPart" to QuantityUnit(0.0),
                    "netWeightPerPart" to QuantityUnit(0.025),
                ),
        )

        addObject(
            "Labor 1",
            Entities.LABOR,
            parent = step1,
            parameters =
                mapOf(
                    "requiredLabor" to Num(0.5.toBigDecimal()),
                    "costPerHour" to Money(0.012144956796480992425.toBigDecimal()),
                    "wagePerHour" to Money(0.006687398179416665.toBigDecimal()),
                    "burden" to Rate(0.8154.toBigDecimal()),
                ),
        )

        calculate(calculation)

        printResultJson(manufacturingEntityToSeperatedDto(calculation))

        validateResultFieldScaled("Manufacturing", "productionCosts", 0.06724, 5)
    }
}
