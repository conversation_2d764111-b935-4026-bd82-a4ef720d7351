package com.nu.bom.core.utils

import com.nu.bom.core.api.DISPLAY_DESIGNATION
import com.nu.bom.core.api.QUANTITY
import com.nu.bom.core.api.dtos.BomNodeDto
import com.nu.bom.core.api.dtos.CreateManufacturingResult
import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.BaseMaterial
import com.nu.bom.core.manufacturing.entities.BomEntry
import com.nu.bom.core.manufacturing.entities.CycleTimeStep
import com.nu.bom.core.manufacturing.entities.ElectronicComponent
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.entities.ManualManufacturingStep
import com.nu.bom.core.manufacturing.entities.ManualMaterialV2
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.SpecialDirectCost
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Date
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.EmissionUnits
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.manufacturing.fieldTypes.MaterialPriceType
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.NumericFieldResultWithUnit
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SpecialDirectCostType
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYearsUnit
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.testentities.TestMachine
import com.nu.bom.core.manufacturing.testentities.TestManufacturing
import com.nu.bom.core.manufacturing.testentities.TestManufacturingStep
import com.nu.bom.core.manufacturing.testentities.entityprovider.StepCreatorMaterial
import com.nu.bom.core.manufacturing.testentities.entityprovider.TestAssembly
import com.nu.bom.core.manufacturing.testentities.entityprovider.TestEntityProviderMultiLayer
import com.nu.bom.core.manufacturing.testentities.entityprovider.TestStepWithImportantParameter
import com.nu.bom.core.manufacturing.testentities.lazyloading.TestBomEntry
import com.nu.bom.core.model.BomEntryCreationData
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.NET_WEIGHT_PER_PART
import com.nu.bom.core.model.PartId
import com.nu.bom.core.model.toMongoID
import com.nu.bom.core.model.toMongoProjectId
import com.nu.bom.core.service.EntityCreationDataConversionService
import com.nu.bomrads.dto.ProjectCreationDTO
import com.tset.core.module.bom.calculation.CalculationCreateRootInput
import com.tset.core.module.bom.calculation.CalculationCreateSubInput
import com.tset.core.service.domain.Currency
import org.assertj.core.api.Assertions.assertThat
import org.bson.types.ObjectId
import java.math.BigDecimal
import kotlin.reflect.KClass
import kotlin.reflect.full.findAnnotation
import kotlin.reflect.full.hasAnnotation
import com.nu.bomrads.id.ProjectId as BomradsProjectId

interface SubParent {
    val subs: List<CalculationBuilder>

    fun withSub(sub: CalculationBuilder): SubParent

    fun withSubs(subs: List<CalculationBuilder>): SubParent
}

interface EntityParent {
    val children: List<EntityBuilder>

    fun withChild(child: EntityBuilder): EntityParent

    fun withChildren(children: List<EntityBuilder>): EntityParent
}

@Suppress("ktlint:standard:property-naming", "ktlint:standard:backing-property-naming")
class CalculationBuilder private constructor() :
    SubParent,
    EntityParent {
        private var _defaultProject = true
        private var _defaultPart = true

        private var _projectId: BomradsProjectId? = null
        private var _partId: PartId? = null
        private var _title: String = DEFAULT_TITLE
        private lateinit var _clazz: KClass<out BaseManufacturing>
        private var _fields: Map<String, FieldResultStar>? = null
        private var _children: MutableList<EntityBuilder> = mutableListOf()
        private var _subs: MutableList<CalculationBuilder> = mutableListOf()

        private var _defaultInputs: Map<String, FieldResultStar> = emptyMap()

        private var _bomEntryFields: Map<String, FieldResultStar> = DEFAULT_INPUTS_BOM_ENTRY.toMutableMap()
        private var _bomEntryClass: KClass<out ManufacturingEntity>? = null
        private var _displayCurrency = Currency.EUR

        // set by the entity creation process for subs
        private var _parentBomNodeId: BomNodeId? = null
        private var _parentEntityId: ObjectId? = null
        private var _parentBranchId: BranchId? = null

        // custom parent selector for subs
        private var _customParentSelector: EntitySelector? = null

        val parentBomNodeId get() = _parentBomNodeId ?: error("no parent configured")
        private val parentEntityId get() = _parentEntityId ?: error("no parent configured")
        private val parentBranchId get() = _parentBranchId

        val hasCustomParentSelector get() = _customParentSelector != null
        val customParentSelector get() = _customParentSelector ?: error("custom parentSelector is not configured")

        val isDefaultProject get() = _defaultProject
        val isDefaultPart get() = _defaultPart
        val isRoot get() = _parentBomNodeId == null

        private var _defaultProjectDto: ProjectCreationDTO? = null
        val projectId get() = _projectId ?: error("invalid call, default project is configured")
        val partId get() = _partId ?: error("invalid call, default part is configured")
        val title get() = _title
        val clazz get() = _clazz
        val fields
            get() =
                (_fields ?: getDefaultInputsOrNull(_clazz) ?: emptyMap()).mapValues {
                    it.value.apply {
                        source = FieldResult.SOURCE.I
                    }
                }
        override val children get() = _children.toList()
        override val subs get() = _subs.toList()
        private val bomEntryFields
            get() = _bomEntryFields.mapValues { it.value.apply { source = FieldResult.SOURCE.I } }.toMap()
        private val bomEntryClass get() = _bomEntryClass
        private val displayCurrency get() = _displayCurrency

        fun collectAllSubs(): List<CalculationBuilder> {
            val result = mutableListOf<CalculationBuilder>()
            result.addAll(subs)
            _children.forEach {
                it.collectAllSubs(result)
            }
            return result
        }

        fun findNewBomNodeId(creationResult: CreateManufacturingResult): BomNodeId =
            if (isRoot) {
                creationResult.bomNode.id.toMongoID()
            } else {
                creationResult.bomNode.subNodes[0]
                    .bomNodeId
                    .toMongoID()
            }

        val defaultProjectParameters
            get(): Pair<BomradsProjectId, String> {
                val project = _defaultProjectDto?.project ?: error("Default project is not configured")
                return project.id to project.key
            }

        val calculationCreateRootInput
            get() =
                CalculationCreateRootInput(
                    projectId = projectId.toMongoProjectId(),
                    title = title,
                    partId = partId,
                    fields =
                        fields.map { (name, result) ->
                            createFieldParameter(name, result)
                        },
                    clazz = clazz,
                    displayCurrency = displayCurrency,
                )

        val calculationCreateSubInput
            get() =
                CalculationCreateSubInput(
                    projectId = projectId.toMongoProjectId(),
                    title = title,
                    partId = partId,
                    fields =
                        fields.map { (name, result) ->
                            createFieldParameter(name, result)
                        },
                    clazz = clazz,
                    displayCurrency = displayCurrency,
                    parentBomData =
                        BomEntryCreationData(
                            bomNodeId = parentBomNodeId,
                            branchId = parentBranchId,
                            stepId = parentEntityId,
                            fields =
                                bomEntryFields.map { (name, result) ->
                                    createFieldParameter(name, result)
                                },
                            // TODO: needs further attention
                            path = emptyList(),
                            bomEntryClass = bomEntryClass?.simpleName,
                        ),
                    // TODO: needs further attention
                    partName = null,
                )

        fun withProject(projectId: BomradsProjectId) =
            apply {
                _projectId = projectId
                _defaultProject = false
            }

        fun withPart(partId: PartId) =
            apply {
                _partId = partId
                _defaultPart = false
            }

        fun withTitle(title: String) =
            apply {
                _title = title
            }

        fun withDefaultProject(projectCreationDTO: ProjectCreationDTO?): CalculationBuilder {
            this._defaultProjectDto = projectCreationDTO
            return this
        }

        fun withClass(clazz: KClass<out BaseManufacturing>) =
            apply {
                _clazz = clazz
                _defaultInputs = getDefaultInputsOrNull(clazz) ?: emptyMap()
            }

        fun withInput(fields: Map<String, FieldResultStar>) =
            apply {
                _fields = fields
            }

        override fun withChild(child: EntityBuilder) =
            apply {
                _children += child
            }

        override fun withChildren(children: List<EntityBuilder>) =
            apply {
                _children += children
            }

        override fun withSubs(subs: List<CalculationBuilder>) =
            apply {
                _subs += subs
            }

        override fun withSub(sub: CalculationBuilder) =
            apply {
                _subs += sub
            }

        fun withBomEntryInput(fields: Map<String, FieldResultStar>) =
            apply {
                _bomEntryFields = fields
            }

        fun withBomEntryClass(clazz: KClass<out ManufacturingEntity>) =
            apply {
                _bomEntryClass = clazz
            }

        fun withCustomParentSelector(selector: EntitySelector) =
            apply {
                _customParentSelector = selector
            }

        // called by the entity creation process for subs
        fun withParent(
            projectId: BomradsProjectId,
            bomNodeId: BomNodeId,
            branchId: BranchId?,
            parentId: ObjectId,
        ) = apply {
            _projectId = projectId
            _defaultProject = false
            _parentBomNodeId = bomNodeId
            _parentBranchId = branchId
            _parentEntityId = parentId
        }

        fun withParentBranch(branchId: BranchId?) =
            apply {
                _parentBranchId = branchId
            }

        companion object {
            const val DEFAULT_TITLE = "DefaultTitle"

            fun create(clazz: KClass<out BaseManufacturing>): CalculationBuilder = CalculationBuilder().withClass(clazz)

            fun calculationBuilder(builder: CalculationBuilder.() -> Unit) =
                CalculationBuilder()
                    .apply(builder)

            fun createManualTree(): CalculationBuilder =
                create(ManualManufacturing::class)
                    .withTitle("MANUAL")
                    .withChild(
                        EntityBuilder
                            .create(ManualManufacturingStep::class)
                            .withName("Step2")
                            .withChild(
                                EntityBuilder
                                    .create(ManualManufacturingStep::class)
                                    .withName("Step1")
                                    .withChildren(
                                        listOf(
                                            EntityBuilder
                                                .create(ManualMaterialV2::class)
                                                .withName("Material1")
                                                .withInput(
                                                    mapOf(
                                                        "netWeightPerPart" to QuantityUnit("7"),
                                                        "scrapWeightPerPart" to QuantityUnit("7"),
                                                        "grossWeightPerPart" to QuantityUnit("7"),
                                                        "materialBasePrice" to Money("1"),
                                                        "materialBaseCO2" to Emission(0.0, EmissionUnits.GRAM_CO2E),
                                                    ),
                                                ),
                                            EntityBuilder
                                                .create(ElectronicComponent::class)
                                                .withName("CPart1")
                                                .withInput(
                                                    mapOf(
                                                        "quantity" to Num("1"),
                                                        "pricePerUnit" to Money("1"),
                                                        "materialBasePrice" to Money("7"),
                                                        "materialBaseCO2" to Emission(0.0, EmissionUnits.GRAM_CO2E),
                                                    ),
                                                ),
                                        ),
                                    ),
                            ),
                    )

            fun createTestTree(): CalculationBuilder =
                create(TestManufacturing::class)
                    // only relevant if used as sub
                    .withBomEntryClass(TestBomEntry::class)
                    .withTitle("TEST")
                    .withChildren(
                        listOf(
                            EntityBuilder
                                .create(TestManufacturingStep::class)
                                .withName("Step2")
                                .withChildren(
                                    listOf(
                                        EntityBuilder
                                            .create(TestManufacturingStep::class)
                                            .withName("Step1"),
                                        EntityBuilder
                                            .create(TestMachine::class)
                                            .withName("Machine1"),
                                    ),
                                ),
                        ),
                    )

            fun createGeneratedTestTree(): CalculationBuilder =
                create(TestEntityProviderMultiLayer::class)
                    // only relevant if used as sub
                    .withBomEntryClass(TestBomEntry::class)
                    .withTitle("GENERATED-TEST")
        }
    }

@Suppress("ktlint:standard:property-naming", "ktlint:standard:backing-property-naming")
class EntityBuilder private constructor() :
    SubParent,
    EntityParent {
        private var _type: Entities? = null
        private var _clazz: KClass<out ManufacturingEntity>? = null
        private var _fields: Map<String, FieldResultStar>? = null
        private var _overwrites: Map<String, FieldResultStar> = emptyMap()
        private var _children: MutableList<EntityBuilder> = mutableListOf()
        private var _subs: MutableList<CalculationBuilder> = mutableListOf()

        private var _name: String? = null
        private var _isolated: Boolean? = null

        // set by the entity creation process
        private var _projectId: BomradsProjectId? = null
        private var _bomNodeId: BomNodeId? = null
        private var _branchId: BranchId? = null
        private var _parentId: ObjectId? = null

        // custom parent selector
        private var _customParentSelector: EntitySelector? = null

        private val defaultName get() = _clazz?.simpleName ?: _type?.name

        private val fields: Map<String, FieldResultStar>
            get() =
                (_fields ?: getDefaultInputsOrNull(_clazz) ?: emptyMap())
                    .toMutableMap()
                    .let { fields ->
                        if (_name != null) {
                            fields[DISPLAY_DESIGNATION] = Text(_name!!)
                        } else if (defaultName != null) {
                            fields.putIfAbsent(DISPLAY_DESIGNATION, Text(defaultName!!))
                        }
                        fields
                    }.mapValues { it.value.apply { source = FieldResult.SOURCE.I } }

        private val overwrites get() = _overwrites

        val type get() = _type ?: error("could not determine entity type")
        val clazz get() = _clazz
        val name get() = fields[DISPLAY_DESIGNATION]?.res as String?
        override val children get() = _children.toList()
        override val subs get() = _subs.toList()
        val isolated get() = _isolated

        val projectId get() = _projectId ?: error("projectId was expected to be set by the entity creation chain")
        val bomNodeId get() = _bomNodeId ?: error("bomNodeId was expected to be set by the entity creation chain")
        val branchId get() = _branchId
        val parentId get() = _parentId ?: error("parentId was expected to be set by the entity creation chain")

        val hasCustomParentSelector get() = _customParentSelector != null
        val customParentSelector get() = _customParentSelector ?: error("custom parentSelector is not configured")

        fun collectAllSubs(collector: MutableList<CalculationBuilder>) {
            collector.addAll(subs)
            _children.forEach {
                it.collectAllSubs(collector)
            }
        }

        val entityCreationData
            get() =
                EntityCreationDataConversionService.EntityCreationData(
                    bomNodeId = bomNodeId,
                    branchId = branchId,
                    parentId = parentId,
                    projectId = projectId.toMongoProjectId(),
                    childBomNodeId = null,
                    items =
                        listOf(
                            EntityCreationDataConversionService.EntityCreationData.ItemData(
                                entityType = type,
                                entityClass = clazz,
                                // TODO: needs further attention
                                masterDataSelector = null,
                                fields = fields,
                                overwrites = overwrites,
                                isolated = isolated ?: false,
                            ),
                        ),
                )

        fun withType(type: Entities) =
            apply {
                if (_clazz != null && _type != null && _type != type) {
                    error(
                        "Invalid type configuration ($type) " +
                            "a different type ($_type) has already been inferred from class=$_clazz",
                    )
                }
                _type = type
            }

        fun withClass(clazz: KClass<out ManufacturingEntity>) =
            apply {
                _clazz = clazz
                if (clazz.hasAnnotation<EntityType>()) {
                    val inferredType = clazz.findAnnotation<EntityType>()!!.name
                    if (_type != null && _type != inferredType) {
                        error("Previously configured type=$_type differs from inferred type=$inferredType")
                    }
                    _type = inferredType
                }
            }

        fun withName(name: String) =
            apply {
                _name = name
            }

        fun withIsolated() =
            apply {
                _isolated = true
            }

        fun withInput(fields: Map<String, FieldResultStar>) =
            apply {
                _fields = fields
            }

        fun withOverwrites(fields: Map<String, FieldResultStar>) =
            apply {
                _overwrites = fields
            }

        override fun withChild(child: EntityBuilder) =
            apply {
                _children += child
            }

        override fun withChildren(children: List<EntityBuilder>) =
            apply {
                _children += children
            }

        override fun withSubs(subs: List<CalculationBuilder>) =
            apply {
                _subs += subs
            }

        override fun withSub(sub: CalculationBuilder) =
            apply {
                _subs += sub
            }

        // called by the entity creation process
        fun withParent(
            projectId: BomradsProjectId,
            bomNodeId: BomNodeId,
            branchId: BranchId?,
            parentId: ObjectId,
        ) = apply {
            _projectId = projectId
            _bomNodeId = bomNodeId
            _branchId = branchId
            _parentId = parentId
        }

        fun withBranch(branchId: BranchId?) =
            apply {
                _branchId = branchId
            }

        companion object {
            fun create(clazz: KClass<out ManufacturingEntity>): EntityBuilder = EntityBuilder().withClass(clazz)

            fun create(type: Entities): EntityBuilder = EntityBuilder().withType(type)

            fun entityBuilder(builder: EntityBuilder.() -> Unit) =
                EntityBuilder()
                    .apply(builder)
        }
    }

// TODO: Can we get rid of this?
private fun createFieldParameter(
    name: String,
    result: FieldResultStar,
): FieldParameter =
    FieldParameter(
        name = name,
        type = result.javaClass.simpleName,
        unit = if (result is NumericFieldResultWithUnit<*, *>) result.unitName else null,
        value = result.res,
        systemValue = result.systemValue,
        source = result.source.name,
        // Meh.
        denominatorUnit = null,
    )

typealias EntitySelector = (BomNodeDto) -> ObjectId

enum class EntityDiffType {
    TYPE,
    CLASS,
    DISPLAY_NAME,
    CHILDREN,
}

/** Structural assertion utils */
fun assertSameStructure(
    expected: ManufacturingEntity,
    actual: ManufacturingEntity,
    diffHandler: EntityDiffHandler = ::defaultErrorDiffHandler,
    ignoreBomEntries: Boolean = false,
    ignoreExchangeRates: Boolean = false,
) {
    if (expected.getEntityType() != actual.getEntityType()) {
        diffHandler(
            expected,
            actual,
            EntityDiffType.TYPE,
            "entity type: expected=${expected.getEntityType()}, actual=${actual.getEntityType()}",
        )
    } else if (expected.getEntityClass() != actual.getEntityClass()) {
        diffHandler(
            expected,
            actual,
            EntityDiffType.CLASS,
            "entity class: expected=${expected.getEntityClass()}, actual=${actual.getEntityClass()}",
        )
    } else if (expected.displayName != actual.displayName) {
        diffHandler(
            expected,
            actual,
            EntityDiffType.DISPLAY_NAME,
            "display name: expected=${expected.displayName}, actual=${actual.displayName}",
        )
    }

    assertSameChildStructure(expected, actual, diffHandler, ignoreBomEntries, ignoreExchangeRates)
}

/** Structural assertion utils */
private fun assertSameChildStructure(
    expected: ManufacturingEntity,
    actual: ManufacturingEntity,
    diffHandler: EntityDiffHandler,
    ignoreBomEntries: Boolean = false,
    ignoreExchangeRates: Boolean = false,
) {
    val (expectedChildren, actualChildren) =
        Pair(
            expected.children.filterNot {
                ignoreEntity(ignoreBomEntries, it, Entities.BOM_ENTRY) ||
                    ignoreEntity(
                        ignoreExchangeRates,
                        it,
                        Entities.EXCHANGE_RATES,
                    )
            },
            actual.children.filterNot {
                ignoreEntity(ignoreBomEntries, it, Entities.BOM_ENTRY) ||
                    ignoreEntity(
                        ignoreExchangeRates,
                        it,
                        Entities.EXCHANGE_RATES,
                    )
            },
        )

    if (expectedChildren.size != actualChildren.size) {
        diffHandler(
            expected,
            actual,
            EntityDiffType.CHILDREN,
            "children count: expected=${expectedChildren.size}, actual=${actualChildren.size}",
        )
    } else {
        expectedChildren.forEach { expectedChild ->
            // engine recalc does not guarantee retained order
            val actualChild =
                actualChildren
                    .filter {
                        it.displayName == expectedChild.displayName &&
                            it.entityRef == expectedChild.entityRef &&
                            it.name == expectedChild.name
                    }
            when {
                actualChild.size == 1 ->
                    assertSameStructure(
                        expectedChild,
                        actualChild.single(),
                        diffHandler,
                        ignoreBomEntries,
                    )

                actualChild.size > 1 ->
                    error(
                        "(displayName + entityRef + name) duplication found while comparing children of " +
                            "expected=$expected, actual=$actual." +
                            "This utility does not support entities with non-unique (displayNames & entityRef & name)." +
                            "Please make one of these values unique by modifying the test structure, or compare manually.",
                    )

                else -> error("unexpected error - actual is empty: expected=$expected, actual=$actual")
            }
        }
    }
}

private fun ignoreEntity(
    ignoreBomEntries: Boolean,
    it: ManufacturingEntity,
    entity: Entities,
) = ignoreBomEntries && it.getEntityTypeAnnotation() == entity

/** Assertion utils for calculation builder */
fun assertDefaultManualStructure(root: ManufacturingEntity) {
    // assert entity structure
    assertThat(root).satisfies({
        assertThat(it::class).isEqualTo(ManualManufacturing::class)
        assertThat(it.displayName).isEqualTo("ManualManufacturing")
        assertThat(it.getFieldResult("costPerPart")).isNotNull

        assertThat(it.children.find { child -> child::class == ManualManufacturingStep::class })
            .isNotNull
            .satisfies({ step2 ->
                assertThat(step2!!.displayName).isEqualTo("Step2")
                assertThat(step2.getFieldResult("costPerPart")).isNotNull

                assertThat(step2.children.find { child -> child::class == ManualManufacturingStep::class })
                    .isNotNull
                    .satisfies({ step1 ->
                        assertThat(step1!!.displayName).isEqualTo("Step1")
                        assertThat(step1.getFieldResult("costPerPart")).isNotNull

                        assertThat(step1.children.find { child -> child::class == ManualMaterialV2::class })
                            .isNotNull
                            .satisfies({ material ->
                                assertThat(material!!.displayName).isEqualTo("Material1")
                                assertThat(material.getFieldResult("costPerPart")).isNotNull
                            })
                        assertThat(step1.children.find { child -> child::class == ElectronicComponent::class })
                            .isNotNull
                            .satisfies({ material ->
                                assertThat(material!!.displayName).isEqualTo("CPart1")
                                assertThat(material.getFieldResult("costPerPart")).isNotNull
                            })
                    })
            })
    })
}

/** Assertion utils for calculation builder */
fun assertDefaultTestStructure(root: ManufacturingEntity) {
    // assert entity structure
    assertThat(root).satisfies({
        assertThat(it::class).isEqualTo(TestManufacturing::class)
        assertThat(it.displayName).isEqualTo("TestManufacturing")
        assertThat(it.getFieldResult("totalValues")).isNotNull
        assertThat(it.getFieldResult("subTotal")).isNotNull
    })
    assertThat(root.children).singleElement().satisfies({ step2 ->

        assertThat(step2::class).isEqualTo(TestManufacturingStep::class)
        assertThat(step2.displayName).isEqualTo("Step2")

        assertThat(step2.children).satisfies({ step2Children ->

            val step1 = step2Children.find { it::class == TestManufacturingStep::class }
            assertThat(step1).isNotNull.satisfies({
                assertThat(it!!.displayName).isEqualTo("Step1")
            })

            val machine = step2Children.find { it::class == TestMachine::class }
            assertThat(machine).isNotNull.satisfies({
                assertThat(it!!.displayName).isEqualTo("Machine1")
            })
        })
    })
}

/** Assertion utils for calculation builder */
fun assertDefaultGeneratedTestStructure(root: ManufacturingEntity) {
    // assert entity structure
    assertThat(root).satisfies({
        assertThat(it::class).isEqualTo(TestEntityProviderMultiLayer::class)
        assertThat(it.name).isEqualTo(TestEntityProviderMultiLayer::class.simpleName)
        assertThat(it.getFieldResult("importantParameterAggregation")).isNotNull

        assertThat(it.children.find { child -> child::class == StepCreatorMaterial::class })
            .isNotNull
            .satisfies({ processedMaterial ->
                assertThat(processedMaterial!!.name).isEqualTo("material")

                assertThat(processedMaterial.children.find { child -> child::class == TestAssembly::class })
                    .isNotNull
                    .satisfies({ assemblyStep ->
                        assertThat(assemblyStep!!.name).isEqualTo("assembly-stepparam-1")

                        assertThat(assemblyStep.children.find { child -> child::class == TestStepWithImportantParameter::class })
                            .isNotNull
                            .satisfies({ step ->
                                assertThat(step!!.name).isEqualTo("step")

                                assertThat(step.children.find { child -> child::class == TestStepWithImportantParameter::class })
                                    .isNotNull
                                    .satisfies({ cleaningStep ->
                                        assertThat(cleaningStep!!.name).isEqualTo("cleaning-step")
                                    })
                            })
                    })
            })
    })
}

typealias EntityDiffHandler = (ManufacturingEntity, ManufacturingEntity, EntityDiffType, String?) -> Unit

fun defaultErrorDiffHandler(
    expected: ManufacturingEntity,
    actual: ManufacturingEntity,
    diffType: EntityDiffType,
    hint: String? = null,
): Unit =
    throw AssertionError(
        "Difference detected: " +
            "expected=$expected, " +
            "actual=$actual " +
            "diffType=$diffType " +
            "hint=$hint",
    )

fun getDefaultInputs(clazz: KClass<out ManufacturingEntity>): Map<String, FieldResultStar> =
    getDefaultInputsOrNull(clazz) ?: error("no default input mapping exists for class=${clazz.simpleName}")

fun getDefaultInputsOrNull(clazz: KClass<out ManufacturingEntity>? = null): Map<String, FieldResultStar>? =
    when (clazz) {
        // Standard entities
        ManualManufacturing::class -> DEFAULT_INPUTS_MANUAL_MANUFACTURING
        ManualManufacturingStep::class -> DEFAULT_INPUTS_MANUAL_MANUFACTURING_STEP
        ManualMaterialV2::class -> DEFAULT_INPUTS_MANUAL_MATERIAL
        ElectronicComponent::class -> DEFAULT_INPUTS_C_PART
        CycleTimeStep::class -> DEFAULT_INPUTS_MANUAL_CYCLE_TIME_STEP
        BomEntry::class -> DEFAULT_INPUTS_BOM_ENTRY
        SpecialDirectCost::class -> DEFAULT_INPUTS_SPECIAL_DIRECT_COST

        // Test entities
        TestManufacturing::class -> DEFAULT_INPUTS_TEST_MANUFACTURING
        TestManufacturingStep::class -> DEFAULT_INPUTS_TEST_MANUFACTURING_STEP
        TestBomEntry::class -> DEFAULT_INPUTS_TEST_BOM_ENTRY
        TestMachine::class -> DEFAULT_INPUTS_TEST_MACHINE

        else -> null
    }

/** Standard entity inputs */

private val DEFAULT_INPUTS_MANUAL_MANUFACTURING =
    mapOf(
        "lifeTime" to TimeInYears(10.0, TimeInYearsUnit.YEAR),
        "location" to Text("tset.ref.classification.austria"),
        "averageUsableProductionVolumePerYear" to QuantityUnit(1000.0),
        "peakUsableProductionVolumePerYear" to QuantityUnit(1200.0),
        "dimension" to Dimension(Dimension.Selection.NUMBER),
        "costUnit" to Text("PIECE"),
        "quantityUnit" to Text("PIECE"),
        "procurementType" to ManufacturingType(ManufacturingType.Type.PURCHASE),
        "calculationDate" to Date.of(2024, 1, 1),
    )

private val DEFAULT_INPUTS_MANUAL_MANUFACTURING_STEP = emptyMap<String, FieldResultStar>()

private val DEFAULT_INPUTS_MANUAL_MATERIAL =
    mapOf(
        NET_WEIGHT_PER_PART to QuantityUnit(1.0),
        "scrapWeightPerPart" to QuantityUnit(0.0),
        "lossRate" to Rate(0.0),
        "materialBasePrice" to Money(10.0),
        "materialPriceType" to MaterialPriceType(MaterialPriceType.Selection.SIMPLE_PRICE),
        "materialRecyclingPrice" to Money(0.0),
    )

private val DEFAULT_INPUTS_MANUAL_CYCLE_TIME_STEP =
    mapOf(
        "time" to Time(1.0, TimeUnits.SECOND),
    )

private val DEFAULT_INPUTS_C_PART =
    mapOf(
        ElectronicComponent::quantity.name to QuantityUnit(1.0),
        ElectronicComponent::pricePerUnit.name to Money(20.0),
        BaseMaterial::cO2PerUnit.name to Emission(0.0, EmissionUnits.KILOGRAM_CO2E),
    )

private val DEFAULT_INPUTS_BOM_ENTRY =
    mapOf(
        QUANTITY to Pieces.ONE,
    )

private val DEFAULT_INPUTS_SPECIAL_DIRECT_COST =
    mapOf(
        "specialDirectCostType" to SpecialDirectCostType(SpecialDirectCostType.Selection.DEVELOPMENT),
        "invest" to Money(BigDecimal("100000")),
        "allocationRate" to Rate(5.0),
        "co2Invest" to Emission(BigDecimal("5"), EmissionUnits.KILOGRAM_CO2E),
    )

/** Test entity inputs */

private val DEFAULT_INPUTS_TEST_MANUFACTURING =
    mapOf(
        "peakUsableProductionVolumePerYear" to QuantityUnit(12000.0),
        "procurementType" to ManufacturingType(ManufacturingType.Type.PURCHASE),
    )

private val DEFAULT_INPUTS_TEST_MANUFACTURING_STEP =
    mapOf(
        "stepWeight" to Rate(1.5),
    )

private val DEFAULT_INPUTS_TEST_BOM_ENTRY =
    mapOf(
        BomEntry::quantity.name to QuantityUnit(BigDecimal.ONE),
    )

private val DEFAULT_INPUTS_TEST_MACHINE =
    mapOf(
        "baseRate" to Rate(1.05),
        "baseValue" to Money(500.0),
    )
