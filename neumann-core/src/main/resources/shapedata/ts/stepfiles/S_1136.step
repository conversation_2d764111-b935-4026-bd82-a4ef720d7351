ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 
'G:/Shared drives/TIP/Validation/SMF KION/Tset SMF Shapes/S_1136.step',

/* time_stamp */ '2021-07-09T14:44:47+02:00',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v18.1',
/* originating_system */ 'Autodesk Translation Framework v10.9.0.1377',

/* authorisation */ '');

FILE_SCHEMA (('AUTOMOTIVE_DESIGN { 1 0 10303 214 3 1 1 }'));
ENDSEC;

DATA;
#10=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#13),#1120);
#11=SHAPE_REPRESENTATION_RELATIONSHIP('SRR','None',#1127,#12);
#12=ADVANCED_BREP_SHAPE_REPRESENTATION('',(#14),#1119);
#13=STYLED_ITEM('',(#1136),#14);
#14=MANIFOLD_SOLID_BREP('Body1',#606);
#15=B_SPLINE_SURFACE_WITH_KNOTS('',3,3,((#923,#924,#925,#926),(#927,#928,
#929,#930),(#931,#932,#933,#934),(#935,#936,#937,#938)),.UNSPECIFIED.,.F.,
 .F.,.F.,(4,4),(4,4),(1.05354269164025,1.34660195017912),(0.,0.4),
 .UNSPECIFIED.);
#16=B_SPLINE_SURFACE_WITH_KNOTS('',3,3,((#977,#978,#979,#980),(#981,#982,
#983,#984),(#985,#986,#987,#988),(#989,#990,#991,#992)),.UNSPECIFIED.,.F.,
 .F.,.F.,(4,4),(4,4),(2.24014507897379,2.53757054095306),(0.,0.4),
 .UNSPECIFIED.);
#17=B_SPLINE_SURFACE_WITH_KNOTS('',3,3,((#1012,#1013,#1014,#1015),(#1016,
#1017,#1018,#1019),(#1020,#1021,#1022,#1023),(#1024,#1025,#1026,#1027)),
 .UNSPECIFIED.,.F.,.F.,.F.,(4,4),(4,4),(0.,0.293649468376935),(0.,0.4),
 .UNSPECIFIED.);
#18=B_SPLINE_SURFACE_WITH_KNOTS('',3,3,((#1034,#1035,#1036,#1037),(#1038,
#1039,#1040,#1041),(#1042,#1043,#1044,#1045),(#1046,#1047,#1048,#1049)),
 .UNSPECIFIED.,.F.,.F.,.F.,(4,4),(4,4),(0.,3.99999999999816),(-0.158539356441414,
-6.93889390390404E-17),.UNSPECIFIED.);
#19=B_SPLINE_SURFACE_WITH_KNOTS('',3,3,((#1072,#1073,#1074,#1075),(#1076,
#1077,#1078,#1079),(#1080,#1081,#1082,#1083),(#1084,#1085,#1086,#1087)),
 .UNSPECIFIED.,.F.,.F.,.F.,(4,4),(4,4),(0.,0.297512167510921),(0.,0.4),
 .UNSPECIFIED.);
#20=B_SPLINE_SURFACE_WITH_KNOTS('',3,3,((#1094,#1095,#1096,#1097),(#1098,
#1099,#1100,#1101),(#1102,#1103,#1104,#1105),(#1106,#1107,#1108,#1109)),
 .UNSPECIFIED.,.F.,.F.,.F.,(4,4),(4,4),(0.,3.99999999999843),(-0.16402588519474,
0.),.UNSPECIFIED.);
#21=B_SPLINE_CURVE_WITH_KNOTS('',3,(#893,#894,#895,#896),.UNSPECIFIED.,
 .F.,.F.,(4,4),(0.,0.508774937194666),.UNSPECIFIED.);
#22=B_SPLINE_CURVE_WITH_KNOTS('',3,(#898,#899,#900,#901),.UNSPECIFIED.,
 .F.,.F.,(4,4),(8.67361737987723E-17,0.16402588519474),.UNSPECIFIED.);
#23=B_SPLINE_CURVE_WITH_KNOTS('',3,(#903,#904,#905,#906),.UNSPECIFIED.,
 .F.,.F.,(4,4),(0.,0.510060423748692),.UNSPECIFIED.);
#24=B_SPLINE_CURVE_WITH_KNOTS('',3,(#910,#911,#912,#913),.UNSPECIFIED.,
 .F.,.F.,(4,4),(0.,0.509978319334027),.UNSPECIFIED.);
#25=B_SPLINE_CURVE_WITH_KNOTS('',3,(#915,#916,#917,#918),.UNSPECIFIED.,
 .F.,.F.,(4,4),(-0.158539356441414,-6.93889390390404E-17),.UNSPECIFIED.);
#26=B_SPLINE_CURVE_WITH_KNOTS('',3,(#919,#920,#921,#922),.UNSPECIFIED.,
 .F.,.F.,(4,4),(0.,0.509807789043849),.UNSPECIFIED.);
#27=B_SPLINE_CURVE_WITH_KNOTS('',3,(#942,#943,#944,#945),.UNSPECIFIED.,
 .F.,.F.,(4,4),(0.,0.508774937194666),.UNSPECIFIED.);
#28=B_SPLINE_CURVE_WITH_KNOTS('',3,(#997,#998,#999,#1000),.UNSPECIFIED.,
 .F.,.F.,(4,4),(0.,0.509978319334027),.UNSPECIFIED.);
#29=B_SPLINE_CURVE_WITH_KNOTS('',3,(#1030,#1031,#1032,#1033),
 .UNSPECIFIED.,.F.,.F.,(4,4),(0.,0.509807789043849),.UNSPECIFIED.);
#30=B_SPLINE_CURVE_WITH_KNOTS('',3,(#1050,#1051,#1052,#1053),
 .UNSPECIFIED.,.F.,.F.,(4,4),(8.67361737987607E-17,0.158539356441413),
 .UNSPECIFIED.);
#31=B_SPLINE_CURVE_WITH_KNOTS('',3,(#1090,#1091,#1092,#1093),
 .UNSPECIFIED.,.F.,.F.,(4,4),(0.,0.510060423748692),.UNSPECIFIED.);
#32=B_SPLINE_CURVE_WITH_KNOTS('',3,(#1110,#1111,#1112,#1113),
 .UNSPECIFIED.,.F.,.F.,(4,4),(8.67361737987723E-17,0.16402588519474),
 .UNSPECIFIED.);
#33=FACE_BOUND('',#87,.T.);
#34=FACE_BOUND('',#117,.T.);
#35=CYLINDRICAL_SURFACE('',#626,4.);
#36=CYLINDRICAL_SURFACE('',#635,8.);
#37=CYLINDRICAL_SURFACE('',#638,8.);
#38=CYLINDRICAL_SURFACE('',#649,7.5);
#39=CYLINDRICAL_SURFACE('',#651,0.999999999999992);
#40=CYLINDRICAL_SURFACE('',#653,0.999999999999991);
#41=CYLINDRICAL_SURFACE('',#655,10.0000000000001);
#42=CYLINDRICAL_SURFACE('',#658,1.);
#43=CYLINDRICAL_SURFACE('',#660,1.);
#44=CYLINDRICAL_SURFACE('',#662,9.99999999999982);
#45=CYLINDRICAL_SURFACE('',#665,4.);
#46=FACE_OUTER_BOUND('',#81,.T.);
#47=FACE_OUTER_BOUND('',#82,.T.);
#48=FACE_OUTER_BOUND('',#83,.T.);
#49=FACE_OUTER_BOUND('',#84,.T.);
#50=FACE_OUTER_BOUND('',#85,.T.);
#51=FACE_OUTER_BOUND('',#86,.T.);
#52=FACE_OUTER_BOUND('',#88,.T.);
#53=FACE_OUTER_BOUND('',#89,.T.);
#54=FACE_OUTER_BOUND('',#90,.T.);
#55=FACE_OUTER_BOUND('',#91,.T.);
#56=FACE_OUTER_BOUND('',#92,.T.);
#57=FACE_OUTER_BOUND('',#93,.T.);
#58=FACE_OUTER_BOUND('',#94,.T.);
#59=FACE_OUTER_BOUND('',#95,.T.);
#60=FACE_OUTER_BOUND('',#96,.T.);
#61=FACE_OUTER_BOUND('',#97,.T.);
#62=FACE_OUTER_BOUND('',#98,.T.);
#63=FACE_OUTER_BOUND('',#99,.T.);
#64=FACE_OUTER_BOUND('',#100,.T.);
#65=FACE_OUTER_BOUND('',#101,.T.);
#66=FACE_OUTER_BOUND('',#102,.T.);
#67=FACE_OUTER_BOUND('',#103,.T.);
#68=FACE_OUTER_BOUND('',#104,.T.);
#69=FACE_OUTER_BOUND('',#105,.T.);
#70=FACE_OUTER_BOUND('',#106,.T.);
#71=FACE_OUTER_BOUND('',#107,.T.);
#72=FACE_OUTER_BOUND('',#108,.T.);
#73=FACE_OUTER_BOUND('',#109,.T.);
#74=FACE_OUTER_BOUND('',#110,.T.);
#75=FACE_OUTER_BOUND('',#111,.T.);
#76=FACE_OUTER_BOUND('',#112,.T.);
#77=FACE_OUTER_BOUND('',#113,.T.);
#78=FACE_OUTER_BOUND('',#114,.T.);
#79=FACE_OUTER_BOUND('',#115,.T.);
#80=FACE_OUTER_BOUND('',#116,.T.);
#81=EDGE_LOOP('',(#383,#384,#385,#386,#387,#388));
#82=EDGE_LOOP('',(#389,#390,#391,#392));
#83=EDGE_LOOP('',(#393,#394,#395,#396));
#84=EDGE_LOOP('',(#397,#398,#399,#400));
#85=EDGE_LOOP('',(#401,#402,#403,#404));
#86=EDGE_LOOP('',(#405,#406,#407,#408,#409,#410,#411,#412));
#87=EDGE_LOOP('',(#413));
#88=EDGE_LOOP('',(#414,#415,#416,#417));
#89=EDGE_LOOP('',(#418,#419,#420,#421));
#90=EDGE_LOOP('',(#422,#423,#424,#425));
#91=EDGE_LOOP('',(#426,#427,#428,#429,#430,#431,#432,#433));
#92=EDGE_LOOP('',(#434,#435,#436,#437));
#93=EDGE_LOOP('',(#438,#439,#440,#441,#442,#443));
#94=EDGE_LOOP('',(#444,#445,#446,#447,#448,#449));
#95=EDGE_LOOP('',(#450,#451,#452,#453,#454));
#96=EDGE_LOOP('',(#455,#456,#457,#458,#459,#460));
#97=EDGE_LOOP('',(#461,#462,#463,#464,#465));
#98=EDGE_LOOP('',(#466,#467,#468,#469));
#99=EDGE_LOOP('',(#470,#471,#472,#473));
#100=EDGE_LOOP('',(#474,#475,#476,#477));
#101=EDGE_LOOP('',(#478,#479,#480,#481));
#102=EDGE_LOOP('',(#482,#483,#484,#485));
#103=EDGE_LOOP('',(#486,#487,#488,#489));
#104=EDGE_LOOP('',(#490,#491,#492,#493,#494));
#105=EDGE_LOOP('',(#495,#496,#497,#498));
#106=EDGE_LOOP('',(#499,#500,#501,#502));
#107=EDGE_LOOP('',(#503,#504,#505,#506));
#108=EDGE_LOOP('',(#507,#508,#509,#510));
#109=EDGE_LOOP('',(#511,#512,#513,#514));
#110=EDGE_LOOP('',(#515,#516,#517,#518));
#111=EDGE_LOOP('',(#519,#520,#521,#522));
#112=EDGE_LOOP('',(#523,#524,#525,#526,#527));
#113=EDGE_LOOP('',(#528,#529,#530,#531));
#114=EDGE_LOOP('',(#532,#533,#534,#535));
#115=EDGE_LOOP('',(#536,#537,#538,#539,#540,#541,#542,#543));
#116=EDGE_LOOP('',(#544,#545,#546,#547,#548,#549,#550,#551));
#117=EDGE_LOOP('',(#552));
#118=CIRCLE('',#621,1.);
#119=CIRCLE('',#622,1.);
#120=CIRCLE('',#624,4.);
#121=CIRCLE('',#625,8.);
#122=CIRCLE('',#627,4.);
#123=CIRCLE('',#631,9.99999999999982);
#124=CIRCLE('',#632,10.0000000000001);
#125=CIRCLE('',#633,7.5);
#126=CIRCLE('',#636,8.);
#127=CIRCLE('',#640,0.999999999999992);
#128=CIRCLE('',#641,0.999999999999991);
#129=CIRCLE('',#643,0.999999999999991);
#130=CIRCLE('',#644,0.999999999999992);
#131=CIRCLE('',#647,1.);
#132=CIRCLE('',#648,1.);
#133=CIRCLE('',#650,7.5);
#134=CIRCLE('',#656,10.0000000000001);
#135=CIRCLE('',#663,9.99999999999982);
#136=LINE('',#822,#191);
#137=LINE('',#824,#192);
#138=LINE('',#828,#193);
#139=LINE('',#831,#194);
#140=LINE('',#834,#195);
#141=LINE('',#838,#196);
#142=LINE('',#842,#197);
#143=LINE('',#845,#198);
#144=LINE('',#847,#199);
#145=LINE('',#849,#200);
#146=LINE('',#850,#201);
#147=LINE('',#853,#202);
#148=LINE('',#854,#203);
#149=LINE('',#857,#204);
#150=LINE('',#861,#205);
#151=LINE('',#863,#206);
#152=LINE('',#865,#207);
#153=LINE('',#868,#208);
#154=LINE('',#873,#209);
#155=LINE('',#875,#210);
#156=LINE('',#876,#211);
#157=LINE('',#878,#212);
#158=LINE('',#883,#213);
#159=LINE('',#885,#214);
#160=LINE('',#887,#215);
#161=LINE('',#888,#216);
#162=LINE('',#891,#217);
#163=LINE('',#908,#218);
#164=LINE('',#940,#219);
#165=LINE('',#946,#220);
#166=LINE('',#948,#221);
#167=LINE('',#950,#222);
#168=LINE('',#954,#223);
#169=LINE('',#960,#224);
#170=LINE('',#963,#225);
#171=LINE('',#966,#226);
#172=LINE('',#968,#227);
#173=LINE('',#969,#228);
#174=LINE('',#970,#229);
#175=LINE('',#975,#230);
#176=LINE('',#994,#231);
#177=LINE('',#995,#232);
#178=LINE('',#1001,#233);
#179=LINE('',#1005,#234);
#180=LINE('',#1007,#235);
#181=LINE('',#1008,#236);
#182=LINE('',#1010,#237);
#183=LINE('',#1029,#238);
#184=LINE('',#1056,#239);
#185=LINE('',#1057,#240);
#186=LINE('',#1061,#241);
#187=LINE('',#1063,#242);
#188=LINE('',#1065,#243);
#189=LINE('',#1067,#244);
#190=LINE('',#1089,#245);
#191=VECTOR('',#671,10.);
#192=VECTOR('',#672,10.);
#193=VECTOR('',#675,10.);
#194=VECTOR('',#678,10.);
#195=VECTOR('',#681,10.);
#196=VECTOR('',#684,10.);
#197=VECTOR('',#689,10.);
#198=VECTOR('',#692,10.);
#199=VECTOR('',#695,10.);
#200=VECTOR('',#696,10.);
#201=VECTOR('',#697,10.);
#202=VECTOR('',#700,10.);
#203=VECTOR('',#701,10.);
#204=VECTOR('',#704,10.);
#205=VECTOR('',#707,10.);
#206=VECTOR('',#708,10.);
#207=VECTOR('',#709,10.);
#208=VECTOR('',#712,10.);
#209=VECTOR('',#717,10.);
#210=VECTOR('',#718,10.);
#211=VECTOR('',#719,10.);
#212=VECTOR('',#722,10.);
#213=VECTOR('',#727,10.);
#214=VECTOR('',#728,10.);
#215=VECTOR('',#729,10.);
#216=VECTOR('',#730,10.);
#217=VECTOR('',#733,10.);
#218=VECTOR('',#734,10.);
#219=VECTOR('',#735,10.);
#220=VECTOR('',#736,10.);
#221=VECTOR('',#739,10.);
#222=VECTOR('',#740,10.);
#223=VECTOR('',#743,10.);
#224=VECTOR('',#750,10.);
#225=VECTOR('',#753,10.);
#226=VECTOR('',#756,10.);
#227=VECTOR('',#757,10.);
#228=VECTOR('',#758,10.);
#229=VECTOR('',#759,10.);
#230=VECTOR('',#764,10.);
#231=VECTOR('',#767,10.);
#232=VECTOR('',#768,10.);
#233=VECTOR('',#769,10.);
#234=VECTOR('',#774,7.5);
#235=VECTOR('',#777,10.);
#236=VECTOR('',#778,10.);
#237=VECTOR('',#781,10.);
#238=VECTOR('',#784,10.);
#239=VECTOR('',#787,10.);
#240=VECTOR('',#788,10.);
#241=VECTOR('',#793,10.);
#242=VECTOR('',#796,10.);
#243=VECTOR('',#799,10.);
#244=VECTOR('',#802,10.);
#245=VECTOR('',#811,10.);
#246=VERTEX_POINT('',#820);
#247=VERTEX_POINT('',#821);
#248=VERTEX_POINT('',#823);
#249=VERTEX_POINT('',#825);
#250=VERTEX_POINT('',#827);
#251=VERTEX_POINT('',#829);
#252=VERTEX_POINT('',#833);
#253=VERTEX_POINT('',#835);
#254=VERTEX_POINT('',#837);
#255=VERTEX_POINT('',#841);
#256=VERTEX_POINT('',#843);
#257=VERTEX_POINT('',#848);
#258=VERTEX_POINT('',#852);
#259=VERTEX_POINT('',#856);
#260=VERTEX_POINT('',#858);
#261=VERTEX_POINT('',#860);
#262=VERTEX_POINT('',#862);
#263=VERTEX_POINT('',#864);
#264=VERTEX_POINT('',#866);
#265=VERTEX_POINT('',#869);
#266=VERTEX_POINT('',#872);
#267=VERTEX_POINT('',#874);
#268=VERTEX_POINT('',#881);
#269=VERTEX_POINT('',#882);
#270=VERTEX_POINT('',#884);
#271=VERTEX_POINT('',#886);
#272=VERTEX_POINT('',#890);
#273=VERTEX_POINT('',#892);
#274=VERTEX_POINT('',#897);
#275=VERTEX_POINT('',#902);
#276=VERTEX_POINT('',#907);
#277=VERTEX_POINT('',#909);
#278=VERTEX_POINT('',#914);
#279=VERTEX_POINT('',#939);
#280=VERTEX_POINT('',#941);
#281=VERTEX_POINT('',#949);
#282=VERTEX_POINT('',#951);
#283=VERTEX_POINT('',#953);
#284=VERTEX_POINT('',#957);
#285=VERTEX_POINT('',#959);
#286=VERTEX_POINT('',#961);
#287=VERTEX_POINT('',#965);
#288=VERTEX_POINT('',#967);
#289=VERTEX_POINT('',#972);
#290=VERTEX_POINT('',#974);
#291=VERTEX_POINT('',#993);
#292=VERTEX_POINT('',#996);
#293=VERTEX_POINT('',#1003);
#294=VERTEX_POINT('',#1028);
#295=VERTEX_POINT('',#1055);
#296=VERTEX_POINT('',#1059);
#297=VERTEX_POINT('',#1088);
#298=EDGE_CURVE('',#246,#247,#136,.T.);
#299=EDGE_CURVE('',#247,#248,#137,.T.);
#300=EDGE_CURVE('',#248,#249,#118,.T.);
#301=EDGE_CURVE('',#249,#250,#138,.T.);
#302=EDGE_CURVE('',#250,#251,#119,.T.);
#303=EDGE_CURVE('',#251,#246,#139,.T.);
#304=EDGE_CURVE('',#252,#246,#140,.T.);
#305=EDGE_CURVE('',#253,#252,#120,.T.);
#306=EDGE_CURVE('',#253,#254,#141,.T.);
#307=EDGE_CURVE('',#246,#254,#121,.T.);
#308=EDGE_CURVE('',#255,#252,#142,.T.);
#309=EDGE_CURVE('',#256,#255,#122,.T.);
#310=EDGE_CURVE('',#256,#253,#143,.T.);
#311=EDGE_CURVE('',#247,#255,#144,.T.);
#312=EDGE_CURVE('',#255,#257,#145,.T.);
#313=EDGE_CURVE('',#257,#248,#146,.T.);
#314=EDGE_CURVE('',#258,#251,#147,.T.);
#315=EDGE_CURVE('',#252,#258,#148,.T.);
#316=EDGE_CURVE('',#259,#256,#149,.T.);
#317=EDGE_CURVE('',#260,#259,#123,.T.);
#318=EDGE_CURVE('',#260,#261,#150,.T.);
#319=EDGE_CURVE('',#262,#261,#151,.T.);
#320=EDGE_CURVE('',#263,#262,#152,.T.);
#321=EDGE_CURVE('',#263,#264,#124,.T.);
#322=EDGE_CURVE('',#264,#253,#153,.T.);
#323=EDGE_CURVE('',#265,#265,#125,.T.);
#324=EDGE_CURVE('',#266,#256,#154,.T.);
#325=EDGE_CURVE('',#267,#266,#155,.T.);
#326=EDGE_CURVE('',#259,#267,#156,.T.);
#327=EDGE_CURVE('',#254,#266,#157,.T.);
#328=EDGE_CURVE('',#266,#247,#126,.T.);
#329=EDGE_CURVE('',#268,#269,#158,.T.);
#330=EDGE_CURVE('',#269,#270,#159,.T.);
#331=EDGE_CURVE('',#270,#271,#160,.T.);
#332=EDGE_CURVE('',#268,#271,#161,.T.);
#333=EDGE_CURVE('',#272,#268,#162,.T.);
#334=EDGE_CURVE('',#272,#273,#21,.T.);
#335=EDGE_CURVE('',#273,#274,#22,.T.);
#336=EDGE_CURVE('',#274,#275,#23,.T.);
#337=EDGE_CURVE('',#275,#276,#163,.T.);
#338=EDGE_CURVE('',#276,#277,#24,.T.);
#339=EDGE_CURVE('',#277,#278,#25,.T.);
#340=EDGE_CURVE('',#278,#268,#26,.T.);
#341=EDGE_CURVE('',#279,#272,#164,.T.);
#342=EDGE_CURVE('',#280,#279,#27,.T.);
#343=EDGE_CURVE('',#280,#273,#165,.T.);
#344=EDGE_CURVE('',#269,#279,#166,.T.);
#345=EDGE_CURVE('',#279,#281,#167,.T.);
#346=EDGE_CURVE('',#281,#282,#127,.T.);
#347=EDGE_CURVE('',#282,#283,#168,.T.);
#348=EDGE_CURVE('',#283,#270,#128,.T.);
#349=EDGE_CURVE('',#271,#284,#129,.T.);
#350=EDGE_CURVE('',#284,#285,#169,.T.);
#351=EDGE_CURVE('',#285,#286,#130,.T.);
#352=EDGE_CURVE('',#286,#272,#170,.T.);
#353=EDGE_CURVE('',#261,#287,#171,.T.);
#354=EDGE_CURVE('',#260,#288,#172,.T.);
#355=EDGE_CURVE('',#275,#288,#173,.T.);
#356=EDGE_CURVE('',#287,#275,#174,.T.);
#357=EDGE_CURVE('',#258,#289,#131,.T.);
#358=EDGE_CURVE('',#289,#290,#175,.T.);
#359=EDGE_CURVE('',#290,#257,#132,.T.);
#360=EDGE_CURVE('',#276,#291,#176,.T.);
#361=EDGE_CURVE('',#291,#262,#177,.T.);
#362=EDGE_CURVE('',#262,#292,#28,.T.);
#363=EDGE_CURVE('',#292,#277,#178,.T.);
#364=EDGE_CURVE('',#293,#293,#133,.T.);
#365=EDGE_CURVE('',#293,#265,#179,.T.);
#366=EDGE_CURVE('',#281,#286,#180,.T.);
#367=EDGE_CURVE('',#282,#285,#181,.T.);
#368=EDGE_CURVE('',#283,#284,#182,.T.);
#369=EDGE_CURVE('',#294,#278,#183,.T.);
#370=EDGE_CURVE('',#294,#269,#29,.T.);
#371=EDGE_CURVE('',#294,#292,#30,.T.);
#372=EDGE_CURVE('',#295,#276,#184,.T.);
#373=EDGE_CURVE('',#263,#295,#185,.T.);
#374=EDGE_CURVE('',#296,#295,#134,.T.);
#375=EDGE_CURVE('',#264,#296,#186,.T.);
#376=EDGE_CURVE('',#254,#296,#187,.T.);
#377=EDGE_CURVE('',#289,#250,#188,.T.);
#378=EDGE_CURVE('',#290,#249,#189,.T.);
#379=EDGE_CURVE('',#288,#267,#135,.T.);
#380=EDGE_CURVE('',#297,#274,#190,.T.);
#381=EDGE_CURVE('',#261,#297,#31,.T.);
#382=EDGE_CURVE('',#280,#297,#32,.T.);
#383=ORIENTED_EDGE('',*,*,#298,.T.);
#384=ORIENTED_EDGE('',*,*,#299,.T.);
#385=ORIENTED_EDGE('',*,*,#300,.T.);
#386=ORIENTED_EDGE('',*,*,#301,.T.);
#387=ORIENTED_EDGE('',*,*,#302,.T.);
#388=ORIENTED_EDGE('',*,*,#303,.T.);
#389=ORIENTED_EDGE('',*,*,#304,.F.);
#390=ORIENTED_EDGE('',*,*,#305,.F.);
#391=ORIENTED_EDGE('',*,*,#306,.T.);
#392=ORIENTED_EDGE('',*,*,#307,.F.);
#393=ORIENTED_EDGE('',*,*,#308,.F.);
#394=ORIENTED_EDGE('',*,*,#309,.F.);
#395=ORIENTED_EDGE('',*,*,#310,.T.);
#396=ORIENTED_EDGE('',*,*,#305,.T.);
#397=ORIENTED_EDGE('',*,*,#311,.T.);
#398=ORIENTED_EDGE('',*,*,#312,.T.);
#399=ORIENTED_EDGE('',*,*,#313,.T.);
#400=ORIENTED_EDGE('',*,*,#299,.F.);
#401=ORIENTED_EDGE('',*,*,#304,.T.);
#402=ORIENTED_EDGE('',*,*,#303,.F.);
#403=ORIENTED_EDGE('',*,*,#314,.F.);
#404=ORIENTED_EDGE('',*,*,#315,.F.);
#405=ORIENTED_EDGE('',*,*,#310,.F.);
#406=ORIENTED_EDGE('',*,*,#316,.F.);
#407=ORIENTED_EDGE('',*,*,#317,.F.);
#408=ORIENTED_EDGE('',*,*,#318,.T.);
#409=ORIENTED_EDGE('',*,*,#319,.F.);
#410=ORIENTED_EDGE('',*,*,#320,.F.);
#411=ORIENTED_EDGE('',*,*,#321,.T.);
#412=ORIENTED_EDGE('',*,*,#322,.T.);
#413=ORIENTED_EDGE('',*,*,#323,.T.);
#414=ORIENTED_EDGE('',*,*,#324,.F.);
#415=ORIENTED_EDGE('',*,*,#325,.F.);
#416=ORIENTED_EDGE('',*,*,#326,.F.);
#417=ORIENTED_EDGE('',*,*,#316,.T.);
#418=ORIENTED_EDGE('',*,*,#298,.F.);
#419=ORIENTED_EDGE('',*,*,#307,.T.);
#420=ORIENTED_EDGE('',*,*,#327,.T.);
#421=ORIENTED_EDGE('',*,*,#328,.T.);
#422=ORIENTED_EDGE('',*,*,#329,.T.);
#423=ORIENTED_EDGE('',*,*,#330,.T.);
#424=ORIENTED_EDGE('',*,*,#331,.T.);
#425=ORIENTED_EDGE('',*,*,#332,.F.);
#426=ORIENTED_EDGE('',*,*,#333,.F.);
#427=ORIENTED_EDGE('',*,*,#334,.T.);
#428=ORIENTED_EDGE('',*,*,#335,.T.);
#429=ORIENTED_EDGE('',*,*,#336,.T.);
#430=ORIENTED_EDGE('',*,*,#337,.T.);
#431=ORIENTED_EDGE('',*,*,#338,.T.);
#432=ORIENTED_EDGE('',*,*,#339,.T.);
#433=ORIENTED_EDGE('',*,*,#340,.T.);
#434=ORIENTED_EDGE('',*,*,#341,.F.);
#435=ORIENTED_EDGE('',*,*,#342,.F.);
#436=ORIENTED_EDGE('',*,*,#343,.T.);
#437=ORIENTED_EDGE('',*,*,#334,.F.);
#438=ORIENTED_EDGE('',*,*,#344,.T.);
#439=ORIENTED_EDGE('',*,*,#345,.T.);
#440=ORIENTED_EDGE('',*,*,#346,.T.);
#441=ORIENTED_EDGE('',*,*,#347,.T.);
#442=ORIENTED_EDGE('',*,*,#348,.T.);
#443=ORIENTED_EDGE('',*,*,#330,.F.);
#444=ORIENTED_EDGE('',*,*,#333,.T.);
#445=ORIENTED_EDGE('',*,*,#332,.T.);
#446=ORIENTED_EDGE('',*,*,#349,.T.);
#447=ORIENTED_EDGE('',*,*,#350,.T.);
#448=ORIENTED_EDGE('',*,*,#351,.T.);
#449=ORIENTED_EDGE('',*,*,#352,.T.);
#450=ORIENTED_EDGE('',*,*,#353,.F.);
#451=ORIENTED_EDGE('',*,*,#318,.F.);
#452=ORIENTED_EDGE('',*,*,#354,.T.);
#453=ORIENTED_EDGE('',*,*,#355,.F.);
#454=ORIENTED_EDGE('',*,*,#356,.F.);
#455=ORIENTED_EDGE('',*,*,#308,.T.);
#456=ORIENTED_EDGE('',*,*,#315,.T.);
#457=ORIENTED_EDGE('',*,*,#357,.T.);
#458=ORIENTED_EDGE('',*,*,#358,.T.);
#459=ORIENTED_EDGE('',*,*,#359,.T.);
#460=ORIENTED_EDGE('',*,*,#312,.F.);
#461=ORIENTED_EDGE('',*,*,#360,.T.);
#462=ORIENTED_EDGE('',*,*,#361,.T.);
#463=ORIENTED_EDGE('',*,*,#362,.T.);
#464=ORIENTED_EDGE('',*,*,#363,.T.);
#465=ORIENTED_EDGE('',*,*,#338,.F.);
#466=ORIENTED_EDGE('',*,*,#364,.F.);
#467=ORIENTED_EDGE('',*,*,#365,.T.);
#468=ORIENTED_EDGE('',*,*,#323,.F.);
#469=ORIENTED_EDGE('',*,*,#365,.F.);
#470=ORIENTED_EDGE('',*,*,#346,.F.);
#471=ORIENTED_EDGE('',*,*,#366,.T.);
#472=ORIENTED_EDGE('',*,*,#351,.F.);
#473=ORIENTED_EDGE('',*,*,#367,.F.);
#474=ORIENTED_EDGE('',*,*,#347,.F.);
#475=ORIENTED_EDGE('',*,*,#367,.T.);
#476=ORIENTED_EDGE('',*,*,#350,.F.);
#477=ORIENTED_EDGE('',*,*,#368,.F.);
#478=ORIENTED_EDGE('',*,*,#348,.F.);
#479=ORIENTED_EDGE('',*,*,#368,.T.);
#480=ORIENTED_EDGE('',*,*,#349,.F.);
#481=ORIENTED_EDGE('',*,*,#331,.F.);
#482=ORIENTED_EDGE('',*,*,#329,.F.);
#483=ORIENTED_EDGE('',*,*,#340,.F.);
#484=ORIENTED_EDGE('',*,*,#369,.F.);
#485=ORIENTED_EDGE('',*,*,#370,.T.);
#486=ORIENTED_EDGE('',*,*,#371,.F.);
#487=ORIENTED_EDGE('',*,*,#369,.T.);
#488=ORIENTED_EDGE('',*,*,#339,.F.);
#489=ORIENTED_EDGE('',*,*,#363,.F.);
#490=ORIENTED_EDGE('',*,*,#360,.F.);
#491=ORIENTED_EDGE('',*,*,#372,.F.);
#492=ORIENTED_EDGE('',*,*,#373,.F.);
#493=ORIENTED_EDGE('',*,*,#320,.T.);
#494=ORIENTED_EDGE('',*,*,#361,.F.);
#495=ORIENTED_EDGE('',*,*,#321,.F.);
#496=ORIENTED_EDGE('',*,*,#373,.T.);
#497=ORIENTED_EDGE('',*,*,#374,.F.);
#498=ORIENTED_EDGE('',*,*,#375,.F.);
#499=ORIENTED_EDGE('',*,*,#306,.F.);
#500=ORIENTED_EDGE('',*,*,#322,.F.);
#501=ORIENTED_EDGE('',*,*,#375,.T.);
#502=ORIENTED_EDGE('',*,*,#376,.F.);
#503=ORIENTED_EDGE('',*,*,#357,.F.);
#504=ORIENTED_EDGE('',*,*,#314,.T.);
#505=ORIENTED_EDGE('',*,*,#302,.F.);
#506=ORIENTED_EDGE('',*,*,#377,.F.);
#507=ORIENTED_EDGE('',*,*,#358,.F.);
#508=ORIENTED_EDGE('',*,*,#377,.T.);
#509=ORIENTED_EDGE('',*,*,#301,.F.);
#510=ORIENTED_EDGE('',*,*,#378,.F.);
#511=ORIENTED_EDGE('',*,*,#359,.F.);
#512=ORIENTED_EDGE('',*,*,#378,.T.);
#513=ORIENTED_EDGE('',*,*,#300,.F.);
#514=ORIENTED_EDGE('',*,*,#313,.F.);
#515=ORIENTED_EDGE('',*,*,#311,.F.);
#516=ORIENTED_EDGE('',*,*,#328,.F.);
#517=ORIENTED_EDGE('',*,*,#324,.T.);
#518=ORIENTED_EDGE('',*,*,#309,.T.);
#519=ORIENTED_EDGE('',*,*,#317,.T.);
#520=ORIENTED_EDGE('',*,*,#326,.T.);
#521=ORIENTED_EDGE('',*,*,#379,.F.);
#522=ORIENTED_EDGE('',*,*,#354,.F.);
#523=ORIENTED_EDGE('',*,*,#353,.T.);
#524=ORIENTED_EDGE('',*,*,#356,.T.);
#525=ORIENTED_EDGE('',*,*,#336,.F.);
#526=ORIENTED_EDGE('',*,*,#380,.F.);
#527=ORIENTED_EDGE('',*,*,#381,.F.);
#528=ORIENTED_EDGE('',*,*,#382,.T.);
#529=ORIENTED_EDGE('',*,*,#380,.T.);
#530=ORIENTED_EDGE('',*,*,#335,.F.);
#531=ORIENTED_EDGE('',*,*,#343,.F.);
#532=ORIENTED_EDGE('',*,*,#341,.T.);
#533=ORIENTED_EDGE('',*,*,#352,.F.);
#534=ORIENTED_EDGE('',*,*,#366,.F.);
#535=ORIENTED_EDGE('',*,*,#345,.F.);
#536=ORIENTED_EDGE('',*,*,#344,.F.);
#537=ORIENTED_EDGE('',*,*,#370,.F.);
#538=ORIENTED_EDGE('',*,*,#371,.T.);
#539=ORIENTED_EDGE('',*,*,#362,.F.);
#540=ORIENTED_EDGE('',*,*,#319,.T.);
#541=ORIENTED_EDGE('',*,*,#381,.T.);
#542=ORIENTED_EDGE('',*,*,#382,.F.);
#543=ORIENTED_EDGE('',*,*,#342,.T.);
#544=ORIENTED_EDGE('',*,*,#327,.F.);
#545=ORIENTED_EDGE('',*,*,#376,.T.);
#546=ORIENTED_EDGE('',*,*,#374,.T.);
#547=ORIENTED_EDGE('',*,*,#372,.T.);
#548=ORIENTED_EDGE('',*,*,#337,.F.);
#549=ORIENTED_EDGE('',*,*,#355,.T.);
#550=ORIENTED_EDGE('',*,*,#379,.T.);
#551=ORIENTED_EDGE('',*,*,#325,.T.);
#552=ORIENTED_EDGE('',*,*,#364,.T.);
#553=PLANE('',#620);
#554=PLANE('',#623);
#555=PLANE('',#628);
#556=PLANE('',#629);
#557=PLANE('',#630);
#558=PLANE('',#634);
#559=PLANE('',#637);
#560=PLANE('',#639);
#561=PLANE('',#642);
#562=PLANE('',#645);
#563=PLANE('',#646);
#564=PLANE('',#652);
#565=PLANE('',#654);
#566=PLANE('',#657);
#567=PLANE('',#659);
#568=PLANE('',#661);
#569=PLANE('',#664);
#570=PLANE('',#666);
#571=ADVANCED_FACE('',(#46),#553,.F.);
#572=ADVANCED_FACE('',(#47),#554,.T.);
#573=ADVANCED_FACE('',(#48),#35,.F.);
#574=ADVANCED_FACE('',(#49),#555,.T.);
#575=ADVANCED_FACE('',(#50),#556,.T.);
#576=ADVANCED_FACE('',(#51,#33),#557,.T.);
#577=ADVANCED_FACE('',(#52),#558,.T.);
#578=ADVANCED_FACE('',(#53),#36,.T.);
#579=ADVANCED_FACE('',(#54),#559,.T.);
#580=ADVANCED_FACE('',(#55),#37,.T.);
#581=ADVANCED_FACE('',(#56),#15,.T.);
#582=ADVANCED_FACE('',(#57),#560,.T.);
#583=ADVANCED_FACE('',(#58),#561,.F.);
#584=ADVANCED_FACE('',(#59),#562,.T.);
#585=ADVANCED_FACE('',(#60),#563,.T.);
#586=ADVANCED_FACE('',(#61),#16,.T.);
#587=ADVANCED_FACE('',(#62),#38,.F.);
#588=ADVANCED_FACE('',(#63),#39,.T.);
#589=ADVANCED_FACE('',(#64),#564,.T.);
#590=ADVANCED_FACE('',(#65),#40,.T.);
#591=ADVANCED_FACE('',(#66),#17,.T.);
#592=ADVANCED_FACE('',(#67),#18,.F.);
#593=ADVANCED_FACE('',(#68),#565,.T.);
#594=ADVANCED_FACE('',(#69),#41,.T.);
#595=ADVANCED_FACE('',(#70),#566,.T.);
#596=ADVANCED_FACE('',(#71),#42,.T.);
#597=ADVANCED_FACE('',(#72),#567,.T.);
#598=ADVANCED_FACE('',(#73),#43,.T.);
#599=ADVANCED_FACE('',(#74),#568,.T.);
#600=ADVANCED_FACE('',(#75),#44,.F.);
#601=ADVANCED_FACE('',(#76),#19,.T.);
#602=ADVANCED_FACE('',(#77),#20,.T.);
#603=ADVANCED_FACE('',(#78),#569,.T.);
#604=ADVANCED_FACE('',(#79),#45,.F.);
#605=ADVANCED_FACE('',(#80,#34),#570,.F.);
#606=CLOSED_SHELL('',(#571,#572,#573,#574,#575,#576,#577,#578,#579,#580,
#581,#582,#583,#584,#585,#586,#587,#588,#589,#590,#591,#592,#593,#594,#595,
#596,#597,#598,#599,#600,#601,#602,#603,#604,#605));
#607=DERIVED_UNIT_ELEMENT(#609,1.);
#608=DERIVED_UNIT_ELEMENT(#1122,-3.);
#609=(
MASS_UNIT()
NAMED_UNIT(*)
SI_UNIT(.KILO.,.GRAM.)
);
#610=DERIVED_UNIT((#607,#608));
#611=MEASURE_REPRESENTATION_ITEM('density measure',
POSITIVE_RATIO_MEASURE(7850.),#610);
#612=PROPERTY_DEFINITION_REPRESENTATION(#617,#614);
#613=PROPERTY_DEFINITION_REPRESENTATION(#618,#615);
#614=REPRESENTATION('material name',(#616),#1119);
#615=REPRESENTATION('density',(#611),#1119);
#616=DESCRIPTIVE_REPRESENTATION_ITEM('Steel','Steel');
#617=PROPERTY_DEFINITION('material property','material name',#1129);
#618=PROPERTY_DEFINITION('material property','density of part',#1129);
#619=AXIS2_PLACEMENT_3D('placement',#818,#667,#668);
#620=AXIS2_PLACEMENT_3D('',#819,#669,#670);
#621=AXIS2_PLACEMENT_3D('',#826,#673,#674);
#622=AXIS2_PLACEMENT_3D('',#830,#676,#677);
#623=AXIS2_PLACEMENT_3D('',#832,#679,#680);
#624=AXIS2_PLACEMENT_3D('',#836,#682,#683);
#625=AXIS2_PLACEMENT_3D('',#839,#685,#686);
#626=AXIS2_PLACEMENT_3D('',#840,#687,#688);
#627=AXIS2_PLACEMENT_3D('',#844,#690,#691);
#628=AXIS2_PLACEMENT_3D('',#846,#693,#694);
#629=AXIS2_PLACEMENT_3D('',#851,#698,#699);
#630=AXIS2_PLACEMENT_3D('',#855,#702,#703);
#631=AXIS2_PLACEMENT_3D('',#859,#705,#706);
#632=AXIS2_PLACEMENT_3D('',#867,#710,#711);
#633=AXIS2_PLACEMENT_3D('',#870,#713,#714);
#634=AXIS2_PLACEMENT_3D('',#871,#715,#716);
#635=AXIS2_PLACEMENT_3D('',#877,#720,#721);
#636=AXIS2_PLACEMENT_3D('',#879,#723,#724);
#637=AXIS2_PLACEMENT_3D('',#880,#725,#726);
#638=AXIS2_PLACEMENT_3D('',#889,#731,#732);
#639=AXIS2_PLACEMENT_3D('',#947,#737,#738);
#640=AXIS2_PLACEMENT_3D('',#952,#741,#742);
#641=AXIS2_PLACEMENT_3D('',#955,#744,#745);
#642=AXIS2_PLACEMENT_3D('',#956,#746,#747);
#643=AXIS2_PLACEMENT_3D('',#958,#748,#749);
#644=AXIS2_PLACEMENT_3D('',#962,#751,#752);
#645=AXIS2_PLACEMENT_3D('',#964,#754,#755);
#646=AXIS2_PLACEMENT_3D('',#971,#760,#761);
#647=AXIS2_PLACEMENT_3D('',#973,#762,#763);
#648=AXIS2_PLACEMENT_3D('',#976,#765,#766);
#649=AXIS2_PLACEMENT_3D('',#1002,#770,#771);
#650=AXIS2_PLACEMENT_3D('',#1004,#772,#773);
#651=AXIS2_PLACEMENT_3D('',#1006,#775,#776);
#652=AXIS2_PLACEMENT_3D('',#1009,#779,#780);
#653=AXIS2_PLACEMENT_3D('',#1011,#782,#783);
#654=AXIS2_PLACEMENT_3D('',#1054,#785,#786);
#655=AXIS2_PLACEMENT_3D('',#1058,#789,#790);
#656=AXIS2_PLACEMENT_3D('',#1060,#791,#792);
#657=AXIS2_PLACEMENT_3D('',#1062,#794,#795);
#658=AXIS2_PLACEMENT_3D('',#1064,#797,#798);
#659=AXIS2_PLACEMENT_3D('',#1066,#800,#801);
#660=AXIS2_PLACEMENT_3D('',#1068,#803,#804);
#661=AXIS2_PLACEMENT_3D('',#1069,#805,#806);
#662=AXIS2_PLACEMENT_3D('',#1070,#807,#808);
#663=AXIS2_PLACEMENT_3D('',#1071,#809,#810);
#664=AXIS2_PLACEMENT_3D('',#1114,#812,#813);
#665=AXIS2_PLACEMENT_3D('',#1115,#814,#815);
#666=AXIS2_PLACEMENT_3D('',#1116,#816,#817);
#667=DIRECTION('axis',(0.,0.,1.));
#668=DIRECTION('refdir',(1.,0.,0.));
#669=DIRECTION('center_axis',(-8.62315106001598E-17,0.866025403784439,0.5));
#670=DIRECTION('ref_axis',(1.,4.97857858576304E-17,8.62315106001598E-17));
#671=DIRECTION('',(1.,9.95616354268505E-17,1.72101563651073E-20));
#672=DIRECTION('',(-4.97857858576304E-17,-0.5,0.866025403784439));
#673=DIRECTION('center_axis',(8.62315106001598E-17,-0.866025403784439,-0.5));
#674=DIRECTION('ref_axis',(-0.259818176463157,-0.482828777927372,0.8362839747266));
#675=DIRECTION('',(-0.965657555854744,0.12990908823158,-0.225009141182045));
#676=DIRECTION('center_axis',(8.62315106001598E-17,-0.866025403784439,-0.5));
#677=DIRECTION('ref_axis',(-1.,-4.97857858576304E-17,-8.62315106001598E-17));
#678=DIRECTION('',(9.87239766506621E-17,0.5,-0.866025403784439));
#679=DIRECTION('center_axis',(-1.,-9.95715717152607E-17,0.));
#680=DIRECTION('ref_axis',(0.,0.,1.));
#681=DIRECTION('',(8.62315106001598E-17,-0.866025403784439,-0.5));
#682=DIRECTION('center_axis',(-1.,-9.95715717152607E-17,0.));
#683=DIRECTION('ref_axis',(0.,0.,-1.));
#684=DIRECTION('',(0.,0.,-1.));
#685=DIRECTION('center_axis',(1.,9.95715717152607E-17,0.));
#686=DIRECTION('ref_axis',(0.,-0.866025403784439,-0.499999999999999));
#687=DIRECTION('center_axis',(-1.,-9.95715717152607E-17,0.));
#688=DIRECTION('ref_axis',(0.,0.,-1.));
#689=DIRECTION('',(-1.,-9.95616354268505E-17,-1.72101563651073E-20));
#690=DIRECTION('center_axis',(-1.,-9.95715717152607E-17,0.));
#691=DIRECTION('ref_axis',(0.,0.,-1.));
#692=DIRECTION('',(-1.,-1.13773370443932E-16,0.));
#693=DIRECTION('center_axis',(1.,4.97857858576304E-17,8.62315106001598E-17));
#694=DIRECTION('ref_axis',(-4.97857858576304E-17,-0.5,0.866025403784439));
#695=DIRECTION('',(-8.62315106001598E-17,0.866025403784439,0.5));
#696=DIRECTION('',(-4.97857858576304E-17,-0.5,0.866025403784439));
#697=DIRECTION('',(8.62315106001598E-17,-0.866025403784439,-0.5));
#698=DIRECTION('center_axis',(-1.,-2.53166904611145E-17,-1.28613227042175E-16));
#699=DIRECTION('ref_axis',(9.87239766506621E-17,0.5,-0.866025403784439));
#700=DIRECTION('',(8.62315106001598E-17,-0.866025403784439,-0.5));
#701=DIRECTION('',(-9.87239766506621E-17,-0.5,0.866025403784439));
#702=DIRECTION('center_axis',(0.,0.,1.));
#703=DIRECTION('ref_axis',(1.,0.,0.));
#704=DIRECTION('',(0.,-1.,0.));
#705=DIRECTION('center_axis',(0.,0.,1.));
#706=DIRECTION('ref_axis',(-0.901877548868668,0.431991767105166,0.));
#707=DIRECTION('',(0.431991767105161,0.90187754886867,0.));
#708=DIRECTION('',(0.822532631035408,-0.568717918551868,0.));
#709=DIRECTION('',(0.432731067584771,0.901523057468273,0.));
#710=DIRECTION('center_axis',(0.,0.,1.));
#711=DIRECTION('ref_axis',(-0.901523057468275,0.432731067584769,0.));
#712=DIRECTION('',(-4.89381907930318E-17,-1.,0.));
#713=DIRECTION('center_axis',(0.,0.,-1.));
#714=DIRECTION('ref_axis',(1.,0.,0.));
#715=DIRECTION('center_axis',(1.,0.,0.));
#716=DIRECTION('ref_axis',(0.,-1.,0.));
#717=DIRECTION('',(0.,0.,1.));
#718=DIRECTION('',(0.,-1.,0.));
#719=DIRECTION('',(0.,0.,-1.));
#720=DIRECTION('center_axis',(-1.,-9.95715717152607E-17,0.));
#721=DIRECTION('ref_axis',(0.,0.,-1.));
#722=DIRECTION('',(1.,1.13773370443932E-16,0.));
#723=DIRECTION('center_axis',(-1.,-9.95715717152607E-17,0.));
#724=DIRECTION('ref_axis',(0.,-2.77555756156289E-16,-1.));
#725=DIRECTION('center_axis',(-0.822215625080924,0.56917531285796,0.000963901358173924));
#726=DIRECTION('ref_axis',(0.285274276287416,0.410633067828835,0.866024867364772));
#727=DIRECTION('',(-0.492524165053326,-0.712334153918316,0.500000000000001));
#728=DIRECTION('',(0.285274276287416,0.410633067828835,0.866024867364772));
#729=DIRECTION('',(0.492524165053326,0.712334153918316,-0.500000000000001));
#730=DIRECTION('',(0.285274276287416,0.410633067828835,0.866024867364772));
#731=DIRECTION('center_axis',(0.822532631035408,-0.568717918551868,0.));
#732=DIRECTION('ref_axis',(0.,0.,-1.));
#733=DIRECTION('',(-0.822532631035408,0.568717918551868,-1.11022302462516E-16));
#734=DIRECTION('',(-0.822532631035408,0.568717918551868,0.));
#735=DIRECTION('',(0.492524165053326,0.712334153918316,-0.500000000000001));
#736=DIRECTION('',(0.291628547687304,0.421780268898626,-0.858518604889607));
#737=DIRECTION('center_axis',(-0.492524165053326,-0.712334153918316,0.500000000000001));
#738=DIRECTION('ref_axis',(0.838279964559016,-0.233894522931724,0.492524165053326));
#739=DIRECTION('',(0.822532631035408,-0.568717918551868,1.11022302462516E-16));
#740=DIRECTION('',(0.289108134488579,0.407972474026078,0.866010939312879));
#741=DIRECTION('center_axis',(-0.492524165053326,-0.712334153918316,0.500000000000001));
#742=DIRECTION('ref_axis',(0.820875406752487,-0.571085382056409,-0.00500529623738356));
#743=DIRECTION('',(-0.844566331373533,0.529880527093444,-0.0770346604940009));
#744=DIRECTION('center_axis',(-0.492524165053326,-0.712334153918316,0.500000000000001));
#745=DIRECTION('ref_axis',(0.21006584384135,0.460224597526738,0.862592407271577));
#746=DIRECTION('center_axis',(-0.492524165053326,-0.712334153918316,0.500000000000001));
#747=DIRECTION('ref_axis',(0.838279964559016,-0.233894522931724,0.492524165053326));
#748=DIRECTION('center_axis',(0.492524165053326,0.712334153918316,-0.500000000000001));
#749=DIRECTION('ref_axis',(0.21006584384135,0.460224597526738,0.862592407271577));
#750=DIRECTION('',(0.844566331373533,-0.529880527093444,0.0770346604940009));
#751=DIRECTION('center_axis',(0.492524165053326,0.712334153918316,-0.500000000000001));
#752=DIRECTION('ref_axis',(0.820875406752487,-0.571085382056409,-0.00500529623738356));
#753=DIRECTION('',(-0.289108134488579,-0.407972474026078,-0.866010939312879));
#754=DIRECTION('center_axis',(0.90187754886867,-0.431991767105161,0.));
#755=DIRECTION('ref_axis',(-0.431991767105161,-0.90187754886867,0.));
#756=DIRECTION('',(0.,0.,-1.));
#757=DIRECTION('',(0.,0.,-1.));
#758=DIRECTION('',(-0.431991767105161,-0.90187754886867,0.));
#759=DIRECTION('',(0.,0.,-1.));
#760=DIRECTION('center_axis',(-8.62315106001598E-17,0.866025403784439,0.5));
#761=DIRECTION('ref_axis',(1.,4.97857858576304E-17,8.62315106001598E-17));
#762=DIRECTION('center_axis',(-8.62315106001598E-17,0.866025403784439,0.5));
#763=DIRECTION('ref_axis',(-1.,-4.97857858576304E-17,-8.62315106001598E-17));
#764=DIRECTION('',(0.965657555854744,-0.12990908823158,0.225009141182045));
#765=DIRECTION('center_axis',(-8.62315106001598E-17,0.866025403784439,0.5));
#766=DIRECTION('ref_axis',(-0.259818176463157,-0.482828777927372,0.8362839747266));
#767=DIRECTION('',(0.,0.,1.));
#768=DIRECTION('',(0.,0.,1.));
#769=DIRECTION('',(0.27762438656669,0.401526151494845,-0.872755091448459));
#770=DIRECTION('center_axis',(0.,0.,-1.));
#771=DIRECTION('ref_axis',(1.,0.,0.));
#772=DIRECTION('center_axis',(0.,0.,1.));
#773=DIRECTION('ref_axis',(1.,0.,0.));
#774=DIRECTION('',(0.,0.,1.));
#775=DIRECTION('center_axis',(0.492524165053326,0.712334153918316,-0.500000000000001));
#776=DIRECTION('ref_axis',(0.820875406752487,-0.571085382056409,-0.00500529623738356));
#777=DIRECTION('',(0.492524165053326,0.712334153918316,-0.500000000000001));
#778=DIRECTION('',(0.492524165053326,0.712334153918316,-0.500000000000001));
#779=DIRECTION('center_axis',(0.210065843841344,0.460224597526742,0.862592407271576));
#780=DIRECTION('ref_axis',(0.844566331373533,-0.529880527093444,0.0770346604940009));
#781=DIRECTION('',(0.492524165053326,0.712334153918316,-0.500000000000001));
#782=DIRECTION('center_axis',(0.492524165053326,0.712334153918316,-0.500000000000001));
#783=DIRECTION('ref_axis',(0.21006584384135,0.460224597526738,0.862592407271577));
#784=DIRECTION('',(0.291124097233992,0.421050685839863,-0.859047775134465));
#785=DIRECTION('center_axis',(-0.901523057468274,0.432731067584772,0.));
#786=DIRECTION('ref_axis',(0.432731067584772,0.901523057468274,0.));
#787=DIRECTION('',(0.432731067584771,0.901523057468273,0.));
#788=DIRECTION('',(0.,0.,-1.));
#789=DIRECTION('center_axis',(0.,0.,-1.));
#790=DIRECTION('ref_axis',(-0.901523057468275,0.432731067584769,0.));
#791=DIRECTION('center_axis',(0.,0.,-1.));
#792=DIRECTION('ref_axis',(-0.901523057468275,0.432731067584769,0.));
#793=DIRECTION('',(0.,0.,-1.));
#794=DIRECTION('center_axis',(-1.,4.89381907930318E-17,0.));
#795=DIRECTION('ref_axis',(4.89381907930318E-17,1.,0.));
#796=DIRECTION('',(4.89381907930318E-17,1.,0.));
#797=DIRECTION('center_axis',(8.62315106001598E-17,-0.866025403784439,-0.5));
#798=DIRECTION('ref_axis',(-1.,-4.97857858576304E-17,-8.62315106001598E-17));
#799=DIRECTION('',(8.62315106001598E-17,-0.866025403784439,-0.5));
#800=DIRECTION('center_axis',(-0.25981817646316,-0.482828777927372,0.836283974726599));
#801=DIRECTION('ref_axis',(-0.965657555854744,0.12990908823158,-0.225009141182045));
#802=DIRECTION('',(8.62315106001598E-17,-0.866025403784439,-0.5));
#803=DIRECTION('center_axis',(8.62315106001598E-17,-0.866025403784439,-0.5));
#804=DIRECTION('ref_axis',(-0.259818176463157,-0.482828777927372,0.8362839747266));
#805=DIRECTION('center_axis',(1.,9.95715717152607E-17,0.));
#806=DIRECTION('ref_axis',(0.,0.,-1.));
#807=DIRECTION('center_axis',(0.,0.,-1.));
#808=DIRECTION('ref_axis',(-0.901877548868668,0.431991767105166,0.));
#809=DIRECTION('center_axis',(0.,0.,1.));
#810=DIRECTION('ref_axis',(-0.901877548868668,0.431991767105166,0.));
#811=DIRECTION('',(0.277665138276903,0.40158509039993,-0.872715008553278));
#812=DIRECTION('center_axis',(0.820875406752485,-0.571085382056413,-0.00500529623739099));
#813=DIRECTION('ref_axis',(-0.289108134488579,-0.407972474026078,-0.866010939312879));
#814=DIRECTION('center_axis',(0.822532631035408,-0.568717918551868,0.));
#815=DIRECTION('ref_axis',(0.,0.,-1.));
#816=DIRECTION('center_axis',(0.,0.,1.));
#817=DIRECTION('ref_axis',(1.,0.,0.));
#818=CARTESIAN_POINT('',(0.,0.,0.));
#819=CARTESIAN_POINT('Origin',(20.8250801285992,21.6167049408048,-23.7792895513918));
#820=CARTESIAN_POINT('',(-1.33946211831077E-15,7.88772571717068,5.55111512312578E-15));
#821=CARTESIAN_POINT('',(22.3,7.88772571717069,4.44089209850063E-15));
#822=CARTESIAN_POINT('',(10.4125400642996,7.88772571717068,6.66133814775094E-15));
#823=CARTESIAN_POINT('',(22.3,4.14800123471307,6.47739280992582));
#824=CARTESIAN_POINT('',(22.3,14.1599920294582,-10.8638839314846));
#825=CARTESIAN_POINT('',(21.0401818235368,3.6651724567857,7.31367678465242));
#826=CARTESIAN_POINT('Origin',(21.3,4.14800123471307,6.47739280992582));
#827=CARTESIAN_POINT('',(0.740181823536839,6.39611416082157,2.58354700075356));
#828=CARTESIAN_POINT('',(0.740181823536839,6.39611416082157,2.58354700075356));
#829=CARTESIAN_POINT('',(-1.53864421323576E-15,6.87894293874894,1.74726302602696));
#830=CARTESIAN_POINT('Origin',(0.999999999999998,6.87894293874894,1.74726302602696));
#831=CARTESIAN_POINT('',(1.26094926845993E-15,21.0578363376754,-22.8113007360167));
#832=CARTESIAN_POINT('Origin',(-2.22044604925031E-15,11.3518273323084,2.));
#833=CARTESIAN_POINT('',(-1.6843881607114E-15,11.3518273323084,2.00000000000001));
#834=CARTESIAN_POINT('',(-1.6843881607114E-15,11.3518273323084,2.));
#835=CARTESIAN_POINT('',(3.93924220654956E-16,14.8159289474462,0.));
#836=CARTESIAN_POINT('Origin',(0.,14.8159289474462,4.));
#837=CARTESIAN_POINT('',(3.93924220654956E-16,14.8159289474462,-4.));
#838=CARTESIAN_POINT('',(3.93924220654956E-16,14.8159289474462,0.));
#839=CARTESIAN_POINT('Origin',(-2.22044604925031E-15,14.8159289474462,4.));
#840=CARTESIAN_POINT('Origin',(11.15,14.8159289474462,4.));
#841=CARTESIAN_POINT('',(22.3,11.3518273323084,2.));
#842=CARTESIAN_POINT('',(10.4125400642996,11.3518273323084,2.00000000000001));
#843=CARTESIAN_POINT('',(22.3,14.8159289474462,0.));
#844=CARTESIAN_POINT('Origin',(22.3,14.8159289474462,4.));
#845=CARTESIAN_POINT('',(10.4125400642996,14.8159289474462,0.));
#846=CARTESIAN_POINT('Origin',(22.3,17.6240936445959,-8.86388393148463));
#847=CARTESIAN_POINT('',(22.3,11.3518273323084,2.));
#848=CARTESIAN_POINT('',(22.3,7.61210284985082,8.47739280992582));
#849=CARTESIAN_POINT('',(22.3,17.6240936445959,-8.86388393148463));
#850=CARTESIAN_POINT('',(22.3,7.61210284985082,8.47739280992582));
#851=CARTESIAN_POINT('Origin',(-1.8835702556364E-15,10.3430445538867,3.74726302602696));
#852=CARTESIAN_POINT('',(-1.8835702556364E-15,10.3430445538867,3.74726302602696));
#853=CARTESIAN_POINT('',(-1.8835702556364E-15,10.3430445538867,3.74726302602696));
#854=CARTESIAN_POINT('',(9.16023226059289E-16,24.5219379528132,-20.8113007360167));
#855=CARTESIAN_POINT('Origin',(20.8250801285992,36.242029499822,0.));
#856=CARTESIAN_POINT('',(22.3,21.3286036771288,0.));
#857=CARTESIAN_POINT('',(22.3,21.3286036771288,0.));
#858=CARTESIAN_POINT('',(23.2812245113133,25.6485213481804,0.));
#859=CARTESIAN_POINT('Origin',(32.2999999999998,21.3286036771288,0.));
#860=CARTESIAN_POINT('',(32.3792633668452,44.6426726430627,0.));
#861=CARTESIAN_POINT('',(23.2812245113133,25.6485213481804,0.));
#862=CARTESIAN_POINT('',(10.6785731410083,59.6470273771006,0.));
#863=CARTESIAN_POINT('',(29.9239996468738,46.3402982686385,0.));
#864=CARTESIAN_POINT('',(0.984769425317267,39.451602969411,0.));
#865=CARTESIAN_POINT('',(0.984769425317267,39.451602969411,0.));
#866=CARTESIAN_POINT('',(1.66533453693773E-15,35.1242922935633,0.));
#867=CARTESIAN_POINT('Origin',(10.0000000000001,35.1242922935632,0.));
#868=CARTESIAN_POINT('',(1.38777878078145E-15,35.1242922935633,0.));
#869=CARTESIAN_POINT('',(5.7,34.8998598294398,0.));
#870=CARTESIAN_POINT('Origin',(13.2,34.8998598294398,0.));
#871=CARTESIAN_POINT('Origin',(22.3,21.3286036771288,0.));
#872=CARTESIAN_POINT('',(22.3,14.8159289474462,-4.));
#873=CARTESIAN_POINT('',(22.3,14.8159289474462,0.));
#874=CARTESIAN_POINT('',(22.3,21.3286036771288,-4.));
#875=CARTESIAN_POINT('',(22.3,21.3286036771288,-4.));
#876=CARTESIAN_POINT('',(22.3,21.3286036771288,0.));
#877=CARTESIAN_POINT('Origin',(11.15,14.8159289474462,4.));
#878=CARTESIAN_POINT('',(10.4125400642996,14.8159289474462,-4.));
#879=CARTESIAN_POINT('Origin',(22.3,14.8159289474462,4.));
#880=CARTESIAN_POINT('Origin',(11.4199384949734,61.5609057599148,-0.543077419028695));
#881=CARTESIAN_POINT('',(14.2277415509208,65.6160641962474,8.88178419700125E-15));
#882=CARTESIAN_POINT('',(12.2576448907075,62.7667275805741,2.00000000000001));
#883=CARTESIAN_POINT('',(12.2576448907075,62.7667275805741,2.00000000000001));
#884=CARTESIAN_POINT('',(14.6422609818447,66.1992215083164,9.23912741421626));
#885=CARTESIAN_POINT('',(11.4199384949734,61.5609057599148,-0.543077419028695));
#886=CARTESIAN_POINT('',(16.612357642058,69.0485581239897,7.23912741421626));
#887=CARTESIAN_POINT('',(14.6422609818447,66.1992215083164,9.23912741421626));
#888=CARTESIAN_POINT('',(13.3900351551867,64.4102423755881,-2.5430774190287));
#889=CARTESIAN_POINT('Origin',(21.1347871665081,52.4173612798062,4.));
#890=CARTESIAN_POINT('',(35.9374892128116,50.6054469432702,8.88178419700125E-15));
#891=CARTESIAN_POINT('',(33.8641929673004,52.0389714999851,8.88178419700125E-15));
#892=CARTESIAN_POINT('',(34.3163924576586,48.2906486402525,-2.86814883911685));
#893=CARTESIAN_POINT('Ctrl Pts',(35.9374892128116,50.6054469432702,2.01257413288768E-14));
#894=CARTESIAN_POINT('Ctrl Pts',(35.5470460430131,50.0506792187227,-1.17496538778508));
#895=CARTESIAN_POINT('Ctrl Pts',(34.9834680316292,49.2455078192994,-2.17243992991942));
#896=CARTESIAN_POINT('Ctrl Pts',(34.3163924576586,48.2906486402525,-2.86814883911684));
#897=CARTESIAN_POINT('',(34.2149488482706,48.1219906783573,-2.98172006842623));
#898=CARTESIAN_POINT('Ctrl Pts',(34.3163924576586,48.2906486402525,-2.86814883911685));
#899=CARTESIAN_POINT('Ctrl Pts',(34.279056097068,48.2372049705795,-2.90708781352018));
#900=CARTESIAN_POINT('Ctrl Pts',(34.2450810592009,48.1807207084264,-2.94510815200922));
#901=CARTESIAN_POINT('Ctrl Pts',(34.2149488482706,48.1219906783573,-2.98172006842623));
#902=CARTESIAN_POINT('',(32.3792633668452,44.6426726430627,-4.));
#903=CARTESIAN_POINT('Ctrl Pts',(34.2149488482706,48.1219906783573,-2.98172006842623));
#904=CARTESIAN_POINT('Ctrl Pts',(33.6684071012095,47.0567381712252,-3.64579151038292));
#905=CARTESIAN_POINT('Ctrl Pts',(33.0242661653287,45.8503286599825,-4.));
#906=CARTESIAN_POINT('Ctrl Pts',(32.3792633668452,44.6426726430627,-4.));
#907=CARTESIAN_POINT('',(10.6785731410083,59.6470273771006,-4.));
#908=CARTESIAN_POINT('',(29.9239996468738,46.3402982686385,-4.));
#909=CARTESIAN_POINT('',(12.516025874751,63.1244265690525,-2.98204073158767));
#910=CARTESIAN_POINT('Ctrl Pts',(10.6785731410083,59.6470273771006,-4.));
#911=CARTESIAN_POINT('Ctrl Pts',(11.32414917682,60.8540208604925,-4.));
#912=CARTESIAN_POINT('Ctrl Pts',(11.9688640400359,62.0597688361594,-3.64590783077776));
#913=CARTESIAN_POINT('Ctrl Pts',(12.516025874751,63.1244265690525,-2.98204073158767));
#914=CARTESIAN_POINT('',(12.6138526676141,63.2876552320489,-2.87238220107572));
#915=CARTESIAN_POINT('Ctrl Pts',(12.516025874751,63.1244265690525,-2.98204073158767));
#916=CARTESIAN_POINT('Ctrl Pts',(12.5451917318259,63.1811769732311,-2.94665402933028));
#917=CARTESIAN_POINT('Ctrl Pts',(12.5779453592402,63.2358261743614,-2.90995420109064));
#918=CARTESIAN_POINT('Ctrl Pts',(12.6138526676141,63.2876552320489,-2.87238220107572));
#919=CARTESIAN_POINT('Ctrl Pts',(12.6138526676141,63.2876552320489,-2.87238220107571));
#920=CARTESIAN_POINT('Ctrl Pts',(13.2789343389105,64.2476424287929,-2.17646680959932));
#921=CARTESIAN_POINT('Ctrl Pts',(13.8402630044747,65.0575724003309,-1.17735065695773));
#922=CARTESIAN_POINT('Ctrl Pts',(14.2277415509208,65.6160641962474,3.88578058618805E-15));
#923=CARTESIAN_POINT('Ctrl Pts',(33.9673925525983,47.7561103275969,2.00000000000001));
#924=CARTESIAN_POINT('Ctrl Pts',(34.6240914393361,48.705889199488,1.33333333333335));
#925=CARTESIAN_POINT('Ctrl Pts',(35.2807903260738,49.6556680713791,0.66666666666669));
#926=CARTESIAN_POINT('Ctrl Pts',(35.9374892128116,50.6054469432702,2.77555756156289E-14));
#927=CARTESIAN_POINT('Ctrl Pts',(33.7698489983282,47.4803319281798,1.41251730610746));
#928=CARTESIAN_POINT('Ctrl Pts',(34.3623596365672,48.3372757983996,0.549844643762724));
#929=CARTESIAN_POINT('Ctrl Pts',(34.9545354047741,49.1937353485029,-0.312292725440348));
#930=CARTESIAN_POINT('Ctrl Pts',(35.5470460430131,50.0506792187227,-1.17496538778508));
#931=CARTESIAN_POINT('Ctrl Pts',(33.4857380232655,47.0793516913248,0.913780035040284));
#932=CARTESIAN_POINT('Ctrl Pts',(33.985084479797,47.8015528762899,-0.115153558674664));
#933=CARTESIAN_POINT('Ctrl Pts',(34.4841215750978,48.5233066343344,-1.14350633620448));
#934=CARTESIAN_POINT('Ctrl Pts',(34.9834680316292,49.2455078192994,-2.17243992991943));
#935=CARTESIAN_POINT('Ctrl Pts',(33.1498782669094,46.603527564658,0.565925580441573));
#936=CARTESIAN_POINT('Ctrl Pts',(33.5387163304925,47.1659012565228,-0.578765892744569));
#937=CARTESIAN_POINT('Ctrl Pts',(33.9275543940756,47.7282749483877,-1.72345736593071));
#938=CARTESIAN_POINT('Ctrl Pts',(34.3163924576586,48.2906486402525,-2.86814883911686));
#939=CARTESIAN_POINT('',(33.9673925525983,47.7561103275969,2.00000000000001));
#940=CARTESIAN_POINT('',(33.9673925525983,47.7561103275969,2.00000000000001));
#941=CARTESIAN_POINT('',(33.1498782669094,46.603527564658,0.565925580441573));
#942=CARTESIAN_POINT('Ctrl Pts',(33.1498782669094,46.603527564658,0.565925580441573));
#943=CARTESIAN_POINT('Ctrl Pts',(33.4857380232655,47.0793516913248,0.913780035040281));
#944=CARTESIAN_POINT('Ctrl Pts',(33.7698489983282,47.4803319281798,1.41251730610745));
#945=CARTESIAN_POINT('Ctrl Pts',(33.9673925525983,47.7561103275969,2.));
#946=CARTESIAN_POINT('',(33.1498782669094,46.603527564658,0.565925580441573));
#947=CARTESIAN_POINT('Origin',(24.9133712984093,42.1548955844801,-14.8985216470948));
#948=CARTESIAN_POINT('',(31.8940963070871,49.1896348843118,2.00000000000001));
#949=CARTESIAN_POINT('',(37.0132701744402,52.0542745116026,11.1237949599359));
#950=CARTESIAN_POINT('',(33.1201343972905,46.5605092201734,-0.537925237615813));
#951=CARTESIAN_POINT('',(36.4024606115291,53.0855844911857,11.9913926634449));
#952=CARTESIAN_POINT('Origin',(36.1923947676877,52.625359893659,11.1288002561733));
#953=CARTESIAN_POINT('',(15.6745424507669,66.0902707929852,10.1007559201297));
#954=CARTESIAN_POINT('',(36.4024606115291,53.0855844911857,11.9913926634449));
#955=CARTESIAN_POINT('Origin',(15.4644766069256,65.6300461954585,9.23816351285809));
#956=CARTESIAN_POINT('Origin',(26.8834679586226,45.0042322001534,-16.8985216470948));
#957=CARTESIAN_POINT('',(17.6446391109802,68.9396074086585,8.10075592012965));
#958=CARTESIAN_POINT('Origin',(17.4345732671389,68.4793828111317,7.23816351285809));
#959=CARTESIAN_POINT('',(38.3725572717424,55.934921106859,9.99139266344487));
#960=CARTESIAN_POINT('',(38.3725572717424,55.934921106859,9.99139266344488));
#961=CARTESIAN_POINT('',(38.9833668346535,54.9036111272759,9.12379495993592));
#962=CARTESIAN_POINT('Origin',(38.162491427901,55.4746965093323,9.12880025617331));
#963=CARTESIAN_POINT('',(35.0902310575038,49.4098458358467,-2.53792523761581));
#964=CARTESIAN_POINT('Origin',(33.6644914366284,47.3258680869962,0.));
#965=CARTESIAN_POINT('',(32.3792633668452,44.6426726430627,-1.76));
#966=CARTESIAN_POINT('',(32.3792633668452,44.6426726430627,0.));
#967=CARTESIAN_POINT('',(23.2812245113133,25.6485213481804,-4.));
#968=CARTESIAN_POINT('',(23.2812245113133,25.6485213481804,0.));
#969=CARTESIAN_POINT('',(23.2812245113133,25.6485213481804,-4.));
#970=CARTESIAN_POINT('',(32.3792633668452,44.6426726430627,0.));
#971=CARTESIAN_POINT('Origin',(20.8250801285992,25.0808065559426,-21.7792895513918));
#972=CARTESIAN_POINT('',(0.740181823536838,9.86021577595932,4.58354700075356));
#973=CARTESIAN_POINT('Origin',(0.999999999999998,10.3430445538867,3.74726302602696));
#974=CARTESIAN_POINT('',(21.0401818235368,7.12927407192345,9.31367678465242));
#975=CARTESIAN_POINT('',(0.740181823536838,9.86021577595932,4.58354700075356));
#976=CARTESIAN_POINT('Origin',(21.3,7.61210284985083,8.47739280992582));
#977=CARTESIAN_POINT('Ctrl Pts',(10.6785731410083,59.6470273771006,0.));
#978=CARTESIAN_POINT('Ctrl Pts',(10.6785731410083,59.6470273771006,-1.33333333333333));
#979=CARTESIAN_POINT('Ctrl Pts',(10.6785731410083,59.6470273771006,-2.66666666666667));
#980=CARTESIAN_POINT('Ctrl Pts',(10.6785731410083,59.6470273771006,-4.));
#981=CARTESIAN_POINT('Ctrl Pts',(10.937437432449,60.2947224487954,0.));
#982=CARTESIAN_POINT('Ctrl Pts',(11.0663737964657,60.4812021837769,-1.33359495271798));
#983=CARTESIAN_POINT('Ctrl Pts',(11.1952128128033,60.6675411255111,-2.66640504728202));
#984=CARTESIAN_POINT('Ctrl Pts',(11.32414917682,60.8540208604925,-4.));
#985=CARTESIAN_POINT('Ctrl Pts',(11.1958711375919,60.9417947666277,0.177046084611119));
#986=CARTESIAN_POINT('Ctrl Pts',(11.4535797501002,61.3145168774885,-1.09752806958861));
#987=CARTESIAN_POINT('Ctrl Pts',(11.7111554275276,61.6870467252986,-2.37133367657803));
#988=CARTESIAN_POINT('Ctrl Pts',(11.9688640400359,62.0597688361594,-3.64590783077776));
#989=CARTESIAN_POINT('Ctrl Pts',(11.4055283284843,61.5183219630731,0.508979634206165));
#990=CARTESIAN_POINT('Ctrl Pts',(11.7756941772399,62.0536901650662,-0.654693821058447));
#991=CARTESIAN_POINT('Ctrl Pts',(12.1458600259955,62.5890583670593,-1.81836727632306));
#992=CARTESIAN_POINT('Ctrl Pts',(12.516025874751,63.1244265690525,-2.98204073158767));
#993=CARTESIAN_POINT('',(10.6785731410083,59.6470273771006,-1.76000000000001));
#994=CARTESIAN_POINT('',(10.6785731410083,59.6470273771006,0.));
#995=CARTESIAN_POINT('',(10.6785731410083,59.6470273771006,0.));
#996=CARTESIAN_POINT('',(11.4055283284843,61.5183219630731,0.508979634206165));
#997=CARTESIAN_POINT('Ctrl Pts',(10.6785731410083,59.6470273771006,0.));
#998=CARTESIAN_POINT('Ctrl Pts',(10.937437432449,60.2947224487954,0.));
#999=CARTESIAN_POINT('Ctrl Pts',(11.1958711375919,60.9417947666277,0.177046084611119));
#1000=CARTESIAN_POINT('Ctrl Pts',(11.4055283284843,61.5183219630731,0.508979634206165));
#1001=CARTESIAN_POINT('',(11.4055283284843,61.5183219630731,0.508979634206166));
#1002=CARTESIAN_POINT('Origin',(13.2,34.8998598294398,0.));
#1003=CARTESIAN_POINT('',(5.7,34.8998598294398,-4.));
#1004=CARTESIAN_POINT('Origin',(13.2,34.8998598294398,-4.));
#1005=CARTESIAN_POINT('',(5.7,34.8998598294398,0.));
#1006=CARTESIAN_POINT('Origin',(36.1923947676877,52.625359893659,11.1288002561733));
#1007=CARTESIAN_POINT('',(37.0132701744402,52.0542745116026,11.1237949599359));
#1008=CARTESIAN_POINT('',(36.4024606115291,53.0855844911857,11.9913926634449));
#1009=CARTESIAN_POINT('Origin',(15.6745424507669,66.0902707929852,10.1007559201297));
#1010=CARTESIAN_POINT('',(15.6745424507669,66.0902707929852,10.1007559201297));
#1011=CARTESIAN_POINT('Origin',(15.4644766069256,65.6300461954585,9.23816351285809));
#1012=CARTESIAN_POINT('Ctrl Pts',(11.4493562786781,61.6034524886894,0.563808899462141));
#1013=CARTESIAN_POINT('Ctrl Pts',(11.8375217416568,62.1648534031426,-0.581588134050479));
#1014=CARTESIAN_POINT('Ctrl Pts',(12.2256872046354,62.7262543175957,-1.7269851675631));
#1015=CARTESIAN_POINT('Ctrl Pts',(12.6138526676141,63.2876552320489,-2.87238220107572));
#1016=CARTESIAN_POINT('Ctrl Pts',(11.7823451711184,62.0831362903232,0.911766595200336));
#1017=CARTESIAN_POINT('Ctrl Pts',(12.2813121193933,62.8047885952292,-0.117839828365512));
#1018=CARTESIAN_POINT('Ctrl Pts',(12.7799673906356,63.5259901238869,-1.14686038603348));
#1019=CARTESIAN_POINT('Ctrl Pts',(13.2789343389105,64.2476424287929,-2.17646680959933));
#1020=CARTESIAN_POINT('Ctrl Pts',(12.0634575606925,62.4877914793541,1.41132467152113));
#1021=CARTESIAN_POINT('Ctrl Pts',(12.6558385282688,63.3445478078281,0.54825292770845));
#1022=CARTESIAN_POINT('Ctrl Pts',(13.2478820368984,64.2008160718569,-0.314278913145048));
#1023=CARTESIAN_POINT('Ctrl Pts',(13.8402630044747,65.0575724003309,-1.17735065695773));
#1024=CARTESIAN_POINT('Ctrl Pts',(12.2576448907075,62.7667275805741,2.));
#1025=CARTESIAN_POINT('Ctrl Pts',(12.9143437774453,63.7165064524652,1.33333333333334));
#1026=CARTESIAN_POINT('Ctrl Pts',(13.5710426641831,64.6662853243563,0.66666666666667));
#1027=CARTESIAN_POINT('Ctrl Pts',(14.2277415509208,65.6160641962474,3.88578058618805E-15));
#1028=CARTESIAN_POINT('',(11.4493562786781,61.6034524886894,0.563808899462141));
#1029=CARTESIAN_POINT('',(11.4493562786781,61.6034524886894,0.563808899462139));
#1030=CARTESIAN_POINT('Ctrl Pts',(11.4493562786781,61.6034524886894,0.563808899462141));
#1031=CARTESIAN_POINT('Ctrl Pts',(11.7823451711184,62.0831362903232,0.911766595200336));
#1032=CARTESIAN_POINT('Ctrl Pts',(12.0634575606925,62.4877914793541,1.41132467152113));
#1033=CARTESIAN_POINT('Ctrl Pts',(12.2576448907075,62.7667275805741,2.));
#1034=CARTESIAN_POINT('Ctrl Pts',(11.4055283284843,61.5183219630731,0.508979634206165));
#1035=CARTESIAN_POINT('Ctrl Pts',(11.4167038734078,61.549053108311,0.52667298533486));
#1036=CARTESIAN_POINT('Ctrl Pts',(11.4313784342088,61.5775546855615,0.545022899454681));
#1037=CARTESIAN_POINT('Ctrl Pts',(11.4493562786781,61.6034524886894,0.563808899462141));
#1038=CARTESIAN_POINT('Ctrl Pts',(11.7756941772399,62.0536901650662,-0.654693821058447));
#1039=CARTESIAN_POINT('Ctrl Pts',(11.7928664928805,62.0930943966177,-0.631102686220186));
#1040=CARTESIAN_POINT('Ctrl Pts',(11.8135674092193,62.1303118484948,-0.606636134060425));
#1041=CARTESIAN_POINT('Ctrl Pts',(11.8375217416568,62.1648534031426,-0.581588134050479));
#1042=CARTESIAN_POINT('Ctrl Pts',(12.1458600259954,62.5890583670593,-1.81836727632306));
#1043=CARTESIAN_POINT('Ctrl Pts',(12.1690291123532,62.6371356849244,-1.78887835777523));
#1044=CARTESIAN_POINT('Ctrl Pts',(12.1957563842297,62.6830690114281,-1.75829516757553));
#1045=CARTESIAN_POINT('Ctrl Pts',(12.2256872046354,62.7262543175957,-1.7269851675631));
#1046=CARTESIAN_POINT('Ctrl Pts',(12.516025874751,63.1244265690525,-2.98204073158767));
#1047=CARTESIAN_POINT('Ctrl Pts',(12.5451917318259,63.1811769732311,-2.94665402933028));
#1048=CARTESIAN_POINT('Ctrl Pts',(12.5779453592402,63.2358261743614,-2.90995420109064));
#1049=CARTESIAN_POINT('Ctrl Pts',(12.6138526676141,63.2876552320489,-2.87238220107572));
#1050=CARTESIAN_POINT('Ctrl Pts',(11.4493562786781,61.6034524886894,0.563808899462141));
#1051=CARTESIAN_POINT('Ctrl Pts',(11.4313784342088,61.5775546855615,0.545022899454681));
#1052=CARTESIAN_POINT('Ctrl Pts',(11.4167038734078,61.549053108311,0.52667298533486));
#1053=CARTESIAN_POINT('Ctrl Pts',(11.4055283284843,61.5183219630731,0.508979634206165));
#1054=CARTESIAN_POINT('Origin',(0.984769425317267,39.451602969411,0.));
#1055=CARTESIAN_POINT('',(0.984769425317267,39.451602969411,-4.));
#1056=CARTESIAN_POINT('',(0.984769425317267,39.451602969411,-4.));
#1057=CARTESIAN_POINT('',(0.984769425317267,39.451602969411,0.));
#1058=CARTESIAN_POINT('Origin',(10.0000000000001,35.1242922935632,0.));
#1059=CARTESIAN_POINT('',(2.22044604925031E-15,35.1242922935633,-4.));
#1060=CARTESIAN_POINT('Origin',(10.0000000000001,35.1242922935632,-4.));
#1061=CARTESIAN_POINT('',(1.66533453693773E-15,35.1242922935633,0.));
#1062=CARTESIAN_POINT('Origin',(0.,6.7665054957103,0.));
#1063=CARTESIAN_POINT('',(1.38777878078145E-15,35.1242922935633,-4.));
#1064=CARTESIAN_POINT('Origin',(0.999999999999998,10.3430445538867,3.74726302602696));
#1065=CARTESIAN_POINT('',(0.740181823536838,9.86021577595932,4.58354700075356));
#1066=CARTESIAN_POINT('Origin',(21.0401818235368,7.12927407192345,9.31367678465242));
#1067=CARTESIAN_POINT('',(21.0401818235368,7.12927407192345,9.31367678465242));
#1068=CARTESIAN_POINT('Origin',(21.3,7.61210284985083,8.47739280992582));
#1069=CARTESIAN_POINT('Origin',(22.3,7.88772571717069,3.88578058618805E-15));
#1070=CARTESIAN_POINT('Origin',(32.2999999999998,21.3286036771288,0.));
#1071=CARTESIAN_POINT('Origin',(32.2999999999998,21.3286036771288,-4.));
#1072=CARTESIAN_POINT('Ctrl Pts',(33.104288295163,46.5156503167576,0.509139965786887));
#1073=CARTESIAN_POINT('Ctrl Pts',(33.4745084795322,47.0510971039575,-0.654480045617485));
#1074=CARTESIAN_POINT('Ctrl Pts',(33.8447286639014,47.5865438911574,-1.81810005702186));
#1075=CARTESIAN_POINT('Ctrl Pts',(34.2149488482706,48.1219906783573,-2.98172006842623));
#1076=CARTESIAN_POINT('Ctrl Pts',(32.8952900257641,45.9385845111757,0.177104244808538));
#1077=CARTESIAN_POINT('Ctrl Pts',(33.1530400646928,46.31136653677,-1.09745068365783));
#1078=CARTESIAN_POINT('Ctrl Pts',(33.4106570622807,46.6839561456309,-2.37123658191656));
#1079=CARTESIAN_POINT('Ctrl Pts',(33.6684071012095,47.0567381712252,-3.64579151038293));
#1080=CARTESIAN_POINT('Ctrl Pts',(32.6374921619553,45.2909402035385,0.));
#1081=CARTESIAN_POINT('Ctrl Pts',(32.7664493049811,45.4774499910513,-1.33359511894594));
#1082=CARTESIAN_POINT('Ctrl Pts',(32.8953090223029,45.6638188724697,-2.66640488105406));
#1083=CARTESIAN_POINT('Ctrl Pts',(33.0242661653287,45.8503286599825,-4.));
#1084=CARTESIAN_POINT('Ctrl Pts',(32.3792633668452,44.6426726430627,0.));
#1085=CARTESIAN_POINT('Ctrl Pts',(32.3792633668452,44.6426726430627,-1.33333333333333));
#1086=CARTESIAN_POINT('Ctrl Pts',(32.3792633668452,44.6426726430627,-2.66666666666667));
#1087=CARTESIAN_POINT('Ctrl Pts',(32.3792633668452,44.6426726430627,-4.));
#1088=CARTESIAN_POINT('',(33.104288295163,46.5156503167576,0.509139965786887));
#1089=CARTESIAN_POINT('',(33.104288295163,46.5156503167576,0.509139965786889));
#1090=CARTESIAN_POINT('Ctrl Pts',(32.3792633668452,44.6426726430627,0.));
#1091=CARTESIAN_POINT('Ctrl Pts',(32.6374921619553,45.2909402035385,-2.48696640090622E-16));
#1092=CARTESIAN_POINT('Ctrl Pts',(32.8952900257641,45.9385845111757,0.177104244808537));
#1093=CARTESIAN_POINT('Ctrl Pts',(33.104288295163,46.5156503167576,0.509139965786885));
#1094=CARTESIAN_POINT('Ctrl Pts',(33.104288295163,46.5156503167576,0.509139965786887));
#1095=CARTESIAN_POINT('Ctrl Pts',(33.115810891509,46.5474653952359,0.527445923995392));
#1096=CARTESIAN_POINT('Ctrl Pts',(33.1310801254986,46.5768955879163,0.546456093239912));
#1097=CARTESIAN_POINT('Ctrl Pts',(33.1498782669094,46.603527564658,0.565925580441573));
#1098=CARTESIAN_POINT('Ctrl Pts',(33.4745084795322,47.0510971039575,-0.654480045617485));
#1099=CARTESIAN_POINT('Ctrl Pts',(33.4922342807396,47.0918838329661,-0.630072101339477));
#1100=CARTESIAN_POINT('Ctrl Pts',(33.5137387826884,47.130332048804,-0.604725209013451));
#1101=CARTESIAN_POINT('Ctrl Pts',(33.5387163304925,47.1659012565228,-0.578765892744569));
#1102=CARTESIAN_POINT('Ctrl Pts',(33.8447286639014,47.5865438911574,-1.81810005702186));
#1103=CARTESIAN_POINT('Ctrl Pts',(33.8686576699703,47.6363022706962,-1.78759012667435));
#1104=CARTESIAN_POINT('Ctrl Pts',(33.8963974398782,47.6837685096918,-1.75590651126681));
#1105=CARTESIAN_POINT('Ctrl Pts',(33.9275543940756,47.7282749483877,-1.72345736593071));
#1106=CARTESIAN_POINT('Ctrl Pts',(34.2149488482706,48.1219906783573,-2.98172006842623));
#1107=CARTESIAN_POINT('Ctrl Pts',(34.2450810592009,48.1807207084264,-2.94510815200922));
#1108=CARTESIAN_POINT('Ctrl Pts',(34.279056097068,48.2372049705795,-2.90708781352018));
#1109=CARTESIAN_POINT('Ctrl Pts',(34.3163924576586,48.2906486402525,-2.86814883911685));
#1110=CARTESIAN_POINT('Ctrl Pts',(33.1498782669094,46.603527564658,0.565925580441573));
#1111=CARTESIAN_POINT('Ctrl Pts',(33.1310801254986,46.5768955879163,0.546456093239912));
#1112=CARTESIAN_POINT('Ctrl Pts',(33.115810891509,46.5474653952359,0.527445923995392));
#1113=CARTESIAN_POINT('Ctrl Pts',(33.104288295163,46.5156503167576,0.509139965786887));
#1114=CARTESIAN_POINT('Origin',(37.0132701744402,52.0542745116026,11.1237949599359));
#1115=CARTESIAN_POINT('Origin',(21.1347871665081,52.4173612798062,4.));
#1116=CARTESIAN_POINT('Origin',(20.8250801285992,36.242029499822,-4.));
#1117=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#1121,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#1118=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.01),#1121,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#1119=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1117))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1121,#1123,#1124))
REPRESENTATION_CONTEXT('','3D')
);
#1120=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1118))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1121,#1123,#1124))
REPRESENTATION_CONTEXT('','3D')
);
#1121=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#1122=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT($,.METRE.)
);
#1123=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#1124=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#1125=SHAPE_DEFINITION_REPRESENTATION(#1126,#1127);
#1126=PRODUCT_DEFINITION_SHAPE('',$,#1129);
#1127=SHAPE_REPRESENTATION('',(#619),#1119);
#1128=PRODUCT_DEFINITION_CONTEXT('part definition',#1133,'design');
#1129=PRODUCT_DEFINITION('S_1136','S_1136 v0',#1130,#1128);
#1130=PRODUCT_DEFINITION_FORMATION('',$,#1135);
#1131=PRODUCT_RELATED_PRODUCT_CATEGORY('S_1136 v0','S_1136 v0',(#1135));
#1132=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2009,#1133);
#1133=APPLICATION_CONTEXT(
'Core Data for Automotive Mechanical Design Process');
#1134=PRODUCT_CONTEXT('part definition',#1133,'mechanical');
#1135=PRODUCT('S_1136','S_1136 v0',$,(#1134));
#1136=PRESENTATION_STYLE_ASSIGNMENT((#1137));
#1137=SURFACE_STYLE_USAGE(.BOTH.,#1138);
#1138=SURFACE_SIDE_STYLE('',(#1139));
#1139=SURFACE_STYLE_FILL_AREA(#1140);
#1140=FILL_AREA_STYLE('Steel - Satin',(#1141));
#1141=FILL_AREA_STYLE_COLOUR('Steel - Satin',#1142);
#1142=COLOUR_RGB('Steel - Satin',0.627450980392157,0.627450980392157,0.627450980392157);
ENDSEC;
END-ISO-10303-21;
