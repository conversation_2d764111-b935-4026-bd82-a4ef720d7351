package com.tset.core.api.calculation

import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity.Companion.REFRESH
import com.nu.bom.core.manufacturing.annotations.toMetaInfoEntry
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostManufacturedMaterial
import com.nu.bom.core.manufacturing.entities.Attachment
import com.nu.bom.core.manufacturing.entities.ExternalManufacturingStep
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.RoughManufacturing
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.dynamicUnitStringToDenominatorUnit
import com.nu.bom.core.manufacturing.factories.FieldFactoryService
import com.nu.bom.core.manufacturing.fieldTypes.CalculationQualityConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.CustomProcurementType
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.EmissionUnits
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.NumericFieldResult
import com.nu.bom.core.manufacturing.fieldTypes.NumericFieldResultWithUnit
import com.nu.bom.core.manufacturing.service.CalculationRequestBuilder
import com.nu.bom.core.manufacturing.service.CalculationService
import com.nu.bom.core.manufacturing.service.ConfigurationManagementService
import com.nu.bom.core.manufacturing.utils.ManufacturingTreeUtils
import com.nu.bom.core.model.BomEntryRelation
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.configurations.DefaultValuesConfiguration
import com.nu.bom.core.model.createBranchId
import com.nu.bom.core.service.BomNodeService
import com.nu.bom.core.service.UnitConversionService
import com.nu.bom.core.service.configurations.ConfigType
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.InterpolationData
import com.nu.bom.core.utils.Maybe
import com.nu.bom.core.utils.component1
import com.nu.bom.core.utils.component2
import com.nu.bom.core.utils.component3
import com.nu.bom.core.utils.hasValue
import com.nu.bom.core.utils.toObjectId
import com.nu.bom.core.utils.toPathVariable
import com.tset.bom.clients.common.DenominatorUnit
import com.tset.core.api.calculation.dto.CalculationCreationModalMode
import com.tset.core.api.calculation.dto.CalculationUpdateAction
import com.tset.core.api.calculation.dto.CalculationUpdateDispatchItem
import com.tset.core.api.calculation.dto.CalculationUpdatePayloadDto
import com.tset.core.api.calculation.dto.CalculationUpdateResponseDto
import com.tset.core.service.domain.calculation.CalculationType
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono
import java.math.BigDecimal

private const val CHANGE_CALCULATION = "changeCalculation"
private const val ADD_CALCULATION = "addCalculation"
private const val REPLACE_AND_CONTINUE = "replaceAndContinue"
private const val REPLACE_CALCULATION = "replaceCalculation"
private const val REPLACE_WITH_TSET_FILE = "replaceWithTsetFile"
private const val START_BOM_IMPORT = "startBomImport"
private const val REPLACE_WITH_EXCEL_FILE = "replaceWithExcelFile"
private const val CONTINUE = "continue"
private const val IMPORT_CALCULATION = "Import calculation"

data class ModalProcessorServices(
    val entityManager: EntityManager,
    val unitConversionService: UnitConversionService,
    val fieldFactoryService: FieldFactoryService,
    val bomNodeService: BomNodeService,
    val configurationService: ConfigurationManagementService,
    val calculationService: CalculationService,
)

abstract class ModalProcessor(
    val accessCheck: AccessCheck,
    val projectId: String,
    val payload: CalculationUpdatePayloadDto,
    val action: CalculationUpdateAction,
    val services: ModalProcessorServices,
    private val isCo2ExtensionEnabled: Boolean,
) {
    val mode: CalculationCreationModalMode = payload.input.mode
    val calculationFields: Map<String, FieldParameter> = payload.data.fields.associateBy { it.name }

    abstract fun process(): Mono<CalculationUpdateResponseDto>

    protected fun toResponse(updatedPayload: CalculationUpdatePayloadDto): CalculationUpdateResponseDto =
        CalculationUpdateResponseDto(
            payload = updatedPayload,
            dispatch =
                listOf(
                    CalculationUpdateDispatchItem(CalculationUpdateAction.REFRESH, REFRESH),
                    CalculationUpdateDispatchItem(
                        action,
                        getLabelForTypeAndMode(updatedPayload.selectedType, updatedPayload.input.mode),
                    ),
                ),
        )

    // called from wizard
    abstract fun getUpdatedFields(): Mono<CalculationUpdatePayloadDto>

    protected fun getTargetSnapshot(): Mono<Maybe<BomNodeSnapshot>> =
        when (mode) {
            CalculationCreationModalMode.CALCULATION_MODE_NEW -> Mono.just(Maybe(null))
            CalculationCreationModalMode.CALCULATION_MODE_EDIT,
            CalculationCreationModalMode.CALCULATION_MODE_CHANGE_TYPE,
            ->
                services.bomNodeService
                    .getNodeWithPartAndBranch(
                        accessCheck,
                        BomNodeId(payload.input.context.bomNodeId),
                        createBranchId(payload.input.context.branchId),
                    ).map { Maybe(it) }
        }

    private fun handleDimensionChange(newField: FieldParameter): FieldParameter {
        val oldField = payload.data.fields.find { it.name == newField.name } ?: return newField

        val adjustedOldField =
            oldField.copy(
                // this is kinda hacky ... makes calculated volume appear consistent over dimension change
                value = if (oldField.source == FieldResult.SOURCE.I.toString()) oldField.value else newField.value,
            )
        return newField.copy(
            value =
                services.unitConversionService.keepValueOverDimensionChange(
                    adjustedOldField,
                    newField,
                    Manufacturing::class.simpleName!!,
                    entityForMetaData = null,
                ) ?: newField.value,
            source = oldField.source,
        )
    }

    protected fun getDefaultPurchasedCustomProcurementTypeField(
        snapshot: Maybe<BomNodeSnapshot>,
        calculationFields: Map<String, FieldParameter>?,
        isReadOnly: Boolean,
    ) = services.configurationService
        .getDefaultConfiguration(
            accessCheck,
            ConfigType.CommercialCo2Operations,
        ).flatMap { co2Operation ->
            services.configurationService
                .getDefaultConfiguration(
                    accessCheck,
                    ConfigType.CommercialCostOperations,
                ).map { costOperation ->
                    assert(
                        co2Operation.procurementTypeConfiguration.procurementTypeHeaderKey ==
                            costOperation.procurementTypeConfiguration.procurementTypeHeaderKey,
                    )
                    assert(
                        co2Operation.procurementTypeConfiguration.getDefaultNotInhouseProcurementType() ==
                            costOperation.procurementTypeConfiguration.getDefaultNotInhouseProcurementType(),
                    )
                    val procType =
                        CustomProcurementType.fromCustomProcurementTypeWrapper(
                            costOperation.procurementTypeConfiguration.getDefaultNotInhouseProcurementType(),
                        )
                    val headerKey = costOperation.procurementTypeConfiguration.procurementTypeHeaderKey

                    getFieldProcurementType(snapshot, calculationFields, procType, isReadOnly, headerKey)
                }
        }

    protected fun postProcessFields(
        fields: List<FieldParameter?>,
        interpolationData: InterpolationData,
    ): List<FieldParameter> =
        fields
            .filterNotNull()
            .map { addMetaInfo(it, interpolationData) }
            .map { handleDimensionChange(it) }

    protected fun getIdentifyingBaseFields(maybeSnapshot: Maybe<BomNodeSnapshot>): List<FieldParameter> =
        listOfNotNull(
            getFieldPartDesignation(maybeSnapshot, calculationFields, payload.input.mode),
            getFieldPartNumber(maybeSnapshot, calculationFields, payload.input.mode),
            getFieldPartImage(maybeSnapshot, calculationFields, payload.input.mode),
            getFieldPartAttachments(maybeSnapshot, calculationFields, payload.input.mode),
        )

    protected fun getCalculationBaseFields(
        maybeSnapshot: Maybe<BomNodeSnapshot>,
        etc: CalculationCreationUtils.FieldsDimensionsEtc,
    ): Mono<List<FieldParameter>> =
        getFieldCalculationQuality(maybeSnapshot, calculationFields).map { calcQuality ->
            listOfNotNull(
                CalculationCreationUtils.getBaseCurrency(maybeSnapshot, calculationFields, services.entityManager),
                etc.dimensionField,
                etc.quantityUnitField,
                etc.costUnitField,
                CalculationCreationUtils.getFieldLifetime(maybeSnapshot, calculationFields, services.entityManager),
                getFieldAverageProductionVolumePerYear(etc, maybeSnapshot),
                getFieldProductionVolumePerYear(etc, maybeSnapshot),
                getFieldLocation(maybeSnapshot, calculationFields),
                getFieldCalculationTitle(maybeSnapshot, calculationFields),
                calcQuality,
                CalculationCreationUtils.getFieldCalculationDate(maybeSnapshot, calculationFields, services.entityManager),
            )
        }

    protected fun getBaseFields(
        maybeSnapshot: Maybe<BomNodeSnapshot>,
        etc: CalculationCreationUtils.FieldsDimensionsEtc,
    ): Mono<List<FieldParameter>> =
        getCalculationBaseFields(maybeSnapshot, etc).map {
            getIdentifyingBaseFields(maybeSnapshot) + it
        }

    protected fun updatePayload(
        updatedFields: List<FieldParameter?>,
        entity: ManufacturingEntity?,
    ): CalculationUpdatePayloadDto =
        payload.copy(
            data =
                payload.data.copy(
                    fields = postProcessFields(updatedFields, InterpolationData(entity = entity)),
                ),
        )

    protected fun getResponse(
        updatedFields: List<FieldParameter?>,
        entity: ManufacturingEntity?,
    ) = toResponse(updatePayload(updatedFields, entity))

    private fun getLabelForTypeAndMode(
        type: CalculationType,
        mode: CalculationCreationModalMode,
    ) = when (type) {
        CalculationType.DETAILED_CALCULATION ->
            when (mode) {
                CalculationCreationModalMode.CALCULATION_MODE_NEW,
                CalculationCreationModalMode.CALCULATION_MODE_EDIT,
                -> CONTINUE

                CalculationCreationModalMode.CALCULATION_MODE_CHANGE_TYPE -> REPLACE_AND_CONTINUE
            }

        CalculationType.MANUAL_CALCULATION ->
            when (mode) {
                CalculationCreationModalMode.CALCULATION_MODE_NEW -> ADD_CALCULATION
                CalculationCreationModalMode.CALCULATION_MODE_EDIT -> CHANGE_CALCULATION
                CalculationCreationModalMode.CALCULATION_MODE_CHANGE_TYPE -> REPLACE_CALCULATION
            }

        CalculationType.ROUGH_CALCULATION ->
            when (mode) {
                CalculationCreationModalMode.CALCULATION_MODE_NEW -> ADD_CALCULATION
                CalculationCreationModalMode.CALCULATION_MODE_EDIT -> CHANGE_CALCULATION
                CalculationCreationModalMode.CALCULATION_MODE_CHANGE_TYPE -> REPLACE_CALCULATION
            }

        CalculationType.TSET_FILE_IMPORT ->
            when (mode) {
                CalculationCreationModalMode.CALCULATION_MODE_NEW,
                CalculationCreationModalMode.CALCULATION_MODE_EDIT,
                -> IMPORT_CALCULATION

                CalculationCreationModalMode.CALCULATION_MODE_CHANGE_TYPE -> REPLACE_WITH_TSET_FILE
            }

        CalculationType.EXCEL_FILE_IMPORT ->
            when (mode) {
                CalculationCreationModalMode.CALCULATION_MODE_NEW,
                CalculationCreationModalMode.CALCULATION_MODE_EDIT,
                -> START_BOM_IMPORT

                CalculationCreationModalMode.CALCULATION_MODE_CHANGE_TYPE -> REPLACE_WITH_EXCEL_FILE
            }

        CalculationType.ACCOUNT_SPECIFIC_IMPORT ->
            when (mode) {
                CalculationCreationModalMode.CALCULATION_MODE_NEW,
                CalculationCreationModalMode.CALCULATION_MODE_EDIT,
                -> IMPORT_CALCULATION

                CalculationCreationModalMode.CALCULATION_MODE_CHANGE_TYPE -> REPLACE_WITH_EXCEL_FILE
            }
    }

    private fun getFieldAverageProductionVolumePerYear(
        etc: CalculationCreationUtils.FieldsDimensionsEtc,
        maybeSnapshot: Maybe<BomNodeSnapshot>,
    ) = CalculationCreationUtils.getFieldAnyProductionVolumePerYear(
        "averageUsableProductionVolumePerYear",
        metaInfo =
            mapOf(
                "frontendOnly" to true,
                "section" to "economic",
            ),
        etc.dimension,
        etc.quantityUnit,
        null,
        maybeSnapshot,
        services.entityManager,
    )

    private fun getFieldProductionVolumePerYear(
        etc: CalculationCreationUtils.FieldsDimensionsEtc,
        maybeSnapshot: Maybe<BomNodeSnapshot>,
    ) = CalculationCreationUtils.getFieldAnyProductionVolumePerYear(
        // Todo: Might need to be changed to average volume. See COST-50914
        "peakUsableProductionVolumePerYear",
        metaInfo =
            mapOf(
                "frontendOnly" to true,
                "section" to "economic",
            ),
        etc.dimension,
        etc.quantityUnit,
        null,
        maybeSnapshot,
        services.entityManager,
    )

    private fun getFieldPartDesignation(
        snapshot: Maybe<BomNodeSnapshot>,
        calculationFields: Map<String, FieldParameter>?,
        mode: CalculationCreationModalMode,
    ) = CalculationCreationUtils.getFieldValue(
        "partDesignation",
        FieldParameter(
            name = "partDesignation",
            type = "Text",
            metaInfo =
                mapOf(
                    "section" to "part",
                ) + addReadOnlyIfChangeType(mode),
            denominatorUnit = null,
        ),
        calculationFields,
        snapshot,
        entityManager = services.entityManager,
    )

    private fun getFieldPartNumber(
        snapshot: Maybe<BomNodeSnapshot>,
        calculationFields: Map<String, FieldParameter>?,
        mode: CalculationCreationModalMode,
    ) = CalculationCreationUtils.getFieldValue(
        "partNumber",
        FieldParameter(
            name = "partNumber",
            value = "",
            type = "Text",
            metaInfo =
                mapOf(
                    "section" to "part",
                    "optional" to true,
                ) + addReadOnlyIfChangeType(mode),
            denominatorUnit = null,
        ),
        calculationFields,
        snapshot,
        entityManager = services.entityManager,
    )

    private fun getFieldPartImage(
        snapshot: Maybe<BomNodeSnapshot>,
        calculationFields: Map<String, FieldParameter>?,
        mode: CalculationCreationModalMode,
    ) = getPartFieldValue(
        "partImage",
        FieldParameter(
            name = "partImage",
            type = "Text",
            metaInfo =
                mapOf(
                    "section" to "",
                    "hidden" to true,
                ) + addReadOnlyIfChangeType(mode),
            denominatorUnit = null,
        ),
        calculationFields,
        snapshot,
    )

    private fun getFieldPartAttachments(
        snapshot: Maybe<BomNodeSnapshot>,
        calculationFields: Map<String, FieldParameter>?,
        mode: CalculationCreationModalMode,
    ) = getPartFieldValue(
        "partAttachments",
        FieldParameter(
            name = "partAttachments",
            type = "ListOfStrings",
            metaInfo =
                mapOf(
                    "section" to "",
                    "hidden" to true,
                ) + addReadOnlyIfChangeType(mode),
            denominatorUnit = null,
        ),
        calculationFields,
        snapshot,
    )

    private fun getFieldProcurementType(
        snapshot: Maybe<BomNodeSnapshot>,
        calculationFields: Map<String, FieldParameter>?,
        manufacturingType: CustomProcurementType,
        isReadOnly: Boolean,
        customProcurementTypeLovTypeKey: String,
    ) = if (isReadOnly) {
        FieldParameter(
            name = CommercialCalculationCostManufacturedMaterial::customProcurementType.name,
            type = CustomProcurementType::class.java.simpleName,
            value = manufacturingType.toString(),
            metaInfo =
                mapOf(
                    "readOnly" to true,
                    "wizardReadOnly" to true,
                    "section" to "economic",
                    "path" to "/api/md/v1/lovtypes/$customProcurementTypeLovTypeKey/entries",
                ),
            denominatorUnit = null,
        )
    } else {
        CalculationCreationUtils.getFieldValue(
            CommercialCalculationCostManufacturedMaterial::customProcurementType.name,
            FieldParameter(
                name = CommercialCalculationCostManufacturedMaterial::customProcurementType.name,
                type = CustomProcurementType::class.java.simpleName,
                metaInfo =
                    mapOf(
                        "section" to "economic",
                        "path" to "/api/md/v1/lovtypes/$customProcurementTypeLovTypeKey/entries",
                    ),
                denominatorUnit = null,
            ),
            calculationFields,
            snapshot,
            manufacturingType,
            services.entityManager,
        )
    }

    protected fun getFieldCO2PerPart(
        snapshot: Maybe<BomNodeSnapshot>,
        calculationFields: Map<String, FieldParameter>?,
        denominatorUnit: DenominatorUnit,
    ) = if (isCo2ExtensionEnabled) {
        CalculationCreationUtils.getFieldValue(
            "cO2PerPart",
            FieldParameter(
                name = "cO2PerPart",
                type = "Emission",
                unit = EmissionUnits.KILOGRAM_CO2E.name,
                denominatorUnit = denominatorUnit,
                metaInfo =
                    mapOf(
                        "section" to "calculation",
                    ),
            ),
            calculationFields,
            snapshot,
            entityManager = services.entityManager,
        )
    } else {
        null
    }

    protected fun getFieldCostPerPart(
        snapshot: Maybe<BomNodeSnapshot>,
        calculationFields: Map<String, FieldParameter>?,
        denominatorUnit: DenominatorUnit,
    ): FieldParameter =
        CalculationCreationUtils.getFieldValue(
            "costPerPart",
            FieldParameter(
                name = "costPerPart",
                type = "Money",
                denominatorUnit = denominatorUnit,
                metaInfo =
                    mapOf(
                        "section" to "calculation",
                    ),
            ),
            calculationFields,
            snapshot,
            entityManager = services.entityManager,
        )

    protected fun getFieldCalculationTitle(
        snapshot: Maybe<BomNodeSnapshot>,
        calculationFields: Map<String, FieldParameter>?,
    ) = CalculationCreationUtils.getFieldValue(
        "calculationTitle",
        FieldParameter(
            name = "calculationTitle",
            type = "Text",
            metaInfo =
                mapOf(
                    "frontendOnly" to true,
                    "section" to "calculation",
                ),
            denominatorUnit = null,
        ),
        calculationFields,
        snapshot,
        entityManager = services.entityManager,
    )

    protected fun getFieldCalculationQuality(
        snapshot: Maybe<BomNodeSnapshot>,
        calculationFields: Map<String, FieldParameter>?,
    ) = services.configurationService
        .getDefaultConfigurationKey(
            accessCheck,
            ConfigType.CalculationQuality,
        ).map { configId ->
            CalculationCreationUtils.getFieldValue(
                Manufacturing::calculationQualityConfigurationKey.name,
                FieldParameter(
                    name = Manufacturing::calculationQualityConfigurationKey.name,
                    type = CalculationQualityConfigurationKey::class.simpleName!!,
                    metaInfo =
                        services.entityManager.getMetaInfo(
                            "Manufacturing",
                            Manufacturing::calculationQualityConfigurationKey.name,
                            InterpolationData(),
                        ) + mapOf("section" to "calculation"),
                    denominatorUnit = null,
                ),
                calculationFields,
                snapshot,
                CalculationQualityConfigurationKey(configId),
                entityManager = services.entityManager,
            )
        }

    protected fun setFieldCalculationType(
        calculationType: CalculationType,
        forSub: Boolean,
    ) = FieldParameter(
        name = "calculationType",
        type = "CalculationType",
        value = calculationType,
        metaInfo =
            mapOf(
                "path" to "/api/calculationType?sub=$forSub",
                "triggerUpdate" to true,
                "section" to "",
                "dontSortOptions" to true,
            ),
        denominatorUnit = null,
    )

    protected fun getFieldQuantity(
        fieldDimension: Dimension.Selection,
        quantityUnit: String,
        parentSnapshot: BomNodeSnapshot,
        targetBomNodeId: String?,
        bomEntryFields: Map<String, FieldParameter>,
        mode: CalculationCreationModalMode,
    ): FieldParameter {
        val parentQuantityUnitString =
            CalculationCreationUtils
                .getFieldValue(
                    "quantityUnit",
                    FieldParameter(
                        name = "quantityUnit",
                        type = "Text",
                        denominatorUnit = null,
                    ),
                    mapOf(),
                    Maybe(parentSnapshot),
                    entityManager = services.entityManager,
                ).value as String

        val defaultValue =
            when (mode) {
                CalculationCreationModalMode.CALCULATION_MODE_NEW -> BigDecimal.ONE
                CalculationCreationModalMode.CALCULATION_MODE_CHANGE_TYPE,
                CalculationCreationModalMode.CALCULATION_MODE_EDIT,
                ->
                    getBomEntryQuantityFromSnapshot(
                        parentSnapshot,
                        targetBomNodeId,
                    )
            }

        val additionalMetaInfo =
            when (mode) {
                CalculationCreationModalMode.CALCULATION_MODE_NEW -> emptyMap()
                CalculationCreationModalMode.CALCULATION_MODE_CHANGE_TYPE,
                CalculationCreationModalMode.CALCULATION_MODE_EDIT,
                -> mapOf("wizardReadOnly" to true)
            }

        val maybePreexistingValue =
            bomEntryFields["quantity"]?.takeIf(FieldParameter::hasValue)?.let {
                when (val fieldResult = services.fieldFactoryService.toFieldResult(it)) {
                    is NumericFieldResultWithUnit<*, *> -> fieldResult.toInputUnit()
                    is NumericFieldResult<*> -> fieldResult.res
                    else -> error("Unexpected quantity result")
                }
            }

        val quantityField =
            FieldParameter(
                name = "quantity",
                type = fieldDimension.type,
                unit = fieldDimension.baseUnit,
                value = maybePreexistingValue ?: defaultValue,
                metaInfo =
                    mapOf(
                        "bomEntry" to true,
                        "triggerUpdate" to true,
                        "section" to "parentCalculation",
                        toMetaInfoEntry(quantityUnit),
                    ) + additionalMetaInfo,
                source = FieldResult.SOURCE.I.toString(),
                denominatorUnit = dynamicUnitStringToDenominatorUnit(parentQuantityUnitString),
            )

        return quantityField
    }

    protected fun getFieldLocation(
        snapshot: Maybe<BomNodeSnapshot>,
        calculationFields: Map<String, FieldParameter>?,
    ) = CalculationCreationUtils.getFieldValue(
        "location",
        FieldParameter(
            name = "location",
            type = "Text",
            metaInfo =
                mapOf(
                    "section" to "economic",
                ),
            denominatorUnit = null,
        ),
        calculationFields,
        snapshot,
        entityManager = services.entityManager,
    )

    fun getFieldStepId(
        parentSnapshot: BomNodeSnapshot,
        bomEntryFields: Map<String, FieldParameter>,
        payload: CalculationUpdatePayloadDto,
    ): FieldParameter {
        val targetBomNodeId = payload.input.context.bomNodeId
        val parentBomNodeId = payload.data.parentBomData?.bomNodeId
        val parentBranchId = payload.data.parentBomData?.branchId
        val contextStepId = payload.input.context.stepId
        return when (payload.input.mode) {
            CalculationCreationModalMode.CALCULATION_MODE_NEW -> // no pre-selected step
                if (contextStepId == null) {
                    bomEntryFields["stepId"]?.takeIf(FieldParameter::hasValue)
                        ?: FieldParameter(
                            name = "stepId",
                            type = "Text",
                            value = parentSnapshot.manufacturing?._id?.toHexString(),
                            label = "No step",
                            metaInfo =
                                mapOf(
                                    "path" to "/api/man/$parentBomNodeId/steps?${"branch".toPathVariable(parentBranchId)}&stepless=true",
                                    "triggerUpdate" to true,
                                    "asObject" to true,
                                    "dontSortOptions" to true,
                                    "translationSection" to "steps",
                                    "section" to "parentCalculation",
                                ),
                            denominatorUnit = null,
                        )
                } else {
                    // dialog started from a selected step
                    val parentStep = getParentStepFromStepId(contextStepId, parentSnapshot)
                    fieldParameterStepId(contextStepId, parentStep?.displayName, parentBomNodeId, parentBranchId)
                }

            CalculationCreationModalMode.CALCULATION_MODE_CHANGE_TYPE,
            CalculationCreationModalMode.CALCULATION_MODE_EDIT,
            ->
                fieldParameterStepId(
                    getStepIdFromSnapshot(parentSnapshot, targetBomNodeId)
                        ?: parentSnapshot.manufacturing?._id?.toHexString(),
                    getStepLabelFromSnapshot(parentSnapshot, targetBomNodeId) ?: "No step",
                    parentBomNodeId,
                    parentBranchId,
                )
        }
    }

    private fun fieldParameterStepId(
        stepId: String?,
        label: String?,
        parentBomNodeId: String?,
        parentBranchId: String?,
    ) = FieldParameter(
        name = "stepId",
        type = "Text",
        value = stepId,
        label = label,
        metaInfo =
            mapOf(
                "path" to "/api/man/$parentBomNodeId/steps?${"branch".toPathVariable(parentBranchId)}&stepless=true",
                "triggerUpdate" to true,
                "asObject" to true,
                "dontSortOptions" to true,
                "translationSection" to "steps",
                "wizardReadOnly" to true,
                "frontendOnly" to true,
                "section" to "parentCalculation",
            ),
        denominatorUnit = null,
    )

    private fun getBomEntryQuantityFromSnapshot(
        parentSnapshot: BomNodeSnapshot,
        targetBomNodeId: String?,
    ): BigDecimal? {
        val bomNodeId: BomNodeId = BomNodeId(targetBomNodeId)

        val stepAndBomEntry = getStepAndBomEntry(parentSnapshot, bomNodeId)
        return (stepAndBomEntry.second ?: parentSnapshot.manufacturing)
            ?.getFieldResult("quantity")
            ?.res as BigDecimal?
    }

    private fun getStepIdFromSnapshot(
        parentSnapshot: BomNodeSnapshot,
        targetBomNodeId: String?,
    ): String? {
        val bomNodeId: BomNodeId = BomNodeId(targetBomNodeId)

        val stepAndBomEntry = getStepAndBomEntry(parentSnapshot, bomNodeId)
        return stepAndBomEntry.first?._id?.toHexString()
    }

    private fun getStepLabelFromSnapshot(
        parentSnapshot: BomNodeSnapshot,
        targetBomNodeId: String?,
    ): String? {
        val bomNodeId: BomNodeId = BomNodeId(targetBomNodeId)

        val stepAndBomEntry = getStepAndBomEntry(parentSnapshot, bomNodeId)
        return stepAndBomEntry.first?.displayName
    }

    private fun getStepAndBomEntry(
        parentSnapshot: BomNodeSnapshot,
        bomNodeId: BomNodeId,
    ): Pair<ManufacturingEntity?, ManufacturingEntity?> {
        val bomEntry =
            ManufacturingTreeUtils
                .getDescendantsWithType(
                    parentSnapshot.manufacturing!!,
                    Entities.BOM_ENTRY,
                ).find { bomEntry: ManufacturingEntity ->
                    parentSnapshot.subNodes.find { bomEntryRelation: BomEntryRelation ->
                        bomEntryRelation.bomNodeId == bomNodeId && bomEntryRelation.bomEntryId == bomEntry._id
                    } != null
                }
        val step =
            bomEntry?.let {
                ManufacturingTreeUtils
                    .findParent(parentSnapshot.manufacturing, bomEntry)
                    ?.takeIf { it.getEntityTypeAnnotation() == Entities.MANUFACTURING_STEP }
            }

        return Pair(step, bomEntry)
    }

    private fun addMetaInfo(
        field: FieldParameter,
        interpolationData: InterpolationData,
    ): FieldParameter {
        val usualMetaInfo =
            services.entityManager.getMetaInfo(
                Manufacturing::class.simpleName!!,
                field.name,
                interpolationData,
            )
        val metaInfoOverride = field.metaInfo ?: emptyMap()

        val metaInfo = usualMetaInfo + metaInfoOverride

        return field.copy(
            metaInfo = metaInfo,
        )
    }

    protected fun getParentStepFromStepId(
        stepId: String?,
        parentSnapshot: BomNodeSnapshot,
    ) = stepId?.toObjectId()?.let { parentStepId ->
        ManufacturingTreeUtils.findEntityById(searchRoot = parentSnapshot.manufacturing!!, id = parentStepId)
            ?: error("parent not found stepId=$stepId, snapshot=$parentSnapshot")
    }

    private fun getPartFieldValue(
        fieldName: String,
        fieldTemplate: FieldParameter,
        calculationFields: Map<String, FieldParameter>?,
        snapshot: Maybe<BomNodeSnapshot>,
    ) = calculationFields
        ?.get(fieldName)
        ?.takeIf(FieldParameter::hasValue)
        ?.takeUnless(CalculationCreationUtils::isWizardReadOnly)
        .let { fieldValue ->
            fieldValue
                ?: fieldTemplate.copy(
                    value =
                        snapshot
                            .mapWithNull { bomNodeSnapshot ->
                                when (fieldName) {
                                    "partImage" -> bomNodeSnapshot?.getBaseManufacturing()?.getImage()?.firstOrNull()
                                    "partAttachments" ->
                                        bomNodeSnapshot
                                            ?.getBaseManufacturing()
                                            ?.getAttachments()
                                            ?.mapNotNull {
                                                it.getFieldResult(Attachment::fileId.name)?.res?.toString()
                                            } ?: emptyList<String>()

                                    else -> null
                                }
                            },
                )
        }

    private fun addReadOnlyIfChangeType(mode: CalculationCreationModalMode) =
        when (mode) {
            CalculationCreationModalMode.CALCULATION_MODE_CHANGE_TYPE -> mapOf("wizardReadOnly" to true)
            else -> emptyMap()
        }

    protected fun getFieldsDimensionEtc(
        targetSnapshot: Maybe<BomNodeSnapshot>,
        defaultValues: DefaultValuesConfiguration,
    ) = CalculationCreationUtils.getFieldsDimensionEtc(
        payload.selectedType,
        targetSnapshot,
        calculationFields,
        services.entityManager,
        defaultValues,
    )
}

abstract class RootModalProcessor(
    accessCheck: AccessCheck,
    projectId: String,
    payload: CalculationUpdatePayloadDto,
    action: CalculationUpdateAction,
    services: ModalProcessorServices,
    isCo2ExtensionEnabled: Boolean,
) : ModalProcessor(
        accessCheck,
        projectId,
        payload,
        action,
        services,
        isCo2ExtensionEnabled,
    ) {
    protected open fun getManufacturing(snapshotInContext: BomNodeSnapshot?): Mono<ManufacturingEntity> =
        when {
            payload.input.mode == CalculationCreationModalMode.CALCULATION_MODE_NEW -> {
                // Calculation is new and root, we need a fake calculation to get correct field information
                val manu = getEntityNeededForInterpolation(payload.selectedType)
                services.calculationService
                    .calculate(
                        CalculationRequestBuilder(
                            accessCheck,
                            manu,
                            ignoreFieldErrors = true,
                            executeEntityCreation = false,
                            skipChildLoading = true,
                            enableCurrencyConversion = false,
                        ),
                    ).map { manu }
            }

            else -> {
                // It's root and edit, the info should already exist
                val manufacturing = snapshotInContext?.manufacturing
                requireNotNull(manufacturing) {
                    "If mode edit or change type, there should be a manu"
                }
                Mono.just(manufacturing)
            }
        }

    override fun process(): Mono<CalculationUpdateResponseDto> =
        getTargetSnapshot().flatMap { maybeSnapshot ->
            getFields(maybeSnapshot).flatMap { fields ->
                getManufacturing(maybeSnapshot.value).map { manufacturing ->
                    getResponse(fields, manufacturing)
                }
            }
        }

    abstract fun getFields(maybeSnapshot: Maybe<BomNodeSnapshot>): Mono<List<FieldParameter>>
}

open class CreateRootWithCostModuleProcessor(
    accessCheck: AccessCheck,
    projectId: String,
    payload: CalculationUpdatePayloadDto,
    action: CalculationUpdateAction,
    services: ModalProcessorServices,
    isCo2ExtensionEnabled: Boolean,
) : RootModalProcessor(accessCheck, projectId, payload, action, services, isCo2ExtensionEnabled) {
    override fun getUpdatedFields(): Mono<CalculationUpdatePayloadDto> =
        getTargetSnapshot().flatMap { maybeSnapshot ->
            getFields(maybeSnapshot).flatMap { fields ->
                getManufacturing(maybeSnapshot.value).map { manufacturing ->
                    updatePayload(fields, manufacturing)
                }
            }
        }

    override fun getFields(maybeSnapshot: Maybe<BomNodeSnapshot>): Mono<List<FieldParameter>> =
        services.configurationService
            .getDefaultConfiguration(accessCheck, ConfigType.DefaultValues)
            .map { getFieldsDimensionEtc(maybeSnapshot, it) }
            .flatMap { etc ->
                getDefaultPurchasedCustomProcurementTypeField(maybeSnapshot, calculationFields, false)
                    .flatMap { procTypeField ->
                        getBaseFields(maybeSnapshot, etc).map { baseFields ->
                            baseFields +
                                listOfNotNull(
                                    setFieldCalculationType(payload.selectedType, forSub = false),
                                    procTypeField,
                                )
                        }
                    }
            }
}

class CreateRootNoCostModuleExcelImportProcessor(
    accessCheck: AccessCheck,
    projectId: String,
    payload: CalculationUpdatePayloadDto,
    services: ModalProcessorServices,
    isCo2ExtensionEnabled: Boolean,
) : CreateRootWithCostModuleProcessor(
        accessCheck,
        projectId,
        payload,
        CalculationUpdateAction.REDIRECT_BOM_IMPORTER,
        services,
        isCo2ExtensionEnabled,
    ) {
    override fun getUpdatedFields(): Mono<CalculationUpdatePayloadDto> = Mono.empty()

    override fun getFields(maybeSnapshot: Maybe<BomNodeSnapshot>) =
        Mono
            .zip(
                services.configurationService.getDefaultConfiguration(accessCheck, ConfigType.DefaultValues).flatMap { config ->
                    getCalculationBaseFields(maybeSnapshot, getFieldsDimensionEtc(maybeSnapshot, config))
                },
                Mono.just(
                    setFieldCalculationType(payload.selectedType, forSub = false),
                ),
                getDefaultPurchasedCustomProcurementTypeField(maybeSnapshot, calculationFields, false),
            ).map { (a, b, c) -> a + b + c }
}

class CreateRootAccountSpecificImportProcessor(
    accessCheck: AccessCheck,
    projectId: String,
    payload: CalculationUpdatePayloadDto,
    services: ModalProcessorServices,
    isCo2ExtensionEnabled: Boolean,
) : RootModalProcessor(
        accessCheck,
        projectId,
        payload,
        CalculationUpdateAction.REFRESH,
        services,
        isCo2ExtensionEnabled,
    ) {
    private fun setFieldImportType() =
        FieldParameter(
            name = "importType",
            type = "ImportType",
            value = null,
            metaInfo =
                mapOf(
                    "path" to "/api/import/type/short",
                    "section" to "part",
                ),
            denominatorUnit = null,
        )

    override fun getFields(maybeSnapshot: Maybe<BomNodeSnapshot>) =
        listOfNotNull(
            setFieldImportType(),
            setFieldCalculationType(CalculationType.ACCOUNT_SPECIFIC_IMPORT, forSub = false),
        ).toMono()

    override fun getUpdatedFields(): Mono<CalculationUpdatePayloadDto> = Mono.empty()
}

class CreateSubNoCostModuleExcelImportProcessor(
    accessCheck: AccessCheck,
    projectId: String,
    payload: CalculationUpdatePayloadDto,
    action: CalculationUpdateAction = CalculationUpdateAction.REDIRECT_BOM_IMPORTER,
    services: ModalProcessorServices,
    isCo2ExtensionEnabled: Boolean,
) : SubModalProcessor(accessCheck, projectId, payload, action, services, isCo2ExtensionEnabled) {
    override fun process(): Mono<CalculationUpdateResponseDto> =
        getParentSnapshot().map { parentSnapshot ->
            val stepId =
                getFieldStepId(
                    parentSnapshot = parentSnapshot,
                    bomEntryFields = bomEntryFields,
                    payload = payload,
                )
            getResponse(
                updatedFields =
                    listOf(
                        stepId,
                        setFieldCalculationType(payload.selectedType, forSub = true),
                    ),
                updatedBomEntryFields = listOf(stepId),
                entity = parentSnapshot.manufacturing,
                parentEntity = parentSnapshot.manufacturing,
            )
        }

    override fun getUpdatedFields(): Mono<CalculationUpdatePayloadDto> =
        getParentSnapshot().map { parentSnapshot ->
            val stepId =
                getFieldStepId(
                    parentSnapshot = parentSnapshot,
                    bomEntryFields = bomEntryFields,
                    payload = payload,
                )
            updatePayload(
                updatedFields = listOf(stepId),
                updatedBomEntryFields = listOf(stepId),
                entity = parentSnapshot.manufacturing,
                parentEntity = parentSnapshot.manufacturing,
            )
        }
}

private const val FOR_INTERPOLATION = "for interpolation"

private fun getEntityNeededForInterpolation(selectedType: CalculationType): ManufacturingEntity =
    when (selectedType) {
        CalculationType.ROUGH_CALCULATION -> RoughManufacturing(FOR_INTERPOLATION)
        CalculationType.MANUAL_CALCULATION,
        CalculationType.EXCEL_FILE_IMPORT,
        CalculationType.ACCOUNT_SPECIFIC_IMPORT,
        -> ManualManufacturing(FOR_INTERPOLATION)

        CalculationType.DETAILED_CALCULATION,
        CalculationType.TSET_FILE_IMPORT,
        -> Manufacturing(FOR_INTERPOLATION)
    }

class CreateRootNoCostModuleImportProcessor(
    accessCheck: AccessCheck,
    projectId: String,
    payload: CalculationUpdatePayloadDto,
    services: ModalProcessorServices,
    isCo2ExtensionEnabled: Boolean,
) : RootModalProcessor(
        accessCheck,
        projectId,
        payload,
        CalculationUpdateAction.IMPORT_AND_CLOSE,
        services,
        isCo2ExtensionEnabled,
    ) {
    override fun getFields(maybeSnapshot: Maybe<BomNodeSnapshot>) =
        listOfNotNull(
            getFieldCalculationTitle(maybeSnapshot, calculationFields),
            setFieldCalculationType(CalculationType.TSET_FILE_IMPORT, forSub = false),
        ).toMono()

    override fun getUpdatedFields(): Mono<CalculationUpdatePayloadDto> = Mono.empty()
}

class CreateRootWithManualCostModuleProcessor(
    accessCheck: AccessCheck,
    projectId: String,
    payload: CalculationUpdatePayloadDto,
    services: ModalProcessorServices,
    isCo2ExtensionEnabled: Boolean,
) : CreateRootWithCostModuleProcessor(
        accessCheck,
        projectId,
        payload,
        CalculationUpdateAction.SAVE_AND_CLOSE,
        services,
        isCo2ExtensionEnabled,
    )

class CreateRootNoCostModuleProcessor(
    accessCheck: AccessCheck,
    projectId: String,
    payload: CalculationUpdatePayloadDto,
    services: ModalProcessorServices,
    isCo2ExtensionEnabled: Boolean,
) : RootModalProcessor(
        accessCheck,
        projectId,
        payload,
        CalculationUpdateAction.SAVE_AND_CLOSE,
        services,
        isCo2ExtensionEnabled,
    ) {
    override fun getFields(maybeSnapshot: Maybe<BomNodeSnapshot>) =
        services.configurationService
            .getDefaultConfiguration(accessCheck, ConfigType.DefaultValues)
            .map { getFieldsDimensionEtc(maybeSnapshot, it) }
            .flatMap { etc ->
                Mono
                    .zip(
                        getBaseFields(maybeSnapshot, etc),
                        getDefaultPurchasedCustomProcurementTypeField(maybeSnapshot, calculationFields, true),
                    ).map { (a, c) ->
                        a + setFieldCalculationType(payload.selectedType, forSub = false) + c +
                            listOfNotNull(
                                getFieldCostPerPart(maybeSnapshot, calculationFields, etc.toDynamicDenominatorUnit()),
                                getFieldCO2PerPart(maybeSnapshot, calculationFields, etc.toDynamicDenominatorUnit()),
                            )
                    }
            }

    override fun getUpdatedFields(): Mono<CalculationUpdatePayloadDto> = Mono.empty()
}

abstract class SubModalProcessor(
    accessCheck: AccessCheck,
    projectId: String,
    payload: CalculationUpdatePayloadDto,
    action: CalculationUpdateAction,
    services: ModalProcessorServices,
    isCo2ExtensionEnabled: Boolean,
) : ModalProcessor(accessCheck, projectId, payload, action, services, isCo2ExtensionEnabled) {
    val bomEntryFields =
        payload.data.parentBomData
            ?.fields
            ?.associateBy { it.name } ?: emptyMap()

    protected fun getManufacturing(snapshotInContext: BomNodeSnapshot?): ManufacturingEntity {
        val manufacturing = snapshotInContext?.manufacturing
        return requireNotNull(manufacturing) { "Manufacturing should be present for snapshot in context" }
    }

    protected fun getParentSnapshot(): Mono<BomNodeSnapshot> =
        // FIXME
        services.bomNodeService.getNodeWithPartAndBranch(
            accessCheck,
            BomNodeId(payload.data.parentBomData?.bomNodeId),
            createBranchId(payload.data.parentBomData?.branchId),
        )

    protected fun getSubBaseFields(
        parentSnapshot: BomNodeSnapshot,
        targetSnapshot: Maybe<BomNodeSnapshot>,
        calType: CalculationType?,
        quantity: FieldParameter,
        etc: CalculationCreationUtils.FieldsDimensionsEtc,
    ): Mono<List<FieldParameter>> {
        // If target snapshot exists -> edit, otherwise new -> default from parent
        val snapshot = targetSnapshot.orElse { parentSnapshot }

        val stepId =
            getFieldStepId(
                parentSnapshot = parentSnapshot,
                bomEntryFields = bomEntryFields,
                payload = payload,
            )

        val partFields =
            getPartFields(
                parentSnapshot = parentSnapshot,
                projectId = projectId,
                stepId = stepId,
                targetSnapshot = targetSnapshot,
            )

        val others =
            getDefaultPurchasedCustomProcurementTypeField(Maybe(snapshot), calculationFields, calType == CalculationType.ROUGH_CALCULATION)
                .map { procType ->
                    listOf(
                        setFieldCalculationType(payload.selectedType, forSub = true),
                        CalculationCreationUtils.getBaseCurrency(
                            Maybe(snapshot),
                            calculationFields,
                            services.entityManager,
                        ),
                        CalculationCreationUtils.getFieldLifetime(
                            Maybe(snapshot),
                            calculationFields,
                            services.entityManager,
                        ),
                        etc.dimensionField,
                        etc.quantityUnitField,
                        etc.costUnitField,
                    ) +
                        CalculationCreationUtils.calculateVolumes(
                            quantityValue = quantity.value?.toString()?.toBigDecimal()!!,
                            stepId = stepId.value?.toString()?.toObjectId(),
                            parentSnapshot = parentSnapshot,
                            targetSnapshot = targetSnapshot,
                            metaInfo =
                                mapOf(
                                    "bomEntry" to true,
                                    "frontendOnly" to true,
                                    "section" to "economic",
                                ),
                            fieldDimension = etc.dimension,
                            quantityUnit = etc.quantityUnit,
                            services.entityManager,
                        ) +
                        listOf(
                            getFieldLocation(Maybe(snapshot), calculationFields),
                            procType,
                            getFieldCalculationTitle(Maybe(snapshot), calculationFields),
                        )
                }
        return Mono.zip(others, getFieldCalculationQuality(Maybe(snapshot), calculationFields)) { o, q ->
            partFields + o + q +
                CalculationCreationUtils.getFieldCalculationDate(Maybe(snapshot), calculationFields, services.entityManager)
        }
    }

    protected fun updatePayload(
        updatedFields: List<FieldParameter?>,
        updatedBomEntryFields: List<FieldParameter?>,
        entity: ManufacturingEntity?,
        parentEntity: ManufacturingEntity?,
    ) = payload.copy(
        data =
            payload.data.copy(
                fields = postProcessFields(updatedFields, InterpolationData(entity = entity)),
                parentBomData =
                    payload.data.parentBomData?.copy(
                        fields =
                            postProcessFields(
                                updatedBomEntryFields,
                                InterpolationData(entity = parentEntity),
                            ),
                    ),
            ),
    )

    protected fun getResponse(
        updatedFields: List<FieldParameter?>,
        updatedBomEntryFields: List<FieldParameter?>,
        entity: ManufacturingEntity?,
        parentEntity: ManufacturingEntity?,
    ) = toResponse(updatePayload(updatedFields, updatedBomEntryFields, entity, parentEntity))

    private fun getPartFields(
        parentSnapshot: BomNodeSnapshot,
        projectId: String,
        stepId: FieldParameter,
        targetSnapshot: Maybe<BomNodeSnapshot>,
    ): List<FieldParameter> {
        val parentStep = getParentStepFromStepId(stepId.value?.toString(), parentSnapshot)

        return if (parentStep is ExternalManufacturingStep) {
            if (parentSnapshot.getBaseManufacturing() != null) {
                getIdentifyingBaseFields(Maybe(parentSnapshot))
            } else {
                listOf(
                    FieldParameter(
                        name = "partName",
                        type = "Text",
                        value = parentSnapshot.partName,
                        metaInfo =
                            mapOf(
                                "partSelect" to true,
                                "wizardReadOnly" to true,
                                "section" to "baseInformation",
                                "path" to "/api/projects/$projectId/parts",
                            ),
                        denominatorUnit = null,
                    ),
                )
            }
        } else {
            getIdentifyingBaseFields(targetSnapshot)
        }
    }

    protected fun getStepAndQuantity(
        parentSnapshot: BomNodeSnapshot,
        fieldsDimensionEtc: CalculationCreationUtils.FieldsDimensionsEtc,
    ): Pair<FieldParameter, FieldParameter> {
        val stepId =
            getFieldStepId(
                parentSnapshot = parentSnapshot,
                bomEntryFields = bomEntryFields,
                payload = payload,
            )

        val quantity =
            getFieldQuantity(
                fieldDimension = fieldsDimensionEtc.dimension,
                quantityUnit = fieldsDimensionEtc.quantityUnit,
                parentSnapshot = parentSnapshot,
                bomEntryFields = bomEntryFields,
                mode = payload.input.mode,
                targetBomNodeId = payload.input.context.bomNodeId,
            )
        return Pair(stepId, quantity)
    }

    protected fun getSubCalcInfo() =
        Mono
            .zip(
                getParentSnapshot(),
                getTargetSnapshot(),
                services.configurationService.getDefaultConfiguration(accessCheck, ConfigType.DefaultValues),
            ).map { (parent, target, defaults) ->
                val etc = getFieldsDimensionEtc(target, defaults)
                Triple(parent, target, etc)
            }
}

open class CreateSubWithCostModuleProcessor(
    accessCheck: AccessCheck,
    projectId: String,
    payload: CalculationUpdatePayloadDto,
    action: CalculationUpdateAction,
    services: ModalProcessorServices,
    isCo2ExtensionEnabled: Boolean,
) : SubModalProcessor(accessCheck, projectId, payload, action, services, isCo2ExtensionEnabled) {
    override fun process(): Mono<CalculationUpdateResponseDto> =
        getSubCalcInfo().flatMap { (parentSnapshot, targetSnapshot, fieldsDimensionEtc) ->
            val (stepId, quantity) = getStepAndQuantity(parentSnapshot, fieldsDimensionEtc)
            getManufacturing(targetSnapshot.value ?: parentSnapshot).let { manufacturing ->
                getSubBaseFields(parentSnapshot, targetSnapshot, null, quantity, fieldsDimensionEtc).map { fields ->
                    getResponse(
                        updatedFields = fields,
                        updatedBomEntryFields = listOf(stepId, quantity),
                        entity = manufacturing,
                        parentEntity = parentSnapshot.manufacturing,
                    )
                }
            }
        }

    override fun getUpdatedFields(): Mono<CalculationUpdatePayloadDto> =
        getSubCalcInfo().flatMap { (parentSnapshot, targetSnapshot, fieldsDimensionEtc) ->
            val (stepId, quantity) = getStepAndQuantity(parentSnapshot, fieldsDimensionEtc)
            getManufacturing(targetSnapshot.value ?: parentSnapshot).let { manufacturing ->
                getSubBaseFields(parentSnapshot, targetSnapshot, null, quantity, fieldsDimensionEtc).map { fields ->
                    updatePayload(
                        updatedFields = fields,
                        updatedBomEntryFields = listOf(stepId, quantity),
                        entity = manufacturing,
                        parentEntity = parentSnapshot.manufacturing,
                    )
                }
            }
        }
}

class CreateSubWithManualCostModuleProcessor(
    accessCheck: AccessCheck,
    projectId: String,
    payload: CalculationUpdatePayloadDto,
    services: ModalProcessorServices,
    isCo2ExtensionEnabled: Boolean,
) : CreateSubWithCostModuleProcessor(
        accessCheck,
        projectId,
        payload,
        CalculationUpdateAction.SAVE_AND_CLOSE,
        services,
        isCo2ExtensionEnabled,
    )

class CreateSubImportModuleProcessor(
    accessCheck: AccessCheck,
    projectId: String,
    payload: CalculationUpdatePayloadDto,
    services: ModalProcessorServices,
    isCo2ExtensionEnabled: Boolean,
) : SubModalProcessor(
        accessCheck,
        projectId,
        payload,
        CalculationUpdateAction.IMPORT_AND_CLOSE,
        services,
        isCo2ExtensionEnabled,
    ) {
    override fun process(): Mono<CalculationUpdateResponseDto> =
        getSubCalcInfo().map { (parentSnapshot, targetSnapshot, fieldsDimensionEtc) ->
            val (stepId, quantity) = getStepAndQuantity(parentSnapshot, fieldsDimensionEtc)
            getManufacturing(targetSnapshot.value ?: parentSnapshot).let { manufacturing ->
                getResponse(
                    updatedFields = listOf(getFieldCalculationTitle(targetSnapshot, calculationFields)),
                    updatedBomEntryFields =
                        listOf(
                            stepId,
                            quantity,
                            setFieldCalculationType(CalculationType.TSET_FILE_IMPORT, forSub = true),
                        ),
                    entity = manufacturing,
                    parentEntity = parentSnapshot.manufacturing,
                )
            }
        }

    override fun getUpdatedFields(): Mono<CalculationUpdatePayloadDto> = Mono.empty()
}

class CreateSubNoCostModuleProcessor(
    accessCheck: AccessCheck,
    projectId: String,
    payload: CalculationUpdatePayloadDto,
    services: ModalProcessorServices,
    isCo2ExtensionEnabled: Boolean,
) : SubModalProcessor(
        accessCheck,
        projectId,
        payload,
        CalculationUpdateAction.SAVE_AND_CLOSE,
        services,
        isCo2ExtensionEnabled,
    ) {
    override fun process(): Mono<CalculationUpdateResponseDto> =
        getSubCalcInfo().flatMap { (parentSnapshot, targetSnapshot, etc) ->
            val (stepId, quantity) = getStepAndQuantity(parentSnapshot, etc)
            getManufacturing(targetSnapshot.value ?: parentSnapshot).let { manufacturing ->
                getSubBaseFields(parentSnapshot, targetSnapshot, CalculationType.ROUGH_CALCULATION, quantity, etc)
                    .map { fields ->
                        getResponse(
                            updatedFields =
                                fields +
                                    getFieldCostPerPart(targetSnapshot, calculationFields, etc.toDynamicDenominatorUnit()) +
                                    getFieldCO2PerPart(targetSnapshot, calculationFields, etc.toDynamicDenominatorUnit()),
                            updatedBomEntryFields = listOf(stepId, quantity),
                            entity = manufacturing,
                            parentEntity = parentSnapshot.manufacturing,
                        )
                    }
            }
        }

    override fun getUpdatedFields(): Mono<CalculationUpdatePayloadDto> = Mono.empty()
}
