package com.tset.core.module.bom.calculation

import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.exception.InternalServerException
import com.nu.bom.core.exception.readable.ErrorCode
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.model.BomEntryCreationData
import com.nu.bom.core.model.BomEntryUpdateData
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.createBranchId
import com.nu.bom.core.service.BomEntryFieldsSanitizer
import com.nu.bom.core.service.EcoFieldsSanitizer
import com.nu.bom.core.utils.getOptional
import com.nu.bom.core.utils.getRequired
import com.tset.core.api.calculation.dto.CalculationCreationModalMode.CALCULATION_MODE_CHANGE_TYPE
import com.tset.core.api.calculation.dto.CalculationCreationModalMode.CALCULATION_MODE_EDIT
import com.tset.core.api.calculation.dto.CalculationCreationModalMode.CALCULATION_MODE_NEW
import com.tset.core.api.calculation.dto.CalculationPosition.ROOT
import com.tset.core.api.calculation.dto.CalculationPosition.SUB
import com.tset.core.api.calculation.dto.CalculationUpdatePayloadDto
import com.tset.core.service.domain.Currency
import com.tset.core.service.domain.calculation.CalculationType
import org.bson.types.ObjectId
import kotlin.reflect.KClass

sealed interface CalculationUpdateInput {
    fun getPath(): List<String>? = null
}

sealed class CalculationUpdateInputBase(
    open val projectId: ProjectId,
    open val title: String,
    open val fields: List<FieldParameter>,
    open val displayCurrency: Currency,
    open val wizardManufacturing: ManufacturingEntity? = null,
) : CalculationUpdateInput {
    companion object {
        fun fromCalculationUpdateDto(
            projectId: String,
            calculationUpdateDto: CalculationUpdatePayloadDto,
            manufacturingClass: KClass<out BaseManufacturing>?,
        ): CalculationUpdateInputBase =
            when (
                Pair(
                    calculationUpdateDto.input.position,
                    calculationUpdateDto.input.mode,
                )
            ) {
                Pair(ROOT, CALCULATION_MODE_NEW) ->
                    CalculationCreateRootInput.fromCalculationUpdateDto(
                        projectId,
                        calculationUpdateDto,
                        manufacturingClass,
                    )

                Pair(SUB, CALCULATION_MODE_NEW) ->
                    CalculationCreateSubInput.fromCalculationUpdateDto(projectId, calculationUpdateDto)

                Pair(ROOT, CALCULATION_MODE_EDIT) ->
                    CalculationEditRootInput.fromCalculationUpdateDto(projectId, calculationUpdateDto)

                Pair(SUB, CALCULATION_MODE_EDIT) ->
                    CalculationEditSubInput.fromCalculationUpdateDto(projectId, calculationUpdateDto)

                Pair(ROOT, CALCULATION_MODE_CHANGE_TYPE) ->
                    CalculationChangeTypeRootInput.fromCalculationUpdateDto(projectId, calculationUpdateDto)

                Pair(SUB, CALCULATION_MODE_CHANGE_TYPE) ->
                    CalculationChangeTypeSubInput.fromCalculationUpdateDto(projectId, calculationUpdateDto)

                else -> throw InternalServerException(ErrorCode.NON_EXHAUSTIVE_MATCH_ERROR)
            }
    }
}

interface CalculationCreationInput {
    val clazz: KClass<out BaseManufacturing>
}

sealed interface CalculationUpdatedNodeInput {
    val bomNodeId: BomNodeId
    val branchId: BranchId? // shared by BomEntry branch
    val selectedType: CalculationType
    val title: String
    val fields: List<FieldParameter>
    val displayCurrency: Currency
}

data class CalculationCreateRootInput(
    override val projectId: ProjectId,
    override val title: String,
    override val fields: List<FieldParameter>,
    override val clazz: KClass<out BaseManufacturing>,
    override val displayCurrency: Currency,
    override val wizardManufacturing: ManufacturingEntity? = null,
    val partDesignation: String? = null,
    val partNumber: String? = null,
) : CalculationUpdateInputBase(projectId, title, fields, displayCurrency, wizardManufacturing),
    CalculationCreationInput {
    companion object {
        private fun List<FieldParameter>.removeByType(type: String) {
            val k = this.find { c -> c.type == type }
            val s = this as MutableList
            if (k != null) {
                s.removeAt(this.indexOf(k))
            }
        }

        fun fromCalculationUpdateDto(
            projectId: String,
            calculationUpdateDto: CalculationUpdatePayloadDto,
            manufacturingClass: KClass<out BaseManufacturing>?,
        ): CalculationCreateRootInput =
            with(calculationUpdateDto) {
                calculationUpdateDto.data.fields.removeByType("CalculationType") // TODO: fix that mutability stuff
                CalculationCreateRootInput(
                    projectId = ProjectId(projectId),
                    title = getCalculationTitle(data.fields),
                    fields =
                        if (selectedType == CalculationType.DETAILED_CALCULATION) {
                            data.fields
                        } else {
                            EcoFieldsSanitizer.sanitize(data.fields)
                        },
                    clazz = manufacturingClass ?: selectedType.entityClass,
                    displayCurrency = input.currency,
                )
            }
    }
}

data class CalculationEditRootInput(
    override val projectId: ProjectId,
    override val title: String,
    override val fields: List<FieldParameter>,
    override val bomNodeId: BomNodeId,
    override val branchId: BranchId?,
    override val selectedType: CalculationType,
    override val displayCurrency: Currency,
    override val wizardManufacturing: ManufacturingEntity? = null,
) : CalculationUpdateInputBase(projectId, title, fields, displayCurrency, wizardManufacturing),
    CalculationUpdatedNodeInput {
    companion object {
        fun fromCalculationUpdateDto(
            projectId: String,
            calculationUpdateDto: CalculationUpdatePayloadDto,
        ): CalculationEditRootInput =
            with(calculationUpdateDto) {
                CalculationEditRootInput(
                    projectId = ProjectId(projectId),
                    title = getCalculationTitle(data.fields),
                    fields = EcoFieldsSanitizer.sanitize(data.fields),
                    bomNodeId = BomNodeId(input.context.bomNodeId!!),
                    branchId = createBranchId(input.context.branchId),
                    selectedType = selectedType,
                    displayCurrency = calculationUpdateDto.input.currency,
                )
            }
    }
}

data class CalculationChangeTypeRootInput(
    override val projectId: ProjectId,
    override val title: String,
    override val fields: List<FieldParameter>,
    override val bomNodeId: BomNodeId,
    override val branchId: BranchId?,
    override val selectedType: CalculationType,
    override val clazz: KClass<out BaseManufacturing>,
    override val displayCurrency: Currency,
) : CalculationUpdateInputBase(projectId, title, fields, displayCurrency),
    CalculationUpdatedNodeInput,
    CalculationCreationInput {
    companion object {
        fun fromCalculationUpdateDto(
            projectId: String,
            calculationUpdateDto: CalculationUpdatePayloadDto,
        ): CalculationChangeTypeRootInput =
            with(calculationUpdateDto) {
                CalculationChangeTypeRootInput(
                    projectId = ProjectId(projectId),
                    title = getCalculationTitle(data.fields),
                    fields = EcoFieldsSanitizer.sanitize(data.fields),
                    bomNodeId = BomNodeId(input.context.bomNodeId!!),
                    branchId = createBranchId(input.context.branchId),
                    selectedType = selectedType,
                    clazz = selectedType.entityClass,
                    displayCurrency = calculationUpdateDto.input.currency,
                )
            }
    }
}

data class CalculationCreateSubInput(
    override val projectId: ProjectId,
    override val title: String,
    override val fields: List<FieldParameter>,
    override val clazz: KClass<out BaseManufacturing>,
    val parentBomData: BomEntryCreationData,
    val partName: String?,
    override val displayCurrency: Currency,
    override val wizardManufacturing: ManufacturingEntity? = null,
) : CalculationUpdateInputBase(projectId, title, fields, displayCurrency, wizardManufacturing),
    CalculationCreationInput {
    override fun getPath() = parentBomData.path

    companion object {
        fun fromCalculationUpdateDto(
            projectId: String,
            calculationUpdateDto: CalculationUpdatePayloadDto,
        ): CalculationCreateSubInput =
            with(calculationUpdateDto) {
                val parentBomData = requireNotNull(data.parentBomData)
                CalculationCreateSubInput(
                    projectId = ProjectId(projectId),
                    title = getCalculationTitle(data.fields),
                    partName = getPartName(data.fields),
                    // TODO kw include partNumber in sanitized fields?
                    // or include it as a separate param like partName?
                    fields = EcoFieldsSanitizer.sanitize(data.fields),
                    clazz = selectedType.entityClass,
                    parentBomData =
                        BomEntryCreationData(
                            bomNodeId = BomNodeId(parentBomData.bomNodeId),
                            branchId = createBranchId(parentBomData.branchId),
                            stepId = ObjectId(getStepId(parentBomData.fields)),
                            fields = BomEntryFieldsSanitizer.sanitize(parentBomData.fields),
                            path = parentBomData.path,
                        ),
                    displayCurrency = calculationUpdateDto.input.currency,
                )
            }
    }
}

data class CalculationEditSubInput(
    override val projectId: ProjectId,
    override val title: String,
    override val fields: List<FieldParameter>,
    override val bomNodeId: BomNodeId,
    override val branchId: BranchId?,
    override val selectedType: CalculationType,
    val parentBomData: BomEntryUpdateData,
    val partName: String?,
    override val displayCurrency: Currency,
    override val wizardManufacturing: ManufacturingEntity? = null,
) : CalculationUpdateInputBase(projectId, title, fields, displayCurrency, wizardManufacturing),
    CalculationUpdatedNodeInput {
    override fun getPath() = parentBomData.path

    companion object {
        fun fromCalculationUpdateDto(
            projectId: String,
            calculationUpdateDto: CalculationUpdatePayloadDto,
        ): CalculationEditSubInput =
            with(calculationUpdateDto) {
                val parentBomData = requireNotNull(data.parentBomData)
                CalculationEditSubInput(
                    projectId = ProjectId(projectId),
                    title = getCalculationTitle(data.fields),
                    partName = getPartName(data.fields),
                    fields = EcoFieldsSanitizer.sanitize(data.fields),
                    bomNodeId = BomNodeId(input.context.bomNodeId!!),
                    branchId = createBranchId(input.context.branchId),
                    parentBomData =
                        BomEntryUpdateData(
                            bomNodeId = BomNodeId(parentBomData.bomNodeId),
                            branchId = createBranchId(parentBomData.branchId),
                            fields = BomEntryFieldsSanitizer.sanitize(parentBomData.fields),
                            path = parentBomData.path,
                        ),
                    selectedType = selectedType,
                    displayCurrency = calculationUpdateDto.input.currency,
                )
            }
    }
}

data class CalculationChangeTypeSubInput(
    override val projectId: ProjectId,
    override val title: String,
    override val fields: List<FieldParameter>,
    override val bomNodeId: BomNodeId,
    override val branchId: BranchId?,
    override val selectedType: CalculationType,
    val parentBomData: BomEntryUpdateData,
    val partName: String?,
    override val clazz: KClass<out BaseManufacturing>,
    override val displayCurrency: Currency,
) : CalculationUpdateInputBase(projectId, title, fields, displayCurrency),
    CalculationUpdatedNodeInput,
    CalculationCreationInput {
    override fun getPath() = parentBomData.path

    companion object {
        fun fromCalculationUpdateDto(
            projectId: String,
            calculationUpdateDto: CalculationUpdatePayloadDto,
        ): CalculationChangeTypeSubInput =
            with(calculationUpdateDto) {
                val parentBomData = requireNotNull(data.parentBomData)
                CalculationChangeTypeSubInput(
                    projectId = ProjectId(projectId),
                    title = getCalculationTitle(data.fields),
                    partName = getPartName(data.fields),
                    fields = EcoFieldsSanitizer.sanitize(data.fields),
                    bomNodeId = BomNodeId(input.context.bomNodeId!!),
                    branchId = createBranchId(input.context.branchId),
                    parentBomData =
                        BomEntryUpdateData(
                            bomNodeId = BomNodeId(parentBomData.bomNodeId),
                            branchId = createBranchId(parentBomData.branchId),
                            fields = BomEntryFieldsSanitizer.sanitize(parentBomData.fields),
                            path = parentBomData.path,
                        ),
                    selectedType = selectedType,
                    clazz = selectedType.entityClass,
                    displayCurrency = calculationUpdateDto.input.currency,
                )
            }
    }
}

data class CalculationConvertToManualInput(
    val bomNodeId: BomNodeId,
    val branchId: BranchId?,
) : CalculationUpdateInput

private fun getCalculationTitle(fields: List<FieldParameter>): String = fields.getRequired("calculationTitle", Text::class).value as String

private fun getStepId(fields: List<FieldParameter>): String = fields.getRequired("stepId", Text::class).value as String

private fun getPartName(fields: List<FieldParameter>): String? =
    fields.getOptional(Manufacturing::partName.name, Text::class)?.value as String?
        ?: fields.getOptional(Manufacturing::partDesignation.name, Text::class)?.value as String?
