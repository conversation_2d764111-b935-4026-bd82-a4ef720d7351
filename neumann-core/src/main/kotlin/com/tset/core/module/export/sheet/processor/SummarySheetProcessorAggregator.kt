package com.tset.core.module.export.sheet.processor

import com.nu.bom.core.api.dtos.FieldConversionService
import com.nu.bom.core.manufacturing.enums.UnitOverrideContext
import com.nu.bom.core.manufacturing.enums.toConvertedValueForUnit
import com.tset.core.module.export.BomNodeEnvironment
import com.tset.core.module.export.dto.ExportTemplateWithConfig
import com.tset.core.module.export.sheet.AggregateValueWithDebug
import com.tset.core.module.export.sheet.AggregatedBomSourcer.computeAggregatePercent
import com.tset.core.module.export.sheet.AggregatedBomSourcer.computeAggregateValue
import com.tset.core.module.export.sheet.CbdSheetProcessor
import com.tset.core.module.export.sheet.getDenominatorString
import com.tset.core.service.domain.Currency
import org.apache.poi.ss.usermodel.DataFormatter
import org.apache.poi.xssf.usermodel.XSSFRow
import org.apache.poi.xssf.usermodel.XSSFSheet
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import reactor.core.publisher.Mono

class SummarySheetProcessorAggregator : CbdSheetProcessor() {
    companion object {
        val logger: Logger = LoggerFactory.getLogger(SummarySheetProcessorAggregator::class.java)
        private const val AGGREGATED_VALUE_PREFIX = "\$_"
        private const val PERCENTAGE_VALUE_PREFIX = "%_"
        val processorPrefixes: List<String> = listOf(AGGREGATED_VALUE_PREFIX, PERCENTAGE_VALUE_PREFIX)
    }

    override fun process(
        sheetName: String,
        exportTemplateWithConfig: ExportTemplateWithConfig,
        snapshotEnvironment: BomNodeEnvironment,
        fieldConversionService: FieldConversionService,
    ): Mono<ExportTemplateWithConfig> {
        logger.info("...export processing $sheetName sheet (aggregated fields)")
        val totalValue =
            computeAggregateValue(
                snapshotEnvironment.entityManager,
                snapshotEnvironment.snapshot,
                exportTemplateWithConfig.config.cbd.totalValue,
            ).sum
        logger.info("   -> total costs = $totalValue")

        val dataFormatter = DataFormatter()
        val sheet = exportTemplateWithConfig.template.getSheet(sheetName)
        val debugSheet = createDebugSheet(exportTemplateWithConfig, sheetName)
        for (row in sheet) {
            for (cell in row) {
                val cellText: String = dataFormatter.formatCellValue(cell)
                if (processorPrefixes.any { cellText.startsWith(it) }) {
                    val maybeAggregate = exportTemplateWithConfig.config.aggregateFieldMap[cellText]
                    if (maybeAggregate != null) {
                        val aggregateValueWithDebug =
                            computeAggregateValue(
                                snapshotEnvironment.entityManager,
                                snapshotEnvironment.snapshot,
                                maybeAggregate.first,
                            )
                        if (exportTemplateWithConfig.debugSheetsEnabled) {
                            logger.info("DEBUG sheets are enabled, creating debug sheet for $cellText")
                            addDebugInfo(
                                debugSheet!!,
                                cellText,
                                aggregateValueWithDebug,
                            )
                        }

                        val useDynamicDenominator = maybeAggregate.second
                        val value =
                            snapshotEnvironment.exchangeRateMap.ccyToCcy(
                                aggregateValueWithDebug.sum,
                                Currency.EUR,
                                snapshotEnvironment.getCurrency(),
                            )

                        updateNumberCellWithValue(
                            cell,
                            if (useDynamicDenominator) {
                                value.toConvertedValueForUnit(
                                    snapshotEnvironment.snapshot.manufacturing?.let {
                                        UnitOverrideContext.fromEntity(
                                            it,
                                        )
                                    },
                                )
                            } else {
                                value
                            },
                        )

                        val denominator =
                            if (useDynamicDenominator) {
                                snapshotEnvironment.snapshot.manufacturing
                                    ?.getFieldResult("costUnit")
                                    ?.res as? String
                            } else {
                                null
                            }

                        cell.cellStyle.dataFormat =
                            exportTemplateWithConfig.template.creationHelper
                                .createDataFormat()
                                .getFormat(
                                    snapshotEnvironment.getCostlikeFormat() + "\"${
                                        getDenominatorString(
                                            denominator,
                                            snapshotEnvironment.translationService,
                                        )
                                    }\"",
                                )
                    } else {
                        val maybeAggregatePercentage =
                            exportTemplateWithConfig.config.aggregatePercentFieldMap[cellText]
                        if (maybeAggregatePercentage != null) {
                            updateNumberCellWithValue(
                                cell,
                                computeAggregatePercent(
                                    snapshotEnvironment.entityManager,
                                    snapshotEnvironment.snapshot,
                                    maybeAggregatePercentage,
                                    totalValue,
                                ),
                            )
                        } else {
                            updateTextCellWithValue(cell, "")
                        }
                    }
                }
            }
        }

        return Mono.just(exportTemplateWithConfig)
    }

    private fun createDebugSheet(
        exportTemplateWithConfig: ExportTemplateWithConfig,
        sheetName: String,
    ): DebugSheetInfo? =
        if (exportTemplateWithConfig.debugSheetsEnabled) {
            val debugSheet = exportTemplateWithConfig.template.createSheet("$sheetName DEBUG")
            val firstRow = debugSheet.createRow(0)
            firstRow.createCell(0).setCellValue("placeHolder")
            firstRow.createCell(1).setCellValue("entityType")
            firstRow.createCell(2).setCellValue("entityId")
            firstRow.createCell(3).setCellValue("entityName")
            firstRow.createCell(4).setCellValue("selector")
            firstRow.createCell(5).setCellValue("selectName")
            firstRow.createCell(6).setCellValue("selected")
            firstRow.createCell(7).setCellValue("fieldName")
            firstRow.createCell(8).setCellValue("fieldValue")
            firstRow.createCell(9).setCellValue("quantity")
            firstRow.createCell(10).setCellValue("calculatedValue")
            DebugSheetInfo(debugSheet)
        } else {
            null
        }

    private fun addDebugInfo(
        debugSheetInfo: DebugSheetInfo,
        cellText: String,
        aggregateValueWithDebug: AggregateValueWithDebug,
    ) {
        aggregateValueWithDebug.debug.onEachIndexed { index, aggregateValueDebugInfo ->
            val row = debugSheetInfo.createRow(index + 1)
            row.createCell(0).setCellValue(cellText)
            row.createCell(1).setCellValue(aggregateValueDebugInfo.entityType)
            row.createCell(2).setCellValue(aggregateValueDebugInfo.entityId)
            row.createCell(3).setCellValue(aggregateValueDebugInfo.entityName)
            row.createCell(4).setCellValue(aggregateValueDebugInfo.selector)
            row.createCell(5).setCellValue(aggregateValueDebugInfo.selectName)
            row.createCell(6).setCellValue(aggregateValueDebugInfo.selected)
            row.createCell(7).setCellValue(aggregateValueDebugInfo.fieldName)
            row.createCell(8).setCellValue(aggregateValueDebugInfo.fieldValue.toDouble())
            row.createCell(9).setCellValue(aggregateValueDebugInfo.quantity.toDouble())
            row.createCell(10).setCellValue(aggregateValueDebugInfo.calculatedValue.toDouble())
        }
        debugSheetInfo.increaseBaseIndex(aggregateValueWithDebug.debug.size)
    }

    private data class DebugSheetInfo(
        private val sheet: XSSFSheet,
        private var baseIndex: Int = 0,
    ) {
        fun createRow(index: Int): XSSFRow = sheet.createRow(baseIndex + index)

        fun increaseBaseIndex(valueToIncrease: Int) {
            baseIndex += valueToIncrease
        }
    }
}
