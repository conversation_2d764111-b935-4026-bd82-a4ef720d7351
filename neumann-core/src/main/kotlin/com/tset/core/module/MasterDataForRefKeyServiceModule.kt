package com.tset.core.module

import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.tset.core.service.domain.Entity
import com.tset.core.service.domain.MasterConsumableEntity
import com.tset.core.service.domain.MasterLocationEntity
import com.tset.core.service.domain.MasterMachineEntity
import com.tset.core.service.domain.MasterMaterialEntity
import com.tset.core.service.domain.TsetMasterConsumableEntity
import com.tset.core.service.domain.TsetMasterLocationEntity
import com.tset.core.service.domain.TsetMasterMachineEntity
import com.tset.core.service.domain.TsetMasterMaterialEntity
import com.tset.core.service.refkey.MasterDataForRefKeyService
import org.springframework.stereotype.Service

val MATERIALS =
    arrayOf(
        MasterDataType.RAW_MATERIAL_BAR,
        MasterDataType.RAW_MATERIAL_CASTING_ALLOY,
        MasterDataType.RAW_MATERIAL_LAMELLA,
        MasterDataType.RAW_MATERIAL_PCB,
        MasterDataType.RAW_MATERIAL_MANUAL,
        MasterDataType.RAW_MATERIAL_PIPE,
        MasterDataType.RAW_MATERIAL_PLASTIC_GRANULATE,
        MasterDataType.RAW_MATERIAL_POWDER,
        MasterDataType.RAW_MATERIAL_RUBBER,
        MasterDataType.RAW_MATERIAL_SAND,
        MasterDataType.RAW_MATERIAL_SHEET,
        MasterDataType.RAW_MATERIAL_WAX,
        MasterDataType.RAW_MATERIAL_WIRE_ROD,
        MasterDataType.RAW_MATERIAL_INGOT,
        MasterDataType.RAW_MATERIAL_RARE_EARTH,
        MasterDataType.RAW_MATERIAL_INK,
        MasterDataType.RAW_MATERIAL_PAINT,
        MasterDataType.RAW_MATERIAL_PAPER_COIL,
        MasterDataType.RAW_MATERIAL_PAPER_SHEET,
        MasterDataType.RAW_MATERIAL_METALLIC_COATING,
        MasterDataType.RAW_MATERIAL_COATING_PCBA,
        MasterDataType.RAW_MATERIAL_COIL,
        MasterDataType.RAW_MATERIAL_VARNISH,
        MasterDataType.MATERIAL,
    )

@Service
class MasterDataForRefKeyServiceModule : MasterDataForRefKeyService {
    override fun getRefKeyEntity(masterDataType: MasterDataType): Entity =
        when (masterDataType) {
            in MATERIALS -> MasterMaterialEntity
            MasterDataType.MACHINE -> MasterMachineEntity
            MasterDataType.LOCATION -> MasterLocationEntity
            MasterDataType.CONSUMABLE -> MasterConsumableEntity
            else -> throw IllegalArgumentException("not supported $masterDataType")
        }

    override fun getTsetMasterRefKeyEntity(masterDataType: MasterDataType): Entity =
        when (masterDataType) {
            in MATERIALS -> TsetMasterMaterialEntity
            MasterDataType.MACHINE -> TsetMasterMachineEntity
            MasterDataType.LOCATION -> TsetMasterLocationEntity
            MasterDataType.CONSUMABLE -> TsetMasterConsumableEntity
            else -> throw IllegalArgumentException("not supported $masterDataType")
        }
}
