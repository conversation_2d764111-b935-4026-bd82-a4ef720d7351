package com.tset.core.module.bom

import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.toMongoFormatStr
import com.nu.bom.core.model.toObjectId
import com.nu.bom.core.user.AccessCheck
import com.nu.bomrads.enumeration.BomNodeStatus
import com.nu.bomrads.id.BranchId
import com.nu.bomrads.id.FolderId
import com.nu.bomrads.id.WorkspaceId
import com.nu.http.EnvironmentNameSupplier
import com.tset.core.service.event.BomNodeCreatedEvent
import com.tset.core.service.event.BomNodeDeletedEvent
import com.tset.core.service.event.BomNodeSnapshotAccessedEvent
import com.tset.core.service.event.BomNodeSnapshotUpdatedEvent
import com.tset.core.service.event.BomNodeStatusUpdatedEvent
import com.tset.core.service.event.BomNodeUpdatedEvent
import com.tset.core.service.event.EventSourcingService
import com.tset.core.service.event.FolderDeletedEvent
import com.tset.core.service.event.FolderUpdatedEvent
import com.tset.core.service.event.GlobalBranchEvent
import com.tset.core.service.event.PartUpdatedEvent
import com.tset.core.service.event.ProjectAccessedEvent
import com.tset.core.service.event.ProjectCreatedEvent
import com.tset.core.service.event.ProjectDeletedEvent
import com.tset.core.service.event.ProjectUpdatedEvent
import com.tset.core.service.event.RootBomNodeDeletedEvent
import com.tset.core.service.event.RootBomNodeUpdatedEvent
import com.tset.core.service.event.WorkspaceDeletedEvent
import com.tset.core.service.event.WorkspaceUpdatedEvent
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import java.time.Instant
import java.util.Date

@Service
class EventSourcingModule(
    private val eventSourcingService: EventSourcingService,
    private val environmentNameSupplier: EnvironmentNameSupplier,
) {
    fun publishWorkspaceUpdated(
        accountId: String,
        accountName: String,
        workspaceId: WorkspaceId,
    ): Mono<Void> =
        eventSourcingService.publishEvent(
            WorkspaceUpdatedEvent(
                accountId = accountId,
                accountName = accountName,
                environment = environmentNameSupplier.getEnv(),
                workspaceId = workspaceId.id().toString(),
            ),
        )

    fun publishWorkspaceDeleted(
        accountId: String,
        accountName: String,
        workspaceId: WorkspaceId,
    ): Mono<Void> =
        eventSourcingService.publishEvent(
            WorkspaceDeletedEvent(
                accountId = accountId,
                accountName = accountName,
                environment = environmentNameSupplier.getEnv(),
                workspaceId = workspaceId.id().toObjectId().toHexString(),
            ),
        )

    fun publishFolderUpdated(
        accountId: String,
        accountName: String,
        folderId: FolderId,
    ): Mono<Void> =
        eventSourcingService.publishEvent(
            FolderUpdatedEvent(
                accountId = accountId,
                accountName = accountName,
                environment = environmentNameSupplier.getEnv(),
                folderId = folderId.id().toString(),
            ),
        )

    fun publishFolderDeleted(
        accountId: String,
        accountName: String,
        folderId: FolderId,
    ): Mono<Void> =
        eventSourcingService.publishEvent(
            FolderDeletedEvent(
                accountId = accountId,
                accountName = accountName,
                environment = environmentNameSupplier.getEnv(),
                folderId = folderId.id().toObjectId().toHexString(),
            ),
        )

    fun publishProjectCreated(
        accountId: String,
        accountName: String,
        projectId: String,
    ): Mono<Void> =
        eventSourcingService.publishEvent(
            ProjectCreatedEvent(
                accountId,
                accountName,
                environment = environmentNameSupplier.getEnv(),
                projectId,
            ),
        )

    fun publishProjectAccessed(
        accountId: String,
        accountName: String,
        projectId: String,
    ): Mono<Void> =
        eventSourcingService.publishEvent(
            ProjectAccessedEvent(
                accountId,
                accountName,
                environment = environmentNameSupplier.getEnv(),
                projectId,
            ),
        )

    fun publishProjectUpdated(
        accountId: String,
        accountName: String,
        projectId: String,
    ): Mono<Void> =
        eventSourcingService.publishEvent(
            ProjectUpdatedEvent(
                accountId,
                accountName,
                environment = environmentNameSupplier.getEnv(),
                projectId,
            ),
        )

    fun publishProjectDeleted(
        accountId: String,
        accountName: String,
        projectId: ProjectId,
    ): Mono<Void> =
        eventSourcingService.publishEvent(
            ProjectDeletedEvent(
                accountId = accountId,
                accountName = accountName,
                environment = environmentNameSupplier.getEnv(),
                projectId = projectId.toHexString(),
            ),
        )

    fun publishBomNodeSnapshotAccessed(
        accountId: String,
        accountName: String,
        userId: String,
        bomNodeSnapshot: BomNodeSnapshot,
    ): Mono<Void> {
        return eventSourcingService.publishEvent(
            BomNodeSnapshotAccessedEvent(
                accountId = accountId,
                accountName = accountName,
                environment = environmentNameSupplier.getEnv(),
                projectId = bomNodeSnapshot.projectId().toHexString(),
                bomNodeId = bomNodeSnapshot.bomNodeId().toHexString(),
                branchId = bomNodeSnapshot.branchIdStr(),
                userId = userId,
                accessedDate = Date.from(Instant.now()),
            ),
        )
    }

    fun publishGlobalBranchUpdated(
        accessCheck: AccessCheck,
        projectId: com.nu.bomrads.id.ProjectId,
        branchId: BranchId,
        deleted: Boolean,
    ): Mono<Void> {
        val event =
            GlobalBranchEvent(
                accountId = accessCheck.accountId,
                accountName = accessCheck.accountName,
                environment = environmentNameSupplier.getEnv(),
                projectId = projectId.toMongoFormatStr(),
                branchId = branchId.toMongoFormatStr(),
                deleted = deleted,
            )
        logger.info("publishGlobalBranchUpdated $event")
        return eventSourcingService.publishEvent(event)
    }

    fun publishGlobalRootBomNodeUpdated(
        accessCheck: AccessCheck,
        projectId: com.nu.bomrads.id.ProjectId,
        bomNodeId: BomNodeId,
    ): Mono<Void> {
        val event =
            RootBomNodeUpdatedEvent(
                accountId = accessCheck.accountId,
                accountName = accessCheck.accountName,
                environment = environmentNameSupplier.getEnv(),
                projectId = projectId.toMongoFormatStr(),
                bomNodeId = bomNodeId.toHexString(),
            )
        logger.info("publishGlobalRootBomNodeUpdated $event")
        return eventSourcingService.publishEvent(event)
    }

    fun publishGlobalRootBomNodeDeleted(
        accessCheck: AccessCheck,
        projectId: com.nu.bomrads.id.ProjectId,
        bomNodeId: BomNodeId,
    ): Mono<Void> {
        val event =
            RootBomNodeDeletedEvent(
                accountId = accessCheck.accountId,
                accountName = accessCheck.accountName,
                environment = environmentNameSupplier.getEnv(),
                projectId = projectId.toMongoFormatStr(),
                bomNodeId = bomNodeId.toHexString(),
            )
        logger.info("publishGlobalRootBomNodeDeleted $event")
        return eventSourcingService.publishEvent(event)
    }

    fun publishBomNodeSnapshotsUpdated(snapshotInfos: List<SnapshotUpdatedInfo>): Mono<Void> {
        return eventSourcingService.publishEvents(
            snapshotInfos.map { (accountId, accountName, projectId, bomNodeId, branchId, userId, accessedDate) ->
                BomNodeSnapshotUpdatedEvent(
                    accountId = accountId,
                    accountName = accountName,
                    environment = environmentNameSupplier.getEnv(),
                    projectId = projectId,
                    bomNodeId = bomNodeId,
                    branchId = branchId,
                    userId = userId,
                    accessedDate = accessedDate,
                )
            },
        )
    }

    fun publishBomNodeCreated(
        accountId: String,
        accountName: String,
        projectId: ProjectId,
        rootBomNodeId: BomNodeId,
    ): Mono<Void> {
        return eventSourcingService.publishEvent(
            BomNodeCreatedEvent(
                accountId = accountId,
                accountName = accountName,
                environment = environmentNameSupplier.getEnv(),
                projectId = projectId.toHexString(),
                bomNodeId = rootBomNodeId.toHexString(),
            ),
        )
    }

    fun publishBomNodeDeleted(
        accountId: String,
        accountName: String,
        projectId: ProjectId,
        bomNodeId: BomNodeId,
    ): Mono<Void> =
        eventSourcingService.publishEvent(
            BomNodeDeletedEvent(
                accountId = accountId,
                accountName = accountName,
                environment = environmentNameSupplier.getEnv(),
                projectId = projectId.toHexString(),
                bomNodeId = bomNodeId.toHexString(),
            ),
        )

    fun publishBomNodeStatusUpdated(
        accountId: String,
        accountName: String,
        projectId: ProjectId,
        bomNodeId: BomNodeId,
        status: BomNodeStatus,
    ): Mono<Void> =
        eventSourcingService.publishEvent(
            BomNodeStatusUpdatedEvent(
                accountId = accountId,
                accountName = accountName,
                environment = environmentNameSupplier.getEnv(),
                projectId = projectId.toHexString(),
                bomNodeId = bomNodeId.toHexString(),
                status = status,
            ),
        )

    fun publishBomNodeUpdated(
        accountId: String,
        accountName: String,
        projectId: ProjectId,
        bomNodeId: BomNodeId,
    ): Mono<Void> {
        return eventSourcingService.publishEvent(
            BomNodeUpdatedEvent(
                accountId = accountId,
                accountName = accountName,
                environment = environmentNameSupplier.getEnv(),
                projectId = projectId.toHexString(),
                bomNodeId = bomNodeId.toHexString(),
            ),
        )
    }

    fun publishPartUpdated(
        accountId: String,
        accountName: String,
        projectId: String,
        partId: String,
    ): Mono<Void> {
        return eventSourcingService.publishEvent(
            PartUpdatedEvent(
                accountId = accountId,
                accountName = accountName,
                environment = environmentNameSupplier.getEnv(),
                projectId = projectId,
                partId = partId,
            ),
        )
    }

    data class SnapshotUpdatedInfo(
        val accountId: String,
        val accountName: String,
        val projectId: String,
        val bomNodeId: String,
        val branchId: String,
        val userId: String,
        val accessedDate: Date,
    )

    companion object {
        private val logger = LoggerFactory.getLogger(EventSourcingModule::class.java)
    }
}
