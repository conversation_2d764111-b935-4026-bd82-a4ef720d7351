package com.tset.core.module.export

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.common.annotations.VisibleForTesting
import com.nu.bom.core.api.dtos.FieldConversionService
import com.nu.bom.core.exception.InternalServerException
import com.nu.bom.core.exception.readable.ErrorCode
import com.nu.bom.core.manufacturing.fieldTypes.EmissionUnits
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.ExportState
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.createBranchId
import com.nu.bom.core.model.toBomNodeId
import com.nu.bom.core.service.BomNodeConversionService
import com.nu.bom.core.service.PPcExportService
import com.nu.bom.core.service.ProjectService
import com.nu.bom.core.service.UserPreferencesService
import com.nu.bom.core.service.bomrads.AllChildren
import com.nu.bom.core.service.bomrads.AllParents
import com.nu.bom.core.service.bomrads.BomNodeLoaderService
import com.nu.bom.core.service.exports.ExportManagerService
import com.nu.bom.core.service.exports.ExportNameUtil
import com.nu.bom.core.service.exports.ExportTempFileManager
import com.nu.bom.core.service.exports.TsetFileExportService
import com.nu.bom.core.service.file.SecureFileService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.user.UserService
import com.nu.bom.core.utils.LegacyInstanceBasedMetaService
import com.nu.bom.core.utils.component1
import com.nu.bom.core.utils.component2
import com.nu.bomrads.dto.admin.ProjectDTO
import com.tset.core.module.TranslationService
import com.tset.core.module.TranslationServiceProvider
import com.tset.core.module.bom.exchangerates.ExchangeRateMap
import com.tset.core.module.export.dto.ExportConfigDto
import com.tset.core.module.export.dto.ExportMode
import com.tset.core.module.export.dto.ExportTemplateWithConfig
import com.tset.core.module.export.dto.FileNameSpec
import com.tset.core.module.export.dto.PcmV9ConfigDto
import com.tset.core.module.export.sheet.CbdSheetProcessors
import com.tset.core.module.export.sheet.DateFieldResult
import com.tset.core.module.export.sheet.FieldSourcers
import com.tset.core.module.export.sheet.FullNumericFieldResult
import com.tset.core.module.export.sheet.LocalDateFieldResult
import com.tset.core.module.export.sheet.MoneyFieldResult
import com.tset.core.module.export.sheet.NumberFieldResult
import com.tset.core.module.export.sheet.SourcedFieldResult
import com.tset.core.module.export.sheet.TextFieldResult
import com.tset.core.module.export.sheet.TranslatedTextFieldResult
import com.tset.core.module.export.sheet.TranslatedTextStepFieldResult
import com.tset.core.module.export.sheet.XSSFUtils
import com.tset.core.module.export.sheet.getNumeratorString
import com.tset.core.module.export.sheet.processor.PcmV9Export
import com.tset.core.module.export.sheet.processor.PcmV9Processor
import com.tset.core.module.export.sheet.processor.SummarySheetProcessorSimple
import com.tset.core.service.ExportService
import com.tset.core.service.account.AccountDataService
import com.tset.core.service.assets.StaticAssetsService
import com.tset.core.service.domain.Currency
import com.tset.core.service.export.AccountSpecificApiExportFormatRequirement
import com.tset.core.service.export.AccountSpecificXlsxExportFormatRequirement
import com.tset.core.service.export.BuiltInExportFormat
import com.tset.core.service.export.BuiltInXlsxExportFormatRequirement
import com.tset.core.service.export.ExportCurrency
import com.tset.core.service.export.ExportFormat
import com.tset.core.service.export.ExportFormatRequirement
import com.tset.core.service.export.ExportResult
import com.tset.core.service.export.PcmV9ExportFormatRequirement
import org.apache.poi.xssf.usermodel.XSSFFormulaEvaluator
import org.apache.poi.xssf.usermodel.XSSFWorkbookFactory
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.core.io.ResourceLoader
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.core.scheduler.Schedulers
import java.io.ByteArrayOutputStream
import java.math.BigDecimal
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.UUID

@Service
class ExportServiceModule(
    private val ppcExportService: PPcExportService,
    @Qualifier("webApplicationContext") private val resourceLoader: ResourceLoader,
    private val bomNodeLoaderService: BomNodeLoaderService,
    private val bomNodeConversionService: BomNodeConversionService,
    private val projectService: ProjectService,
    private val userService: UserService,
    private val entityManager: LegacyInstanceBasedMetaService,
    private val accountDataService: AccountDataService,
    private val translationServiceProvider: TranslationServiceProvider,
    private val fileService: SecureFileService,
    private val userPreferencesService: UserPreferencesService,
    private val tsetFileExport: TsetFileExportService,
    private val pcmV9Processor: PcmV9Processor,
    private val secureFileService: SecureFileService,
    private val staticAssetsService: StaticAssetsService,
    private val fieldConversionService: FieldConversionService,
    private val exportManagerService: ExportManagerService,
    private val tempFileService: ExportTempFileManager,
    private val objectMapper: ObjectMapper,
) : ExportService {
    companion object {
        val logger: Logger = LoggerFactory.getLogger(ExportServiceModule::class.java)
        const val DEBUG_SHEETS_ENABLED_SWITCH = "enableCbdExportDebugSheets"
    }

    override fun export(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        bomNodeId: BomNodeId,
        branchId: String?,
        format: ExportFormatRequirement,
        currency: ExportCurrency,
    ): Mono<ExportResult> =
        when (format.formatName) {
            ExportFormat.XLSX -> exportXlsx(accessCheck, bomNodeId, branchId, format, currency)
            ExportFormat.CSV ->
                exportV9(
                    accessCheck,
                    bomNodeId,
                    branchId,
                    format as PcmV9ExportFormatRequirement,
                    currency,
                )

            ExportFormat.TSET -> exportTset(accessCheck, projectId, bomNodeId, branchId)
            else -> throw IllegalArgumentException("format $format not support for File Export")
        }

    private fun exportTset(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        bomNodeId: BomNodeId,
        branchId: String?,
    ): Mono<ExportResult> =
        tsetFileExport
            .exportTsetFile(accessCheck, projectId, bomNodeId, createBranchId(branchId))
            .map(ExportResult::fromPair)

    override fun exportStatus(
        accessCheck: AccessCheck,
        exportId: UUID,
    ): Mono<ExportState> = exportManagerService[exportId]

    override fun exportData(
        accessCheck: AccessCheck,
        exportId: UUID,
    ): Mono<ExportResult> =
        tempFileService.get(exportId, accessCheck).map { content ->
            val fileName = ExportNameUtil.artifactName(exportId)
            ExportResult(fileName, content)
        }

    override fun exportProject(
        accessCheck: AccessCheck,
        format: ExportFormatRequirement,
        currency: ExportCurrency,
        projectId: ProjectId,
    ): Mono<UUID> =
        when (format.formatName) {
            ExportFormat.TSET -> tsetFileExport.exportTsetProjectFile(accessCheck, projectId)
            else -> throw IllegalArgumentException("format $format not support for Project Export")
        }

    override fun exportImportTcPCM(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
        branchId: String?,
        format: ExportFormatRequirement,
        targetId: Int?,
    ): Mono<String> =
        exportV9Data(
            accessCheck,
            bomNodeId,
            branchId,
            PcmV9ExportFormatRequirement(
                "TcPCM",
                ExportFormat.CSV,
                "TcPCM",
                "cbd_export_config_v9.json",
            ),
            ExportCurrency(Currency.EUR, "#,##0.00\" €\""),
        ).flatMap { (env, v9Export) ->
            ppcExportService.importTcPCM(
                accessCheck,
                (format as AccountSpecificApiExportFormatRequirement).exportApiConfigDto,
                env,
                v9Export,
            )
        }

    private fun exportXlsx(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
        branchId: String?,
        format: ExportFormatRequirement,
        currency: ExportCurrency,
    ): Mono<ExportResult> {
        logger.info("Export format ${format.formatName} requested, checking sheets...")
        return when (format.formatId) {
            BuiltInExportFormat.PPC_V9_XLSX.formatRequirement.formatId ->
                exportV9(accessCheck, bomNodeId, branchId, format as PcmV9ExportFormatRequirement, currency)

            else ->
                getDebugSheetsEnabled(accessCheck).flatMap { debugSheetsEnabled ->
                    getTemplateWithConfig(format, debugSheetsEnabled)
                        .flatMap { exportTemplateWithConfig ->
                            getBomNodeSnapshotForExport(
                                accessCheck,
                                bomNodeId,
                                branchId,
                                currency,
                                format.exportMode,
                                AllParents(bomNodeId.toBomNodeId()),
                            ).flatMap { snapshotEnvironment ->
                                Flux
                                    .fromIterable(
                                        XSSFUtils
                                            .getSheetNames(exportTemplateWithConfig.template)
                                            .flatMap { sheetName ->
                                                CbdSheetProcessors.entries.mapNotNull { sheetProcessor ->
                                                    if (sheetProcessor.canProcess(sheetName)) {
                                                        sheetProcessor
                                                    } else {
                                                        null
                                                    }
                                                }
                                            },
                                    ).concatMap { toBeProcessed ->
                                        toBeProcessed.process(
                                            exportTemplateWithConfig = exportTemplateWithConfig,
                                            snapshotEnvironment = snapshotEnvironment,
                                            fieldConversionService = fieldConversionService,
                                            exportServiceModule = this,
                                            format = format,
                                            currency = currency,
                                        )
                                    }.then(Mono.just(exportTemplateWithConfig))
                            }
                        }.map { exportTemplateWithConfig: ExportTemplateWithConfig ->
                            logger.info("...export finished, now recalculating all formulas...")
                            XSSFFormulaEvaluator.evaluateAllFormulaCells(exportTemplateWithConfig.template)
                            logger.info("CBD Export done.")
                            val bytes = ByteArrayOutputStream()
                            exportTemplateWithConfig.template.write(bytes)
                            val exportFileName =
                                getExportFileName(
                                    exportTemplateWithConfig.config.filename,
                                    exportTemplateWithConfig.fieldResults,
                                    ExportFormat.XLSX,
                                )
                            exportTemplateWithConfig.template.close()
                            ExportResult(exportFileName, bytes.toByteArray())
                        }
                }
        }
    }

    private fun exportV9(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
        branchId: String?,
        format: PcmV9ExportFormatRequirement,
        currency: ExportCurrency,
    ): Mono<ExportResult> {
        logger.info("Export format ${format.formatName} requested, checking config...")
        val config = getConfig(format.configResourceFile, PcmV9ConfigDto::class.java)
        return exportV9Data(
            accessCheck,
            bomNodeId,
            branchId,
            format,
            currency,
        ).map { (env, export) ->
            logger.info("PcmV9 Export done.")
            val bytes = export.writeAsFormat(format.formatName, env)
            // We don't have field results from processing a summary page, so we manually create a map of just the
            // part name, part number and last changed date
            val fields =
                mapOf(
                    "@_PART_NAME" to TextFieldResult("${env.snapshot.partDesignation}"),
                    "@_PART_NUMBER" to TextFieldResult("${env.snapshot.manufacturing?.getField("partNumber")?.result?.res}"),
                    "@_LAST_CHANGED_DATE" to DateFieldResult(env.snapshot.lastModifiedDate!!),
                )
            ExportResult(getExportFileName(config.filename, fields, format.formatName), bytes)
        }
    }

    fun exportV9Data(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
        branchId: String?,
        format: PcmV9ExportFormatRequirement,
        currency: ExportCurrency,
    ): Mono<Pair<BomNodeEnvironment, PcmV9Export>> {
        logger.info("Export format ${format.formatName} requested, checking config...")
        val config = getConfig(format.configResourceFile, PcmV9ConfigDto::class.java)
        return getBomNodeSnapshotForExport(
            accessCheck,
            bomNodeId,
            branchId,
            currency,
            format.exportMode,
            AllChildren(bomNodeId = bomNodeId),
        ).flatMap { env ->
            pcmV9Processor.process(config, env).map { env to it }
        }
    }

    private fun getDebugSheetsEnabled(accessCheck: AccessCheck): Mono<Boolean> =
        userPreferencesService
            .getDebugValue(
                accessCheck = accessCheck,
                DEBUG_SHEETS_ENABLED_SWITCH,
            ).map { value ->
                logger.info("DEBUG sheets override detected: $DEBUG_SHEETS_ENABLED_SWITCH = $value")
                when (value) {
                    is Boolean -> value
                    is String -> value.toBoolean()
                    else -> false
                }
            }.switchIfEmpty(Mono.just(false))

    private fun getExportFileName(
        spec: FileNameSpec,
        fields: Map<String, SourcedFieldResult>,
        exportFormat: ExportFormat,
    ): String {
        val fileNameChunks =
            spec.nameParts.map { part ->
                if (SummarySheetProcessorSimple.processorPrefixes.any { part.startsWith(it) }) {
                    when (val res = fields[part]) {
                        is TextFieldResult -> res.text
                        is TranslatedTextFieldResult -> res.text
                        is TranslatedTextStepFieldResult -> res.text
                        is NumberFieldResult -> res.number
                        is MoneyFieldResult -> res.number
                        is DateFieldResult ->
                            res.date
                                .toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDateTime()
                                .format(DateTimeFormatter.ofPattern(spec.dateTimeFormat))

                        is LocalDateFieldResult -> DateTimeFormatter.ofPattern(spec.dateTimeFormat).format(res.date)
                        is FullNumericFieldResult -> res.res
                        null -> part
                    }
                } else {
                    part
                }
            }
        return fileNameChunks
            .mapIndexed { index, value ->
                if (index == 1 && value.toString().isNotEmpty()) {
                    "($value)"
                } else {
                    value
                }
            }.joinToString("", postfix = exportFormat.extension)
    }

    @VisibleForTesting
    fun getBomNodeSnapshotForExport(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
        branchId: String?,
        currency: ExportCurrency,
        exportMode: ExportMode,
        bomradLoadingMode: com.nu.bom.core.service.bomrads.LoadingMode,
    ): Mono<BomNodeEnvironment> =
        bomNodeLoaderService
            .getBomNode(
                accessCheck,
                bomNodeId = bomNodeId,
                branchId = createBranchId(branchId),
                loadingMode = bomradLoadingMode,
            ).flatMap { rootSnapshot ->
                projectService.getProject(accessCheck, rootSnapshot.projectId()).flatMap { project ->
                    val snapshot = rootSnapshot.findChildrenBy(bomNodeId)!!
                    val bomNodeSnapshot =
                        bomNodeConversionService
                            .bomNodeToManufacturingCalculationTree(
                                rootSnapshot,
                                snapshot.collectChildrenBomNodeIds().toSet(),
                            ).snapshot!!
                    val exchangeRateMap = bomNodeSnapshot.manufacturing?.getExchangeRateMap() ?: ExchangeRateMap.empty()
                    translationServiceProvider.getTranslationsService().flatMap { translationService ->
                        Mono
                            .zip(
                                userService.getUserById(accessCheck, snapshot.createdBy),
                                userService.getUserById(accessCheck, snapshot.lastModifiedBy),
                            ).map { (createdByUser, lastModifiedByUser) ->
                                BomNodeEnvironment(
                                    accessCheck = accessCheck,
                                    entityManager = entityManager,
                                    translationService = translationService,
                                    fileService = fileService,
                                    userService = userService,
                                    snapshot = bomNodeSnapshot,
                                    exchangeRateMap = exchangeRateMap,
                                    project = project,
                                    createdByUserName = createdByUser.map { it.name }.orElse(null),
                                    lastModifiedByUserName = lastModifiedByUser.map { it.name }.orElse(null),
                                    currency = currency,
                                    exportMode = exportMode,
                                    fieldSourcers = FieldSourcers(secureFileService, staticAssetsService),
                                    fieldConversionService = fieldConversionService,
                                )
                            }
                    }
                }
            }

    private fun getTemplateWithConfig(
        format: ExportFormatRequirement,
        debugSheetsEnabled: Boolean,
    ): Mono<ExportTemplateWithConfig> =
        when (format) {
            is BuiltInXlsxExportFormatRequirement -> getTemplateWithConfigForBuiltIn(format, debugSheetsEnabled)
            is AccountSpecificXlsxExportFormatRequirement -> getTemplateWithConfig(format, debugSheetsEnabled)
            else -> Mono.error(InternalServerException(ErrorCode.EXPORT_FORMAT_UNKNOWN))
        }

    private fun getTemplateWithConfig(
        format: AccountSpecificXlsxExportFormatRequirement,
        debugSheetsEnabled: Boolean,
    ): Mono<ExportTemplateWithConfig> =
        accountDataService.getDataById(format.templateAccountDataId).flatMap { templateWorkbookBytes ->
            accountDataService.getDataById(format.configAccountDataId).map { configJsonBytes ->
                val templateWorkbook = XSSFWorkbookFactory().create(templateWorkbookBytes.inputStream())
                val exportConfigDto = objectMapper.readValue(configJsonBytes, ExportConfigDto::class.java)

                ExportTemplateWithConfig(
                    template = templateWorkbook,
                    config = exportConfigDto,
                    fieldResults = HashMap(),
                    debugSheetsEnabled = debugSheetsEnabled,
                )
            }
        }

    private fun getTemplateWithConfigForBuiltIn(
        format: BuiltInXlsxExportFormatRequirement,
        debugSheetsEnabled: Boolean,
    ): Mono<ExportTemplateWithConfig> =
        Mono
            .fromCallable {
                XSSFWorkbookFactory().create(
                    TemplateResource
                        .getTemplateAsClasspathFileResource(
                            format.templateResourceFile,
                            resourceLoader,
                        ).inputStream,
                )
            }.map { templateWorkbook ->
                val exportConfigDto = getConfig(format.configResourceFile, ExportConfigDto::class.java)
                ExportTemplateWithConfig(
                    template = templateWorkbook,
                    config = exportConfigDto,
                    fieldResults = HashMap(),
                    debugSheetsEnabled = debugSheetsEnabled,
                )
            }.subscribeOn(Schedulers.boundedElastic())

    private fun <T> getConfig(
        configResourceFile: String,
        clazz: Class<T>,
    ) = objectMapper.readValue(
        TemplateResource
            .getConfigAsClasspathFileResource(
                configResourceFile,
                resourceLoader,
            ).inputStream,
        clazz,
    )

    @VisibleForTesting
    fun getPcmV9ConfigDto(): PcmV9ConfigDto =
        getConfig(
            (BuiltInExportFormat.PPC_V9_XLSX.formatRequirement as PcmV9ExportFormatRequirement).configResourceFile,
            PcmV9ConfigDto::class.java,
        )
}

private const val CO2_FORMAT = "#,##0.00\" kg CO₂e\""

data class BomNodeEnvironment(
    val accessCheck: AccessCheck,
    val entityManager: LegacyInstanceBasedMetaService,
    val translationService: TranslationService,
    val fileService: SecureFileService,
    val userService: UserService,
    val snapshot: BomNodeSnapshot,
    val exchangeRateMap: ExchangeRateMap,
    val project: ProjectDTO,
    val createdByUserName: String?,
    val lastModifiedByUserName: String?,
    private val currency: ExportCurrency,
    val exportMode: ExportMode,
    val fieldSourcers: FieldSourcers,
    val fieldConversionService: FieldConversionService,
) {
    fun getCurrency() =
        if (exportMode == ExportMode.CO2) {
            Currency.EUR
        } else {
            currency.ccy
        }

    fun getCostlikeFormat() =
        if (exportMode == ExportMode.COST) {
            currency.numberFormat
        } else {
            CO2_FORMAT
        }

    fun getDisplayUnit() =
        if (exportMode == ExportMode.CO2) {
            getNumeratorString(EmissionUnits.KILOGRAM_CO2E.toString(), translationService)
        } else {
            currency.ccy.ccy
        }

    fun convertToEnvironmentCurrency(fieldValue: BigDecimal): BigDecimal =
        exchangeRateMap.ccyToCcy(
            fieldValue,
            Currency.EUR,
            getCurrency(),
        ) ?: error("Cannot find currency info in environment")
}
