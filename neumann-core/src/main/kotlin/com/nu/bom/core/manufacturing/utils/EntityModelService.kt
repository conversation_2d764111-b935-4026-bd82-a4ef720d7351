package com.nu.bom.core.manufacturing.utils

import com.google.common.annotations.VisibleForTesting
import com.nu.bom.core.manufacturing.annotations.BehaviourCreation
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityDeletion
import com.nu.bom.core.manufacturing.annotations.EntityLinkProvider
import com.nu.bom.core.manufacturing.annotations.EntityProvider
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Modularized
import com.nu.bom.core.manufacturing.annotations.Nocalc
import com.nu.bom.core.manufacturing.annotations.OrderedEntityCreation
import com.nu.bom.core.manufacturing.annotations.ThreeDb
import com.nu.bom.core.manufacturing.annotations.ThreeDbResourceTracker
import com.nu.bom.core.manufacturing.annotations.ThreeDbVersionedPart
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.ManufacturingWithRef
import com.nu.bom.core.manufacturing.service.FieldType
import com.nu.bom.core.manufacturing.service.behaviour.DynamicBehaviour
import com.nu.bom.core.manufacturing.service.virtualfield.VirtualFieldProviderRegistry
import com.nu.bom.core.utils.ExtensionManager
import org.springframework.stereotype.Service
import java.util.stream.Collectors
import kotlin.reflect.KCallable
import kotlin.reflect.KClass
import kotlin.reflect.KType
import kotlin.reflect.full.createType
import kotlin.reflect.full.declaredMembers
import kotlin.reflect.full.findAnnotation
import kotlin.reflect.full.isSubtypeOf
import kotlin.reflect.jvm.jvmErasure

private typealias EntityClass = KClass<out ManufacturingEntity>

@Deprecated("Do not use this", ReplaceWith("BomNodeSchemaService"))
@Service
class EntityModelService(
    private val virtualFieldProviderRegistry: VirtualFieldProviderRegistry,
    private val fieldService: EntityFieldService,
    private val extensionManager: ExtensionManager,
) {
    private val entityModels = mutableMapOf<EntityClass, EntityModel>()

    fun getEntityModelFor(
        prototype: ManufacturingEntity,
        baseEntity: Boolean = true,
    ): EntityModel = getEntityModel(entityClass = prototype::class, prototype = prototype, validateAsBaseEntity = baseEntity)

    @VisibleForTesting
    fun putEntityModel(
        entityClass: EntityClass,
        model: EntityModel,
    ) {
        entityModels[entityClass] = model
    }

    fun reset() {
        entityModels.clear()
    }

    fun getSuperClassesIncludingSelf(entityKClass: KClass<out ManufacturingEntity>): List<KClass<out ManufacturingEntity>> {
        val entityModel = getEntityModel(entityKClass, true)
        val extends = entityModel.extendsEntityModel
        return if (extends != null) {
            // we add the own class to the end that the order is from base to requested entity class
            getSuperClassesIncludingSelf(extends.type) + entityKClass
        } else {
            listOf(entityKClass)
        }
    }

    fun getEntityModel(entityClass: EntityClass): EntityModel? = entityModels[entityClass]

    fun getEntityModel(
        entityClass: EntityClass,
        validateAsBaseEntity: Boolean,
        prototype: ManufacturingEntity? = null,
    ): EntityModel =
        getEntityModel(entityClass)
            ?: synchronized(this) {
                val doubleCheck = getEntityModel(entityClass)
                if (doubleCheck == null) {
                    val newModel = createModel(entityClass, prototype)
                    if (validateAsBaseEntity) {
                        newModel.validateBaseModel()
                    }
                    putEntityModel(entityClass, newModel)
                    newModel
                } else {
                    doubleCheck
                }
            }

    private fun createModel(
        entityClass: EntityClass,
        prototype: ManufacturingEntity?,
    ): EntityModel {
        val entityToUse = prototype ?: ManufacturingWithRef.createNamedEntity(entityClass.java, "prototype")

        val resultMap = collectFieldsRecursively(entityClass, entityToUse)
        val extends = entityToUse.extends
        val extendsEntityModel =
            if (extends != null) {
                getEntityModel(extends::class, validateAsBaseEntity = false, prototype = extends)
            } else {
                null
            }

        val behaviours = entityToUse.behaviours.map { getEntityModel(it::class, prototype = it, validateAsBaseEntity = false) }

        return createEntityModel(
            entityClass = entityClass,
            fields = resultMap,
            extendsEntityModel = extendsEntityModel,
            behaviours = behaviours,
        )
    }

    private fun createEntityModel(
        entityClass: EntityClass,
        fields: Map<String, FieldModel>,
        extendsEntityModel: EntityModel?,
        behaviours: List<EntityModel>,
    ): EntityModel {
        var allFields = fields
        if (extendsEntityModel != null) {
            allFields = extendsEntityModel.allFields + allFields
        }
        allFields =
            behaviours
                .foldRight(allFields) { value, acc ->
                    value.allFields + acc
                }.filter {
                    it.value.fieldType != FieldType.ChildLoadTrigger
                }
        val virtualFields =
            virtualFieldProviderRegistry.providers
                .mapNotNull { fieldProvider ->
                    if (fieldProvider.needsVirtualFieldModel(allFields)) {
                        fieldProvider.fieldTypeForProvider() to fieldProvider.createFieldModel()
                    } else {
                        null
                    }
                }.toMap()

        val modularizedAnnot = entityClass.findAnnotation<Modularized>()

        val model =
            EntityModel(
                entityClass,
                fields = fields,
                extendsEntityModel = extendsEntityModel,
                behaviours = behaviours,
                allFields = allFields,
                virtualFields = virtualFields,
                modularized = modularizedAnnot,
                extensionFieldModels = getExtensionFieldModels(entityClass),
            )

        fields.values.forEach {
            it.entityModel = model
        }

        model.validate()
        return model
    }

    private fun getExtensionFieldModels(entityKClass: KClass<out ManufacturingEntity>): Map<String, FieldModel> {
        val extensionClasses = extensionManager.getExtensionsClasses(entityKClass)

        return extensionClasses
            .map { extensionKClass ->
                getEntityModel(extensionKClass, false).allFields
            }.reduceOrNull { acc, map -> acc + map } ?: mapOf()
    }

    private fun collectFieldsRecursively(
        entityClass: KClass<*>,
        prototype: ManufacturingEntity,
    ): Map<String, FieldModel> {
        val superTypes = entityClass.supertypes
        val res =
            if (superTypes.isNotEmpty()) {
                superTypes
                    .map {
                        collectFieldsRecursively(it.jvmErasure, prototype)
                    }.fold(
                        emptyMap<String, FieldModel>(),
                    ) { acc, one ->
                        acc + one
                    }
            } else {
                emptyMap()
            }
        return res + createFieldModels(entityClass.declaredMembers, prototype)
    }

    private fun createFieldModels(
        members: Collection<KCallable<*>>,
        prototype: ManufacturingEntity,
    ): Map<String, FieldModel> =
        members
            .parallelStream()
            .filter {
                fieldService.isField(it)
            }.map { field ->
                EntityModelHelper.createFieldModelFromFunction(getFieldType(field), field, prototype)
            }
            .collect(Collectors.groupingBy { it.name })
            .mapValues {
                check(it.value.size <= 1) {
                    val fields = it.value.joinToString(separator = "\n\t") { model -> model.toString() }
                    "Multiple field declared with the same name: ${it.key} in ${prototype.toHumanReadableName()}\n\t$fields"
                }
                it.value.last()
            }

    companion object {
        private fun getInputStatus(field: KCallable<*>): FieldType.InputStatus {
            val input = field.findAnnotation<Input>()

            return when {
                input == null || !input.active -> FieldType.InputStatus.NO_INPUT
                input.useAsFallback -> FieldType.InputStatus.INPUT_AS_FALLBACK
                else -> FieldType.InputStatus.INPUT
            }
        }

        fun getFieldType(field: KCallable<*>): FieldType {
            return when {
                field.findAnnotation<OrderedEntityCreation>() != null -> FieldType.OrderedEntityCreation
                field.findAnnotation<EntityProvider>() != null -> FieldType.EntityProvider
                field.findAnnotation<EntityCreation>() != null -> FieldType.Creation
                field.findAnnotation<BehaviourCreation>() != null -> FieldType.BehaviourCreation
                field.findAnnotation<EntityDeletion>() != null -> FieldType.EntityDeletion
                field.findAnnotation<EntityLinkProvider>() != null -> FieldType.EntityLinkProvider
                (
                    field.findAnnotation<ThreeDb>() ?: field.findAnnotation<ThreeDbVersionedPart>()
                        ?: field.findAnnotation<ThreeDbResourceTracker>()
                ) != null ->
                    FieldType.Calculation(
                        FieldType.CalculationFieldType.THREE_DB,
                        inputStatus = getInputStatus(field),
                    )
                else -> FieldType.Calculation(FieldType.CalculationFieldType.NORMAL, inputStatus = getInputStatus(field))
            }
        }
    }
}

@Service
class EntityFieldService {
    fun isField(field: KCallable<*>): Boolean =
        isChildOrderingField(field) ||
            isEntityProvider(field) ||
            (
                EntityModelHelper.isResultType(field.returnType) ||
                    EntityModelHelper.isAsyncResultType(field.returnType) ||
                    isObjectCreationField(
                        field,
                    ) ||
                    isDynamicBehaviourCreationField(
                        field,
                    ) ||
                    isEntityDeletionField(field)
            ) &&
            field.findAnnotation<Nocalc>() == null

    private fun isChildOrderingField(field: KCallable<*>): Boolean =
        if (field.findAnnotation<OrderedEntityCreation>() != null) {
            check(field.parameters.size <= 1) {
                "Field $field should not have parameters as a ChildOrdering field, but it has: ${field.parameters.drop(1)}"
            }
            check(field.returnType == EntityModelHelper.stringArray) {
                "Field $field should return a String array instead of ${field.returnType}"
            }
            val otherAnnotation = field.annotations.firstOrNull { it !is OrderedEntityCreation }
            check(otherAnnotation == null) { "Unexpected annotation $otherAnnotation on $field" }
            true
        } else {
            false
        }

    private fun isEntityProvider(field: KCallable<*>): Boolean =
        if (field.findAnnotation<EntityProvider>() != null) {
            check(isManufacturingEntitySubType(field.returnType)) {
                "Field $field should return a ManufacturingEntity instead of ${field.returnType}"
            }
            val otherAnnotation = field.annotations.firstOrNull { it !is EntityProvider }
            check(otherAnnotation == null) {
                "Unexpected annotation $otherAnnotation on $field"
            }
            true
        } else {
            false
        }

    private fun isObjectCreationField(field: KCallable<*>): Boolean =
        field.findAnnotation<EntityCreation>() != null &&
            isManufacturingEntitySubType(field.returnType)

    private fun isDynamicBehaviourCreationField(field: KCallable<*>): Boolean =
        field.findAnnotation<BehaviourCreation>() != null &&
            isDynamicBehaviourSubType(field.returnType)

    private fun isDynamicBehaviourSubType(type: KType) =
        EntityModelHelper.isPotentiallyNullableSubtypeOf<DynamicBehaviour>(type) ||
            type.arguments.find {
                it.type?.isSubtypeOf(DynamicBehaviour::class.createType()) ?: false
            } != null

    private fun isEntityDeletionField(field: KCallable<*>): Boolean =
        field.findAnnotation<EntityDeletion>() != null &&
            EntityModelHelper.isPotentiallyNullableSubtypeOf<String>(field.returnType)

    private fun isManufacturingEntitySubType(type: KType) =
        EntityModelHelper.isPotentiallyNullableSubtypeOf<ManufacturingEntity>(type) ||
            type.arguments.find { it.type?.isSubtypeOf(ManufacturingEntity::class.createType()) ?: false } != null
}
