package com.nu.bom.core.api.dtos

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.nu.bom.core.api.dtos.ExternalDataDto.Companion.matchingExternalDataSupplement
import com.nu.bom.core.manufacturing.annotations.ExternalData
import com.nu.bom.core.manufacturing.entities.Attachment
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.FieldWithResult
import com.nu.bom.core.manufacturing.fieldTypes.NoCalcData
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.ThreeDbVersionedPart
import com.nu.bom.core.manufacturing.fieldTypes.VersionedPart
import com.nu.bom.core.manufacturing.service.FieldKey
import com.nu.bom.core.model.MasterDataSelector
import com.nu.bom.core.service.ExternalDataService
import com.nu.bom.core.service.MasterDataMergeService
import com.nu.bom.core.service.imports.CalculationImportExportService
import org.bson.types.ObjectId
import java.time.Instant
import kotlin.reflect.KClass

data class ManufacturingDto(
    val id: String,
    val type: Entities?,
    val name: String,
    val ref: String,
    val version: Int,
    val part: PartDto?,
    val shape: ShapeDto?,
    @JsonIgnore
    val data: Map<String, Any> = mapOf(),
    val className: String?,
    val isolated: Boolean,
    val fields: List<FieldParameter>,
    val children: MutableList<ManufacturingDto>,
    val model: CalculationModel?,
    val createdByField: CreatedByFieldDto?,
    val createdByMocked: Boolean,
    val createdOnBranch: String?,
    val masterDataKey: MasterDataKeyDto?,
    val title: String? = null,
    val deletable: Boolean,
    val copyable: Boolean,
    val dynamicFields: Map<String, DynamicFieldDto>?,
    val customProcurementType: String?,
) {
    fun findEntity(entityName: String): List<ManufacturingDto> =
        if (this.name == entityName) {
            listOf(this)
        } else {
            children.flatMap { it.findEntity(entityName) }
        }

    fun getField(name: String): FieldParameter =
        this.fields.find {
            it.name == name
        } ?: error(
            "Field not found with name:$name in " +
                this.fields.joinToString(
                    separator = "\n",
                ),
        )
}

data class ModuleImportDto(
    val type: Entities? = null,
    val name: String,
    val manufacturingType: String,
    val part: PartDto?,
    val inputs: List<com.nu.bom.core.publicapi.dtos.FieldParameter>,
    val overrides: List<com.nu.bom.core.publicapi.dtos.FieldParameter>,
    var children: List<ModuleImportDto>,
    val masterDataKey: MasterDataKeyDto?,
    val title: String? = null,
    val year: Int,
)

private fun getShapeId(fields: List<FieldWithResult>): Text? =
    fields
        .find {
            it.name.name == Manufacturing::shapeId.name
        }?.let { it.result as? Text }

data class ManufacturingImportDto(
    val id: String,
    val type: Entities?,
    val name: String,
    val ref: String,
    val version: Int,
    val part: PartDto?,
    val partName: String?,
    @JsonIgnore
    val data: Map<String, Any> = mapOf(),
    val className: String?,
    val fieldsWithResult: List<FieldWithResult>,
    val initialFieldsWithResult: List<FieldWithResult>,
    val children: MutableList<ManufacturingImportDto>,
    val model: CalculationModel?,
    val createdByField: CreatedByFieldDto?,
    val providerField: ProviderFieldDto?,
    val createdByMocked: Boolean?,
    val createdOnBranch: String?,
    val masterDataKey: MasterDataKeyDto?,
    val title: String? = null,
    val year: Int,
    // Only needed for public api, not for import/export
    @JsonIgnore
    val bomNodeId: String? = null,
    val dirtyChildLoading: Boolean = false,
    val isolated: Boolean = false,
    val dynamicFields: Map<String, DynamicFieldDto> = mapOf(),
    val protectedAt: Instant?,
    val protectedBy: String?,
) {
    fun shapeId(): Text? = getShapeId(fieldsWithResult) ?: getShapeId(initialFieldsWithResult)
}

@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, include = JsonTypeInfo.As.PROPERTY, property = "@class")
data class ImportExportDto(
    val manufacturingDto: ManufacturingImportDto,
    val externalData: List<ExternalDataDto> = listOf(),
    val noCalcData: List<NoCalcDataDto> = listOf(),
    val versionedParts: List<VersionedPartDataDto> = listOf(),
)

// ExternalDataInternal by default only allows for a single piece of information,
// so if the export import flow requires more than this, ExternalDataSupplement is your friend
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
sealed interface ExternalDataSupplement

data object EmptyExternalDataSupplement : ExternalDataSupplement

data class ThreeDbAttachmentSupplement(
    val versionedPart: ThreeDbVersionedPart,
) : ExternalDataSupplement

data class ExternalDataInternal(
    val field: FieldWithResult,
    val type: ExternalData.EXTERNAL_DATA_TYPE,
    val isMainImage: Boolean,
    val supplement: ExternalDataSupplement = EmptyExternalDataSupplement,
) {
    init {
        require(matchingExternalDataSupplement(type).isInstance(supplement))
    }

    fun addData(data: ExternalDataService.ExternalDto) = ExternalDataDto(field, type, data, isMainImage)

    companion object {
        fun createFromAttachment(
            attachment: ManufacturingEntity,
            parentEntityId: String,
            parentEntityType: Entities,
        ): ExternalDataInternal? {
            val fileId = attachment.getFieldResult(Attachment::fileId.name) as? Text?
            if (fileId == null || !ObjectId.isValid(fileId.res)) {
                return null
            }

            val maybeThreeDbVersionedPart = attachment.getFieldResult(Attachment::versionedPart.name) as? VersionedPart?
            val isMainImage = (attachment.getFieldResult(Attachment::isMainImage.name) as? Bool)?.res ?: false

            val (externalDataType, supplement) =
                if (maybeThreeDbVersionedPart != null) {
                    ExternalData.EXTERNAL_DATA_TYPE.THREE_DB_ATTACHMENT to ThreeDbAttachmentSupplement(maybeThreeDbVersionedPart.res)
                } else {
                    ExternalData.EXTERNAL_DATA_TYPE.PART_IMAGE to EmptyExternalDataSupplement
                }

            val fieldKey =
                FieldKey(
                    name = "partImage",
                    type = "text",
                    entityId = parentEntityId,
                    entityType = parentEntityType.toString(),
                    version = 0,
                )

            return ExternalDataInternal(
                field = FieldWithResult(name = fieldKey, result = fileId),
                type = externalDataType,
                isMainImage = isMainImage,
                supplement = supplement,
            )
        }
    }
}

@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, include = JsonTypeInfo.As.PROPERTY, property = "@class")
data class ExternalDataDto(
    val field: FieldWithResult,
    val type: ExternalData.EXTERNAL_DATA_TYPE,
    val data: ExternalDataService.ExternalDto,
    val isMainImage: Boolean,
) {
    init {
        val supplementClazz = matchingExternalDataSupplement(type)
        if (supplementClazz != EmptyExternalDataSupplement::class) {
            require(data is CalculationImportExportService.FileUpload)
            require(supplementClazz.isInstance(data.supplement))
        }
    }

    companion object {
        fun matchingExternalDataSupplement(type: ExternalData.EXTERNAL_DATA_TYPE): KClass<out ExternalDataSupplement> {
            return when (type) {
                ExternalData.EXTERNAL_DATA_TYPE.TURNING_PROFILE,
                ExternalData.EXTERNAL_DATA_TYPE.MILLING_PROFILE,
                ExternalData.EXTERNAL_DATA_TYPE.PART_IMAGE,
                ExternalData.EXTERNAL_DATA_TYPE.SMF_PROFILE,
                -> EmptyExternalDataSupplement::class

                ExternalData.EXTERNAL_DATA_TYPE.THREE_DB_ATTACHMENT -> ThreeDbAttachmentSupplement::class
            }
        }
    }
}

data class NoCalcDataDto(
    val id: String,
    @JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
    val data: NoCalcData,
)

data class VersionedPartDataDto(
    val id: ThreeDbVersionedPart,
    val data: Map<String, ByteArray>,
)

data class DynamicFieldDto(
    val entityClass: String?,
    val entityFieldName: String,
)

data class MasterDataKeyDto(
    val type: MasterDataType,
    val key: String,
    val year: Int,
    val location: String,
    val usedId: String?,
    val latestId: String?,
    val usedVersion: Int?,
    val latestVersion: Int?,
) {
    constructor(
        selector: MasterDataSelector,
        usedId: String?,
        latestId: String?,
        usedVersion: Int?,
        latestVersion: Int?,
    ) : this(
        type = selector.type,
        key = selector.key,
        year = selector.year,
        location = selector.location,
        usedId = usedId,
        latestId = latestId,
        usedVersion = usedVersion,
        latestVersion = latestVersion,
    )

    companion object {
        fun from(
            entity: ManufacturingEntity,
            latestMasterData: MasterDataMergeService.LatestMasterData?,
        ): MasterDataKeyDto? {
            val selector = entity.masterDataSelector

            return if (selector != null) {
                MasterDataKeyDto(
                    selector = selector,
                    usedId = entity.masterDataObjectId?.toHexString(),
                    usedVersion = entity.masterDataVersion,
                    latestId = latestMasterData?.latestObjectId?.toHexString(),
                    latestVersion = latestMasterData?.latestVersion,
                )
            } else {
                null
            }
        }
    }
}
