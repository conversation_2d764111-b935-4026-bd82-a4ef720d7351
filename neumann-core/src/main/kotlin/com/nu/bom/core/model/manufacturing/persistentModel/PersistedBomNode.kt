package com.nu.bom.core.model.manufacturing.persistentModel

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer
import com.nu.bom.core.manufacturing.annotations.CreatedWithSystemVersion
import com.nu.bom.core.model.AccountId
import com.nu.bom.core.model.BaseEntity
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.Indicator
import com.nu.bom.core.model.KeyPerformanceIndicator
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.SnapshotId
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import com.nu.bom.core.service.migration.lazy.MigrationManager
import com.nu.bom.core.version.PlatformVersion
import com.querydsl.core.annotations.QueryEntity
import org.bson.types.ObjectId
import org.reactivestreams.Publisher
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.Id
import org.springframework.data.annotation.LastModifiedBy
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.annotation.Transient
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.mapping.event.ReactiveAfterConvertCallback
import org.springframework.stereotype.Component
import reactor.core.publisher.Mono
import java.util.Date

@Document(collection = BomNodeSnapshot.COLLECTION_NAME)
@QueryEntity
@Suppress("ktlint:standard:backing-property-naming")
data class PersistedBomNode(
    val name: String,
    val year: Int,
    val title: String,
    val accountId: AccountId?,
    // mutable, just to enable late initialization, in the persistence callback
    @Deprecated("use getBaseManufacturing().partId")
    var partId: ObjectId?,
    val manufacturing: org.bson.Document,
    val generated: Boolean = false,
    /**
     * Key performance indicators to ease the access in the db or in the frontend.
     */
    val kpi: KeyPerformanceIndicator =
        KeyPerformanceIndicator(
            cO2PerPart = Indicator(null, null, true),
            costPerPart = Indicator(null, null, true),
            branchId = null,
        ),
    @Deprecated("Replaced with manufacturing.fieldWithResults['responsible']")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    val responsibleUser: String? = null,
    val partName: String?,
    var lastMigrationChangeSetId: MigrationChangeSetId?,
) : BaseEntity {
    @Id
    @JsonSerialize(using = ToStringSerializer::class)
    @Suppress("PropertyName")
    var _id: SnapshotId? = null

    @CreatedBy
    @JsonIgnoreProperties(ignoreUnknown = true)
    var createdBy: String? = null

    @CreatedDate
    @JsonIgnoreProperties(ignoreUnknown = true)
    var createdDate: Date? = null

    @LastModifiedBy
    @JsonIgnoreProperties(ignoreUnknown = true)
    var lastModifiedBy: String? = null

    @LastModifiedDate
    @JsonIgnoreProperties(ignoreUnknown = true)
    var lastModifiedDate: Date? = null

    @CreatedWithSystemVersion
    @JsonIgnoreProperties(ignoreUnknown = true)
    var createdWithSystemVersion: PlatformVersion? = null

    /**
     * This will always be non-null, as after loading a snapshot, a service will fill in.
     * Call projectId() for the compile safe, non-null variant.
     *
     * Setting the [bomNode] will implicitly set this variable to its project Id.
     */
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    lateinit var projectId: ProjectId

    fun hasProjectId() = ::projectId.isInitialized

    override fun getId() = _id!!

    @Transient
    lateinit var bomNodeSnapshot: BomNodeSnapshot
}

@Component
class BomNodeSnapshotAfterConvertCallback(
    private val migrationManager: MigrationManager,
) : ReactiveAfterConvertCallback<PersistedBomNode> {
    override fun onAfterConvert(
        node: PersistedBomNode,
        document: org.bson.Document,
        collection: String,
    ): Publisher<PersistedBomNode> =
        Mono.fromCallable {
            if (collection == BomNodeSnapshot.COLLECTION_NAME) {
                node.apply {
                    lastMigrationChangeSetId = migrationManager.getLatestChangeSetId()
                }
            } else {
                node
            }
        }
}
