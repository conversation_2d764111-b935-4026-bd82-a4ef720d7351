package com.nu.bom.core.smf.model

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import com.nu.bom.core.exception.userException.WsiOpticsErrorType
import com.nu.bom.core.exception.userException.WsopticsException
import com.nu.bom.core.manufacturing.fieldTypes.NoCalcData
import com.nu.bom.core.smf.FriedelException
import com.nu.bom.core.turn.model.pointDistance
import com.nu.bom.core.utils.Either
import com.nu.bom.core.utils.TAU
import kotlin.math.abs
import kotlin.math.acos
import kotlin.math.pow
import kotlin.math.sin

@JsonInclude(JsonInclude.Include.ALWAYS)
class NestorRequest(
    val options: FriedelNestingOption,
    val sheet: FriedelNestingSheet,
    @JsonProperty("closed_curve_to_nest")
    val closedCurveToNest: FriedelNestingCurve,
    @JsonProperty("closed_curve_to_visualize_instead")
    val closedCurveToVisualizeInstead: FriedelNestingCurve?,
)

data class FriedelNestingOption(
    val mode: FriedelNestingMode,
    @JsonProperty("distance_between_parts_mm")
    val distanceBetweenPartsMm: Double,
    @JsonProperty("distance_to_sheet_edge_mm")
    val distanceToSheetEdgeMm: Double,
)

@JsonTypeName("Mode")
enum class FriedelNestingMode {
    Default,
    SingleRotation,
    MultiBurner,
    CardinalDirections,
    Restricted,
}

@JsonTypeName("Sheet")
data class FriedelNestingSheet(
    @JsonProperty("length_mm")
    val lengthMm: Double,
    @JsonProperty("width_mm")
    val widthMm: Double,
)

data class FriedelNestingCurve(
    val segments: List<FriedelNestingSegment>,
) : NoCalcData {
    fun areaMm2(): Double = segments.sumOf(FriedelNestingSegment::signedVolumeBelow)
}

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "@type")
sealed class FriedelNestingSegment {
    abstract fun toSmfComponent(): SmfUnfoldedPartComponent

    fun signedVolumeBelow(): Double = toSmfComponent().getSignedVolumeBelow()
}

@JsonTypeName("line")
data class FriedelNestingLine(
    val start: FriedelNestingCoord,
    val end: FriedelNestingCoord,
) : FriedelNestingSegment() {
    override fun toSmfComponent(): SmfUnfoldedPartComponent =
        SmfUnfoldedPartComponentLine(
            content =
                SmfUnfoldedPartLineContent(
                    from = SmfUnfoldedPartPoint(start.xMm, start.yMm),
                    to = SmfUnfoldedPartPoint(end.xMm, end.yMm),
                ),
        )
}

@JsonTypeName("arc")
data class FriedelNestingArc(
    val start: FriedelNestingCoord,
    val end: FriedelNestingCoord,
    val center: FriedelNestingCoord,
    @get:JsonProperty("is_ccw")
    val isCcw: Boolean,
) : FriedelNestingSegment() {
    override fun toSmfComponent(): SmfUnfoldedPartComponent =
        SmfUnfoldedPartComponentArc(
            content =
                SmfUnfoldedPartArcContent(
                    from = SmfUnfoldedPartPoint(start.xMm, start.yMm),
                    to = SmfUnfoldedPartPoint(end.xMm, end.yMm),
                    center = SmfUnfoldedPartPoint(center.xMm, center.yMm),
                    ccw = isCcw,
                ),
        )
}

@JsonTypeName("Coord")
data class FriedelNestingCoord(
    @get:JsonProperty("x_mm")
    val xMm: Double,
    @get:JsonProperty("y_mm")
    val yMm: Double,
)

data class WsiUnfolderRequest(
    val stepContent: String,
    val enforceSheetPart: Boolean,
)

data class WsiStepToGltfRequest(
    val stepContent: String,
)

data class WsiStepToGltfResponse(
    val tag: String,
    val glbBase64: String,
) : WsiStepToGltfResponseOrError(),
    NoCalcData {
    @JsonIgnore
    override fun getResponseOrError(): Either<WsiStepToGltfErrorResponse, WsiStepToGltfResponse> = Either.second(this)
}

@JsonTypeInfo(use = JsonTypeInfo.Id.DEDUCTION)
sealed class WsiStepToGltfResponseOrError : ResponseOrError<WsiStepToGltfResponse, WsiStepToGltfErrorResponse>

data class WsiStepToGltfErrorCause(
    val loc: List<String>,
    val msg: String,
    val type: String,
)

data class WsiStepToGltfErrorResponse(
    val detail: List<WsiStepToGltfErrorCause>,
    val errorType: String,
) : WsiStepToGltfResponseOrError(),
    ExceptionWrapper {
    @JsonIgnore
    override fun getResponseOrError(): Either<WsiStepToGltfErrorResponse, WsiStepToGltfResponse> = Either.first(this)

    override fun toException(): Exception = Exception("StepToGltf returned errorType '$errorType'.")
}

interface ExceptionWrapper {
    fun toException(): Exception
}

interface ResponseOrError<Response, Error : ExceptionWrapper> {
    fun getResponseOrError(): Either<Error, Response>
}

class Transformation(
    val origin: SmfUnfoldedPartPoint,
    val unitaryMatrix: SmfMatrix,
)

class SmfMatrix(
    val entries: List<Double>,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class NestorResponse(
    val tag: String? = null,
    val svg: String? = null,
    val transformations: List<Transformation>? = null,
    @JsonProperty("nested_parts")
    val nestedParts: Int? = null,
    @JsonProperty("grid_size")
    val gridSize: GridSize? = null,
) : NoCalcData

data class GridSize(
    val x: Int,
    val y: Int,
)

@JsonTypeInfo(use = JsonTypeInfo.Id.DEDUCTION)
sealed class WsiUnfolderResponseOrError : ResponseOrError<WsiUnfolderResponse, WsiUnfolderErrorResponse>

data class NestorErrorResponse(
    val kind: String,
    val message: String,
) : ExceptionWrapper {
    override fun toException(): Exception = FriedelException(kind, message)
}

data class WsiUnfolderErrorResponse(
    val tag: String,
    val errorType: String,
) : WsiUnfolderResponseOrError(),
    ExceptionWrapper {
    @JsonIgnore
    override fun getResponseOrError(): Either<WsiUnfolderErrorResponse, WsiUnfolderResponse> = Either.first(this)

    override fun toException(): Exception =
        WsopticsException(
            errorType = WsiOpticsErrorType.UNFOLDING_FAILED,
            fallbackMessage = "Unfolder returned error tag '$tag' with errorType '$errorType'.",
        )
}

data class WsiUnfolderResponse(
    val tag: String,
    val sheetPartEnforced: Boolean,
    val innerOuterPolygons: List<SmfUnfoldedPart>,
    val bendLines: List<SmfUnfoldedPartBendLine>,
    // We ignore the returned sheetThickness, because we want to use the manual user input in all cases anyway!
    val twoDimSvg: String,
    val boundingBox2: SmfUnfoldedBoundingBox2Content?,
    val boundingBox3: SmfUnfoldedBoundingBox3Content?,
) : WsiUnfolderResponseOrError(),
    NoCalcData {
    init {
        val numberOfInnerOuterPolygons = innerOuterPolygons.size
        if (numberOfInnerOuterPolygons != 1) {
            throw WsopticsException(
                errorType = WsiOpticsErrorType.UNFOLDING_MULTIPLE_PARTS,
                fallbackMessage = "Unfolder returned $numberOfInnerOuterPolygons parts, but there should be exactly one",
            )
        }
    }

    @JsonIgnore
    override fun getResponseOrError(): Either<WsiUnfolderErrorResponse, WsiUnfolderResponse> = Either.second(this)

    val innerOuterPolygon get() = innerOuterPolygons.single()

    fun getPartLength() = boundingBox3?.getLength() ?: 0.0

    fun getPartWidth() = boundingBox3?.getWidth() ?: 0.0

    fun getPartHeight() = boundingBox3?.getHeight() ?: 0.0
}

fun SmfUnfoldedPartPolygon.areaMm2(): Double = sumOf(SmfUnfoldedPartComponent::getSignedVolumeBelow)

fun SmfUnfoldedPart.areaMm2(): Double = outerPolygon.areaMm2() + innerPolygons.sumOf(SmfUnfoldedPartPolygon::areaMm2)

data class SmfUnfoldedPartBendLine(
    val bendAngle: Double,
    val bendDescriptor: Int,
    val innerRadius: Double,
    // hopefully just lines …
    val segments: List<SmfUnfoldedPartComponent>,
)

fun SmfUnfoldedPartBendLine.getPoints(): List<SmfUnfoldedPartPoint> {
    val lines = segments.map { (it as SmfUnfoldedPartComponentLine).content }
    return lines.map { it.from } + lines.map { it.to }
}

fun SmfUnfoldedPartBendLine.totalLength(): Double {
    val points = getPoints()

    if (points.isEmpty()) {
        return 0.0
    }

    fun getFarthestFrom(p: SmfUnfoldedPartPoint) = points.maxByOrNull { pointDistance(p, it) }!!

    val randomPoint = points.first()
    val extremePointA = getFarthestFrom(randomPoint)
    val extremePointB = getFarthestFrom(extremePointA)
    return pointDistance(extremePointA, extremePointB)
}

data class SmfUnfoldedPart(
    val outerPolygon: SmfUnfoldedPartPolygon,
    val innerPolygons: List<SmfUnfoldedPartPolygon>,
) {
    init {
        if (!isValidPolygon(outerPolygon)) {
            throw Exception("Outer polygon not closed.")
        }

        innerPolygons.forEachIndexed { i, poly ->
            if (!isValidPolygon(poly)) {
                throw Exception("Inner polygon $i not closed.")
            }
        }
    }
}

typealias SmfUnfoldedPartPolygon = List<SmfUnfoldedPartComponent>

fun isValidPolygon(poly: SmfUnfoldedPartPolygon): Boolean {
    val corners = mutableMapOf<SmfUnfoldedPartPoint, Int>().withDefault { 0 }

    poly.map { component ->
        val from = component.from()
        val to = component.to()
        corners[from] = corners.getValue(from) + 1
        corners[to] = corners.getValue(to) - 1
    }

    return corners.all { (_, i) -> i == 0 }
}

fun SmfUnfoldedPartPolygon.getLength() = this.sumOf { it.getLength() }

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
sealed class SmfUnfoldedPartComponent {
    abstract fun getLength(): Double

    abstract fun from(): SmfUnfoldedPartPoint

    abstract fun to(): SmfUnfoldedPartPoint

    abstract fun getSignedVolumeBelow(): Double

    abstract fun toFriedel(): FriedelNestingSegment
}

@JsonTypeName("line")
data class SmfUnfoldedPartComponentLine(
    val content: SmfUnfoldedPartLineContent,
) : SmfUnfoldedPartComponent() {
    override fun getLength() = pointDistance(content.from, content.to)

    override fun getSignedVolumeBelow() = signedVolumeBelow(content.from, content.to)

    override fun toFriedel(): FriedelNestingSegment =
        FriedelNestingLine(
            start =
                FriedelNestingCoord(
                    content.from.x,
                    content.from.y,
                ),
            end =
                FriedelNestingCoord(
                    content.to.x,
                    content.to.y,
                ),
        )

    override fun from(): SmfUnfoldedPartPoint = content.from

    override fun to(): SmfUnfoldedPartPoint = content.to
}

@JsonTypeName("arc")
data class SmfUnfoldedPartComponentArc(
    val content: SmfUnfoldedPartArcContent,
) : SmfUnfoldedPartComponent() {
    val radius: Double get() = content.radius

    override fun from(): SmfUnfoldedPartPoint = content.from

    override fun to(): SmfUnfoldedPartPoint = content.to

    private fun angle(): Double {
        val a = getDirection(content.center, content.from)
        val b = getDirection(content.center, content.to)

        val cosAlpha = (innerProduct(a, b) / radius.pow(2)).coerceIn(-1.0, 1.0)
        val alpha = acos(cosAlpha)

        val cross = cross(a, b)

        if (alpha.isNaN()) {
            throw Error("NaN with radius $radius, and content $content.")
        }

        return if ((abs(cross) < 0.000001) || (cross > 0.0) != content.ccw) TAU - alpha else alpha
    }

    private fun signedAngle() =
        angle().let {
            if (content.ccw) {
                it
            } else {
                -it
            }
        }

    override fun getLength() = radius * angle()

    override fun getSignedVolumeBelow(): Double {
        val angle = signedAngle()
        return 0.5 * radius.pow(2) * (angle - sin(angle)) + signedVolumeBelow(content.from, content.to)
    }

    override fun toFriedel(): FriedelNestingSegment =
        FriedelNestingArc(
            start =
                FriedelNestingCoord(
                    content.from.x,
                    content.from.y,
                ),
            end =
                FriedelNestingCoord(
                    content.to.x,
                    content.to.y,
                ),
            isCcw = content.ccw,
            center =
                FriedelNestingCoord(
                    content.center.x,
                    content.center.y,
                ),
        )
}

data class SmfUnfoldedPartLineContent(
    val from: SmfUnfoldedPartPoint,
    val to: SmfUnfoldedPartPoint,
)

data class SmfUnfoldedPartArcContent(
    val from: SmfUnfoldedPartPoint,
    val to: SmfUnfoldedPartPoint,
    val center: SmfUnfoldedPartPoint,
    val ccw: Boolean,
) {
    val radius: Double get() = (pointDistance(center, from) + pointDistance(center, to)) / 2.0
}

data class SmfUnfoldedPartPoint(
    val entries: List<Double>,
) {
    constructor(x: Double, y: Double) : this(listOf(x, y))

    val x get() = entries[0]
    val y get() = entries[1]
}

fun getLength(a: SmfUnfoldedPartPoint) =
    kotlin.math.sqrt(
        (a.x).pow(2) + (a.y).pow(2),
    )

fun getDirection(
    from: SmfUnfoldedPartPoint,
    to: SmfUnfoldedPartPoint,
) = SmfUnfoldedPartPoint(
    to.x - from.x,
    to.y - from.y,
)

fun getSum(
    a: SmfUnfoldedPartPoint,
    b: SmfUnfoldedPartPoint,
) = SmfUnfoldedPartPoint(
    a.x + b.x,
    a.y + b.y,
)

fun signedVolumeBelow(
    from: SmfUnfoldedPartPoint,
    to: SmfUnfoldedPartPoint,
) = cross(from, to) / 2.0

fun getDirection(line: SmfUnfoldedPartComponentLine) = getDirection(line.content.from, line.content.to)

fun innerProduct(
    a: SmfUnfoldedPartPoint,
    b: SmfUnfoldedPartPoint,
) = a.x * b.x + a.y * b.y

fun cross(
    a: SmfUnfoldedPartPoint,
    b: SmfUnfoldedPartPoint,
) = a.x * b.y - b.x * a.y

fun getNormalized(a: SmfUnfoldedPartPoint): SmfUnfoldedPartPoint {
    val length = getLength(a)
    return SmfUnfoldedPartPoint(a.x / length, a.y / length)
}

data class SmfUnfoldedPartPoint3D(
    val entries: List<Double>,
) {
    constructor(x: Double, y: Double, z: Double) : this(listOf(x, y, z))

    val x get() = entries[0]
    val y get() = entries[1]
    val z get() = entries[2]
}

data class SmfUnfoldedBoundingBox2Content(
    val upper: SmfUnfoldedPartPoint,
    val lower: SmfUnfoldedPartPoint,
)

data class SmfUnfoldedBoundingBox3Content(
    val upper: SmfUnfoldedPartPoint3D,
    val lower: SmfUnfoldedPartPoint3D,
) {
    fun getLength() = upper.x - lower.x

    fun getWidth() = upper.y - lower.y

    fun getHeight() = upper.z - lower.z
}
