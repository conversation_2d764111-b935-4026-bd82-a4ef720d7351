package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import org.springframework.stereotype.Service

@Service
class ManufacturingStepLargeCastingMigration : ManufacturingModelEntityMapper {
    override val changeSetId = MigrationChangeSetId("2024-06-10-largecasting-migration")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity =
        entity.copyAll(
            entityRef = adaptEntityRef(entity.entityRef),
        )

    private fun adaptEntityRef(ref: String?): String? =
        if (ref == "Large casting") {
            "ManufacturingStepSandCasting"
        } else {
            ref
        }
}
