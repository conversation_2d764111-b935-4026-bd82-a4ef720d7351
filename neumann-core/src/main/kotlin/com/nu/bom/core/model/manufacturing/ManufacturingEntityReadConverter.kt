package com.nu.bom.core.model.manufacturing

import com.google.common.annotations.VisibleForTesting
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.factories.FieldFactoryService
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.FieldWithResult
import com.nu.bom.core.manufacturing.fieldTypes.InputDependency
import com.nu.bom.core.manufacturing.fieldTypes.TypeUnits
import com.nu.bom.core.manufacturing.service.FieldKey
import com.nu.bom.core.manufacturing.service.asEntityClassName
import com.nu.bom.core.model.manufacturing.SnapshotDocumentHelper.Companion.isLegacySnapshotModel
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.InputDependencyModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.model.manufacturing.persistentModel.RootPersistedManufacturingModel
import com.nu.bom.core.model.manufacturing.schema.persistence.PersistedBomNodeSchemaService
import com.nu.bom.core.service.ManufacturingEntityFactoryService
import com.nu.bom.core.service.migration.lazy.MigrationMapperService
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.changelog.EntityHash
import com.nu.bom.core.utils.traverseTo
import org.apache.commons.codec.digest.DigestUtils
import org.bson.Document
import org.slf4j.LoggerFactory
import org.springframework.data.mongodb.core.convert.MappingMongoConverter
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono

@Service
class ManufacturingEntityReadConverter(
    private val mappingMongoConverter: MappingMongoConverter,
    private val entityFactoryService: ManufacturingEntityFactoryService,
    private val fieldFactoryService: FieldFactoryService,
    private val migrationMapperService: MigrationMapperService,
    private val persistedBomNodeSchemaService: PersistedBomNodeSchemaService,
    private val entityManager: EntityManager,
) {
    companion object {
        private val LOG = LoggerFactory.getLogger(ManufacturingEntityReadConverter::class.java)
    }

    private val fallbackHashesForEntities by lazy {
        entityManager
            .getEntityKClasses()
            .associateWith { EntityHash(DigestUtils.sha256Hex(it.java.name.toByteArray())) }
    }

    fun convert(source: Document): Mono<ManufacturingEntity> {
        val modelEntity =
            if (isLegacySnapshotModel(source)) {
                mappingMongoConverter.read(ManufacturingModelEntity::class.java, source).toMono()
            } else {
                val rootPersistedManufacturingModel = mappingMongoConverter.read(RootPersistedManufacturingModel::class.java, source)
                rootPersistedManufacturingModel.toManufacturingModelEntity(persistedBomNodeSchemaService)
            }

        // we need to log and rethrow because the Mongo Converter shadows conversion errors
        return try {
            modelEntity.flatMap { toManufacturingEntity(it) }
        } catch (t: Throwable) {
            LOG.error("Serializing manufacturing entity failed: ${t.message}", t)
            throw t
        }
    }

    @VisibleForTesting
    fun toManufacturingEntity(modelEntity: ManufacturingModelEntity): Mono<ManufacturingEntity> {
        val hashMigratedModelEntity =
            if (modelEntity.extensionHashes.isNullOrEmpty()) {
                prefillExtensionHashes(modelEntity)
            } else {
                modelEntity
            }
        return migrationMapperService.applyMigrationChangeSets(hashMigratedModelEntity).flatMap { convertToEntity(it) }
    }

    /**
     * if we do not have any historical extension hashes we are going to prefill them with how the world currently looks like
     * it might not be 100% correct, but it is the best we know.
     */
    private fun prefillExtensionHashes(modelEntity: ManufacturingModelEntity): ManufacturingModelEntity =
        modelEntity.apply {
            extensionHashes =
                run {
                    entityFactoryService
                        .createEntity(
                            name = modelEntity.name,
                            clazz = modelEntity.clazz.asEntityClassName(),
                            entityType = Entities.valueOf(modelEntity.type),
                            args = modelEntity.args,
                        ).apply {
                            setVersionHashes(
                                entityManager,
                                fallbackHashesForEntities,
                            )
                        }.getAllHashesToSave()
                        .associate { it.toPersistentRepresentation() }
                }
        }

    private fun convertToEntity(hashMigratedModelEntity: ManufacturingModelEntity): Mono<ManufacturingEntity> =
        hashMigratedModelEntity.children.traverseTo(mutableListOf()) { toManufacturingEntity(it) }.map { convertedChildren ->
            entityFactoryService
                .createEntity(
                    name = hashMigratedModelEntity.name,
                    clazz = hashMigratedModelEntity.clazz.asEntityClassName(),
                    entityType = Entities.valueOf(hashMigratedModelEntity.type),
                    args = hashMigratedModelEntity.args,
                ).apply {
                    _id = hashMigratedModelEntity.id

                    version = hashMigratedModelEntity.version
                    initVersion = hashMigratedModelEntity.initVersion
                    previouslyCalculatedVersionHash =
                        hashMigratedModelEntity.versionHash?.let { EntityHash(it) } ?: previouslyCalculatedVersionHash

                    val internalRepresentation =
                        hashMigratedModelEntity.extensionHashes
                            ?.mapNotNull { (className, hash) ->
                                val kClass = entityManager.getKClassOfEntityOrExtensionOrNull(className)
                                kClass?.let { Pair(kClass, EntityHash(hash)) }
                            }?.toMap()
                    setVersionHashes(savedHashes = internalRepresentation)

                    entityRef = hashMigratedModelEntity.entityRef

                    createdBy = hashMigratedModelEntity.createdBy
                    createdByMocked = hashMigratedModelEntity.createdByMocked
                    createdOnBranch = hashMigratedModelEntity.createdOnBranch
                    providerField = hashMigratedModelEntity.providerField
                    dirtyChildLoading = hashMigratedModelEntity.dirtyChildLoading

                    masterDataSelector = hashMigratedModelEntity.masterDataSelector
                    masterDataObjectId = hashMigratedModelEntity.masterDataObjectId
                    masterDataVersion = hashMigratedModelEntity.masterDataVersion

                    lastMigrationChangeSetId = hashMigratedModelEntity.lastMigrationChangeSetId

                    children = convertedChildren

                    val entityId = entityId
                    val entityRef = entityRefForFieldKey()
                    val entityType = getEntityType()
                    initialFieldsWithResults =
                        hashMigratedModelEntity.initialFieldWithResults
                            .mapTo(mutableListOf()) {
                                fromFieldWithResultModel(entityId, entityRef, entityType, it)
                            }
                    fieldWithResults =
                        hashMigratedModelEntity.fieldWithResults
                            .mapTo(mutableListOf()) {
                                fromFieldWithResultModel(entityId, entityRef, entityType, it)
                            }

                    isolated = (hashMigratedModelEntity.isolated != null && hashMigratedModelEntity.isolated)
                    if (hashMigratedModelEntity.dynamicFields != null) {
                        dynamicFields =
                            hashMigratedModelEntity.dynamicFields
                                .associateByTo(
                                    mutableMapOf(),
                                    { it.name },
                                    { ManufacturingEntity.DynamicField(it.entityClass, it.nameInEntity()) },
                                )
                    }
                }
        }

    private fun fromFieldWithResultModel(
        entityId: String,
        entityRef: String,
        entityType: String,
        fieldEntry: Map.Entry<String, FieldResultModel>,
    ): FieldWithResult {
        val fieldModel = fieldEntry.value
        val fieldResult = toFieldResult(fieldModel)

        return FieldWithResult(
            fieldResult,
            FieldKey(
                name = fieldEntry.key,
                entityId = entityId,
                entityRef = entityRef,
                entityType = entityType,
                type = fieldResult::class.qualifiedName!!,
                version = fieldModel.version,
                newVersion = fieldModel.newVersion,
            ),
        )
    }

    fun toFieldResult(fieldResultModel: FieldResultModel): FieldResultStar {
        val denominator =
            fieldResultModel.denominator?.let {
                TypeUnits.valueOf(it.dimension).getTypeUnit(it.unit)
            }
        val numerator =
            fieldResultModel.numerator?.let {
                TypeUnits.valueOf(it.dimension).getTypeUnit(it.unit)
            }

        return fieldFactoryService
            .parseField(
                type = fieldResultModel.type,
                value = fieldResultModel.value,
                unit = fieldResultModel.unit,
                systemValue = fieldResultModel.systemValue,
                denominatorUnit = null,
                // I assume this is correct since FieldResultModel is a backend type and doesn't bother with denominatorUnits.
                currencyIsoCode = null,
                denominator = denominator,
                numerator = numerator,
                defaultValue = fieldResultModel.defaultValue,
            ).apply {
                this.source = FieldResult.SOURCE.valueOf(fieldResultModel.source)
                this.inputs = mapInputDependencies(fieldResultModel.inputs)
            }
    }

    private fun mapInputDependencies(inputs: Set<InputDependencyModel>?): Set<InputDependency> {
        // Intentionally verbose as a micro-optimization to ensure we don't unnecessarily allocate new mutable sets.
        if (inputs != null) {
            return inputs.mapTo(mutableSetOf()) {
                InputDependency(
                    it.name,
                    it.entityId,
                    it.version,
                )
            }
        }
        return emptySet()
    }
}
