package com.nu.bom.core.api.dtos

import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.nu.bom.core.model.Part
import java.time.Instant

data class PartDto(
    val id: String?,
    val designation: String,
    val number: String,
    val images: List<String> = emptyList(),
    @JsonSerialize()
    val createdDate: Instant?,
    val lastModifiedDate: Instant?,
) {
    companion object {
        fun from(part: Part): PartDto =
            PartDto(
                id = part._id?.toHexString(),
                designation = part.designation,
                number = part.number,
                images = part.images,
                createdDate = part.createdDate?.toInstant(),
                lastModifiedDate = part.lastModifiedDate?.toInstant(),
            )
    }
}
