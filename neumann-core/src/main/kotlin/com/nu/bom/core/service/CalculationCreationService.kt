package com.nu.bom.core.service

import com.nu.bom.core.api.dtos.CreateManufacturingResult
import com.nu.bom.core.api.dtos.FieldConversionService
import com.nu.bom.core.exception.userException.ManufacturingEntityNotFoundException
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.BomEntry
import com.nu.bom.core.manufacturing.entities.ExternalBomEntry
import com.nu.bom.core.manufacturing.entities.ExternalManufacturingStep
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.service.asEntityClass
import com.nu.bom.core.manufacturing.utils.ManufacturingTreeUtils
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.EntityCreation
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.user.AccessCheckProvider
import com.tset.core.module.bom.exchangerates.ExchangeRateMap
import com.tset.core.service.domain.Currency
import org.bson.types.ObjectId
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono
import java.time.LocalDate
import kotlin.reflect.KClass

@Service
class CalculationCreationService(
    private val projectService: ProjectService,
    private val manufacturingCreationService: ManufacturingCreationService,
    private val manufacturingCalculationService: ManufacturingCalculationService,
    private val manufacturingEntityFactoryService: ManufacturingEntityFactoryService,
    private val fieldConversionService: FieldConversionService,
) {
    companion object {
        private val LOG = LoggerFactory.getLogger(CalculationCreationService::class.java)

        private fun defaultYear(): Int = LocalDate.now().year
    }

    fun createStandardRootCalculation(
        projectId: ProjectId,
        title: String,
        fields: Map<String, FieldResultStar>,
        clazz: KClass<out BaseManufacturing>,
        ccy: List<Currency>?,
    ): Mono<CreateManufacturingResult> {
        return AccessCheckProvider.getCurrentAccessCheck().flatMap { accessCheck ->
            projectService.getProject(accessCheck, projectId).flatMap { projectNumber ->
                manufacturingCreationService.create(
                    accessCheck = accessCheck,
                    type = clazz,
                    name = clazz.simpleName!!,
                    args =
                        ManufacturingCreationService.standardArgs(
                            accessCheck,
                            projectNumber,
                            title,
                        ),
                    fields = fields,
                    projectId = projectNumber.id,
                    year = defaultYear(),
                    title = title,
                )
            }.flatMap { calculationResult ->
                ManufacturingCalculationService.toCreateManufacturingResult(
                    accessCheck,
                    calculationResult,
                    exchangeRatesMap = calculationResult.newRootSnapshot.manufacturing?.getExchangeRateMap(ccy) ?: ExchangeRateMap.empty(),
                    fieldConversionService = fieldConversionService,
                )
            }
        }
    }

    fun createStandardSubCalculation(
        projectId: ProjectId,
        title: String,
        fields: Map<String, FieldResultStar>,
        bomNodeId: BomNodeId,
        branchId: BranchId?,
        stepId: ObjectId,
        bomEntryFields: Map<String, FieldResultStar>,
        path: List<String>,
        clazz: KClass<out BaseManufacturing>,
        ccy: List<Currency>?,
    ): Mono<CreateManufacturingResult> {
        // TODO: validate required fields here

        return AccessCheckProvider.getCurrentAccessCheck().flatMap { accessCheck ->

            projectService.getProject(accessCheck, projectId).flatMap { project ->
                val currentUser = accessCheck.userId

                val trigger =
                    EntityCreation(
                        bomNodeId = bomNodeId,
                        entityType = Entities.MANUFACTURING.name,
                        parentId = stepId,
                    )

                LOG.info("calling transformAndCalculate: $bomNodeId, triggerAction= $trigger in branch: $branchId")

                manufacturingCalculationService.transformAndCalculate(
                    accessCheck,
                    bomNodeId,
                    triggerAction = trigger,
                    branchId = branchId,
                ) { manufacturing, _, _ ->

                    val parentStep =
                        ManufacturingTreeUtils.findEntityById(searchRoot = manufacturing, id = stepId)
                            ?: throw ManufacturingEntityNotFoundException(
                                stepId.toHexString(),
                                manufacturing.bomNodeId?.toHexString(),
                            )

                    val subMan =
                        manufacturingEntityFactoryService.createEntity(
                            name = clazz.simpleName!!,
                            entityType = Entities.MANUFACTURING,
                            clazz = clazz.asEntityClass(),
                            args =
                                hashMapOf(
                                    "key" to project.key,
                                    "user" to currentUser,
                                    "title" to title,
                                    // TODO kw there is now no longer any part data
                                    // Can we get it from fields...?
                                    // TODO kw maybe this matters - sometimes "isPart" is set to false...
                                    // What effects does it have?
                                    // "isPart" to true,
                                    // "isPartModelConverted" to true,
                                ),
                            fields = fields,
                        )

                    // Update the trigger with the created entity id
                    trigger.ext =
                        EntityCreation.Ext(
                            entityId = subMan._id,
                            entityName = subMan.name,
                            entityClass = Entities.MANUFACTURING.name,
                            displayName = subMan.displayName,
                        )

                    val bomEntryClass =
                        if (parentStep is ExternalManufacturingStep) ExternalBomEntry::class else BomEntry::class

                    val bomEntry =
                        manufacturingEntityFactoryService.createEntity(
                            name = bomEntryClass.simpleName!!,
                            clazz = bomEntryClass.asEntityClass(),
                            entityType = Entities.BOM_ENTRY,
                            fields = bomEntryFields,
                        )

                    bomEntry.addChild(subMan)
                    parentStep.addChild(bomEntry)

                    manufacturing.toMono()
                }
            }.flatMap { calculationResult ->
                ManufacturingCalculationService.toCreateManufacturingResult(
                    accessCheck,
                    calculationResult,
                    path,
                    calculationResult.newRootSnapshot.manufacturing?.getExchangeRateMap(ccy) ?: ExchangeRateMap.empty(),
                    fieldConversionService,
                )
            }
        }
    }
}
