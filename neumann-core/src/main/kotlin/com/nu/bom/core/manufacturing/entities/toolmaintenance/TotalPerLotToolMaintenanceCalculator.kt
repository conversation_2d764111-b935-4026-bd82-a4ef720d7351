package com.nu.bom.core.manufacturing.entities.toolmaintenance

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.TimeInYears

@EntityType(Entities.NONE)
class TotalPerLotToolMaintenanceCalculator(
    name: String,
) : ManufacturingEntity(name) {
    fun maintenanceCostPerLot(): Money = Money.ZERO

    fun maintenanceCostOverLifetime(
        @Parent(Entities.MANUFACTURING)
        lifeTime: TimeInYears,
        @Parent(Entities.MANUFACTURING)
        callsPerYear: Num,
        maintenanceCostPerLot: Money,
    ): Money = maintenanceCostPerLot * lifeTime * callsPerYear
}
