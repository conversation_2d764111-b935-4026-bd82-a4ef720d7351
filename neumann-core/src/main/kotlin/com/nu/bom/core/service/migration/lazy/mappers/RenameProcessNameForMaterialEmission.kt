package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import org.springframework.stereotype.Service

@Service
class RenameProcessNameForMaterialEmission : ManufacturingModelEntityMapper {
    override val changeSetId = MigrationChangeSetId("2022-11-01-rename-process-name-material-emission")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity =
        entity.copy(
            initialFieldWithResults = migrateProcessField(entity.initialFieldWithResults),
            fieldWithResults = migrateProcessField(entity.fieldWithResults),
        )

    private fun migrateProcessField(fields: Map<String, FieldResultModel>): Map<String, FieldResultModel> =
        fields.mapValues { (fieldName, fieldResult) ->
            if (fieldName == "process" && fieldResult.value == "Process") {
                fieldResult.copy(value = "Extrusion")
            } else {
                fieldResult
            }
        }
}
