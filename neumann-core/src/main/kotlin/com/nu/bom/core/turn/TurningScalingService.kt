package com.nu.bom.core.turn

import com.nu.bom.core.exception.userException.InvalidSketchScalingException
import com.nu.bom.core.model.TurningProfile
import com.nu.bom.core.turn.model.FrontEndLineAttributes
import com.nu.bom.core.turn.model.Point
import com.nu.bom.core.utils.SketchScaling
import org.springframework.stereotype.Service
import kotlin.math.roundToInt

@Service
class TurningScalingService {
    companion object {
        // if scaling fails, return original sketch with a message explaining that scaling failed.
        fun maybeScaleSketch(
            sketch: TurningProfile.FrontendSketch,
            lengthMillimeter: Double,
            innerDiameterMillimeter: Double,
            outerDiameterMillimeter: Double,
        ): TurningProfile.FrontendSketch =
            scaleSketchEnsuringIdempotency(
                sketch = sketch,
                lengthMillimeter = lengthMillimeter,
                innerDiameterMillimeter = innerDiameterMillimeter,
                outerDiameterMillimeter = outerDiameterMillimeter,
                ::maybeScaleSketchImpl,
            )

        fun scaleSketch(
            sketch: TurningProfile.FrontendSketch,
            partProperties: TurningPartProperties,
        ): TurningProfile.FrontendSketch =
            scaleSketchEnsuringIdempotency(
                sketch = sketch,
                lengthMillimeter = partProperties.length.inMillimeter.toDouble(),
                innerDiameterMillimeter = partProperties.dmin.inMillimeter.toDouble(),
                outerDiameterMillimeter = partProperties.dmax.inMillimeter.toDouble(),
                ::scaleSketch,
            )

        private fun scaleSketchEnsuringIdempotency(
            sketch: TurningProfile.FrontendSketch,
            lengthMillimeter: Double,
            innerDiameterMillimeter: Double,
            outerDiameterMillimeter: Double,
            scalingFunction: (TurningProfile.FrontendSketch, Double, Double, Double) -> TurningProfile.FrontendSketch,
        ): TurningProfile.FrontendSketch {
            val scaledOnce = scalingFunction(sketch, lengthMillimeter, innerDiameterMillimeter, outerDiameterMillimeter)
            val scaledTwice = scalingFunction(scaledOnce, lengthMillimeter, innerDiameterMillimeter, outerDiameterMillimeter)
            require(scaledOnce == scaledTwice) {
                "sketch scaling is not idempotent!"
            }
            return scaledOnce
        }

        private fun maybeScaleSketchImpl(
            sketch: TurningProfile.FrontendSketch,
            lengthMillimeter: Double,
            innerDiameterMillimeter: Double,
            outerDiameterMillimeter: Double,
        ): TurningProfile.FrontendSketch =
            try {
                scaleSketch(
                    sketch,
                    lengthMillimeter,
                    innerDiameterMillimeter,
                    outerDiameterMillimeter,
                )
            } catch (th: Throwable) {
                val unscaledSketch =
                    sketch.copy(
                        scalingException = th.message ?: "Unknown exception occurred during sketch scaling.",
                    )
                unscaledSketch
            }

        private fun scaleSketch(
            sketch: TurningProfile.FrontendSketch,
            lengthMillimeter: Double,
            innerDiameterMillimeter: Double,
            outerDiameterMillimeter: Double,
        ): TurningProfile.FrontendSketch {
            sketch.line_attributes?.let { makeSureThereAreNoGearings(sketch.line_attributes) }

            val bb = sketch.geometry?.let { BoundingBox.fromPoints(it) }

            val lengthInMillimeterRounded = lengthMillimeter.roundToInt().toDouble()
            val innerDiameterMillimeterRounded = innerDiameterMillimeter.roundToInt().toDouble()
            val outerDiameterMillimeterRounded = outerDiameterMillimeter.roundToInt().toDouble()

            val transform =
                computeScalingFactor(
                    bb,
                    lengthInMillimeterRounded,
                    innerRadius = innerDiameterMillimeterRounded,
                    outerRadius = outerDiameterMillimeterRounded,
                )

            val scaledGeometry =
                scaleGeometry(sketch.geometry, transform)?.let { points ->
                    if (hasDuplicatedPoints(points)) {
                        throw InvalidSketchScalingException()
                    }
                    points
                }

            val scaledBb = scaledGeometry?.let { BoundingBox.fromPoints(it) }

            val scaledTransform =
                computeScalingFactor(
                    scaledBb,
                    lengthInMillimeterRounded,
                    innerRadius = innerDiameterMillimeterRounded,
                    outerRadius = outerDiameterMillimeterRounded,
                )

            require(scaledTransform == Transform.IDENTITY) {
                "scaling not idempotent! transform: $scaledTransform"
            }

            return TurningProfile.FrontendSketch(
                geometry = scaledGeometry,
                point_attributes = sketch.point_attributes,
                line_attributes = scaleLineAttributes(sketch.line_attributes, transform.scaleRadius),
                global_attributes = sketch.global_attributes,
                part_properties = sketch.part_properties,
                scalingException = null,
            )
        }

        private data class BoundingBox(
            val minX: Int,
            val maxX: Int,
            val minY: Int,
            val maxY: Int,
        ) {
            companion object {
                fun fromPoints(points: List<Point>): BoundingBox? {
                    if (points.isEmpty()) {
                        return null
                    }
                    return BoundingBox(
                        minX = points.minByOrNull { it.x }!!.x,
                        maxX = points.maxByOrNull { it.x }!!.x,
                        minY = points.minByOrNull { it.y }!!.y,
                        maxY = points.maxByOrNull { it.y }!!.y,
                    )
                }
            }
        }

        private fun computeScalingFactor(
            boundingBox: BoundingBox?,
            length: Double,
            innerRadius: Double,
            outerRadius: Double,
        ): Transform {
            if (boundingBox == null) {
                return Transform.IDENTITY
            }

            val scaleX = length / (boundingBox.maxX - boundingBox.minX)
            val scaleY = (outerRadius - innerRadius) / (boundingBox.maxY - boundingBox.minY)
            val translateX = boundingBox.minX - boundingBox.minX * scaleY
            val translateY = innerRadius - boundingBox.minY * scaleY

            return Transform(
                translationLength = translateX,
                translationRadius = translateY,
                scaleLength = scaleX,
                scaleRadius = scaleY,
            )
        }

        private fun scaleGeometry(
            points: List<Point>?,
            transform: Transform,
        ) = points?.map { point ->
            Point(
                point.id,
                (point.x * transform.scaleLength + transform.translationLength).roundToInt(),
                (point.y * transform.scaleRadius + transform.translationRadius).roundToInt(),
            )
        }

        private fun scaleLineAttributes(
            lineAttributes: List<FrontEndLineAttributes>?,
            scaleRadius: Double,
        ) = lineAttributes?.map { lineAttribute -> scaleLineAttribute(lineAttribute, scaleRadius) }

        private fun scaleLineAttribute(
            lineAttribute: FrontEndLineAttributes,
            scaleRadius: Double,
        ) = lineAttribute.copy(
            line = lineAttribute.line,
            type = lineAttribute.type,
            hardeningType = lineAttribute.hardeningType,
            chd = lineAttribute.chd,
            threadingType = SketchScaling.scaleThreadingTypeFieldParameter(lineAttribute.threadingType, scaleRadius),
            surfaceFinish = lineAttribute.surfaceFinish,
            gearingType = lineAttribute.gearingType,
            toothingType = lineAttribute.toothingType,
            modul = lineAttribute.modul,
            pressureAngle = lineAttribute.pressureAngle,
            finishingType = lineAttribute.finishingType,
            twistFree = lineAttribute.twistFree,
            helixAngle = lineAttribute.helixAngle,
            teethNumber = lineAttribute.teethNumber,
        )

        private fun makeSureThereAreNoGearings(lineAttributes: List<FrontEndLineAttributes>) {
            val hasGearing =
                lineAttributes.any { it.gearingType != null }
            if (hasGearing) {
                throw Throwable("Turning sketches containing gearing line attributes cannot be scaled.")
            }
        }

        private fun hasDuplicatedPoints(points: List<Point>): Boolean = points.map { Pair(it.x, it.y) }.toHashSet().size != points.size

        private data class Transform(
            val translationLength: Double,
            val translationRadius: Double,
            val scaleLength: Double,
            val scaleRadius: Double,
        ) {
            companion object {
                val IDENTITY =
                    Transform(
                        translationLength = 0.0,
                        translationRadius = 0.0,
                        scaleLength = 1.0,
                        scaleRadius = 1.0,
                    )
            }
        }
    }
}
