package com.nu.bom.core.model

import com.nu.bomrads.id.ManufacturingTreeId
import org.bson.types.ObjectId
import java.nio.ByteBuffer
import java.util.UUID
import com.nu.bomrads.id.AccountId as BomradsAccountId
import com.nu.bomrads.id.BomEntryId as BomradsBomEntryId
import com.nu.bomrads.id.BomNodeId as BomradsBomNodeId
import com.nu.bomrads.id.BranchId as BomradsBranchId
import com.nu.bomrads.id.ChangesetId as BomradsChangesetId
import com.nu.bomrads.id.FileUploadId as BomradsFileUploadId
import com.nu.bomrads.id.ImportId as BomradsImportId
import com.nu.bomrads.id.ManufacturingTreeId as BomradsManufacturingTreeId
import com.nu.bomrads.id.ProjectId as BomradsProjectId
import com.nu.bomrads.id.SnapshotId as BomradsSnapshotId

// just to make all the typealias forward compatible - when typealias could be changed to inline classes, this getter can be used
// to retrieve the underlying id.
val ObjectId.id
    get() = this

typealias AccountId = ObjectId
typealias BomNodeId = ObjectId
typealias BomId = ObjectId
typealias BranchId = ObjectId
typealias ChangesetId = ObjectId
typealias ProjectId = ObjectId
typealias WorkspaceId = ObjectId
typealias PartId = ObjectId
typealias SnapshotId = ObjectId
typealias BomNodeChangeId = ObjectId
typealias SimulationId = ObjectId
typealias DeltaComparisonId = ObjectId
typealias TriggerActionId = ObjectId
typealias FileUploadId = ObjectId
typealias NoCalcDataId = ObjectId
typealias MillingProfileId = ObjectId

const val MAIN_BRANCH = "master"

/**
 * Convert the given string to a BranchId, if not null, or return null.
 */
fun createBranchId(str: String?): BranchId? =
    // accept string "master" as valid branchId (null)
    if (str != null && str != MAIN_BRANCH) {
        str.toMongoID()
    } else {
        null
    }

fun ObjectId.toUUID(): UUID {
    val buffer = ByteBuffer.allocate(16)
    this.putToByteBuffer(buffer)
    val first = buffer.getLong(0)
    val second = buffer.getLong(8)
    return UUID(first, second)
}

fun UUID.toObjectId(): ObjectId {
    val buffer = ByteBuffer.allocate(16)
    buffer.putLong(0, this.mostSignificantBits)
    buffer.putLong(8, this.leastSignificantBits)
    return ObjectId(buffer)
}

fun ObjectId.toProjectId(): BomradsProjectId = BomradsProjectId(this.toUUID())

fun ObjectId.toImportId(): BomradsImportId = BomradsImportId(this.toUUID())

fun ObjectId.toBranchId(): BomradsBranchId = BomradsBranchId(this.toUUID())

fun ObjectId.toBomNodeId(): BomradsBomNodeId = BomradsBomNodeId(this.toUUID())

fun SnapshotId.toManufacturingTreeId(): BomradsManufacturingTreeId = BomradsManufacturingTreeId(this.toUUID())

fun ObjectId.toChangeSetId(): BomradsChangesetId = BomradsChangesetId(this.toUUID())

fun ObjectId.toBomEntryId(): BomradsBomEntryId = BomradsBomEntryId(this.toUUID())

fun ObjectId.toFileUploadId(): BomradsFileUploadId = BomradsFileUploadId(this.toUUID())

fun BomradsBomNodeId.toMongoBomNodeId(): BomNodeId = this.id().toObjectId()

fun BomradsBomNodeId.toMongoFormatStr(): String = this.toMongoBomNodeId().toHexString()

fun ManufacturingTreeId.toMongoSnapshotId(): SnapshotId = this.id().toObjectId()

fun ManufacturingTreeId.toMongoFormatStr(): String = this.toMongoSnapshotId().toHexString()

fun BomradsAccountId.toMongoAccountId(): AccountId = this.id().toObjectId()

fun BomradsBranchId.toMongoBranchId(): BranchId = this.id().toObjectId()

fun BomradsSnapshotId.toMongoSnapshotId(): SnapshotId = this.id().toObjectId()

fun BomradsBranchId.toMongoFormatStr(): String = this.toMongoBranchId().toHexString()

fun BomradsProjectId.toMongoProjectId(): ProjectId = this.id().toObjectId()

fun BomradsProjectId.toMongoFormatStr(): String = this.toMongoProjectId().toHexString()

fun BomradsChangesetId.toMongoChangesetId(): ChangesetId = this.id().toObjectId()

fun BomradsChangesetId.toMongoFormatStr(): String = this.toMongoChangesetId().toHexString()

fun BomradsBomEntryId.toMongoObjectId(): ObjectId = this.id().toObjectId()

fun BomradsBomEntryId.toMongoFormatStr(): String = this.toMongoObjectId().toHexString()

/**
 * Convert the string to UUID - either from ObjectID format, or from the genuine UUID format.
 */
fun String.toUUID(): UUID =
    try {
        ObjectId(this).toUUID()
    } catch (ex: Exception) {
        UUID.fromString(this)
    }

/**
 * Convert the string to ObjectID - either from ObjectID format, or from the genuine UUID format.
 */
fun String.toMongoID(): ObjectId =
    try {
        ObjectId(this)
    } catch (ex: Exception) {
        UUID.fromString(this).toObjectId()
    }

// inline class BranchId(val id: ObjectId) {
//    constructor() : this(ObjectId())
//    constructor(id: String) : this(ObjectId(id))
//
//    fun toHexString() = id.toHexString()
//
//    companion object {
//        fun from(str: String?): BranchId? =
//                if (str != null) {
//                    BranchId(ObjectId(str))
//                } else {
//                    null
//                }
//    }
// }

// inline class BomId(val id: ObjectId) {
//    constructor() : this(ObjectId())
//    constructor(id: String) : this(ObjectId(id))
//
//    fun toHexString() = id.toHexString()
//
//    @Deprecated("call toHexString")
//    override fun toString(): String {
//        return id.toString()
//    }
//
// }

// inline class BomNodeId(val id: ObjectId) {
//    constructor() : this(ObjectId())
//    constructor(id: String) : this(ObjectId(id))
//
//    fun toHexString() = id.toHexString()
//    @Deprecated("call toHexString")
//    override fun toString(): String {
//        return id.toString()
//    }
// }

// inline class ChangesetId(val id: ObjectId) {
//    constructor(): this(ObjectId())
//
//    fun toHexString() = id.toHexString()
//
//    @Deprecated("call toHexString")
//    override fun toString(): String {
//        return id.toString()
//    }
// }

// inline class ProjectId(val id: ObjectId) {
//    constructor(id: String): this(ObjectId(id))
//    constructor(): this(ObjectId())
//
//    fun toHexString() = id.toHexString()
//
//    @Deprecated("call toHexString")
//    override fun toString(): String {
//        return id.toString()
//    }
// }

// inline class SnapshotId(val id: ObjectId) {
//    constructor() : this(ObjectId())
//
//    fun toHexString() = id.toHexString()
//
//    @Deprecated("call toHexString")
//    override fun toString(): String {
//        return id.toString()
//    }
//
// }
//
