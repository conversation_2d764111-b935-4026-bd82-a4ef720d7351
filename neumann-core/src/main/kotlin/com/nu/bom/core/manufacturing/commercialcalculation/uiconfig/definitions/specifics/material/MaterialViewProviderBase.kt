package com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.specifics.material

import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostMaterialUsage
import com.nu.bom.core.manufacturing.entities.RawMaterial
import com.nu.bom.core.manufacturing.entities.masterdata.material.MaterialConsumerExtension
import com.nu.bom.core.manufacturing.extension.MaterialDimensionExtension
import com.nu.bom.core.manufacturing.fieldTypes.MaterialViewConfig
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.FieldConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.FieldSectionConfigFeDto

/**
 * Base provider for material view providers in rough-part-technologies.
 */
abstract class MaterialViewProviderBase {
    abstract val materialView: MaterialViewConfig.MaterialView

    abstract val substanceImageName: String

    private val materialImagesFolderName: String = "materialImages"

    private val commonSubstanceLeftFields: List<String> =
        listOf(
            RawMaterial::reuseOfScrap.name,
            RawMaterial::masterDataType.name,
            MaterialDimensionExtension::dimension.name,
            MaterialDimensionExtension::quantityUnit.name,
            MaterialDimensionExtension::costUnit.name,
            RawMaterial::density.name,
            CommercialCalculationCostMaterialUsage::customProcurementType.name,
        )

    // region Substance card (cost)

    open val substanceExtraSectionCost: FieldSectionConfigFeDto?
        get() = null

    private val costSpecificSubstanceLeftFields: List<String> =
        listOf(
            RawMaterial::itemNumber.name,
        )

    private val substanceCardLeftSideCost: List<FieldSectionConfigFeDto> =
        listOf(
            FieldSectionConfigFeDto(commonSubstanceLeftFields + costSpecificSubstanceLeftFields),
        )

    private val substanceCardRightSideCost: List<FieldSectionConfigFeDto>
        get() =
            listOfNotNull(
                FieldSectionConfigFeDto(
                    fieldNames = emptyList(),
                    imageSource = "$materialImagesFolderName/$substanceImageName",
                ),
                substanceExtraSectionCost,
            )

    open val substanceCardCost: FieldConfigFeDto?
        get() = FieldConfigFeDto(left = substanceCardLeftSideCost, right = substanceCardRightSideCost)

    // endregion

    // region Substance card (emission)

    open val substanceExtraSectionEmission: FieldSectionConfigFeDto?
        get() = substanceExtraSectionCost

    private val emissionSpecificSubstanceLeftFields: List<String> =
        listOf(
            MaterialConsumerExtension::location.name,
        )

    private val substanceCardLeftSideEmission: List<FieldSectionConfigFeDto> =
        listOf(
            FieldSectionConfigFeDto(commonSubstanceLeftFields + emissionSpecificSubstanceLeftFields),
        )

    private val substanceCardRightSideEmission: List<FieldSectionConfigFeDto>
        get() =
            listOfNotNull(
                FieldSectionConfigFeDto(
                    fieldNames = emptyList(),
                    imageSource = "$materialImagesFolderName/$substanceImageName",
                ),
                substanceExtraSectionEmission,
            )

    open val substanceCardEmission: FieldConfigFeDto?
        get() = FieldConfigFeDto(left = substanceCardLeftSideEmission, right = substanceCardRightSideEmission)

    // endregion

    // region Material specific card

    abstract val materialSpecificCardCost: FieldConfigFeDto?

    open val materialSpecificCardEmission: FieldConfigFeDto?
        get() = materialSpecificCardCost

    // endregion
}
