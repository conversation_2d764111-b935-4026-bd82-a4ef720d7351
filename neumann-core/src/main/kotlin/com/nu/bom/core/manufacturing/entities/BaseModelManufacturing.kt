package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.exception.readable.IncompatibleTechnologiesException
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.Children
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.FieldIndex
import com.nu.bom.core.manufacturing.annotations.FieldName
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Nocalc
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.RequiredFor
import com.nu.bom.core.manufacturing.annotations.TranslationSection
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.Null
import com.nu.bom.core.manufacturing.fieldTypes.Text
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.util.Locale
import kotlin.reflect.KClass

@EntityType(Entities.MANUFACTURING)
abstract class BaseModelManufacturing(
    name: String,
) : BaseManufacturing(name) {
    // DO NOT IMPLEMENT MORE ENGINE FIELDS IN THIS ENTITY!
    // Fields added over actual inheritance can conflict with ones implemented via the usual
    // extends way, so let's just not do that.

    abstract val model: Model

    /** List of manufacturing steps - in the order they are applied - in this technology */
    abstract val modelSteps: List<KClass<out ManufacturingEntity>>

    fun checkDimension(dimension: Dimension) =
        Null().also {
            if (dimension.res != Dimension.Selection.NUMBER && model != Model.MANUAL) {
                throw Exception("Dimension '${dimension.res}' is not allowed for cost modules.")
            }
        }

    @Input
    @Parent(Entities.MANUFACTURING)
    @FieldIndex(index = 20)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    fun shapeId(): Text? = null

    // Overwritten by ManufacturingRawt, because it has to use the raw model instead of the model for the lookup.
    @Input
    open fun shapeTechnologyGroup(
        shapeId: Text,
        technologyModel: Text,
    ): Mono<Text> {
        val model = Model.fromEntity(technologyModel.res) ?: model
        val modelPath = model.path
        val modelHasShape = model.hasShape
        return if (model == Model.MANUAL) {
            Mono.just(Text("none"))
        } else if (modelPath.isNotEmpty() && modelHasShape) {
            services.getLookupTable(modelPath.uppercase(Locale.getDefault()) + "_Shape") {
                it
            }.onErrorResume {
                Mono.empty()
            }.filter {
                it[0] == shapeId.res
            }.single()
                .map {
                    Text(it[1])
                }
        } else {
            Mono.just(Text(""))
        }
    }

    fun <T> getLookupTable(
        lookUpName: String,
        rowParser: (List<String>) -> T,
    ): Flux<T> = services.getLookupTable(lookUpName, rowParser)

    fun technologyKey(): Text = Text(model.path)

    fun internalTechnologyModel(): Text = Text(model.entity)

    @ReadOnly
    @TranslationSection("models")
    fun technologyModel(
        @FieldName("technologyModel")
        @Children(Entities.MANUFACTURING_STEP, directOnly = false)
        stepTechnologyModel: List<Text>?,
        @FieldName("technologyModel")
        @Children(Entities.MATERIAL, directOnly = false)
        materialTechnologyModel: List<Text>?,
    ): Text {
        if (stepTechnologyModel == null && materialTechnologyModel == null) return Text(model.entity)

        val technologyModel = (materialTechnologyModel ?: emptyList()) + (stepTechnologyModel ?: emptyList())
        if ((technologyModel.distinctBy { it.res }.size) > 1) throw IncompatibleTechnologiesException()
        return technologyModel.first()
    }

    /**
     * Gets shapeId from result fields
     *
     * @return shape ID or null if not available or not found
     */
    @Nocalc
    fun getShapeIdResult() = getFieldResult("shapeId")?.res as? String
}
