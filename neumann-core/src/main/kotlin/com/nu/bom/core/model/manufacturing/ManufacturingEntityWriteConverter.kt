package com.nu.bom.core.model.manufacturing

import com.google.common.annotations.VisibleForTesting
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity.DynamicField
import com.nu.bom.core.manufacturing.fieldTypes.DynamicQuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.FieldWithResult
import com.nu.bom.core.manufacturing.fieldTypes.InputDependency
import com.nu.bom.core.manufacturing.fieldTypes.NumericFieldResultWithNumeratorAndDenominator
import com.nu.bom.core.manufacturing.fieldTypes.NumericFieldResultWithUnit
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.InputDependencyModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.model.manufacturing.persistentModel.PersistedDynamicFieldModel
import com.nu.bom.core.model.manufacturing.persistentModel.PersistedUnitInformation
import com.nu.bom.core.model.manufacturing.persistentModel.RootPersistedManufacturingModel
import com.nu.bom.core.model.manufacturing.schema.persistence.PersistedBomNodeSchemaService
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import com.nu.bom.core.service.migration.lazy.MigrationManager
import com.nu.bom.core.utils.filterNotNull
import org.bson.Document
import org.slf4j.LoggerFactory
import org.springframework.data.mongodb.core.convert.MappingMongoConverter
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import java.time.LocalDate

@Service
class ManufacturingEntityWriteConverter(
    private val mappingMongoConverter: MappingMongoConverter,
    private val migrationManager: MigrationManager,
    private val persistedBomNodeSchemaService: PersistedBomNodeSchemaService,
) {
    @VisibleForTesting
    var useLegacyModel: Boolean = false

    companion object {
        private val logger = LoggerFactory.getLogger(ManufacturingEntityWriteConverter::class.java)
    }

    fun convert(source: ManufacturingEntity): Mono<Document> {
        return persistedBomNodeSchemaService
            .getLatest()
            .map { schema ->
                val backwardCompatibilityModel = toManufacturingModelEntity(source, schema.lastMigrationChangeSetId)
                Pair(
                    backwardCompatibilityModel,
                    RootPersistedManufacturingModel.fromManufacturingModelEntityAndCurrentSchema(
                        backwardCompatibilityModel,
                        schema,
                    ),
                )
            }.map { (backwardCompatibilityModel, rootPersistedManufacturingModel) ->
                val document = Document()
                mappingMongoConverter.write(
                    rootPersistedManufacturingModel.takeUnless { useLegacyModel }
                        ?: backwardCompatibilityModel,
                    document,
                )
                document
            }
    }

    @VisibleForTesting
    fun toManufacturingModelEntity(
        entity: ManufacturingEntity,
        lastMigrationChangeSetId: MigrationChangeSetId?,
    ): ManufacturingModelEntity =
        ManufacturingModelEntity(
            id = entity._id,
            name = entity.name,
            type = entity.getEntityType(),
            clazz = entity::class.simpleName!!,
            args = entity.extractArgs().filterNotNull(),
            children = entity.children.map { toManufacturingModelEntity(it, lastMigrationChangeSetId) },
            fieldWithResults = toFieldWithResultModels(entity.fieldWithResults),
            isolated = entity.isolated,
            dynamicFields = toDynamicFieldModels(entity.dynamicFields),
            initialFieldWithResults = toFieldWithResultModels(entity.initialFieldsWithResults),
        ).apply {
            entityClass = entity::class
            version = entity.version
            initVersion = entity.initVersion

            versionHash = entity.currentImplementationVersionHash?.hash
            extensionHashes =
                entity
                    .getAllHashesToSave()
                    .associate { it.toPersistentRepresentation() }

            entityRef = entity.entityRef

            masterDataSelector = entity.masterDataSelector
            masterDataObjectId = entity.masterDataObjectId
            masterDataVersion = entity.masterDataVersion

            createdBy = entity.createdBy
            createdByMocked = entity.createdByMocked
            createdOnBranch = entity.createdOnBranch
            providerField = entity.providerField
            dirtyChildLoading = entity.dirtyChildLoading

            this.lastMigrationChangeSetId = lastMigrationChangeSetId
        }

    private fun toDynamicFieldModels(dynamicFields: MutableMap<String, DynamicField>): List<PersistedDynamicFieldModel> =
        dynamicFields.map {
            PersistedDynamicFieldModel(
                it.key,
                it.value.entityClass,
                entityFieldName = if (it.key != it.value.entityFieldName) it.value.entityFieldName else null,
            )
        }

    private fun toFieldWithResultModels(fields: List<FieldWithResult>): Map<String, FieldResultModel> =
        fields.associateBy(
            { field -> field.name.name },
            { field ->
                toFieldWithResultModel(
                    version = field.name.version,
                    newVersion = field.name.newVersion,
                    fieldResult = field.result,
                )
            },
        )

    private fun toFieldWithResultModel(
        version: Int,
        newVersion: Int,
        fieldResult: FieldResultStar,
    ): FieldResultModel {
        val unit =
            when (fieldResult) {
                is NumericFieldResultWithUnit<*, *> -> fieldResult.unitName
                else -> null
            }

        val (numerator, denominator) =
            when (fieldResult) {
                is NumericFieldResultWithNumeratorAndDenominator<*, *, *> ->
                    fieldResult.numeratorUnit to fieldResult.denominatorUnit

                else -> null to null
            }

        val defaultValue =
            when (fieldResult) {
                is DynamicQuantityUnit ->
                    fieldResult.defaultValue

                else -> null
            }

        val value = fieldResult.dbValue()
        // TODO: revisit system value handling https://tsetplatform.atlassian.net/browse/COST-78996
        val systemValue = (fieldResult.systemValue as? LocalDate)?.toString() ?: fieldResult.systemValue

        return FieldResultModel(
            version = version,
            newVersion = newVersion,
            type = fieldResult.getType(),
            value = value,
            source = fieldResult.source.name,
            unit = unit,
            systemValue = systemValue,
            numerator = PersistedUnitInformation.fromTypeUnit(numerator),
            denominator = PersistedUnitInformation.fromTypeUnit(denominator),
            defaultValue = defaultValue,
            inputs = mapInputDependencies(fieldResult.inputs),
        )
    }

    private fun mapInputDependencies(inputs: Set<InputDependency>): Set<InputDependencyModel>? {
        val mappedInputDependencies =
            inputs
                .mapTo(mutableSetOf()) { input ->
                    InputDependencyModel(
                        input.name,
                        input.entityId,
                        input.version,
                    )
                }
        return mappedInputDependencies.takeUnless { it.isEmpty() }
    }
}
