package com.nu.bom.core.service.exports

import com.nu.bom.core.model.toObjectId
import org.bson.types.ObjectId
import java.util.UUID

@Suppress("ktlint:standard:property-naming")
object ExportNameUtil {
    private const val ID_PATTERN = """([a-zA-Z0-9]*)"""
    private const val UUID_PATTERN = """([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})"""

    fun artifactName(id: UUID) = "$id.tset"

    const val EXPORT_EXTENSION = ".tset"
    const val idsFile = "IDs.json"
    const val projectFile = "Project.json"

    fun fileName(id: String) = "$id.json"

    fun fileName(id: ObjectId) = fileName(id.toString())

    fun fileName(id: UUID?) = fileName(id?.toObjectId() ?: ObjectId())

    const val BRANCH_DIR = "branches"
    val branchRegex = Regex("""/$BRANCH_DIR/$ID_PATTERN\.json""")

    const val BOMNODES_DIR = "bomnodes"
    val bomNodesRegex = Regex("""/$BOMNODES_DIR/$ID_PATTERN\.json""")

    const val SNAPSHOTS_DIR = "snapshots"
    val snapshotRegex = Regex("""/$SNAPSHOTS_DIR/$ID_PATTERN\.json""")

    const val FILES_DIR = "filesDocuments"
    val filesRegex = Regex("""/$FILES_DIR/$ID_PATTERN\.json""")

    const val TURNINGPROFILES_DIR = "turningProfilesDocuments"
    val turningProfilesRegex = Regex("""/$TURNINGPROFILES_DIR/$ID_PATTERN\.json""")

    const val NOCALCDATA_DIR = "noCalcDataDocuments"
    val noCalcDataRegex = Regex("""/$NOCALCDATA_DIR/$ID_PATTERN\.json""")

    const val MILLINGPROFILE_DIR = "millingProfileDocuments"
    val millingProfileRegex = Regex("""/$MILLINGPROFILE_DIR/$ID_PATTERN\.json""")

    const val VERSIONEDPARTS_DIR = "versionedParts"
    val versionedPartsRegex =
        Regex("""/$VERSIONEDPARTS_DIR/${UUID_PATTERN}_${UUID_PATTERN}\.json""")

    const val PARTS_DIR = "partsDocuments"
    val partsRegex = Regex("""/$PARTS_DIR/$ID_PATTERN\.json""")

    const val ATTACHMENTS_DIR = "attachments"
    val attachmentsRegex = Regex("""/$ATTACHMENTS_DIR/([a-zA-Z0-9\-]*)""")
}
