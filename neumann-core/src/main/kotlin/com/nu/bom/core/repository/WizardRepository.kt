package com.nu.bom.core.repository

import com.nu.bom.core.model.PersistedWizard
import com.nu.bom.core.model.Wizard
import com.nu.bom.core.model.manufacturing.ManufacturingEntityReadConverter
import com.nu.bom.core.model.manufacturing.ManufacturingEntityWriteConverter
import org.bson.types.ObjectId
import org.springframework.stereotype.Repository
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.switchIfEmpty

@Repository
class WizardRepository(
    private val persistedWizardRepository: PersistedWizardRepository,
    private val manufacturingEntityReadConverter: ManufacturingEntityReadConverter,
    private val manufacturingEntityWriteConverter: ManufacturingEntityWriteConverter,
) {
    fun findById(id: ObjectId): Mono<Wizard> {
        return persistedWizardRepository
            .findById(id)
            .flatMap { convertToWizard(it) }
    }

    fun findByIdAndAccountId(
        id: ObjectId,
        accountId: ObjectId,
    ): Mono<Wizard> {
        return persistedWizardRepository
            .findBy_idAndAccountId(id, accountId)
            .flatMap { convertToWizard(it) }
    }

    fun save(wizard: Wizard): Mono<Wizard> {
        return convertToPersistedWizard(wizard)
            .flatMap { persistedWizardRepository.save(it) }
            .flatMap { convertToWizard(it) }
    }

    fun deleteAll(): Mono<Void> = persistedWizardRepository.deleteAll()

    private fun convertToWizard(persistedWizard: PersistedWizard): Mono<Wizard> {
        fun createWizard(persistedWizard: PersistedWizard): Wizard {
            return Wizard(
                projectId = persistedWizard.projectId,
                standardCalculationData = persistedWizard.standardCalculationData,
                parentPartName = persistedWizard.parentPartName,
                accountId = persistedWizard.accountId,
            ).apply {
                _id = persistedWizard._id
                steps = persistedWizard.steps
                turningProfileId = persistedWizard.turningProfileId
                millingProfileId = persistedWizard.millingProfileId
                rawPartTechnology = persistedWizard.rawPartTechnology
                editInitialStep = persistedWizard.editInitialStep
            }
        }

        return Mono
            .justOrEmpty(persistedWizard.manufacturingEntity)
            .flatMap { manufacturingEntityReadConverter.convert(it!!) }
            .map {
                createWizard(persistedWizard).apply {
                    manufacturingEntity = it
                }
            }.switchIfEmpty {
                Mono.just(createWizard(persistedWizard))
            }
    }

    private fun convertToPersistedWizard(wizard: Wizard): Mono<PersistedWizard> {
        fun createPersistedWizard(wizard: Wizard): PersistedWizard {
            return PersistedWizard(
                projectId = wizard.projectId,
                standardCalculationData = wizard.standardCalculationData,
                parentPartName = wizard.parentPartName,
                accountId = wizard.accountId,
            ).apply {
                _id = wizard._id
                steps = wizard.steps
                turningProfileId = wizard.turningProfileId
                millingProfileId = wizard.millingProfileId
                rawPartTechnology = wizard.rawPartTechnology
                editInitialStep = wizard.editInitialStep
            }
        }

        return Mono
            .justOrEmpty(wizard.manufacturingEntity)
            .flatMap { manufacturingEntityWriteConverter.convert(it!!) }
            .map {
                createPersistedWizard(wizard).apply {
                    manufacturingEntity = it
                }
            }.switchIfEmpty {
                Mono.just(createPersistedWizard(wizard))
            }
    }
}
