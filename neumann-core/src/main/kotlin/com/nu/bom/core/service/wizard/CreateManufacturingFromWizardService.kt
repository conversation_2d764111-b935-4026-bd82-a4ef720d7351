package com.nu.bom.core.service.wizard

import com.fasterxml.jackson.databind.ObjectMapper
import com.nu.bom.core.api.dtos.CalculationModel
import com.nu.bom.core.api.dtos.CreateManufacturingResult
import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.exception.readable.WizardInternalException
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.BaseManufacturingFields
import com.nu.bom.core.manufacturing.fieldTypes.MachiningForTechnology
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.service.CalculationContextService
import com.nu.bom.core.model.BomEntryCreationData
import com.nu.bom.core.model.BomEntryUpdateData
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.Wizard
import com.nu.bom.core.model.createBranchId
import com.nu.bom.core.service.BomEntryFieldsSanitizer
import com.nu.bom.core.service.EcoFieldsSanitizer.sanitizeForEdit
import com.nu.bom.core.service.ManufacturingModelsUtils
import com.nu.bom.core.service.wizard.steps.WizardCbdFieldStep
import com.nu.bom.core.service.wizard.steps.WizardCbdStepManager
import com.nu.bom.core.service.wizard.steps.WizardSpecFieldStep
import com.nu.bom.core.service.wizard.steps.WizardTechStep
import com.nu.bom.core.service.wizard.steps.convertConfigurationIdentifierToJsonString
import com.nu.bom.core.turn.TurningService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.getRequired
import com.tset.core.api.calculation.dto.CalculationCreationModalMode
import com.tset.core.module.bom.CalculationUpdateModule
import com.tset.core.module.bom.calculation.CalculationChangeTypeRootInput
import com.tset.core.module.bom.calculation.CalculationChangeTypeSubInput
import com.tset.core.module.bom.calculation.CalculationCreateRootInput
import com.tset.core.module.bom.calculation.CalculationCreateSubInput
import com.tset.core.module.bom.calculation.CalculationEditRootInput
import com.tset.core.module.bom.calculation.CalculationEditSubInput
import com.tset.core.service.domain.Currency
import org.bson.types.ObjectId
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import kotlin.reflect.KClass

private const val RAW_COST_MODULE_CONFIGURATION_IDENTIFIER = "rawCostModuleConfigurationIdentifier"

@Service
class CreateManufacturingFromWizardService(
    private val wizardService: WizardService,
    private val wizardCbdStepManager: WizardCbdStepManager,
    private val turningService: TurningService,
    private val calculationUpdateModule: CalculationUpdateModule,
    private val entityManager: EntityManager,
    private val calculationContextService: CalculationContextService,
    private val objectMapper: ObjectMapper,
) {
    fun createFromWizard(
        accessCheck: AccessCheck,
        wizardId: ObjectId,
        dirtyChildLoading: Boolean = false,
    ): Mono<CreateManufacturingResult> {
        return wizardService.getWizard(accessCheck, wizardId).flatMap { wizard ->

            when (wizard.isSubManufacturing()) {
                true -> updateSubFromWizard(accessCheck, wizard, dirtyChildLoading)
                false -> updateRootFromWizard(accessCheck, wizard)
            }
        }
    }

    private fun getModel(wizard: Wizard): CalculationModel {
        val modelId =
            wizard.getSteps(WizardTechStep::class)?.modelId()?.value?.toString()
                ?: throw IllegalArgumentException("missing modelId in TECH step")

        var model: CalculationModel = ManufacturingModelsUtils.findByEntity(modelId)!!
        if (wizard.steps[WizardSpecFieldStep::class.simpleName] != null) {
            val machining = (wizard.steps[WizardSpecFieldStep::class.simpleName] as WizardSpecFieldStep).machining()
            // TODO: How to do properly? --jgr/21/05
            if (machining != null && model.path != "bart") {
                when (machining.res) {
                    MachiningForTechnology.Selection.MILLING -> model = Model.MILL.toCalculationModel()
                    MachiningForTechnology.Selection.TURNING -> model = Model.RAWT.toCalculationModel()
                    MachiningForTechnology.Selection.NONE -> {}
                }
            }
        }
        return model
    }

    private fun getBaseFields(wizard: Wizard): List<FieldParameter> {
        val standardFields = wizard.getStandardFields()
        val wizardFields = wizard.getWizardFields()

        val millingProfileId =
            if (getModel(wizard) == Model.MILL.toCalculationModel()) {
                FieldParameter(
                    name = "millingProfileId",
                    value = (wizard.steps[WizardSpecFieldStep::class.simpleName]!! as WizardSpecFieldStep).millingProfileId!!.toHexString(),
                    type = "Text",
                    source = "I",
                    denominatorUnit = null,
                )
            } else {
                null
            }

        return (standardFields + wizardFields + millingProfileId).filterNotNull()
    }

    private fun updateRootFromWizard(
        accessCheck: AccessCheck,
        wizard: Wizard,
    ): Mono<CreateManufacturingResult> {
        return getTurningFields(wizard, accessCheck)
            .flatMap { turningFields ->
                validateTurningFields(turningFields, wizard)
                val calculationUpdateData = wizard.standardCalculationData.calculationUpdateData!!
                getManuAndFields(accessCheck, wizard, turningFields).flatMap { (manufacturingClass, entityFields) ->
                    val updateDto =
                        when (calculationUpdateData.input.mode) {
                            CalculationCreationModalMode.CALCULATION_MODE_NEW ->
                                CalculationCreateRootInput(
                                    projectId = wizard.projectId,
                                    title = wizard.standardCalculationData.calculationTitle,
                                    fields = entityFields,
                                    clazz = manufacturingClass,
                                    displayCurrency = calculationUpdateData.input.currency,
                                    wizardManufacturing = wizard.manufacturingEntity,
                                )

                            CalculationCreationModalMode.CALCULATION_MODE_EDIT ->
                                CalculationEditRootInput(
                                    projectId = wizard.projectId,
                                    title = wizard.standardCalculationData.calculationTitle,
                                    fields = sanitizeForEdit(entityFields),
                                    bomNodeId = BomNodeId(calculationUpdateData.input.context.bomNodeId),
                                    branchId = createBranchId(calculationUpdateData.input.context.branchId),
                                    selectedType = calculationUpdateData.selectedType,
                                    displayCurrency = calculationUpdateData.input.currency,
                                    wizardManufacturing = wizard.manufacturingEntity,
                                )

                            CalculationCreationModalMode.CALCULATION_MODE_CHANGE_TYPE ->
                                CalculationChangeTypeRootInput(
                                    projectId = wizard.projectId,
                                    title = wizard.standardCalculationData.calculationTitle,
                                    fields = entityFields,
                                    bomNodeId = BomNodeId(calculationUpdateData.input.context.bomNodeId),
                                    branchId = createBranchId(calculationUpdateData.input.context.branchId),
                                    selectedType = calculationUpdateData.selectedType,
                                    clazz = manufacturingClass,
                                    displayCurrency = calculationUpdateData.input.currency,
                                )
                        }
                    calculationUpdateModule.dispatch(accessCheck, updateDto)
                }
            }.flatMap { result ->
                addCbdStep(
                    result,
                    wizardId = wizard._id!!,
                    accessCheck = accessCheck,
                    currency = wizard.standardCalculationData.calculationUpdateData?.input?.currency!!,
                )
            }
    }

    private fun getManuAndFields(
        accessCheck: AccessCheck,
        wizard: Wizard,
        turningFields: List<FieldParameter>,
    ): Mono<Pair<KClass<out BaseManufacturing>, List<FieldParameter>>> {
        val calculationModel = getModel(wizard)
        return updateVersioningConfig(accessCheck, getBaseFields(wizard), calculationModel).map { entityFields ->
            val manufacturingClass =
                entityManager.getClass(calculationModel.entity).kotlin as KClass<out BaseManufacturing>
            manufacturingClass to entityFields + turningFields
        }
    }

    /**
     * For RawT technology, we get the technology specific config and not the RawT. We need to replace it, so we provide
     * the correct config far RawT and rename the config to be inherited by the sub calculation.
     */
    private fun updateVersioningConfig(
        accessCheck: AccessCheck,
        baseFields: List<FieldParameter>,
        calculationModel: CalculationModel,
    ): Mono<List<FieldParameter>> {
        val baseConfig = baseFields.find { it.name == BaseManufacturingFields::costModuleConfigurationIdentifier.name }
        val rawtConfig = baseFields.find { it.name == RAW_COST_MODULE_CONFIGURATION_IDENTIFIER }
        if (calculationModel.name != Model.RAWT.displayName || rawtConfig != null) {
            return Mono.just(baseFields)
        }
        return calculationContextService.getDefaultCostModuleConfigurationKey(
            accessCheck,
            Model.RAWT,
        ).map { configKey ->
            val configFields =
                listOf(
                    BaseManufacturingFields::costModuleConfigurationIdentifier.name,
                    RAW_COST_MODULE_CONFIGURATION_IDENTIFIER,
                )
            val newBaseFields = baseFields.filterNot { it.name in configFields }

            newBaseFields +
                baseConfig!!.copy(name = RAW_COST_MODULE_CONFIGURATION_IDENTIFIER) +
                baseConfig.copy(value = convertConfigurationIdentifierToJsonString(objectMapper, configKey))
        }
    }

    private fun validateTurningFields(
        turningFields: List<FieldParameter>,
        wizard: Wizard,
    ) {
        if (
            turningFields.find { field ->
                field.name == "rawPartInnerDiameter" ||
                    field.name == "barDiameter"
            } == null && wizard.getSteps(WizardSpecFieldStep::class)
                // v might be null if this is called by a unit test.
                ?.machining() == MachiningForTechnology.turning
        ) {
            throw WizardInternalException(
                "Part Properties missing in createFromWizardSimple!",
                wizard._id?.toHexString(),
            )
        }
    }

    private fun updateSubFromWizard(
        accessCheck: AccessCheck,
        wizard: Wizard,
        dirtyChildLoading: Boolean,
    ): Mono<CreateManufacturingResult> {
        return getTurningFields(wizard, accessCheck).flatMap { turningFields ->
            val parentBomData =
                wizard.standardCalculationData.bomEntry
                    ?: error("createSubFromWizard missing standardCalculationData.bomEntry")
            val calculationUpdateData = wizard.standardCalculationData.calculationUpdateData
            requireNotNull(calculationUpdateData)
            getManuAndFields(accessCheck, wizard, turningFields).flatMap { (manufacturingClass, entityFields) ->
                val updateDto =
                    when (calculationUpdateData.input.mode) {
                        CalculationCreationModalMode.CALCULATION_MODE_NEW ->
                            CalculationCreateSubInput(
                                projectId = wizard.projectId,
                                title = wizard.standardCalculationData.calculationTitle,
                                partName = wizard.standardCalculationData.partName,
                                fields = entityFields,
                                clazz = manufacturingClass,
                                parentBomData =
                                    BomEntryCreationData(
                                        bomNodeId = parentBomData.bomNodeId,
                                        branchId = parentBomData.branchId,
                                        // bomEntryData.stepId,
                                        stepId = ObjectId(getStepId(parentBomData.fields)),
                                        fields = wizard.getBomEntryFields(),
                                        path = parentBomData.path,
                                    ),
                                displayCurrency = calculationUpdateData.input.currency,
                                wizardManufacturing = wizard.manufacturingEntity,
                            )

                        CalculationCreationModalMode.CALCULATION_MODE_EDIT ->
                            CalculationEditSubInput(
                                projectId = wizard.projectId,
                                title = wizard.standardCalculationData.calculationTitle,
                                partName = wizard.standardCalculationData.partName,
                                fields = sanitizeForEdit(entityFields),
                                parentBomData =
                                    BomEntryUpdateData(
                                        bomNodeId = parentBomData.bomNodeId,
                                        branchId = parentBomData.branchId,
                                        fields = BomEntryFieldsSanitizer.sanitizeForEdit(wizard.getBomEntryFields()),
                                        path = parentBomData.path,
                                    ),
                                bomNodeId = BomNodeId(calculationUpdateData.input.context.bomNodeId),
                                branchId = createBranchId(calculationUpdateData.input.context.branchId),
                                selectedType = calculationUpdateData.selectedType,
                                displayCurrency = calculationUpdateData.input.currency,
                                wizardManufacturing = wizard.manufacturingEntity,
                            )

                        CalculationCreationModalMode.CALCULATION_MODE_CHANGE_TYPE ->
                            CalculationChangeTypeSubInput(
                                projectId = wizard.projectId,
                                title = wizard.standardCalculationData.calculationTitle,
                                partName = wizard.standardCalculationData.partName,
                                fields = entityFields,
                                parentBomData =
                                    BomEntryUpdateData(
                                        bomNodeId = parentBomData.bomNodeId,
                                        branchId = parentBomData.branchId,
                                        fields = wizard.getBomEntryFields(),
                                        path = parentBomData.path,
                                    ),
                                bomNodeId = BomNodeId(calculationUpdateData.input.context.bomNodeId),
                                branchId = createBranchId(calculationUpdateData.input.context.branchId),
                                selectedType = calculationUpdateData.selectedType,
                                clazz = manufacturingClass,
                                displayCurrency = calculationUpdateData.input.currency,
                            )
                    }
                calculationUpdateModule.dispatch(accessCheck, updateDto, dirtyChildLoading)
            }
        }.flatMap { result ->
            addCbdStep(
                result,
                wizardId = wizard._id!!,
                accessCheck = accessCheck,
                currency = wizard.standardCalculationData.calculationUpdateData?.input?.currency!!,
            )
        }
    }

    private fun getTurningFields(
        wizard: Wizard,
        accessCheck: AccessCheck,
    ): Mono<List<FieldParameter>> {
        return wizard.turningProfileId()?.let {
            turningService.getFieldParameters(it, accessCheck)
                .switchIfEmpty(
                    Mono.error(IllegalArgumentException("getFieldParameters didn't work.")),
                )
        } ?: Mono.just(listOf())
    }

    private fun addCbdStep(
        result: CreateManufacturingResult,
        wizardId: ObjectId,
        accessCheck: AccessCheck,
        currency: Currency,
    ): Mono<CreateManufacturingResult> {
        return wizardCbdStepManager.saveStep(
            wizardId = wizardId,
            wizardStep =
                WizardCbdFieldStep(
                    fields =
                        listOf(
                            FieldParameter(
                                name = "id",
                                value = result.bomNode.id,
                                // TODO This looks very wrong …
                                type = "id",
                                metaInfo = mapOf("frontendOnly" to true),
                                denominatorUnit = null,
                            ),
                        ),
                ),
            accessCheck = accessCheck,
            currency = currency,
        ).map {
            result
        }
    }

    private fun getStepId(fields: List<FieldParameter>): String = fields.getRequired("stepId", Text::class).value as String
}
