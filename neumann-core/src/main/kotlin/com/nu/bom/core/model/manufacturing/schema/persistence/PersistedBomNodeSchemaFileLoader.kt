package com.nu.bom.core.model.manufacturing.schema.persistence

import com.fasterxml.jackson.databind.ObjectMapper
import com.nu.bom.core.model.manufacturing.schema.exception.InvalidSchemaFormatException
import com.nu.bom.core.utils.FileResourceLoader
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.context.annotation.Profile
import org.springframework.core.ResolvableType
import org.springframework.core.codec.DecodingException
import org.springframework.core.io.Resource
import org.springframework.core.io.buffer.DataBufferUtils
import org.springframework.core.io.buffer.DefaultDataBufferFactory
import org.springframework.http.codec.json.Jackson2JsonDecoder
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.StandardOpenOption
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.SortedMap

@Service
@Profile("!schema-loading-test")
class PersistedBomNodeSchemaFileLoader(
    private val fileResourceLoader: FileResourceLoader,
    private val objectMapper: ObjectMapper,
    private val fileResourcePattern: String = "bomNodeSchema/*.json",
    private val fileWriteDirectory: String = "neumann-core/src/main/resources/bomNodeSchema",
) {
    // The key is the schema version, derived from the filename. The resources are sorted by version, ascending.
    private var schemaResourcesByVersion: SortedMap<Int, Resource> = this.loadSchemaResources(fileResourcePattern)

    protected fun loadSchemaResources(fileResourcePattern: String) =
        fileResourceLoader
            .loadFiles(fileResourcePattern)
            .associateBy { resource -> extractVersionFromFilename(resource.filename!!) }
            .toSortedMap()

    fun loadLastSchema(): Mono<PersistedBomNodeSchemaWithVersion> {
        if (schemaResourcesByVersion.isEmpty()) {
            return Mono.empty()
        }
        val lastSchemaVersion = schemaResourcesByVersion.lastKey()
        return loadSchema(lastSchemaVersion)
    }

    fun loadSchema(version: Int): Mono<PersistedBomNodeSchemaWithVersion> {
        logger.debug("Loading schema resource version {}...", version)
        val schemaResource =
            schemaResourcesByVersion[version]
                ?: throw IllegalStateException("No schema file for version $version exists!")

        val jackson2JsonDecoder =
            Jackson2JsonDecoder(objectMapper).apply {
                maxInMemorySize = schemaResource.contentLength().toInt()
            }
        val bytes =
            DataBufferUtils
                .read(schemaResource, DefaultDataBufferFactory(), jackson2JsonDecoder.maxInMemorySize)

        return jackson2JsonDecoder
            .decodeToMono(
                bytes,
                ResolvableType.forType(PersistedBomNodeSchemaWithVersion::class.java),
                null,
                null,
            ).onErrorMap {
                if (it is DecodingException) {
                    InvalidSchemaFormatException(
                        "Failed to deserialize schema file ${schemaResource.filename} to class 'PersistedBomNodeSchemaWithVersion'!",
                        it,
                    )
                } else {
                    it
                }
            }.cast(PersistedBomNodeSchemaWithVersion::class.java)
            .handle { it, sink ->
                if (it.version != version) {
                    sink.error(
                        IllegalStateException(
                            "Schema file ${schemaResource.filename} version " +
                                "(${it.version}) is not equal to its filename prefix ($version)!",
                        ),
                    )
                } else {
                    sink.next(it)
                }
            }
    }

    protected fun extractVersionFromFilename(filename: String): Int {
        // format: [version]_[date].json
        val versionPart = filename.substringBefore("_")
        return versionPart.toInt()
    }

    fun saveSchema(
        version: Int,
        schema: PersistedBomNodeSchema,
    ): Path {
        val createdDate = LocalDate.now()
        val versionedSchema =
            PersistedBomNodeSchemaWithVersion(
                schema = schema,
                version = version,
                createdDate = createdDate,
            )

        // Filename format: [version]_[date].json
        val dateFormatted = createdDate.format(DATE_FORMAT)
        val filename = "${version}_$dateFormatted.json"

        Files.createDirectories(Path.of(fileWriteDirectory))

        val filePath = Path.of("$fileWriteDirectory/$filename")
        val serialized = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsBytes(versionedSchema)
        val finalPath =
            Files.write(filePath, serialized, StandardOpenOption.CREATE_NEW).also {
                logger.info("Successfully saved schema $filename")
            }

        // Refresh cached schema resource files after we've created a new schema file.
        schemaResourcesByVersion = loadSchemaResources(fileResourcePattern)

        return finalPath
    }

    companion object {
        @JvmStatic
        protected val logger: Logger = LoggerFactory.getLogger(PersistedBomNodeSchemaFileLoader::class.java)
        private val DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd")
    }
}

/**
 * This configuration overrides the [PersistedBomNodeSchemaFileLoader] bean when the
 * 'continue-after-schema-generation' profile is active during local development.
 * It switches schema file loading from classpath resources to the file system,
 * enabling the application to write and immediately reload schema files within a
 * single run. This is not possible with classpath resource loading, as classpath
 * resources are read-only after application startup.
 *
 * This approach is strictly for local use, as file system loading would fail
 * in packaged environments where resources are bundled inside a JAR.
 */
@Configuration
@Profile("local && continue-after-schema-generation")
class LocalDevelopmentPersistedBomNodeSchemaFileLoaderConfig {
    @Bean
    @Primary
    fun persistedBomNodeSchemaFileLoader(
        fileResourceLoader: FileResourceLoader,
        objectMapper: ObjectMapper,
    ): PersistedBomNodeSchemaFileLoader {
        return object : PersistedBomNodeSchemaFileLoader(
            fileResourceLoader = fileResourceLoader,
            objectMapper = objectMapper,
            fileResourcePattern = "neumann-core/src/main/resources/bomNodeSchema/*.json",
        ) {
            override fun loadSchemaResources(fileResourcePattern: String): SortedMap<Int, Resource> {
                logger.error(
                    "Warning: loading schema files via file system resource loader! " +
                        "This should only happen during local development!",
                )
                @OptIn(FileResourceLoader.OptInResourceLoaderFunction::class)
                return fileResourceLoader
                    .loadFilesFromFileSystem(fileResourcePattern)
                    .associateBy { resource -> extractVersionFromFilename(resource.filename!!) }
                    .toSortedMap()
            }
        }
    }
}
