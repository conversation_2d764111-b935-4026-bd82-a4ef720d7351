package com.nu.bom.core.model.manufacturing

import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.manufacturing.persistentModel.PersistedBomNode
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono

@Service
class BomNodeConverter(
    private val writeConverter: ManufacturingEntityWriteConverter,
    private val readConverter: ManufacturingEntityReadConverter,
) {
    fun convertFromDomainObject(bomNode: BomNodeSnapshot): Mono<PersistedBomNode> =
        onBeforeConvert(bomNode).let {
            writeConverter.convert(it.manufacturing!!).map { m ->
                PersistedBomNode(
                    name = it.name,
                    year = it.year,
                    title = it.title,
                    accountId = it.accountId,
                    manufacturing = m,
                    generated = it.generated,
                    kpi = it.kpi,
                    partName = it.partName ?: "",
                    lastMigrationChangeSetId = it.lastMigrationChangeSetId,
                ).apply {
                    _id = it._id
                    createdBy = it.createdBy
                    createdDate = it.createdDate
                    projectId = it.projectId()
                    bomNodeSnapshot = it
                }
            }
        }

    fun convertToDomainObject(persistedBomNode: PersistedBomNode): Mono<BomNodeSnapshot> =
        readConverter.convert(persistedBomNode.manufacturing, persistedBomNode).map { m ->
            BomNodeSnapshot(
                name = persistedBomNode.name,
                year = persistedBomNode.year,
                title = persistedBomNode.title,
                accountId = persistedBomNode.accountId,
                manufacturing = m,
                generated = persistedBomNode.generated,
                deleted = persistedBomNode.isDeleted(),
                kpi = persistedBomNode.kpi,
            ).apply {
                _id = persistedBomNode._id
                createdBy = persistedBomNode.createdBy
                createdDate = persistedBomNode.createdDate
                lastModifiedBy = persistedBomNode.lastModifiedBy
                lastModifiedDate = persistedBomNode.lastModifiedDate
                createdWithSystemVersion = persistedBomNode.createdWithSystemVersion
                projectId = persistedBomNode.projectId
                partName = persistedBomNode.partName
                lastMigrationChangeSetId = persistedBomNode.lastMigrationChangeSetId
            }
        }

    private fun onBeforeConvert(node: BomNodeSnapshot): BomNodeSnapshot {
        val baseManufacturing = node.getBaseManufacturing()

        // TODO kw get partDesignation?
        val partName =
            baseManufacturing
                ?.getFieldResultSafeCast<Text>(BaseManufacturing::partName.name)?.res
                ?.takeIf { it.isNotBlank() }

        // Set createdOnBranch if not yet filled
        node.manufacturing?.visitChildren { entity, _ ->
            // Set createdOnBranch if not yet filled
            if (entity.createdOnBranch == null) {
                entity.createdOnBranch = node.branch
            }
        }

        return node
            .apply {
                this.partName = partName
            }
    }
}
