package com.nu.bom.core.utils

import com.fasterxml.jackson.annotation.JsonInclude
import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.AlternativeNames
import com.nu.bom.core.manufacturing.annotations.CompositeMandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.CompositeMandatoryForEntityDto
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DynamicTranslationLabel
import com.nu.bom.core.manufacturing.annotations.EntityCreationMasterDataType
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.FieldBasedUnits
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntityContext
import com.nu.bom.core.manufacturing.annotations.ManualClassSelector
import com.nu.bom.core.manufacturing.annotations.Modularized
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.SourceDataInput
import com.nu.bom.core.manufacturing.annotations.SourceDataKey
import com.nu.bom.core.manufacturing.annotations.TranslationLabel
import com.nu.bom.core.manufacturing.annotations.Units
import com.nu.bom.core.manufacturing.annotations.defaultUnitInfoFromMetaInfo
import com.nu.bom.core.manufacturing.annotations.toMetaInfoEntry
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.fieldmapping.BackMappingFieldsBuilder
import com.nu.bom.core.manufacturing.entities.ManualManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ManufacturingEntityExtension
import com.nu.bom.core.manufacturing.entities.ManufacturingStep
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.enums.UnitOverrideContext
import com.nu.bom.core.manufacturing.enums.dynamicUnitStringToTypeUnit
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.ClassSelector
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.IntegerFieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Null
import com.nu.bom.core.manufacturing.fieldTypes.NumericFieldResult
import com.nu.bom.core.manufacturing.fieldTypes.NumericFieldResultWithUnit
import com.nu.bom.core.manufacturing.fieldTypes.SelectEnumFieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TypeUnit
import com.nu.bom.core.manufacturing.service.EntityClassOrName
import com.nu.bom.core.manufacturing.service.InjectableEngineService
import com.nu.bom.core.manufacturing.service.asEntityClass
import com.nu.bom.core.manufacturing.service.asString
import com.nu.bom.core.manufacturing.service.virtualfield.sourceproviders.DataSourceProviders
import com.nu.bom.core.manufacturing.utils.ManufacturingTreeUtils
import com.nu.bom.core.model.MasterDataSelector
import com.nu.bom.core.service.EntityHashFallbackProvider
import com.nu.bom.core.utils.changelog.EntityChangelogEntry
import com.nu.bom.core.utils.changelog.EntityHash
import com.tset.bom.clients.common.DenominatorBehavior
import com.tset.bom.clients.common.DenominatorUnit
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.util.EnumMap
import java.util.Locale
import kotlin.reflect.KClass
import kotlin.reflect.KType
import kotlin.reflect.full.findAnnotation
import kotlin.reflect.full.isSuperclassOf
import kotlin.reflect.full.memberProperties
import kotlin.reflect.full.primaryConstructor
import kotlin.reflect.jvm.jvmErasure
import com.nu.bom.core.manufacturing.annotations.MasterDataType as MasterDataTypeAnnotation
import com.nu.bom.core.manufacturing.enums.MasterDataType as MasterDataTypeEnum

private val logger: Logger = LoggerFactory.getLogger(EntityManager::class.java)

@Deprecated("Do not use this", ReplaceWith("BomNodeSchemaService"))
@Service
class EntityManager(
    private val entityMetaInfoExtractionService: EntityMetaInfoExtractionService,
    private val extensionManager: ExtensionManager,
    private val entityHashFallbackProvider: EntityHashFallbackProvider,
    private val manualCreationEntityLoader: ManualCreationEntityLoader,
    private val backMappingBuilder: BackMappingFieldsBuilder,
) : InjectableEngineService {
    private val entities: MutableMap<String, Class<out ManufacturingEntity>> = mutableMapOf()
    private val entityAlternativeNames: MutableMap<String, String> = mutableMapOf()
    private val units: MutableMap<String, MutableMap<String, TypeUnit>> = mutableMapOf()
    private val entityMetaInfo: MutableMap<String, EntityMetaInfo> = mutableMapOf()
    private val masterDataEntities: EnumMap<MasterDataTypeEnum, Class<out ManufacturingEntity>> =
        EnumMap(MasterDataTypeEnum::class.java)
    private val masterDataEntitiesForCreation: EnumMap<MasterDataTypeEnum, Class<out ManufacturingEntity>> =
        EnumMap(MasterDataTypeEnum::class.java)
    private val technologyMaterials: MutableMap<String, MutableList<EntityMetaInfo>> = mutableMapOf()
    private val modularizedEntities: EnumMap<
        Model,
        EnumMap<
            Entities,
            MutableList<Pair<Modularized, Class<out ManufacturingEntity>>>,
        >,
    > = EnumMap(Model::class.java)
    private val modularizedEntitiesToMasterDataType: EnumMap<MasterDataTypeEnum, EnumMap<Model, Class<out ManufacturingEntity>>> =
        EnumMap(MasterDataTypeEnum::class.java)

    private val fieldTypes: MutableMap<String, Class<out FieldResultStar>> = mutableMapOf()
    private val selectableTypes: MutableMap<String, Class<out Enum<*>>> = mutableMapOf()

    val entityNames: Set<String>
        get() = entities.keys

    fun entityMetaInfoFields(): Map<String, Map<String, FieldInfo>> = entityMetaInfo.mapValues { entry -> entry.value.fields }

    fun clear() {
        logger.warn("cleaning up $this")
        entities.clear()
        fieldTypes.clear()
        units.clear()
        selectableTypes.clear()
        entityMetaInfo.clear()
        masterDataEntities.clear()
        masterDataEntitiesForCreation.clear()
        technologyMaterials.clear()
        modularizedEntitiesToMasterDataType.clear()
    }

    /**
     * Convenience method with fallback to the default manual class of the provided entity type.
     *
     * Fields which are added by a dynamic behaviour are not returned by this method.
     * To get dynamic fields use [com.nu.bom.core.utils.InstanceBasedMetaService]
     * */
    fun getMetaInfo(
        entityType: Entities,
        entityClass: String?,
        fieldName: String,
        interpolationData: InterpolationData,
    ) = getMetaInfo(
        entityClass ?: manualCreationEntityLoader.getDefaultClass(entityType).simpleName!!,
        fieldName,
        interpolationData,
    )

    /**
     * Fields which are added by a dynamic behaviour are not returned by this method.
     * To get dynamic fields use [com.nu.bom.core.utils.InstanceBasedMetaService]
     */
    fun getMetaInfo(
        entity: ManufacturingEntity,
        field: String,
    ): Map<String, Any> = getMetaInfo(entity::class.simpleName!!, field, interpolationData = InterpolationData.fromEntity(entity))

    fun getMetaInfo(
        entity: Class<out ManufacturingEntity>,
        field: String,
        interpolationData: InterpolationData,
    ): Map<String, Any> = getMetaInfo(entity.simpleName, field, interpolationData)

    /**
     * Gets metainfo for the provided class/field.
     *
     * Fields which are added by a dynamic behaviour are not returned by this method.
     * To get dynamic fields use [com.nu.bom.core.utils.InstanceBasedMetaService]
     * */
    fun getMetaInfo(
        entityType: String,
        field: String,
        interpolationData: InterpolationData,
    ): Map<String, Any> =
        getMetaInfo(
            entityType = entityType,
            field = field,
            originalType = entityType,
            interpolationData = interpolationData,
        )

    private fun getMetaInfo(
        entityType: String,
        field: String,
        originalType: String = entityType,
        interpolationData: InterpolationData,
    ): Map<String, Any> {
        val metaInfo =
            requireNotNull(getEntityInfo(entityType)) {
                "No EntityMetaInfo found for $entityType - available ones are: ${entityMetaInfo.keys}"
            }
        val coreMetaInfo = interpolateFields(metaInfo.getFieldInfo(field), originalType, interpolationData)

        val extendsMetaInfo =
            metaInfo.extends?.let { extendedType ->
                getMetaInfo(extendedType, field, originalType, interpolationData)
            } ?: emptyMap()

        val pluginExtensionMetaInfo =
            getAndInterpolatePluginExtensionFieldMetaInfo(entityType, field, interpolationData)

        return pluginExtensionMetaInfo + extendsMetaInfo + coreMetaInfo
    }

    private fun getAndInterpolatePluginExtensionFieldMetaInfo(
        entityType: String,
        field: String,
        interpolationData: InterpolationData,
    ): Map<String, Any> =
        extensionManager.getUninterpolatedExtensionsMetaInfo(entityType).fold(emptyMap()) { acc, extensionMetaInfo ->
            acc + interpolateFields(extensionMetaInfo.getFieldInfo(field), entityType, interpolationData)
        }

    fun getUninterpolatedPluginExtensionMetaInfo(
        entityType: String,
        includeFieldsWithoutMetaInfo: Boolean = false,
    ): Map<String, FieldInfo> =
        extensionManager.getUninterpolatedExtensionsMetaInfo(entityType).fold(emptyMap()) { acc, extensionMetaInfo ->
            acc +
                if (includeFieldsWithoutMetaInfo) {
                    extensionMetaInfo.fields
                } else {
                    extensionMetaInfo.notEmptyFields
                }
        }

    /**
     * Return EntityMetaInfo about the type, this only contains, the not-inherited fields.
     */
    fun getEntityInfo(entity: ManufacturingEntity): EntityMetaInfo? = getEntityInfo(entity.getEntityClass())

    /**
     * Return EntityMetaInfo about the type, this only contains, the not-inherited fields.
     * Probably, you need getMetaInfo(entity, fieldName),
     * or getEntityFields(entityType) which do include inherited fields.
     *
     * Fields which are added by a dynamic behaviour are not returned by this method.
     * To get dynamic fields use [com.nu.bom.core.utils.InstanceBasedMetaService]
     */
    fun getEntityInfo(entity: Class<out ManufacturingEntity>): EntityMetaInfo? = getEntityInfo(entity.simpleName)

    fun getEntityType(entity: Class<out ManufacturingEntity>): Entities? = getEntityInfo(entity.simpleName)?.entityType

    fun getTechnologies(entity: Class<out ManufacturingEntity>): List<Model> = getEntityInfo(entity.simpleName)?.technologies ?: listOf()

    /**
     * Return EntityMetaInfo about the type, this only contains, the not-inherited fields.
     * Probably, you need getMetaInfo(entity, fieldName),
     * or getEntityFields(entityType) which do include inherited fields.
     *
     * Fields which are added by a dynamic behaviour are not returned by this method.
     * To get dynamic fields use [com.nu.bom.core.utils.InstanceBasedMetaService]
     */
    fun getEntityInfo(simpleName: String): EntityMetaInfo? = entityMetaInfo[simpleName]

    fun getMasterDataType(simpleName: String): MasterDataTypeEnum? = entityMetaInfo[simpleName]?.masterDataType

    fun getMasterDataTypeRecursively(simpleName: String): MasterDataTypeEnum? {
        val extendsSimpleName: String? = getEntityInfo(simpleName)?.extends
        return getMasterDataType(simpleName)
            ?: if (extendsSimpleName == null) {
                null
            } else {
                getMasterDataTypeRecursively(extendsSimpleName)
            }
    }

    /**
     * Fields which are added by a dynamic behaviour are not returned by this method.
     * To get dynamic fields use [com.nu.bom.core.utils.InstanceBasedMetaService]
     * */
    fun getEntityInfo(
        simpleTypeName: String,
        inputOnly: Boolean,
        includePluginExtensions: Boolean,
    ): EntityMetaInfo? {
        val baseInfo = entityMetaInfo[simpleTypeName] ?: return null

        val pluginExtensionFields =
            if (includePluginExtensions) {
                getUninterpolatedPluginExtensionMetaInfo(simpleTypeName, true)
            } else {
                emptyMap()
            }

        val backMappedFields =
            backMappingBuilder.getFieldInfosForEntity(
                getClass(simpleTypeName).kotlin,
            )

        val unfilteredFields = pluginExtensionFields.combine(baseInfo.fields).combine(backMappedFields)

        val resultFields =
            if (inputOnly) {
                (unfilteredFields).filter { it.value.metaInfo["input"] as? Boolean == true }
            } else {
                unfilteredFields
            }

        // create info including extended fields
        return baseInfo.copy(fields = resultFields)
    }

    fun getEntityHash(entity: Class<out ManufacturingEntity>): EntityHash =
        (
            (
                getEntityInfo(entity.simpleName)
                    ?: extensionManager.getMetaInfoForExtensionClass(entity.simpleName)
            )?.versionHash
        )
            ?: entityHashFallbackProvider.ifEmpty(entity)

    /**
     * Fields which are added by a dynamic behaviour are not returned by this method.
     * To get dynamic fields use [com.nu.bom.core.utils.InstanceBasedMetaService]
     * */
    fun getEntityFields(
        entityType: String,
        includeExtensions: Boolean = true,
        originalType: String = entityType,
        interpolationData: InterpolationData,
        includeFieldsWithoutMetaInfo: Boolean = false,
    ): Map<String, FieldInfo> {
        val entityMetaInfo =
            entityMetaInfo[entityType]
                ?: throw IllegalArgumentException("Entity MetaInfo for $entityType is not available!")
        val entityFields = if (includeFieldsWithoutMetaInfo) entityMetaInfo.fields else entityMetaInfo.notEmptyFields

        val extensionFields =
            when {
                includeExtensions && (entityMetaInfo.extends != null) ->
                    getEntityFields(
                        entityMetaInfo.extends,
                        includeExtensions = true,
                        originalType = originalType,
                        interpolationData,
                        includeFieldsWithoutMetaInfo,
                    )

                else -> emptyMap()
            }

        val pluginExtensionFields =
            when (includeExtensions) {
                true -> getUninterpolatedPluginExtensionMetaInfo(entityType, includeFieldsWithoutMetaInfo)
                false -> emptyMap()
            }

        return extensionFields.combine(
            pluginExtensionFields.combine(entityFields).mapValues { (_, fieldInfo) ->
                fieldInfo.copy(
                    metaInfo =
                        interpolateFields(
                            fieldInfo,
                            originalType,
                            interpolationData,
                        ),
                )
            },
        )
    }

    /**
     * Fields which are added by a dynamic behaviour are not returned by this method.
     * To get dynamic fields use [com.nu.bom.core.utils.InstanceBasedMetaService]
     */
    fun getEntityMetaInfo(
        entityType: String,
        includeExtensions: Boolean = true,
        originalType: String = entityType,
        interpolationData: InterpolationData,
    ): Map<String, Map<String, Any>> =
        getEntityFields(entityType, includeExtensions, originalType, interpolationData).mapValues { it.value.metaInfo }

    /**
     * Fields which are added by a dynamic behaviour are not returned by this method.
     * To get dynamic fields use [com.nu.bom.core.utils.InstanceBasedMetaService]
     */
    fun getFieldsByMetaInfo(
        entity: Class<out ManufacturingEntity>?,
        metaInfoKey: String,
        interpolationData: InterpolationData,
    ) = getFieldsByMetaInfo(entity?.simpleName!!, metaInfoKey, interpolationData)

    /**
     * Fields which are added by a dynamic behaviour are not returned by this method.
     * To get dynamic fields use [com.nu.bom.core.utils.InstanceBasedMetaService]
     */
    fun getFieldsByMetaInfo(
        entityClass: String,
        metaInfoKey: String,
        interpolationData: InterpolationData,
    ): List<FieldParameter> {
        val metaInfo = getEntityFields(entityClass, interpolationData = interpolationData)

        return metaInfo
            .filter {
                it.value.metaInfo.containsKey(metaInfoKey) && it.value.metaInfo[metaInfoKey] != false
            }.map {
                it.value
                    .toFieldParameter(UnitOverrideContext.noContext, this)
                    .excludeMetaInfoForMasterdata(ReadOnly.META_INFO)
            }
    }

    /**
     * Gets the fields of the given [entityClass] annotated with [MandatoryForEntity] for the given [context].
     * */
    fun getMandatoryForEntityFields(
        entityClass: Class<out ManufacturingEntity>?,
        context: MandatoryForEntityContext,
        unitOverrideContext: UnitOverrideContext,
        excludeComputedFields: Boolean,
        interpolationData: InterpolationData,
    ): List<FieldParameter> {
        val metaInfo =
            getEntityFields(
                entityClass?.simpleName!!,
                interpolationData = interpolationData,
            )

        return filterMetaInfoAndCollectList(
            metaInfo,
            context,
            unitOverrideContext,
            excludeComputedFields,
        )
    }

    fun filterMetaInfoAndCollectList(
        metaInfo: Map<String, FieldInfo>,
        context: MandatoryForEntityContext,
        unitOverrideContext: UnitOverrideContext,
        excludeComputedFields: Boolean,
    ): List<FieldParameter> =
        metaInfo
            .filter {
                val compositeDto =
                    it.value.metaInfo[CompositeMandatoryForEntity.META_INFO] as CompositeMandatoryForEntityDto?
                val dto =
                    compositeDto?.context?.get(context) ?: compositeDto?.context?.get(MandatoryForEntityContext.ANY)
                (dto?.value == true) && !(excludeComputedFields && dto!!.computed)
            }.mapNotNull {
                serializeMandatoryForEntityAnnotation(
                    context = context,
                    field = it.value.toFieldParameter(unitOverrideContext, this),
                ).takeIf { field ->
                    // return field only if it is mandatory for the current context, and it's enabled
                    field.metaInfo?.containsKey(MandatoryForEntity.META_INFO) == true &&
                        field.metaInfo[MandatoryForEntity.META_INFO] == true
                }
            }.sortedBy { fields ->
                fields.metaInfo?.get(MandatoryForEntity.INDEX) as Int? ?: Int.MAX_VALUE
            }

    /**
     * Serializes a [CompositeMandatoryForEntityDto] metainfo into [MandatoryForEntity] components
     * for the given [field] and [context].
     *
     * Other metainfo entries are untouched. If the metainfo is not present, the whole field is returned untouched.
     * */
    fun serializeMandatoryForEntityAnnotation(
        context: MandatoryForEntityContext,
        field: FieldParameter,
    ): FieldParameter {
        val configs = field.metaInfo?.get(CompositeMandatoryForEntity.META_INFO) as CompositeMandatoryForEntityDto?

        val selectedConfig =
            when (context) {
                MandatoryForEntityContext.CREATE_MANUAL -> configs?.context?.get(MandatoryForEntityContext.CREATE_MANUAL)
                MandatoryForEntityContext.CREATE_FROM_MASTERDATA -> configs?.context?.get(MandatoryForEntityContext.CREATE_FROM_MASTERDATA)
                MandatoryForEntityContext.CREATE_FOR_MASTERDATA -> configs?.context?.get(MandatoryForEntityContext.CREATE_FOR_MASTERDATA)
                MandatoryForEntityContext.CREATE_FROM_EXTERNALDATA ->
                    configs?.context?.get(
                        MandatoryForEntityContext.CREATE_FROM_EXTERNALDATA,
                    )
                MandatoryForEntityContext.ANY -> configs?.context?.get(MandatoryForEntityContext.ANY)
            } ?: configs?.context?.get(MandatoryForEntityContext.ANY)

        return if (selectedConfig != null) {
            field.copy(
                metaInfo =
                    (field.metaInfo?.toMutableMap() ?: mutableMapOf()).apply {
                        // serialize selected config for FE
                        // TODO: make FE aware of the MandatoryForEntityDto structure, and we don't need to split the fields
                        remove(CompositeMandatoryForEntity.META_INFO)
                        set(ReadOnly.META_INFO, selectedConfig.readOnly)
                        set(MandatoryForEntity.META_INFO, selectedConfig.value)
                        set(MandatoryForEntity.INDEX, selectedConfig.index)
                        set(MandatoryForEntity.SECTION, selectedConfig.section)
                        set(MandatoryForEntity.COMPUTED, selectedConfig.computed)
                        set(MandatoryForEntity.REFRESH, selectedConfig.refresh)
                        set(MandatoryForEntity.SHOW_SYSTEM_VALUE, selectedConfig.showSystemValue)
                    },
            )
        } else {
            field
        }
    }

    /**
     * Return a FieldInfo for a field in a particular entity type, it considers the extension mechanism.
     *
     * Fields which are added by a dynamic behaviour are not returned by this method.
     * To get dynamic fields use [InstanceBasedMetaService]
     */
    fun getFieldInfo(
        entity: ManufacturingEntity,
        fieldName: String,
    ): FieldInfo? = getFieldInfo(entity::class.java.simpleName, fieldName)

    /**
     * Return a FieldInfo for a field in a particular entity type, it considers the extension mechanism.
     * @param entityType is the simple name of the entity class, like ManufacturingDieCasting
     *
     * Fields which are added by a dynamic behaviour are not returned by this method.
     * To get dynamic fields use [InstanceBasedMetaService]
     */
    fun getFieldInfo(
        entityType: String,
        fieldName: String,
    ): FieldInfo? {
        val entityInfo = getEntityInfo(entityType)
        return if (entityInfo != null) {
            entityInfo.getFieldInfo(fieldName)?.let { fieldInfo ->
                // This logic only works as long as fields from extensions cannot be overwritten, which I assume, because how should that even work together with extends?
                when (fieldInfo.denominatorUnitInfo) {
                    null ->
                        entityInfo.extends
                            ?.let {
                                getFieldInfo(it, fieldName)?.denominatorUnitInfo
                            }?.let {
                                fieldInfo.copy(denominatorUnitInfo = it)
                            } ?: fieldInfo

                    else -> fieldInfo
                }
            } ?: if (entityInfo.extends != null) {
                getFieldInfo(entityInfo.extends, fieldName)
            } else {
                null
            }
        } else {
            null
            // TODO! behaviours are not handled here and in other places,
            //   but imo we need a general discussion about that, see https://tsetplatform.atlassian.net/browse/COST-15825
        } ?: getPluginExtensionFieldInfo(entityType, fieldName)
    }

    fun addFieldType(clazz: Class<out FieldResultStar>) {
        fieldTypes[clazz.simpleName] = clazz
        val unitAnnotation = clazz.kotlin.findAnnotation<Units>()
        unitAnnotation?.unit?.java?.enumConstants?.forEach {
            addUnit(clazz, it)
        }

        if (SelectEnumFieldResult::class.isSuperclassOf(clazz.kotlin)) {
            val selectableType =
                clazz.kotlin.primaryConstructor
                    ?.parameters
                    ?.first()
                    ?.type
                    ?.jvmErasure
            selectableType?.let {
                selectableTypes[clazz.simpleName] = it.java as Class<out Enum<*>>
            }
        }
    }

    fun getSelectableTypes(clazz: KClass<out FieldResultStar>): Class<out Enum<*>>? = selectableTypes[clazz.simpleName]

    private fun getPluginExtensionFieldInfo(
        entityType: String,
        field: String,
    ): FieldInfo? =
        extensionManager.getUninterpolatedExtensionsMetaInfo(entityType).findFirstNotNull { extensionMetaInfo ->
            extensionMetaInfo.getFieldInfo(field)
        }

    fun getAvailableEntitiesForCreation(
        entityType: Entities,
        subType: String? = null,
    ): List<String> =
        entityMetaInfo
            .filter {
                it.value.entityType == entityType && it.value.userCreatable && it.value.isSubType(subType)
            }.map {
                it.key
            }

    fun getAvailableEntitiesForCreation(entityType: Entities): List<String> =
        entityMetaInfo
            .filter {
                it.value.entityType == entityType && it.value.userCreatable
            }.map {
                it.key
            }

    fun getDefaultEntityForCreation(entityType: Entities): String? =
        entityMetaInfo
            .filter {
                it.value.entityType == entityType && it.value.userCreatable && it.value.default
            }.map {
                it.key
            }.firstOrNull()

    /**
     * Fields which are added by a dynamic behaviour are not returned by this method.
     * To get dynamic fields use [InstanceBasedMetaService]
     */
    fun getEntityMetaInfo(): Map<String, EntityMetaInfo> = entityMetaInfo

    fun getEntityMetaInfo(
        entity: ManufacturingEntity,
        includeExtensions: Boolean = true,
        interpolationData: InterpolationData,
    ): Map<String, Map<String, Any>> =
        getEntityMetaInfo(
            entity::class.simpleName!!,
            includeExtensions = includeExtensions,
            interpolationData = interpolationData,
        )

    fun getUnit(
        type: String,
        unit: String?,
    ): TypeUnit? = units[type.simpleName()]?.get(unit)

    fun addUnit(
        clazz: Class<out FieldResult<*, *>>,
        typeUnit: TypeUnit,
    ) {
        val unitMap = units.getOrPut(clazz.simpleName) { mutableMapOf() }
        unitMap[typeUnit.toString()] = typeUnit
    }

    fun getUnitsIncludingDeprecated(type: String): List<TypeUnit> = units[type.simpleName()]?.values?.toList() ?: emptyList()

    fun getUnits(type: String): List<TypeUnit> = getUnitsIncludingDeprecated(type).filterNot { it.hideInDropdown }

    fun getDenominatorUnitConversionFactor(denominatorUnit: DenominatorUnit): BigDecimal {
        val denominatorType = denominatorUnit.type.toUpperCamelCase()

        return when (denominatorUnit.behavior) {
            DenominatorBehavior.TRANSFORM ->
                getUnit(denominatorType, denominatorUnit.unit)?.baseFactor
                    ?: throw IllegalArgumentException("Could not find unit '${denominatorUnit.unit}' of type '$denominatorType'. ")

            DenominatorBehavior.WRITE_THROUGH -> 1.toBigDecimal()
        }
    }

    // TODO: Better static typing that String for types …

    // Call this if it could be something like ManufacturingEntity.
    fun getMaybeFieldType(type: String): Class<out FieldResultStar>? = fieldTypes[type.simpleName()]

    fun getFieldType(type: String): Class<out FieldResultStar> =
        requireNotNull(getMaybeFieldType(type)) {
            "Could not find type: $type"
        }

    fun addEntity(
        entity: ManufacturingEntity,
        changelogEntries: List<EntityChangelogEntry>,
    ) {
        val clazz = entity::class.java
        addEntity(clazz.simpleName, clazz, entity.extends?.javaClass?.simpleName, changelogEntries, entity)
    }

    private fun addEntity(
        name: String,
        clazz: Class<out ManufacturingEntity>,
        extends: String?,
        changelogEntries: List<EntityChangelogEntry>,
        entity: ManufacturingEntity,
    ) {
        require(!entities.containsKey(name)) {
            "Duplicate Entity $name remove/rename one of them to avoid conflicts ![${entities[name]} and $clazz]"
        }
        entities[name] = clazz

        clazz.getAnnotation(AlternativeNames::class.java)?.let {
            it.alternatives.forEach { alternative ->
                require(
                    !entityAlternativeNames.containsKey(alternative),
                ) { "Duplicate Entity $name remove/rename one of them to avoid conflicts !" }
                entityAlternativeNames[alternative] = name
            }
        }
        clazz.getAnnotation(Modularized::class.java)?.let { annotation ->
            annotation.technologies.forEach { model: Model ->
                modularizedEntities
                    .getOrPut(model) { EnumMap(Entities::class.java) }
                    .getOrPut(entity.getEntityTypeAnnotation()!!) { mutableListOf() }
                    .add(annotation to clazz)

                getMasterDataTypeForEntity(clazz, entity)?.let {
                    modularizedEntitiesToMasterDataType.getOrPut(it) { EnumMap(Model::class.java) }.put(model, clazz)
                }
            }

            val uniqueAnnotationParentModels = annotation.parents.mapTo(mutableSetOf()) { it.model }
            val uniqueTechnologyModels = annotation.technologies.toSet()

            check(uniqueTechnologyModels.containsAll(uniqueAnnotationParentModels)) {
                """Expected parents and technology models differ:
                    |${uniqueTechnologyModels.joinToString(", ")}
                    |
                    |must be a strict superset of
                    |
                    |${uniqueAnnotationParentModels.joinToString(", ")}
                    |
                """.trimMargin()
            }
        }

        addEntityMetaInfo(clazz, extends, changelogEntries)

        addMasterdataInfo(clazz)
    }

    private tailrec fun getMasterDataTypeForEntity(
        clazz: Class<out ManufacturingEntity>,
        entity: ManufacturingEntity,
    ): MasterDataTypeEnum? {
        val extends = entity.extends
        // if we have a master data type annotation, return that
        val value: MasterDataTypeEnum? = clazz.getAnnotation(MasterDataTypeAnnotation::class.java)?.value

        // if we don't, first check if we can keep going or we reached a leaf
        return if (value != null) {
            value
        } else if (extends == null) {
            null
        } else {
            // if it's not a leaf, go for the class it extends
            getMasterDataTypeForEntity(extends::class.java, extends)
        }
    }

    private fun addTechnologyMaterials(metaInfo: EntityMetaInfo) {
        metaInfo.technologies.forEach { tech ->
            val materials = technologyMaterials.getOrPut(tech.name) { mutableListOf() }
            if (materials.isNotEmpty()) {
                require(materials.all { it.masterDataType != metaInfo.masterDataType })
            }
            materials.add(metaInfo)
        }
    }

    private fun addEntityMetaInfo(
        clazz: Class<out ManufacturingEntity>,
        extends: String?,
        changelogEntries: List<EntityChangelogEntry>,
    ) {
        val metaInfo = entityMetaInfoExtractionService.extractEntityMetaInfo(entity = clazz, extends = extends)

        addTechnologyMaterials(metaInfo)
        metaInfo.changelogEntries = changelogEntries
        this.entityMetaInfo[clazz.simpleName] = metaInfo
    }

    private fun addMasterdataInfo(clazz: Class<out ManufacturingEntity>) {
        val masterDataType = clazz.getAnnotation(MasterDataTypeAnnotation::class.java)?.value

        if (masterDataType != null) {
            masterDataEntities[masterDataType] = clazz
        }
        val entityCreationMasterDataType = clazz.getAnnotation(EntityCreationMasterDataType::class.java)?.value

        if (entityCreationMasterDataType != null) {
            masterDataEntitiesForCreation[entityCreationMasterDataType] = clazz
        }
    }

    fun filterAndGetModularizedMasterDataTypes(types: List<MasterDataTypeEnum>): Set<MasterDataTypeEnum> =
        if (types.isEmpty()) {
            modularizedEntitiesToMasterDataType.keys
        } else {
            types.filter { modularizedEntitiesToMasterDataType.contains(it) }.toSet()
        }

    fun modularizedClassForMasterDataType(
        type: MasterDataTypeEnum,
        technologyPath: List<String>,
    ): List<Class<out ManufacturingEntity>> =
        technologyPath
            .map { Model.valueOf(it.uppercase()) }
            .mapNotNull { modularizedEntitiesToMasterDataType[type]?.get(it) }
            .ifEmpty {
                modularizedEntitiesToMasterDataType[type]?.values?.toList() ?: emptyList()
            }

    fun modularizedClassForMasterDataType(
        type: MasterDataTypeEnum,
        technologyPath: String,
    ) = modularizedEntitiesToMasterDataType[type]?.get(Model.valueOf(technologyPath.uppercase()))

    fun getModularizedEntities(): List<Class<out ManufacturingEntity>> {
        return modularizedEntitiesToMasterDataType.values.mapNotNull { it?.values }.flatten()
    }

    fun getEntityKClasses(): List<KClass<out ManufacturingEntity>> = entities.map { it.value.kotlin }

    fun getEntities(): List<Pair<String, String>> =
        entities.map {
            val name = it.key
            val className = it.value
            val masterDataType = className.getAnnotation(EntityType::class.java)?.name
            name to (masterDataType?.name ?: "NONE")
        }

    fun getTypeDefinitions(): List<TypeDefinition> = fieldTypes.values.map { typeClass -> mapType(typeClass) }

    fun mapType(typeClass: Class<out FieldResultStar>): TypeDefinition {
        val unitAnnotation = typeClass.kotlin.findAnnotation<Units>()
        val units =
            unitAnnotation
                ?.unit
                ?.java
                ?.enumConstants
                ?.asList()
        val defaultUnit =
            units?.let { unitsNotNull ->
                unitsNotNull.filterNot { it.hideInDropdown }.find { it.baseFactor.compareTo(BigDecimal.ONE) == 0 }
                    ?: throw Exception("no unit with base factor 1 for '${typeClass.name}'")
            }

        val depreciatedAnnotation = typeClass.kotlin.findAnnotation<Deprecated>()
        val typeDetails = mapTypeDetails(typeClass)

        val selectables =
            if (typeClass.simpleName == "Bool") {
                listOf("true", "false")
            } else {
                selectableTypes[typeClass.simpleName]?.enumConstants?.map { it.name }
            }

        return TypeDefinition(
            type = typeClass.simpleName,
            typeDetails = typeDetails,
            deprecated = depreciatedAnnotation != null,
            units =
                units?.map {
                    UnitDefinition(
                        unit = it.toString(),
                        factor = it.baseFactor,
                        hideInDropdown = it.hideInDropdown,
                        reciprocal = it.reciprocal(),
                    )
                },
            defaultUnit = defaultUnit?.toString(),
            selectables = selectables,
        )
    }

    private fun mapTypeDetails(typeClass: Class<out FieldResultStar>): TypeDetails {
        // handle well known types
        if (SelectEnumFieldResult::class.java.isAssignableFrom(typeClass)) {
            return TypeDetails(TypeCategory.Selectable, primitiveType = PrimitiveType.String)
        }
        if (Bool::class.java.isAssignableFrom(typeClass)) {
            return TypeDetails(TypeCategory.Selectable, primitiveType = PrimitiveType.String)
        }
        if (NumericFieldResultWithUnit::class.java.isAssignableFrom(typeClass)) {
            return TypeDetails(TypeCategory.DecimalWithUnit, primitiveType = PrimitiveType.Decimal)
        }
        if (NumericFieldResult::class.java.isAssignableFrom(typeClass)) {
            return TypeDetails(primitiveType = PrimitiveType.Decimal)
        }
        if (IntegerFieldResult::class.java.isAssignableFrom(typeClass)) {
            return TypeDetails(primitiveType = PrimitiveType.Integer)
        }
        if (Text::class.java.isAssignableFrom(typeClass)) return TypeDetails(primitiveType = PrimitiveType.String)

        // try to figure out the type
        val internalType = findInternalFieldType(typeClass.kotlin) ?: return TypeDetails(TypeCategory.Unknown)
        val argNames = internalType.arguments.map { (it.type?.classifier as KClass<*>).simpleName }
        val className = (internalType.classifier as KClass<*>).simpleName

        if (internalType.classifier == List::class || List::class.isSuperclassOf(internalType.classifier as KClass<*>)) {
            val valueType = toPrimitiveType(argNames[0])?.name ?: argNames[0]
            return TypeDetails(TypeCategory.List, valueType = valueType)
        }

        if (internalType.classifier == Map::class || Map::class.isSuperclassOf(internalType.classifier as KClass<*>)) {
            val keyType = toPrimitiveType(argNames[0])?.name ?: argNames[0]
            val valueType = toPrimitiveType(argNames[1])?.name ?: argNames[1]
            return TypeDetails(TypeCategory.Map, keyType = keyType, valueType = valueType)
        }

        return if (argNames.any()) {
            TypeDetails(TypeCategory.Complex, valueType = "$className<${argNames.joinToString()}>")
        } else {
            val primitiveType = toPrimitiveType(className)
            if (primitiveType != null) {
                TypeDetails(primitiveType = primitiveType)
            } else {
                TypeDetails(TypeCategory.Complex, valueType = "$className")
            }
        }
    }

    private fun toPrimitiveType(name: String?): PrimitiveType? =
        when (name) {
            BigDecimal::class.simpleName -> PrimitiveType.Decimal
            Double::class.simpleName -> PrimitiveType.Decimal
            String::class.simpleName -> PrimitiveType.String
            Int::class.simpleName -> PrimitiveType.Integer
            Boolean::class.simpleName -> PrimitiveType.Boolean
            else -> null
        }

    private fun findInternalFieldType(kClass: KClass<*>): KType? {
        if (!FieldResult::class.isSuperclassOf(kClass)) return null
        val kProperty = kClass.memberProperties.firstOrNull { it.name == FieldResultStar::res.name } ?: return null
        return kProperty.returnType
    }

    fun getClass(name: String): Class<out ManufacturingEntity> =
        getClassOrNull(name) ?: throw IllegalArgumentException("entity not found: $name")

    private fun getClassOrNull(name: String): Class<out ManufacturingEntity>? {
        val lookupName = name.simpleName()
        return entities[lookupName] ?: entityAlternativeNames[lookupName]?.let {
            entities[it]
        }
    }

    fun getKClassOfEntityOrExtensionOrNull(name: String): KClass<out ManufacturingEntity>? {
        // check entities first
        return getClassOrNull(name)?.kotlin ?: extensionManager.getExtensionKClassOrNull(name)
    }

    fun getEntityType(name: String): Entities = ManufacturingTreeUtils.getEntityType(getClass(name).kotlin)

    /** Gets the nested (raw) class containing the actual MasterData fields */
    fun getEntityForMasterDataType(type: MasterDataTypeEnum): Class<out ManufacturingEntity> =
        masterDataEntities[type] ?: throw Exception("Missing master data entity for '$type'.")

    fun getMasterDataTypes(): List<MasterDataTypeEnum> = masterDataEntities.keys.toList()

    /** Gets the entity class used for entity creation from masterdata */
    fun getEntityCreationType(type: MasterDataTypeEnum): Class<out ManufacturingEntity>? =
        masterDataEntitiesForCreation[type] ?: masterDataEntities[type]

    fun materialTypesForTechnology(technology: String): List<MasterDataTypeEnum> {
        val entities = technologyMaterials[technology.uppercase(Locale.getDefault())]
        return entities?.mapNotNull { it.masterDataType } ?: emptyList()
    }

    private fun interpolatePath(
        path: String,
        entityType: String,
        interpolationData: InterpolationData? = null,
    ): String =
        path
            .replace("{entity}", entityType)
            .replace(
                "{masterDataType}",
                (interpolationData?.masterDataType ?: entityMetaInfo[entityType]?.masterDataType)?.toString()
                    ?: "{masterDataType}",
            ).replace("{masterdataKey}", interpolationData?.masterDataKey ?: "{masterdataKey}")
            .replace(
                "{bomPath}",
                interpolationData?.bomNodeId?.let {
                    "/bomNode/$it"
                } ?: "",
            ).replace(
                "{branchPath}",
                interpolationData?.branchId?.let {
                    "/branch/$it"
                } ?: "",
            ).replace(
                "{bomNodeId}",
                interpolationData?.bomNodeId ?: "",
            ).replace(
                "{branchId}",
                interpolationData?.branchId ?: "",
            ).replace("{dimension}", interpolationData?.dimension?.res?.type ?: "{dimension}")
            // match `{field:<fieldName>}`, where `fieldName` can contain letters, digits, and `_` and is replaced
            // by the actual field value
            .replace("""\{field:(?<field>\w+)}""".toRegex()) { match ->
                match.groups["field"]?.value?.let { field ->
                    val entityFieldResult =
                        interpolationData
                            ?.entity
                            ?.getFieldResult(field)
                            ?.takeIf { it !is Null }
                            ?.res
                            ?.toString()
                    entityFieldResult ?: interpolationData?.manualFieldValuesForPath?.get(field)
                } ?: ""
            }.replace(
                "{entityClass}",
                interpolationData?.entity?.getEntityClass() ?: "{entityClass}",
            )

    fun interpolateFields(
        fieldInfo: FieldInfo?,
        entityType: String,
        interpolationData: InterpolationData?,
    ): Map<String, Any> =
        interpolateFields(
            fieldInfo?.metaInfo ?: emptyMap(),
            fieldInfo?.type,
            entityType,
            interpolationData,
        )

    fun interpolateFields(
        metaInfo: Map<String, Any>?,
        type: String?,
        entityType: String,
        interpolationData: InterpolationData?,
    ): Map<String, Any> {
        val result = metaInfo?.toMutableMap() ?: mutableMapOf()

        if (result.containsKey("path")) {
            result.computeIfPresent("path") { _, path ->
                interpolatePath(path as String, entityType, interpolationData)
            }
        }

        setDynamicTranslationLabel(result, interpolationData)
        setFieldBasedUnits(result, interpolationData)

        if (type?.simpleName() == "QuantityUnit") {
            val defaultUnit = interpolationData?.quantityUnit

            // Otherwise, we currently just  don't add the according meta info, because we seem to be not in a
            // situation where we know the context, therefore we also don't need it.
            // Of course, this is very error-prone! If someone passes an empty InterpolationData even though
            // more context is known, the defaultUnit might be missing unexpectedly down the line!
            if (defaultUnit != null) {
                val (key, value) = toMetaInfoEntry(defaultUnit.toString())

                if (result.containsKey(key)) {
                    throw Exception("QuantityUnit field should not already contain a static defaultUnit!")
                }
                result[key] = value
            }
        }

        return result
    }

    private fun setDynamicTranslationLabel(
        metaInfo: MutableMap<String, Any>,
        interpolationData: InterpolationData?,
    ) {
        metaInfo.remove(DynamicTranslationLabel.META_INFO)?.let { labelField ->
            val label = interpolationData?.entity?.getFieldResult(labelField as String)?.res as? String
            label?.let { metaInfo.put(TranslationLabel.META_INFO, it) }
        }
    }

    fun setFieldBasedUnits(
        metaInfo: MutableMap<String, Any>,
        extractor: (String) -> String?,
    ) {
        val numeratorField = metaInfo.remove(FieldBasedUnits.META_INFO_NUMERATOR_FIELD)
        val numeratorValue =
            numeratorField?.let {
                extractor(numeratorField as String)?.also { metaInfo[FieldBasedUnits.META_INFO_NUMERATOR_VALUE] = it }
            }
        metaInfo.remove(FieldBasedUnits.META_INFO_DENOMINATOR_FIELD)?.let { denominatorField ->
            extractor(denominatorField as String)?.let { metaInfo[FieldBasedUnits.META_INFO_DENOMINATOR_VALUE] = it }
        }

        val defaultUnitInfo = defaultUnitInfoFromMetaInfo(metaInfo)
        if (defaultUnitInfo?.unitReferencesField == true) {
            check(defaultUnitInfo.unit == numeratorField) {
                "Expected default unit `${defaultUnitInfo.unit}` to equal field based numerator field `$numeratorField`"
            }
            metaInfo +=
                toMetaInfoEntry(
                    DefaultUnit(
                        numeratorValue ?: "",
                        defaultUnitInfo.isFixed,
                        unitReferencesField = false,
                    ),
                )
        }
    }

    private fun setFieldBasedUnits(
        metaInfo: MutableMap<String, Any>,
        interpolationData: InterpolationData?,
    ) {
        setFieldBasedUnits(metaInfo) { interpolationData?.entity?.getFieldResult(it)?.res as? String }
    }

    fun getExtensions(entity: ManufacturingEntity): List<ManufacturingEntityExtension> =
        extensionManager.getEnabledExtensions(entity)?.map { instantiateExtension(entity, it.extensionClass) }
            ?: listOf()

    fun instantiateExtension(
        extensionPointEntity: ManufacturingEntity,
        extensionClass: Class<out ManufacturingEntityExtension>,
    ): ManufacturingEntityExtension {
        val name = "${extensionPointEntity.name}.${extensionClass.simpleName}"
        val extension =
            extensionClass.getConstructor(String::class.java).newInstance(name) as ManufacturingEntityExtension
        extension.setVersionHashes(this, extensionPointEntity.savedExtensionHashes)
        return extension
    }

    fun getMasterDataNameMap(entityClass: String) =
        getEntityFields(entityClass, interpolationData = InterpolationData())
            .mapNotNull { (name, info) ->
                info.metaInfo[SourceDataInput.META_INFO]?.let { it as String to name }
            }.toMap()

    fun getManualMasterDataSelector(
        entityClass: EntityClassOrName,
        fields: Map<String, FieldResultStar>,
    ) = getEntityInfo(entityClass.asString())?.masterDataType?.let { type ->
        val meta = getEntityMetaInfo(entityClass.asString(), interpolationData = InterpolationData())
        fields
            .firstNotNullOfOrNull { (field, res) ->
                (res.res as? String).takeIf {
                    meta[field]?.get(SourceDataKey.META_INFO) == DataSourceProviders.MASTER_DATA_SOURCE
                }
            }?.let { key -> MasterDataSelector(type, key, null, null) }
    }

    fun getActualCreationClass(
        entityType: Entities,
        baseClass: String?,
        fields: Map<String, FieldResultStar>,
        masterDataSelector: MasterDataSelector?,
    ): EntityClassOrName {
        val metaInfo =
            (baseClass ?: getDefaultEntityForCreation(entityType))
                ?.let { getEntityMetaInfo(it, interpolationData = InterpolationData()) }
                ?: emptyMap()
        val entityClassFieldValue =
            fields
                .toList()
                .find { (name, res) ->
                    res is ClassSelector || metaInfo[name]?.get(ManualClassSelector.META_INFO) as? Boolean ?: false
                }?.second
                ?.res
                ?.toString()

        val entityClass = (entityClassFieldValue ?: baseClass)?.let(::getClass)?.kotlin
        val selectorType = masterDataSelector?.type
        val entityCreationClass =
            when {
                entityClass != null -> entityClass
                selectorType == MasterDataType.NONE -> manualCreationEntityLoader.getDefaultClass(entityType)
                selectorType != null ->
                    getEntityCreationType(selectorType)?.kotlin
                        ?: error("Could not find entity creation class for masterDataType=$selectorType")
                else -> manualCreationEntityLoader.getDefaultClass(entityType)
            }.asEntityClass()
        return entityCreationClass
    }

    fun canBeUsedAsModularized(clazz: KClass<out ManufacturingEntity>): Boolean = getEntityInfo(clazz.java)?.modularized != null

    fun canBeUsedAsModularized(clazz: Class<out ManufacturingEntity>): Boolean = getEntityInfo(clazz)?.modularized != null

    fun getEntitiesForTechnology(
        model: Model,
        dimension: Dimension.Selection?,
    ): List<Class<out ManufacturingEntity>>? =
        modularizedEntities[model]
            ?.values
            ?.flatten()
            ?.filter {
                it.first.dimensions.contains(dimension) || dimension == null
            }?.map { it.second }

    fun getAllModularizedClasses(dimension: Dimension.Selection?): List<Class<out ManufacturingEntity>>? =
        modularizedEntities.values
            .flatMap { it.values }
            .flatten()
            .filter { it.first.dimensions.contains(dimension) || dimension == null }
            .map { it.second }

    private val lazyCopyableEntities: List<Class<out ManufacturingEntity>> by lazy {
        masterDataEntitiesForCreation.values + masterDataEntities.values +
            run {
                entities
                    .filter {
                        it.value.getAnnotation(EntityType::class.java)?.userCreatable ?: false
                    }.map { it.value }
            } + listOf(ManualManufacturing::class.java, ManufacturingStep::class.java)
    }

    fun getCopyableEntities(): List<Class<out ManufacturingEntity>> = lazyCopyableEntities

    fun getModularizedEntities(
        technology: String,
        entityType: Entities,
        dimension: Dimension.Selection?,
        entityClasses: List<String>?,
    ): List<Pair<Modularized, Class<out ManufacturingEntity>>>? {
        val model = Model.fromEntity(technology)
        return modularizedEntities[model]
            ?.get(entityType)
            ?.filter { it.first.dimensions.contains(dimension) || dimension == null }
            ?.filter { (_, klass) -> entityClasses == null || klass.simpleName in entityClasses }
    }

    fun getModularizedTechnologies(
        technology: String?,
        entityTypes: Set<Entities>,
        dimension: Dimension.Selection?,
    ): List<Model> =
        modularizedEntities.entries
            .filter { (model, modularizedClassesPerEntityType) ->
                val isValidTechnology = (technology.isNullOrBlank() || model.entity == technology)
                val hasValidEntities =
                    entityTypes.any { entityType ->
                        modularizedClassesPerEntityType[entityType]?.any { (modEntity, _) ->
                            modEntity.userCreatable && (dimension == null || modEntity.dimensions.contains(dimension))
                        } ?: false
                    }
                isValidTechnology && hasValidEntities
            }.map { it.key }
}

data class InterpolationData(
    val masterDataKey: String? = null,
    val masterDataType: MasterDataTypeEnum? = null,
    val bomNodeId: String? = null,
    val branchId: String? = null,
    val dimension: Dimension? = null,
    val quantityUnit: TypeUnit? = null,
    val entity: ManufacturingEntity? = null,
    val manualFieldValuesForPath: Map<String, String>? = null,
) {
    companion object {
        fun fromEntity(
            entity: ManufacturingEntity,
            bomInfo: InterpolationBomInfo? = null,
        ): InterpolationData {
            val unitOverrideContext = UnitOverrideContext.fromEntity(entity)

            return InterpolationData(
                masterDataKey = entity.masterDataSelector?.key,
                masterDataType = entity.masterDataSelector?.type,
                bomNodeId = bomInfo?.bomNodeId,
                branchId = bomInfo?.branchId,
                dimension = unitOverrideContext.dimension?.let { Dimension(it) },
                quantityUnit = unitOverrideContext.quantityUnitString?.let { dynamicUnitStringToTypeUnit(it) },
                entity = entity,
            )
        }

        fun fromUnitOverrideContext(unitOverrideContext: UnitOverrideContext) =
            InterpolationData(
                masterDataKey = null,
                masterDataType = null,
                bomNodeId = null,
                branchId = null,
                dimension = unitOverrideContext.dimension?.let { Dimension(it) },
                quantityUnit =
                    unitOverrideContext.quantityUnitString?.let {
                        unitOverrideContext.getDynamicUnit(
                            DynamicUnitOverride.QUANTITY_UNIT,
                        )
                    },
            )
    }
}

@JsonInclude(JsonInclude.Include.NON_EMPTY)
data class UnitDefinition(
    val unit: String,
    val factor: BigDecimal,
    val hideInDropdown: Boolean,
    val reciprocal: Boolean?,
)

enum class TypeCategory {
    Primitive,
    DecimalWithUnit,
    Selectable,
    List,
    Map,
    Complex,
    Unknown,
}

enum class PrimitiveType {
    String,
    Decimal,
    Integer,
    Boolean,
}

data class TypeDetails(
    val category: TypeCategory,
    val valueType: String? = null,
    val keyType: String? = null,
) {
    constructor(category: TypeCategory = TypeCategory.Primitive, primitiveType: PrimitiveType) : this(
        category,
        primitiveType.name,
    )
}

@JsonInclude(JsonInclude.Include.NON_EMPTY)
data class TypeDefinition(
    val type: String,
    val typeDetails: TypeDetails,
    val deprecated: Boolean,
    val units: List<UnitDefinition>?,
    val defaultUnit: String?,
    val selectables: List<String>?,
)

data class InterpolationBomInfo(
    val bomNodeId: String? = null,
    val branchId: String? = null,
)
