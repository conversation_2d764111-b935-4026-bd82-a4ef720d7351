package com.nu.bom.core.startup

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.factories.createEntity
import com.nu.bom.core.utils.EntityCollector
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.changelog.EntityChangelogReader
import com.tset.common.util.timedInfo
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.annotation.Configuration
import org.springframework.context.event.EventListener
import org.springframework.core.annotation.Order
import org.springframework.core.io.Resource

const val ENTITY_PACKAGE_NAME = "com.nu.bom.core"
const val CHANGELOG_FILE_NAME = "entity-changelog.log"

@Configuration
class EntityLoader(
    private val entityCollector: EntityCollector,
    private val entityManager: EntityManager,
    @Value("classpath:$CHANGELOG_FILE_NAME") private val changelogResource: Resource,
) {
    private val logger: Logger = LoggerFactory.getLogger(EntityLoader::class.java)

    @EventListener(ApplicationReadyEvent::class)
    @Order(1)
    fun onStartup(event: ApplicationReadyEvent) {
        try {
            load()
        } catch (e: RuntimeException) {
            logger.error("Starting error found. Stopping!", e)
            event.applicationContext.close()
        }
    }

    fun load() {
        logger.timedInfo("Entity loader: Might take several minutes") {
            val entityClasses = entityCollector.findAnnotatedEntityClasses(ENTITY_PACKAGE_NAME, EntityType::class.java)
            loadEntities(entityClasses)
        }
    }

    private fun loadEntities(entityClasses: List<Class<out ManufacturingEntity>>) {
        var count = 0
        val packages = mutableSetOf<String>()
        val changelog = EntityChangelogReader(changelogResource.inputStream).getChangelog()
        entityClasses.forEach { clazz ->
            logger.trace("loading entityclass: ${clazz.canonicalName}")
            val entity = createEntity("entityLoaderObject", clazz)
            if (entity == null) {
                logger.error("could not create entity '$clazz'")
            } else {
                val changelogEntries =
                    changelog.getClassLog(clazz)
//                    ?: throw RuntimeException("Changelog for entity '$clazz' not found!")
                        ?: emptyList() // todo: re-enable entity startup check once distinction between
                entityManager.addEntity(entity, changelogEntries)
                count += 1
                packages.add(clazz.packageName)
            }
        }

        logger.info("Imported $count entity classes from ${packages.size} packages")
    }
}
