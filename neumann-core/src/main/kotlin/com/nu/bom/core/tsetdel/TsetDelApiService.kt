package com.nu.bom.core.tsetdel

import com.nu.bom.core.manufacturing.service.InjectableEngineService
import com.tset.bom.clients.tsetdel.model.TsetDelCoatingAreaRequest
import com.tset.bom.clients.tsetdel.model.TsetDelCoatingAreaResponse
import com.tset.bom.clients.tsetdel.model.TsetDelPolygonUnionAreaRequest
import com.tset.bom.clients.tsetdel.model.TsetDelPolygonUnionAreaResponse
import com.tset.bom.clients.tsetdel.model.TsetDelProjectedAreaRequest
import com.tset.bom.clients.tsetdel.model.TsetDelProjectedAreaResponse
import com.tset.bom.clients.tsetdel.model.TsetDelSliderRequest
import com.tset.bom.clients.tsetdel.model.TsetDelSliderResponse
import reactor.core.publisher.Mono

interface TsetDelApiService : InjectableEngineService {
    fun projectedArea(
        id: String,
        request: TsetDelProjectedAreaRequest,
    ): Mono<TsetDelProjectedAreaResponse>

    fun polygonUnionArea(
        id: String,
        request: TsetDelPolygonUnionAreaRequest,
    ): Mono<TsetDelPolygonUnionAreaResponse>

    fun slider(
        id: String,
        request: TsetDelSliderRequest,
    ): Mono<TsetDelSliderResponse>

    fun coatingArea(
        id: String,
        request: TsetDelCoatingAreaRequest,
    ): Mono<TsetDelCoatingAreaResponse>
}
