package com.nu.bom.core.manufacturing.fieldTypes

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.manufacturing.annotations.Units
import com.nu.bom.core.utils.square
import java.math.BigDecimal
import java.math.MathContext

enum class LengthUnits(
    override val baseFactor: BigDecimal,
) : TypeUnit {
    KILOMETER(1000.0.toBigDecimal()),
    METER(BigDecimal.ONE),
    DECIMETER(0.1.toBigDecimal()),
    CENTIMETER(0.01.toBigDecimal()),
    MILLIMETER(0.001.toBigDecimal()),
    MICROMETER(0.000_001.toBigDecimal()),
    ;

    override val type = TypeUnits.LENGTH
    override val hideInDropdown = false

    companion object {
        private val mdTranslations =
            mapOf(
                "TSET_UNIT_LENGTH_METER" to METER,
                "TSET_UNIT_LENGTH_DECIMETER" to DECIMETER,
                "TSET_UNIT_LENGTH_CENTIMETER" to CENTIMETER,
                "TSET_UNIT_LENGTH_MILLIMETER" to MILLIMETER,
            )

        fun fromString(name: String): LengthUnits = mdTranslations[name] ?: valueOf(name)
    }
}

@Units(LengthUnits::class)
@JsonIgnoreProperties(value = [ "inMeter", "inMillimeter"])
class Length(
    res: BigDecimal,
    unit: LengthUnits,
) : NumericFieldResultWithUnit<Length, LengthUnits>(res, unit) {
    constructor(res: Double, unit: LengthUnits) : this(res.toBigDecimal(), unit)
    constructor(res: String, unit: String) : this(BigDecimal(res), LengthUnits.fromString(unit))
    constructor(res: BigDecimal, unit: String) : this(res, LengthUnits.fromString(unit))

    constructor(field: FieldParameter) : this(field.value!!.toString().toDouble(), LengthUnits.valueOf(field.unit!!))
    constructor(num: Num) : this(num.res, LengthUnits.METER)

    operator fun compareTo(other: Length): Int = res.compareTo(other.res)

    val inKilometer
        get() = to(LengthUnits.KILOMETER)
    val inMeter: BigDecimal
        get() {
            return to(LengthUnits.METER)
        }
    val inDecimeter: BigDecimal
        get() {
            return to(LengthUnits.DECIMETER)
        }
    val inCentimeter: BigDecimal
        get() {
            return to(LengthUnits.CENTIMETER)
        }
    val inMillimeter: BigDecimal
        get() {
            return to(LengthUnits.MILLIMETER)
        }
    val inMicrometer: BigDecimal
        get() {
            return to(LengthUnits.MICROMETER)
        }

    override fun create(res: BigDecimal): Length = Length(res.divide(inputUnit().baseFactor, MathContext.DECIMAL128), inputUnit())

    fun square() = Area(inMeter.square(), AreaUnits.QM)

    companion object {
        val ZERO = Length(BigDecimal.ZERO, LengthUnits.METER)
        val ONE_METER = Length(BigDecimal.ONE, LengthUnits.METER)

        fun create(
            res: String?,
            unit: LengthUnits,
        ): Length? = if (!res.isNullOrBlank()) Length(BigDecimal(res), unit) else null

        fun create(
            outerDiameter: Length,
            innerDiameter: Length,
            thickness: Length,
        ): Length {
            require(outerDiameter > innerDiameter) { "Outer diameter must be greater than inner diameter" }
            require(thickness > ZERO) { "Material thickness must be positive" }

            // Formula for Archimedean spiral L = π * (D_o² - D_i²) / (4 * t)
            return (outerDiameter * outerDiameter - innerDiameter * innerDiameter).times(Math.PI) / (thickness * 4.0)
        }

        /**
         * Calculates the total length of coil layers from outer layer towards the inner part of the coil.
         */
        fun calculateCoilOuterLayerLength(
            outerDiameter: Length,
            coilThickness: Length,
            layers: Num,
        ): Length = calculateCoilLayerLength(outerDiameter, coilThickness, layers)

        /**
         * Calculates the total length of coil layers from inner layer towards the outer part of the coil.
         */
        fun calculateCoilInnerLayerLength(
            innerDiameter: Length,
            coilThickness: Length,
            layers: Num,
        ): Length =
            // negating coilThickness makes the layers "go outwards"
            calculateCoilLayerLength(innerDiameter, coilThickness * -1.0, layers)

        /**
         * Calculates the total length of coil layers.
         *
         * Let *n = layers, r = radius, t = coilThickness*
         *
         * The length of a layer is the circumference (2π×r), therefore the length of the i'th layer is 2π(r-i*t).
         * The sum[from i=0 to n-1] of 2π(r-i×t) can be reduced to the formula:
         * ```
         * 2πn(r - t(n-1) / 2)
         * ```
         */
        private fun calculateCoilLayerLength(
            diameter: Length,
            coilThickness: Length,
            layers: Num,
        ): Length {
            val radius = diameter * 0.5
            require(radius > ZERO)
            require(radius - coilThickness * layers > ZERO)

            return (radius - coilThickness * (layers - 1.0).div(2)) * layers * 2.0 * Math.PI
        }
    }
}
