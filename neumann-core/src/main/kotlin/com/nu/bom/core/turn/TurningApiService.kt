package com.nu.bom.core.turn

import com.nu.bom.core.machining.model.TurningRequest
import com.nu.bom.core.machining.model.TurningResponse
import com.nu.bom.core.turn.model.RawProfileRequest
import com.nu.bom.core.turn.model.RawProfileResponse
import com.nu.bom.core.turn.model.TurningStepsRequest
import com.nu.bom.core.turn.model.TurningStepsResponse
import reactor.core.publisher.Mono

interface TurningApiService {
    fun turn(
        id: String,
        request: TurningRequest<*>,
    ): Mono<TurningResponse>

    fun rawProfile(
        id: String,
        request: RawProfileRequest<*>,
    ): Mono<RawProfileResponse>

    fun steps(
        id: String,
        request: TurningStepsRequest<*>,
    ): Mono<TurningStepsResponse>
}
