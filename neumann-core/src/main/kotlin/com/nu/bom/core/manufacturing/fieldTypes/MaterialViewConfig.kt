package com.nu.bom.core.manufacturing.fieldTypes

class MaterialViewConfig(
    res: MaterialView,
) : SelectEnumFieldResult<MaterialViewConfig.MaterialView, MaterialViewConfig>(res) {
    enum class MaterialView(
        val asString: String,
    ) {
        DEFAULT("MaterialDefaultConfig"),
        MATERIAL_ALU_EXTRUSION("MaterialAluExtrusionConfig"),
        MATERIAL_BENDING("MaterialBendingConfig"),
        MATERIAL_CASTING("MaterialCastingConfig"),
        MATERIAL_CHILL_CASTING("MaterialChillCastingConfig"),
        MATERIAL_CORE_SAND("MaterialCoreSandConfig"),
        MATERIAL_DIE_STAMPING("MaterialDieStampingConfig"),
        MATERIAL_FORGING_AFOR("MaterialForgingAforConfig"),
        MATERIAL_FORGING_CEXT("MaterialForgingCextConfig"),
        MATERIAL_FORGING_CHAT("MaterialForgingChatConfig"),
        MATERIAL_FORGING_CROL("MaterialForgingCrolConfig"),
        MATERIAL_FORGING_DFOR("MaterialForgingDforConfig"),
        MATERIAL_FORGING_RROL("MaterialForgingRrolConfig"),
        MATERIAL_FORGING_RSWA("MaterialForgingRswaConfig"),
        MATERIAL_FORGING_WHAT("MaterialForgingWhatConfig"),
        MATERIAL_LAMINATION_STACK("MaterialLaminationStackConfig"),
        MATERIAL_MAGNET("MaterialMagnetConfig"),
        MATERIAL_MICRO_PLASTIC("MaterialMicroPlasticConfig"),
        MATERIAL_MILLING("MaterialMillingConfig"),
        MATERIAL_PBOX_CORRUGATED_FLUTE("MaterialPboxCorrugatedFluteConfig"),
        MATERIAL_PBOX_INK_BLACK("MaterialPboxInkBlackConfig"),
        MATERIAL_PBOX_INK_CYAN("MaterialPboxInkCyanConfig"),
        MATERIAL_PBOX_INK_MAGENTA("MaterialPboxInkMagentaConfig"),
        MATERIAL_PBOX_INK_YELLOW("MaterialPboxInkYellowConfig"),
        MATERIAL_PBOX_INNER_LINER("MaterialPboxInnerLinerConfig"),
        MATERIAL_PBOX_OUTER_LINER("MaterialPboxOuterLinerConfig"),
        MATERIAL_PBOX_VARNISH("MaterialPboxVarnishConfig"),
        MATERIAL_PCBA_COATING("MaterialPcbaCoatingConfig"),
        MATERIAL_PLASTIC("MaterialPlasticConfig"),
        MATERIAL_PLASTIC2("MaterialPlastic2Config"),
        MATERIAL_PLASTIC_GALVANIZING("MaterialPlasticGalvanizingConfig"),
        MATERIAL_PLASTIC_PAINT("MaterialPlasticPaintConfig"),
        MATERIAL_PRECISION_CASTING("MaterialPrecisionCastingConfig"),
        MATERIAL_RUBBER("MaterialRubberConfig"),
        MATERIAL_SAND_CASTING("MaterialSandCastingConfig"),
        MATERIAL_SAND_COMPOSITION_LARGE_CASTING("MaterialSandCompositionLargeCastingConfig"),
        MATERIAL_SINTERING("MaterialSinteringConfig"),
        MATERIAL_STANDARD_SAND("MaterialStandardSandConfig"),
        MATERIAL_TRANSFER_STAMPING("MaterialTransferStampingConfig"),
        MATERIAL_TURNING_BART("MaterialTurningBartConfig"),
        MATERIAL_TURNING_RAWT("MaterialTurningRawtConfig"),
        MATERIAL_VACUUM_PRECISION_CASTING("MaterialVacuumPrecisionCastingConfig"),
        MATERIAL_WAX_MODEL("MaterialWaxModelConfig"),
        MATERIAL_WAX_RUNNER_SYSTEM("MaterialWaxRunnerSystemConfig"),

        // for the sake of all non direct raw materials....
        EMPTY("EmptyMaterialDefaultConfig"),
    }

    @Suppress("unused")
    companion object {
        val DEFAULT = MaterialViewConfig(MaterialView.DEFAULT)
        val MATERIAL_ALU_EXTRUSION = MaterialViewConfig(MaterialView.MATERIAL_ALU_EXTRUSION)
        val MATERIAL_BENDING = MaterialViewConfig(MaterialView.MATERIAL_BENDING)
        val MATERIAL_CASTING = MaterialViewConfig(MaterialView.MATERIAL_CASTING)
        val MATERIAL_CHILL_CASTING = MaterialViewConfig(MaterialView.MATERIAL_CHILL_CASTING)
        val MATERIAL_CORE_SAND = MaterialViewConfig(MaterialView.MATERIAL_CORE_SAND)
        val MATERIAL_DIE_STAMPING = MaterialViewConfig(MaterialView.MATERIAL_DIE_STAMPING)
        val MATERIAL_FORGING_AFOR = MaterialViewConfig(MaterialView.MATERIAL_FORGING_AFOR)
        val MATERIAL_FORGING_CEXT = MaterialViewConfig(MaterialView.MATERIAL_FORGING_CEXT)
        val MATERIAL_FORGING_CHAT = MaterialViewConfig(MaterialView.MATERIAL_FORGING_CHAT)
        val MATERIAL_FORGING_CROL = MaterialViewConfig(MaterialView.MATERIAL_FORGING_CROL)
        val MATERIAL_FORGING_DFOR = MaterialViewConfig(MaterialView.MATERIAL_FORGING_DFOR)
        val MATERIAL_FORGING_RROL = MaterialViewConfig(MaterialView.MATERIAL_FORGING_RROL)
        val MATERIAL_FORGING_RSWA = MaterialViewConfig(MaterialView.MATERIAL_FORGING_RSWA)
        val MATERIAL_FORGING_WHAT = MaterialViewConfig(MaterialView.MATERIAL_FORGING_WHAT)
        val MATERIAL_LAMINATION_STACK = MaterialViewConfig(MaterialView.MATERIAL_LAMINATION_STACK)
        val MATERIAL_MAGNET = MaterialViewConfig(MaterialView.MATERIAL_MAGNET)
        val MATERIAL_MICRO_PLASTIC = MaterialViewConfig(MaterialView.MATERIAL_MICRO_PLASTIC)
        val MATERIAL_MILLING = MaterialViewConfig(MaterialView.MATERIAL_MILLING)
        val MATERIAL_PBOX_CORRUGATED_FLUTE = MaterialViewConfig(MaterialView.MATERIAL_PBOX_CORRUGATED_FLUTE)
        val MATERIAL_PBOX_INK_BLACK = MaterialViewConfig(MaterialView.MATERIAL_PBOX_INK_BLACK)
        val MATERIAL_PBOX_INK_CYAN = MaterialViewConfig(MaterialView.MATERIAL_PBOX_INK_CYAN)
        val MATERIAL_PBOX_INK_MAGENTA = MaterialViewConfig(MaterialView.MATERIAL_PBOX_INK_MAGENTA)
        val MATERIAL_PBOX_INK_YELLOW = MaterialViewConfig(MaterialView.MATERIAL_PBOX_INK_YELLOW)
        val MATERIAL_PBOX_INNER_LINER = MaterialViewConfig(MaterialView.MATERIAL_PBOX_INNER_LINER)
        val MATERIAL_PBOX_OUTER_LINER = MaterialViewConfig(MaterialView.MATERIAL_PBOX_OUTER_LINER)
        val MATERIAL_PBOX_VARNISH = MaterialViewConfig(MaterialView.MATERIAL_PBOX_VARNISH)
        val MATERIAL_PCBA_COATING = MaterialViewConfig(MaterialView.MATERIAL_PCBA_COATING)
        val MATERIAL_PLASTIC = MaterialViewConfig(MaterialView.MATERIAL_PLASTIC)
        val MATERIAL_PLASTIC2 = MaterialViewConfig(MaterialView.MATERIAL_PLASTIC2)
        val MATERIAL_PLASTIC_GALVANIZING = MaterialViewConfig(MaterialView.MATERIAL_PLASTIC_GALVANIZING)
        val MATERIAL_PLASTIC_PAINT = MaterialViewConfig(MaterialView.MATERIAL_PLASTIC_PAINT)
        val MATERIAL_PRECISION_CASTING = MaterialViewConfig(MaterialView.MATERIAL_PRECISION_CASTING)
        val MATERIAL_RUBBER = MaterialViewConfig(MaterialView.MATERIAL_RUBBER)
        val MATERIAL_SAND_CASTING = MaterialViewConfig(MaterialView.MATERIAL_SAND_CASTING)
        val MATERIAL_SAND_COMPOSITION_LARGE_CASTING = MaterialViewConfig(MaterialView.MATERIAL_SAND_COMPOSITION_LARGE_CASTING)
        val MATERIAL_SINTERING = MaterialViewConfig(MaterialView.MATERIAL_SINTERING)
        val MATERIAL_STANDARD_SAND = MaterialViewConfig(MaterialView.MATERIAL_STANDARD_SAND)
        val MATERIAL_TRANSFER_STAMPING = MaterialViewConfig(MaterialView.MATERIAL_TRANSFER_STAMPING)
        val MATERIAL_TURNING_BART = MaterialViewConfig(MaterialView.MATERIAL_TURNING_BART)
        val MATERIAL_TURNING_RAWT = MaterialViewConfig(MaterialView.MATERIAL_TURNING_RAWT)
        val MATERIAL_VACUUM_PRECISION_CASTING = MaterialViewConfig(MaterialView.MATERIAL_VACUUM_PRECISION_CASTING)
        val MATERIAL_WAX_MODEL = MaterialViewConfig(MaterialView.MATERIAL_WAX_MODEL)
        val MATERIAL_WAX_RUNNER_SYSTEM = MaterialViewConfig(MaterialView.MATERIAL_WAX_RUNNER_SYSTEM)
        val EMPTY = MaterialViewConfig(MaterialView.EMPTY)

        fun getMaterialViewOrDefault(materialViewConfigName: String) =
            MaterialViewConfig(MaterialView.entries.find { it.asString == materialViewConfigName } ?: MaterialView.DEFAULT)
    }
}
