package com.nu.bom.core.api.dtos

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer
import com.nu.bom.core.manufacturing.annotations.MasterDataCalculation
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.user.AccessCheck
import com.tset.bom.clients.common.DenominatorUnit
import com.tset.bom.clients.common.FieldStructure
import com.tset.bom.clients.nuledge.FieldParameterDto
import com.tset.common.util.BARE_BONE_NOT_NULL_STYLE
import com.tset.core.api.dto.CurrencyInfo
import com.tset.core.module.bom.exchangerates.ExchangeRateMap
import com.tset.core.service.domain.Currency
import org.apache.commons.lang3.builder.ToStringBuilder
import reactor.core.publisher.Mono

data class CalculationCreationDto(
    val costModuleEnabled: Boolean,
    val partId: String,
    val fields: List<FieldParameter>,
    val bomEntry: BomEntryCreationDto? = null,
    val parentStepId: String? = null,
    val currency: Currency? = Currency.EUR,
)

data class BomEntryCreationDto(
    val bomNodeId: String?,
    val branchId: String? = null,
    val fields: List<FieldParameter> = emptyList(),
    val path: List<String>,
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class FieldParameter(
    @JsonInclude(JsonInclude.Include.ALWAYS)
    override val name: String,
    @JsonInclude(JsonInclude.Include.ALWAYS)
    override val type: String,
    @JsonInclude(JsonInclude.Include.ALWAYS)
    override val unit: String? = null,
    @JsonInclude(JsonInclude.Include.ALWAYS)
    override val value: Any? = null,
    val valueInDefaultUnit: Any? = null,
    @JsonInclude(JsonInclude.Include.ALWAYS)
    override val source: String? = null,
    val metaInfo: Map<String, Any>? = null,
    @JsonInclude(JsonInclude.Include.ALWAYS)
    val systemValue: Any? = null,
    val label: String? = null,
    val context: FieldContextDto? = null,
    val currencyInfo: CurrencyInfo? = null,
    val systemValueCurrencyInfo: CurrencyInfo? = null,
    // Only used by BulkAction Controller to add the currency info from request
    override val currencyIsoCode: String? = null,
    override val denominatorUnit: DenominatorUnit? = null,
) : FieldStructure {
    constructor() : this("", "", denominatorUnit = null) // FIXME: Remove when Elasticsearch ObjectMapper is fixed

    companion object {
        fun createEmpty(
            name: String,
            type: String,
        ) = FieldParameter(name, type, denominatorUnit = null)

        inline fun <reified Z : FieldResult<T, Z>, T> create(
            name: String,
            value: T,
            unit: String? = null,
            metaInfo: Map<String, Any>? = null,
            denominatorUnit: DenominatorUnit? = null,
        ): FieldParameter =
            FieldParameter(
                name = name,
                type = Z::class.simpleName!!,
                unit = unit,
                value = value,
                metaInfo = metaInfo,
                denominatorUnit = denominatorUnit,
            )
    }

    fun excludeMetaInfoForMasterdata(key: String): FieldParameter =
        if (metaInfo?.containsKey(MasterDataCalculation.META_INFO) != true) {
            excludeMetaInfo(key)
        } else {
            this
        }

    private fun excludeMetaInfo(key: String): FieldParameter = this.copy(metaInfo = metaInfo?.toMutableMap()?.apply { remove(key) })

    fun toFieldParameterDto(): FieldParameterDto =
        FieldParameterDto(
            name = this.name,
            type = this.type,
            value = this.value,
            unit = this.unit,
            source = this.source,
            denominatorUnit = this.denominatorUnit,
        )
}

data class FieldContextDto(
    val deactivatedBy: List<FieldPathDto>,
) {
    // FIXME: Remove when Elasticsearch ObjectMapper is fixed}
    constructor() : this(emptyList())

    override fun toString(): String =
        ToStringBuilder(this, BARE_BONE_NOT_NULL_STYLE)
            .append("deactivatedBy", deactivatedBy)
            .toString()
}

data class FieldPathDto(
    val name: String,
    val path: List<EntityPathDto>,
)

data class CreatedByFieldDto(
    val name: String,
    val type: String,
    val entityId: String,
    val entityType: String,
    val entityRef: String?,
)

data class ProviderFieldDto(
    val name: String,
    val entityId: String,
)

@JsonInclude(JsonInclude.Include.NON_EMPTY)
data class FieldDefinition(
    val name: String,
    val component: String,
    val type: String,
    val units: List<String>?,
    val defaultUnit: String?,
)

data class CalculationModel(
    // TODO: should be label resolved by i18n
    val name: String,
    val path: String,
    val entity: String,
    @JsonSerialize(using = ToStringSerializer::class)
    val displayState: DisplayState = DisplayState.ACTIVE,
    val hasShape: Boolean = true,
    val cardNames: List<String> = DEFAULT_STEPS,
    val isThreeDbEnabled: Boolean = false,
) {
    enum class DisplayState {
        ACTIVE,
        HIDDEN,
    }

    fun getNextCard(cardName: String): String? {
        val idx = cardNames.indexOf(cardName)
        return if (idx != -1 && idx + 1 < cardNames.size) {
            cardNames[idx + 1]
        } else {
            null
        }
    }

    companion object {
        val DEFAULT_STEPS = listOf("TECH", "WAM", "SHAPE", "SPEC")
    }
}

data class CreateManufacturingResult(
    val explorerNode: BomExplorerNode?,
    val bomNode: BomNodeDto,
    val path: List<String>?,
) {
    companion object {
        fun from(
            accessCheck: AccessCheck,
            snapshot: BomNodeSnapshot,
            bomNode: BomNodeDto,
            path: List<String>?,
            exchangeRateMap: ExchangeRateMap,
            fieldConversionService: FieldConversionService,
        ): Mono<CreateManufacturingResult> =
            nodeToExplorerEntry(
                accessCheck,
                node = snapshot,
                branch = snapshot.originalRootSource?.branch,
                isMaster = snapshot.isMainBranch(),
                noBranches = snapshot.node().getAllBranches().count(),
                exchangeRateMap = exchangeRateMap,
                fieldConversionService = fieldConversionService,
            ).map { explorerNode ->
                CreateManufacturingResult(explorerNode, bomNode, path)
            }
    }
}
