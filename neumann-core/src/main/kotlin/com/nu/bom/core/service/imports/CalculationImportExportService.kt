package com.nu.bom.core.service.imports

import com.fasterxml.jackson.annotation.JsonIgnore
import com.google.common.annotations.VisibleForTesting
import com.nu.bom.core.api.dtos.BomNodeDto
import com.nu.bom.core.api.dtos.BomNodeDtoConversionService
import com.nu.bom.core.api.dtos.ExternalDataDto
import com.nu.bom.core.api.dtos.ExternalDataInternal
import com.nu.bom.core.api.dtos.ImportExportDto
import com.nu.bom.core.api.dtos.ManufacturingImportDto
import com.nu.bom.core.api.dtos.MasterDataKeyDto
import com.nu.bom.core.api.dtos.ModuleImportDto
import com.nu.bom.core.api.dtos.NoCalcDataDto
import com.nu.bom.core.api.dtos.PartDto
import com.nu.bom.core.api.dtos.VersionedPartDataDto
import com.nu.bom.core.controller.CalculationResultWithSnapshot
import com.nu.bom.core.exception.readable.ErrorCode
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.ExternalData
import com.nu.bom.core.manufacturing.entities.Attachment
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.BaseManufacturingFields
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.factories.FieldFactoryService
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.ConfigIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ExchangeRatesField
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.FieldWithResult
import com.nu.bom.core.manufacturing.fieldTypes.InternalRef
import com.nu.bom.core.manufacturing.fieldTypes.KnowledgeStepFields
import com.nu.bom.core.manufacturing.fieldTypes.NoCalcData
import com.nu.bom.core.manufacturing.fieldTypes.NoCalcFieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.ThreeDbVersionedPart
import com.nu.bom.core.manufacturing.fieldTypes.VersionedPart
import com.nu.bom.core.manufacturing.service.ConfigurationManagementService
import com.nu.bom.core.manufacturing.service.EntityClassOrName
import com.nu.bom.core.manufacturing.service.ExternalStorageService
import com.nu.bom.core.manufacturing.service.FieldIdKey
import com.nu.bom.core.manufacturing.service.FieldKey
import com.nu.bom.core.manufacturing.service.asEntityClass
import com.nu.bom.core.manufacturing.service.createEmptyFieldKey
import com.nu.bom.core.manufacturing.utils.ManufacturingTreeUtils
import com.nu.bom.core.manufacturing.utils.ManufacturingTreeUtils.ensureRootManufacturingFieldsAreInputs
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.IMasterData
import com.nu.bom.core.model.MasterDataSelector
import com.nu.bom.core.model.Part
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.configurations.CustomCostField
import com.nu.bom.core.model.configurations.CustomField
import com.nu.bom.core.model.configurations.CustomFieldsConfiguration
import com.nu.bom.core.model.configurations.CustomMasterdataClassificationField
import com.nu.bom.core.model.configurations.CustomMasterdataLovField
import com.nu.bom.core.model.toMongoID
import com.nu.bom.core.model.toMongoProjectId
import com.nu.bom.core.publicapi.service.PublicAPICalculationServiceImpl.Companion.getTechnologyModel
import com.nu.bom.core.repository.NoCalcDataWrapper
import com.nu.bom.core.service.AccountMasterDataService
import com.nu.bom.core.service.BomNodeConversionService
import com.nu.bom.core.service.BomNodeService
import com.nu.bom.core.service.ExternalDataService
import com.nu.bom.core.service.ManufacturingCreationService
import com.nu.bom.core.service.ManufacturingEntityFactoryService
import com.nu.bom.core.service.MasterDataService
import com.nu.bom.core.service.PartService
import com.nu.bom.core.service.ProjectService
import com.nu.bom.core.service.bomnode.LoadingMode
import com.nu.bom.core.service.configurations.ConfigType
import com.nu.bom.core.service.configurations.ConfigurationGroup
import com.nu.bom.core.service.configurations.ConfigurationTypeRegistry
import com.nu.bom.core.service.nuledge.NuLedgeService
import com.nu.bom.core.threedb.ThreeDbCalculationCreationInitializerService
import com.nu.bom.core.threedb.ThreeDbHeader
import com.nu.bom.core.threedb.ThreeDbService
import com.nu.bom.core.turn.TurningCalculationCreationInitializerService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.nu.bomrads.dto.admin.ProjectDTO
import com.tset.core.module.bom.exchangerates.ExchangeRateMap
import org.bson.types.ObjectId
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toFlux

@Service
class CalculationImportExportService(
    private val manufacturingCreationService: ManufacturingCreationService,
    private val partService: PartService,
    private val projectService: ProjectService,
    private val masterDataService: MasterDataService,
    private val accountMasterDataService: AccountMasterDataService,
    private val manufacturingEntityFactoryService: ManufacturingEntityFactoryService,
    private val externalDataService: ExternalDataService,
    private val noCalcDataService: ExternalStorageService,
    private val nuLedgeService: NuLedgeService,
    private val bomNodeDtoConversionService: BomNodeDtoConversionService,
    private val bomNodeConversionService: BomNodeConversionService,
    private val bomNodeService: BomNodeService,
    private val configurationManagementService: ConfigurationManagementService,
    private val configurationTypeRegistry: ConfigurationTypeRegistry,
    private val threeDbCalculationCreationInitializerService: ThreeDbCalculationCreationInitializerService,
    private val turningCalculationCreationInitializerService: TurningCalculationCreationInitializerService,
    private val threeDbService: ThreeDbService,
    private val fieldFactoryService: FieldFactoryService,
) {
    companion object {
        val logger: Logger = LoggerFactory.getLogger(CalculationImportExportService::class.java)
        const val NO_CALC_DATA_EXTENSION = ".nocalcdata.json"

        @VisibleForTesting
        @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
        fun applyIncludingChildren(
            accessCheck: AccessCheck,
            dto: ManufacturingImportDto,
            apply: (AccessCheck, ManufacturingImportDto) -> Mono<ManufacturingImportDto>,
        ): Mono<ManufacturingImportDto> =
            apply(accessCheck, dto).flatMap { updatedDto ->
                updatedDto.children
                    .toFlux()
                    .flatMap { applyIncludingChildren(accessCheck, it, apply) }
                    .collectList()
                    .map { updatedChildren ->
                        updatedDto.copy(children = updatedChildren)
                    }
            }
    }

    data class ImportDto(
        val dto: ManufacturingImportDto,
        val entity: ManufacturingEntity,
        val project: ProjectDTO,
        val externalDataMapping: Map<FieldIdKey, List<ExternalIdTypeMapping>>,
        val entitiesWithTemplates: Set<String>,
        val parts: Map<String, Part>,
    )

    data class ExternalIdTypeMapping(
        val id: ObjectId,
        val type: ExternalData.EXTERNAL_DATA_TYPE,
    )

    fun createRootCalculationFromImportDto(
        dto: ManufacturingImportDto,
        externalData: List<ExternalDataDto>,
        noCalcData: List<NoCalcDataDto>,
        versionedPartData: List<VersionedPartDataDto>,
        accessCheck: AccessCheck,
        projectId: ProjectId,
        applyMasterdata: Boolean = false,
    ): Mono<BomNodeDto> =
        projectService.getProject(accessCheck, projectId).flatMap { project ->
            calculateAndConvert(dto, externalData, noCalcData, versionedPartData, accessCheck, project, applyMasterdata)
                .map { (calcResult, data) ->
                    calcResult.result.bomNode.copy(
                        manufacturing =
                            calcResult.result.bomNode.manufacturing?.copy(
                                part =
                                    PartDto.from(
                                        checkNotNull(data.parts[dto.part?.id]),
                                    ),
                            ),
                    ) to data
                }.flatMap { (bomNodeDto, data) ->
                    externalDataService
                        .assignExternalDataUploadsToBomNode(
                            accessCheck,
                            data.externalDataMapping.values.flatten(),
                            BomNodeId(bomNodeDto.id),
                        ).collectList()
                        .thenReturn(bomNodeDto)
                }
        }

    fun calculateAndConvert(
        dto: ManufacturingImportDto,
        externalData: List<ExternalDataDto>,
        noCalcData: List<NoCalcDataDto>,
        versionedPartData: List<VersionedPartDataDto>,
        accessCheck: AccessCheck,
        project: ProjectDTO,
        applyMasterdata: Boolean = false,
        mandatoryFields: List<String> = emptyList(),
    ): Mono<Pair<CalculationResultWithSnapshot, ImportDto>> =
        prepareData(
            accessCheck,
            project,
            dto,
            externalData,
            noCalcData,
            versionedPartData,
            applyMasterdata,
        ).flatMap { data ->
            manufacturingCreationService
                .calculateAndConvertOneBomNodeManyVariants(
                    accessCheck,
                    year = dto.year,
                    projectId = data.project.id,
                    name = dto.name,
                    entities =
                        listOf(
                            ManufacturingCreationService.TitledEntity(
                                entity = data.entity,
                                title = dto.title ?: "Test",
                                mainBranch = true,
                            ),
                        ),
                    uploadId = null,
                    mandatoryFields = mandatoryFields,
                ).map { it.first() to data }
        }

    private data class IdMainImageMapping(
        val id: ObjectId,
        val main: Boolean,
        val type: ExternalData.EXTERNAL_DATA_TYPE,
        val extData: ExternalDataService.ExtData,
    )

    fun prepareData(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        dto: ManufacturingImportDto,
        externalData: List<ExternalDataDto>,
        noCalcData: List<NoCalcDataDto>,
        versionedPartData: List<VersionedPartDataDto>,
        applyMasterdata: Boolean = false,
    ): Mono<ImportDto> =
        projectService.getProject(accessCheck, projectId).flatMap { project ->
            prepareData(accessCheck, project, dto, externalData, noCalcData, versionedPartData, applyMasterdata)
        }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    fun prepareData(
        accessCheck: AccessCheck,
        project: ProjectDTO,
        initialDto: ManufacturingImportDto,
        externalData: List<ExternalDataDto>,
        noCalcData: List<NoCalcDataDto>,
        versionedPartData: List<VersionedPartDataDto>,
        applyMasterdata: Boolean = false,
    ): Mono<ImportDto> {
        val projectId = project.id.toMongoProjectId()
        return checkMasterdata(
            entity = MasterdataImportHelper.handleMasterdataRelatedAdaptions(initialDto),
            apply = applyMasterdata,
            accessCheck = accessCheck,
        ).flatMap { updatedDto ->
            applyIncludingChildren(accessCheck, updatedDto, ::applyThreeDbInitializationExcludingChildren)
        }.flatMap { updatedDto ->
            applyIncludingChildren(accessCheck, updatedDto, ::setConfigIfNotProvidedExcludingChildren)
        }.flatMap { updatedDto ->
            applyIncludingChildren(accessCheck, updatedDto) { acc, dto ->
                applyTurningInitializationExcludingChildren(acc, projectId, dto)
            }
        }.flatMap { updatedDto ->
            createImportDto(
                accessCheck,
                updatedDto,
                project,
                externalData,
                noCalcData,
                versionedPartData,
            )
        }
    }

    private fun applyTurningInitializationExcludingChildren(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        dto: ManufacturingImportDto,
    ): Mono<ManufacturingImportDto> =
        turningCalculationCreationInitializerService
            .createDefaultSketchDependentFieldsIfApplicable(accessCheck, projectId, dto)
            .map {
                if (it.isNotEmpty()) {
                    val turningFieldsAsFieldsWithResult =
                        fieldFactoryService
                            .toFieldResultMap(it)
                            .map { (key, value) ->
                                FieldWithResult(result = value, name = createEmptyFieldKey(key))
                            }
                    dto.copy(initialFieldsWithResult = dto.initialFieldsWithResult + turningFieldsAsFieldsWithResult)
                } else {
                    dto
                }
            }

    fun applyThreeDbInitializationExcludingChildren(
        accessCheck: AccessCheck,
        dto: ManufacturingImportDto,
    ): Mono<ManufacturingImportDto> {
        if (dto.initialFieldsWithResult.find { it.name.name == "versionedPart" } != null) {
            logger.info("skipping threeDb initialization for ${dto.name} since 'versionedPart' already exists")
            return Mono.just(dto)
        }

        var countAlreadyInitialized = 0
        val shapeIdAsString = dto.shapeId()?.res
        val threeDbFieldsMono =
            if (dto.model != null) {
                threeDbCalculationCreationInitializerService
                    .createInitialThreeDbFieldParameterIfApplicable(accessCheck, dto.model, shapeIdAsString)
            } else {
                threeDbCalculationCreationInitializerService
                    .createInitialThreeDbFieldParameterIfApplicable(accessCheck, dto.className, shapeIdAsString)
            }
        return threeDbFieldsMono.map { threeDbFields ->
            if (threeDbFields.isNotEmpty()) {
                threeDbFields.forEach { fieldParameter ->
                    require(dto.initialFieldsWithResult.find { it.name.name == fieldParameter.name } == null)
                    if (dto.fieldsWithResult.find { it.name.name == fieldParameter.name } != null) {
                        countAlreadyInitialized++
                    }
                }
                // for now, forbid partial initializations, wherever they might come from
                require(countAlreadyInitialized in listOf(0, threeDbFields.size))

                val threeDbFieldWithResults =
                    threeDbCalculationCreationInitializerService.toFieldResultMap(threeDbFields).map {
                        FieldWithResult(result = it.value, name = createEmptyFieldKey(it.key))
                    }

                // TODO: regular fields with `Source.I` should be set as `initialFieldsWithResult`,
                //  otherwise they cannot be computed. Presumably this is not the case with 3db fields
                //  since we have our own field-calculator, but for consistency, we should think about unifying that behavior
                dto.copy(fieldsWithResult = dto.fieldsWithResult + threeDbFieldWithResults)
            } else {
                dto
            }
        }
    }

    // Instead of having this function, there should be a "OrEmpty" variant of
    // ConfigurationManagementService::getDefaultCostModuleConfigurationIdentifier,
    // but that would require a chain of changes all the way down to the ConfigurationService.
    private fun hasCostModuleConfiguration(model: Model): Mono<Boolean> =
        configurationTypeRegistry[ConfigurationGroup.COST_MODULES_VERSION.value, model.name]
            .map { true }
            .defaultIfEmpty(false)

    // Note that this is not the only place where initialization of CostModule-Configurations happens
    // (see e.g. `PublicAPICalculationServiceImpl`), so keep that in mind when refactoring this.
    // Hopefully we will have a central definition of such preprocessing/initialization at some point, see COST-81859
    private fun setConfigIfNotProvidedExcludingChildren(
        accessCheck: AccessCheck,
        importDto: ManufacturingImportDto,
    ): Mono<ManufacturingImportDto> {
        val containsConfigIdentifier: (ManufacturingImportDto) -> Boolean = { manu ->
            manu.initialFieldsWithResult.find {
                it.name.name == BaseManufacturingFields::costModuleConfigurationIdentifier.name
            } != null
        }
        val technologyModel = getTechnologyModel(importDto)?.let { Model.fromEntity(it.result.res as String) }
        val modelFromEntity = technologyModel ?: importDto.className?.let { Model.fromEntity(it) }
        if (modelFromEntity == null || containsConfigIdentifier(importDto)) {
            return Mono.just(importDto)
        }
        return hasCostModuleConfiguration(modelFromEntity)
            .flatMap { hasConfiguration ->
                if (hasConfiguration) {
                    configurationManagementService
                        .getDefaultCostModuleConfigurationIdentifier(
                            accessCheck,
                            modelFromEntity,
                        ).map { configurationIdentifier ->
                            val initialFieldConfig =
                                FieldWithResult(
                                    ConfigIdentifier(configurationIdentifier),
                                    createEmptyFieldKey(BaseManufacturingFields::costModuleConfigurationIdentifier.name),
                                )

                            importDto.copy(initialFieldsWithResult = importDto.initialFieldsWithResult + initialFieldConfig)
                        }
                } else {
                    Mono.just(importDto)
                }
            }
    }

    private data class ImportDataMappings(
        val externalDataMapping: Map<FieldIdKey, List<IdMainImageMapping>>,
        val noCalcDataDataMapping: Map<ObjectId, NoCalcDataWrapper>,
        val versionedPartDataMapping: Map<ThreeDbVersionedPart, ThreeDbVersionedPart>,
    )

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun createImportDto(
        accessCheck: AccessCheck,
        dto: ManufacturingImportDto,
        project: ProjectDTO,
        externalData: List<ExternalDataDto>,
        noCalcsData: List<NoCalcDataDto>,
        versionedPartsData: List<VersionedPartDataDto>,
    ): Mono<ImportDto> {
        val projectId = project.id.toMongoProjectId()
        val entitiesWithTemplatesMono = getEntitiesWithTemplates(accessCheck, dto)

        val extDataMappingMono =
            externalData
                .toFlux()
                .flatMap { extData ->
                    val mapping =
                        externalDataService
                            .saveExternalData(
                                extData.type,
                                extData.data,
                                accessCheck,
                                projectId,
                            ).map { data ->
                                IdMainImageMapping(data.id, extData.isMainImage, extData.type, data) to
                                    FieldIdKey(
                                        entityId = extData.field.name.entityId,
                                        name = extData.field.name.name,
                                    )
                            }
                    mapping
                }.collectList()
                .map { it.groupBy({ it.second }, { it.first }) }

        val noCalcDataMappingMono =
            Flux
                .fromIterable(noCalcsData)
                .flatMap { noCalcData ->
                    noCalcDataService.persistAndReturn(noCalcData.data).map { ObjectId(noCalcData.id) to it }
                }.collectMap({ it.first }, { it.second })

        val versionedPartMappingMono =
            versionedPartsData
                .toFlux()
                .flatMap { versionedPartData ->
                    threeDbService
                        .exists(versionedPartData.id.part_id, accessCheck)
                        .flatMap { exists ->
                            if (exists) {
                                Mono.just(versionedPartData.id)
                            } else {
                                threeDbService.uploadEverything(
                                    versionedPartData.data,
                                    accessCheck,
                                )
                            }
                        }.map { newVersionedPart -> Pair(versionedPartData.id, newVersionedPart) }
                }.collectMap({ it.first }, { it.second })

        return Mono
            .zip(
                entitiesWithTemplatesMono,
                extDataMappingMono,
                noCalcDataMappingMono,
                versionedPartMappingMono,
            ).flatMap {
                // > Mom, can we have n-tuple destructuring?
                // > We have n-tuple destructuring at home!
                // > n-tuple destructuring at home:
                actuallyCreateImportDto(
                    accessCheck,
                    dto,
                    project,
                    entitiesWithTemplates = it.t1,
                    importDataMappings = ImportDataMappings(it.t2, it.t3, it.t4),
                )
            }
    }

    private fun actuallyCreateImportDto(
        accessCheck: AccessCheck,
        dto: ManufacturingImportDto,
        project: ProjectDTO,
        entitiesWithTemplates: Set<String>,
        importDataMappings: ImportDataMappings,
    ): Mono<ImportDto> {
        val projectId = project.id.toMongoProjectId()

        return importParts(accessCheck, dto, projectId, project.key, importDataMappings.externalDataMapping)
            .flatMap { parts ->
                prepareManufacturingTree(
                    accessCheck,
                    dto,
                    parts,
                    importDataMappings,
                    entitiesWithTemplates,
                ).map { parts to it }
            }.map { (parts, manu) ->
                ImportDto(
                    dto = dto,
                    project = project,
                    externalDataMapping =
                        importDataMappings.externalDataMapping.mapValues { (_, value) ->
                            value.map {
                                ExternalIdTypeMapping(
                                    it.id,
                                    it.type,
                                )
                            }
                        },
                    entitiesWithTemplates = entitiesWithTemplates,
                    parts = parts.mapValues { it.value.part },
                    entity = manu,
                )
            }.onErrorResume {
                logger.info("Unexpected error during import: " + it.message)
                val deletedFiles =
                    importDataMappings.externalDataMapping.values.flatten().map { extDataItem ->
                        externalDataService.deleteExternalData(
                            accessCheck = accessCheck,
                            id = extDataItem.id.toString(),
                        )
                        extDataItem.id.toString()
                    }
                logger.info("Import - clean up - files marked as deleted : $deletedFiles")
                error("Unexpected error during import: ${it.message}")
            }
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun getEntitiesWithTemplates(
        accessCheck: AccessCheck,
        dto: ManufacturingImportDto,
    ): Mono<Set<String>> {
        fun getKnowledgeEntities(dto: ManufacturingImportDto): List<Pair<String, MasterDataKeyDto>> {
            val children = dto.children.flatMap { getKnowledgeEntities(it) }
            return if (dto.type == Entities.MANUFACTURING_STEP &&
                dto.className == "KnowledgeManufacturingStep" &&
                dto.masterDataKey != null
            ) {
                children + (dto.id to dto.masterDataKey)
            } else {
                children
            }
        }

        return Flux
            .fromIterable(getKnowledgeEntities(dto))
            .flatMap { (entityId, key) -> nuLedgeService.templateExists(accessCheck, key.key).map { entityId to it } }
            .filter { (_, exists) -> exists }
            .map { it.first }
            .collectList()
            .map { it.toSet() }
    }

    @TsetSuppress("tset:reactive:flux-flatmapsequential") // whitelisted already existing calls
    private fun checkMasterdata(
        entity: ManufacturingImportDto,
        apply: Boolean,
        accessCheck: AccessCheck,
    ): Mono<ManufacturingImportDto> {
        val exchangeRateMap =
            (entity.fieldsWithResult.firstOrNull { it.name.name == "exchangeRates" }?.result as ExchangeRatesField?)?.toExchangeRateMap()
                ?: ExchangeRateMap.empty()
        return Flux
            .fromIterable(getMasterDataConnectedEntities(entity))
            .flatMapSequential({
                checkAndCreateMasterdataRecord(it, accessCheck, exchangeRateMap)
            }, 1)
            .collectList()
            .map { masterDataList ->
                if (apply) {
                    val masterData = masterDataList.groupBy { it.selector }.mapValues { it.value.first() }
                    applyMasterData(entity, masterData)
                } else {
                    entity
                }
            }.switchIfEmpty(
                Mono.just(entity),
            )
    }

    private fun applyMasterData(
        entity: ManufacturingImportDto,
        masterDataMap: Map<MasterDataSelector, IMasterData>,
    ): ManufacturingImportDto {
        val children = entity.children.map { applyMasterData(it, masterDataMap) }.toMutableList()
        return if (entity.masterDataKey != null) {
            val masterDataSelector = entity.masterDataKey.toSelector()
            val masterDataLookupResult =
                masterDataMap[masterDataSelector]
                    ?: throw ReadableImportException(
                        ErrorCode.MASTER_DATA_NOT_FOUND,
                        "Master data not found: selector=$masterDataSelector",
                    )
            val masterData =
                masterDataLookupResult.data.mapValues { entry ->
                    FieldWithResult(
                        name =
                            FieldKey(
                                name = entry.key,
                                entityId = entity.id,
                                entityType = entity.type.toString(),
                                type = entry.value.getType(),
                                version = entity.version,
                            ),
                        result = entry.value,
                    )
                }
            val initials =
                (
                    entity.initialFieldsWithResult +
                        // Needed to set dynamic overrides for master data entities if we use public API (for internal API and FE case its possible, but there is separate logic).
                        entity.fieldsWithResult.filter { it.result.source == FieldResult.SOURCE.I }
                )
                    .groupBy { it.name.name }.mapValues { it.value.first() }
            val newInitialMap = initials + masterData
            val newInitials = newInitialMap.toList().map { it.second }

            val newOverrides =
                entity.initialFieldsWithResult.mapNotNull {
                    val newValue = newInitialMap[it.name.name]
                    if (newValue != null && newValue.result.res != it.result.res) {
                        it.withSource(FieldResult.SOURCE.I)
                    } else {
                        null
                    }
                }

            entity.copy(initialFieldsWithResult = newInitials, fieldsWithResult = newOverrides, children = children)
        } else {
            entity.copy(children = children)
        }
    }

    private fun getMasterDataConnectedEntities(entity: ManufacturingImportDto): List<ManufacturingImportDto> =
        if (entity.masterDataKey != null &&
            entity.masterDataKey.type != MasterDataType.EXCHANGE_RATES &&
            entity.masterDataKey.type != MasterDataType.EXCHANGE_RATE
        ) {
            entity.children.flatMap { getMasterDataConnectedEntities(it) } + entity
        } else {
            entity.children.flatMap { getMasterDataConnectedEntities(it) }
        }

    private fun MasterDataKeyDto.toSelector() =
        MasterDataSelector(
            type = this.type,
            key = this.key,
            location = this.location,
            year = this.year,
        )

    private fun checkAndCreateMasterdataRecord(
        entity: ManufacturingImportDto,
        accessCheck: AccessCheck,
        exchangeRateMap: ExchangeRateMap,
    ): Mono<IMasterData> {
        val masterDataKey = checkNotNull(entity.masterDataKey)
        val selector = masterDataKey.toSelector()
        return findMasterdata(
            accessCheck = accessCheck,
            selector = selector,
        ).switchIfEmpty(createMissingMasterdataRecord(entity, selector, accessCheck, exchangeRateMap))
    }

    private fun findMasterdata(
        accessCheck: AccessCheck,
        selector: MasterDataSelector,
    ): Mono<IMasterData> =
        masterDataService
            .getLatestMasterDataByCompositeKey(
                accessCheck = accessCheck,
                selector = selector,
                activeOnly = false,
                mode = MasterDataService.SearchMode.FALLBACK_GLOBAL,
            ).flatMap {
                if (it.active != true) {
                    masterDataService
                        .getLatestAccountMasterDataByCompositeKey(
                            accessCheck = accessCheck,
                            selector = selector,
                            activeOnly = false,
                        ).flatMap { accountMasterData ->
                            masterDataService.saveAccountMasterData(
                                accountMasterData.newVersion(active = true),
                            )
                        }
                } else {
                    Mono.just(it as IMasterData)
                }
            }

    private fun createMissingMasterdataRecord(
        entity: ManufacturingImportDto,
        selector: MasterDataSelector,
        accessCheck: AccessCheck,
        exchangeRateMap: ExchangeRateMap,
    ): Mono<out IMasterData> {
        val masterdataFields = getMasterdataFields(entity)
        return if (masterdataFields.isNotEmpty()) {
            logger.info("creating missing Masterdatarecord $selector")
            accountMasterDataService.updateMasterData(
                accessCheck = accessCheck,
                selector = selector,
                newVersion = 0,
                newFields = masterdataFields,
                isImport = true,
                exchangeRateMap = exchangeRateMap,
            )
        } else {
            Mono.empty()
        }
    }

    private fun getMasterdataFields(entity: ManufacturingImportDto): Map<String, FieldResult<*, *>> {
        val masterdataFields =
            entity.initialFieldsWithResult
                .filter { result ->
                    result.result.source == FieldResult.SOURCE.M
                }.associate {
                    it.name.name to it.result
                }
        return masterdataFields
    }

    private fun prepareManufacturingTree(
        accessCheck: AccessCheck,
        dto: ManufacturingImportDto,
        parts: Map<String, PartWithAttachments>,
        importDataMappings: ImportDataMappings,
        entitiesWithTemplates: Set<String>,
    ): Mono<ManufacturingEntity> {
        val context = ManufacturingTreeUtils.CreateOperationContext()

        // This is a band-aid that assumes the custom fields configuration is never an input.
        // We need a general approach for updating and importing configurations.
        // Likely we'll need to fetch the configurations for each entity in the future.
        return configurationManagementService
            .getDefaultConfiguration(
                accessCheck,
                ConfigType.CustomFields,
            ).ofType(CustomFieldsConfiguration::class.java)
            .map { config ->
                val entity =
                    ensureRootManufacturingFieldsAreInputs(
                        createManufacturingEntityFromDto(
                            accessCheck,
                            dto,
                            parts,
                            importDataMappings,
                            context,
                            entitiesWithTemplates,
                            config,
                        ),
                    )
                logger.info("entities created")
                ManufacturingTreeUtils.updateEntityIdLinks(entity, context)
                entity
            }
    }

    private data class PartWithAttachments(
        val part: Part,
        val attachments: List<IdMainImageMapping>,
    )

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun importParts(
        accessCheck: AccessCheck,
        dto: ManufacturingImportDto,
        projectId: ObjectId,
        projectKey: String,
        externalDataMapping: Map<FieldIdKey, List<IdMainImageMapping>>,
    ): Mono<Map<String, PartWithAttachments>> {
        fun getParts(dto: ManufacturingImportDto): List<Pair<String, PartDto>> =
            dto.children.flatMap { getParts(it) } + (dto.part?.let { listOf(dto.id to it) } ?: listOf())

        val parts = getParts(dto)

        return Flux
            .fromIterable(parts)
            .flatMap { (entityId, partDto) ->
                if (partDto.id != null) {
                    val entry =
                        externalDataMapping[
                            FieldIdKey(
                                entityId = entityId,
                                name = "partImage",
                            ),
                        ]?.sortedBy { !it.main }
                    val partImages = entry?.map { it.id.toHexString() } ?: listOf()

                    partService
                        .createPart(
                            accessCheck = accessCheck,
                            designation = partDto.designation,
                            number = partDto.number,
                            projectId = projectId,
                            images = partImages,
                            projectKey = projectKey,
                            hasMainImage = entry?.any { it.main } ?: false,
                            attachmentToVersionedPart = emptyMap(),
                        ).map { newPart ->
                            partDto.id to PartWithAttachments(newPart, entry ?: emptyList())
                        }
                } else {
                    // It would be nice to import parts without ids. But this is currently not happening, I guess.
                    Mono.empty()
                }
            }.collectList()
            .map { it.toMap() }
    }

    private fun createManufacturingEntityFromDto(
        accessCheck: AccessCheck,
        dto: ManufacturingImportDto,
        parts: Map<String, PartWithAttachments>,
        importDataMappings: ImportDataMappings,
        context: ManufacturingTreeUtils.CreateOperationContext,
        entitiesWithTemplates: Set<String>,
        customFieldsConfig: CustomFieldsConfiguration,
    ): ManufacturingEntity {
        dto.children
            .filter { it.isolated }
            .forEach { child -> context.putCreatedEntity(ObjectId(child.id), ObjectId()) }
        val childrenList =
            dto.children
                .map { child ->
                    createManufacturingEntityFromDto(
                        accessCheck,
                        child,
                        parts,
                        importDataMappings,
                        context,
                        entitiesWithTemplates,
                        customFieldsConfig,
                    )
                }.toMutableList()

        val mapExternalData: (FieldWithResult) -> FieldWithResult = { field ->
            when (field.result) {
                is NoCalcFieldResult -> {
                    importDataMappings.noCalcDataDataMapping.mapKeys { it.key.toHexString() }[field.result.res]?.let { r ->
                        FieldWithResult(
                            name = field.name.copy(version = field.name.version),
                            result =
                                (field.result)
                                    .apply {
                                        data = r.data
                                    }.withRes(r.getId().toHexString()),
                        )
                    } ?: field
                }

                is VersionedPart -> {
                    importDataMappings.versionedPartDataMapping[field.result.res]?.let { versionedPart ->
                        FieldWithResult(
                            name = field.name.copy(version = field.name.version),
                            result =
                                (field.result).withRes(versionedPart),
                        )
                    } ?: field
                }

                else -> {
                    val newId =
                        importDataMappings.externalDataMapping[
                            FieldIdKey(
                                entityId = field.name.entityId,
                                name = field.name.name,
                            ),
                        ]
                    newId?.let {
                        when (newId.size) {
                            1 ->
                                FieldWithResult(
                                    name = field.name.copy(version = field.name.version),
                                    result = (field.result as Text).withRes(newId.first().id.toString()),
                                )

                            0 -> field
                            else -> error("external data should have only one entry")
                        }
                    } ?: field
                }
            }
        }

        val (className, templateInputs) =
            when {
                dto.className == "KnowledgeManufacturingStep" && !entitiesWithTemplates.contains(dto.id) -> {
                    val templateField =
                        dto.fieldsWithResult.find { it.name.name == "template" }?.result as? KnowledgeStepFields
                    val templateInputs = templateField?.res?.data?.toInitialInputs() ?: emptyMap()
                    "ManualManufacturingStep" to templateInputs
                }

                else -> dto.className to emptyMap()
            }

        val customFieldsMap = customFieldsConfig.customFields.associateBy { it.fieldKey }
        val typeMatches = { f: FieldWithResult -> customFieldTypeMatches(customFieldsMap, f) }

        val initialFields =
            dto.initialFieldsWithResult
                .filter(typeMatches)
                .map(mapExternalData)
                .associate { it.name.name to it.result } + templateInputs

        val entityRefsToOverride =
            dto.fieldsWithResult
                .filter {
                    it.name.type == "com.nu.bom.core.manufacturing.fieldTypes.EntityRef" &&
                        (it.result.res as InternalRef).id != null &&
                        context.hasCreatedEntity(checkNotNull((it.result.res as InternalRef).id))
                }.filter(typeMatches)
                .associateBy({ it.name.name }, { context.getCreatedEntity(checkNotNull((it.result.res as InternalRef).id)) })

        val oldFieldWithResults =
            dto.fieldsWithResult
                .filter(typeMatches)
                .map(mapExternalData)

        val args =
            mapOf("user" to accessCheck.userId) +
                if (dto.partName != null && dto.part == null) mapOf("partName" to dto.partName) else mapOf()

        val entity =
            manufacturingEntityFactoryService.createEntity(
                name = dto.name,
                entityType = checkNotNull(dto.type),
                clazz = EntityClassOrName.secondOrNull(className),
                fields = initialFields,
                args = args,
                version = dto.version,
                entityId = if (context.hasCreatedEntity(ObjectId(dto.id))) context.getCreatedEntity(ObjectId(dto.id)) else null,
                entityRefsToOverride = entityRefsToOverride,
                entityRef = dto.ref,
                masterDataSelector =
                    dto.masterDataKey?.let {
                        MasterDataSelector(
                            type = it.type,
                            key = it.key,
                        )
                    },
                isolated = dto.isolated,
                masterDataObjectId = dto.masterDataKey?.usedId?.let { ObjectId(it) },
                masterDataVersion = dto.masterDataKey?.usedVersion,
                oldFieldWithResults = oldFieldWithResults,
            )
        if (entity is BaseManufacturing) {
            if (dto.part?.id != null) {
                val newPart = checkNotNull(parts[dto.part.id]).part
                val attachments = checkNotNull(parts[dto.part.id]).attachments
                entity.setPartInfo(checkNotNull(newPart._id))
                entity.part = newPart
                // We need to recreate the attachment entities since it won't trigger automatically due to no changes detected
                val attachmentEntities =
                    attachments.mapNotNull {
                        (it.extData as? ExternalDataService.ExtData.PartImageExtData)?.uploadResponse?.let { uploadResponse ->
                            it.extData

                            val fields =
                                mutableMapOf<_, FieldResultStar>(
                                    "fileId" to Text(uploadResponse.id),
                                    "mimeType" to Text(uploadResponse.mimeType),
                                    "isMainImage" to
                                        Bool(
                                            uploadResponse.id.toMongoID().toHexString() == newPart.getImage(),
                                        ),
                                    "name" to Text(uploadResponse.filename),
                                    "createdDate" to Text(uploadResponse.createdDate.toString()),
                                )

                            if (uploadResponse.threeDbVersionedPart != null) {
                                fields["versionedPart"] = VersionedPart(uploadResponse.threeDbVersionedPart)
                            }

                            manufacturingEntityFactoryService.createEntity(
                                name = entity.name + ".Attachment",
                                entityType = Entities.ATTACHMENT,
                                clazz = Attachment::class.asEntityClass(),
                                fields = fields,
                            )
                        }
                    }
                childrenList.addAll(attachmentEntities)
            } else if (dto.part?.designation != null) {
                entity.partName = dto.partName ?: "n/a"
            }
        }

        entity.createdBy =
            if (dto.createdByField != null) {
                FieldKey(
                    name = dto.createdByField.name,
                    entityId = dto.createdByField.entityId,
                    entityType = dto.createdByField.entityType,
                    entityRef = dto.createdByField.entityRef,
                    type = dto.createdByField.type,
                    version = 0,
                )
            } else {
                null
            }

        entity.providerField =
            dto.providerField?.let {
                FieldIdKey(
                    name = it.name,
                    entityId = it.entityId,
                )
            }

        entity.children = childrenList

        entity.createdByMocked = dto.createdByMocked ?: false

        entity.dirtyChildLoading = dto.dirtyChildLoading

        entity.isolated = dto.isolated

        entity.dynamicFields =
            dto.dynamicFields.entries
                .associate { (name, dynamicField) ->
                    name to
                        ManufacturingEntity.DynamicField(
                            entityClass = dynamicField.entityClass,
                            entityFieldName = dynamicField.entityFieldName,
                        )
                }.toMutableMap()

        context.putCreatedEntity(
            source = ObjectId(dto.id),
            target = entity._id,
        )
        return entity
    }

    private fun customFieldTypeMatches(
        customFields: Map<String, CustomField>,
        field: FieldWithResult,
    ): Boolean =
        customFields[field.name.name]?.let { customField ->
            when (customField) {
                is CustomCostField -> customField.fieldType
                // Note that we cannot match the lov type key as that is metadata...
                is CustomMasterdataLovField, is CustomMasterdataClassificationField -> "Text"
            } == checkNotNull(field.result::class.simpleName)
        } ?: true

    fun getModuleImportDto(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        bomNodeId: BomNodeId,
        branchId: BranchId?,
    ): Mono<ModuleImportDto> {
        // Nice to have: This should call bomNodeLoaderService.getSnapshotLoadingResult(...)
        return bomNodeService
            .getNodesRecursive(
                accessCheck,
                loadingMode = LoadingMode.CALCULATION_CONTEXT,
                nodeId = bomNodeId,
                branch = branchId,
            ).map { snapshot ->
                bomNodeConversionService.bomNodeToManufacturingCalculationTree(snapshot, null)
            }.map {
                bomNodeDtoConversionService.manufacturingEntityToModuleDto(
                    it,
                    accessCheck.asAccountId(),
                    checkNotNull(it.snapshot?.projectId),
                )
            }
    }

    fun getManufacturingImportDto(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
        branchId: BranchId?,
        recursive: Boolean,
    ): Mono<ImportExportDto> {
        // Nice to have: This should call bomNodeLoaderService.getSnapshotLoadingResult(...)
        return bomNodeService
            .getNodesRecursive(
                accessCheck,
                loadingMode = if (recursive) LoadingMode.CALCULATION_CONTEXT else LoadingMode.LAZY_CHILDREN,
                nodeId = bomNodeId,
                branch = branchId,
            ).map { snapshot ->
                bomNodeConversionService.bomNodeToManufacturingCalculationTree(snapshot, null)
            }.map {
                bomNodeDtoConversionService.manufacturingEntityToImportExportDto(
                    it,
                )
            }.flatMap { (dto, additionalData) ->
                addExternalData(additionalData.first, additionalData.second, accessCheck, dto, additionalData.third)
            }
    }

    data class FileUpload(
        val name: String,
        val base64Content: String,
        @JsonIgnore
        val pureContent: ByteArray?,
        val zipEntryName: String?,
    ) : ExternalDataService.ExternalDto

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun addExternalData(
        externalData: List<ExternalDataInternal>,
        noCalcData: List<BomNodeDtoConversionService.NoCalcFieldWithKey>,
        accessCheck: AccessCheck,
        dto: ManufacturingImportDto,
        versionedParts: Set<ThreeDbVersionedPart>,
    ): Mono<ImportExportDto> {
        val versionedPartsMono =
            Flux
                .fromIterable(versionedParts)
                .flatMap { versionedPart ->

                    val header =
                        ThreeDbHeader(
                            accessCheck = accessCheck,
                            threeDbVersionedPart = versionedPart,
                        )

                    threeDbService.downloadEverything(header).map { downloaded ->
                        Pair(versionedPart, downloaded)
                    }
                }.collectList()
                .map { it.toMap() }

        val externalDataMono =
            Flux
                .fromIterable(externalData)
                .flatMap { extDataDto ->
                    externalDataService
                        .getExternalData(
                            extDataDto.type,
                            extDataDto.field.result.res as String,
                            accessCheck,
                        ).map { data ->
                            extDataDto.addData(data = data)
                        }
                }.collectList()

        val noCalcDataDtoMono =
            Flux
                .fromIterable(noCalcData)
                .distinct { it.fieldResult.objectId }
                .concatMap { field ->
                    val objectId = field.fieldResult.objectId
                    (
                        field.fieldResult.data?.let { Mono.just(it) }
                            ?: noCalcDataService
                                .retrieve(objectId)
                                .onErrorResume {
                                    logger.debug(
                                        "Cannot retrieve no calc data by object id {} for field {}.{}, " +
                                            "fallback to object plus account id",
                                        objectId,
                                        field.key.entityType,
                                        field.key.name,
                                    )
                                    noCalcDataService.retrieve(objectId, accessCheck.asAccountId())
                                }.onErrorResume {
                                    logger.warn(
                                        "Cannot retrieve no calc data by object id {} for field {}.{}, skipping",
                                        objectId,
                                        field.key.entityType,
                                        field.key.name,
                                    )
                                    Mono.empty()
                                }
                    ).map { data: NoCalcData ->
                        NoCalcDataDto(objectId.toHexString(), data)
                    }
                }.collectList()

        return Mono.zip(externalDataMono, noCalcDataDtoMono, versionedPartsMono).map { triple ->
            ImportExportDto(
                manufacturingDto = dto,
                externalData = triple.t1,
                noCalcData = triple.t2,
                versionedParts = triple.t3.entries.map { VersionedPartDataDto(id = it.key, data = it.value) },
            )
        }
    }
}
