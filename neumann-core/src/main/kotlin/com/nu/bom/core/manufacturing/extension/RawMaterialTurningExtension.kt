package com.nu.bom.core.manufacturing.extension

import com.nu.bom.core.manufacturing.annotations.AsObject
import com.nu.bom.core.manufacturing.annotations.Condition
import com.nu.bom.core.manufacturing.annotations.DontSortOptions
import com.nu.bom.core.manufacturing.annotations.Extends
import com.nu.bom.core.manufacturing.annotations.Hidden
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MasterDataCalculation
import com.nu.bom.core.manufacturing.annotations.Selectable
import com.nu.bom.core.manufacturing.entities.ManufacturingEntityExtension
import com.nu.bom.core.manufacturing.entities.RawMaterialBar
import com.nu.bom.core.manufacturing.entities.RawMaterialCastingAlloy
import com.nu.bom.core.manufacturing.entities.RawMaterialPipe
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Text
import reactor.core.publisher.Mono

@Extends([RawMaterialBar::class, RawMaterialPipe::class, RawMaterialCastingAlloy::class])
class RawMaterialTurningExtension(name: String) : ManufacturingEntityExtension(name) {
    data class MaterialGroupLookup(
        val group: String,
        val name: String,
        val turning: Boolean,
        val milling: Boolean,
        val usableforQuenched: Boolean,
        val canBeQuenched: Boolean,
        val canBeHardened: Boolean,
    )

    private val materialGroupLookupReader: (row: List<String>) -> MaterialGroupLookup = {
        MaterialGroupLookup(
            group = it[0],
            name = it[1],
            turning = it[2].toBoolean(),
            milling = it[3].toBoolean(),
            usableforQuenched = it[4].toBoolean(),
            canBeQuenched = it[5].toBoolean(),
            canBeHardened = it[6].toBoolean(),
        )
    }

    @Selectable("materialGroup")
    @Input
    @AsObject
    @DontSortOptions
    @MasterDataCalculation
    val materialGroup: Text = Text("P1")

    @Condition(field = "askForCaseHardeningPossible", value = "true", operator = Condition.EQUALS)
    @MasterDataCalculation
    @Input
    fun caseHardeningPossibleUserInput(askForCaseHardeningPossible: Bool): Bool = askForCaseHardeningPossible

    @MasterDataCalculation
    fun caseHardeningPossible(
        askForCaseHardeningPossible: Bool,
        caseHardeningPossibleUserInput: Bool,
    ): Bool = Bool(askForCaseHardeningPossible.res && caseHardeningPossibleUserInput.res)

    @Condition(field = "askForQuenchingPossible", value = "true", operator = Condition.EQUALS)
    @MasterDataCalculation
    @Input
    fun quenchingPossibleUserInput(askForQuenchingPossible: Bool): Bool = askForQuenchingPossible

    @MasterDataCalculation
    fun quenchingPossible(
        askForQuenchingPossible: Bool,
        quenchingPossibleUserInput: Bool,
    ): Bool = Bool(askForQuenchingPossible.res && quenchingPossibleUserInput.res)

    @Selectable("materialGroupQuenched")
    @Condition(field = "showMaterialGroupQuenchedUserInput", value = "true", operator = Condition.EQUALS)
    @Input
    @AsObject
    @DontSortOptions
    @MasterDataCalculation
    val materialGroupQuenchedUserInput: Text = Text("P3")

    @Hidden
    @MasterDataCalculation
    fun showMaterialGroupQuenchedUserInput(
        quenchingPossible: Bool,
        askForQuenchingPossible: Bool,
    ): Bool {
        return Bool(quenchingPossible.res && askForQuenchingPossible.res)
    }

    @MasterDataCalculation
    fun materialGroupQuenched(
        materialGroupQuenchedUserInput: Text,
        quenchingPossible: Bool,
        askForQuenchingPossible: Bool,
    ) = if (quenchingPossible.res && askForQuenchingPossible.res) {
        materialGroupQuenchedUserInput
    } else {
        null
    }

    @MasterDataCalculation
    @Hidden
    fun askForCaseHardeningPossible(materialGroup: Text?): Mono<Bool> =
        services.getLookupTable("Materialgroups", materialGroupLookupReader)
            .filter { it.group == materialGroup?.res }
            .map { Bool(it.canBeHardened) }
            .collectList()
            .mapNotNull {
                it.firstOrNull()
            }

    @MasterDataCalculation
    @Hidden
    fun askForQuenchingPossible(materialGroup: Text?): Mono<Bool> {
        return services.getLookupTable("Materialgroups", materialGroupLookupReader)
            .filter { it.group == materialGroup?.res }
            .map { Bool(it.canBeQuenched) }
            .collectList()
            .map {
                it.firstOrNull() ?: Bool(false)
            }
    }
}
