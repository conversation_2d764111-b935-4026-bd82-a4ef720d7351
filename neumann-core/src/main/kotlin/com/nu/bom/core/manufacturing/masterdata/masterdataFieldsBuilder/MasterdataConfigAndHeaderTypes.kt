package com.nu.bom.core.manufacturing.masterdata.masterdataFieldsBuilder

import com.nu.bom.core.model.configurations.CurrentMasterdataConfiguration
import com.nu.masterdata.dto.v1.header.HeaderTypeResponseDto

class MasterdataConfigAndHeaderTypes(
    val config: CurrentMasterdataConfiguration,
    val headerTypeInfoOverheads: HeaderTypeResponseDto?,
    val headerTypeInfoInterests: HeaderTypeResponseDto?,
    val headerTypeInfoExchangeRates: HeaderTypeResponseDto?,
    val headerTypeInfoWages: HeaderTypeResponseDto?,
    val headerTypeInfoLaborBurdens: HeaderTypeResponseDto?,
    val headerTypeInfoElectricityPrices: HeaderTypeResponseDto?,
    val headerTypeInfoNaturalGasPrices: HeaderTypeResponseDto?,
    val headerTypeInfoElectricityEmissions: HeaderTypeResponseDto?,
    val headerTypeInfoNaturalGasEmissions: HeaderTypeResponseDto?,
    val headerTypeInfoFloorSpacePrices: HeaderTypeResponseDto?,
    val headerTypeInfoCostFactorInterests: HeaderTypeResponseDto?,
    val headerTypeInfoAluminiumShares: HeaderTypeResponseDto?,
    val headerTypeInfoAluminiumEmissions: HeaderTypeResponseDto?,
    val headerTypeInfoCastExcipientsPrice: HeaderTypeResponseDto?,
    val headerTypeInfoOxygenPrice: HeaderTypeResponseDto?,
    val headerTypeInfoCountryInfo: HeaderTypeResponseDto?,
    val headerTypeInfoMaterialPrice: HeaderTypeResponseDto?,
    val headerTypeInfoMaterialCo2: HeaderTypeResponseDto?,
)
