package com.nu.bom.core.service

import com.nu.bom.core.api.dtos.BomEntryCreationDto
import com.nu.bom.core.api.dtos.CalculationCreationDto
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.model.BomEntryCreationData
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.WizardData
import com.nu.bom.core.model.createBranchId
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.getOptional
import com.nu.bom.core.utils.getRequired
import com.nu.bom.core.utils.getUnitOverrideContext
import com.tset.core.api.calculation.dto.CalculationCreationModalMode
import com.tset.core.api.calculation.dto.CalculationPosition
import com.tset.core.api.calculation.dto.CalculationUpdateContextDto
import com.tset.core.api.calculation.dto.CalculationUpdateData
import com.tset.core.api.calculation.dto.CalculationUpdateInputDto
import com.tset.core.api.calculation.dto.CalculationUpdatePayloadDto
import com.tset.core.service.domain.Currency
import com.tset.core.service.domain.calculation.CalculationType
import org.bson.types.ObjectId
import org.springframework.stereotype.Service

@Service
class CalculationCreationDataConversionService {

    fun convert(creationDto: CalculationCreationDto, entityManager: EntityManager): WizardData {

        val calculationTitle = creationDto.fields.getRequired("calculationTitle", Text::class).value as String
        val unitOverrideContext = creationDto.fields.getUnitOverrideContext(entityManager)
        val bomEntry = creationDto.bomEntry

        return if (bomEntry != null) {

            val stepId = bomEntry.fields.getRequired("stepId", Text::class).value as String

            return WizardData(
                calculationType = getSelectedType(creationDto),
                partName = creationDto.fields.getOptional("partName")?.value as String?,
                calculationTitle = calculationTitle,
                fields = EcoFieldsSanitizer.sanitize(creationDto.fields),
                bomEntry = BomEntryCreationData(
                    bomNodeId = BomNodeId(bomEntry.bomNodeId),
                    branchId = createBranchId(bomEntry.branchId),
                    stepId = ObjectId(stepId),
                    fields = BomEntryFieldsSanitizer.sanitize(bomEntry.fields),
                    path = bomEntry.path
                ),
                calculationUpdateData = CalculationUpdatePayloadDto(
                    input = CalculationUpdateInputDto(
                        mode = CalculationCreationModalMode.CALCULATION_MODE_NEW,
                        position = CalculationPosition.SUB,
                        originalType = null,
                        context = CalculationUpdateContextDto(bomEntry.bomNodeId, bomEntry.branchId, stepId),
                        currency = Currency.EUR
                    ),
                    data = CalculationUpdateData(
                        fields = EcoFieldsSanitizer.sanitize(creationDto.fields),
                        parentBomData = BomEntryCreationDto(
                            bomNodeId = bomEntry.bomNodeId,
                            branchId = bomEntry.branchId,
                            fields = BomEntryFieldsSanitizer.sanitize(bomEntry.fields),
                            path = bomEntry.path
                        )
                    ),
                    selectedType = getSelectedType(creationDto)
                ),
                unitOverrideContext = unitOverrideContext
            )
        } else {
            WizardData(
                calculationType = getSelectedType(creationDto),
                partName = creationDto.fields.getOptional("partName")?.value as String?,
                calculationTitle = calculationTitle,
                fields = EcoFieldsSanitizer.sanitize(creationDto.fields),
                calculationUpdateData = CalculationUpdatePayloadDto(
                    input = CalculationUpdateInputDto(
                        mode = CalculationCreationModalMode.CALCULATION_MODE_NEW,
                        position = CalculationPosition.ROOT,
                        originalType = null,
                        context = CalculationUpdateContextDto(bomNodeId = null, branchId = null, stepId = null),
                        currency = Currency.EUR
                    ),
                    data = CalculationUpdateData(
                        fields = EcoFieldsSanitizer.sanitize(creationDto.fields),
                        parentBomData = null
                    ),
                    selectedType = getSelectedType(creationDto)
                ),
                unitOverrideContext = unitOverrideContext
            )
        }
    }

    private fun getSelectedType(creationDto: CalculationCreationDto) = CalculationType.valueOf(
        creationDto.fields.getRequired("calculationType", "CalculationType").value as String
    )
}
