package com.nu.bom.core.service.bomrads

import com.nu.bom.core.api.dtos.ExtraNodeInfo
import com.nu.bom.core.exception.userException.BomNodeNotFoundException
import com.nu.bom.core.model.BomEntryRelation
import com.nu.bom.core.model.BomNode
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.SnapshotId
import com.nu.bom.core.model.toBomNodeId
import com.nu.bom.core.model.toBranchId
import com.nu.bom.core.model.toMongoBomNodeId
import com.nu.bom.core.model.toMongoObjectId
import com.nu.bom.core.model.toMongoProjectId
import com.nu.bom.core.model.toMongoSnapshotId
import com.nu.bom.core.model.toObjectId
import com.nu.bom.core.model.toProjectId
import com.nu.bom.core.service.PartService
import com.nu.bom.core.service.PostPublishService
import com.nu.bom.core.service.bomnode.ExtraNodeInfoHandlerService
import com.nu.bom.core.service.bomnode.SnapshotLoaderService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.nu.bom.core.utils.associateByNotNull
import com.nu.bom.core.utils.component1
import com.nu.bom.core.utils.component2
import com.nu.bomrads.dto.BomExplorerResponseDTO
import com.nu.bomrads.dto.BranchViewDTO
import com.nu.bomrads.dto.NodeSnapshotDTO
import com.nu.bomrads.id.BomNodeId
import com.nu.bomrads.id.BranchId
import com.nu.bomrads.id.ManufacturingTreeId
import com.nu.bomrads.id.ProjectId
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toFlux
import java.util.Date
import com.nu.bom.core.model.BomNodeId as MongoBomNodeId
import com.nu.bom.core.model.BranchId as MongoBranchId
import com.nu.bom.core.model.ProjectId as MongoProjectId

@Service
class BomNodeLoaderService(
    private val bomradsBomNodeService: BomradsBomNodeService,
    private val snapshotLoaderService: SnapshotLoaderService,
    private val extraNodeInfoHandlerService: ExtraNodeInfoHandlerService,
    private val objectMapperService: BomradsObjectMapperService,
    private val partService: PartService,
) {
    companion object {
        private val logger = LoggerFactory.getLogger(BomNodeLoaderService::class.java)
    }

    data class EnrichedBranchView(
        val branchViewDTO: BranchViewDTO,
        val extraNodeInfoMap: Map<BomNodeId, ExtraNodeInfo>,
    )

    data class SnapshotLoadingResult(
        val branchViewDTO: BranchViewDTO,
        val snapshotMap: Map<MongoBomNodeId, BomNodeSnapshot>,
        val subNodeTreeRoot: BomNodeId?,
    ) {
        fun getRoot(): BomNodeSnapshot {
            val rootId =
                if (subNodeTreeRoot != null) {
                    subNodeTreeRoot
                } else {
                    val fakeRoot =
                        branchViewDTO.snapshots().find { it.root }
                            ?: run {
                                logger.info("No root node found in ${branchViewDTO.toSnapshotListAsString()}")
                                throw BomNodeNotFoundException(id = null)
                            }
                    fakeRoot.bomNodeId()
                }
            return snapshotMap[rootId.toMongoBomNodeId()]
                ?: run {
                    logger.info("Node not found with nodeId=$subNodeTreeRoot, available: nodeIds=${snapshotMap.keys}")
                    throw BomNodeNotFoundException(subNodeTreeRoot?.idToString())
                }
        }

        fun getNode(bomNodeId: BomNodeId) = snapshotMap[bomNodeId.toMongoBomNodeId()]

        fun toPair(): Pair<BomNodeSnapshot, BranchViewDTO> = getRoot() to branchViewDTO
    }

    data class BomExplorerLoadingResult(
        val snapshotsAndBranches: BomExplorerResponseDTO,
        val snapshotMap: Map<MongoBomNodeId, BomNodeSnapshot>,
        val extraNodeInfoMap: Map<MongoBomNodeId, ExtraNodeInfo>,
    )

    /**
     * Return the full BomNode tree, if limitSubNodeTree is null. Otherwise, only returns BomNodes starting with the given BomNodeId.
     * This is necessary to support cases, where the bomNodeId is not the root of the calculation context.
     */
    fun getBomNodeTree(
        accessCheck: AccessCheck,
        bomNodeId: MongoBomNodeId,
        branchId: MongoBranchId?,
        loadingMode: LoadingMode,
        loadSourceChangeset: Boolean = false,
    ): Mono<Pair<BomNodeSnapshot, BranchViewDTO>> =
        bomradsBomNodeService
            .getBranchViewFromNode(
                accessCheck,
                branchId = branchId?.toBranchId(),
                bomNodeId = bomNodeId.toBomNodeId(),
                loadingMode = loadingMode,
                loadSourceChangeset = loadSourceChangeset,
            ).flatMap { dto ->
                loadSnapshotsRoot(accessCheck, dto, loadingMode)
            }

    fun getEnrichedBranchView(
        accessCheck: AccessCheck,
        bomNodeId: MongoBomNodeId,
        branchId: MongoBranchId?,
        loadingMode: LoadingMode,
    ): Mono<EnrichedBranchView> {
        val nodeId = bomNodeId.toBomNodeId()
        return bomradsBomNodeService
            .getBranchViewFromNode(
                accessCheck,
                branchId = branchId?.toBranchId(),
                bomNodeId = nodeId,
                loadingMode = loadingMode,
                loadSourceChangeset = false,
            ).flatMap { dto ->
                val (relevantSnapshots, _) =
                    collectNodesToLoad(
                        dto,
                        loadingMode,
                    )
                val snapshotsWithExtraNodeInfo =
                    relevantSnapshots.map { snapshot ->
                        snapshot to objectMapperService.deserializeExtraNodeInfo(snapshot)
                    }

                loadExtraNodes(
                    accessCheck,
                    dto.projectId,
                    snapshotsWithExtraNodeInfo,
                ) { snapshot ->
                    snapshot.currentOrPreviousTreeId()
                }.map { extraNodes ->
                    EnrichedBranchView(
                        branchViewDTO = dto,
                        extraNodeInfoMap =
                            extraNodes
                                .filter {
                                    it.second != null
                                }.associate {
                                    it.first.bomNodeId to it.second!!
                                },
                    )
                }
            }
    }

    private fun <X> loadExtraNodes(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        nodes: List<Pair<X, ExtraNodeInfo?>>,
        getId: (X) -> ManufacturingTreeId,
    ): Mono<List<Pair<X, ExtraNodeInfo?>>> {
        val (withExtraNodeInfo, withoutExtraNodes) = nodes.partition { it.second != null }
        if (withoutExtraNodes.isEmpty()) {
            return Mono.just(withExtraNodeInfo as List<Pair<X, ExtraNodeInfo>>)
        }
        val snapshotIds = withoutExtraNodes.map { getId(it.first).toMongoSnapshotId() }
        return extraNodeInfoHandlerService
            .loadSnapshotsAndUpdateMeta(accessCheck, projectId, snapshotIds)
            .map { extraNodeInfoMap ->
                nodes.map { (node, extraNodeInfo) ->
                    val id = getId(node).toMongoSnapshotId()
                    val freshExtraNodeInfo = extraNodeInfo ?: extraNodeInfoMap[id]
                    if (freshExtraNodeInfo == null) {
                        logger.warn("Unable to load snapshot=$id in $projectId for $accessCheck - known properties: $node")
                    }
                    node to freshExtraNodeInfo
                }
            }
    }

    /**
     * Return all the latest BomNodeSnapshot for a given project and branch
     */
    fun getBranchView(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        branchId: MongoBranchId?,
        loadingMode: LoadingMode? = null,
    ): Mono<List<BomNodeSnapshot>> =
        getSnapshotLoadingResult(
            accessCheck,
            projectId,
            loadingMode = loadingMode,
            branchId = branchId?.toBranchId(),
        ).map {
            it.snapshotMap.values.toList()
        }

    /**
     * Return a SnapshotLoadingResult - which contains both the BranchViewDTO from Bomrads, and the relevant BomNodeSnaphosts
     */
    fun getSnapshotLoadingResult(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        branchId: BranchId?,
        loadingMode: LoadingMode? = null,
    ): Mono<SnapshotLoadingResult> =
        bomradsBomNodeService
            .getBranchView(
                accessCheck,
                projectId,
                loadingMode = loadingMode,
                loadSourceChangeset = false,
                branchId = branchId,
            ).flatMap { dto ->
                loadAllSnapshots(accessCheck, dto)
            }

    /**
     * Return the bomNode tree starting from the given BomNodeId, so result.bomNodeId == param.bomNodeId.
     */
    fun getBomNode(
        accessCheck: AccessCheck,
        bomNodeId: MongoBomNodeId,
        branchId: MongoBranchId?,
        loadingMode: LoadingMode,
    ): Mono<BomNodeSnapshot> =
        getBomNodeTree(
            accessCheck,
            bomNodeId,
            branchId,
            loadingMode = loadingMode,
        ).map { (node, _) -> node }

    fun getBomNodeTreeForUpdateCompatibility(
        accessCheck: AccessCheck,
        nodeId: MongoBomNodeId?,
        sourceBranch: MongoBranchId?,
        loadingMode: LoadingMode,
    ): Mono<Pair<BomNodeSnapshot, BranchViewDTO>> {
        val bomNodeId = nodeId?.toBomNodeId()
        return bomradsBomNodeService
            .getBomNodeTreeForUpdateCompatibility(
                accessCheck,
                branchId = sourceBranch?.toBranchId(),
                bomNodeId = bomNodeId,
            ).flatMap { dto ->
                loadSnapshotsRoot(
                    accessCheck,
                    dto,
                    loadingMode = loadingMode,
                )
            }
    }

    fun getBomNodeTreeForUpdate(
        accessCheck: AccessCheck,
        projectId: MongoProjectId,
        nodeId: MongoBomNodeId,
        sourceBranch: MongoBranchId,
        loadingMode: LoadingMode,
    ) = bomradsBomNodeService
        .getBomNodeTreeForUpdate(
            accessCheck,
            projectId.toProjectId(),
            sourceBranch.toBranchId(),
            nodeId.toBomNodeId(),
        ).flatMap { loadSnapshotsRoot(accessCheck, it, loadingMode) }

    /**
     * Load all the BomNodeSnapshots, either limited to the given BomNodeId, or every BomNodeId.
     */
    fun loadSnapshotsRoot(
        accessCheck: AccessCheck,
        dto: BranchViewDTO,
        loadingMode: LoadingMode,
    ): Mono<Pair<BomNodeSnapshot, BranchViewDTO>> =
        loadSnapshots(
            accessCheck,
            dto,
            loadingMode = loadingMode,
        ).map { loadingResult ->
            loadingResult.toPair()
        }

    /**
     * Load all the BomNodeSnapshots from a BranchView.
     */
    fun loadAllSnapshots(
        accessCheck: AccessCheck,
        dto: BranchViewDTO,
    ): Mono<SnapshotLoadingResult> =
        loadSnapshots(
            accessCheck,
            dto,
            loadingMode = All,
        )

    /**
     * Load all the BomNodeSnapshots, either limited to the given BomNodeId, or every BomNodeId.
     */
    fun loadSnapshots(
        accessCheck: AccessCheck,
        dto: BranchViewDTO,
        loadingMode: LoadingMode,
    ): Mono<SnapshotLoadingResult> {
        val (relevantSnapshots, calculationRootId) =
            collectNodesToLoad(
                dto,
                loadingMode,
            )
        val snapshotIds =
            relevantSnapshots
                .map { nodeSnapshot ->
                    val ext = nodeSnapshot.externalParents.mapNotNull { it.treeId()?.toMongoSnapshotId() }
                    ext + nodeSnapshot.currentOrPreviousTreeId().toMongoSnapshotId()
                }.flatten()
                .distinct()

        return loadSelectedSnapshots(accessCheck, relevantSnapshots, snapshotIds, dto, calculationRootId)
    }

    /**
     * Load the snapshot lists, and link together with the BomNodeSnapshots, and for all the snapshot in the 'selectedSnapshots' list, create new one
     */
    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls

    private fun loadSelectedSnapshots(
        accessCheck: AccessCheck,
        selectedSnapshots: Collection<NodeSnapshotDTO>,
        snapshotIds: Collection<SnapshotId>,
        dto: BranchViewDTO,
        calculationRootId: BomNodeId?,
        snapshotIdsToBomNodeIds: Map<SnapshotId, BomNodeId> = emptyMap(),
    ): Mono<SnapshotLoadingResult> =
        snapshotLoaderService
            .loadSnapshots(accessCheck, snapshotIds)
            .flatMap { snapshotMapping ->
                Flux
                    .fromIterable(snapshotMapping.values)
                    .flatMap { partService.addPartInfo(accessCheck, it) }
                    .then(Mono.just(snapshotMapping))
            }.map { snapshotMapping ->
                snapshotMapping.values.forEach { mongoSnapshot ->
                    mongoSnapshot.setBranchView(dto)
                }
                val mappingByBomNodeId = getMappingByBomNodeIdFromBr(snapshotMapping, dto, snapshotIdsToBomNodeIds)
                val newMapping = convertAll(mappingByBomNodeId, dto, selectedSnapshots)
                SnapshotLoadingResult(
                    snapshotMap = newMapping,
                    branchViewDTO = dto,
                    subNodeTreeRoot = calculationRootId,
                )
            }

    private fun getMappingByBomNodeIdFromBr(
        snapshotMapping: Map<SnapshotId, BomNodeSnapshot>,
        bomradsBranchView: BranchViewDTO,
        snapshotIdsToBomNodeIds: Map<SnapshotId, BomNodeId>,
    ): Map<List<BomNodeId>, BomNodeSnapshot> {
        val mappingFromBomrads =
            bomradsBranchView.snapshots().groupBy { snapshot ->
                val mongoSnapshotId =
                    checkNotNull(snapshot.currentOrPreviousTreeId().toMongoSnapshotId()) {
                        "Both treeId and previousTreeId are null"
                    }
                mongoSnapshotId
            }
        return snapshotMapping
            .mapNotNull { (snapshotId, mongoSnapshot) ->
                val sourceMongoSnapshotId = mongoSnapshot.originalSource?.currentOrPreviousTreeId()?.toMongoSnapshotId()
                val bomNodeIds =
                    (
                        mappingFromBomrads[mongoSnapshot.id()]
                            ?: mappingFromBomrads[sourceMongoSnapshotId]
                    )?.map { it.bomNodeId() } ?: snapshotIdsToBomNodeIds[snapshotId]?.let { listOf(it) }
                if (bomNodeIds == null) {
                    null
                } else {
                    bomNodeIds to mongoSnapshot
                }
            }.toMap()
    }

    fun loadSnapshotsForBranchView(
        accessCheck: AccessCheck,
        selectedSnapshots: Collection<NodeSnapshotDTO>,
        dto: BranchViewDTO,
    ): Mono<SnapshotLoadingResult> =
        loadSelectedSnapshots(
            accessCheck,
            selectedSnapshots = selectedSnapshots,
            dto = dto,
            calculationRootId = null,
            snapshotIds =
                selectedSnapshots.map {
                    it.currentOrPreviousTreeId().toMongoSnapshotId()
                },
        )

    fun loadSnapshotsForCalculationStep(
        accessCheck: AccessCheck,
        branchView: BranchViewDTO,
        snapshotIdsToBomNodeIds: Map<SnapshotId, BomNodeId>,
        step: PostPublishService.CalculationStep,
    ): Mono<SnapshotLoadingResult> =
        loadSelectedSnapshots(
            accessCheck,
            selectedSnapshots = step.snapshots,
            snapshotIds = snapshotIdsToBomNodeIds.keys,
            dto = branchView,
            calculationRootId = step.root.bomNodeId,
            snapshotIdsToBomNodeIds = snapshotIdsToBomNodeIds,
        )

    /**
     * this method loads snapshots and ExtraNodeInfo for the bom Explorer. It follows very specific semantics:
     *
     *  - The <code>BomExplorerLoadingMode</code> specifies which nodes to load.
     *  - Either all root nodes (without children) are loaded or specific nodes with or without children
     *  - For all loaded nodes <code>ExtraNodeInfo</code> is returned. Preferably the ExtraNodeInfo is loaded from bomrads,
     *  if not found in bomrads its loaded from MongoDb
     *  - For all nodes where the request defines <code>withChildren=true</code> <b>and</b> when the <code>treeId</code> in the request
     *  for this node is not the current snapshotId or it is null, the <code>BomNodeSnapshot</code> is returned
     *
     *
     */
    fun loadSnapshotsForBomExplorer(
        accessCheck: AccessCheck,
        loadingMode: BomExplorerLoadingMode,
    ): Mono<BomExplorerLoadingResult> =
        bomradsBomNodeService
            .getSnapshotsForBomExplorer(
                accessCheck,
                loadingMode.projectId,
                loadingMode.convertToNodeSnapshotRequest(),
            ).flatMap {
                snapshotsToBomExplorerLoadingResult(accessCheck, it, loadingMode)
            }

    fun snapshotsToBomExplorerLoadingResult(
        accessCheck: AccessCheck,
        snapshotsAndBranches: BomExplorerResponseDTO,
        loadingMode: BomExplorerLoadingMode,
    ): Mono<BomExplorerLoadingResult> {
        val snapshots = snapshotsAndBranches.snapshots
        val bomradsExtraNodeInfosNullable =
            snapshots.associateBy(
                { it.bomNodeId },
                { objectMapperService.deserializeExtraNodeInfo(it) },
            )

        val filteredNodes = loadingMode.filterNodes(snapshots, bomradsExtraNodeInfosNullable)
        val nodesToReturn = filteredNodes.map { it.first }

        val snapshotsToLoadWithoutMeta =
            filteredNodes
                .filter { it.second }
                .filter { bomradsExtraNodeInfosNullable[it.first.bomNodeId] == null }
                .map { it.first.currentOrPreviousTreeId().toMongoSnapshotId() }
        val snapshotIdsToLoadWithMeta =
            filteredNodes
                .filter { it.second }
                .map { it.first.currentOrPreviousTreeId().toMongoSnapshotId() }
                .filter { !snapshotsToLoadWithoutMeta.contains(it) }

        logger.debug(
            "loading {} snapshots for bom explorer from mongodb",
            snapshotsToLoadWithoutMeta.size + snapshotIdsToLoadWithMeta.size,
        )

        // we use the extraNodeInfoHandlerService to load all snapshots where the meta field is missing
        // the extraNodeInfoHandlerService then takes care of updating the meta field in bomrads in the background
        // for all snapshots where the meta field is not missing (i.e. snapshotIdsToLoadWithMeta) we
        // use the normal snapshotLoaderService
        val monoSnapshotsAndExtra =
            if (snapshotsToLoadWithoutMeta.isEmpty()) {
                Mono.just(emptyMap())
            } else {
                extraNodeInfoHandlerService.loadAndReturnExtraNodeInfoSnapshotsAndUpdateMeta(
                    accessCheck = accessCheck,
                    projectId = loadingMode.projectId,
                    snapshotIds = snapshotsToLoadWithoutMeta,
                )
            }
        val monoSnapshots =
            snapshotLoaderService
                .loadSnapshots(
                    accessCheck,
                    snapshotIdsToLoadWithMeta,
                ).map { bomNodeSnapshotMap ->
                    bomNodeSnapshotMap.forEach { (snapshotId, bomNodeSnapshot) ->
                        val snapshot =
                            requireNotNull(snapshots.firstOrNull { it.currentOrPreviousTreeId().toMongoSnapshotId() == snapshotId }) {
                                "We need the bomNode information from bomrads"
                            }
                        bomNodeSnapshot.bomNodeId = snapshot.bomNodeId.toMongoBomNodeId()
                        bomNodeSnapshot.protectedAt = snapshot.protectedAt
                        bomNodeSnapshot.protectedBy = snapshot.protectedBy
                        snapshotId to bomNodeSnapshot
                    }
                    bomNodeSnapshotMap
                }
        return Mono
            .zip(monoSnapshotsAndExtra, monoSnapshots)
            .flatMap { (loadedSnapshotsAndExtra, loadedSnapshots) ->
                val allSnapshots = loadedSnapshotsAndExtra.values.map { it.second } + loadedSnapshots.values
                loadedSnapshotsAndExtra
                    .filter { it.value.second.bomNodeId == null }
                    .forEach {
                        val snapshot =
                            requireNotNull(snapshots.firstOrNull { sn -> sn.currentOrPreviousTreeId().toMongoSnapshotId() == it.key }) {
                                "We need the bomNode information from bomrads"
                            }
                        it.value.second.bomNodeId = snapshot.bomNodeId.toMongoBomNodeId()
                        it.value.second.protectedAt = snapshot.protectedAt
                        it.value.second.protectedBy = snapshot.protectedBy
                    }

                val additionalLoadedExtraNodeInfos = loadExtraNodeInfos(loadedSnapshots, accessCheck)
                additionalLoadedExtraNodeInfos.collectMap({ it.first }, { it.second }).map { map ->
                    val mongoDbExtraNodeInfos =
                        loadedSnapshotsAndExtra.entries.associate {
                            it.value.second
                                .bomNodeId()
                                .toBomNodeId() to it.value.first
                        } + map

                    val extraNodeInfos =
                        snapshots.associateByNotNull(
                            { it.bomNodeId.toMongoBomNodeId() },
                            { mongoDbExtraNodeInfos[it.bomNodeId] ?: bomradsExtraNodeInfosNullable[it.bomNodeId] },
                        )

                    BomExplorerLoadingResult(
                        BomExplorerResponseDTO(nodesToReturn, snapshotsAndBranches.branchInfos),
                        allSnapshots.associateBy { it.bomNodeId() },
                        extraNodeInfos,
                    )
                }
            }
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun loadExtraNodeInfos(
        snapshots: Map<SnapshotId, BomNodeSnapshot>,
        accessCheck: AccessCheck,
    ): Flux<Pair<BomNodeId, ExtraNodeInfo>> =
        snapshots.values.toFlux().flatMap { snap ->
            objectMapperService.toExtraNodeInfo(accessCheck, snap).map {
                snap.bomNodeId().toBomNodeId() to it
            }
        }

    private fun collectNodesToLoad(
        dto: BranchViewDTO,
        loadingMode: LoadingMode,
    ): Pair<Collection<NodeSnapshotDTO>, BomNodeId?> = loadingMode.filter(dto) to loadingMode.nodeId

    private fun convertAll(
        bomnodeIdTosnapshotMap: Map<List<BomNodeId>, BomNodeSnapshot>,
        dto: BranchViewDTO,
        relevantSnapshots: Collection<NodeSnapshotDTO>,
    ): Map<MongoBomNodeId, BomNodeSnapshot> {
        val newSnapshots =
            relevantSnapshots.map {
                convertToMongo(bomnodeIdTosnapshotMap, dto, it)
            }
        val branch = objectMapperService.bomradsToMongoBranch(dto)
        val branchId = branch.id()
        newSnapshots.forEach { snapshot ->
            snapshot.branch = branchId
            snapshot.branchEntity = branch
            if (dto.branch().main()) {
                snapshot.node().lastPublishedBranch = branchId
            }
            snapshot.setAsHeadVersion()
            snapshot.parentsToUse = emptyList()
            snapshot.subNodes = emptyList()
        }
        val newSnapshotMap = newSnapshots.associateBy { it.id() }
        // and link the parent child relations together ...
        newSnapshots.forEach { snapshot ->
            convertRelations(newSnapshotMap, snapshot, dto)
        }
        return newSnapshots.associateBy { it.bomNodeId() }
    }

    private fun convertToMongo(
        snapshotMap: Map<List<BomNodeId>, BomNodeSnapshot>,
        dto: BranchViewDTO,
        node: NodeSnapshotDTO,
    ): BomNodeSnapshot {
        val mongoProjectId = dto.projectId().toMongoProjectId()
        val mongoBomNodeId = toMongoBomNodeId(node)
        val mongoSnapshotId = node.currentOrPreviousTreeId().toMongoSnapshotId()

        val snapshot =
            checkNotNull(
                snapshotMap.entries
                    .find { entry ->
                        val snapshotList = entry.key
                        snapshotList.any { it.toMongoBomNodeId() == mongoBomNodeId }
                    }?.value,
            ) {
                "Unable to find bomNodeId=$mongoBomNodeId in keys=${snapshotMap.keys}\n\t" +
                    "- bomNodeTree is : ${dto.toSmallSnapshotListAsString()}"
            }

        val newSnapshot =
            snapshot.copy(
                name = node.name(),
                title = node.title(),
                year = node.year,
                //  kpi = node.kpi().toInternal(),
            )
        newSnapshot._id = mongoSnapshotId
        newSnapshot.setBranchView(dto, node)
        newSnapshot.partId = snapshot.partId
        newSnapshot.partName = snapshot.partName
        // TODO: get these date
        newSnapshot.markAsPersisted()
        newSnapshot.createdDate = Date.from(node.created)
        newSnapshot.lastModifiedDate = snapshot.lastModifiedDate
        newSnapshot.lastMigrationChangeSetId = snapshot.lastMigrationChangeSetId
        newSnapshot.calculationRoot = node.calculationRoot.toMongoBomNodeId()
        newSnapshot.protectedAt = node.protectedAt
        newSnapshot.protectedBy = node.protectedBy
        val bomNode =
            BomNode(
                name = node.name(),
                projectId = mongoProjectId,
                year = node.year,
                partId = snapshot.partId,
                status = node.status,
            )
        bomNode._id = mongoBomNodeId
        newSnapshot.bomNode = bomNode
        return newSnapshot
    }

    private fun convertRelations(
        snapshotMap: Map<SnapshotId, BomNodeSnapshot>,
        mongoSnapshot: BomNodeSnapshot,
        dto: BranchViewDTO,
    ) {
        val bomradSnapshot: NodeSnapshotDTO = mongoSnapshot.originalSource!!

        bomradSnapshot.allChildren().forEach { child ->
            val childSnapshot = getSnapshotIfLoaded(dto, child.bomNodeId(), snapshotMap)
            val bomEntryId = child.bomEntryId()!!.toMongoObjectId()
            if (childSnapshot != null) {
                // replace or add if not found the parent relation side
                childSnapshot.replaceParentRelation(BomEntryRelation(snapshot = mongoSnapshot, bomEntryId = bomEntryId))
                // replace or add if not found
                mongoSnapshot.replaceChildRelation(BomEntryRelation(snapshot = childSnapshot, bomEntryId = bomEntryId))
            } else {
                mongoSnapshot.replaceChildRelation(
                    BomEntryRelation(
                        bomEntryId = bomEntryId,
                        bomNodeId = child.bomNodeId().id.toObjectId(),
                        version = null,
                    ),
                )
            }
        }
        bomradSnapshot.allParents().forEach { extParent ->
            val parentSnapshot = getSnapshotIfLoaded(dto, extParent.bomNodeId(), snapshotMap)
            val bomEntryId = extParent.bomEntryId()!!.toMongoObjectId()
            if (parentSnapshot != null) {
                mongoSnapshot.replaceParentRelation(
                    BomEntryRelation(
                        snapshot = parentSnapshot,
                        bomEntryId = bomEntryId,
                    ),
                )
                // replace or add the child relation side
                parentSnapshot.replaceChildRelation(BomEntryRelation(snapshot = mongoSnapshot, bomEntryId = bomEntryId))
            } else {
                mongoSnapshot.replaceParentRelation(
                    BomEntryRelation(
                        bomEntryId = bomEntryId,
                        bomNodeId = extParent.bomNodeId().id.toObjectId(),
                        version = null,
                    ),
                )
            }
        }
    }

    private fun getSnapshotIfLoaded(
        dto: BranchViewDTO,
        bomNodeId: BomNodeId,
        snapshotMap: Map<SnapshotId, BomNodeSnapshot>,
    ): BomNodeSnapshot? {
        val snapshotId = dto.findByBomNodeId(bomNodeId)?.currentOrPreviousTreeId()?.toMongoSnapshotId()
        return if (snapshotId != null) {
            snapshotMap[snapshotId]
        } else {
            null
        }
    }

    private fun toMongoBomNodeId(dto: NodeSnapshotDTO): MongoBomNodeId = dto.bomNodeId().toMongoBomNodeId()
}
