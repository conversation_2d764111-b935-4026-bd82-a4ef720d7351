package com.nu.bom.core.service.migration.lazy.mappers

import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.JsonMappingException
import com.nu.bom.core.manufacturing.entities.BaseMaterial
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.masterdata.MasterdataEntity
import com.nu.bom.core.manufacturing.entities.masterdata.material.MasterdataMaterialEmissionEntity
import com.nu.bom.core.manufacturing.entities.masterdata.material.MasterdataMaterialParent
import com.nu.bom.core.manufacturing.entities.masterdata.material.MasterdataMaterialPriceEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.DynamicBehaviourGeneration
import com.nu.bom.core.manufacturing.fieldTypes.EmissionUnits
import com.nu.bom.core.manufacturing.fieldTypes.EntityGeneration
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.MasterdataConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.MdLookupRequestField
import com.nu.bom.core.manufacturing.fieldTypes.MdLookupRequestFieldData
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.masterdata.operationsAndExecutionContext.MasterdataContextOperationConfigBuilders
import com.nu.bom.core.manufacturing.service.FieldKey
import com.nu.bom.core.manufacturing.service.behaviour.ConfigBasedDynamicBehaviour
import com.nu.bom.core.manufacturing.service.virtualfield.DataSourcerUpdaterProvider
import com.nu.bom.core.model.configurations.ConfigId
import com.nu.bom.core.model.configurations.SemanticVersion
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.InputDependencyModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.publicapi.dtos.configurations.masterdata.HeaderTypeConsumer
import com.nu.bom.core.service.configurations.ConfigType
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import com.nu.bom.core.utils.findAllInTree
import org.bson.types.Decimal128
import org.bson.types.ObjectId
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDate

/**
 * Create the missing entities needed in the masterdata tree when a consumable was created
 * from masterdata.
 *
 * @see com.nu.bom.core.manufacturing.entities.masterdata.material.MasterdataMaterialParent
 * @see com.nu.bom.core.manufacturing.entities.masterdata.material.MasterdataMaterialPriceEntity
 * @see com.nu.bom.core.manufacturing.entities.masterdata.material.MasterdataMaterialEmissionEntity
 *
 */
@Service
class ConsumableMdMigration : ManufacturingModelEntityMapper {
    override val changeSetId: MigrationChangeSetId
        get() = MigrationChangeSetId("2025-03-29-consumable-md-migration")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity {
        val consumablesWithParent =
            (entity to entity).findAllInTree({ manu -> manu.first.type == Entities.CONSUMABLE.name }) { parentPair ->
                parentPair.first.children.map { it to parentPair.first }
            }
        return createMdEntitiesForConsumables(entity, consumablesWithParent)
    }

    private fun createMdEntitiesForConsumables(
        entity: ManufacturingModelEntity,
        consumablesWithParent: Sequence<Pair<ManufacturingModelEntity, ManufacturingModelEntity>>,
    ): ManufacturingModelEntity {
        val consumablesFromMd =
            consumablesWithParent
                .filter { it.first.masterDataSelector?.key != null }
                .distinctBy { it.first.masterDataSelector }
        if (consumablesFromMd.none()) {
            return entity
        }
        return entity.copyAll(children = entity.children + createMdParent(entity, consumablesFromMd))
    }

    private fun createMdParent(
        entity: ManufacturingModelEntity,
        consumablesWithParent: Sequence<Pair<ManufacturingModelEntity, ManufacturingModelEntity>>,
    ): ManufacturingModelEntity {
        val mdParentId = ObjectId.get()

        val mdTimestampField = entity.fieldWithResults[Manufacturing::masterdataTimestamp.name]
        val mdTimestamp = (mdTimestampField?.value as? Decimal128)?.bigDecimalValue()

        val childrenPriceEntities = createMdPriceEntities(consumablesWithParent, mdParentId, mdTimestamp)
        val childrenEmissionEntities = createMdEmissionEntities(consumablesWithParent, mdParentId, mdTimestamp)

        return ManufacturingModelEntity(
            id = mdParentId,
            name = "MasterdataMaterialParentNew",
            type = Entities.MD_MATERIAL_PARENT.name,
            clazz = MasterdataMaterialParent::class.simpleName!!,
            args = emptyMap(),
            fieldWithResults =
                mapOf(
                    MasterdataMaterialParent::createMaterialPrices.name to
                        createMdCostFactorParentCreateCostFactorField(
                            entity,
                        ),
                    MasterdataMaterialParent::createMaterialEmissions.name to
                        createMdCostFactorParentCreateCostFactorField(
                            entity,
                        ),
                    MasterdataMaterialParent::masterDataTypeInternal.name to
                        FieldResultModel(
                            version = entity.version,
                            newVersion = entity.version,
                            type = Text::class.java.simpleName,
                            source = FieldResult.SOURCE.C.name,
                            value = MasterDataType.NONE.name,
                        ),
                    MasterdataMaterialParent::entityDesignation.name to
                        FieldResultModel(
                            version = entity.version,
                            newVersion = entity.version,
                            type = Text::class.java.simpleName,
                            source = FieldResult.SOURCE.C.name,
                            value = "MasterdataMaterialParentNew",
                        ),
                    MasterdataMaterialParent::displayDesignation.name to
                        FieldResultModel(
                            version = entity.version,
                            newVersion = entity.version,
                            type = Text::class.java.simpleName,
                            source = FieldResult.SOURCE.C.name,
                            value = "Material",
                        ),
                ),
            initialFieldWithResults = emptyMap(),
            children = childrenPriceEntities + childrenEmissionEntities,
        ).also {
            it.version = entity.version
            it.createdBy =
                FieldKey(
                    "materialParentCreationNew",
                    entity.id.toHexString(),
                    "MANUFACTURING",
                    "MD_MATERIAL_PARENT",
                    entity.version,
                    entity.version,
                    entity.name,
                )
        }
    }

    private fun createMdPriceEntities(
        consumables: Sequence<Pair<ManufacturingModelEntity, ManufacturingModelEntity>>,
        mdParentId: ObjectId,
        mdTimestamp: BigDecimal?,
    ): List<ManufacturingModelEntity> {
        return consumables.map { (consumable, parentEntity) ->
            val priceFields =
                mapOf(
                    MasterdataMaterialPriceEntity::denominatorUnit.name to
                        createFieldResultModel(
                            parentEntity.version,
                            "Text",
                            consumable.fieldWithResults["costUnit"]?.value,
                        ),
                    MasterdataMaterialPriceEntity::numeratorCurrency.name to
                        createFieldResultModel(
                            parentEntity.version,
                            "Text",
                            consumable.fieldWithResults["baseCurrency"]?.value,
                        ),
                    MasterdataMaterialPriceEntity::value.name to
                        createFieldResultModel(
                            parentEntity.version,
                            "Money",
                            consumable.fieldWithResults["pricePerUnit"]?.value,
                        ),
                )
            ManufacturingModelEntity(
                id = ObjectId.get(),
                name = consumable.name,
                type = Entities.MD_MATERIAL_PRICE.name,
                clazz = MasterdataMaterialPriceEntity::class.simpleName!!,
                args = emptyMap(),
                fieldWithResults =
                    createFieldWithResults(
                        parentEntity,
                        consumable,
                        priceFields,
                        HeaderTypeConsumer.MATERIAL_PRICE,
                        mdTimestamp,
                        "MasterdataMaterialPriceEntity",
                        false,
                    ),
                initialFieldWithResults =
                    createFieldWithResults(
                        parentEntity,
                        consumable,
                        priceFields,
                        HeaderTypeConsumer.MATERIAL_PRICE,
                        mdTimestamp,
                        "MasterdataMaterialPriceEntity",
                        false,
                    ),
            ).apply {
                version = parentEntity.version
                entityRef = consumable.name
                createdBy =
                    FieldKey(
                        name = MasterdataMaterialParent::createMaterialPrices.name,
                        entityId = mdParentId.toHexString(),
                        entityType = Entities.MD_MATERIAL_PARENT.name,
                        type = Entities.MD_MATERIAL_PRICE.name,
                        version = parentEntity.version,
                        newVersion = parentEntity.version,
                        entityRef = "MasterdataMaterialParentNew",
                    )
            }
        }.toList()
    }

    private fun createMdEmissionEntities(
        consumables: Sequence<Pair<ManufacturingModelEntity, ManufacturingModelEntity>>,
        mdParentId: ObjectId,
        mdTimestamp: BigDecimal?,
    ): List<ManufacturingModelEntity> {
        return consumables.map { (consumable, parentEntity) ->
            val emissionFields =
                mapOf(
                    MasterdataMaterialEmissionEntity::denominatorUnit.name to
                        createFieldResultModel(
                            parentEntity.version,
                            "Text",
                            consumable.fieldWithResults["costUnit"]?.value,
                        ),
                    MasterdataMaterialEmissionEntity::numeratorUnit.name to
                        createFieldResultModel(
                            parentEntity.version,
                            "Text",
                            consumable.fieldWithResults["cO2PerUnit"]?.unit,
                        ),
                    MasterdataMaterialEmissionEntity::value.name to
                        createFieldResultModel(
                            parentEntity.version,
                            "Emission",
                            consumable.fieldWithResults["cO2PerUnit"]?.value,
                            unit = EmissionUnits.KILOGRAM_CO2E.name,
                        ),
                )
            ManufacturingModelEntity(
                id = ObjectId.get(),
                name = consumable.name,
                type = Entities.MD_MATERIAL_EMISSION.name,
                clazz = MasterdataMaterialEmissionEntity::class.simpleName!!,
                args = emptyMap(),
                fieldWithResults =
                    createFieldWithResults(
                        parentEntity,
                        consumable,
                        emissionFields,
                        HeaderTypeConsumer.MATERIAL_CO2,
                        mdTimestamp,
                        "MasterdataMaterialEmissionEntity",
                        false,
                    ),
                initialFieldWithResults =
                    createFieldWithResults(
                        parentEntity,
                        consumable,
                        emissionFields,
                        HeaderTypeConsumer.MATERIAL_CO2,
                        mdTimestamp,
                        "MasterdataMaterialEmissionEntity",
                        true,
                    ),
            ).apply {
                version = parentEntity.version
                entityRef = consumable.name
                createdBy =
                    FieldKey(
                        name = MasterdataMaterialParent::createMaterialEmissions.name,
                        entityId = mdParentId.toHexString(),
                        entityType = Entities.MD_MATERIAL_PARENT.name,
                        type = Entities.MD_MATERIAL_EMISSION.name,
                        version = parentEntity.version,
                        newVersion = parentEntity.version,
                        entityRef = "MasterdataMaterialParentNew",
                    )
            }
        }.toList()
    }

    private fun createMdCostFactorParentCreateCostFactorField(entity: ManufacturingModelEntity): FieldResultModel =
        FieldResultModel(
            version = entity.version,
            newVersion = entity.version,
            type = EntityGeneration::class.java.simpleName,
            source = FieldResult.SOURCE.C.name,
            value = true,
            inputs =
                setOf(
                    InputDependencyModel(
                        BaseMaterial::location.name,
                        entity.id.toHexString(),
                        entity.version,
                    ),
                    InputDependencyModel(
                        BaseMaterial::headerKey.name,
                        entity.id.toHexString(),
                        entity.version,
                    ),
                ),
        )

    private fun createFieldWithResults(
        parentEntity: ManufacturingModelEntity,
        consumable: ManufacturingModelEntity,
        fields: Map<String, FieldResultModel>,
        headerConsumer: HeaderTypeConsumer,
        mdTimestamp: BigDecimal?,
        simpleClassName: String,
        initial: Boolean = false,
    ): Map<String, FieldResultModel> {
        val headerKey = consumable.masterDataSelector?.key!!
        val dimension =
            consumable.fieldWithResults["dimension"]?.value ?: consumable.initialFieldWithResults["dimension"]?.value
        val location =
            parentEntity.fieldWithResults["location"]?.value ?: parentEntity.initialFieldWithResults["location"]?.value
        val locationName =
            parentEntity.fieldWithResults["locationName"]?.value
                ?: parentEntity.initialFieldWithResults["locationName"]?.value
        val behaviourField =
            createMdCostFactorBehaviourField(
                parentManufacturing = parentEntity,
                consumable = consumable,
                simpleClassName,
            )

        val lookupField = createMdCostFactorLookupField(consumable, mdTimestamp, headerConsumer, headerKey)
        val additionalFields =
            mapOf(
                BaseMaterial::headerKey.name to createFieldResultModel(parentEntity.version, "Text", headerKey),
                BaseMaterial::location.name to createFieldResultModel(parentEntity.version, "Text", location),
                MasterdataContextOperationConfigBuilders.LOOKUP_FIELD_NAME to lookupField,
                MasterdataEntity::masterDataTypeInternal.name to
                    FieldResultModel(
                        version = consumable.version,
                        newVersion = consumable.version,
                        type = Text::class.java.simpleName,
                        source = FieldResult.SOURCE.C.name,
                        value = MasterDataType.NONE.name,
                    ),
                DataSourcerUpdaterProvider.VIRTUAL_FIELD_NAME to lookupField.copyAll(),
                MasterdataEntity::createBehaviour.name to behaviourField,
                REQUEST_EFFECTIVITY_TSET_REF_FIELD_REGION to
                    createFieldResultModel(
                        parentEntity.version,
                        "Text",
                        location,
                    ),
                REQUEST_EFFECTIVITY_TSET_REF_FIELD_REGION_DISPLAY_NAME to
                    createFieldResultModel(
                        parentEntity.version,
                        "Text",
                        locationName,
                        locationName,
                    ),
                "executeLookup" to
                    createFieldResultModel(
                        parentEntity.version,
                        "Bool",
                        false,
                        null,
                        FieldResult.SOURCE.C.name,
                    ),
                ACTUAL_EFFECTIVITY_TSET_DATA_SOURCE_FIELD to
                    createFieldResultModel(
                        parentEntity.version,
                        "Text",
                        "tset.tset",
                    ),
                ACTUAL_EFFECTIVITY_TSET_DATA_SOURCE_FIELD_DISPLAY_NAME to
                    createFieldResultModel(
                        parentEntity.version,
                        "Text",
                        "Tset",
                    ),
                ACTUAL_EFFECTIVITY_TSET_REF_FIELD_REGION to
                    createFieldResultModel(
                        parentEntity.version,
                        "Null",
                        null,
                    ),
                ACTUAL_EFFECTIVITY_TSET_REF_FIELD_REGION_DISPLAY_NAME to
                    createFieldResultModel(
                        parentEntity.version,
                        "Null",
                        null,
                    ),
                "mdDetailModifier" to createFieldResultModel(parentEntity.version, "Text", "TSET reference data"),
                "mdDetailModificationDate" to createFieldResultModel(parentEntity.version, "Date", LocalDate.now()),
                "dimension" to createFieldResultModel(parentEntity.version, "Dimension", dimension),
            )
        return if (initial) {
            (fields + additionalFields).filterKeys { key ->
                key in
                    setOf(
                        "headerKey",
                        "location",
                        "headerDisplayName",
                        "mdDetailModificationDate",
                        "mdDetailModifier",
                        "value",
                        BaseMaterial::baseCurrency.name,
                        BaseMaterial::dimension.name,
                        MasterdataMaterialPriceEntity::denominatorUnit.name,
                        MasterdataMaterialPriceEntity::numeratorCurrency.name,
                        MasterdataMaterialEmissionEntity::numeratorUnit.name,
                        ACTUAL_EFFECTIVITY_TSET_DATA_SOURCE_FIELD,
                        ACTUAL_EFFECTIVITY_TSET_DATA_SOURCE_FIELD_DISPLAY_NAME,
                    )
            }
        } else {
            fields + additionalFields
        }
    }

    private fun createMdCostFactorLookupField(
        locationEntity: ManufacturingModelEntity,
        mdTimestamp: BigDecimal?,
        headerTypeConsumer: HeaderTypeConsumer,
        headerKey: String,
    ) = FieldResultModel(
        version = locationEntity.version,
        newVersion = locationEntity.version,
        type = MdLookupRequestField::class.java.simpleName,
        source = FieldResult.SOURCE.C.name,
        value =
            MdLookupRequestFieldData(
                strategyKey = "tset.ref.strategy.material",
                headerTypeKey = "tset.ref.header-type.material",
                headerKey = headerKey,
                headerTypeConsumer = headerTypeConsumer,
                effectivities = emptyList(),
                executeLookup = false,
                timestampEpochMillis = mdTimestamp,
            ),
    )

    private fun createFieldResultModel(
        version: Int,
        type: String,
        value: Any?,
        systemValue: Any? = null,
        source: String = FieldResult.SOURCE.C.name,
        unit: String? = null,
    ): FieldResultModel =
        FieldResultModel(
            version = version,
            newVersion = version,
            type = type,
            value = value,
            source = source,
            systemValue = systemValue,
            unit = unit,
        )

    private fun createMdCostFactorBehaviourField(
        parentManufacturing: ManufacturingModelEntity,
        consumable: ManufacturingModelEntity,
        hostType: String,
    ): FieldResultModel {
        val mdConfigIdentifier = getMasterdataConfigurationIdentifier(parentManufacturing)
        return FieldResultModel(
            version = consumable.version,
            newVersion = consumable.version,
            type = DynamicBehaviourGeneration::class.simpleName!!,
            value =
                DynamicBehaviourGeneration(
                    DynamicBehaviourGeneration.DynamicBehaviourTypeMapping(
                        hostType = hostType,
                        behaviourType = "CONFIG",
                        behaviourImplementationClassName = ConfigBasedDynamicBehaviour::class.qualifiedName!!,
                        configurationIdentifier = ConfigId(ConfigType.Masterdata, mdConfigIdentifier),
                    ),
                ).dbValue(),
            FieldResult.SOURCE.C.name,
        )
    }

    private fun getMasterdataConfigurationIdentifier(parentManufacturing: ManufacturingModelEntity): ConfigurationIdentifier {
        val mdConfigKeyStr =
            parentManufacturing.fieldWithResults[Manufacturing::masterdataConfigurationKey.name]?.value as? String?
        if (mdConfigKeyStr != null) {
            try {
                return MasterdataConfigurationKey(mdConfigKeyStr).res
            } catch (ex: JsonProcessingException) {
                // fishy, but ignore invalid key format data here and fallback to default
            } catch (ex: JsonMappingException) {
                // fishy, but ignore invalid key format data here and fallback to default
            }
        }
        return ConfigurationIdentifier.tset(
            MasterdataTsetConfigurationService.TSET_CONFIGURATION_KEY,
            SemanticVersion.initialVersion(),
        )
    }
}
