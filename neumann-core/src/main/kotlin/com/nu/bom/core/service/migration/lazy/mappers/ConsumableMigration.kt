package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.entities.BaseMaterial
import com.nu.bom.core.manufacturing.entities.masterdata.material.MaterialConsumerExtension
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import org.springframework.stereotype.Service

/**
 * Migrated the old masterdata key to the new implementation where the MD key is a field of the
 * consumable entity
 *
 * @see com.nu.bom.core.manufacturing.entities.BaseMaterial.headerKey
 */
@Service
class ConsumableMigration : ManufacturingModelEntityMapper {
    override val changeSetId: MigrationChangeSetId
        get() = MigrationChangeSetId("2025-03-31-consumable-migration")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity =
        entity.copyAll(
            fieldWithResults = entity.fieldWithResults + addHeaderKey(entity.masterDataSelector?.key, entity.version),
            initialFieldWithResults = newInitialFields(entity),
        )

    private fun newInitialFields(entity: ManufacturingModelEntity): Map<String, FieldResultModel> {
        val initialCo2 =
            if (entity.masterDataSelector == null) {
                mapOf(
                    BaseMaterial::cO2PerUnit.name to
                        FieldResultModel(
                            version = entity.version,
                            newVersion = entity.version,
                            type = Emission::class.java.simpleName,
                            value = Weight(0.0, WeightUnits.KILOGRAM),
                            source = FieldResult.SOURCE.I.name,
                        ),
                )
            } else {
                emptyMap()
            }
        return entity.initialFieldWithResults +
            addHeaderKey(entity.masterDataSelector?.key, entity.version) +
            initialCo2
    }

    private fun addHeaderKey(
        headerKey: String?,
        version: Int,
    ): Map<String, FieldResultModel> =
        mapOf(
            MaterialConsumerExtension::headerKey.name to
                FieldResultModel(
                    version = version,
                    newVersion = version,
                    type = Text::class.java.simpleName,
                    value = headerKey?.let { Text(headerKey) },
                    source = FieldResult.SOURCE.I.name,
                ),
        )
}
