package com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.structure

import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.ExternalConfigurationOperation

class CO2OperationConfigurationV4(
    operations: List<ExternalConfigurationOperation>,
    val procurementTypeConfiguration: ProcurementTypeConfiguration,
) : CO2OperationConfigurationV3(operations) {
    init {
        verifyProcurementConfiguration(procurementTypeConfiguration)
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        return contentEquals(other as CO2OperationConfigurationV4)
    }

    override fun hashCode() = operations.hashCode()

    private fun contentEquals(other: CO2OperationConfigurationV4): Boolean =
        super.contentEquals(other) && procurementTypeConfiguration == other.procurementTypeConfiguration
}
