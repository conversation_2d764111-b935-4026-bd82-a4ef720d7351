package com.nu.bom.core.model.manufacturing.schema.business

import com.nu.bom.core.manufacturing.utils.EntityModelService
import com.nu.bom.core.model.manufacturing.schema.business.ToPersistedSchemaConverter.convertSchemaToPersistedSchema
import com.nu.bom.core.model.manufacturing.schema.exception.SchemaValidationException
import com.nu.bom.core.model.manufacturing.schema.persistence.PersistedBomNodeSchemaService
import com.nu.bom.core.service.migration.lazy.LazyMigrationUtils
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.ExtensionManager
import com.nu.bom.core.utils.changelog.EntityChangelogClassProvider
import com.tset.common.util.timedInfo
import org.slf4j.LoggerFactory
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile
import org.springframework.context.event.EventListener
import org.springframework.core.annotation.Order
import org.springframework.stereotype.Service

/**
 * When running the application with the 'schema-generation' profile, the current schema will be persisted to a file
 * if it is different from the last persisted schema.
 * If the 'continue-after-schema-generation' profile is also active, the application will continue running as normal.
 * Otherwise, the application will exit.
 */
@Configuration
@Profile("schema-generation")
@Suppress("DEPRECATION")
class BomNodeSchemaGeneratorConfig(
    private val bomNodeSchemaService: BomNodeSchemaService,
) {
    companion object {
        private val logger = LoggerFactory.getLogger(BomNodeSchemaGeneratorConfig::class.java)
    }

    @EventListener(ApplicationReadyEvent::class)
    @Order(2)
    fun init() {
        logger.info("Started with 'schema-generation' profile - trying to persist current schema...")
        bomNodeSchemaService.persistCurrentSchema()
    }
}

/**
 * On regular application startup, validate and cache the current schema. If you have made any changes to the schema,
 * you must run the application with the 'schema-generation' profile first.
 */
@Configuration
@Profile(
    "!schema-loading-test" +
        "&& !schema-integrity-test" +
        "&& !schema-extraction-test" +
        "&& !(schema-generation && !continue-after-schema-generation)",
)
class BomNodeSchemaStartupConfig(
    private val bomNodeSchemaService: BomNodeSchemaService,
) {
    @EventListener(ApplicationReadyEvent::class)
    @Order(3)
    fun onStartup() {
        bomNodeSchemaService.validateAndCacheCurrentSchema()
    }
}

@Service
class BomNodeSchemaService(
    private val entityModelService: EntityModelService,
    private val entityManager: EntityManager,
    private val extensionManager: ExtensionManager,
    private val entityChangelogClassProvider: EntityChangelogClassProvider,
    private val storageService: PersistedBomNodeSchemaService,
) {
    companion object {
        private val logger = LoggerFactory.getLogger(BomNodeSchemaService::class.java)
    }

    val currentSchema: BomNodeSchema by lazy {
        logger.timedInfo("Initializing current schema...") {
            SchemaExtractionUtils.getCurrentSchema(
                entityModelService,
                entityManager,
                extensionManager,
                entityChangelogClassProvider,
                LazyMigrationUtils.getLastLazyMigrationChangeSetId(),
            )
        }
    }

    fun persistCurrentSchema() {
        storageService.persistSchema(convertSchemaToPersistedSchema(currentSchema))
    }

    fun validateAndCacheCurrentSchema() {
        logger.info("Validating and caching current schema...")
        try {
            storageService.validateAndCacheCurrentSchema(convertSchemaToPersistedSchema(currentSchema))
        } catch (e: SchemaValidationException) {
            logger.error(
                """Failed to validate current schema against the last persisted schema!
                    |If you've made any changes to the schema (e.g. added or changed an entity field, class, annotation, or lazy migration),
                    |start the application with the 'schema-generation' profile to persist the new schema, then restart.
                    |If you're encountering this error in a remote environment (e.g. a feature branch), it is likely
                    |that you've forgotten to generate and/or commit the new schema file.
                """.trimMargin(),
                e,
            )
            throw e
        }

        logger.info("Successfully validated current schema!")
    }
}
