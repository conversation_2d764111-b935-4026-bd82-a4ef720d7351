package com.nu.bom.core.api.dtos

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.google.common.annotations.VisibleForTesting
import com.nu.bom.core.api.CO2_PER_PART
import com.nu.bom.core.api.COST_PER_PART
import com.nu.bom.core.api.dtos.ExternalDataInternal.Companion.createFromAttachment
import com.nu.bom.core.api.dtos.IndicatorDto.Companion.bigDecimalToCurrencyInfo
import com.nu.bom.core.exception.FieldConversionException
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.DontExport
import com.nu.bom.core.manufacturing.annotations.ExternalData
import com.nu.bom.core.manufacturing.annotations.Label
import com.nu.bom.core.manufacturing.annotations.entityfieldmetainfo.FieldConversionBehaviour
import com.nu.bom.core.manufacturing.annotations.entityfieldmetainfo.FieldConversionEntityFieldMetaInfo
import com.nu.bom.core.manufacturing.annotations.extractFieldBasedUnits
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostManufacturedMaterial
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.BaseModelManufacturing
import com.nu.bom.core.manufacturing.entities.BomNodeReference
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.TechnologyPageViewGroup
import com.nu.bom.core.manufacturing.enums.UnitOverrideContext
import com.nu.bom.core.manufacturing.fieldTypes.CustomProcurementType
import com.nu.bom.core.manufacturing.fieldTypes.DynamicQuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.ExchangeRatesField
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.FieldWithResult
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.NoCalcFieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Null
import com.nu.bom.core.manufacturing.fieldTypes.NumericFieldResult
import com.nu.bom.core.manufacturing.fieldTypes.NumericFieldResultWithNumeratorAndDenominator
import com.nu.bom.core.manufacturing.fieldTypes.ObjectIdField
import com.nu.bom.core.manufacturing.fieldTypes.SelectEnumFieldResult
import com.nu.bom.core.manufacturing.fieldTypes.ThreeDbVersionedPart
import com.nu.bom.core.manufacturing.fieldTypes.VersionedPart
import com.nu.bom.core.manufacturing.service.FieldIdKey
import com.nu.bom.core.manufacturing.service.FieldKey
import com.nu.bom.core.manufacturing.service.MatchKey
import com.nu.bom.core.model.AccountId
import com.nu.bom.core.model.BomEntryRelation
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.EntityPathElement
import com.nu.bom.core.model.Merge
import com.nu.bom.core.model.MergeSourceType
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.service.BomNodeBranchStateService
import com.nu.bom.core.service.CbdService
import com.nu.bom.core.service.MasterDataMergeService
import com.nu.bom.core.service.MergeService
import com.nu.bom.core.service.ShapesService
import com.nu.bom.core.service.UnitConversionService
import com.nu.bom.core.turn.TurningService.Companion.RAW_PART_TECHNOLOGY
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.user.UserService
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.FieldInfo
import com.nu.bom.core.utils.InstanceBasedMetaService
import com.nu.bom.core.utils.InterpolationBomInfo
import com.nu.bom.core.utils.InterpolationData
import com.nu.bom.core.utils.LegacyInstanceBasedMetaService
import com.nu.bom.core.utils.Maybe
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.nu.bom.core.utils.applyIfExists
import com.nu.bom.core.utils.component1
import com.nu.bom.core.utils.component2
import com.nu.bom.core.utils.findInTree
import com.nu.bom.core.utils.logHowSensibleTheDenominatorUnitInfoIs
import com.nu.bom.core.utils.mapWithNull
import com.nu.bom.core.utils.separateByType
import com.nu.bom.core.utils.simpleName
import com.nu.bom.core.utils.toUpperCamelCase
import com.nu.bom.core.utils.traverse
import com.nu.bom.core.utils.wrapIntoMaybe
import com.nu.bomrads.enumeration.BomNodeStatus
import com.nu.user.UserInfoDto
import com.tset.bom.clients.common.DenominatorBehavior
import com.tset.bom.clients.common.DenominatorUnit
import com.tset.core.api.ProjectDashboardWaterfallService
import com.tset.core.api.dto.ChartColumn
import com.tset.core.api.dto.CurrencyInfo
import com.tset.core.api.dto.DrillDownType
import com.tset.core.module.bom.BomNodeModule
import com.tset.core.module.bom.exchangerates.ExchangeRateMap
import com.tset.core.module.bom.exchangerates.ExchangeRateMap.Companion.getConversionRate
import com.tset.core.service.domain.Currency
import com.tset.core.service.domain.calculation.CalculationType
import org.bson.types.Decimal128
import org.bson.types.ObjectId
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.math.BigDecimal
import java.math.MathContext
import java.time.Instant
import java.time.LocalDate
import java.time.Year
import java.time.ZoneOffset
import java.util.Date
import java.util.Optional
import kotlin.reflect.full.createType

data class WaterfallDto(
    val cost: List<ChartColumn>,
    val co2: List<ChartColumn>,
)

data class BomNodeDto(
    val id: String,
    val parents: List<BomLink>,
    val subNodes: List<BomLink>,
    val manufacturing: ManufacturingDto?,
    val year: Int,
    val status: BomNodeStatus,
    val responsibleUser: Optional<UserInfoDto>,
    val kpi: KeyPerformanceIndicatorApiDto,
    val branch: BranchDto,
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    val cbd: Cbd?,
    val openMergesAvailable: List<MergeSourceType>,
    val waterfall: WaterfallDto?,
    val title: String?,
    val calculationType: FieldParameter,
    // combination of snapshotID and branchId
    // together they are indicating if a bomnode represents the same state or not
    val internalRevisionId: String,
    val lastModifiedDate: Instant?,
    val dirtyChildLoading: Boolean,
    val protectedAt: Instant?,
    val protectedBy: String?,
) {
    @VisibleForTesting
    fun getField(field: String): FieldParameter? = manufacturing?.fields?.find { it.name == field }

    @VisibleForTesting
    fun getFieldValue(field: String): Any? =
        (
            getField(field)
                ?: error(
                    "Unknown field $field in ${
                        manufacturing?.fields?.sortedBy { it.name }?.joinToString(separator = "\n")
                    }",
                )
        ).value

    @VisibleForTesting
    fun getField(
        entityName: String,
        field: String,
    ): FieldParameter {
        val entity =
            findInTree {
                it.name == entityName
            } ?: error("Entity not found with name=$entityName")
        return entity.getField(field)
    }

    @VisibleForTesting
    fun findInTree(predicate: (ManufacturingDto) -> Boolean): ManufacturingDto? =
        this.manufacturing?.findInTree(
            predicate = predicate,
            children = { it.children },
        )
}

data class BomLink(
    val bomEntryId: String,
    val bomNodeId: String,
)

@JsonInclude(JsonInclude.Include.NON_EMPTY)
data class KeyPerformanceIndicatorApiDto(
    @JsonProperty("co2PerPart")
    val cO2PerPart: IndicatorDto<BigDecimal>?,
    val costPerPart: IndicatorDto<CurrencyInfo>?,
) {
    companion object {
        fun fromKeyPerformanceIndicatorDto(
            dto: KeyPerformanceIndicatorDto?,
            exchangeRateMap: ExchangeRateMap,
        ) = dto?.let {
            KeyPerformanceIndicatorApiDto(
                cO2PerPart = it.cO2PerPart,
                costPerPart = it.costPerPart.bigDecimalToCurrencyInfo(exchangeRateMap),
            )
        } ?: KeyPerformanceIndicatorApiDto(
            cO2PerPart = null,
            costPerPart = null,
        )
    }
}

@JsonInclude(JsonInclude.Include.NON_EMPTY)
data class KeyPerformanceIndicatorDto(
    val cO2PerPart: IndicatorDto<BigDecimal>?,
    val costPerPart: IndicatorDto<BigDecimal>,
    val totalTargetCosts: IndicatorDto<BigDecimal>,
    val totalCurrentCosts: IndicatorDto<BigDecimal>,
    val additionalTargetCosts: IndicatorDto<BigDecimal>,
    val additionalCurrentCosts: IndicatorDto<BigDecimal>,
    val costPerPartInBaseCurrency: IndicatorDto<BigDecimal>,
)

@JsonInclude(JsonInclude.Include.NON_EMPTY)
data class IndicatorDto<T>(
    val oldValue: T?,
    val newValue: T?,
    val percentageDifference: BigDecimal? = null,
    val broken: Boolean,
) {
    companion object {
        fun (IndicatorDto<BigDecimal>).bigDecimalToCurrencyInfo(exchangeRateMap: ExchangeRateMap) =
            IndicatorDto(
                oldValue = this.oldValue?.let { exchangeRateMap.eurToCurrencyInfo(it) },
                newValue = this.newValue?.let { exchangeRateMap.eurToCurrencyInfo(it) },
                percentageDifference = percentageDifference,
                broken = broken,
            )
    }
}

enum class NullValues {
    KEEP,
    REMOVE,
    KEEP_EXTERNAL_DEPENDENCIES,
}

@Service
class FieldConversionService(
    private val unitConversionService: UnitConversionService,
    private val entityManager: EntityManager,
    private val instanceBasedMetaService: InstanceBasedMetaService,
    private val legacyInstanceBasedMetaService: LegacyInstanceBasedMetaService,
) {
    private val logger = LoggerFactory.getLogger(FieldConversionService::class.java)

    fun getFieldAsFieldParameter(
        accessCheck: AccessCheck,
        entity: ManufacturingEntity,
        fieldName: String,
        exchangeRates: ExchangeRateMap,
        unitOverrideContext: UnitOverrideContext = UnitOverrideContext.fromEntity(entity),
    ): Mono<FieldParameter> =
        Mono.justOrEmpty(entity.getFieldResultSafe(fieldName)).flatMap { fieldValue ->
            fieldResultToFieldParameterWithOthers(
                accessCheck,
                fieldName,
                fieldValue!!,
                entity.getEntityClass(),
                otherFields = null,
                null,
                exchangeRateMap = exchangeRates,
                unitOverrideContext = unitOverrideContext,
                entityForDynamicMetaData = entity,
            )
        }

    @Deprecated(
        "Use fieldResultToFieldParameterWithOthers",
        replaceWith = ReplaceWith("fieldResultToFieldParameterWithOthers"),
    )
    fun monoFieldWithResultToFieldParameterWithOthers(
        accessCheck: AccessCheck,
        fieldName: String,
        fieldResult: FieldResultStar,
        entityClass: String,
        otherFields: ManufacturingEntity?,
        entity: ManufacturingEntity?,
        exchangeRateMap: ExchangeRateMap,
        bomInfo: InterpolationBomInfo? = null,
        unitOverrideContext: UnitOverrideContext,
        nullValueHandling: NullValues = NullValues.REMOVE,
        entityForDynamicMetaData: ManufacturingEntity?,
    ): Mono<FieldParameter> =
        instanceBasedMetaService
            .getMetaInfo(
                accessCheck,
                null,
                entityClass,
                fieldName,
                entity?.let { InterpolationData.fromEntity(it, bomInfo) } ?: InterpolationData(),
                entityForDynamicMetaData,
            ).map { metaInfo ->
                val label = getLabel(metaInfo, otherFields)

                fieldWithResultToFieldParameter(
                    fieldName,
                    fieldResult,
                    entityClass,
                    entity,
                    exchangeRateMap,
                    Currency.EUR,
                    label,
                    bomInfo,
                    unitOverrideContext,
                    nullValueHandling,
                    entityForDynamicMetaData,
                )
            }

    fun fieldResultToFieldParameterWithOthers(
        accessCheck: AccessCheck,
        fieldName: String,
        fieldResult: FieldResultStar,
        entityClass: String,
        otherFields: ManufacturingEntity?,
        entity: ManufacturingEntity?,
        exchangeRateMap: ExchangeRateMap,
        bomInfo: InterpolationBomInfo? = null,
        unitOverrideContext: UnitOverrideContext,
        // Differentiate between FE and internal flow for the Null type,
        nullValueHandling: NullValues = NullValues.REMOVE,
        entityForDynamicMetaData: ManufacturingEntity?,
        interpolatedMetaInfo: Map<String, Any>? = null,
        interpolatedFieldInfo: FieldInfo? = null,
        baseCurrency: Currency = Currency.EUR,
    ): Mono<FieldParameter> =
        Mono
            .justOrEmpty(interpolatedMetaInfo)
            .switchIfEmpty(
                instanceBasedMetaService.getMetaInfo(
                    accessCheck,
                    entity?.getEntityTypeAnnotation(),
                    entityClass,
                    fieldName,
                    getInterpolationData(unitOverrideContext, entity, bomInfo),
                    entityForDynamicMetaData,
                ),
            ).map { metaInfo ->
                fieldWithResultToFieldParameter(
                    fieldName,
                    fieldResult,
                    entityClass,
                    entity,
                    exchangeRateMap,
                    baseCurrency,
                    getLabel(metaInfo, otherFields),
                    bomInfo,
                    unitOverrideContext,
                    nullValueHandling,
                    entityForDynamicMetaData,
                    metaInfo,
                    interpolatedFieldInfo,
                )
            }

    private fun getLabel(
        metaInfo: Map<String, Any>?,
        otherFields: ManufacturingEntity?,
    ): String? {
        val labelFieldName = metaInfo?.get(Label.META_INFO) as String?
        val label = otherFields?.getFieldResultSafe(labelFieldName.toString())?.res?.toString()
        return label
    }

    fun convertEntityPath(entityPath: EntityPathElement): EntityPathDto =
        EntityPathDto(
            entityType = entityPath.type.toString(),
            objectId = entityPath.entityId.toHexString(),
            displayDesignation =
                fieldWithResultToFieldParameter(
                    name = "displayDesignation",
                    resultPreConversion = entityPath.displayDesignation,
                    entityClass = entityPath.entityClass,
                    exchangeRateMap = ExchangeRateMap.empty(),
                    baseCurrency = Currency.EUR,
                    unitOverrideContext = UnitOverrideContext.noContext,
                    entityForDynamicMetaData = null,
                ),
        )

    fun fieldWithResultToFieldParameter(
        name: String,
        resultPreConversion: FieldResultStar,
        entityClass: String,
        entity: ManufacturingEntity? = null,
        exchangeRateMap: ExchangeRateMap,
        baseCurrency: Currency,
        label: String? = null,
        bomInfo: InterpolationBomInfo? = null,
        unitOverrideContext: UnitOverrideContext,
        nullValueHandling: NullValues = NullValues.REMOVE,
        entityForDynamicMetaData: ManufacturingEntity?,
        interpolatedMetaInfo: Map<String, Any>? = null,
        interpolatedFieldInfo: FieldInfo? = null,
    ): FieldParameter {
        entity?.let {
            if (entityClass != it::class.java.simpleName) {
                throw Exception("inconsistent inputs")
            }
        }

        val effectiveMetaInfo =
            interpolatedMetaInfo ?: legacyInstanceBasedMetaService.getMetaInfo(
                entityClass,
                name,
                getInterpolationData(unitOverrideContext, entity, bomInfo),
                entityForDynamicMetaData,
            )

        val fieldInfo =
            interpolatedFieldInfo ?: legacyInstanceBasedMetaService.getFieldInfo(
                entityClass,
                name,
                entityForDynamicMetaData,
            )

        return createFieldParameter(
            name,
            resultPreConversion,
            entityClass,
            exchangeRateMap,
            baseCurrency,
            label,
            unitOverrideContext,
            nullValueHandling = nullValueHandling,
            entityForDynamicMetaData,
            effectiveMetaInfo,
            fieldInfo,
        )
    }

    private fun createFieldParameter(
        name: String,
        resultPreConversion: FieldResultStar,
        entityClass: String,
        exchangeRateMap: ExchangeRateMap,
        baseCurrency: Currency,
        label: String? = null,
        unitOverrideContext: UnitOverrideContext,
        nullValueHandling: NullValues,
        entityForDynamicMetaData: ManufacturingEntity?,
        interpolatedMetaInfo: Map<String, Any>,
        interpolatedFieldInfo: FieldInfo?,
    ): FieldParameter {
        val fieldTypeStaticPreConversion = resultPreConversion.getType()
        val fieldTypeResultIfConversionIsPossible = getFieldTypeResult(interpolatedFieldInfo, resultPreConversion)
        val result =
            convertToFieldResultAndHandleError(
                fieldTypeResultIfConversionIsPossible,
                resultPreConversion,
                interpolatedMetaInfo,
                name,
                entityClass,
                fieldTypeStaticPreConversion,
            )

        data class NumFieldUnitResult(
            val fieldType: String,
            val unit: String?,
            val denominatorUnit: DenominatorUnit?,
            val factor: BigDecimal,
        )

        val (fieldType, unit, denominatorUnit, factor) =
            if (result is NumericFieldResultWithNumeratorAndDenominator<*, *, *> && result.numeratorUnit != null) {
                val (denominatorUnit, factor) =
                    interpolatedFieldInfo?.denominatorUnitInfo?.getDenominatorUnitAndFactor(unitOverrideContext) ?: (null to BigDecimal.ONE)
                val numeratorUnit = result.numeratorUnit

                val calculatedDenominator = result.denominatorUnit
                if (calculatedDenominator == null) {
                    NumFieldUnitResult(
                        numeratorUnit.type.name.toUpperCamelCase(),
                        numeratorUnit.name2,
                        denominatorUnit,
                        factor.divide(result.numeratorUnit.baseFactor, MathContext.DECIMAL64),
                    )
                } else {
                    NumFieldUnitResult(
                        numeratorUnit.type.name.toUpperCamelCase(),
                        numeratorUnit.name2,
                        calculatedDenominator.let {
                            DenominatorUnit(
                                it.name2,
                                it.type.name.toUpperCamelCase(),
                                DenominatorBehavior.WRITE_THROUGH,
                            )
                        },
                        (calculatedDenominator.baseFactor).divide(result.numeratorUnit.baseFactor, MathContext.DECIMAL64),
                    )
                }
            } else {
                val (fieldType, unit) =
                    unitOverrideContext.getFieldTypeAndUnit(
                        when (resultPreConversion.getType()) {
                            "NoCalcFieldResult" -> resultPreConversion.getType()
                            else -> interpolatedFieldInfo?.type?.simpleName() ?: fieldTypeStaticPreConversion
                        },
                        entityManager,
                    )
                // TODO! getFieldInfo (and probably getMetaInfo) do not work properly for behaviours (they do for extensions though),
                //    thus denominatorUnits won’t work for behaviours. Imo we need a general discussion about that, see https://tsetplatform.atlassian.net/browse/COST-15825

                val (denominatorUnit, factor) =
                    interpolatedFieldInfo
                        ?.denominatorUnitInfo
                        .also { info ->
                            logHowSensibleTheDenominatorUnitInfoIs(
                                info,
                                fieldType,
                                name,
                                entityClass,
                                logger,
                            )
                        }?.getDenominatorUnitAndFactor(unitOverrideContext) ?: (null to BigDecimal.ONE)
                NumFieldUnitResult(
                    fieldType,
                    unit,
                    denominatorUnit,
                    factor,
                )
            }

        checkDenominatorUnit(denominatorUnit, result, name)

        fun transformValue(value: BigDecimal): BigDecimal = factor * value

        val value =
            when (result) {
                is SelectEnumFieldResult -> result.res.toString()
                is Null -> if (nullValueHandling == NullValues.KEEP) result.res else null
                is DynamicQuantityUnit ->
                    if (result.defaultValue) null else transformValue(result.res)
                is ObjectIdField -> result.res
                else ->
                    result.res!!.let {
                        if (it is BigDecimal) {
                            transformValue(it)
                        } else {
                            it
                        }
                    }
            }

        val valueInDefaultUnit =
            value?.let {
                unitConversionService.getValueInDefaultUnit(
                    entityClass,
                    name,
                    result,
                    fieldType,
                    ::transformValue,
                    interpolatedMetaInfo,
                    fieldTypeResultIfConversionIsPossible,
                    unitOverrideContext,
                    it,
                    entityForMetaData = entityForDynamicMetaData,
                )
            }

        val systemValue =
            result.systemValue.let {
                when (it) {
                    // TODO! search for label wtf128 for why this should always be BigDecimal, if it is numerical.
                    is BigDecimal -> transformValue(it)
                    is Decimal128 -> transformValue(it.bigDecimalValue())
                    is Double, Int -> transformValue(it.toString().toBigDecimal())
                    is Date -> LocalDate.ofInstant(it.toInstant(), ZoneOffset.UTC)
                    else -> it
                }
            }

        val systemValueCurrencyInfo = calculateCurrencyInfo(result, systemValue, baseCurrency, exchangeRateMap)
        val (fieldBasedNumeratorUnit, fieldBasedDenominatorUnit) = extractFieldBasedUnits(interpolatedMetaInfo)

        return FieldParameter(
            name = name,
            unit = unit ?: fieldBasedNumeratorUnit,
            value = value,
            valueInDefaultUnit = valueInDefaultUnit,
            type =
                when (nullValueHandling) {
                    NullValues.KEEP -> fieldType
                    NullValues.REMOVE, NullValues.KEEP_EXTERNAL_DEPENDENCIES -> {
                        fieldType.takeIf { it != "Null" }
                            ?: interpolatedFieldInfo?.type?.simpleName()
                            ?: "Null"
                    }
                },
            source = result.source.toString(),
            metaInfo = interpolatedMetaInfo,
            systemValue = systemValue,
            label = label,
            currencyInfo =
                when (result) {
                    is Money ->
                        exchangeRateMap.ccyToCurrencyInfo(
                            value as BigDecimal,
                            baseCurrency,
                        )
                    else -> null
                },
            systemValueCurrencyInfo = systemValueCurrencyInfo,
            denominatorUnit = denominatorUnit ?: fieldBasedDenominatorUnit,
        )
    }

    private fun convertToFieldResultAndHandleError(
        fieldTypeResultIfConversionIsPossible: String,
        resultPreConversion: FieldResultStar,
        interpolatedMetaInfo: Map<String, Any>,
        name: String,
        entityClass: String,
        fieldTypeStaticPreConversion: String,
    ) = try {
        ifConversionPossible(fieldTypeResultIfConversionIsPossible, resultPreConversion)
    } catch (ex: FieldConversionException) {
        if (interpolatedMetaInfo[FieldConversionEntityFieldMetaInfo.META_INFO] == FieldConversionBehaviour.ERROR_IS_NULL.name) {
            Null()
        } else {
            throw FieldConversionException(
                MatchKey(name, "unknown entity id", entityClass),
                "Cannot convert value from '$fieldTypeStaticPreConversion' to " +
                    "'$fieldTypeResultIfConversionIsPossible' for field '$name' on entity class '$entityClass'",
                cause = ex,
            )
        }
    }

    private fun getInterpolationData(
        unitOverrideContext: UnitOverrideContext,
        entity: ManufacturingEntity?,
        bomInfo: InterpolationBomInfo?,
    ): InterpolationData {
        val interpolationDataContext = InterpolationData.fromUnitOverrideContext(unitOverrideContext)
        val interpolationData =
            if (entity == null) {
                interpolationDataContext
            } else {
                InterpolationData.fromEntity(entity, bomInfo).copy(
                    dimension = interpolationDataContext.dimension,
                    quantityUnit = interpolationDataContext.quantityUnit,
                )
            }
        return interpolationData
    }

    private fun getFieldTypeResult(
        fieldInfo: FieldInfo?,
        resultPreConversion: FieldResultStar,
    ): String {
        return when (val fieldTypeStaticPreConversion = resultPreConversion.getType()) {
            "Null", "NoCalcFieldResult" -> fieldTypeStaticPreConversion
            else -> fieldInfo?.type?.simpleName() ?: fieldTypeStaticPreConversion
        }
    }

    private fun ifConversionPossible(
        fieldTypeResultIfConversionIsPossible: String,
        resultPreConversion: FieldResultStar,
    ): FieldResultStar =
        if (fieldTypeResultIfConversionIsPossible != resultPreConversion.getType()) {
            val type = entityManager.getMaybeFieldType(fieldTypeResultIfConversionIsPossible)
            type?.let {
                resultPreConversion.convert(it.kotlin.createType())
            } ?: resultPreConversion
        } else {
            resultPreConversion
        }

    private fun calculateCurrencyInfo(
        result: FieldResultStar,
        systemValue: Any?,
        baseCurrency: Currency,
        exchangeRateMap: ExchangeRateMap,
    ): CurrencyInfo? =
        if (result is Money && systemValue is BigDecimal) {
            // toBigDecimal() would support more types, but systemValue can have the
            // private type InnerNull, so yeah
            exchangeRateMap.ccyToCurrencyInfo(
                systemValue,
                baseCurrency,
            )
        } else {
            null
        }

    private fun checkDenominatorUnit(
        denominatorUnit: DenominatorUnit?,
        result: FieldResultStar,
        name: String,
    ) {
        if (denominatorUnit == null) return

        when (result) {
            is NumericFieldResult, is Null -> return
            else -> throw Exception("Unexpected denominator of '$name', which has type '${result.getType()}'.")
        }
    }
}

@Service
class BomNodeDtoConversionService(
    private val mergeService: MergeService,
    private val masterDataMergeService: MasterDataMergeService,
    private val fieldConversionService: FieldConversionService,
    private val shapesService: ShapesService,
    private val shapeDtoConversionService: ShapeDtoConversionService,
    private val cbdService: CbdService,
    private val instanceBasedMetaService: InstanceBasedMetaService,
    private val legacyInstanceBasedMetaService: LegacyInstanceBasedMetaService,
    private val entityManager: EntityManager,
    private val bomNodeBranchStateService: BomNodeBranchStateService,
    private val projectDashboardWaterfallService: ProjectDashboardWaterfallService,
    private val bomNodeModule: BomNodeModule,
    private val userService: UserService,
) {
    private val logger = LoggerFactory.getLogger(BomNodeDtoConversionService::class.java)

    fun bomNodeToDtoDirect(
        accessCheck: AccessCheck,
        nodeSnapshot: BomNodeSnapshot,
        latestMasterDataMap: Map<ObjectId, MasterDataMergeService.LatestMasterData>? = null,
        openMergesAvailable: List<MergeSourceType>,
        waterfall: WaterfallWithCbd?,
        parentShapeModelManufacturing: ManufacturingEntity?,
        exchangeRateMap: ExchangeRateMap,
    ): Mono<BomNodeDto> {
        val manufacturing = nodeSnapshot.manufacturing
        val unitOverrideContext = manufacturing?.let { UnitOverrideContext.fromEntity(it) }
        val manuMono =
            Mono
                .justOrEmpty(manufacturing)
                .flatMap { manu ->
                    manufacturingEntityToDto(
                        ctx =
                            DtoConversionContext(
                                accessCheck = accessCheck,
                                exchangeRateMap = exchangeRateMap,
                                latestMasterDataMap = latestMasterDataMap,
                                interpolationBomInfo =
                                    InterpolationBomInfo(
                                        nodeSnapshot.bomNodeId().toHexString(),
                                        nodeSnapshot.branchIdStr(),
                                    ),
                                parentShapeModelManufacturing = parentShapeModelManufacturing,
                            ),
                        rootEntity = manu!!,
                    )
                }.wrapIntoMaybe()

        return manuMono.mapWithNull { maybeManufacturing ->
            val isMain = nodeSnapshot.branchEntity().published
            val calculationType = CalculationType.fromManufacturingEntityClass(maybeManufacturing?.className)
            val userName =
                nodeSnapshot.protectedBy?.let { userId ->
                    userService.getUserById(accessCheck, userId).map { it.name }.orElse(null)
                }
            val responsibleUser =
                manufacturing
                    ?.getResponsibleUser()
                    ?.let { userService.getUserById(accessCheck, it.res) }
                    ?: Optional.empty()

            BomNodeDto(
                id = nodeSnapshot.bomNodeId().toHexString(),
                parents =
                    nodeSnapshot.parentsToUse.map {
                        bomRelationToLink(it)
                    },
                subNodes = nodeSnapshot.subNodes.map { bomRelationToLink(it) },
                manufacturing = maybeManufacturing,
                year = nodeSnapshot.year,
                status = nodeSnapshot.node().status,
                responsibleUser = responsibleUser,
                branch = toBranchDto(nodeSnapshot.branchEntity(), isMain, nodeSnapshot.title),
                kpi =
                    KeyPerformanceIndicatorApiDto.fromKeyPerformanceIndicatorDto(
                        nodeSnapshot.kpi.toDto(unitOverrideContext),
                        exchangeRateMap,
                    ),
                openMergesAvailable = openMergesAvailable,
                cbd = waterfall?.cbd,
                waterfall = waterfall?.waterfallDto,
                title = nodeSnapshot.title,
                calculationType =
                    FieldParameter(
                        name = "calculationType",
                        type = "CalculationType",
                        value = calculationType,
                        metaInfo =
                            mapOf(
                                "path" to "/api/calculationType?sub${nodeSnapshot.parentsToUse.isNotEmpty()}",
                                "triggerUpdate" to true,
                            ),
                        denominatorUnit = null,
                    ),
                internalRevisionId = nodeSnapshot._id?.toHexString() + nodeSnapshot.branchIdStr(),
                lastModifiedDate = nodeSnapshot.lastModifiedDate?.toInstant(),
                dirtyChildLoading = nodeSnapshot.dirtyChildLoading,
                protectedAt = nodeSnapshot.protectedAt,
                protectedBy = userName,
            )
        }
    }

    data class WaterfallWithCbd(
        val waterfallDto: WaterfallDto,
        val cbd: Cbd,
        val exchangeRateMap: ExchangeRateMap,
    )

    fun getWaterfallWithCbd(
        accessCheck: AccessCheck,
        node: BomNodeSnapshot,
        ccy: List<Currency>? = null,
    ): Mono<WaterfallWithCbd> {
        val manufacturing = node.manufacturing!!
        val exchangeRateMap = manufacturing.getExchangeRateMap(ccy)
        val cbd = cbdService.createCbdFromManufacturing(manufacturing, exchangeRateMap, normalizeCostUnit = false)

        return Mono
            .zip(
                getWaterfall(accessCheck, node, false, exchangeRateMap),
                getWaterfall(accessCheck, node, true, exchangeRateMap),
            ).map {
                WaterfallWithCbd(WaterfallDto(it.t1, it.t2), cbd, exchangeRateMap)
            }
    }

    /**
     * Due to usage of bomNodeModule.getParentSnapshotUntil(...) it is assumed that the parent bomNodes are also already loaded.
     * So: BomNodeLoaderService.LoadingMode.ALL_PARENTS were used for loading mode.
     */
    fun bomNodeToDto(
        accessCheck: AccessCheck,
        node: BomNodeSnapshot,
        showOpenMerges: Boolean = false,
        ccy: List<Currency>? = null,
    ): Mono<BomNodeDto> =
        getWaterfallWithCbd(accessCheck, node, ccy).flatMap { waterfallWithCbd ->
            getOpenMergesIfRequested(accessCheck, node, showOpenMerges).flatMap { latestMasterDataMap ->
                getOpenMergesAvailable(node, accessCheck, showOpenMerges).flatMap { openMergesAvailable ->
                    val parentShapeModelSnapshot =
                        bomNodeModule.getParentSnapshotUntil(accessCheck, node) { snapshotToCheck ->
                            when (snapshotToCheck.manufacturing) {
                                is BaseModelManufacturing ->
                                    snapshotToCheck.manufacturing.model.technologyPageViewGroup !=
                                        TechnologyPageViewGroup.INACTIVE
                                else -> false
                            }
                        }
                    bomNodeToDtoDirect(
                        accessCheck,
                        node,
                        latestMasterDataMap.value,
                        openMergesAvailable,
                        waterfall = waterfallWithCbd,
                        parentShapeModelSnapshot.manufacturing,
                        waterfallWithCbd.exchangeRateMap,
                    )
                }
            }
        }

    private fun getOpenMergesAvailable(
        node: BomNodeSnapshot,
        accessCheck: AccessCheck,
        showOpenMerges: Boolean,
    ) = bomNodeBranchStateService
        .getOpenMergesAvailable(
            node.bomNodeId(),
            node.branchId(),
        ).flatMap { openMergesAvailable ->
            checkMergesInChildren(accessCheck, node, showOpenMerges, openMergesAvailable)
        }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun checkMergesInChildren(
        accessCheck: AccessCheck,
        node: BomNodeSnapshot,
        showOpenMerges: Boolean,
        openMergesAvailable: List<MergeSourceType>,
    ) = if (showOpenMerges) {
        Flux
            .fromIterable(node.collectChildren().filterNot { it == node })
            .flatMap { child ->
                getOpenMergesIfRequested(accessCheck, child, true).flatMap {
                    bomNodeBranchStateService
                        .getOpenMergesAvailable(
                            child.bomNodeId(),
                            child.branchId(),
                        ).map { it.contains(MergeSourceType.MASTERDATA) }
                }
            }.takeUntil {
                it
            }.collectList()
            .map { mergeList ->
                if (mergeList.any { it }) {
                    openMergesAvailable + MergeSourceType.MASTERDATA_CHILDREN
                } else {
                    openMergesAvailable
                }
            }
    } else {
        Mono.just(openMergesAvailable)
    }

    private fun getOpenMergesIfRequested(
        accessCheck: AccessCheck,
        node: BomNodeSnapshot,
        showOpenMerges: Boolean,
    ): Mono<Maybe<Map<ObjectId, MasterDataMergeService.LatestMasterData>>> =
        if (showOpenMerges) {
            getMergeResult(accessCheck, node)
                .flatMap { openMerges ->
                    masterDataMergeService
                        .checkMerge(accessCheck = accessCheck, node = node)
                        .map { (masterDataMerge, latestMasterDataMap) ->
                            if (masterDataMerge != null) {
                                Pair(latestMasterDataMap, Maybe(openMerges + Merge.createMasterDataMerge(node)))
                            } else {
                                Pair(latestMasterDataMap, Maybe(openMerges))
                            }
                        }
                }.flatMap { mergeResult ->
                    bomNodeBranchStateService
                        .setOpenMergesAvailable(
                            node.bomNodeId(),
                            node.branchId(),
                            mergeResult.second.value?.map { it.sourceType } ?: emptyList(),
                        ).map {
                            Maybe(mergeResult.first)
                        }
                }
        } else {
            Mono.just(Maybe(null))
        }

    fun getWaterfall(
        accessCheck: AccessCheck,
        node: BomNodeSnapshot,
        isCo2: Boolean,
        exchangeRateMap: ExchangeRateMap,
    ): Mono<List<ChartColumn>> {
        val manufacturing = node.manufacturing!!
        val totalCostFieldName = if (isCo2) CO2_PER_PART else COST_PER_PART
        val totalCostResult = manufacturing.getFieldResult(totalCostFieldName)

        return if (totalCostResult == null) {
            Mono.just(
                this.projectDashboardWaterfallService.getWaterfallBreakdown(
                    drillDownType = DrillDownType.ALL,
                    requestQuantity = BigDecimal.ONE,
                    totalCost = BigDecimal.ZERO,
                    showTotal = true,
                    isCo2 = isCo2,
                    exchangeRateMap = exchangeRateMap,
                    overrideSnapshot = node,
                ),
            )
        } else {
            this.fieldConversionService
                .monoFieldWithResultToFieldParameterWithOthers(
                    accessCheck,
                    totalCostFieldName,
                    totalCostResult,
                    manufacturing::class.simpleName!!,
                    otherFields = manufacturing,
                    null,
                    exchangeRateMap,
                    unitOverrideContext = UnitOverrideContext.fromEntity(manufacturing),
                    entityForDynamicMetaData = manufacturing,
                ).map { fieldParameter ->
                    val totalCost = fieldParameter.value as BigDecimal

                    this.projectDashboardWaterfallService.getWaterfallBreakdown(
                        drillDownType = DrillDownType.ALL,
                        requestQuantity = BigDecimal.ONE,
                        totalCost = totalCost,
                        showTotal = true,
                        isCo2 = isCo2,
                        exchangeRateMap = exchangeRateMap,
                        overrideSnapshot = node,
                    )
                }
        }
    }

    private fun getMergeResult(
        accessCheck: AccessCheck,
        node: BomNodeSnapshot,
    ): Mono<List<Merge>> {
        val openMerges = node.openMerges
        return if (openMerges != null) {
            Mono.just(openMerges)
        } else {
            val branchId = node.branch
            if (branchId != null) {
                mergeService.checkMerge(accessCheck, branchId).collectList()
            } else {
                Mono.just(emptyList())
            }
        }
    }

    fun manufacturingEntityToModuleDto(
        entity: ManufacturingEntity,
        accountId: AccountId,
        projectId: ProjectId,
        latestMasterDataMap: Map<ObjectId, MasterDataMergeService.LatestMasterData>? = null,
    ): ModuleImportDto {
        val children =
            entity.children
                .filter {
                    (it.createdBy == null && it.providerField == null) && it.getEntityType() != Entities.ATTACHMENT.name
                }.map { child ->
                    child.setParent(entity)
                    manufacturingEntityToModuleDto(child, accountId, projectId, latestMasterDataMap)
                }
        val meta = entityManager.getEntityMetaInfo(entity, interpolationData = InterpolationData())

        val overrideFields =
            entity.fieldWithResults
                .filter {
                    it.result.source == FieldResult.SOURCE.I
                }.map {
                    com.nu.bom.core.publicapi.dtos.FieldParameter(
                        it.name.name,
                        it.result.res,
                        it.result.getType(),
                        it.result.getUnit(),
                        null,
                    )
                }
        val inputFields =
            filterFields(entity.initialFieldsWithResults, meta).first.map {
                com.nu.bom.core.publicapi.dtos.FieldParameter(
                    it.name.name,
                    it.result.res,
                    it.result.getType(),
                    it.result.getUnit(),
                    null,
                )
            }

        return ModuleImportDto(
            type = entity.getEntityTypeAnnotation(),
            name = entity.name,
            manufacturingType = entity::class.simpleName!!,
            part = extractPartDtoOnlyId(entity),
            children = children,
            masterDataKey =
                MasterDataKeyDto.from(
                    entity = entity,
                    latestMasterData = latestMasterDataMap?.get(entity._id),
                ),
            title = if (entity is BaseModelManufacturing) entity.snapshot?.title else null,
            year = Year.now().value,
            inputs = inputFields,
            overrides = overrideFields,
        )
    }

    fun manufacturingEntityToImportExportDto(
        entity: ManufacturingEntity,
        latestMasterDataMap: Map<ObjectId, MasterDataMergeService.LatestMasterData>? = null,
    ): Pair<ManufacturingImportDto, Triple<List<ExternalDataInternal>, List<NoCalcFieldWithKey>, Set<ThreeDbVersionedPart>>> {
        val children =
            entity.children
                .filter {
                    it.getEntityType() != Entities.ATTACHMENT.name
                }.map { child ->
                    child.setParent(entity)
                    manufacturingEntityToImportExportDto(child, latestMasterDataMap)
                }

        val childrenList = children.map { it.first }.toMutableList()
        val childrenExternal = children.flatMap { it.second.first }
        val childrenNoCalc = children.flatMap { it.second.second }
        val childrenThreeDb = children.flatMap { it.second.third }

        val meta = legacyInstanceBasedMetaService.getEntityMetaInfo(entity, interpolationData = InterpolationData())
        val (fields, noCalcFields) = filterFields(entity.fieldWithResults, meta)
        val (initialFieldsWithResults, initialNoCalcFields) = filterFields(entity.initialFieldsWithResults, meta)

        val attachments =
            if (entity is BaseManufacturing) {
                entity
                    .getAttachments()
                    .mapNotNull { createFromAttachment(it, entity.entityId, entity.getEntityTypeAnnotationOrThrow()) }
            } else {
                listOf()
            }

        val smfStep: List<ExternalDataInternal> =
            if (entity is BaseManufacturing) {
                val dto =
                    fields.find { it.name.name == "fileUploadId" && it.result !is Null }?.let {
                        ExternalDataInternal(
                            it,
                            ExternalData.EXTERNAL_DATA_TYPE.SMF_PROFILE,
                            isMainImage = false,
                        )
                    }
                listOfNotNull(dto)
            } else {
                emptyList()
            }

        val externalDataFields =
            entity.fieldWithResults
                .map {
                    it to meta[it.name.name]?.get(ExternalData.META_INFO) as ExternalData.EXTERNAL_DATA_TYPE?
                }.filter { it.second != null }
                .map {
                    ExternalDataInternal(
                        field = it.first,
                        type = it.second!!,
                        isMainImage = true,
                    )
                }

        val versionedPart =
            entity.fieldWithResults
                .find {
                    it.name.name == "versionedPart"
                }?.let {
                    it.result as? VersionedPart
                }
        val initialVersionedPart =
            entity.initialFieldsWithResults
                .find {
                    it.name.name == "versionedPart"
                }?.let {
                    it.result as? VersionedPart
                }

        val externalData = externalDataFields + childrenExternal + attachments + smfStep
        val noCalcData = noCalcFields + initialNoCalcFields + childrenNoCalc
        val threeDbData = childrenThreeDb.toMutableSet()
        if (versionedPart != null) {
            threeDbData.add(versionedPart.res)
        }
        if (initialVersionedPart != null) {
            threeDbData.add(initialVersionedPart.res)
        }
        val manufacturingImportDto =
            ManufacturingImportDto(
                id = entity._id.toString(),
                type = entity.getEntityTypeAnnotation(),
                className = entity::class.simpleName,
                name = entity.name,
                version = entity.version,
                data =
                    entity.fieldWithResults
                        .sortedBy {
                            it.name.name
                        }.associateBy({ it.name.name }, { it.result.res!! }),
                fieldsWithResult = fields,
                initialFieldsWithResult = initialFieldsWithResults,
                part = extractPartDtoOnlyId(entity),
                partName = getPartName(entity),
                children = childrenList,
                model = if (entity is BaseModelManufacturing) entity.model.toCalculationModel() else null,
                ref = entity.entityRef ?: entity.name,
                createdByField = createdByToDto(entity.createdBy),
                createdByMocked = entity.createdByMocked,
                providerField = providerFieldToDto(entity.providerField),
                createdOnBranch = entity.createdOnBranch?.toHexString(),
                masterDataKey =
                    MasterDataKeyDto.from(
                        entity = entity,
                        latestMasterData = latestMasterDataMap?.get(entity._id),
                    ),
                title = if (entity is BaseModelManufacturing) entity.snapshot?.title else null,
                year = Year.now().value,
                bomNodeId = bomNodeId(entity),
                dirtyChildLoading = entity.dirtyChildLoading,
                isolated = entity.isolated,
                dynamicFields =
                    entity.dynamicFields.entries.associate { (k, v) ->
                        k to
                            DynamicFieldDto(
                                v.entityClass,
                                v.entityFieldName,
                            )
                    },
                protectedAt = entity.snapshot?.protectedAt,
                protectedBy = entity.snapshot?.protectedBy,
            )
        return Pair(manufacturingImportDto, Triple(externalData, noCalcData, threeDbData))
    }

    data class NoCalcFieldWithKey(
        val fieldResult: NoCalcFieldResult,
        val key: MatchKey,
    )

    private fun filterFields(
        fields: List<FieldWithResult>,
        meta: Map<String, Map<String, Any>>,
    ): Pair<List<FieldWithResult>, List<NoCalcFieldWithKey>> =
        fields
            .filterNot { meta[it.name.name]?.get(DontExport.META_INFO) == true }
            .separateByType {
                if (it.result is NoCalcFieldResult) {
                    it to NoCalcFieldWithKey(it.result, it.name.toMatchKey())
                } else {
                    it to null
                }
            }

    private fun bomNodeId(manufacturingEntity: ManufacturingEntity): String? =
        when (manufacturingEntity) {
            is BomNodeReference ->
                manufacturingEntity
                    .getBomNodeSnapshot()
                    ?.subNodes
                    ?.find {
                        it.bomEntryId == manufacturingEntity._id
                    }?.bomNodeId
                    ?.toHexString()
            else -> null
        }

    data class DtoConversionContext(
        val accessCheck: AccessCheck,
        val exchangeRateMap: ExchangeRateMap,
        val latestMasterDataMap: Map<ObjectId, MasterDataMergeService.LatestMasterData>?,
        val interpolationBomInfo: InterpolationBomInfo,
        val parentShapeModelManufacturing: ManufacturingEntity?,
    ) {
        fun latestMasterData(objectId: ObjectId) = latestMasterDataMap?.get(objectId)
    }

    fun manufacturingEntityToDto(
        ctx: DtoConversionContext,
        rootEntity: ManufacturingEntity,
    ): Mono<ManufacturingDto> {
        updateExchangeRates(rootEntity, ctx.exchangeRateMap)
        return manufacturingEntityToDtoWithMasterdataCurrencies(
            ctx = ctx,
            rootEntity = rootEntity,
            entity = rootEntity,
        )
    }

    private fun updateExchangeRates(
        rootEntity: ManufacturingEntity,
        exchangeRateMap: ExchangeRateMap,
    ) {
        rootEntity.getExchangeRates()?.replaceFieldResult("exchangeRates") { oldResult ->
            (oldResult as ExchangeRatesField).withRes(exchangeRateMap.values)
        }
    }

    @TsetSuppress("tset:reactive:flux-flatmap", "tset:reactive:flux-flatmapsequential") // whitelisted already existing calls
    private fun manufacturingEntityToDtoWithMasterdataCurrencies(
        ctx: DtoConversionContext,
        rootEntity: ManufacturingEntity,
        entity: ManufacturingEntity,
        parent: ManufacturingEntity? = null,
    ): Mono<ManufacturingDto> {
        // Can be called for old, not yet recomputed entities.
        val unitOverrideContext = UnitOverrideContext.fromEntity(entity)
        val interpolationData = InterpolationData.fromEntity(entity, ctx.interpolationBomInfo)
        entity.setParent(parent)
        val nullValueHandling =
            if (entity.isolated) {
                NullValues.KEEP_EXTERNAL_DEPENDENCIES
            } else {
                NullValues.REMOVE
            }

        return instanceBasedMetaService
            .getEntityMetaInfo(
                ctx.accessCheck,
                entity,
                true,
                interpolationData,
            ).zipWith(
                instanceBasedMetaService.getEntityFields(
                    accessCheck = ctx.accessCheck,
                    entityType = entity::class.simpleName!!,
                    interpolationData = interpolationData,
                    entity = entity,
                ),
            ).flatMap { (entityMetaInfo, entityFieldInfo) ->
                (entity.fieldWithResults).traverse { fieldWithResult ->
                    val fieldName = fieldWithResult.name.name
                    fieldConversionService.fieldResultToFieldParameterWithOthers(
                        accessCheck = ctx.accessCheck,
                        fieldName = fieldName,
                        fieldResult = fieldWithResult.result,
                        entityClass = entity::class.simpleName!!,
                        otherFields = entity,
                        entity = entity,
                        exchangeRateMap = ctx.exchangeRateMap,
                        bomInfo = ctx.interpolationBomInfo,
                        unitOverrideContext = unitOverrideContext,
                        nullValueHandling = nullValueHandling,
                        entityForDynamicMetaData = entity,
                        interpolatedMetaInfo = entityMetaInfo[fieldName],
                        interpolatedFieldInfo = entityFieldInfo[fieldName],
                    )
                }
            }.flatMap { fieldParameterList ->
                val fieldParameterListSorted =
                    (
                        getIndirectExchangeRateFieldParameter(fieldParameterList).applyIfExists {
                            fieldParameterList.plus(it)
                        } ?: fieldParameterList
                    ).sortedBy { it.name }
                entity.children
                    .traverse { child ->
                        manufacturingEntityToDtoWithMasterdataCurrencies(
                            ctx = ctx,
                            rootEntity = rootEntity,
                            entity = child,
                            parent = entity,
                        )
                    }.map { childrenList ->
                        ManufacturingDto(
                            id = entity._id.toString(),
                            type = entity.getEntityTypeAnnotation(),
                            className = entity::class.simpleName,
                            name = entity.name,
                            version = entity.version,
                            data =
                                entity.fieldWithResults
                                    .sortedBy { it.name.name }
                                    .associateBy({ it.name.name }, { it.result.res!! }),
                            fields = fieldParameterListSorted,
                            part = extractPartDto(entity),
                            shape =
                                rootEntity
                                    .takeIf { it === entity && !it.hasParent() }
                                    ?.let {
                                        extractShapeDto(
                                            ctx.accessCheck,
                                            rootEntity,
                                            ctx.parentShapeModelManufacturing,
                                        )
                                    },
                            // only for root
                            children = childrenList.toMutableList(),
                            model = if (entity is BaseModelManufacturing) entity.model.toCalculationModel() else null,
                            ref = entity.entityRef ?: entity.name,
                            // TODO: simplify - FE only wants to know if an entity is deletable or not
                            createdByField = createdByToDto(entity.createdBy),
                            createdByMocked = entity.createdByMocked,
                            createdOnBranch = entity.createdOnBranch?.toHexString(),
                            masterDataKey =
                                MasterDataKeyDto.from(
                                    entity = entity,
                                    latestMasterData = ctx.latestMasterData(entity._id),
                                ),
                            title = if (entity is BaseModelManufacturing) entity.snapshot?.title else null,
                            deletable = entity.canBeDeleted(),
                            copyable = entity.canBeCopied(),
                            isolated = entity.isolated,
                            dynamicFields =
                                entity.dynamicFields.entries.associate { (k, v) ->
                                    k to
                                        DynamicFieldDto(
                                            v.entityClass,
                                            v.entityFieldName,
                                        )
                                },
                            customProcurementType =
                                (
                                    entity.fieldWithResults
                                        .singleOrNull {
                                            it.name.name ==
                                                CommercialCalculationCostManufacturedMaterial::customProcurementType.name
                                        }
                                        ?.result as? CustomProcurementType
                                )?.res,
                        )
                    }
            }
    }

    private fun getIndirectExchangeRateFieldParameter(fields: List<FieldParameter>): FieldParameter? =
        fields.find { it.name == "exchangeRate" }?.let {
            it.copy(name = "exchangeRateIndirect", value = getConversionRate(it.value as BigDecimal, true))
        }

    private fun createdByToDto(createdBy: FieldKey?): CreatedByFieldDto? =
        createdBy?.let {
            CreatedByFieldDto(
                name = it.name,
                type = it.type,
                entityId = it.entityId,
                entityType = it.entityType,
                entityRef = it.entityRef,
            )
        }

    private fun providerFieldToDto(providerField: FieldIdKey?): ProviderFieldDto? =
        providerField?.let {
            ProviderFieldDto(
                name = it.name,
                entityId = it.entityId,
            )
        }

    private fun extractPartDto(entity: ManufacturingEntity): PartDto? =
        if (entity is BaseManufacturing) {
            entity.getFieldResult("partDesignation")?.let {
                PartDto(
                    id = "",
                    designation = it.toString(),
                    number = entity.getFieldResult("partNumber")?.res?.toString() ?: "",
                    // TODO kw check if these are used anywhere at all
                    createdDate = null,
                    lastModifiedDate = null,
                )
            } ?: PartDto(
                id = "",
                // TODO kw check for side effects
                designation = "",//entity.partName,
                number = "",
                createdDate = null,
                lastModifiedDate = null,
            )
        } else {
            null
        }

    fun extractPartDtoOnlyId(entity: ManufacturingEntity): PartDto? =
        if (entity is BaseManufacturing) {
            PartDto(
                id = ObjectId.get().toHexString(),
                designation = entity.getFieldResult("partDesignation")?.res?.toString()!!,
                number = entity.getFieldResult("partNumber")?.res?.toString()!!,
                createdDate = null,
                lastModifiedDate = null,
            )
        } else {
            null
        }

    private fun getPartName(entity: ManufacturingEntity): String? =
        if (entity is BaseManufacturing) {
            entity.getFieldResult("partDesignation")?.res as? String?
                ?: entity.getFieldResult("partName")?.res as String?
        } else {
            null
        }

    fun extractShapeDto(
        accessCheck: AccessCheck,
        entity: ManufacturingEntity,
        parentShapeModelManufacturing: ManufacturingEntity?,
    ): ShapeDto? =
        (entity as? BaseModelManufacturing)?.let { ownModelManufacturing ->
            (parentShapeModelManufacturing as? BaseModelManufacturing)?.let { parentModelManufacturing ->
                val technologyModelFromField =
                    ownModelManufacturing
                        .getFieldResult("technologyModel")
                        ?.res
                        ?.takeIf { !(it as? String).isNullOrEmpty() }
                        ?.let { it as? String }
                        ?.let { Model.fromEntity(it) }
                val fallbackTechnologyModel = parentModelManufacturing.model
                val isolatedTechnologyModel =
                    parentModelManufacturing
                        .getFieldResult(RAW_PART_TECHNOLOGY)
                        ?.res
                        .applyIfExists {
                            when (it) {
                                is String -> Model.valueOf(it)
                                else -> null
                            }
                        } ?: technologyModelFromField ?: fallbackTechnologyModel
                val shapeId = entity.getShapeIdResult()

                val oldTechnologyModel =
                    (
                        ownModelManufacturing.getFieldResult(RAW_PART_TECHNOLOGY)?.res
                            ?: parentModelManufacturing.model.name
                    ).applyIfExists {
                        when (it) {
                            is String -> Model.valueOf(it)
                            else -> null
                        }
                    } ?: entity.model

                val technologyModel =
                    if (fallbackTechnologyModel == Model.MANUAL) {
                        isolatedTechnologyModel
                    } else {
                        oldTechnologyModel
                    }

                shapesService.getByIdCheck(accessCheck, technologyModel, shapeId)?.let { shapeInfo ->
                    shapeDtoConversionService.shapeInfoToDto(accessCheck, shapeInfo)
                }
            }
        }
}

private fun bomRelationToLink(it: BomEntryRelation): BomLink =
    BomLink(
        bomNodeId = it.bomNodeId.toHexString(),
        bomEntryId = it.bomEntryId.toString(),
    )
