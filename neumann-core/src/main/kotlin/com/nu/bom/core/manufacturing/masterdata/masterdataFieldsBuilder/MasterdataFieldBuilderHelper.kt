package com.nu.bom.core.manufacturing.masterdata.masterdataFieldsBuilder

import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.entityfieldmetainfo.DisplayLabelEntityFieldMetaInfo
import com.nu.bom.core.manufacturing.annotations.entityfieldmetainfo.FieldConversionBehaviour
import com.nu.bom.core.manufacturing.annotations.entityfieldmetainfo.FieldConversionEntityFieldMetaInfo
import com.nu.bom.core.manufacturing.annotations.entityfieldmetainfo.LabelEntityFieldMetaInfo
import com.nu.bom.core.manufacturing.annotations.entityfieldmetainfo.ObjectViewEntityFieldMetaInfo
import com.nu.bom.core.manufacturing.annotations.entityfieldmetainfo.PathEntityFieldMetaInfo
import com.nu.bom.core.manufacturing.configurablefields.config.FieldConfig
import com.nu.bom.core.manufacturing.configurablefields.config.ParameterConfig
import com.nu.bom.core.manufacturing.customfields.CustomFieldsBuilder.Companion.classificationPath
import com.nu.bom.core.manufacturing.customfields.CustomFieldsBuilder.Companion.lovPath
import com.nu.bom.core.manufacturing.customfields.CustomFieldsBuilder.Companion.nameFieldName
import com.nu.bom.core.manufacturing.customfields.CustomFieldsContext
import com.nu.bom.core.manufacturing.fieldTypes.Date
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.masterdata.ClassificationFieldDefinition
import com.nu.bom.core.manufacturing.fieldTypes.masterdata.DateFieldDefinition
import com.nu.bom.core.manufacturing.fieldTypes.masterdata.FieldDefinition
import com.nu.bom.core.manufacturing.fieldTypes.masterdata.LovFieldDefinition
import com.nu.bom.core.manufacturing.fieldTypes.masterdata.NumericFieldDefinition
import com.nu.bom.core.manufacturing.fieldTypes.masterdata.TextFieldDefinition
import com.nu.bom.core.manufacturing.service.FieldType
import kotlin.reflect.full.starProjectedType
import kotlin.reflect.full.valueParameters

@Suppress("TooManyFunctions")
object MasterdataFieldBuilderHelper {
    fun buildField(
        fieldDef: FieldDefinition,
        objectView: String?,
        index: Int,
        dataSourceInput: Boolean?,
    ): List<FieldConfig> =
        when (fieldDef) {
            is NumericFieldDefinition -> listOf(buildNumericField(fieldDef, objectView, index, dataSourceInput))
            is LovFieldDefinition -> buildLovField(fieldDef, objectView, index, dataSourceInput)
            is DateFieldDefinition -> listOf(buildDateField(fieldDef, objectView, index, dataSourceInput))
            is TextFieldDefinition -> listOf(buildTextField(fieldDef, objectView, index, dataSourceInput))
            is ClassificationFieldDefinition -> buildClassificationField(fieldDef, objectView, index, dataSourceInput)
        }

    private fun createFieldName(
        field: FieldDefinition,
        objectView: String?,
    ): String = createFieldName(field.key, objectView)

    fun createFieldName(
        fieldKey: String,
        objectView: String? = null,
    ): String =
        CLASSIFICATION_FIELD_PREFIX +
            (objectView?.let { "$it-" } ?: "") +
            MasterdataEffectivityFieldsHelper.sanitizeFieldName(fieldKey)

    private fun buildNumericField(
        numericFieldDefinition: NumericFieldDefinition,
        objectView: String?,
        index: Int,
        dataSourceInput: Boolean?,
    ): FieldConfig {
        val mapping = MasterdataFieldMappingHelper.getFieldTypeAndMetaInfoForNumericField(numericFieldDefinition)
        return FieldConfig(
            fieldName = createFieldName(numericFieldDefinition, objectView),
            kFunction = CustomFieldsContext::identityNullDefault,
            fieldClass = mapping.fieldClass,
            parameters = emptyMap(),
            entityFieldMetaInfos = baseMeta(numericFieldDefinition, objectView, index) + mapping.metadata,
            availableOutsideTheEngine = true,
            dataSourceInputOverride = dataSourceInput,
            keepOld = dataSourceInput == true,
            returnTypeOverride = mapping.fieldClass.starProjectedType,
            fieldType = FieldType.Calculation(FieldType.CalculationFieldType.NORMAL, FieldType.InputStatus.INPUT),
        )
    }

    private fun buildDateField(
        dateFieldDefinition: DateFieldDefinition,
        objectView: String?,
        index: Int,
        dataSourceInput: Boolean?,
    ): FieldConfig =
        FieldConfig(
            fieldName = createFieldName(dateFieldDefinition, objectView),
            kFunction = CustomFieldsContext::identityNullDefault,
            fieldClass = Date::class,
            parameters = emptyMap(),
            entityFieldMetaInfos = baseMeta(dateFieldDefinition, objectView, index),
            availableOutsideTheEngine = true,
            dataSourceInputOverride = dataSourceInput,
            keepOld = dataSourceInput == true,
            returnTypeOverride = Date::class.starProjectedType,
            fieldType = FieldType.Calculation(FieldType.CalculationFieldType.NORMAL, FieldType.InputStatus.INPUT),
        )

    private fun buildTextField(
        textFieldDefinition: TextFieldDefinition,
        objectView: String?,
        index: Int,
        dataSourceInput: Boolean?,
    ): FieldConfig =
        FieldConfig(
            fieldName = createFieldName(textFieldDefinition, objectView),
            kFunction = CustomFieldsContext::identityNullDefault,
            fieldClass = Text::class,
            parameters = emptyMap(),
            entityFieldMetaInfos = baseMeta(textFieldDefinition, objectView, index),
            availableOutsideTheEngine = true,
            dataSourceInputOverride = dataSourceInput,
            keepOld = dataSourceInput == true,
            returnTypeOverride = Text::class.starProjectedType,
            fieldType = FieldType.Calculation(FieldType.CalculationFieldType.NORMAL, FieldType.InputStatus.INPUT),
        )

    private fun buildLovField(
        lovFieldDefinition: LovFieldDefinition,
        objectView: String?,
        index: Int,
        dataSourceInput: Boolean?,
    ): List<FieldConfig> {
        val fieldName = createFieldName(lovFieldDefinition, objectView)
        return listOf(
            FieldConfig(
                fieldName = fieldName,
                kFunction = CustomFieldsContext::identityNullDefault,
                fieldClass = Text::class,
                parameters = emptyMap(),
                entityFieldMetaInfos =
                    baseMeta(
                        lovFieldDefinition,
                        objectView,
                        index,
                    ) + lovMeta(lovFieldDefinition.fieldSchema.lovTypeKey, fieldName),
                availableOutsideTheEngine = true,
                dataSourceInputOverride = dataSourceInput,
                keepOld = dataSourceInput == true,
                returnTypeOverride = Text::class.starProjectedType,
                fieldType = FieldType.Calculation(FieldType.CalculationFieldType.NORMAL, FieldType.InputStatus.INPUT),
            ),
            FieldConfig(
                fieldName = nameFieldName(fieldName),
                kFunction = CustomFieldsContext::lovEntryName,
                fieldClass = Text::class,
                parameters = lovNameParam(fieldName),
                entityFieldMetaInfos = emptyList(),
                availableOutsideTheEngine = true,
                dataSourceInputOverride = dataSourceInput,
            ),
        )
    }

    private fun lovNameParam(costFieldName: String) =
        mapOf(
            CustomFieldsContext::lovEntryName.valueParameters.first().name!! to
                listOf(
                    ParameterConfig(costFieldName),
                ),
        )

    private fun lovMeta(
        typeKey: String,
        fieldName: String,
    ) = listOf(
        PathEntityFieldMetaInfo(lovPath(typeKey), remoteSearch = false, grayOutMissingItem = true),
        LabelEntityFieldMetaInfo(nameFieldName(fieldName)),
    )

    private fun buildClassificationField(
        classificationFieldDefinition: ClassificationFieldDefinition,
        objectView: String?,
        index: Int,
        dataSourceInput: Boolean?,
    ): List<FieldConfig> {
        val fieldName = createFieldName(classificationFieldDefinition, objectView)
        return listOf(
            FieldConfig(
                fieldName = fieldName,
                kFunction = CustomFieldsContext::identityNullDefault,
                fieldClass = Text::class,
                parameters = emptyMap(),
                entityFieldMetaInfos =
                    baseMeta(
                        classificationFieldDefinition,
                        objectView,
                        index,
                    ) + classificationMeta(classificationFieldDefinition.fieldSchema.classificationTypeKey, fieldName),
                availableOutsideTheEngine = true,
                dataSourceInputOverride = dataSourceInput,
                keepOld = dataSourceInput == true,
                returnTypeOverride = Text::class.starProjectedType,
                fieldType = FieldType.Calculation(FieldType.CalculationFieldType.NORMAL, FieldType.InputStatus.INPUT),
            ),
            FieldConfig(
                fieldName = nameFieldName(fieldName),
                kFunction = CustomFieldsContext::classificationName,
                fieldClass = Text::class,
                parameters = classificationNameParam(fieldName),
                entityFieldMetaInfos = emptyList(),
                availableOutsideTheEngine = true,
                dataSourceInputOverride = dataSourceInput,
            ),
        )
    }

    private fun classificationNameParam(costFieldName: String) =
        mapOf(
            CustomFieldsContext::classificationName.valueParameters.first().name!! to
                listOf(
                    ParameterConfig(costFieldName),
                ),
        )

    private fun classificationMeta(
        typeKey: String,
        fieldName: String,
    ) = listOf(
        PathEntityFieldMetaInfo(classificationPath(typeKey), remoteSearch = false, grayOutMissingItem = true),
        LabelEntityFieldMetaInfo(nameFieldName(fieldName)),
    )

    private fun baseMeta(
        fieldDef: FieldDefinition,
        objectView: String?,
        index: Int,
    ) = listOf(
        DisplayLabelEntityFieldMetaInfo(fieldDef.fieldSchema.displayName ?: fieldDef.key),
        ObjectViewEntityFieldMetaInfo(objectView ?: ObjectView.CUSTOM_FIELDS, index, mode = ObjectView.Mode.both),
        // we add ERROR_IS_NULL conversion metadata since we don't want to throw an error if for some reason
        // an incompatible type change of a masterdata field occurs.
        // In such cases we prefer to throw away the old value, then throwing an error
        FieldConversionEntityFieldMetaInfo(FieldConversionBehaviour.ERROR_IS_NULL),
    )
}

private const val CLASSIFICATION_FIELD_PREFIX = "#classification_field_"
