package com.nu.bom.core.service.imports

import com.fasterxml.jackson.databind.ObjectMapper
import com.jayway.jsonpath.Configuration
import com.jayway.jsonpath.DocumentContext
import com.jayway.jsonpath.JsonPath
import com.jayway.jsonpath.Option
import com.mongodb.reactivestreams.client.MongoDatabase
import com.nu.bom.core.milldrill.model.MillingDrillingProfile
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.TurningProfile
import com.nu.bom.core.model.toUUID
import com.nu.bom.core.publicapi.dtos.ProjectDetails
import com.nu.bom.core.publicapi.service.PublicApiDtoConverter
import com.nu.bom.core.repository.NoCalcDataWrapper
import com.nu.bom.core.service.ProjectService
import com.nu.bom.core.service.bomrads.BomradsBomNodeService
import com.nu.bom.core.service.bomrads.BomradsFileUploadService
import com.nu.bom.core.service.bomrads.BomradsImportService
import com.nu.bom.core.service.file.SecureFileService
import com.nu.bom.core.service.file.UploadType
import com.nu.bom.core.service.file.UploadablePayload
import com.nu.bom.core.threedb.ThreeDbService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.nu.bomrads.dto.FileUploadDto
import com.nu.bomrads.dto.ImportViewDTO
import com.nu.bomrads.dto.admin.BomNodeDTO
import com.nu.bomrads.dto.admin.ImportDTO
import com.nu.bomrads.enumeration.BomNodeStatus
import com.nu.bomrads.enumeration.MigratedEnum
import com.nu.bomrads.id.BomNodeId
import com.nu.bomrads.id.FolderId
import org.bson.Document
import org.bson.types.ObjectId
import org.slf4j.LoggerFactory
import org.springframework.data.mongodb.ReactiveMongoDatabaseFactory
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.io.File
import java.time.Instant
import kotlin.reflect.KClass
import kotlin.reflect.KMutableProperty
import kotlin.reflect.full.memberProperties

const val IMPORT_DEFAULT_BATCH_SIZE = 50

@Service
class ProjectImportService(
    private val objectMapper: ObjectMapper,
    private val projectService: ProjectService,
    private val reactiveMongoDb: ReactiveMongoDatabaseFactory,
    private val fileService: SecureFileService,
    private val bomradsImportService: BomradsImportService,
    private val bomradsFileUploadService: BomradsFileUploadService,
    private val threeDbService: ThreeDbService,
    private val bomradsBomNodeService: BomradsBomNodeService,
) {
    private val logger = LoggerFactory.getLogger(ProjectImportService::class.java)!!

    fun importProjectFromTsetFile(
        accessCheck: AccessCheck,
        files: List<File>,
        projectKey: String,
        import: ImportDTO,
        folderId: String,
    ): Mono<ProjectDetails> =
        importProjectFromTsetZipArchive(
            accessCheck,
            files,
            projectKey,
            import,
            folderId,
        )

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun loadMongoEntities(
        mongoDatabase: MongoDatabase,
        collection: String,
        files: Flux<IdEntry<String>>,
    ): Mono<Long> =
        files
            .map { file ->
                Document.parse(file.data)
            }.buffer(IMPORT_DEFAULT_BATCH_SIZE)
            .flatMap { batch ->
                mongoDatabase.getCollection(collection).insertMany(batch)
            }.count()

    private fun <T : Any> mapBomradsEntities(
        type: KClass<T>,
        parsedFiles: Flux<IdEntry<String>>,
        import: ImportDTO,
    ): Flux<T> =
        parsedFiles.map {
            val entity = objectMapper.readValue(it.data, type.java)
            val property =
                entity::class.memberProperties.find { member -> member.name == "importField" }
            property?.let {
                if (property is KMutableProperty<*>) {
                    property.setter.call(entity, import)
                }
            }
            entity
        }

    private fun importProjectFromTsetZipArchive(
        accessCheck: AccessCheck,
        filesToImport: List<File>,
        projectKey: String,
        import: ImportDTO,
        folderId: String,
    ): Mono<ProjectDetails> {
        logger.info("Import project - zip file - input stream created - $projectKey")
        return ImportParser
            .archiveContents(
                filesToImport,
                projectKey,
                accessCheck.asAccountId().toHexString(),
                threeDbService,
                accessCheck,
                objectMapper,
            ).flatMap { parsedFiles ->
                val projectName = parsedFiles.newProjectName()

                projectService
                    .createProject(
                        accessCheck,
                        name = projectName,
                        key = parsedFiles.newProjectKey,
                        folderId = FolderId(folderId),
                        importId = import.id,
                        // we generated mongo compatible ids on load, so this is valid
                        projectId = ObjectId(parsedFiles.projectDto._id),
                    ).map { it.project!! }
                    .flatMap { project ->
                        reactiveMongoDb.mongoDatabase.map { db ->
                            Triple(project, PublicApiDtoConverter.toProjectDetails(project), db)
                        }
                    }.flatMap { (project, projectDto, mongoDatabase) ->
                        val bomradsProjectId = project.id

                        // Parts are no longer used, so we don't need to load them
                        val loadTurningProfiles =
                            loadMongoEntities(mongoDatabase, TurningProfile.COLLECTION_NAME, parsedFiles.turningProfiles())
                        val loadMillingProfiles =
                            loadMongoEntities(
                                mongoDatabase,
                                MillingDrillingProfile.COLLECTION_NAME,
                                parsedFiles.millingProfiles(),
                            )
                        val loadNoCalcData =
                            loadMongoEntities(mongoDatabase, NoCalcDataWrapper.COLLECTION_NAME, parsedFiles.noCalcData())
                        val loadSnapshots =
                            loadMongoEntities(mongoDatabase, BomNodeSnapshot.COLLECTION_NAME, parsedFiles.snapshots())

                        val fileDocumentsLoad =
                            saveAttachmentFilesAndRetrieveFileDocuments(
                                parsedFiles.attachments(),
                                parsedFiles.fileDocuments(),
                                accessCheck,
                            ).map { file ->
                                // this will stop the import if a file is not well-formed
                                val document = objectMapper.readValue(file, FileUploadDto::class.java)
                                FileUploadDto(
                                    document.id,
                                    document.uploadType,
                                    document.uploadId,
                                    document.ownerType,
                                    document.ownerId,
                                    document.filename,
                                    document.mimeType,
                                    document.deleted,
                                    MigratedEnum.CREATED,
                                )
                            }.collectList()
                                .flatMap { fileUploads ->
                                    bomradsFileUploadService.saveBatch(accessCheck, fileUploads)
                                }

                        val deletedBomNodesLoad =
                            parsedFiles
                                .deletedBomNodesKeys()
                                .map { key ->
                                    BomNodeDTO(
                                        id = BomNodeId(key.toUUID()),
                                        version = 0,
                                        lastModified = Instant.now(),
                                        created = Instant.now(),
                                        deleted = true,
                                        status = BomNodeStatus.DONE,
                                        importField = import,
                                    )
                                }.collectList()
                                .flatMap { batch ->
                                    bomradsImportService.insertNodes(
                                        accessCheck,
                                        projectId = bomradsProjectId,
                                        importId = import.id,
                                        bomNodes = batch,
                                    )
                                }

                        val bomNodesToLoad = mapBomradsEntities(BomNodeDTO::class, parsedFiles.bomNodes(), import).collectList()
                        val protectedNodeIds =
                            bomNodesToLoad.map {
                                it.filter { it.protectedAt != null }.mapNotNull { it.id }
                            }
                        val loadBomNodes =
                            bomNodesToLoad
                                .flatMap { batch ->
                                    bomradsImportService.insertNodes(
                                        accessCheck,
                                        projectId = bomradsProjectId,
                                        importId = import.id,
                                        bomNodes = batch,
                                    )
                                }

                        val loadBranchTrees =
                            mapBomradsEntities(ImportViewDTO::class, parsedFiles.branches(), import)
                                .collectList()
                                .flatMap { branchImports ->
                                    bomradsImportService
                                        .insertBranchesAndChangesets(
                                            accessCheck,
                                            importId = import.id,
                                            projectId = bomradsProjectId,
                                            importViewDTOs = branchImports,
                                        ).thenReturn(branchImports.size)
                                }

                        // we have to protect bomnodes after we insert all the branches, otherwise insert of the branches fails,
                        // because node is already protected.
                        val protectedBomNodes =
                            protectedNodeIds.flatMap { nodeIds ->
                                bomradsBomNodeService.protectBomNode(accessCheck, nodeIds)
                            }
                        val finishImport = bomradsImportService.finishImport(accessCheck, import.id)

                        loadTurningProfiles
                            .then(loadMillingProfiles)
                            .then(loadNoCalcData)
                            .then(loadSnapshots)
                            .then(deletedBomNodesLoad)
                            .then(loadBomNodes)
                            .then(loadBranchTrees)
                            .then(fileDocumentsLoad)
                            .then(protectedBomNodes)
                            .then(finishImport)
                            .thenReturn(projectDto)
                    }
            }
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun saveAttachmentFilesAndRetrieveFileDocuments(
        attachments: Flux<AttachmentEntry<ByteArray>>,
        documentEntries: List<IdEntry<String>>,
        accessCheck: AccessCheck,
    ): Flux<String> =
        attachments.flatMap { (oldId, data) ->
            // this exists, since we export the attachments from these files. If document does not exist, then we want to stop, since something is corrupted
            val document = documentEntries.filter { it.data.contains(oldId) }.map { it.data }.first()
            val uploadType = UploadType.valueOf(getUploadType(document))
            val newId =
                fileService.save(uploadType, UploadablePayload.fromByteArray(oldId, data), accessCheck)
            newId.map { document.replace(oldId, it) }
        }

    private fun getUploadType(jsonFile: String): String {
        val document: DocumentContext =
            JsonPath.parse(
                jsonFile,
                Configuration.defaultConfiguration().addOptions(Option.DEFAULT_PATH_LEAF_TO_NULL),
            )
        return document.read("$.uploadType")
    }
}
