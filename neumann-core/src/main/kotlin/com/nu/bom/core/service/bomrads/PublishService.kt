package com.nu.bom.core.service.bomrads

import com.nu.bom.core.model.BaseTriggerAction
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.TriggerAction
import com.nu.bom.core.model.toBomNodeId
import com.nu.bom.core.model.toBranchId
import com.nu.bom.core.model.toManufacturingTreeId
import com.nu.bom.core.model.toMongoBomNodeId
import com.nu.bom.core.model.toMongoBranchId
import com.nu.bom.core.model.toMongoFormatStr
import com.nu.bom.core.model.toMongoProjectId
import com.nu.bom.core.model.toMongoSnapshotId
import com.nu.bom.core.service.BomNodeBranchStateService
import com.nu.bom.core.service.BomNodeSaver
import com.nu.bom.core.service.BomPublishService
import com.nu.bom.core.service.PostPublishService
import com.nu.bom.core.service.change.ChangeContext
import com.nu.bom.core.service.change.ChangedEntities
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.then
import com.nu.bomrads.dto.BranchViewDTO
import com.nu.bomrads.dto.MinimalBranchDTO
import com.nu.bomrads.dto.NodeCalculation
import com.nu.bomrads.dto.NodeSnapshotDTO
import com.nu.bomrads.dto.RecalculationResultDTO
import com.nu.bomrads.id.ManufacturingTreeId
import com.nu.bomrads.id.PublishProcessId
import com.tset.common.util.TopologicalSorter
import com.tset.core.module.bom.EventSourcingModule
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import java.time.Instant
import java.time.LocalDate
import java.util.Date
import com.nu.bom.core.model.BomNodeId as MongoBomNodeId

/**
 * Service to initiate publishes and pushing changes to Bomrads and handling the recalculation of the external parent nodes.
 */
@Service
class PublishService(
    private val bomNodeService: BomradsBomNodeService,
    private val bomNodeLoaderService: BomNodeLoaderService,
    private val postPublishService: PostPublishService,
    private val mapperService: BomradsObjectMapperService,
    private val bomNodeSaver: BomNodeSaver,
    private val eventSourcingModule: EventSourcingModule,
    private val bomNodeBranchStateService: BomNodeBranchStateService,
) {
    companion object {
        private val logger: Logger = LoggerFactory.getLogger(PublishService::class.java)
    }

    fun publish(
        accessCheck: AccessCheck,
        branchId: BranchId,
        bomNodeId: MongoBomNodeId?,
        publishTriggerAction: BaseTriggerAction,
        unpublishTriggerAction: BaseTriggerAction?,
    ): Mono<BomPublishService.PublishResult> {
        val correctBranchId = branchId.toBranchId()
        return bomNodeService
            .publishBranch(
                accessCheck,
                correctBranchId,
                publishTriggerAction,
                unpublishTriggerAction,
                bomNodeId?.toBomNodeId(),
            ).flatMap { publishResultDto ->
                logger.info(
                    "publish result for {} -> targetBranch: {} status.branch: {}",
                    correctBranchId.idToString(),
                    publishResultDto.publishTargetBranch,
                    publishResultDto.status.branch,
                )
                calculateExternalParents(
                    accessCheck,
                    publishResultDto.status,
                    publishResultDto.publishId,
                    publishResultDto.publishTargetBranch,
                    bomNodeId,
                ).flatMap { publishResult ->
                    eventSourcingModule
                        .publishGlobalRootBomNodeUpdated(
                            accessCheck,
                            publishResult.projectId,
                            publishResult.publishedNode.bomNodeId!!,
                        ).then {
                            bomNodeBranchStateService.updateOpenMerges(
                                publishResult.publishedNode.bomNodeId(),
                                branchId,
                                publishResult.branch.id(),
                            )
                        }.thenReturn(publishResult)
                }
            }
    }

    private fun calculateExternalParents(
        accessCheck: AccessCheck,
        branchView: BranchViewDTO,
        publishProcessId: PublishProcessId?,
        targetBranch: MinimalBranchDTO,
        bomNodeId: MongoBomNodeId?,
    ): Mono<BomPublishService.PublishResult> {
        val bomNodeIdToLoad =
            bomNodeId ?: branchView.branch.bomNodeId?.toMongoBomNodeId()
                ?: throw java.lang.IllegalArgumentException("Trying to publish a branch without rootnode and no publish node set")
        return if (nodesToRecalculate(branchView)) {
            calculateNewSnapshots(accessCheck, branchView, publishProcessId, targetBranch)
                .flatMap {
                    bomNodeLoaderService.loadSnapshots(
                        accessCheck,
                        branchView,
                        loadingMode = DirectParents(bomNodeIdToLoad),
                    )
                }.map { mapperService.toPublishResult(branchView, targetBranch, it.getRoot()) }
        } else {
            bomNodeLoaderService
                .loadSnapshots(
                    accessCheck,
                    branchView,
                    loadingMode = SingleNode(bomNodeIdToLoad),
                ).map { snapshotLoadin ->
                    mapperService.toPublishResult(branchView, targetBranch, snapshotLoadin.getRoot())
                }
        }.flatMap {
            eventSourcingModule
                .publishBomNodeSnapshotsUpdated(
                    listOf(
                        EventSourcingModule.SnapshotUpdatedInfo(
                            accountId = accessCheck.accountId,
                            accountName = accessCheck.accountName,
                            projectId = branchView.projectId.toMongoFormatStr(),
                            bomNodeId = it.publishedNode.bomNodeId().toHexString(),
                            branchId = targetBranch.id.toMongoBranchId().toHexString(),
                            userId = accessCheck.userId,
                            accessedDate = Date.from(Instant.now()),
                        ),
                    ),
                ).thenReturn(it)
        }
    }

    private fun nodesToRecalculate(branchView: BranchViewDTO) =
        branchView.snapshots.any { nodeSnapshotDTO ->
            nodeSnapshotDTO.treeId == null
        }

    data class PublishResult(val parentUpdateResults: List<RecalculationResultDTO> = emptyList()) {
        operator fun plus(dto: RecalculationResultDTO): PublishResult {
            return copy(parentUpdateResults = parentUpdateResults + dto)
        }
    }

    /**
     * Recalculate a couple of snapshots based on the current status plus the updated child nodes.
     * Called when external parents were re
     */
    fun calculateNewSnapshots(
        accessCheck: AccessCheck,
        branchView: BranchViewDTO,
        publishProcessId: PublishProcessId?,
    ): Mono<PublishResult> {
        val steps = sortNodes(branchView)
        return Mono
            .just(emptyMap<com.nu.bomrads.id.BomNodeId, NodeCalculation>() to steps)
            .expand { (results, stepsToDo) ->
                if (stepsToDo.isNotEmpty()) {
                    val step = stepsToDo.first()
                    executeStep(accessCheck, branchView, step, results).map {
                        // add the RecalculationResultDTO to the end of the result list, and drop the first step - which is done.
                        val newResult = results + it
                        newResult to stepsToDo.drop(1)
                    }
                } else {
                    Mono.empty()
                    // Mono.just(results to stepsToDo)
                }
            }.last()
            .flatMap { (results, _) ->
                bomNodeService
                    .updateCalculation(
                        accessCheck,
                        branchView.projectId,
                        branchView.branch.id,
                        RecalculationResultDTO(
                            branchView.changeset.id(),
                            publishProcessId,
                            results,
                        ),
                    ).map {
                        PublishResult(listOf(it))
                    }
            }
    }

    private fun executeStep(
        accessCheck: AccessCheck,
        branchView: BranchViewDTO,
        step: PostPublishService.CalculationStep,
        result: RecalculationResultNodes,
    ): Mono<RecalculationResultNodes> {
        // We need to replace with
        val snapshotIdsToBomNodeIds =
            step.snapshots.associate {
                result.getLatestTreeId(it).toMongoSnapshotId() to it.bomNodeId()
            }

        return bomNodeLoaderService
            .loadSnapshotsForCalculationStep(
                accessCheck,
                branchView = branchView,
                step = step,
                snapshotIdsToBomNodeIds = snapshotIdsToBomNodeIds,
            ).flatMap { snapshotLoadingResult ->
                executeStep(
                    accessCheck,
                    branchView,
                    PostPublishService.CalculationStepWithRoot(step, snapshotLoadingResult.getRoot()),
                )
            }
    }

    private fun executeStep(
        accessCheck: AccessCheck,
        branchView: BranchViewDTO,
        step: PostPublishService.CalculationStepWithRoot,
    ): Mono<RecalculationResultNodes> {
        val changeEntities = create(step.root)
        return postPublishService
            .calculateNodeInContext(
                accessCheck,
                step,
                changeEntities,
            ).flatMap {
                if (changeEntities.didSnapshotChange(it)) {
                    bomNodeSaver.saveSnapshots(listOf(it))
                } else {
                    Mono.just(listOf(changeEntities.getOriginalSnapshot(it.bomNodeId())))
                }
            }.flatMap { allSaved ->
                val calculated = allSaved[0]
                eventSourcingModule
                    .publishBomNodeSnapshotsUpdated(
                        listOf(
                            EventSourcingModule.SnapshotUpdatedInfo(
                                accountId = accessCheck.accountId,
                                accountName = accessCheck.accountName,
                                projectId = branchView.projectId.toMongoFormatStr(),
                                bomNodeId = calculated.bomNodeId().toHexString(),
                                branchId =
                                    branchView.branch.id
                                        .toMongoBranchId()
                                        .toHexString(),
                                userId = accessCheck.userId,
                                accessedDate = Date.from(Instant.now()),
                            ),
                        ),
                    ).then(getSnapshotResults(accessCheck, calculated))
            }
    }

    private fun calculateNewSnapshots(
        accessCheck: AccessCheck,
        branchView: BranchViewDTO,
        publishProcessId: PublishProcessId?,
        targetBranch: MinimalBranchDTO,
    ): Mono<BomPublishService.PublishResult> =
        calculateNewSnapshots(accessCheck, branchView, publishProcessId).map {
            // There is no way, we can make any sense of the 'root' snapshot anymore after Branching 2.0
            val snapshot =
                BomNodeSnapshot(
                    name = "Mock",
                    year = LocalDate.now().year,
                    manufacturing = null,
                    partId = null,
                    title = "title",
                    accountId = accessCheck.asAccountId(),
                )
            snapshot.bomNodeId =
                com.nu.bom.core.model
                    .BomNodeId("111122223333444455556666")
            snapshot.projectId = branchView.projectId().toMongoProjectId()
            mapperService.toPublishResult(branchView, targetBranch, snapshot)
        }

    private fun sortNodes(branchViewDTO: BranchViewDTO): List<PostPublishService.CalculationStep> {
        val steps = groupByBomNode(branchViewDTO)
        val sorter =
            TopologicalSorter(
                nodes = steps,
                edges = { step ->
                    val parentRelations = step.root.externalParents

                    parentRelations.mapNotNull { relation ->
                        steps.find { parentStep ->
                            parentStep.rootId() == relation.bomNodeId.toMongoBomNodeId()
                        }
                    }
                },
            )
        val sortedNodes = sorter.sort()
        logger.info("order of calculation: {}", sortedNodes)
        return sortedNodes
    }

    private fun groupByBomNode(branchViewDTO: BranchViewDTO): List<PostPublishService.CalculationStep> =
        branchViewDTO
            .snapshots()
            .groupBy {
                it.bomNodeId
            }.mapNotNull { (rootId, snapshots) ->
                // Only need to recalculate, where any of the snapshot has a null treeId:
                if (snapshots.any { it.treeId() == null }) {
                    // just for a nicer exception ...
                    val root =
                        snapshots.firstOrNull { it.bomNodeId == rootId }
                            ?: error("Root node($rootId) not found among the snapshots: $snapshots")
                    val directChildren =
                        snapshots.flatMap { it.allChildren() }.mapNotNull { branchViewDTO.findByBomNodeId(it.bomNodeId()) }
                    val directParents =
                        snapshots.flatMap { it.externalParents }.mapNotNull { branchViewDTO.findByBomNodeId(it.bomNodeId) }
                    PostPublishService.CalculationStep(root, snapshots + directChildren + directParents)
                } else {
                    null
                }
            }

    private fun getSnapshotResults(
        accessCheck: AccessCheck,
        rootSnapshot: BomNodeSnapshot,
    ) = mapperService
        .serializeExtraNodeInfo(accessCheck, rootSnapshot)
        .defaultIfEmpty("")
        .map { meta ->
            mapOf(
                Pair(
                    rootSnapshot.bomNodeId().toBomNodeId(),
                    NodeCalculation(rootSnapshot.id().toManufacturingTreeId(), meta),
                ),
            )
        }

    /**
     * Create a bare-bone ChangedEntities
     */
    private fun create(nodeToPublish: BomNodeSnapshot): ChangedEntities {
        val changeContext =
            ChangeContext(
                triggerAction = TriggerAction(nodeToPublish.bomNodeId(), null),
                triggerBuilder = null,
            )
        return ChangedEntities.new(
            changeContext,
            branchViewDTO = nodeToPublish.originalRootSource,
            originalSnapshots = mapOf(nodeToPublish.bomNodeId() to nodeToPublish),
        )
    }
}

typealias RecalculationResultNodes = Map<com.nu.bomrads.id.BomNodeId, NodeCalculation>

fun RecalculationResultNodes.getLatestTreeId(node: NodeSnapshotDTO): ManufacturingTreeId =
    this[node.bomNodeId()]?.treeId ?: node.currentOrPreviousTreeId()
