package com.nu.bom.core.manufacturing.behaviours

import com.nu.bom.core.manufacturing.annotations.BehaviourCreation
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.KeepOld
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.MasterDataCalculation
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.FieldCalculatorType
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.service.behaviour.DynamicBehaviour
import com.nu.bom.core.manufacturing.service.behaviour.EntityBasedDynamicBehaviour

@EntityType(Entities.NONE)
class GrossWeightBehaviour(name: String) : ManufacturingEntity(name) {
    @MandatoryForEntity(
        index = 7,
        computed = true,
    )
    @MasterDataCalculation(false)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun grossWeightPerPart(
        netWeightPerPart: QuantityUnit,
        scrapWeightPerPart: QuantityUnit,
    ) = netWeightPerPart + scrapWeightPerPart

    @Input
    @MandatoryForEntity(index = 6, refresh = true)
    @MasterDataCalculation(false)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    @KeepOld
    fun scrapWeightPerPart(): QuantityUnit? = null

    @BehaviourCreation
    fun createNetWeightBehavior(netWeightCalculatorMode: FieldCalculatorType): DynamicBehaviour {
        return when (netWeightCalculatorMode) {
            FieldCalculatorType.DIRECT -> EntityBasedDynamicBehaviour(this, NetWeightPerPartDirectCalculation("Direct Net weight"))
            FieldCalculatorType.DETAILED -> EntityBasedDynamicBehaviour(this, NetWeightPerPartDetailedCalculation("Detailed Net weight"))
            else -> throw IllegalArgumentException("There was no suitable calculation type found.")
        }
    }
}
