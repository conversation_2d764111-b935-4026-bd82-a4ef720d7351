package com.nu.bom.core.model

import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bomrads.enumeration.BomNodeStatus
import com.tset.common.util.NOT_NULL_MULTILINE_STYLE
import org.apache.commons.lang3.builder.ToStringBuilder
import org.bson.types.ObjectId
import org.slf4j.LoggerFactory

/**
 * The central object for the manufacturing.
 *
 * Important: if you add new fields to this class, please ensure, that the 'clone' method is changed too.
 */
@Suppress("ktlint:standard:backing-property-naming")
data class BomNode(
    val name: String,
    var year: Int,
    // TODO: projectId is nullable, due to backward compatibility.
    @JsonSerialize(using = ToStringSerializer::class)
    val projectId: ProjectId?,
    /**
     * The Bom id - the root of the BomNode hierarchy. Only set in the root BomNode.
     */
    @JsonSerialize(using = ToStringSerializer::class)
    val rootOf: BomId? = null,
    // this can be null, when a BomNode is created as a child node, for a branch
    var masterHead: SnapshotId? = null,
    var lastPublishedBranch: BranchId? = null,
    private val branchHeads: MutableList<BranchSnapshotRelation> = mutableListOf(),
    /**
     * This Map is used to Save the HEADS for Snapshots used as external parents in branch calculations
     */
    private val externalParentHeads: MutableList<BranchSnapshotRelation> = mutableListOf(),
    /**
     * Points to the latest changeset, which is triggered on this BomNode - maybe there are other,
     * newer Changeset's which changed a snapshot in this BomNode, in a branch, but they are not triggered from this node.
     */
    private val latestChangesets: MutableMap<BranchId, ChangesetId> = mutableMapOf(),
    var latestMainChangeset: ChangesetId? = null,
    private val lastMergedChangesetsFromMain: MutableMap<BranchId, ChangesetId> = mutableMapOf(),
    private val lastChangesetTriggered: MutableMap<BranchId, ChangesetId> = mutableMapOf(),
    val generated: Boolean = false,
    var uploadId: String? = null,
    private var deleted: Boolean = false,
    var version: ObjectId? = ObjectId(),
    /**
     * Available for nodes created by the wizard directly (root or sub), empty for generated sub-calculations.
     * */
    val partId: PartId? = null,
    var status: BomNodeStatus = BomNodeStatus.TODO,
) {
    var _id: BomNodeId? = null

    private val branchHeadsMap: Map<BranchId, SnapshotId> get() = branchHeads.associateBy({ it.branch }, { it.snapshot })
    private val externalParentHeadsMap: Map<BranchId, SnapshotId> get() = externalParentHeads.associateBy({ it.branch }, { it.snapshot })

    fun getId() = _id!!

    override fun toString(): String =
        ToStringBuilder(this, NOT_NULL_MULTILINE_STYLE)
            .append("id", _id)
            .append("version", version)
            .append("name", name)
            .append("year", year)
            .build()

    @Deprecated("The only usage of the BomId - you don't need it")
    fun isRoot(): Boolean = rootOf != null

    fun isMain(branch: BranchId?): Boolean = branch == null || branch.id == this.lastPublishedBranch

    fun isBranchPointsTo(
        branch: BranchId?,
        snapshotId: SnapshotId,
    ) = if (isMain(branch)) {
        snapshotId == masterHead
    } else {
        snapshotId == this.branchHeadsMap.get(branch!!)
    }

    fun getAllBranches(): List<BranchId> = this.branchHeads.map { it.branch }

    /**
     * Return the masterHead if branch parameter is null.
     * If branch parameter not null, that the head snapshot of that branch, if exists, otherwise the master branch's head.
     * or null, if not set
     */
    fun getBranchOrMaster(branch: BranchId?): SnapshotId? =
        if (isMain(
                branch,
            )
        ) {
            this.masterHead
        } else {
            this.branchHeadsMap.getOrDefault(
                branch,
                this.externalParentHeadsMap.getOrDefault(branch, this.masterHead),
            )
        }

    /**
     * @return true, if the field is actually changed
     */
    fun setExternalParentHead(
        name: BranchId,
        snapshotId: SnapshotId?,
    ): Boolean = setExternalParentHeadImpl(name, snapshotId)

    private fun setExternalParentHeadImpl(
        name: BranchId,
        snapshotId: SnapshotId?,
    ): Boolean {
        val changed = this.externalParentHeads.removeIf { it.branch == name }
        return if (snapshotId != null) {
            val added = this.externalParentHeads.add(BranchSnapshotRelation(branch = name, snapshot = snapshotId))
            changed or added
        } else {
            changed
        }
    }

    fun setBranchHead(
        name: BranchId?,
        snapshotId: SnapshotId,
    ) {
        if (isMain(name)) {
            this.masterHead = snapshotId
        } else {
            setBranchHeadImpl(name!!, snapshotId)
        }
    }

    fun setBranchHead(snapshot: BomNodeSnapshot) {
        val snapshotBranch = snapshot.branch
        val snapshotId = snapshot._id!!
        if (isMain(snapshotBranch)) {
            this.masterHead = snapshotId
        } else {
            setBranchHeadImpl(snapshotBranch!!, snapshot._id!!)
        }
    }

    private fun setBranchHeadImpl(
        name: BranchId,
        snapshotId: SnapshotId,
    ) {
        this.branchHeads.removeIf { it.branch == name }
        this.branchHeads.add(BranchSnapshotRelation(branch = name, snapshot = snapshotId))
    }

    private fun setLatestChangeSetTriggeredId(
        name: BranchId,
        changeSetId: ChangesetId,
    ) {
        this.lastChangesetTriggered[name] = changeSetId
    }

    private fun getLatestChangeSetId(name: BranchId?): ChangesetId? =
        if (name == null) {
            this.latestMainChangeset
        } else {
            this.latestChangesets.get(name)
        }

    private fun getLatestTriggeredChangeSetId(name: BranchId?): ChangesetId? =
        this.lastChangesetTriggered.get(name) ?: this.latestChangesets.get(name)

    private fun getLatestTriggeredChangeSetIdFromMaster(): ChangesetId? =
        this.lastChangesetTriggered.get(this.lastPublishedBranch) ?: this.latestMainChangeset

    private fun setLastMergedChangeSetFromMain(
        branchId: BranchId,
        newLastMergeFromMaster: ChangesetId,
    ) {
        lastMergedChangesetsFromMain[branchId] = newLastMergeFromMaster
    }

    fun updateLastMergedChangeset(targetBranchId: BranchId) {
        // If Sourcebranch is master - set lastMergedMainCS to current master, and set it to last TriggeredCS of new branch as well
        val lastMergedMasterChangeSet = getLatestTriggeredChangeSetIdFromMaster()
        if (lastMergedMasterChangeSet != null) {
            setLastMergedChangeSetFromMain(targetBranchId, lastMergedMasterChangeSet)
            setLatestChangeSetTriggeredId(targetBranchId, lastMergedMasterChangeSet)
        }
    }

    fun createSnapshot(
        accountId: AccountId,
        manufacturing: ManufacturingEntity?,
        title: String,
    ): BomNodeSnapshot {
        val id = SnapshotId()
        val snapshot =
            BomNodeSnapshot(
                name = this.name,
                year = this.year,
                manufacturing = manufacturing,
                title = title,
                partId = manufacturing?.getOptionalPartId()?.orElse(null),
                accountId = accountId,
            )
        snapshot._id = id
        setBranchHead(null, id)
        return snapshot.setTransientState(this, null)
    }

    /**
     * Compare MAIN and [targetBranch] states of this node to assess its merge state.
     *
     * Possible outcomes are:
     * - [MergeType.MERGABLE] -> node state changed on MAIN -> changes from MAIN can be pulled onto the target branch
     * - [MergeType.CONFLICT] -> node state changed on both MAIN and [targetBranch] -> changes from MAIN are in conflict with changes on target branch
     * - null -> no change on MAIN
     *
     * */
    fun compareMainWithTarget(targetBranch: Branch): Merge? {
        if (targetBranch.id() == this.lastPublishedBranch || this.lastPublishedBranch == null) {
            return null
        }

        val targetBranchHead = this.branchHeadsMap[targetBranch.id()]
        val externalTargetSnapshot = this.externalParentHeadsMap[targetBranch.id()]

        val latestTriggeredChangesetFromMain = this.getLatestTriggeredChangeSetId(this.lastPublishedBranch!!)

        // node has a checked-out variant on target branch
        if (targetBranchHead != null && latestTriggeredChangesetFromMain != null) {
            // There was a change on master which was not merged yet into the targetbranch
            val lastMergedFromMainOnTarget = this.lastMergedChangesetsFromMain[targetBranch.id()]
            if (latestTriggeredChangesetFromMain != lastMergedFromMainOnTarget) {
                // There was a change in mybranch, and this change is not the same as the actual merged Change
                val latestTriggeredChangeSetId = this.getLatestTriggeredChangeSetId(targetBranch.id())
                if (latestTriggeredChangeSetId != null && latestTriggeredChangeSetId != lastMergedFromMainOnTarget) {
                    // => CONFLICT
                    return Merge(
                        sourceType = MergeSourceType.MASTER,
                        type = MergeType.CONFLICT,
                        previouslyMergedCS = lastMergedFromMainOnTarget,
                        newUnmergedCS = this.getLatestChangeSetId(targetBranch.id()),
                        // take snapshot from target
                        snapshotToUse = targetBranchHead,
                        sourceBranch = this.lastPublishedBranch!!,
                        bomNodeId = this.id(),
                    )
                } else {
                    // => Mergeable change from Sourcebranch
                    return Merge(
                        sourceType = MergeSourceType.MASTER,
                        type = MergeType.MERGABLE,
                        previouslyMergedCS = lastMergedFromMainOnTarget,
                        newUnmergedCS = this.getLatestChangeSetId(targetBranch.id()),
                        // take change from main
                        snapshotToUse = this.masterHead!!,
                        sourceBranch = this.lastPublishedBranch!!, // == main
                        bomNodeId = this.id(),
                    )
                }
            }
        } else {
            // node is used as external parent on the target branch
            if (externalTargetSnapshot != null) {
                // is external parent on targetBranch still in sync with master?
                if (externalTargetSnapshot != this.masterHead) {
                    // Mergeable change
                    return Merge(
                        sourceType = MergeSourceType.MASTER,
                        type = MergeType.MERGABLE,
                        previouslyMergedCS = null,
                        newUnmergedCS = null,
                        // take change from main
                        snapshotToUse = this.masterHead!!,
                        sourceBranch = this.lastPublishedBranch!!, // == main
                        bomNodeId = this.id(),
                    )
                }
            }
        }

        return null
    }

    fun id(): BomNodeId = _id ?: error("No ID for BomNode ( $this )")

    companion object {
        const val MAIN_BRANCH = "Main"

        val logger = LoggerFactory.getLogger(BomNode::class.java)!!

        fun create(
            name: String,
            year: Int,
            projectId: ProjectId,
            bomId: BomId? = null,
            partId: PartId? = null,
            uploadId: String? = null,
        ): BomNode {
            val bomNodeId = BomNodeId()

            val bomNode =
                BomNode(
                    name = name,
                    year = year,
                    projectId = projectId,
                    rootOf = bomId,
                    partId = partId,
                    uploadId = uploadId,
                )
            bomNode._id = bomNodeId
            return bomNode
        }
    }
}
