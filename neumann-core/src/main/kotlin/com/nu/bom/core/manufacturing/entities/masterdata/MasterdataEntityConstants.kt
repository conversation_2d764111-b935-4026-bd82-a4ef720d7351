package com.nu.bom.core.manufacturing.entities.masterdata

import com.nu.bom.core.manufacturing.enums.Entities

object MasterdataEntityConstants {
    val entityTypesToSkipLookup =
        setOf(
            Entities.MD_EXCHANGERATE,
            Entities.MD_INTEREST,
            Entities.MD_OVERHEAD,
            Entities.MD_COSTFACTORS_WAGE,
            Entities.MD_COSTFACTORS_LABOR_BURDEN,
            Entities.MD_COSTFACTORS_ELECTRICITY_PRICE,
            Entities.MD_COSTFACTORS_NATURAL_GAS_PRICE,
            Entities.MD_COSTFACTORS_ELECTRICITY_EMISSIONS,
            Entities.MD_COSTFACTORS_NATURAL_GAS_EMISSIONS,
            Entities.MD_COSTFACTORS_FLOOR_SPACE_PRICE,
            Entities.MD_COSTFACTORS_ALUMINIUM_SHARE,
            Entities.MD_COSTFACTORS_ALUMINIUM_EMISSIONS,
            Entities.MD_COSTFACTORS_OXYGEN_PRICE,
            Entities.MD_COSTFACTORS_CAST_EXCIPIENTS_PRICE,
            Entities.MD_COSTFACTORS_COUNTRY_INFO,
            Entities.MD_COSTFACTORS_INTEREST,
            Entities.MD_MATERIAL_PRICE,
            Entities.MD_MATERIAL_EMISSION,
        )

    val entityTypesToSkipLookupDuringImport = entityTypesToSkipLookup

    val entityTypesToRemoveSkipLookupDuringMdUpdate = entityTypesToSkipLookup
}
