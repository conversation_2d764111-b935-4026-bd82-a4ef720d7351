package com.nu.bom.core.threedb

import com.fasterxml.jackson.databind.ObjectMapper
import com.nu.bom.core.manufacturing.factories.FieldFactoryService
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.ThreeDbVersionedPart
import com.nu.bom.core.user.AccessCheck
import com.nu.http.EnvironmentNameSupplier
import com.nu.http.TsetService
import com.tset.bom.clients.RndApiService
import org.slf4j.LoggerFactory
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service
import org.springframework.util.LinkedMultiValueMap
import org.springframework.web.reactive.function.client.ClientResponse
import org.springframework.web.reactive.function.client.bodyToMono
import reactor.core.publisher.Mono
import java.util.UUID

@ConfigurationProperties(prefix = "three-db")
class ThreeDbProperties {
    lateinit var url: String
}

data class ThreeDbShapeIdentifier(
    @Suppress("PropertyName") val shape_id: String,
    val account: String,
    val part: ThreeDbVersionedPart?,
)

@Service
@Profile("!test")
@EnableConfigurationProperties(ThreeDbProperties::class)
class ThreeDbServiceImpl(
    tsetService: TsetService,
    private val fieldFactoryService: FieldFactoryService,
    private val properties: ThreeDbProperties,
    environmentNameSupplier: EnvironmentNameSupplier,
    private val objectMapper: ObjectMapper,
) : ThreeDbService {
    private val rndApiService: RndApiService = RndApiService(tsetService, environmentNameSupplier)

    private val logger = LoggerFactory.getLogger(ThreeDbServiceImpl::class.java)

    override fun getResource(
        header: ThreeDbHeader,
        resourceType: ThreeDbFieldType.Resource,
        useCalculatedValue: Boolean,
    ): Mono<FieldResultStar> =
        internalGetResource<String>(header, resourceType.resourceName, useCalculatedValue)
            .map {
                val tdbvalue = objectMapper.readValue(it, resourceType.resClazz.java)
                logger.info(">>> called getResource for '${resourceType.resourceName}'.")
                val result =
                    fieldFactoryService.parseFieldImpl(
                        resourceType.clazz.java,
                        tdbvalue,
                        resourceType.unit,
                        null,
                        currencyIsoCode = null,
                    )
                logger.info("result is of type ${result::class}")
                result
            }

    override fun getBinaryResource(
        header: ThreeDbHeader,
        resourceName: String,
        useCalculatedValue: Boolean,
    ): Mono<ByteArray> = internalGetResource<ByteArray>(header, resourceName, useCalculatedValue)

    override fun setBinaryResource(
        header: ThreeDbHeader,
        resourceName: String,
        resourceValue: ByteArray,
    ): Mono<ThreeDbVersionedPart> = internalSetResource(header, resourceValue, resourceName)

    override fun setResource(
        header: ThreeDbHeader,
        resourceType: ThreeDbFieldType.Resource,
        value: FieldResultStar,
    ): Mono<ThreeDbVersionedPart> {
        val dbValue = value.dbValue()
        requireNotNull(dbValue)
        val alt = objectMapper.writeValueAsString(dbValue)
        logger.info(
            ">>> calling setResource for '{}' with value '{}', valueAsString is '{}', alt '{}'",
            resourceType.resourceName,
            value,
            dbValue,
            alt,
        )
        return internalSetResource(header, alt, resourceType.resourceName)
    }

    override fun deleteResource(
        header: ThreeDbHeader,
        resourceType: ThreeDbFieldType.Resource,
    ): Mono<ThreeDbVersionedPart> {
        val generatedUUID = UUID.randomUUID().toString()
        logger.info(
            "delete called for resource '{}' | wizardId [{}] version [{}]",
            resourceType,
            generatedUUID,
            header.threeDbVersionedPart.version_id,
        )

        return rndApiService.delete(
            wizardId = generatedUUID,
            path = resourcePath(header.threeDbVersionedPart, resourceType.resourceName),
            endpoint = properties.url,
            successHandler = {
                it
                    .bodyToMono<ThreeDbVersionedPart>()
                    .doOnSuccess { versionedPart ->
                        logger.info(
                            "VersionedPart after deleting ${resourceType.resourceName}: " +
                                "changed from ${header.threeDbVersionedPart} to $versionedPart",
                        )
                    }
            },
            errorHandler = getErrorHandler(),
            jwtToken = getJwt(header.accessCheck),
            additionalHeaders = getAdditionalHeaders(header.accessCheck),
        )
    }

    override fun getSet(header: ThreeDbHeader): Mono<Set<String>> {
        val generatedUUID = UUID.randomUUID().toString()
        logger.info(
            "getSet called for wizardId [$generatedUUID] version [${header.threeDbVersionedPart.version_id}]",
        )

        return rndApiService.get(
            wizardId = generatedUUID,
            path = "${partPath(header.threeDbVersionedPart)}/set",
            endpoint = properties.url,
            successHandler = {
                it.bodyToMono<Set<String>>().doOnSuccess { setResources ->
                    logger.info("Got ${setResources.size} set resources.")
                }
            },
            errorHandler = getErrorHandler(),
            jwtToken = getJwt(header.accessCheck),
            additionalHeaders = getAdditionalHeaders(header.accessCheck),
        )
    }

    override fun exists(
        partId: String,
        accessCheck: AccessCheck,
    ): Mono<Boolean> {
        val wizardId = UUID.randomUUID().toString()
        logger.info("exists called | wizard_id [$wizardId]")
        return rndApiService.get(
            wizardId = wizardId,
            path = "api/parts/$partId/exists",
            endpoint = properties.url,
            successHandler = { it.bodyToMono<Boolean>() },
            errorHandler = getErrorHandler(),
            jwtToken = getJwt(accessCheck),
            additionalHeaders = getAdditionalHeaders(accessCheck),
        )
    }

    override fun createPart(accessCheck: AccessCheck): Mono<ThreeDbVersionedPart> {
        val generatedUUID = UUID.randomUUID().toString()
        logger.info("createPart called | wizardId [$generatedUUID]")
        return rndApiService.post(
            generatedUUID,
            "",
            "api/parts",
            properties.url,
            errorHandler = getErrorHandler(),
            jwtToken = getJwt(accessCheck),
            additionalHeaders = getAdditionalHeaders(accessCheck),
        )
    }

    override fun copyPart(
        accessCheck: AccessCheck,
        threeDBShapeIdentifier: ThreeDbShapeIdentifier,
    ): Mono<ThreeDbVersionedPart> {
        val generatedUUID = UUID.randomUUID().toString()
        logger.info("copyPart called | wizardId [{}] identifier [{}]", generatedUUID, threeDBShapeIdentifier)

        requireNotNull(threeDBShapeIdentifier.part) { "null as part is only valid for user upload shape" }
        val partId = threeDBShapeIdentifier.part.part_id
        val versionId = threeDBShapeIdentifier.part.version_id

        return rndApiService.post(
            generatedUUID,
            "",
            "api/copy/accounts/${threeDBShapeIdentifier.account}/parts/$partId/versions/$versionId",
            properties.url,
            errorHandler = getErrorHandler(),
            jwtToken = getJwt(accessCheck),
            additionalHeaders = getAdditionalHeaders(accessCheck),
        )
    }

    private inline fun <reified T : Any> internalGetResource(
        threeDbHeader: ThreeDbHeader,
        resourceName: String,
        useCalculatedValue: Boolean,
    ): Mono<T> {
        val generatedUUID = UUID.randomUUID().toString()
        logger.info(
            "getResource called for '{}' | wizard_id [{}] version [{}]",
            resourceName,
            generatedUUID,
            threeDbHeader.threeDbVersionedPart,
        )

        return rndApiService.get(
            wizardId = generatedUUID,
            path = resourcePath(threeDbHeader.threeDbVersionedPart, resourceName),
            endpoint = properties.url,
            queryParams = LinkedMultiValueMap(mapOf("use_calculated_value" to listOf(useCalculatedValue.toString()))),
            successHandler = { it.bodyToMono<T>() },
            errorHandler = getErrorHandler(),
            jwtToken = getJwt(threeDbHeader.accessCheck),
            additionalHeaders = getAdditionalHeaders(threeDbHeader.accessCheck),
        )
    }

    private inline fun <reified T : Any> internalSetResource(
        threeDbHeader: ThreeDbHeader,
        content: T,
        resourceName: String,
    ): Mono<ThreeDbVersionedPart> {
        val generatedUUID = UUID.randomUUID().toString()
        logger.info(
            "setResource called for '$resourceName' | wizardId [$generatedUUID] version [${threeDbHeader.threeDbVersionedPart.version_id}]",
        )
        return rndApiService.post(
            wizardId = generatedUUID,
            request = content,
            path = resourcePath(threeDbHeader.threeDbVersionedPart, resourceName),
            endpoint = properties.url,
            errorHandler = getErrorHandler(),
            jwtToken = getJwt(threeDbHeader.accessCheck),
            additionalHeaders = getAdditionalHeaders(threeDbHeader.accessCheck),
        )
    }

    // this means local nbk only works with local 3db
    private fun getJwt(accessCheck: AccessCheck): String = accessCheck.token

    private fun partPath(threeDbVersionedPart: ThreeDbVersionedPart): String =
        "api/parts/${threeDbVersionedPart.part_id}/versions/${threeDbVersionedPart.version_id}"

    private fun resourcePath(
        threeDbVersionedPart: ThreeDbVersionedPart,
        resourceType: String,
    ): String = "${partPath(threeDbVersionedPart)}//$resourceType"

    override fun getShapes(accessCheck: AccessCheck): Mono<List<ThreeDbShapeIdentifier>> {
        val wizardId = UUID.randomUUID().toString()

        logger.info("calling getShapes | wizardId [$wizardId]")

        return rndApiService.get(
            wizardId,
            path = "api/shapes",
            endpoint = properties.url,
            successHandler = {
                it
                    .bodyToMono<List<ThreeDbShapeIdentifier>>()
                    .doOnSuccess { shapes ->
                        logger.info("Got ${shapes.size} shapes")
                    }
            },
            errorHandler = getErrorHandler(),
            jwtToken = getJwt(accessCheck),
            additionalHeaders = getAdditionalHeaders(accessCheck),
        )
    }

    private fun getAdditionalHeaders(accessCheck: AccessCheck): Map<String, String> =
        mapOf("3db_nu_account_claim" to accessCheck.accountName)

    private fun <T> getErrorHandler(): (ClientResponse) -> Mono<T> =
        { errorResponse ->
            errorResponse
                .bodyToMono<String>()
                .map {
                    logger.info("ERROR: $it")
                    it
                }.switchIfEmpty(Mono.error(IllegalArgumentException("No body no crime.")))
                .handle { err, sink -> sink.error(RuntimeException(err)) }
        }
}
