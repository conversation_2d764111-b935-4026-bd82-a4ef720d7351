package com.nu.bom.core.manufacturing.service.behaviour

import com.nu.bom.core.manufacturing.configurablefields.config.EntityFieldConfigurations
import com.nu.bom.core.manufacturing.configurablefields.config.EntityFieldConfigurationsWithContext
import com.nu.bom.core.manufacturing.configurablefields.config.FieldConfig
import com.nu.bom.core.manufacturing.customfields.CustomFieldsContext
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.fieldTypes.masterdata.FieldDefinition
import com.nu.bom.core.manufacturing.masterdata.masterdataFieldsBuilder.MasterdataFieldBuilderHelper
import kotlin.reflect.KClass
import kotlin.reflect.full.isSubclassOf

data class MasterdataClassificationFieldsConfig(
    val entityKClass: KClass<out ManufacturingEntity>,
    val classificationFields: List<FieldDefinition>,
    val objectView: String?,
    val startIndex: Int? = null,
    val dataSourceInput: Boolean? = null,
    val includeObjectView: Boolean = true,
)

abstract class BaseMasterdataClassificationFieldsBuilder : GeneralDynamicFieldBuilder<MasterdataClassificationFieldsConfig> {
    override fun createFieldConfigurations(configuration: MasterdataClassificationFieldsConfig): EntityFieldConfigurationsWithContext =
        EntityFieldConfigurationsWithContext(
            entityFieldConfigs = getFieldMappings(configuration),
            executionContext = CustomFieldsContext("Classifications"),
        )

    @Suppress("UNCHECKED_CAST")
    protected fun hostClassNameToKClass(hostClazzName: String): KClass<out ManufacturingEntity> {
        val clazz = Class.forName(hostClazzName).kotlin
        require(clazz.isSubclassOf(ManufacturingEntity::class)) {
            "illegal host class for dynamic behaviour ${clazz.simpleName}"
        }
        return clazz as KClass<out ManufacturingEntity>
    }

    private fun getFieldMappings(configuration: MasterdataClassificationFieldsConfig): EntityFieldConfigurations {
        val fields: List<FieldConfig> =
            configuration.classificationFields.withIndex().flatMap {
                MasterdataFieldBuilderHelper.buildField(
                    it.value,
                    configuration.objectView,
                    configuration.startIndex ?: (DEFAULT_START_INDEX_FIELDS + it.index),
                    configuration.dataSourceInput,
                    configuration.includeObjectView,
                )
            }
        return EntityFieldConfigurations().apply {
            if (fields.isNotEmpty()) {
                add(configuration.entityKClass, fields)
            }
        }
    }
}

private const val DEFAULT_START_INDEX_FIELDS = 100
