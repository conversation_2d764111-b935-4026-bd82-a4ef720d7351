package com.nu.bom.core.utils

import com.nu.bom.core.utils.annotations.TsetSuppress
import kotlinx.coroutines.reactor.mono
import kotlinx.coroutines.sync.Mutex
import org.reactivestreams.Publisher
import reactor.core.Disposable
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.util.function.Tuple2
import reactor.util.function.Tuple3
import reactor.util.function.Tuple4
import reactor.util.function.Tuple5
import java.util.Optional
import java.util.concurrent.atomic.AtomicReference

operator fun <T1, T2> <PERSON>ple2<T1, T2>.component1(): T1 = this.t1

operator fun <T1, T2> <PERSON>ple2<T1, T2>.component2(): T2 = this.t2

operator fun <T1, T2, T3> Tuple3<T1, T2, T3>.component3(): T3 = this.t3

operator fun <T1, T2, T3, T4> <PERSON>ple4<T1, T2, T3, T4>.component4(): T4 = this.t4

operator fun <T1, T2, T3, T4, T5> <PERSON><PERSON>5<T1, T2, T3, T4, T5>.component1(): T1 = this.t1

operator fun <T1, T2, T3, T4, T5> <PERSON>ple5<T1, T2, T3, T4, T5>.component2(): T2 = this.t2

operator fun <T1, T2, T3, T4, T5> Tuple5<T1, T2, T3, T4, T5>.component3(): T3 = this.t3

operator fun <T1, T2, T3, T4, T5> Tuple5<T1, T2, T3, T4, T5>.component4(): T4 = this.t4

operator fun <T1, T2, T3, T4, T5> Tuple5<T1, T2, T3, T4, T5>.component5(): T5 = this.t5

/**
 * With this, it is easy to log the empty result of a Mono
 */
fun <T> Mono<T>.doOnEmpty(action: () -> Unit): Mono<T> = this.switchIfEmpty(Mono.empty<T>().doOnTerminate(action))

/**
 * With this, it is easy to log the empty result of a Flux
 */
fun <T> Flux<T>.doOnEmpty(action: () -> Unit): Flux<T> = this.switchIfEmpty(Flux.empty<T>().doOnTerminate(action))

fun <T> Mono<T>.exceptionOnEmpty(supplier: () -> Exception): Mono<T> {
    val onEmpty =
        Mono.defer<T> {
            Mono.error(
                supplier.invoke(),
            )
        }
    return this.switchIfEmpty(onEmpty)
}

fun <T> Mono<T>.exceptionOnEmpty(err: Exception): Mono<T> = this.switchIfEmpty(Mono.error(err))

fun <T> Flux<T>.exceptionOnEmpty(supplier: () -> Exception): Flux<T> {
    val onEmpty =
        Mono.defer<T> {
            Mono.error(
                supplier.invoke(),
            )
        }
    return this.switchIfEmpty(onEmpty)
}

fun <T> Flux<T>.exceptionOnEmpty(err: Exception): Flux<T> = this.switchIfEmpty(Mono.error(err))

fun <T> Flux<T>.pickFirstOrEmpty(): Mono<T> =
    this
        .take(1)
        .collectList()
        .flatMap {
            Mono.justOrEmpty(it.firstOrNull())
        }

fun <T : Any> Flux<T>.firstOrException(supplier: () -> Exception): Mono<T> =
    this
        .take(1)
        .exceptionOnEmpty(supplier)
        .collectList()
        .map { it.first() }

fun <T> Mono<T>.subscribeWithContextCapture(): Disposable = this.contextCapture().subscribe()

fun <T> Flux<T>.subscribeWithContextCapture(): Disposable = this.contextCapture().subscribe()

/**
 * Only map values that are not null.
 */
fun <T, U> Mono<T>.mapNotNull(mapper: (T) -> U?): Mono<U> = this.handle { item, sink -> mapper(item)?.let { sink.next(it) } }

object NoValue

fun <T> Mono<Mono<T>>.flatten() = this.flatMap { it }

fun <T, A : Any> Flux<T>.flatFold(
    init: Mono<A>,
    func: (A, T) -> Mono<A>,
): Mono<A> =
    init.flatMap {
        val acc = AtomicReference(it)
        this
            .concatMap { t ->
                func(acc.get(), t).map {
                    acc.set(it)
                    it
                }
            }.last()
    }

fun <Value, Folded : Any> Iterable<Value>.flatFold(
    initial: Mono<Folded>,
    combinator: (Folded, Value) -> Mono<Folded>,
): Mono<Folded> = Flux.fromIterable(this).flatFold(initial, combinator)

fun <A, B> Iterable<A>.traverse(fn: (A) -> Mono<B>): Mono<List<B>> = Flux.fromIterable(this).concatMap { fn(it) }.collectList()

fun <A, B, C : MutableCollection<B>> Iterable<A>.traverseTo(
    list: C,
    fn: (A) -> Mono<B>,
): Mono<C> =
    Flux.fromIterable(this).concatMap { fn(it) }.collect({ list }) { c, a ->
        c.add(a)
    }

/**
 * Sequentially applies [transformers] from left to right, using this [Mono] as the initial input.
 *
 * @return the output [Mono] of the last applied transformation.
 * */
fun <T> Mono<T>.mapSequential(transformers: List<(T) -> T>): Mono<T> =
    this.map { initialValue ->
        transformers.fold(initialValue) { currentValue, currentTransformer ->
            currentTransformer(currentValue)
        }
    }

/** Converts the [T] - including an empty emission - into an [Optional].
 *
 * The resulting [Mono] always emits a value.
 *
 * */
fun <T> Mono<T>.optional(): Mono<Optional<T & Any>> =
    this
        .map { Optional.of(it!!) }
        .defaultIfEmpty(Optional.empty<T>())

fun <T, R> Mono<T>.then(supplier: () -> Mono<R>): Mono<R> = this.then(Mono.defer(supplier))

fun <T, R> Flux<T>.then(supplier: () -> Mono<R>): Mono<R> = this.then(Mono.defer(supplier))

/**
 * Zips current [Mono] with new one created by [otherCreator] function which can result in null (not created).
 * In that case, the second object in tuple is [Optional.empty].
 *
 * @see Mono.zipWith
 * @param otherCreator callback crating other nullable Mono
 * @return [Tuple2] of this Mono and the object zipped with enclosed by [Optional]
 */
inline fun <T, T2> Mono<T>.zipWith(otherCreator: () -> Mono<out T2>?): Mono<Tuple2<T, Optional<T2 & Any>>> =
    zipWith(otherCreator()?.map { Optional.ofNullable(it) } ?: Mono.just(Optional.empty()))

/**
 * Create a paged list if the page and the size is bigger than 0
 */
fun <X> Flux<X>.paging(
    page: Long,
    size: Long,
): Mono<List<X>> =
    if (page >= 0 && size > 0) {
        this.skip((page * size)).take(size).collectList()
    } else {
        this.collectList()
    }

@TsetSuppress("tset:reactive:flux-flatmap")
fun <T, R> Flux<T>.flatMapWithConcurrency(
    concurrency: Int,
    mapper: (T) -> Publisher<R>,
): Flux<R> = flatMap(mapper, concurrency)

/**
 * returns a flux that emits unique values w.r.t. the projection.
 */
inline fun <T : Any, X> Flux<T>.unique(crossinline projection: (T) -> X): Flux<T> {
    val alreadySeen = mutableSetOf<X>()
    return this.filter { alreadySeen.add(projection(it)) }
}

fun <T : Any> Flux<T>.unique(): Flux<T> = this.unique { identity(it) }

fun <T : Any> Mono<T>.synchronized(
    alternative: T,
    lock: Mutex,
    useAlternative: () -> Boolean,
): Mono<T> =
    Mono.defer {
        if (useAlternative()) {
            Mono.just(alternative)
        } else {
            Mono
                .defer {
                    mono {
                        lock.lock()
                    }.then {
                        if (useAlternative()) {
                            Mono.just(alternative)
                        } else {
                            this
                        }
                    }
                }.map {
                    lock.unlock()
                    it
                }
        }
    }
