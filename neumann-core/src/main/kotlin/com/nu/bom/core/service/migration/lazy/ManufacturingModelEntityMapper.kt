package com.nu.bom.core.service.migration.lazy

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.entities.BaseManufacturingFields
import com.nu.bom.core.manufacturing.fieldTypes.ConfigIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.model.configurations.SemanticVersion
import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono

interface AsyncManufacturingModelEntityMapper {
    val changeSetId: MigrationChangeSetId

    fun mapAsync(entity: ManufacturingModelEntity): Mono<ManufacturingModelEntity>
}

interface ManufacturingModelEntityMapper : AsyncManufacturingModelEntityMapper {
    override fun mapAsync(entity: ManufacturingModelEntity): Mono<ManufacturingModelEntity> = map(entity).toMono()

    fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity

    fun addConfigurationFields(
        fields: Map<String, FieldResultModel>,
        version: Int,
        model: Model?,
        source: FieldResult.SOURCE,
        fieldName: String,
        upsertTechnology: Boolean,
    ): Map<String, FieldResultModel> {
        val mappedFields = fields.toMutableMap()
        if (upsertTechnology) {
            upsertField(
                mappedFields,
                BaseManufacturingFields::configurationTechnology.name,
                version,
                Text(model?.name ?: ""),
                source,
            )
        }
        upsertField(
            mappedFields,
            fieldName,
            version,
            ConfigIdentifier(
                model?.let {
                    ConfigurationIdentifier.tset(
                        "${model.name}_CONFIGURATION_KEY",
                        SemanticVersion.initialVersion(),
                    )
                } ?: ConfigurationIdentifier.empty(),
            ),
            source,
        )
        return mappedFields
    }

    private fun upsertField(
        fields: MutableMap<String, FieldResultModel>,
        fieldName: String,
        version: Int,
        value: FieldResultStar,
        source: FieldResult.SOURCE,
    ) {
        fields[fieldName] =
            FieldResultModel(
                version = version,
                newVersion = version,
                type = value::class.java.simpleName,
                value = value.res,
                source = source.name,
            )
    }
}

interface FieldMapper {
    fun mapFieldIfNotExist(
        entity: ManufacturingModelEntity,
        fieldName: String,
        fieldType: String,
        fieldValue: Any?,
    ): Map<String, FieldResultModel> {
        val fields = entity.fieldWithResults.toMutableMap()
        if (fields[fieldName]?.value == null) {
            fields[fieldName] =
                FieldResultModel(
                    entity.version,
                    entity.version,
                    fieldType,
                    fieldValue,
                    FieldResult.SOURCE.C.name,
                )
        }
        return fields
    }
}

data class MigrationChangeSetId(
    val id: String,
)
