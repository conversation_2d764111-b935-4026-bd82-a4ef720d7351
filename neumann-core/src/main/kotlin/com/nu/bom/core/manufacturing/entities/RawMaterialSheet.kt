package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.FieldName
import com.nu.bom.core.manufacturing.annotations.FilteredChildren
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntityContext
import com.nu.bom.core.manufacturing.annotations.MasterDataType
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.Selectable
import com.nu.bom.core.manufacturing.annotations.Technologies
import com.nu.bom.core.manufacturing.defaults.NullProvider
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.BarProcess
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.CastingAlloyMaterialGroup
import com.nu.bom.core.manufacturing.fieldTypes.HotOrColdRolled
import com.nu.bom.core.manufacturing.fieldTypes.MaterialPriceType
import com.nu.bom.core.manufacturing.fieldTypes.MaterialSubstances
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Pressure
import com.nu.bom.core.manufacturing.fieldTypes.PressureUnits
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SheetMaterialGroup
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.utils.findResultByTextAttribute
import org.bson.types.ObjectId
import reactor.core.publisher.Mono

@EntityType(Entities.MATERIAL)
@MasterDataType(com.nu.bom.core.manufacturing.enums.MasterDataType.RAW_MATERIAL_SHEET)
@Technologies([Model.CUBE])
class RawMaterialSheet(name: String) : ManufacturingEntity(name) {
    companion object {
        private const val ELONGATION_AT_BREAK_MASTER_DATA_FIELD =
            "#classification_field_tset-ref-field-elongationAtBreak"
        private const val SHEET_MATERIAL_GROUP_MASTER_DATA_FIELD =
            "#classification_field_tset-ref-field-sheetMaterialGroup"
        private const val ROLLING_TYPE_MASTER_DATA_FIELD =
            "#classification_field_tset-ref-field-rollingType"
        private const val TENSILE_STRENGTH_MASTER_DATA_FIELD =
            "#classification_field_tset-ref-field-tensileStrength"

        // classificationFields used in the lazy migration
        val classificationFields =
            mapOf(
                "elongationAtBreak" to ELONGATION_AT_BREAK_MASTER_DATA_FIELD,
                "sheetMaterialGroup" to SHEET_MATERIAL_GROUP_MASTER_DATA_FIELD,
                "rollingType" to ROLLING_TYPE_MASTER_DATA_FIELD,
                "tensileStrength" to TENSILE_STRENGTH_MASTER_DATA_FIELD,
            )
    }

    override val extends = RawMaterial(name)

    @Input
    fun designation(materialBaseDisplayDesignation: Text): Text = materialBaseDisplayDesignation

    @Input
    @MandatoryForEntity(index = 20, context = MandatoryForEntityContext.CREATE_FOR_MASTERDATA)
    @Selectable("sheetMaterialGroup")
    fun materialGroup(
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName(SHEET_MATERIAL_GROUP_MASTER_DATA_FIELD)
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        materialGroupFromClassification: Map<ObjectId, Text?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKeyFromClassification: Map<ObjectId, Text>?,
    ): Text? =
        findResultByTextAttribute(
            headerKey,
            materialGroupFromClassification,
            mapIdToHeaderKeyFromClassification,
        )

    fun materialGroupSheet(materialGroup: Text) = SheetMaterialGroup(materialGroup)

    fun materialGroupCasting(): CastingAlloyMaterialGroup? = null

    // necessary for backwards compatibility
    fun coldRolled(): Bool? = null

    @Input
    @ReadOnly
    @MandatoryForEntity(index = 21, context = MandatoryForEntityContext.CREATE_FOR_MASTERDATA)
    fun rollingType(
        coldRolled: Bool?,
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName(ROLLING_TYPE_MASTER_DATA_FIELD)
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        rollingTypeFromClassification: Map<ObjectId, HotOrColdRolled?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKeyFromClassification: Map<ObjectId, Text>?,
    ): HotOrColdRolled? =
        findResultByTextAttribute(
            headerKey,
            rollingTypeFromClassification,
            mapIdToHeaderKeyFromClassification,
        ) ?: when (coldRolled?.res) {
            true -> HotOrColdRolled.COLD_ROLLED
            false -> HotOrColdRolled.HOT_ROLLED
            null -> null
        }

    @Input
    @DefaultUnit(DefaultUnit.MEGAPASCAL)
    @MandatoryForEntity(index = 22, context = MandatoryForEntityContext.CREATE_FOR_MASTERDATA)
    fun tensileStrength(headerKey: Text?): Mono<Pressure> =
        headerKey?.let {
            services.findTensileStrength(
                accessCheck = calculationContext().accessCheck,
                materialKey = headerKey.res,
            ).switchIfEmpty(Mono.just(Pressure(500.0, PressureUnits.MEGAPASCAL)))
        } ?: Mono.just(Pressure(500.0, PressureUnits.MEGAPASCAL))

    @Input
    fun elongationAtBreak(
        headerKey: Text?,
        @Default(NullProvider::class)
        @FieldName(ELONGATION_AT_BREAK_MASTER_DATA_FIELD)
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        elongationAtBreakFromClassification: Map<ObjectId, Rate?>?,
        @Default(NullProvider::class)
        @FieldName("headerKey")
        @FilteredChildren(
            name = Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        mapIdToHeaderKeyFromClassification: Map<ObjectId, Text>?,
    ): Rate? =
        findResultByTextAttribute(
            headerKey,
            elongationAtBreakFromClassification,
            mapIdToHeaderKeyFromClassification,
        )

    @Input
    fun materialSubstances(): MaterialSubstances = MaterialSubstances(emptyList())

    // default value for creation from masterdata, for CUBE cost module set by ManufacturingStepCuttingCube
    fun sheetPrice(): Money = Money(0.0)

    @ObjectView(ObjectView.MATERIAL, 50)
    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun pricePerUnit(
        materialPriceType: MaterialPriceType,
        materialBasePrice: Money?,
        composedPricePerUnit: Money?,
        sheetPrice: Money,
    ): Money =
        when (materialPriceType.res) {
            MaterialPriceType.Selection.SIMPLE_PRICE -> (materialBasePrice ?: Money.ZERO) + sheetPrice
            MaterialPriceType.Selection.COMPOSED_PRICE -> (composedPricePerUnit ?: Money.ZERO) + sheetPrice
        }

    @Input
    @ReadOnly
    fun preheated(): Bool = Bool(false)

    @Input
    fun barProcess(): BarProcess = BarProcess.ROLLED
}
