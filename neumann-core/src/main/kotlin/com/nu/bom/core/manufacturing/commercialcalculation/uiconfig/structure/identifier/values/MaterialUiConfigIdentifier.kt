package com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.values

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo

@JsonTypeInfo(use = JsonTypeInfo.Id.DEDUCTION)
@JsonSubTypes(
    JsonSubTypes.Type(value = MaterialViewWrapper::class, name = "MaterialViewWrapper"),
    JsonSubTypes.Type(value = MaterialPriceTypeWrapper::class, name = "MaterialPriceTypeWrapper"),
)
interface MaterialUiConfigIdentifier : BaseUiConfigIdentifier
