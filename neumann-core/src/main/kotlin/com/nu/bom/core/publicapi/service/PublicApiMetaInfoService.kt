package com.nu.bom.core.publicapi.service

import com.nu.bom.core.exception.InternalServerException
import com.nu.bom.core.exception.readable.ErrorCode
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.Modularized
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.enums.TechnologyPageViewGroup
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.publicapi.dtos.FieldParameter
import com.nu.bom.core.publicapi.utils.PublicApiWildcard
import com.nu.bom.core.service.ExtendedShapeInfo
import com.nu.bom.core.service.MasterDataService
import com.nu.bom.core.service.MasterDataService.MasterDataWithProvenance
import com.nu.bom.core.service.ShapesService
import com.nu.bom.core.service.wizard.WizardCardService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.ShapesData
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toFlux

@Service
class PublicApiMetaInfoService(
    private val shapesService: ShapesService,
    private val masterDataService: MasterDataService,
    private val entityManager: EntityManager,
) {
    val logger: Logger = LoggerFactory.getLogger(PublicApiMetaInfoService::class.java)

    fun getActiveTechnologies(accessCheck: AccessCheck): List<Model> =
        Model.entries
            .filter { it.technologyPageViewGroup != TechnologyPageViewGroup.INACTIVE }
            .filter { WizardCardService.applyAccountSpecificFilter(accessCheck, it.displayName) }

    fun getModularizedTechnologies(): List<Model> =
        entityManager.getModularizedTechnologies(
            technology = null,
            entityTypes = setOf(Entities.MANUFACTURING_STEP, Entities.MATERIAL),
            dimension = Dimension.Selection.NUMBER,
        )

    fun getModularizedTechnologyEntityByEntityType(
        technology: String,
        entityType: Entities,
        dimension: Dimension.Selection = Dimension.Selection.NUMBER,
    ): List<Pair<Modularized, Class<out ManufacturingEntity>>> {
        val model = Model.valueOf(technology.uppercase())
        val modularizedEntities = entityManager.getModularizedEntities(model.entity, entityType, dimension, null) ?: return emptyList()
        return modularizedEntities.filter { it.first.userCreatable }
    }

    fun getModularizedMaterialPossibleMasterDataMaterials(
        accessCheck: AccessCheck,
        materialClassName: String,
    ): Flux<MasterDataWithProvenance> {
        val rawMaterialType =
            entityManager.getMasterDataTypeRecursively(materialClassName)
                ?: throw InternalServerException(ErrorCode.MASTER_DATA_TYPE_INVALID)
        return masterDataService.findByTypeAndTechAndKeyIncludingProvenance(accessCheck, rawMaterialType, null, null)
    }

    fun getModularizedMaterialMasterDataInputs(
        accessCheck: AccessCheck,
        key: String,
    ): Mono<List<FieldParameter>> =
        masterDataService
            .getMaterialMasterdata(key, accessCheck)
            .map { masterdataEntry ->
                masterdataEntry.data.map { materialInput ->
                    FieldParameter(
                        name = materialInput.key,
                        value = materialInput.value.res,
                        type = materialInput.value.getType(),
                        unit = materialInput.value.getUnit(),
                        source = null,
                    )
                }
            }

    fun entityContainsShapeIdField(entity: Class<out ManufacturingEntity>): Boolean? {
        val entityInfo = entityManager.getEntityInfo(entity) ?: return null
        return entityManager.getFieldInfo(entityInfo.name, "shapeId") != null
    }

    // no support for account specific shapes
    fun getActiveShapes(
        accessCheck: AccessCheck,
        technology: String,
    ): Mono<List<ExtendedShapeInfo>> =
        shapesService.getActiveShapes(accessCheck, wizardId = "publicApiMetaInfoService", technology).map { activeShape ->
            activeShape.filterNot { it.isAccountSpecificShape() || isUserUploadShape(it.shapeInfo) }
        }

    fun getMaterials(
        accessCheck: AccessCheck,
        technology: String,
    ): Flux<MasterDataWithProvenance> {
        val masterDataTypes = entityManager.materialTypesForTechnology(technology)
        // concatMap instead of flatMap: performance shouldn't be an issue since
        // the amount of types per technology is very limited, so let's guarantee order.
        return masterDataTypes.toFlux().concatMap {
            masterDataService.findByTypeAndTechAndKeyIncludingProvenance(
                accessCheck,
                it,
                tech = technology,
                key = null,
            )
        }
    }

    fun getLocations(
        accessCheck: AccessCheck,
        pattern: String?,
        caseSensitive: Boolean,
    ): Flux<MasterDataWithProvenance> {
        val result =
            masterDataService.findByTypeAndTechAndKeyIncludingProvenance(
                accessCheck,
                type = MasterDataType.LOCATION,
                tech = null,
                key = null,
            )
        return if (pattern == null) {
            result
        } else {
            val wildcard = PublicApiWildcard(pattern, caseSensitive)
            result.filter { it.res.displayDesignation == null || wildcard.matches(it.res.displayDesignation!!) }
        }
    }

    // should dynamically be deduced from WizardWamLikeStepInterface, but "FTI_WAM" does not implement that?
    private val wizardStepToMaterialFieldName =
        listOf(
            "WAM" to "materialName",
            "COMP" to "materialTemplate",
            "FTI_WAM" to "materialName",
        )

    fun deduceMaterialFieldNameFromWizardSteps(
        accessCheck: AccessCheck,
        technology: String,
    ): String? {
        val cardNames =
            getActiveTechnologies(accessCheck)
                .single { it.path == technology }
                .cardNames
                .toSet()

        val fieldNames =
            wizardStepToMaterialFieldName.mapNotNull { (card, fieldName) ->
                if (cardNames.contains(card)) {
                    fieldName
                } else {
                    null
                }
            }
        return when (fieldNames.size) {
            0 -> null
            1 -> fieldNames.single()
            else -> error("technology should not have more than one card to input the material")
        }
    }

    // Very unhappy about this.
    // Think about a proper concept to communicate those input field names
    // next time we introduce a technology that uses several materials
    fun getMaterialInputFieldName(
        accessCheck: AccessCheck,
        technology: String,
        masterDataMaterial: MasterDataWithProvenance,
    ): String {
        val res =
            when (technology) {
                Model.PBOX.path -> {
                    val paperCategory = masterDataMaterial.res.data["paperCategory"]
                    requireNotNull(paperCategory)
                    when (paperCategory.res.toString()) {
                        "INNER_LINER" -> "materialKeyInnerLiner"
                        "OUTER_LINER" -> "materialKeyOuterLiner"
                        "CORRUGATED_FLUTE" -> "materialKeyCorrugatedFlute"
                        else -> error("unsupported paper category: $paperCategory")
                    }
                }
                else -> {
                    val inputFieldName = deduceMaterialFieldNameFromWizardSteps(accessCheck, technology)
                    requireNotNull(inputFieldName) {
                        "Material '${masterDataMaterial.res.displayDesignation}' should be matched to a field name"
                    }
                }
            }
        return res
    }

    companion object {
        fun isUserUploadShape(info: ShapesData.ShapeInfo): Boolean = info.shapeId in listOf("BART_S1", "CUBE_S1")
    }
}
