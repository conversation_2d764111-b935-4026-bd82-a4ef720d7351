package com.nu.bom.core.service.bomnode

import com.nu.bom.core.exception.readable.SnapshotNotFoundException
import com.nu.bom.core.model.AccountId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.SnapshotId
import com.nu.bom.core.model.manufacturing.BomNodeConverter
import com.nu.bom.core.model.manufacturing.persistentModel.PersistedBomNode
import com.nu.bom.core.model.manufacturing.persistentModel.QPersistedBomNode
import com.nu.bom.core.model.toProjectId
import com.nu.bom.core.service.bomrads.BomradsProjectService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.nu.bom.core.utils.asString
import com.nu.bom.core.utils.exceptionOnEmpty
import org.slf4j.LoggerFactory
import org.springframework.data.mongodb.core.ReactiveMongoTemplate
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.isEqualTo
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

@Service
class SnapshotLoaderService(
    private val reactiveMongoTemplate: ReactiveMongoTemplate,
    private val bomradsProjectService: BomradsProjectService,
    private val bomNodeConverter: BomNodeConverter,
) {
    companion object {
        private val logger = LoggerFactory.getLogger(SnapshotLoaderService::class.java)!!
    }

    /**
     * Load snapshots, but do not set the originalSource and originalRootSource to point to the original dto.
     */
    fun loadSnapshots(
        accessCheck: AccessCheck,
        snapshotIds: Collection<SnapshotId>,
    ): Mono<Map<SnapshotId, BomNodeSnapshot>> =
        if (snapshotIds.isEmpty()) {
            Mono.just(emptyMap())
        } else {
            val start = System.currentTimeMillis()
            loadSnapshotsList(accessCheck, snapshotIds)
                .collectList()
                .map { snapshots ->
                    val time = System.currentTimeMillis() - start
                    logger.info("loaded ${snapshotIds.size} (found ${snapshots.size}) snapshots in $time ms")
                    snapshots.associateBy { it.id() }
                }
        }

    /**
     * Load snapshots, but do not set the originalSource and originalRootSource to point to the original dto.
     */
    @TsetSuppress("tset:reactive:flux-flatmapsequential")
    fun loadSnapshotsList(
        accessCheck: AccessCheck,
        snapshotIds: Collection<SnapshotId>,
    ): Flux<BomNodeSnapshot> =
        fetchMongoSnapshotsAndCheckAccess(
            accessCheck.toAccountId(),
            snapshotIds,
            accessCheck.accountName,
        ).flatMapSequential {
            bomNodeConverter.convertToDomainObject(it)
        }

    /**
     * Load snapshots, but do not set the originalSource and originalRootSource to point to the original dto.
     */
    @TsetSuppress("tset:reactive:flux-flatmapsequential")
    fun loadAdminSnapshots(
        accountId: AccountId,
        accountName: String,
        snapshotIds: Collection<SnapshotId>,
        environmentName: String? = null,
    ): Mono<Map<SnapshotId, BomNodeSnapshot>> =
        fetchMongoSnapshotsAndCheckAccess(accountId, snapshotIds, accountName, environmentName)
            .flatMapSequential {
                bomNodeConverter.convertToDomainObject(it)
            }.collectList()
            .map { snapshots ->
                snapshots.associateBy { it.id() }
            }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun fetchMongoSnapshotsAndCheckAccess(
        accountId: AccountId,
        snapshotIds: Collection<SnapshotId>,
        accountName: String,
        environmentName: String? = null,
    ) = reactiveMongoTemplate
        .find(
            Query.query(createCriteria(accountId, snapshotIds)),
            PersistedBomNode::class.java,
            BomNodeSnapshot.COLLECTION_NAME,
        ).flatMap { ensureSnapshotAccess(accountId, accountName, it, environmentName) }

    private fun createCriteria(
        accountId: AccountId,
        snapshotIds: Collection<SnapshotId>,
    ): Criteria {
        val q = QPersistedBomNode.persistedBomNode
        return Criteria.where(q._id.asString()).`in`(snapshotIds).andOperator(
            Criteria().orOperator(
                Criteria
                    .where(q.accountId.asString())
                    .isEqualTo(accountId),
                Criteria
                    .where(q.accountId.asString())
                    .exists(false),
            ),
        )
    }

    // remove this when COST-45381 (migrate snapshots w/o accountId) is implemented
    // currently for legacy snapshots we only have projectId and for latest snapshots we only have accountId, so we need a fallback check
    private fun ensureSnapshotAccess(
        accountId: AccountId,
        accountName: String,
        bomNodeSnapshot: PersistedBomNode,
        environmentName: String? = null,
    ): Mono<PersistedBomNode> =
        if (bomNodeSnapshot.accountId != null) {
            checkSnapshotAccountAccessibility(accountId, bomNodeSnapshot)
                .thenReturn(bomNodeSnapshot)
        } else if (bomNodeSnapshot.hasProjectId() && bomNodeSnapshot.projectId != null) {
            checkBomradsProjectAccessibility(
                accountName,
                bomNodeSnapshot.projectId,
                bomNodeSnapshot.getId(),
                environmentName,
            ).thenReturn(bomNodeSnapshot)
        } else {
            logger.error(
                "Snapshot with id={} is missing both accountId and projectId.",
                bomNodeSnapshot.getId().toHexString(),
            )
            Mono.error(SnapshotNotFoundException(bomNodeSnapshot.getId().toHexString()))
        }

    private fun checkSnapshotAccountAccessibility(
        accountId: AccountId,
        bomNodeSnapshot: PersistedBomNode,
    ): Mono<Any> =
        if (bomNodeSnapshot.accountId == accountId) {
            // passed simple accountId check
            Mono.empty()
        } else {
            logger.error(
                "Snapshot with id={} has unexpected accountId. Expected={}, actual={}.",
                bomNodeSnapshot.getId().toHexString(),
                accountId.toHexString(),
                bomNodeSnapshot.accountId!!.toHexString(),
            )
            Mono.error(SnapshotNotFoundException(bomNodeSnapshot.getId().toHexString()))
        }

    private fun checkBomradsProjectAccessibility(
        accountName: String,
        projectId: ProjectId,
        snapshotId: SnapshotId,
        environmentName: String? = null,
    ): Mono<Unit> {
        // accountId is missing, so we need to check if the snapshot's project is accessible from current account
        return bomradsProjectService
            .projectForAdmin(
                accountName,
                projectId.toProjectId(),
                includePath = false,
                includeDeleted = true,
                environmentName,
            ).exceptionOnEmpty {
                logger.error(
                    "Snapshot with id={} and project={} could not be found on account={}.",
                    snapshotId.toHexString(),
                    projectId.toHexString(),
                    accountName,
                )
                SnapshotNotFoundException(snapshotId.toHexString())
            }.flatMap { projectDTO ->
                val projectAccountName = projectDTO.account?.key
                if (projectAccountName == accountName) {
                    // project is found and is on the same account
                    Mono.empty()
                } else {
                    logger.error(
                        "Snapshot with id={} and project={} and account={} cannot be accessed from current account={}.",
                        snapshotId.toHexString(),
                        projectId.toHexString(),
                        projectAccountName,
                        accountName,
                    )
                    Mono.error(
                        SnapshotNotFoundException(snapshotId.toHexString()),
                    )
                }
            }
    }
}
