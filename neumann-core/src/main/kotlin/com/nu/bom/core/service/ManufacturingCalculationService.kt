package com.nu.bom.core.service

import com.google.common.annotations.VisibleForTesting
import com.nu.bom.core.Trace
import com.nu.bom.core.api.CO2_PER_PART
import com.nu.bom.core.api.COST_PER_PART
import com.nu.bom.core.api.dtos.BomNodeDto
import com.nu.bom.core.api.dtos.BomNodeDtoConversionService
import com.nu.bom.core.api.dtos.CreateManufacturingResult
import com.nu.bom.core.api.dtos.FieldConversionService
import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.api.dtos.bomradsToBranchDTO
import com.nu.bom.core.controller.CalculationResultDto
import com.nu.bom.core.controller.CalculationResultWithSnapshot
import com.nu.bom.core.controller.InputParameter
import com.nu.bom.core.controller.ManufacturingParameters
import com.nu.bom.core.exception.readable.AttachmentNotFoundException
import com.nu.bom.core.exception.readable.ManufacturingEntityNotFoundException
import com.nu.bom.core.manufacturing.entities.Attachment
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.factories.FieldFactoryService
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.service.CalculationRequestBuilder
import com.nu.bom.core.manufacturing.service.CalculationService
import com.nu.bom.core.manufacturing.service.FieldIdKey
import com.nu.bom.core.model.BomNode
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.FieldValueChange
import com.nu.bom.core.model.FieldValueChangeWithCurrency
import com.nu.bom.core.model.MainImageChange
import com.nu.bom.core.model.MergeSourceType
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.RecalculationTrigger
import com.nu.bom.core.model.RootManufacturingChange
import com.nu.bom.core.model.TriggerAction
import com.nu.bom.core.model.toMongoBranchId
import com.nu.bom.core.service.bomrads.BomradsMergeService
import com.nu.bom.core.service.bomrads.DirectParentAndAllChildren
import com.nu.bom.core.service.bomrads.DirectParents
import com.nu.bom.core.service.change.ChangedEntities
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.applyIfExists
import com.nu.bom.core.utils.withCache
import com.tset.core.api.dto.CostParameterApi.Companion.toBigDecimal
import com.tset.core.module.bom.calculation.CalculationUpdatedNodeInput
import com.tset.core.module.bom.exchangerates.ExchangeRateMap
import com.tset.core.service.domain.Currency
import org.bson.types.ObjectId
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono
import com.nu.bomrads.id.BranchId as BomradBranchId

/**
 * Main entry point for the calculation engine, this handles the loading of the bomnodes, and calling the calculation and saving the results.
 *
 * These transformAndLoad/etc method all work the same way:
 * - you should give them the bomNodeId which you want to change
 * - and the callback will receive the whole tree, starting from the root calculation context
 */
@Trace
@Service
class ManufacturingCalculationService(
    private val bomNodeDtoConversionService: BomNodeDtoConversionService,
    private val calculationService: CalculationService,
    private val bomNodeConversionService: BomNodeConversionService,
    private val bomTreeService: BomTreeService,
    private val fieldFactoryService: FieldFactoryService,
    private val bomradsMergeService: BomradsMergeService,
    private val bomNodeBranchStateService: BomNodeBranchStateService,
    private val manufacturingRecalculationIssueService: ManufacturingRecalculationIssueService,
) {
    /**
     * The Context  is used to specify from what bomnodes, do we want to extract the fields.
     * which is a sort of an optimization thing for lazy-child loading.
     * If Context.LIMITED  is specified, that the engine tries to calculate only the requested bomNode,
     * and it's parents to the root.
     * The rest of the bomnodes are only extracted, when it is determined, that a field, marked
     * with [ChildLoadingTrigger] has changed
     */
    enum class Context {
        ALL_CHILD,
        LIMITED,
    }

    data class Request(
        val accessCheck: AccessCheck,
        val bomNodeId: BomNodeId,
        val context: Context,
        val loadingMode: com.nu.bom.core.service.bomrads.LoadingMode = DirectParents(bomNodeId),
        // List of fields to be updated. When set - list of those fields will be updated.
        // When not set - transformManufacturing will be called to set the fields.
        val parameters: List<InputParameter> = emptyList(),
        val extraBomNodeId: BomNodeId? = null,
        val forceRecalculate: Boolean = false,
        /**
         * Callback to specify custom function which transform the root manufacturing.
         * This is mostly needed, because @see BomNodeSnapshot.manufacturing is immutable, so it is impossible to alter it later.
         * If not specified, the original BaseManufacturing stay the same.
         */
        val transformRoot: ((BaseManufacturing, BomNodeSnapshot) -> Mono<BaseManufacturing>)? = null,
        val transformManufacturing: (BaseManufacturing, BomNodeSnapshot, ChangedEntities) -> Mono<BaseManufacturing> = nopTransformation,
        val customInputs: ((BaseManufacturing, BomNodeSnapshot) -> Map<FieldIdKey, FieldResultStar>?)? = null,
        /**
         * customCopy is used to create the new snapshots, which later saved into the database.
         * If not specified, BomNodeSnapshot.newSnapshot(...) will be used.
         */
        val customCopy: BomNodeSnapshot.CustomCopy? = null,
        /** Marks entity dirty instead of lazy child loading
         * (this option is respected even if [forceRecalculate] is true)
         */
        val dirtyChildLoading: Boolean = false,
        val onlyNewFieldsShouldBeRecalculated: Boolean = false,
        val throwIfKPIIsBroken: Boolean = false,
        val unsafeMutateInPlace: Boolean = false,
        val projectId: ProjectId? = null,
    ) {
        fun getInputParameters(
            rootManufacturing: BaseManufacturing,
            rootSnapshot: BomNodeSnapshot,
        ): Map<FieldIdKey, FieldResultStar>? {
            val ci = customInputs
            return if (ci != null) {
                ci(rootManufacturing, rootSnapshot)
            } else {
                null
            }
        }
    }

    data class RequestWithBranchAndTrigger(
        val req: Request,
        val triggerAction: TriggerAction,
        val branchId: BranchId?,
    )

    data class MinimalMergeResult(
        val result: BomNodeDto,
        val branch: BranchId?,
    )

    fun setField(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
        branchId: BranchId?,
        field: InputParameter,
        dirtyChildLoading: Boolean = false,
    ): Mono<CalculationResultWithSnapshot> {
        val triggerAction =
            if (field.type == "Money" || field.type == "PriceComponents") {
                FieldValueChangeWithCurrency(
                    bomNodeId = bomNodeId,
                    fieldName = field.name,
                    entityId = ObjectId(field.entityId),
                    fieldValue = field.value,
                    currency = field.currency ?: Currency.EUR,
                )
            } else {
                FieldValueChange(
                    bomNodeId = bomNodeId,
                    fieldName = field.name,
                    entityId = ObjectId(field.entityId),
                    fieldValue = field.value,
                )
            }
        val req =
            RequestWithBranchAndTrigger(
                req =
                    Request(
                        accessCheck = accessCheck,
                        bomNodeId = bomNodeId,
                        parameters = listOf(field),
                        context = Context.LIMITED,
                        dirtyChildLoading = dirtyChildLoading,
                    ),
                branchId = branchId,
                triggerAction = triggerAction,
            )
        return loadTransformAndCalculate(req)
    }

    fun setMainImage(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
        branchId: BranchId?,
        inputField: InputParameter,
    ): Mono<CalculationResultWithSnapshot> {
        val triggerAction = MainImageChange(bomNodeId, null, null)
        return transformAndCalculate(
            accessCheck = accessCheck,
            bomNodeId = bomNodeId,
            branchId = branchId,
            triggerAction = triggerAction,
        ) { baseManufacturing, _, _ ->
            val attachments = baseManufacturing.getAttachments()
            attachments.find { it.entityId == inputField.entityId }?.let { attachment ->
                triggerAction.newValue = (attachment.getFieldResult("name") as Text?)?.res
                if (inputField.value == true) {
                    findOldMain(attachments)?.let { oldAttachment ->
                        oldAttachment.replaceFieldResult(Attachment::isMainImage.name) {
                            ((it as Bool?) ?: Bool(true)).withRes(false)
                        }
                        triggerAction.oldValue = (oldAttachment.getFieldResult("name") as Text?)?.res
                    }
                }
                attachment.replaceFieldResult(Attachment::isMainImage.name) {
                    ((it as Bool?) ?: Bool(true)).withRes(inputField.value as Boolean)
                }
                baseManufacturing.toMono()
            } ?: Mono.error(AttachmentNotFoundException(inputField.entityId))
        }
    }

    private fun findOldMain(attachments: List<ManufacturingEntity>): ManufacturingEntity? =
        attachments.find {
            (it.getFieldResult(Attachment::isMainImage.name) as Bool?)?.res == true
        }

    fun recalculate(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
        branchId: BranchId?,
        forceRecalculate: Boolean = false,
        onlyNewFieldsShouldBeRecalculated: Boolean = false,
        throwIfKPIIsBroken: Boolean = false,
        unsafeMutateInPlace: Boolean = false,
    ): Mono<CalculationResultWithSnapshot> {
        val trigger = RecalculationTrigger(bomNodeId = bomNodeId)
        val req =
            RequestWithBranchAndTrigger(
                req =
                    Request(
                        accessCheck = accessCheck,
                        bomNodeId = bomNodeId,
                        context = Context.LIMITED,
                        forceRecalculate = forceRecalculate,
                        onlyNewFieldsShouldBeRecalculated = onlyNewFieldsShouldBeRecalculated,
                        customCopy = { bomNodeSnapshot ->
                            if (unsafeMutateInPlace) bomNodeSnapshot.unsafeMarkAsSafeToPersist() else null
                        },
                        unsafeMutateInPlace = unsafeMutateInPlace,
                        throwIfKPIIsBroken = throwIfKPIIsBroken,
                        transformManufacturing = { manufacturing, newSnapshot, _ ->
                            if (newSnapshot.kpi.costPerPart.broken == true || newSnapshot.kpi.cO2PerPart.broken == true) {
                                manufacturingRecalculationIssueService
                                    .createIssueForBrokenRecalculation(
                                        accessCheck,
                                        manufacturing,
                                        newSnapshot.kpi,
                                        newSnapshot.projectId(),
                                        newSnapshot.title,
                                    ).onErrorResume {
                                        // In case there was an error creating the issue, continue as normal
                                        // with the recalculation.
                                        Mono.just(manufacturing)
                                    }
                            } else {
                                Mono.just(manufacturing)
                            }
                        },
                    ),
                branchId = branchId,
                triggerAction = trigger,
            )
        return loadTransformAndCalculate(req)
    }

    /**
     * Only for tests, the trigger action is a dummy value.
     */
    @VisibleForTesting
    fun updateAndCalculate(
        parameters: ManufacturingParameters,
        accessCheck: AccessCheck,
        branchId: BomradBranchId?,
    ): Mono<CalculationResultWithSnapshot> =
        loadTransformAndCalculate(
            RequestWithBranchAndTrigger(
                req =
                    Request(
                        accessCheck = accessCheck,
                        context = Context.LIMITED,
                        bomNodeId = parameters.bomNodeId,
                        parameters = parameters.parameters,
                        forceRecalculate = parameters.forceRecalculate,
                    ),
                branchId = branchId?.toMongoBranchId(),
                triggerAction =
                    TriggerAction(
                        bomNodeId = parameters.bomNodeId,
                        entityId = null,
                    ),
            ),
        )

    fun updateRootAndCalculate(
        accessCheck: AccessCheck,
        input: CalculationUpdatedNodeInput,
        wizardManufacturing: BaseManufacturing? = null,
        findManufacturingToChange: (BomNodeSnapshot, BaseManufacturing) -> BaseManufacturing? = { _, root -> root },
    ): Mono<CalculationResultWithSnapshot> =
        loadTransformAndCalculate(
            RequestWithBranchAndTrigger(
                req =
                    Request(
                        accessCheck = accessCheck,
                        bomNodeId = input.bomNodeId,
                        context = Context.ALL_CHILD,
                        loadingMode = DirectParentAndAllChildren(input.bomNodeId),
                        customCopy = { snapshot ->
                            // if the snapshot match
                            if (snapshot.bomNodeId == input.bomNodeId && wizardManufacturing == null) {
                                val baseManufacturing = (snapshot.manufacturing as BaseManufacturing)
                                baseManufacturing.title = input.title
                                snapshot.newSnapshot(title = input.title)
                            } else {
                                null
                            }
                        },
                        customInputs =
                            if (wizardManufacturing == null) {
                                createCustomInputs(
                                    input.fields,
                                    findManufacturingToChange,
                                )
                            } else {
                                { _, _ -> emptyMap() }
                            },
                        transformManufacturing =
                            if (wizardManufacturing == null) {
                                { manufacturing, rootSnapshot, _ ->
                                    findManufacturingToChange(rootSnapshot, manufacturing)
                                        ?: throw ManufacturingEntityNotFoundException(
                                            null,
                                            rootSnapshot.manufacturing?.bomNodeId?.toHexString(),
                                        )

                                    fieldFactoryService
                                        .toFieldResultMap(
                                            input.fields,
                                            input.displayCurrency,
                                        ).forEach {
                                            manufacturing.replaceOrAddInitialFieldResult(it.key) { _ ->
                                                it.value
                                            }
                                        }
                                    Mono.just(manufacturing)
                                }
                            } else {
                                { manufacturing, _, _ -> Mono.just(manufacturing) }
                            },
                        transformRoot = { manufacturing, _ -> Mono.just(wizardManufacturing ?: manufacturing) },
                    ),
                branchId = input.branchId,
                triggerAction =
                    RootManufacturingChange(
                        input.bomNodeId,
                        input.selectedType.name,
                        input.fields.map { RootManufacturingChange.FieldChange(it.name, it.value) },
                    ),
            ),
        )

    fun createCustomInputs(
        fields: List<FieldParameter>,
        findManufacturingToChange: (BomNodeSnapshot, BaseManufacturing) -> BaseManufacturing? = { _, root -> root },
    ): (BaseManufacturing, BomNodeSnapshot) -> Map<FieldIdKey, FieldResultStar> =
        { manufacturing, rootSnapshot ->
            val manufacturingToChange =
                findManufacturingToChange(rootSnapshot, manufacturing)
                    ?: throw ManufacturingEntityNotFoundException(
                        null,
                        rootSnapshot.manufacturing?.bomNodeId?.toHexString(),
                    )

            // Convert all the inputs to Map<FieldIdKey,FieldResultStar> with setting the entityId to the root entity id.
            val fieldValues =
                fieldFactoryService.toFieldResultMap(
                    fields,
                    Currency.EUR,
                )
            fieldValues.mapKeys { entry ->
                FieldIdKey(name = entry.key, entityId = manufacturingToChange.entityId)
            }
        }

    fun transformAndCalculate(
        accessCheck: AccessCheck,
        bomNodeId: BomNodeId,
        branchId: BranchId?,
        triggerAction: TriggerAction,
        context: Context = Context.LIMITED,
        forceRecalculate: Boolean = false,
        dirtyChildLoading: Boolean = false,
        // = org.bson.types.ObjectId?
        extraBomNodeId: BomNodeId? = null,
        loadingMode: com.nu.bom.core.service.bomrads.LoadingMode = DirectParents(bomNodeId),
        customInputs: ((BaseManufacturing, BomNodeSnapshot) -> Map<FieldIdKey, FieldResultStar>?)? = null,
        transformRoot: ((BaseManufacturing, BomNodeSnapshot) -> Mono<BaseManufacturing>)? = null,
        transformManufacturing: (BaseManufacturing, BomNodeSnapshot, ChangedEntities) -> Mono<BaseManufacturing>,
    ): Mono<CalculationResultWithSnapshot> =
        loadTransformAndCalculate(
            RequestWithBranchAndTrigger(
                req =
                    Request(
                        accessCheck = accessCheck,
                        bomNodeId = bomNodeId,
                        context = context,
                        loadingMode = loadingMode,
                        customInputs = customInputs,
                        extraBomNodeId = extraBomNodeId,
                        transformManufacturing = transformManufacturing,
                        transformRoot = transformRoot,
                        forceRecalculate = forceRecalculate,
                        dirtyChildLoading = dirtyChildLoading,
                    ),
                triggerAction = triggerAction,
                branchId = branchId,
            ),
        )

    fun loadTransformAndCalculate(request: RequestWithBranchAndTrigger): Mono<CalculationResultWithSnapshot> {
        val req = request.req

        return bomTreeService.getBomradsBomTreeForUpdate(
            accessCheck = req.accessCheck,
            projectId = req.projectId,
            nodeId = req.bomNodeId,
            loadingMode = req.loadingMode,
            branchId = request.branchId,
            triggerAction = request.triggerAction,
            unsafeMutateInPlace = req.unsafeMutateInPlace,
            updater = { ctx, rootSnapshot ->
                transformLoadedAndCalculate(req, request.triggerAction, rootSnapshot, ctx)
            },
            bomradsDataTransformer = { branchViewDTO, calculationResultWithSnapshot ->
                calculationResultWithSnapshot.copy(
                    result =
                        calculationResultWithSnapshot.result.copy(
                            bomNode =
                                calculationResultWithSnapshot.result.bomNode.copy(
                                    branch = bomradsToBranchDTO(branchViewDTO),
                                ),
                        ),
                )
            },
        )
    }

    fun mergeAndRemoveOpenMerges(
        accessCheck: AccessCheck,
        branchId: BranchId,
        mergeSource: MergeSourceType,
        bomNodeId: BomNodeId,
    ): Mono<MinimalMergeResult> =
        bomradsMergeService
            .merge(
                accessCheck,
                branchId,
                mergeSource,
                bomNodeId,
            ).flatMap { result ->
                bomNodeBranchStateService
                    .removeOpenMerges(
                        bomNodeId,
                        branchId,
                        listOf(mergeSource),
                    ).thenReturn(result)
            }

    fun recalculateValueOfCcyFieldToEur(
        fieldType: String,
        fieldValue: Any,
        fieldCurrency: Currency,
        exchangeRatesMap: ExchangeRateMap,
    ): Any =
        when (fieldType) {
            "Money" -> exchangeRatesMap.ccyToEur(toBigDecimal(fieldValue), fieldCurrency).toString()
            "PriceComponents" ->
                (fieldValue as List<Map<String, Any>>).map { component ->
                    val price = component["price"]
                    val updatedPrice =
                        price.applyIfExists { it ->
                            exchangeRatesMap.ccyToEur(toBigDecimal(it), fieldCurrency) ?: it
                        }
                    component.plus("price" to updatedPrice)
                }

            else -> fieldValue
        }

    fun transformLoadedAndCalculate(
        req: Request,
        triggerAction: TriggerAction,
        rootSnapshot: BomNodeSnapshot,
        ctx: ChangedEntities,
    ): Mono<CalculationResultWithSnapshot> {
        val transformRoot = req.transformRoot
        if (transformRoot != null) {
            val root = rootSnapshot.getBaseManufacturing()
            if (root != null) {
                return transformRoot(root, rootSnapshot).flatMap { newRootManufacturing ->
                    val snapshotToWrite =
                        rootSnapshot.newSnapshotRecursively(
                            newRootManufacturing = newRootManufacturing,
                            title = newRootManufacturing.title,
                            customCopy = req.customCopy,
                            ignoreUnfetched = true,
                        )
                    transformLoadedAndCalculateWithNewSnapshot(
                        req,
                        triggerAction,
                        rootSnapshot,
                        ctx,
                        snapshotToWrite,
                    )
                }
            }
        }
        val snapshotToWrite =
            rootSnapshot.newSnapshotRecursively(customCopy = req.customCopy, ignoreUnfetched = true)
        return transformLoadedAndCalculateWithNewSnapshot(
            req,
            triggerAction,
            rootSnapshot,
            ctx,
            snapshotToWrite,
        )
    }

    private fun transformLoadedAndCalculateWithNewSnapshot(
        req: Request,
        triggerAction: TriggerAction,
        rootSnapshot: BomNodeSnapshot,
        ctx: ChangedEntities,
        snapshotToWrite: BomNodeSnapshot,
    ): Mono<CalculationResultWithSnapshot> {
        val extractionBoundaries = collectRelevantNodes(req, snapshotToWrite)

        ctx.addSnapshotsAndNodes(snapshotToWrite.collectChildren(ignoreUnfetched = true))

        val baseManufacturing =
            bomNodeConversionService.bomNodeToManufacturingCalculationTree(
                snapshotToWrite,
                extractionBoundaries,
            ) as BaseManufacturing
        val transformManufacturing = req.transformManufacturing
        return transformManufacturing(baseManufacturing, snapshotToWrite, ctx).flatMap { modified ->
            if (modified != baseManufacturing && modified._id != baseManufacturing._id) {
                throw IllegalArgumentException(
                    "Different ManufacturingEntity returned from $transformManufacturing " +
                        "- you should use the transformRoot callback to change the root entity!",
                )
            }
            val isBrokenBeforeCalculation =
                snapshotToWrite.kpi.costPerPart.broken == true ||
                    snapshotToWrite.kpi.cO2PerPart.broken == true ||
                    modified.getFieldResult(COST_PER_PART)?.res == null ||
                    modified.getFieldResult(CO2_PER_PART)?.res == null

            calculationService
                .calculate(
                    CalculationRequestBuilder(
                        accessCheck = req.accessCheck,
                        changedEntities = ctx,
                        entity = modified,
                        inputs =
                            req.getInputParameters(
                                rootManufacturing = baseManufacturing,
                                rootSnapshot = rootSnapshot,
                            ),
                        parameters = req.parameters,
                        year = modified.snapshot?.year,
                        contextBoundaries = extractionBoundaries,
                        forceRecalculate = req.forceRecalculate,
                        dirtyChildLoading = req.dirtyChildLoading,
                        onlyNewFieldsShouldBeRecalculated = req.onlyNewFieldsShouldBeRecalculated,
                    ),
                ).flatMap { calculationResults ->
                    /**
                     * We must do this after the calculation so that the costPerPart and cO2PerPart of the
                     * manufacturing entity has already been calculated. We then update the KPIs again
                     * to determine whether the costPerPart and cO2PerPart are (still) broken after this calculation.
                     * See [ManufacturingRecalculationIssueService.createIssueForBrokenRecalculation]
                     */
                    snapshotToWrite.updateKpi()
                    if (snapshotToWrite.kpi.costPerPart.broken != true && snapshotToWrite.kpi.cO2PerPart.broken != true) {
                        manufacturingRecalculationIssueService
                            .removeIssueField(req.accessCheck, modified, triggerAction)
                            .onErrorResume {
                                logger.error(it.message)
                                Mono.empty()
                            }.thenReturn(calculationResults)
                    } else {
                        if (req.throwIfKPIIsBroken && !isBrokenBeforeCalculation) {
                            Mono.error(IllegalStateException("KPI is broken : $snapshotToWrite.kpi!"))
                        } else {
                            Mono.just(calculationResults)
                        }
                    }
                }.flatMap { calculationResult ->
                    convertResponse(
                        accessCheck = req.accessCheck,
                        manufacturing = modified,
                        res = calculationResult,
                        requestedNodeId = req.bomNodeId,
                        ctx = ctx,
                        projectId = rootSnapshot.projectId(),
                    )
                }.map {
                    CalculationResultWithSnapshot(
                        result = it,
                        newRootSnapshot = snapshotToWrite,
                    )
                }
        }
    }

    private fun collectRelevantNodes(
        req: Request,
        node: BomNodeSnapshot,
    ): Set<BomNodeId> {
        val collected =
            when (req.context) {
                Context.LIMITED -> collectRelevantNodesRecursively(node, req.bomNodeId)
                Context.ALL_CHILD -> node.visitChildren(true) { it.bomNodeId }.toSet()
            }
        return if (req.extraBomNodeId != null) {
            collected + req.extraBomNodeId
        } else {
            collected
        }
    }

    private fun collectRelevantNodesRecursively(
        node: BomNodeSnapshot,
        bomNodeId: BomNodeId,
    ): Set<BomNodeId> {
        return if (node.bomNodeId() == bomNodeId) {
            setOf(node.bomNodeId())
        } else {
            for (child in node.subNodes) {
                if (child.snapshot != null) {
                    val relevantNodes = collectRelevantNodesRecursively(child.snapshot!!, bomNodeId)
                    if (relevantNodes.isNotEmpty()) {
                        return relevantNodes + node.bomNodeId()
                    }
                }
            }
            emptySet()
        }
    }

    fun calculate(
        accessCheck: AccessCheck,
        manufacturing: ManufacturingEntity,
        changedEntities: ChangedEntities? = null,
        parameters: ManufacturingParameters? = null,
        year: Int? = null,
        executeEntityCreation: Boolean = true,
        ignoreFieldErrors: Boolean = false,
        mandatoryFields: List<String> = emptyList(),
        dirtyChildLoading: Boolean = false,
        skipChildLoading: Boolean = false,
        enableCurrencyConversion: Boolean = true,
        defaultExchangeRates: ExchangeRateMap? = null,
    ): Mono<Pair<ManufacturingEntity, CalculationService.CalculationResults>> =
        calculationService
            .calculate(
                requestBuilder =
                    CalculationRequestBuilder(
                        accessCheck = accessCheck,
                        changedEntities = changedEntities,
                        entity = manufacturing,
                        parameters = parameters?.parameters,
                        year = year,
                        executeEntityCreation = executeEntityCreation,
                        ignoreFieldErrors = ignoreFieldErrors,
                        mandatoryRootFields = mandatoryFields,
                        dirtyChildLoading = dirtyChildLoading,
                        skipChildLoading = skipChildLoading,
                        enableCurrencyConversion = enableCurrencyConversion,
                        defaultExchangeRates = defaultExchangeRates,
                    ),
            ).map {
                manufacturing to it
            }

    fun convert(
        accessCheck: AccessCheck,
        manufacturing: ManufacturingEntity,
        res: CalculationService.CalculationResults,
        requestedNodeId: BomNodeId,
        nodesToCheckMerge: List<BomNode>?,
        projectId: ProjectId,
    ): Mono<Pair<List<BomNodeSnapshot>, CalculationResultDto>> =
        bomNodeConversionService
            .createConversionResult(
                accessCheck = accessCheck,
                requestedNodeId = requestedNodeId,
                projectId = projectId,
                nodesToCheckMerge = nodesToCheckMerge,
                entity = manufacturing,
            ).flatMap { conversionResult ->
                finishConversion(accessCheck, res, conversionResult)
            }

    fun finishConversion(
        accessCheck: AccessCheck,
        res: CalculationService.CalculationResults,
        conversionResult: BomNodeConversionService.ConversionResult,
    ): Mono<Pair<List<BomNodeSnapshot>, CalculationResultDto>> =
        convertRest(
            accessCheck = accessCheck,
            res = res,
            conversionResult = conversionResult,
        ).withCache()

    private fun convertRest(
        accessCheck: AccessCheck,
        res: CalculationService.CalculationResults,
        conversionResult: BomNodeConversionService.ConversionResult,
    ): Mono<Pair<List<BomNodeSnapshot>, CalculationResultDto>> {
        val resultNode = conversionResult.reqestedNode
        return bomNodeDtoConversionService
            .getWaterfallWithCbd(accessCheck, resultNode)
            .flatMap { waterFallWithCbd ->
                val merges = conversionResult.mergeResult
                bomNodeBranchStateService
                    .setOpenMergesAvailable(
                        conversionResult.requestedBomNodeId,
                        resultNode.branch ?: resultNode.branchEntity?._id ?: ObjectId(),
                        merges.map { it.sourceType },
                    ).flatMap { openMergesAvailable ->
                        bomNodeDtoConversionService.bomNodeToDtoDirect(
                            accessCheck = accessCheck,
                            nodeSnapshot = resultNode,
                            exchangeRateMap = waterFallWithCbd.exchangeRateMap,
                            openMergesAvailable = openMergesAvailable,
                            waterfall = waterFallWithCbd,
                            // FIXME?
                            parentShapeModelManufacturing = resultNode.manufacturing,
                        )
                    }.map { dto ->
                        conversionResult.nodes to calculationResultDto(res, dto, conversionResult.manufacturing)
                    }
            }
    }

    private fun calculationResultDto(
        result: CalculationService.CalculationResults,
        resultNodeDto: BomNodeDto,
        manufacturing: ManufacturingEntity,
    ): CalculationResultDto {
        val (additionalMissingInputs, additionalOpenCalculations) = CalculationService.splitInputCalculation(result.open)

        return CalculationResultDto(
            result = resultNodeDto.manufacturing!!,
            bomNode = resultNodeDto,
            manufacturing = manufacturing,
            missingInputs =
                (result.nullInputs + additionalMissingInputs)
                    .map { it.calcKey },
            openCalculations =
                (result.nullCalculations + additionalOpenCalculations)
                    .map { it.calcKey },
            open = result.open,
        )
    }

    fun convertResponse(
        accessCheck: AccessCheck,
        manufacturing: ManufacturingEntity,
        res: CalculationService.CalculationResults,
        requestedNodeId: BomNodeId,
        ctx: ChangedEntities,
        projectId: ProjectId,
    ): Mono<CalculationResultDto> =
        convert(
            accessCheck,
            manufacturing,
            res,
            requestedNodeId,
            ctx.nodes.values.toList(),
            projectId,
        ).map { (snapshots, calculationResultsDto) ->
            ctx.addSnapshotsAndNodes(snapshots)
            /*
            when a former subtree is being removed from the full tree as part of the transformation (or calculation)
            this subtree is not undergoing conversion anymore which also misses removal of external subtrees
            therefore removed subtrees are saved as a whole in 1 snapshot document -
            to prevent this ensures we remove external subtrees from all snapshots of the changedEntities
             */
            ctx.snapshots.forEach {
                it.value.manufacturing?.let { manu -> bomNodeConversionService.removeExternalSubTrees(manu) }
            }
            calculationResultsDto
        }

    companion object {
        val nopTransformation =
            { manufacturing: BaseManufacturing, _: BomNodeSnapshot, _: ChangedEntities -> Mono.just(manufacturing) }

        val logger: Logger = LoggerFactory.getLogger(ManufacturingCalculationService::class.java)

        fun toCreateManufacturingResult(
            accessCheck: AccessCheck,
            calculationResult: CalculationResultWithSnapshot,
            path: List<String>? = null,
            exchangeRatesMap: ExchangeRateMap,
            fieldConversionService: FieldConversionService,
        ): Mono<CreateManufacturingResult> =
            CreateManufacturingResult.from(
                accessCheck,
                calculationResult.newRootSnapshot,
                logAndExtractResult(calculationResult.result),
                path ?: listOf(calculationResult.result.bomNode.id),
                exchangeRateMap = exchangeRatesMap,
                fieldConversionService = fieldConversionService,
            )

        private fun logAndExtractResult(calculationResult: CalculationResultDto): BomNodeDto {
            if (calculationResult.missingInputs.isNotEmpty()) {
                calculationResult.missingInputs.map { it.name }.distinct().forEach {
                    logger.info("Missing input: $it")
                }
            }
            return calculationResult.bomNode
        }
    }
}
