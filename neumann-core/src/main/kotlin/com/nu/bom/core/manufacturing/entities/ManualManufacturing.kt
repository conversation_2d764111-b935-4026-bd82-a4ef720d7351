package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.StaticDenominatorUnit
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.StaticUnitOverride
import com.nu.bom.core.manufacturing.fieldTypes.ConfigIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Text
import kotlin.reflect.KClass

@EntityType(Entities.MANUFACTURING)
class ManualManufacturing(
    name: String,
) : BaseModelManufacturing(name) {
    override val extends = Manufacturing(name)

    override val model: Model
        get() = Model.MANUAL

    override val modelSteps: List<KClass<out ManufacturingEntity>>
        get() = emptyList()

    @Input
    fun costModuleConfigurationIdentifier(): ConfigIdentifier = ConfigIdentifier(ConfigurationIdentifier.empty())

    // not internalTechnologyModel, need special logic for modularization
    fun configurationTechnology(technologyModel: Text): Text = Text(Model.fromEntity(technologyModel.res)!!.name)

    @Input
    @StaticDenominatorUnit(StaticUnitOverride.YEAR)
    fun callsPerYear(internalCallsPerYear: Num) = internalCallsPerYear
}
