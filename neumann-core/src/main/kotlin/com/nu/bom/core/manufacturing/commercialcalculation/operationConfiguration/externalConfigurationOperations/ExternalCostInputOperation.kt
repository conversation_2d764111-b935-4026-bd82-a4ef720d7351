package com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations

import com.fasterxml.jackson.annotation.JsonTypeName
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.StandardCalculationValue
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.externalConfigurationOperations.staticElements.StaticCostElement

@JsonTypeName("Cost-Input")
data class ExternalCostInputOperation(
    val sourceElement: StaticCostElement,
    override val destinationElementKey: String,
    override val origin: AggregationLevel,
    val standardCalculationValue: StandardCalculationValue? = null,
) : ExternalConfigurationOperation {
    init {
        when (sourceElement) {
            StaticCostElement.PURCHASE_PRICE,
            -> ensureAggregationLevelIs(AggregationLevel.SOLD_MATERIAL)

            StaticCostElement.LABOR_COSTS,
            -> ensureAggregationLevelIs(AggregationLevel.LABOR)

            StaticCostElement.MACHINE_DEPRECIATION_COSTS,
            StaticCostElement.MACHINE_INTEREST_COSTS,
            StaticCostElement.MACHINE_AREA_COSTS,
            StaticCostElement.MACHINE_ENERGY_COSTS,
            StaticCostElement.MACHINE_MAINTENANCE_COSTS,
            StaticCostElement.MACHINE_OPERATION_SUPPLY_COSTS,
            StaticCostElement.ROUGH_MACHINE_COSTS,
            -> ensureAggregationLevelIs(AggregationLevel.MACHINE)

            StaticCostElement.TOOL_ALLOCATION_COSTS,
            StaticCostElement.TOOL_INTEREST_COSTS,
            StaticCostElement.TOOL_MAINTENANCE_COSTS,
            -> ensureAggregationLevelIs(AggregationLevel.TOOL)

            StaticCostElement.ROUGH_PROCESS_COSTS,
            -> ensureAggregationLevelIs(AggregationLevel.ROUGH_PROCESS)

            StaticCostElement.ALLOCATED_COST_PER_QUANTITY_FOR_INVEST,
            StaticCostElement.INTEREST_COST_PER_QUANTITY_FOR_INVEST,
            -> ensureAggregationLevelIs(AggregationLevel.INVEST)
        }
    }
}
