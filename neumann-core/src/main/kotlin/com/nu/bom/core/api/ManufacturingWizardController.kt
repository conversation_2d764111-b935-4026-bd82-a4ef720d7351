package com.nu.bom.core.api

import com.fasterxml.jackson.databind.ObjectMapper
import com.nu.bom.core.api.dtos.CalculationCreationDto
import com.nu.bom.core.api.dtos.CreateManufacturingResult
import com.nu.bom.core.api.dtos.PartDto
import com.nu.bom.core.api.dtos.SubManufacturingDataDto
import com.nu.bom.core.api.dtos.WizardDto
import com.nu.bom.core.api.dtos.WizardStepResponseDto
import com.nu.bom.core.model.Wizard
import com.nu.bom.core.model.toMongoID
import com.nu.bom.core.service.ProjectService
import com.nu.bom.core.service.wizard.CreateManufacturingFromWizardService
import com.nu.bom.core.service.wizard.WizardManager
import com.nu.bom.core.service.wizard.WizardService
import com.nu.bom.core.service.wizard.steps.StepPathMapping
import com.nu.bom.core.service.wizard.steps.WizardStep
import com.nu.bom.core.turn.TurningService
import com.nu.bom.core.turn.model.Point
import com.nu.bom.core.turn.model.Section
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.user.AccessCheckProvider
import com.nu.bom.core.utils.DataValidatorUtils
import com.nu.bom.core.utils.applyIfExists
import com.tset.core.api.calculation.dto.CalculationCreationModalMode
import com.tset.core.api.calculation.dto.CalculationUpdatePayloadDto
import com.tset.core.service.domain.Currency
import org.bson.types.ObjectId
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Mono

@RestController
@RequestMapping("/api/projects/{projectId}/wizards")
class ManufacturingWizardController(
    private val wizardManager: WizardManager,
    private val wizardService: WizardService,
    private val createManufacturingFromWizardService: CreateManufacturingFromWizardService,
    private val accessCheckProvider: AccessCheckProvider,
    private val turningService: TurningService,
    private val projectService: ProjectService,
    private val objectMapper: ObjectMapper,
) {
    @PostMapping("/{wizardId}/{step}")
    fun saveStep(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable wizardId: ObjectId,
        @PathVariable step: String,
        @RequestBody content: String,
        @RequestParam(required = false) currency: Currency?,
        @PathVariable projectId: String,
    ): Mono<WizardDto>? {
        return accessCheckProvider.doAs(jwt) { accessCheck ->
            val stepDto = objectMapper.readValue(content, StepPathMapping.valueOf(step).clazz.java)!!
            wizardManager.saveStep(accessCheck, wizardId, stepDto, currency ?: Currency.EUR)
                .map {
                    toWizardDto(wizard = it, step = step)
                }
        }
    }

    @PostMapping("/{wizardId}/{step}/refresh")
    fun refreshStep(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable wizardId: ObjectId,
        @PathVariable step: String,
        @RequestBody content: String,
    ): Mono<out WizardStepResponseDto<*>> {
        val stepDto = objectMapper.readValue(content, StepPathMapping.valueOf(step).clazz.java)!!

        return accessCheckProvider.doAsOut(jwt) { accessCheck ->

            wizardManager.refreshStep(accessCheck, wizardId, stepDto)
                .flatMap {
                    toStepDto(wizardId, it, step, accessCheck = accessCheck)
                }
        }
    }

    @GetMapping("/{wizardId}/{step}")
    fun requestStep(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable wizardId: ObjectId,
        @PathVariable step: String,
        @PathVariable projectId: String,
        // TODO: empty mono instead of nullable mono
    ): Mono<out WizardStepResponseDto<*>>? {
        return accessCheckProvider.doAsOut(jwt) { accessCheck ->
            wizardService.getWizard(accessCheck, wizardId).flatMap { wizard ->
                wizardManager.getStep(
                    accessCheck,
                    wizardId,
                    StepPathMapping.valueOf(step).clazz,
                    wizard.standardCalculationData.calculationUpdateData?.input?.mode
                        ?: CalculationCreationModalMode.CALCULATION_MODE_NEW,
                ).map {
                    toStepDto(wizard, step, it)
                }
            }
        }
    }

    @PostMapping("/{wizardId}/turning/dimensions")
    fun calculateDimensions(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable wizardId: ObjectId,
        @RequestBody content: List<Point>,
        @PathVariable projectId: String,
    ): Mono<List<Section>> {
        return accessCheckProvider.doAs(jwt) { accessCheck ->
            wizardService.getWizard(accessCheck, wizardId).flatMap { wizard: Wizard ->
                turningService.calculateDimensions(wizard, content, accessCheck)
            }
        }
    }

    @PostMapping("/start")
    fun startWizard(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable projectId: String,
        @RequestBody payloadDto: CalculationUpdatePayloadDto,
        @RequestParam(required = false) step: String?,
    ): Mono<WizardDto> {
        return accessCheckProvider.doAs(jwt) { accessCheck ->
            projectService.getProject(accessCheck, projectId.toMongoID()).flatMap { project ->
                DataValidatorUtils.validateQuantityField(payloadDto.data.fields)
                // TODO kw check how this part id was actually used
                // calculationInputPartConverter.convertPartFieldsToPartId(
                //     accessCheck,
                //     project.id.toMongoProjectId(),
                //     project.key,
                //     payloadDto.data.fields,
                // ).flatMap { partId ->
                    wizardService.startWizard(
                        accessCheck,
                        projectId.toMongoID(),
                        // TODO remove?
                        payloadDto,
                        step.applyIfExists { StepPathMapping.valueOf(it) },
                    )
                        .map {
                            toWizardDto(wizard = it, step = step)
                        }
                // }
            }
        }
    }

    @PostMapping("")
    fun startWizardLegacy(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable projectId: String,
        @RequestBody creationDto: CalculationCreationDto,
    ): Mono<WizardDto> {
        return accessCheckProvider.doAs(jwt) { accessCheck ->
            wizardService.startWizardLegacy(accessCheck, projectId.toMongoID(), creationDto)
                .map {
                    toWizardDto(wizard = it, step = null)
                }
        }
    }

    @GetMapping("/{wizardId}")
    fun getWizard(
        @AuthenticationPrincipal jwt: Jwt?,
        @PathVariable wizardId: String,
        @PathVariable projectId: String,
    ): Mono<WizardDto> {
        return accessCheckProvider.doAs(jwt) { accessCheck ->
            wizardService.getWizard(accessCheck, ObjectId(wizardId))
                .map {
                    toWizardDto(it, null)
                }
        }
    }

    @PostMapping("/{wizardId}/finish")
    fun finishWizard(
        @PathVariable wizardId: String,
        @RequestParam(required = false, defaultValue = "false") dirtyChildLoading: Boolean,
        @AuthenticationPrincipal jwt: Jwt?,
    ): Mono<CreateManufacturingResult> =

        accessCheckProvider.doAs(jwt) { accessCheck ->
            createManufacturingFromWizardService.createFromWizard(
                wizardId = ObjectId(wizardId),
                accessCheck = accessCheck,
                dirtyChildLoading = dirtyChildLoading,
            )
        }

    private fun toStepDto(
        wizardId: ObjectId,
        wizardStep: WizardStep,
        stepName: String,
        accessCheck: AccessCheck,
    ): Mono<WizardStepResponseDto<*>> {
        return wizardService.getWizard(accessCheck, wizardId).map {
            toStepDto(it, stepName, wizardStep)
        }
    }

    private fun <T : WizardStep> toStepDto(
        it: Wizard,
        stepName: String,
        wizardStep: T,
    ): WizardStepResponseDto<WizardStep> {
        val nextStep = it.getNextStep(stepName)
        return WizardStepResponseDto(
            nextStep = nextStep,
            sectionData = wizardStep,
        )
    }

    private fun toWizardDto(
        wizard: Wizard,
        step: String?,
    ): WizardDto {
        val nextStep = wizard.getNextStep(step)

        // TODO maybe we need to construct a PartDto from the fields in wizard.standardCalculationData.fields here...
        // val partMono =
        //     wizard.standardCalculationData.partId?.let {
        //         partService
        //             .getPart(wizard.standardCalculationData.partId, accessCheck = accessCheck)
        //             .map { part -> PartDto.from(part) }
        //             .map { Maybe(it) }
        //     } ?: Mono.just(Maybe())
        //
        // PartDto(
        //     id = null,
        //     designation = wizard.standardCalculationData.fields.
        // )
        val part = wizard.manufacturingEntity?.let { PartDto.from(it) }
        return WizardDto(
            projectId = wizard.projectId.toHexString(),
            _id = wizard._id!!.toHexString(),
            steps = wizard.steps,
            nextStep = nextStep,
            subManufacturingData =
                wizard.standardCalculationData.bomEntry?.let { bomEntry ->
                    SubManufacturingDataDto(
                        bomNodeId = bomEntry.bomNodeId.toHexString(),
                        parentId = bomEntry.stepId?.toHexString(),
                        parentPartName = wizard.parentPartName ?: "",
                    )
                },
            part = part,
            creationPayloadDto = wizard.standardCalculationData.calculationUpdateData,
        )
    }
}
