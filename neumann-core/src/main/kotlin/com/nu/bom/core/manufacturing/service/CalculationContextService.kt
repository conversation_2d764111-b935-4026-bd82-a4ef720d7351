package com.nu.bom.core.manufacturing.service

import com.nu.bom.core.machining.model.MillingRequest
import com.nu.bom.core.machining.model.MillingResponse
import com.nu.bom.core.machining.model.TurningRequest
import com.nu.bom.core.machining.model.TurningResponse
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.CalculationOperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.rollupconfiguration.RollUpConfiguration
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataCategory
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.extension.classificationcalculator.ClassificationCalculatorRestriction
import com.nu.bom.core.manufacturing.extension.lookups.CO2SteelLookup
import com.nu.bom.core.manufacturing.factories.FieldFactoryService
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.CO2CalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.ClassificationResponse
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.CostCalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.ListOfStrings
import com.nu.bom.core.manufacturing.fieldTypes.MaterialFurnaceType
import com.nu.bom.core.manufacturing.fieldTypes.MaterialSubstances
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TurningStep
import com.nu.bom.core.manufacturing.utils.InjectionUtilsService
import com.nu.bom.core.model.IMasterData
import com.nu.bom.core.model.Lookup
import com.nu.bom.core.model.MasterData
import com.nu.bom.core.model.MasterDataSelector
import com.nu.bom.core.model.TurningProfile
import com.nu.bom.core.model.configurations.ConfigurationValue
import com.nu.bom.core.model.configurations.CurrentMasterdataConfiguration
import com.nu.bom.core.model.configurations.VersionedCostModule
import com.nu.bom.core.service.configurations.ConfigType
import com.nu.bom.core.service.fti.FtiService
import com.nu.bom.core.service.nexar.NexarQueryService
import com.nu.bom.core.service.nuledge.NuLedgeService
import com.nu.bom.core.smf.SmfUnfoldedPartScalingService
import com.nu.bom.core.smf.model.NestorRequest
import com.nu.bom.core.smf.model.NestorResponse
import com.nu.bom.core.smf.model.WsiUnfolderResponse
import com.nu.bom.core.turn.model.InductiveHardeningGroup
import com.nu.bom.core.turn.model.Technology
import com.nu.bom.core.turn.model.TurningOperationCycleTimeRequestData
import com.nu.bom.core.turn.model.TurningOperationResponse
import com.nu.bom.core.turn.model.TurningStepsRequest
import com.nu.bom.core.turn.model.TurningStepsResponse
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.Either
import com.nu.bom.core.utils.ShapesData
import com.nu.bomrads.dto.FileUploadDto
import com.tset.bom.clients.tsetdel.model.TsetDelFileLocation
import org.bson.types.ObjectId
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.math.BigDecimal
import kotlin.reflect.KClass

typealias EntityClassOrName = Either<KClass<out ManufacturingEntity>, String>

fun <X : ManufacturingEntity> KClass<X>.asEntityClass(): EntityClassOrName = Either.first(this)

fun String.asEntityClassName(): EntityClassOrName = Either.second(this)

fun EntityClassOrName.asString(): String = this.mapFirst { it.simpleName!! }.firstValue() ?: this.secondValue()!!

interface InjectableEngineService

interface CalculationContextService : InjectableEngineService {
    data class TurningProfileInputFields<T>(
        val turningProfileId: Text,
        val templateNames: ListOfStrings,
        val materialName: Text,
        val doesHardening: Bool,
        val softTurningMaterialIsHardenedTempered: Bool,
        val systemParameterType: MasterDataType,
        val technology: Technology<T>,
    )

    data class MillingDrillingInputFields(
        val sketchId: ObjectId,
        val materialName: Text,
        val systemParameterType: MasterDataType,
        val productionVolume: QuantityUnit,
        val netWeightPerPart: QuantityUnit,
    )

    fun getMasterData(
        accessCheck: AccessCheck,
        type: MasterDataType,
        key: String,
        year: Int,
        location: String,
    ): Mono<MasterData>

    fun getMasterData(
        accessCheck: AccessCheck,
        category: MasterDataCategory,
        key: String,
        year: Int,
        location: String,
    ): Mono<MasterData>

    fun getShapeInfo(
        accessCheck: AccessCheck,
        technology: String,
        shapeId: String,
        onlyActive: Boolean,
    ): ShapesData.ShapeInfo?

    fun createEntity(
        name: String,
        entityType: Entities,
        clazz: EntityClassOrName? = null,
        args: Map<String, Any> = emptyMap(),
        fields: Map<String, FieldResult<*, *>> = emptyMap(),
        overwrites: Map<String, FieldResult<*, *>> = emptyMap(),
        entityRef: String? = null,
        version: Int,
        entityId: ObjectId? = null,
    ): ManufacturingEntity

    fun createManufacturing(
        name: String,
        clazz: EntityClassOrName,
        args: Map<String, Any?>,
        masterDataSelector: MasterDataSelector?,
        masterDataObjectId: ObjectId?,
        fields: Map<String, FieldResult<*, *>>,
        version: Int,
    ): BaseManufacturing

    fun getLatestLookupTable(
        key: String,
        accessCheck: AccessCheck,
    ): Mono<Map<String, String>>

    fun <T> getLatestLookupTable(
        key: String,
        accessCheck: AccessCheck,
        rowParser: (List<String>) -> T,
    ): Flux<T>

    fun <T> getLookupRows(
        key: String,
        accessCheck: AccessCheck,
        rowParser: (Lookup.Row) -> T,
    ): Flux<T>

    @Deprecated(
        "old version relying on old lookup format (1st column key, 2nd column value) instead of new multicolumn based lookup format",
    )
    fun lookupValue(
        lookupTableKey: String,
        lookupKey: String,
    ): Mono<String?>

    fun predictNum(
        technology: String,
        variable: String,
        inputs: Map<String, Any>,
    ): Mono<BigDecimal>

    fun predictString(
        technology: String,
        variable: String,
        inputs: Map<String, Any>,
    ): Mono<String>

    fun <T> predict(
        technology: String,
        variable: String,
        inputs: Map<String, Any>,
    ): Mono<T>

    fun createEntitiesFromTemplate(
        accessCheck: AccessCheck,
        name: String,
        location: String,
        year: Int,
        additionalArgs: Map<String, Any> = mapOf(),
        version: Int,
    ): Flux<ManufacturingEntity>

    fun <T : ManufacturingEntity> createSystemParameter(
        accessCheck: AccessCheck,
        type: Entities,
        masterData: MasterDataType,
        clazz: KClass<out T>,
        system: String,
        year: Int,
        location: String,
        version: Int,
        entityRef: String? = null,
    ): Mono<T>

    fun createEntityWithMasterdata(
        accessCheck: AccessCheck,
        name: String,
        entityType: Entities,
        clazz: EntityClassOrName,
        masterDataSelector: MasterDataSelector,
        version: Int,
        args: Map<String, Any> = emptyMap(),
        overwrites: Map<String, FieldResult<*, *>> = mapOf(),
        fields: Map<String, FieldResult<*, *>> = mapOf(),
        entityRef: String? = null,
        entityId: ObjectId? = null,
    ): Mono<ManufacturingEntity>

    fun getTurningProfile(
        turningProfileId: Text,
        accessCheck: AccessCheck,
    ): Mono<TurningProfile>

    fun <T> getTurningSequence(
        step: TurningStep,
        turningProfileId: Text,
        input: TurningProfileInputFields<T>,
        accessCheck: AccessCheck,
    ): Mono<Pair<TurningRequest<T>, TurningResponse>>

    fun getTurningOperationCycleTime(request: TurningOperationCycleTimeRequestData): Mono<TurningOperationResponse>

    fun getUnfolderResponse(
        wizardId: String,
        fileContent: String,
    ): Mono<WsiUnfolderResponse>

    fun getNestingResponse(
        wizardId: String,
        request: NestorRequest,
        accessCheck: AccessCheck,
        uploadNestingImage: Boolean,
    ): Mono<Pair<String?, NestorResponse>>

    fun getInductiveHardeningGroups(
        turningProfileId: Text,
        accessCheck: AccessCheck,
    ): Mono<List<InductiveHardeningGroup>>

    fun <T> getTurningCalculationSteps(
        turningProfileId: Text,
        technology: Technology<T>,
        accessCheck: AccessCheck,
    ): Mono<Pair<TurningStepsRequest<T>, TurningStepsResponse>>

    fun getMillingCalculation(
        input: MillingDrillingInputFields,
        accessCheck: AccessCheck,
    ): Mono<Pair<MillingRequest, MillingResponse>>

    fun getClassificationCalculator(
        materialSubstances: MaterialSubstances,
        steel: CO2SteelLookup,
        materialFurnaceType: MaterialFurnaceType?,
    ): Mono<ClassificationResponse>

    fun getClassificationCalculator(
        materialSubstances: MaterialSubstances,
        restriction: List<Pair<String, ClassificationCalculatorRestriction>>,
        materialFurnaceType: MaterialFurnaceType?,
    ): Mono<ClassificationResponse>

    fun nuLedge(): NuLedgeService

    fun octoPartService(): NexarQueryService

    fun fieldFactoryService(): FieldFactoryService

    fun getSmfUnfoldedPartScalingService(): SmfUnfoldedPartScalingService

    fun getTsetDelFileLocation(
        accessCheck: AccessCheck,
        technologyKey: String,
        shapeId: String,
    ): Mono<TsetDelFileLocation>

    fun getFile(
        accessCheck: AccessCheck,
        fileId: String,
    ): Mono<ByteArray>

    fun getFileInfo(
        accessCheck: AccessCheck,
        fileId: String,
    ): Mono<FileUploadDto>

    fun findMachiningTool(
        accessCheck: AccessCheck,
        type: String,
        materialGroup: String?,
        entityClass: String,
        toolDiameter: BigDecimal?,
        selectedTool: String?,
        allowEqual: Boolean,
    ): Mono<List<IMasterData>>

    fun getInjectionUtilsService(): InjectionUtilsService

    fun <T : ConfigurationValue, C : ConfigType<T>> getConfiguration(
        accessCheck: AccessCheck,
        type: C,
        key: ConfigurationKey<C>,
    ): Mono<T>

    fun getCostModuleConfiguration(
        accessCheck: AccessCheck,
        group: String,
        type: String,
        id: ConfigurationIdentifier,
    ): Mono<VersionedCostModule>

    fun getDefaultConfigurationKey(
        accessCheck: AccessCheck,
        type: ConfigType<*>,
    ): Mono<ConfigurationIdentifier>

    fun getDefaultCostModuleConfigurationKey(
        accessCheck: AccessCheck,
        type: Model,
    ): Mono<ConfigurationIdentifier>

    fun getCostCalculationConfiguration(
        accessCheck: AccessCheck,
        costCalculationOperationsConfigurationKey: CostCalculationOperationsConfigurationKey,
        rollUpConfiguration: RollUpConfiguration,
    ): Mono<CalculationOperationConfiguration>

    fun getCO2CalculationConfiguration(
        accessCheck: AccessCheck,
        cO2CalculationOperationsConfigurationKey: CO2CalculationOperationsConfigurationKey,
        rollUpConfiguration: RollUpConfiguration,
    ): Mono<CalculationOperationConfiguration>

    fun getDefaultMasterdataConfiguration(accessCheck: AccessCheck): Mono<CurrentMasterdataConfiguration>

    fun getFtiService(): FtiService

    fun findMaterialDensity(
        accessCheck: AccessCheck,
        key: String,
    ): Mono<Density>

    fun findMaterialDensityOrNull(
        accessCheck: AccessCheck,
        key: String,
    ): Mono<Density>

    fun <T : FieldResultStar> findMaterialField(
        accessCheck: AccessCheck,
        key: String,
        fieldKey: String,
    ): Mono<T>

    fun <T : FieldResultStar> findMaterialFieldOrNull(
        accessCheck: AccessCheck,
        key: String,
        fieldKey: String,
    ): Mono<T>

    fun <T : FieldResultStar> findMaterialFields(
        accessCheck: AccessCheck,
        key: String,
    ): Mono<Map<String, T>>

    fun findMaterialName(
        accessCheck: AccessCheck,
        key: String,
    ): Mono<String>
}
