package com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel

import com.nu.bom.core.api.dtos.ManufacturingDto
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostManufacturedMaterial
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostMaterialUsage
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.ProcurementTypeAndMaterialClass
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.helper.CriteriaHelper
import com.nu.bom.core.manufacturing.entities.BaseEntityFields
import com.nu.bom.core.manufacturing.entities.ExternalBomEntry
import com.nu.bom.core.manufacturing.entities.ManualBaseMaterial
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.FieldTableColumnIdentifier
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.AndCriteria
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityLocatorCriteria
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableFieldDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EqualCriteria
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.NotEqualCriteria
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.options.FieldsOptionsFeDto
import com.nu.bom.core.utils.mapOfNotNull

object MaterialEntityTableCriteria {
    fun readOnlyQuantityAndOverridePurchasePriceCriteria(valueType: ValueType): Map<String, EditableConfig> {
        val isMaterialCriteria = EqualCriteria(ManufacturingDto::type.name, Entities.MATERIAL)
        val overridePurchasePriceColumn =
            CommercialCalculationCostManufacturedMaterial::purchasePrice.name to
                when (valueType) {
                    ValueType.CO2 -> null
                    ValueType.COST -> EntityTableFieldDefinitionFeDto(ManualBaseMaterial::materialBasePrice.name)
                }
        return mapOf(
            "quantityReadOnly" to
                EditableConfig(
                    isMaterialCriteria,
                    mapOfNotNull(
                        CommercialCalculationCostMaterialUsage::purchasedQuantity.name to
                            EntityTableFieldDefinitionFeDto(
                                CommercialCalculationCostMaterialUsage::purchasedQuantity.name,
                                FieldsOptionsFeDto(editable = false),
                            ),
                        overridePurchasePriceColumn,
                    ),
                ),
            "quantityEditable" to
                EditableConfig(
                    EntityLocatorCriteria.inverseCriteriaCondition(
                        isMaterialCriteria,
                    ),
                    emptyMap(),
                ),
        )
    }

    /** for bom entries we mark the designation and the custom procurement type as readonly */
    fun bomEntryCriteria(): Map<String, EditableConfig> {
        val displayDesignationCannotBeChangedCriteria = EqualCriteria(ManufacturingDto::type.name, Entities.BOM_ENTRY)

        return mapOf(
            "isBomEntry" to
                EditableConfig(
                    displayDesignationCannotBeChangedCriteria,
                    mapOf(
                        BaseEntityFields::displayDesignation.name to
                            EntityTableFieldDefinitionFeDto(
                                BaseEntityFields::displayDesignation.name,
                                FieldsOptionsFeDto(editable = false),
                            ),
                        CommercialCalculationCostMaterialUsage::customProcurementType.name to
                            EntityTableFieldDefinitionFeDto(
                                CommercialCalculationCostMaterialUsage::customProcurementType.name,
                                FieldsOptionsFeDto(editable = false),
                            ),
                    ),
                ),
            "isNotBomEntry" to
                EditableConfig(
                    EntityLocatorCriteria.inverseCriteriaCondition(
                        displayDesignationCannotBeChangedCriteria,
                    ),
                    emptyMap(),
                ),
        )
    }

    fun readOnlyParentStepCriteria(): Map<String, EditableConfig> {
        val parentStepCannotBeChangedCriteria =
            CriteriaHelper.or(
                // External steps can not be changed
                CriteriaHelper.and(
                    EqualCriteria(
                        ManufacturingDto::type.name,
                        Entities.BOM_ENTRY,
                    ),
                    EqualCriteria(
                        ManufacturingDto::className.name,
                        ExternalBomEntry::class.java.simpleName,
                    ),
                ),
                // For those entities we always can change the step
                AndCriteria(
                    listOf(
                        Entities.BOM_ENTRY,
                        Entities.MATERIAL,
                        Entities.C_PART,
                        Entities.CONSUMABLE,
                    ).map {
                        NotEqualCriteria(ManufacturingDto::type.name, it)
                    },
                ),
                // Cost module steps cant be edited
                NotEqualCriteria(ManufacturingDto::createdByField.name, null),
            )
        return mapOf(
            "parentStepRefReadOnly" to
                EditableConfig(
                    parentStepCannotBeChangedCriteria,
                    mapOf(
                        BaseEntityFields::parentStepRef.name to
                            EntityTableFieldDefinitionFeDto(
                                BaseEntityFields::parentStepRef.name,
                                FieldsOptionsFeDto(editable = false),
                            ),
                    ),
                ),
            "parentStepRefEditable" to
                EditableConfig(
                    EntityLocatorCriteria.inverseCriteriaCondition(parentStepCannotBeChangedCriteria),
                    emptyMap(),
                ),
        )
    }

    fun criteriaOfProcurementAndMaterialType(
        procurementTypeAndMaterialClasses: List<ProcurementTypeAndMaterialClass>,
    ): EntityLocatorCriteria {
        val criteria =
            procurementTypeAndMaterialClasses.map { procurementTypeAndMaterialClass ->
                val materialClassCriteria =
                    procurementTypeAndMaterialClass.materialClass?.let { mc ->
                        EqualCriteria(
                            ManufacturingDto::type.name,
                            mc.toMaterialEntityClassForMaterialTable(),
                        )
                    }

                val procurementCriteria =
                    EqualCriteria(
                        ManufacturingDto::customProcurementType.name,
                        procurementTypeAndMaterialClass.procurementType.value,
                    )

                CriteriaHelper.and(
                    entityLocatorCriteria = listOfNotNull(materialClassCriteria, procurementCriteria).toTypedArray(),
                )
            }

        return CriteriaHelper.or(entityLocatorCriteria = criteria.toTypedArray())
    }

    data class EditableConfig(
        val criteria: EntityLocatorCriteria,
        val specialColumnDefinition: Map<FieldTableColumnIdentifier, EntityTableFieldDefinitionFeDto>,
    )
}
