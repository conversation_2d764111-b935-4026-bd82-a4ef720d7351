package com.nu.bom.core.service

import com.fasterxml.jackson.core.JsonParseException
import com.fasterxml.jackson.databind.JsonMappingException
import com.nu.bom.core.api.dtos.BomNodeChangeDto
import com.nu.bom.core.api.dtos.BomNodeHistoryDto
import com.nu.bom.core.api.dtos.EntityDeletionTriggerDtoData
import com.nu.bom.core.api.dtos.EntityPathDto
import com.nu.bom.core.api.dtos.ExtraNodeInfo
import com.nu.bom.core.api.dtos.FieldConversionService
import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.api.dtos.IndicatorDto
import com.nu.bom.core.api.dtos.KeyPerformanceIndicatorApiDto
import com.nu.bom.core.api.dtos.LegacyTriggerDtoData
import com.nu.bom.core.api.dtos.TriggerDto
import com.nu.bom.core.manufacturing.enums.UnitOverrideContext
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.ChangesetId
import com.nu.bom.core.model.Indicator
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.RenameBranch
import com.nu.bom.core.model.SaveToSource
import com.nu.bom.core.model.SnapshotId
import com.nu.bom.core.model.SubBomCreation
import com.nu.bom.core.model.createBranchId
import com.nu.bom.core.model.normalize
import com.nu.bom.core.model.toBomNodeId
import com.nu.bom.core.model.toBranchId
import com.nu.bom.core.model.toChangeSetId
import com.nu.bom.core.model.toMongoChangesetId
import com.nu.bom.core.model.toMongoFormatStr
import com.nu.bom.core.model.toMongoID
import com.nu.bom.core.model.toMongoSnapshotId
import com.nu.bom.core.model.toProjectId
import com.nu.bom.core.service.bomnode.ExtraNodeInfoHandlerService
import com.nu.bom.core.service.bomrads.BomNodeLoaderService
import com.nu.bom.core.service.bomrads.BomradsBomNodeService
import com.nu.bom.core.service.bomrads.BomradsObjectMapperService
import com.nu.bom.core.service.bomrads.SingleNode
import com.nu.bom.core.service.change.ChangeTrackingService
import com.nu.bom.core.service.change.HISTORY_TYPE
import com.nu.bom.core.service.change.RequestedPartInfo
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.user.UserClient
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.nu.bom.core.utils.applyIfExists
import com.nu.bom.core.utils.pickFirstOrEmpty
import com.nu.bomrads.dto.NodeHistoryDTO
import com.nu.bomrads.enumeration.ChangeType
import com.nu.bomrads.enumeration.TriggerRelationType
import com.nu.user.UserInfoDto
import com.tset.core.api.dto.CurrencyInfo
import com.tset.core.api.dto.RefKeyDto
import com.tset.core.module.bom.exchangerates.ExchangeRateMap
import org.codehaus.jackson.JsonProcessingException
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toFlux
import reactor.kotlin.core.publisher.toMono
import java.util.Optional
import java.util.function.Predicate
import com.nu.bomrads.id.ChangesetId as BomradChangesetId
import com.nu.bomrads.id.ProjectId as BomradProjectId

@Service
class HistoryService(
    private val changeTrackingService: ChangeTrackingService,
    private val bomradsBomNodeService: BomradsBomNodeService,
    private val mapperService: BomradsObjectMapperService,
    private val extraNodeInfoHandlerService: ExtraNodeInfoHandlerService,
    private val fieldConversionService: FieldConversionService,
    private val bomNodeLoaderService: BomNodeLoaderService,
    private val userClient: UserClient,
) {
    fun getNodeHistoryForNode(
        accessCheck: AccessCheck,
        projectId: String,
        nodeId: String,
        branch: String?,
        limit: Long?,
        startChangeset: String?,
        type: HISTORY_TYPE?,
        includeChildren: Boolean?,
        includeParents: Boolean?,
        includePublish: Boolean?,
        includeSaves: Boolean? = false,
    ): Mono<List<BomNodeHistoryDto>> {
        val realLimit = if (limit == null || limit > 20) 20 else limit
        val filter =
            ChangeTrackingService.HistoryFilter(
                includeChildChanges = includeChildren ?: true,
                includeParentChange = includeParents ?: true,
                includePublishes = includePublish ?: true,
                includeSaves = includeSaves ?: false,
                includeUnrelated = false,
            )
        return getNodeHistoryForNodeBomrads(
            accessCheck,
            type,
            branch,
            projectId.toMongoID(),
            nodeId,
            realLimit,
            filter,
            startChangesetId = startChangeset.applyIfExists { ChangesetId(it).toChangeSetId() },
        )
    }

    /**
     * this class implements a stateful filter.
     * It returns true as long as not more then <code>realLimit</code> entries of type
     * <code>ChangeType.PUBLISH</code> are encountered
     */
    private class PredicateNonPublishChanges(val realLimit: Long) : Predicate<NodeHistoryDTO> {
        private var counter: Int = 0

        override fun test(historyEntry: NodeHistoryDTO): Boolean {
            if (historyEntry.type != ChangeType.PUBLISH) {
                counter++
            }
            return counter <= realLimit
        }
    }

    private fun getLatestHistoryEntry(
        accessCheck: AccessCheck,
        branch: String?,
        projId: ProjectId,
        nodeId: String,
        historyList: List<NodeHistoryDTO>,
        historyListIncludesCurrentBranch: Boolean,
    ): Mono<NodeHistoryDTO> =
        if (historyListIncludesCurrentBranch) {
            Mono.justOrEmpty(historyList.firstOrNull())
        } else {
            bomradsBomNodeService.getHistory(
                accessCheck,
                projId.toProjectId(),
                BomNodeId(nodeId).toBomNodeId(),
                createBranchId(branch)?.toBranchId(),
                startChangesetId = null,
                includeCurrentBranch = true,
            ).pickFirstOrEmpty()
        }

    private fun getNodeHistoryForNodeBomrads(
        accessCheck: AccessCheck,
        type: HISTORY_TYPE?,
        branch: String?,
        projId: ProjectId,
        nodeId: String,
        realLimit: Long,
        filter: ChangeTrackingService.HistoryFilter,
        startChangesetId: BomradChangesetId?,
    ): Mono<List<BomNodeHistoryDto>> {
        val includeCurrentBranch = type != HISTORY_TYPE.PREVIOUS
        return bomradsBomNodeService.getHistory(
            accessCheck,
            projId.toProjectId(),
            BomNodeId(nodeId).toBomNodeId(),
            createBranchId(branch)?.toBranchId(),
            startChangesetId,
            includeCurrentBranch = includeCurrentBranch,
            // we want to have up to realLimit "non-published-changes" in the result (publish changes will be filtered out later)
        ).collectList().flatMap { historyList ->
            getLatestHistoryEntry(accessCheck, branch, projId, nodeId, historyList, includeCurrentBranch)
                .flatMap { latestHistoryEntry ->
                    getNodeHistoryForNodeBomrads(
                        accessCheck,
                        historyList,
                        latestHistoryEntry,
                        type,
                        branch,
                        projId,
                        nodeId,
                        realLimit,
                        filter,
                    )
                }
        }
    }

    private fun getNodeHistoryForNodeBomrads(
        accessCheck: AccessCheck,
        historyList: List<NodeHistoryDTO>,
        latestHistoryEntry: NodeHistoryDTO,
        type: HISTORY_TYPE?,
        branch: String?,
        projId: ProjectId,
        nodeId: String,
        realLimit: Long,
        filter: ChangeTrackingService.HistoryFilter,
    ): Mono<List<BomNodeHistoryDto>> {
        val bomradsProjectId = projId.toProjectId()
        val bomNodeId = BomNodeId(nodeId)
        return bomNodeLoaderService
            .getBomNode(accessCheck, bomNodeId, branch?.let { BranchId(it) }, SingleNode(bomNodeId.toBomNodeId()))
            .flatMap { snapshot ->
                // we use the BomNode to extract the ExchangeRates
                val exchangeRateMap = snapshot.manufacturing?.getExchangeRateMap() ?: ExchangeRateMap.empty()
                // here something goes wrong
                processBomradsHistory(
                    historyList,
                    latestHistoryEntry,
                    accessCheck,
                    bomradsProjectId,
                    exchangeRateMap,
                    filter.copy(extraFilter = PredicateNonPublishChanges(realLimit)),
                ).flatMap { bomNodeHistoryDtoList ->
                    val legacyChangesetId = historyList.lastOrNull()?.legacyPreviousId
                    val limitLegacyHistory = realLimit - bomNodeHistoryDtoList.size
                    if (legacyChangesetId != null && limitLegacyHistory > 0) {
                        // if legacyChangesetId is set in the last element of the history
                        // we need to load the history in the legacy way (without bomrads),
                        // because bomrads does not store the legacy history
                        // after loading the legacy history we combine it with the new history (history from bomrads)
                        // using Mono.zip
                        changeTrackingService.getBomNodeHistoryAsDto(
                            accessCheck = accessCheck,
                            projectId = projId,
                            branchId = createBranchId(branch),
                            startChangeset = legacyChangesetId.toMongoChangesetId(),
                            bomNodeId = BomNodeId(nodeId),
                            limit = limitLegacyHistory,
                            type = type ?: HISTORY_TYPE.ALL,
                            filter = filter,
                            exchangeRateMap = exchangeRateMap,
                        ).map {
                            bomNodeHistoryDtoList +
                                it.subList(
                                    0,
                                    it.size.coerceAtMost(realLimit.toInt() - bomNodeHistoryDtoList.size),
                                )
                        }
                    } else {
                        Mono.just(
                            bomNodeHistoryDtoList.subList(
                                0,
                                bomNodeHistoryDtoList.size.coerceAtMost(realLimit.toInt()),
                            ),
                        )
                    }
                }
            }
    }

    @TsetSuppress("tset:reactive:flux-flatmapsequential") // whitelisted already existing calls
    private fun processBomradsHistory(
        historyList: List<NodeHistoryDTO>,
        latestHistoryEntry: NodeHistoryDTO,
        accessCheck: AccessCheck,
        projectId: BomradProjectId,
        masterDataExchangeRate: ExchangeRateMap,
        filter: ChangeTrackingService.HistoryFilter,
    ): Mono<List<BomNodeHistoryDto>> {
        // here something goes wrong already in the filter
        val filteredHistoryList = historyList.filter { filter.apply(it) }
        val snapshotIds = collectSnapshotIdsToLoad(filteredHistoryList).toSet()
        return extraNodeInfoHandlerService.loadSnapshotsAndUpdateMeta(accessCheck, projectId, snapshotIds)
            .flatMapMany { extraNodeInfo ->
                val latestUnitOverrideContext =
                    mapperService.getExtraNodeInfo(latestHistoryEntry, extraNodeInfo)?.unitOverrideContext
                filteredHistoryList.toFlux().flatMapSequential { nodeHistoryDTO ->
                    getBomNodeChangesFromHistory(
                        snapshotsMap = extraNodeInfo,
                        nodeHistoryDTO = nodeHistoryDTO,
                        dataExchangeRates = masterDataExchangeRate,
                        projectId = projectId,
                        accessCheck = accessCheck,
                        latestUnitOverrideContext = latestUnitOverrideContext,
                    )
                }
            }.collectList().map(::grouping)
    }

    private fun collectSnapshotIdsToLoad(historyList: List<NodeHistoryDTO>): List<SnapshotId> {
        val snapshotIds =
            historyList
                .filter { it.branchId != null && it.meta == null }
                .mapNotNull { it.treeId }
                .map { it.toMongoSnapshotId() }
        val triggerSnapshotIds =
            historyList.flatMap {
                it.triggers
            }.filter {
                it.meta == null
            }.mapNotNull {
                it.treeId
            }.map {
                it.toMongoSnapshotId()
            }
        return snapshotIds + triggerSnapshotIds
    }

    data class Accum(
        var key: String = "",
        val changes: MutableList<MutableList<BomNodeChangeDto>> = mutableListOf<MutableList<BomNodeChangeDto>>(),
    ) {
        fun startNewList(
            newKey: String,
            current: BomNodeChangeDto,
        ) {
            // add new list - with the current item in it as the only item
            changes.add(0, mutableListOf(current))
            key = newKey
        }

        fun addToCurrent(current: BomNodeChangeDto) {
            if (changes.isEmpty()) {
                changes.add(0, mutableListOf(current))
            } else {
                changes.first().add(0, current)
            }
        }
    }

    private fun grouping(bomNodeChanges: List<BomNodeChangeDto>): List<BomNodeHistoryDto> {
        val acc = Accum()
        for (current in bomNodeChanges.reversed()) {
            val currentBranch = current.branch ?: ""
            if (acc.key == currentBranch || current.trigger.triggerType == SaveToSource::class.java.simpleName) {
                // SaveToSource should go
                acc.addToCurrent(current)
            } else {
                // add new list - with the current item in it as the only item
                acc.startNewList(currentBranch, current)
            }
        }
        return acc.changes.map {
            removeRenameWhenSavePublicVariant(it)
            val first = it.first()
            BomNodeHistoryDto(first.branch, first.kpi, it)
        }
    }

    /**
     * for a "save as" action we have history entries for both RenameBranch and SavePublicVariantV2,
     * the RenameBranch is removed in such cases to get a correct history
     */
    private fun removeRenameWhenSavePublicVariant(nodeChanges: MutableList<BomNodeChangeDto>) {
        val containsSaveVariant =
            nodeChanges.any { n -> n.trigger.triggerType == "SavePublicVariant" }
        val renameNode =
            if (containsSaveVariant) {
                nodeChanges.find { n ->
                    n.trigger.triggerType == RenameBranch::class.simpleName
                }
            } else {
                null
            }
        renameNode.applyIfExists(nodeChanges::remove)
    }

    private fun getBomNodeChangesFromHistory(
        snapshotsMap: Map<SnapshotId, ExtraNodeInfo>,
        nodeHistoryDTO: NodeHistoryDTO,
        dataExchangeRates: ExchangeRateMap,
        projectId: BomradProjectId,
        accessCheck: AccessCheck,
        latestUnitOverrideContext: UnitOverrideContext?,
    ): Mono<BomNodeChangeDto> =
        getTrigger(accessCheck, dataExchangeRates, nodeHistoryDTO).map { trigger ->
            bomNodeChangeDto(
                projectId,
                nodeHistoryDTO,
                trigger,
                dataExchangeRates,
                // Get the extra node info, either from NodeHistoryDTO.meta field, or from the loaded Map<SnapshotId, ExtraNodeInfo> structure
                extraNodeInfo =
                    mapperService.getExtraNodeInfo(nodeHistoryDTO, snapshotsMap),
                requestedPartInfo =
                    nodeHistoryDTO.trigger()
                        ?.let(mapperService::deserialize)?.triggerContext?.partInfoTrigger,
                createdBy = userClient.getUserById(accessCheck, nodeHistoryDTO.creatorUser).orElse(null),
                latestUnitOverrideContext = latestUnitOverrideContext,
            )
        }

    private fun createCostPerPartChange(
        extraNode: ExtraNodeInfo,
        exchangeRateMap: ExchangeRateMap,
        latestUnitOverrideContext: UnitOverrideContext?,
    ): IndicatorDto<CurrencyInfo> {
        val old =
            if (extraNode.oldCostPerPartInBaseCurrency != null && extraNode.oldBaseCurrency != null) {
                exchangeRateMap.ccyToCurrencyInfo(
                    normalize(extraNode.oldCostPerPartInBaseCurrency, extraNode.unitOverrideContext),
                    extraNode.oldBaseCurrency,
                )
            } else {
                exchangeRateMap.eurToCurrencyInfo(normalize(extraNode.oldCostPerPart, extraNode.unitOverrideContext))
            }
        return Indicator(
            old,
            exchangeRateMap.ccyToCurrencyInfo(
                normalize(extraNode.costPerPartInBaseCurrency, extraNode.unitOverrideContext),
                extraNode.baseCurrency,
            ),
            broken = extraNode.costPerPart == null,
        ).toDto(latestUnitOverrideContext, extraNode.baseCurrency)
    }

    private fun bomNodeChangeDto(
        projectId: BomradProjectId,
        nodeHistoryDTO: NodeHistoryDTO,
        trigger: TriggerDto,
        exchangeRateMap: ExchangeRateMap,
        extraNodeInfo: ExtraNodeInfo?,
        requestedPartInfo: RequestedPartInfo?,
        createdBy: UserInfoDto? = null,
        latestUnitOverrideContext: UnitOverrideContext?,
    ): BomNodeChangeDto {
        val partInfo = requestedPartInfo ?: getPartInfo(extraNodeInfo)
        val relationToRequested = getRelationToRequested(nodeHistoryDTO)
        val (path, refKey) = getEntityPathAndRefKey(trigger)
        val kpi =
            extraNodeInfo?.let { extraNode ->
                KeyPerformanceIndicatorApiDto(
                    costPerPart = createCostPerPartChange(extraNode, exchangeRateMap, latestUnitOverrideContext),
                    cO2PerPart =
                        Indicator(
                            normalize(extraNode.oldCo2PerPart, extraNode.unitOverrideContext),
                            normalize(extraNode.co2PerPart, extraNode.unitOverrideContext),
                            broken = extraNode.co2PerPart == null,
                        ).toDto(latestUnitOverrideContext),
                )
            }

        return BomNodeChangeDto(
            nodeId = nodeHistoryDTO.bomNodeId.toMongoFormatStr(),
            changesetId = nodeHistoryDTO.changesetId.toMongoFormatStr(),
            timestamp = nodeHistoryDTO.created,
            projectId = projectId.toMongoFormatStr(),
            branch = nodeHistoryDTO.branchId?.toMongoFormatStr(),
            createdBy =
                Optional.ofNullable(createdBy)
                    .or { Optional.of(UserInfoDto(nodeHistoryDTO.creatorUser, nodeHistoryDTO.creatorUser)) },
            trigger = trigger,
            kpi = kpi,
            createdWithSystemVersion = null,
            relationToRequested = relationToRequested,
            partInfo = partInfo,
            oldTitle = nodeHistoryDTO.title,
            newTitle = nodeHistoryDTO.variantName,
            path = path,
            refKey = refKey,
            dirtyChildLoading = extraNodeInfo?.dirtyChildLoading,
        )
    }

    private fun getPartInfo(extraNodeInfo: ExtraNodeInfo?): RequestedPartInfo {
        return RequestedPartInfo(
            partName = extraNodeInfo?.partName ?: "N/A",
            partNumber = extraNodeInfo?.partNumber,
            partImage = null,
            title = "",
        )
    }

    private fun getRelationToRequested(nodeHistoryDTO: NodeHistoryDTO): BomNodeSnapshot.BomNodeRelationType {
        return when (nodeHistoryDTO.triggerRelation()) {
            null -> BomNodeSnapshot.BomNodeRelationType.EXT
            TriggerRelationType.SAME -> BomNodeSnapshot.BomNodeRelationType.SAME
            TriggerRelationType.CHILD -> BomNodeSnapshot.BomNodeRelationType.CHILD
            TriggerRelationType.PARENT -> BomNodeSnapshot.BomNodeRelationType.PARENT
        }
    }

    private fun getEntityPathAndRefKey(trigger: TriggerDto): Pair<List<EntityPathDto>, RefKeyDto?> {
        val triggerDtoData = trigger.data
        return if (triggerDtoData is LegacyTriggerDtoData && triggerDtoData.entityId != null && triggerDtoData.entityType != null) {
            if (triggerDtoData.triggerType == SubBomCreation::class.simpleName) {
                // for SubBomCreation events dont send a path, because with a path FE does not show the correct badge
                Pair(emptyList(), null)
            } else {
                val pathElement =
                    EntityPathDto(
                        entityType = triggerDtoData.entityType,
                        objectId = triggerDtoData.entityId,
                        displayDesignation =
                            FieldParameter(
                                name = "displayDesignation",
                                type = "Text",
                                value = triggerDtoData.entityDisplayName,
                                denominatorUnit = null,
                            ),
                    )
                Pair(listOf(pathElement), null)
            }
        } else if (triggerDtoData is EntityDeletionTriggerDtoData && triggerDtoData.entityId != null && triggerDtoData.entityType != null) {
            val pathElement =
                EntityPathDto(
                    entityType = triggerDtoData.entityType,
                    objectId = triggerDtoData.entityId,
                    displayDesignation =
                        FieldParameter(
                            name = "displayDesignation",
                            type = "Text",
                            value = triggerDtoData.entityDisplayName,
                            denominatorUnit = null,
                        ),
                )
            Pair(listOf(pathElement), null)
        } else if (triggerDtoData is LegacyTriggerDtoData && triggerDtoData.path != null) {
            Pair(
                triggerDtoData.path.map {
                    EntityPathDto(
                        entityType = it.entityType,
                        objectId = it.objectId,
                        displayDesignation = it.displayDesignation,
                    )
                },
                null,
            )
        } else {
            Pair(emptyList(), null)
        }
    }

    private fun getTrigger(
        accessCheck: AccessCheck,
        exchangeRateMap: ExchangeRateMap,
        nodeHistoryDTO: NodeHistoryDTO,
    ): Mono<TriggerDto> =
        nodeHistoryDTO.trigger().toMono().flatMap {
            try {
                val triggerInfo = mapperService.deserialize(it)
                triggerInfo.triggerAction.toTriggerDto(
                    accessCheck,
                    fieldConversionService,
                    exchangeRateMap,
                    triggerInfo.triggerContext,
                )
            } catch (ex: JsonProcessingException) {
                Mono.empty()
            } catch (ex: JsonMappingException) {
                Mono.empty()
            } catch (ex: JsonParseException) {
                Mono.empty()
            }
        }
}
