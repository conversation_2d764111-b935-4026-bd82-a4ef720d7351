package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import com.nu.bom.core.service.migration.lazy.mappers.utils.ConfigurationFields.addConfigurationFields
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class ModularizedRawtManuConfigurationMigration : ManufacturingModelEntityMapper {
    companion object {
        private val logger = LoggerFactory.getLogger(ModularizedRawtManuConfigurationMigration::class.java)
        const val FIELD_NAME = "rawCostModuleConfigurationIdentifier"
    }

    override val changeSetId = MigrationChangeSetId("2024-10-01-modularized-rawt-manu-config-migration")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity {
        val model = Model.fromEntity(entity.clazz)
        if (model == null || model != Model.RAWT) {
            logger.warn("Incorrect cost module. Entity id:{}, clazz:{}", entity.id, entity.clazz)
        }
        val technologyModel =
            entity.fieldWithResults["rawPartTechnology"]?.let { rawPartTechnology ->
                (rawPartTechnology.value as String?)?.let { Model.valueOf(it) }
            }
        return entity.copy(
            initialFieldWithResults =
                addConfigurationFields(
                    entity.initialFieldWithResults,
                    entity.version,
                    technologyModel,
                    FieldResult.SOURCE.I,
                    FIELD_NAME,
                    upsertTechnology = false,
                ),
            fieldWithResults =
                addConfigurationFields(
                    entity.fieldWithResults,
                    entity.version,
                    technologyModel,
                    FieldResult.SOURCE.C,
                    FIELD_NAME,
                    upsertTechnology = false,
                ),
        )
    }
}
