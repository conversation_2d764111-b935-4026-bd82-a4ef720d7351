package com.nu.bom.core.manufacturing.enums

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.FieldBasedUnits
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.RoughManufacturingStep
import com.nu.bom.core.manufacturing.fieldTypes.AreaTimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.AreaUnits
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.EnergyUnits
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Null
import com.nu.bom.core.manufacturing.fieldTypes.PiecesUnits
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.TypeUnit
import com.nu.bom.core.manufacturing.fieldTypes.VolumeUnits
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.mapOfNotNull
import com.tset.bom.clients.common.DenominatorBehavior
import com.tset.bom.clients.common.DenominatorUnit
import java.math.BigDecimal

enum class DynamicUnitOverride(
    val isManufacturingLevel: Boolean,
    val isCostType: Boolean,
) {
    COST_UNIT(false, true),
    QUANTITY_UNIT(false, false),
    MANUFACTURING_COST_UNIT(true, true),
    MANUFACTURING_QUANTITY_UNIT(true, false),
}

enum class StaticUnitOverride(
    val typeUnit: TypeUnit,
) {
    YEAR(TimeUnits.YEAR),
    HOUR(TimeUnits.HOUR),
    SECOND(TimeUnits.SECOND),
    MINUTE(TimeUnits.MINUTE),
    WEEK(TimeUnits.WEEK),
    DAY(TimeUnits.DAY),
    QM(AreaUnits.QM),
    QMYEAR(AreaTimeUnits.QMYEAR),
    QMHOUR(AreaTimeUnits.QMHOUR),
    KILOWATTHOUR(EnergyUnits.KILOWATTHOUR),
    KILOGRAM(WeightUnits.KILOGRAM),
    CM(VolumeUnits.CM),
    LITER(VolumeUnits.LITER),
    METER(LengthUnits.METER),
    KILOMETER(LengthUnits.KILOMETER),
    QMMONTH(AreaTimeUnits.QMMONTH),
    PIECE(PiecesUnits.PIECE),
}

// TODO: Would be nice to not have this data duplication. --jgr
fun dynamicUnitStringToTypeUnit(str: String) = dynamicUnitStringToTypeUnitNullable(str) ?: error("unknown dynamic unit '$str'.")

fun dynamicUnitStringToTypeUnitNullable(str: String): TypeUnit? =
    when (str) {
        "KILOMETER" -> LengthUnits.KILOMETER
        "METER" -> LengthUnits.METER
        "DECIMETER" -> LengthUnits.DECIMETER
        "CENTIMETER" -> LengthUnits.CENTIMETER
        "MILLIMETER" -> LengthUnits.MILLIMETER
        "MICROMETER" -> LengthUnits.MICROMETER

        "QMM" -> AreaUnits.QMM
        "QCM" -> AreaUnits.QCM
        "QDM" -> AreaUnits.QDM
        "QM" -> AreaUnits.QM

        "PIECE" -> PiecesUnits.PIECE
        "TEN_PIECES" -> PiecesUnits.TEN_PIECES
        "HUNDRED_PIECES" -> PiecesUnits.HUNDRED_PIECES
        "THOUSAND_PIECES" -> PiecesUnits.THOUSAND_PIECES

        "GRAM" -> WeightUnits.GRAM
        "MILLIGRAM" -> WeightUnits.MILLIGRAM
        "KILOGRAM" -> WeightUnits.KILOGRAM
        "TON" -> WeightUnits.TON

        "CMM" -> VolumeUnits.CMM
        "CDM" -> VolumeUnits.CDM
        "CCM" -> VolumeUnits.CCM
        "CM" -> VolumeUnits.CM
        "LITER" -> VolumeUnits.LITER
        "MILLILITER" -> VolumeUnits.MILLILITER

        else -> null
    }

// when calculating something that is per 100 pcs instead of per piece, the value needs to represent
// the price of the 100 pieces, not just one. Same for 100l or similar.
fun BigDecimal?.toConvertedValueForUnit(unitOverrideContext: UnitOverrideContext?): BigDecimal? =
    if (this != null && unitOverrideContext?.costUnitString != null) {
        val unit = dynamicUnitStringToTypeUnit(unitOverrideContext.costUnitString)
        this * unit.baseFactor
    } else {
        this
    }

fun dynamicUnitStringToDenominatorUnit(str: String): DenominatorUnit =
    dynamicUnitStringToTypeUnit(str).toDenominatorUnit(
        DenominatorBehavior.TRANSFORM,
    )

// TODO! Better encapsulation candidate, once I know how and still keep Jackson working.
data class UnitOverrideContext(
    val costUnitString: String?,
    val quantityUnitString: String?,
    val dimension: Dimension.Selection?,
    val manufacturingCostUnitString: String?,
    val manufacturingQuantityUnitString: String?,
    val manufacturingDimension: Dimension.Selection?,
) {
    private val costUnit get() = costUnitString?.let { dynamicUnitStringToTypeUnit(it) }
    private val quantityUnit get() = quantityUnitString?.let { dynamicUnitStringToTypeUnit(it) }
    private val manufacturingCostUnit get() = manufacturingCostUnitString?.let { dynamicUnitStringToTypeUnit(it) }
    private val manufacturingQuantityUnit get() = manufacturingQuantityUnitString?.let { dynamicUnitStringToTypeUnit(it) }

    fun getFieldTypeAndUnit(
        fieldTypeStatic: String,
        entityManager: EntityManager,
    ) = when (fieldTypeStatic) {
        QuantityUnit::class.simpleName ->
            Pair(
                dimension?.type
                    ?: throw IllegalArgumentException(
                        "Used dynamic numerator, but didn't provide the required context (specifically, a dimension).",
                    ),
                dimension.baseUnit,
            )

        else ->
            Pair(
                fieldTypeStatic,
                entityManager.getUnits(fieldTypeStatic).find { it.baseFactor == BigDecimal.ONE }?.toString(),
            )
    }

    fun getDynamicUnit(unit: DynamicUnitOverride) =
        when (unit) {
            DynamicUnitOverride.COST_UNIT ->
                costUnit ?: run {
                    throw IllegalArgumentException("Used a cost unit, but didn't provide the required context (what costUnit to use).")
                }

            DynamicUnitOverride.QUANTITY_UNIT ->
                quantityUnit ?: run {
                    throw IllegalArgumentException(
                        "Used a quantity unit, but didn't provide the required context (what quantityUnit to use).",
                    )
                }

            DynamicUnitOverride.MANUFACTURING_COST_UNIT ->
                manufacturingCostUnit ?: run {
                    throw IllegalArgumentException(
                        "Used a manufacturingCost unit, but didn't provide the required context (what manufacturingCostUnit to use).",
                    )
                }

            DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT ->
                manufacturingQuantityUnit ?: run {
                    throw IllegalArgumentException(
                        "Used a manufacturingQuantity unit, but didn't provide the required context " +
                            "(what manufacturingQuantityUnit to use).",
                    )
                }
        }

    companion object {
        val noContext get() = UnitOverrideContext(null, null, null, null, null, null)

        val mockWithDefaults get() = defaultManufacturing

        val defaultManufacturing
            get() =
                Dimension.getDefault(Entities.MANUFACTURING)!!.res.let {
                    UnitOverrideContext(
                        it.getDefaultCostUnit(),
                        it.getDefaultQuantityUnit(),
                        it,
                        it.getDefaultCostUnit(),
                        it.getDefaultQuantityUnit(),
                        it,
                    )
                }

        fun defaultFromDimension(dimension: Dimension.Selection?) =
            noContext.let {
                when (dimension) {
                    null -> it
                    else ->
                        it.copy(
                            dimension = dimension,
                            costUnitString = dimension.getDefaultCostUnit(),
                            quantityUnitString = dimension.getDefaultQuantityUnit(),
                        )
                }
            }

        fun fromEntityWhichWasCalculatedWithoutTheParentManufacturing(
            entity: ManufacturingEntity,
            unitOverrideContextParentManufacturing: UnitOverrideContext,
            entityType: Entities,
        ): UnitOverrideContext {
            require(entity.getEntityTypeAnnotationOrThrow() == entityType)

            @Suppress("DEPRECATION")
            return when (entityType) {
                Entities.MANUFACTURING -> throw IllegalArgumentException(
                    "It doesn't make sense that we assume that this entity, which is supposed to be a manufacturing, " +
                        "exists inside another manufacturing.",
                )
                Entities.MANUFACTURING_STEP ->
                    if (entity.extensionSet().contains(RoughManufacturingStep::class.java)) {
                        fromEntity(
                            entity,
                        ).copy(
                            manufacturingCostUnitString = unitOverrideContextParentManufacturing.manufacturingCostUnitString,
                            manufacturingQuantityUnitString = unitOverrideContextParentManufacturing.manufacturingQuantityUnitString,
                            manufacturingDimension = unitOverrideContextParentManufacturing.manufacturingDimension,
                        )
                    } else {
                        unitOverrideContextParentManufacturing
                    }

                Entities.LABOR,
                Entities.SETUP, Entities.TOOL,
                Entities.MACHINE,
                Entities.SPECIAL_DIRECT_COST,
                Entities.TRANSPORT_ROUTE,
                Entities.OVERHEAD_SUB_CALCULATOR,
                Entities.MD_OVERHEAD,
                Entities.MD_INTEREST,
                Entities.MD_OVERHEADS_PARENT,
                Entities.MD_INTERESTS_PARENT,
                Entities.MD_EXCHANGERATE,
                Entities.MD_EXCHANGERATE_PARENT,
                Entities.MD_COSTFACTORS_PARENT,
                Entities.MD_COSTFACTORS_WAGE,
                Entities.MD_COSTFACTORS_LABOR_BURDEN,
                Entities.MD_COSTFACTORS_ELECTRICITY_PRICE,
                Entities.MD_COSTFACTORS_NATURAL_GAS_PRICE,
                Entities.MD_COSTFACTORS_ELECTRICITY_EMISSIONS,
                Entities.MD_COSTFACTORS_NATURAL_GAS_EMISSIONS,
                Entities.MD_COSTFACTORS_FLOOR_SPACE_PRICE,
                Entities.MD_COSTFACTORS_CAST_EXCIPIENTS_PRICE,
                Entities.MD_COSTFACTORS_OXYGEN_PRICE,
                Entities.MD_COSTFACTORS_INTEREST,
                Entities.MD_COSTFACTORS_ALUMINIUM_SHARE,
                Entities.MD_COSTFACTORS_ALUMINIUM_EMISSIONS,
                Entities.MD_COSTFACTORS_COUNTRY_INFO,
                Entities.MD_MATERIAL_PARENT,
                Entities.MD_MATERIAL_PRICE,
                Entities.MD_MATERIAL_CLASSIFICATION_SCHEMA,
                Entities.MD_MATERIAL_CLASSIFICATION_VALUE,
                Entities.MD_MATERIAL_EMISSION,
                Entities.MATERIAL, Entities.COMPONENT_MATERIAL, Entities.PROCESSED_MATERIAL,
                Entities.CONSUMABLE, Entities.BOM_ENTRY, Entities.C_PART, Entities.MATERIAL_GEOMETRY,
                Entities.PRICE_COMPONENT,
                ->
                    fromEntity(
                        entity,
                    ).copy(
                        manufacturingCostUnitString = unitOverrideContextParentManufacturing.manufacturingCostUnitString,
                        manufacturingQuantityUnitString = unitOverrideContextParentManufacturing.manufacturingQuantityUnitString,
                        manufacturingDimension = unitOverrideContextParentManufacturing.manufacturingDimension,
                    )

                Entities.CYCLETIME_STEP,
                Entities.CYCLETIME_STEP_GROUP,
                Entities.METHOD_PLAN_STAGE,
                Entities.METHOD_PLAN_FEATURE,
                Entities.TOOL_COST_STRUCTURE,
                Entities.TOOL_COST_ROW,
                Entities.PART,
                Entities.EXCHANGE_RATES,
                Entities.EXCHANGE_RATE,
                Entities.ATTACHMENT,
                Entities.TTYPE,
                Entities.TTYPE2,
                Entities.SYSTEM_PARAMETER,
                Entities.BASE,
                Entities.CO2_PROCESSING_MATERIAL,
                Entities.NONE,
                Entities.ANY,
                Entities.DETAILED_TOOL_MAINTENANCE_CALCULATOR,
                Entities.PART_TARGET,
                Entities.GROUP,
                -> noContext
            }
        }

        fun fromEntity(entity: ManufacturingEntity): UnitOverrideContext {
            // Last resort, we fall back to the manufacturing default. There might be some fields in entities like tools that use QuantityUnit,
            // because they copy a field from somewhere else where such a unit exists …
            val fallbackDimension =
                Dimension.getDefault(entity::class.java.getAnnotation(EntityType::class.java)!!.name)?.res
                    ?: Dimension.getDefault(Entities.MANUFACTURING)!!.res
            val fallbackCostUnit = fallbackDimension.getDefaultCostUnit()
            val fallbackQuantityUnit = fallbackDimension.getDefaultQuantityUnit()

            val fallbackManufacturingDimension = Dimension.getDefault(Entities.MANUFACTURING)!!.res
            val fallbackManufacturingCostUnit = fallbackManufacturingDimension.getDefaultCostUnit()
            val fallbackManufacturingQuantityUnit =
                fallbackManufacturingDimension.getDefaultQuantityUnit()

            val fieldResult =
                entity.getFieldResults(
                    "manufacturingCostUnit",
                    "manufacturingQuantityUnit",
                    "manufacturingDimension",
                    "costUnit",
                    "quantityUnit",
                    "dimension",
                )

            val manufacturingCostUnit =
                fieldResult["manufacturingCostUnit"]?.res as String?
            val manufacturingQuantityUnit =
                fieldResult["manufacturingQuantityUnit"]?.res as String?
            val manufacturingDimension =
                fieldResult["manufacturingDimension"]?.res as Dimension.Selection?

            val costUnit = fieldResult["costUnit"]?.res as? String?
            val quantityUnit =
                fieldResult["quantityUnit"]
                    ?.let {
                        when (it) {
                            // legacy versions of Coke, GravityCastingFilter,// and GravityCastingStainer, can contain this field.
                            is Null -> null
                            else -> it
                        }
                    }?.res as String?
            // TODO check that dimension is always provided by the header so it cannot be null
            val dimension = fieldResult["dimension"]?.res as? Dimension.Selection?

            return UnitOverrideContext(
                costUnit ?: fallbackCostUnit,
                quantityUnit ?: fallbackQuantityUnit,
                dimension ?: fallbackDimension,
                manufacturingCostUnit ?: fallbackManufacturingCostUnit,
                manufacturingQuantityUnit ?: fallbackManufacturingQuantityUnit,
                manufacturingDimension ?: fallbackManufacturingDimension,
            )
        }
    }
}

/**
 * this class stores information about the units "generated" by the [FieldBasedUnits] annotation
 */
data class FieldBasedUnitOverrideContext(
    val numeratorUnit: String?,
    val denominatorUnit: String?,
    val denominatorUnitType: String?,
) {
    fun toMetaInfo(): Map<String, Any>? =
        mapOfNotNull(
            FieldBasedUnits.META_INFO_NUMERATOR_VALUE to numeratorUnit,
            FieldBasedUnits.META_INFO_DENOMINATOR_VALUE to denominatorUnit,
            FieldBasedUnits.META_INFO_DENOMINATOR_UNIT_TYPE to denominatorUnitType,
        ).takeIf { it.isNotEmpty() }

    companion object {
        fun fromMetaInfo(metaInfo: Map<String, Any>): FieldBasedUnitOverrideContext? {
            val numeratorField = metaInfo[FieldBasedUnits.META_INFO_NUMERATOR_VALUE] as String?
            val denominatorField = metaInfo[FieldBasedUnits.META_INFO_DENOMINATOR_VALUE] as String?
            val denominatorUnitType = metaInfo[FieldBasedUnits.META_INFO_DENOMINATOR_UNIT_TYPE] as String?

            return if (numeratorField != null || denominatorField != null) {
                FieldBasedUnitOverrideContext(
                    numeratorUnit = numeratorField,
                    denominatorUnit = denominatorField,
                    denominatorUnitType = denominatorUnitType ?: FieldBasedUnits.DENOMINATOR_UNIT_TYPE_MONEY,
                )
            } else {
                null
            }
        }
    }
}
