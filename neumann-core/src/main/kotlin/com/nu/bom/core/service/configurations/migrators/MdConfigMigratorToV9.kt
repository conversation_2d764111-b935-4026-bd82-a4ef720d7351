package com.nu.bom.core.service.configurations.migrators

import com.nu.bom.core.model.configurations.masterdata.MasterdataConfigurationV8
import com.nu.bom.core.model.configurations.masterdata.MasterdataConfigurationV9
import com.nu.bom.core.model.configurations.masterdata.MaterialMasterdataConfiguration
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService
import org.springframework.stereotype.Component

@Component
class MdConfigMigratorToV9 : MasterdataConfigMigrator<MasterdataConfigurationV8, MasterdataConfigurationV9> {
    override fun migrate(configurationValue: MasterdataConfigurationV8): MasterdataConfigurationV9 =
        MasterdataConfigurationV9(
            effectivityDefinitions = configurationValue.effectivityDefinitions,
            overheadsConfiguration = configurationValue.overheadsConfiguration,
            skillTypeConfiguration = configurationValue.skillTypeConfiguration,
            interestsConfiguration = configurationValue.interestsConfiguration,
            exchangeRatesConfiguration = configurationValue.exchangeRatesConfiguration,
            overheadMethodConfiguration = configurationValue.overheadMethodConfiguration,
            wageConfiguration = configurationValue.wageConfiguration,
            laborBurdenConfiguration = configurationValue.laborBurdenConfiguration,
            electricityPriceConfiguration = configurationValue.electricityPriceConfiguration,
            naturalGasPriceConfiguration = configurationValue.naturalGasPriceConfiguration,
            electricityEmissionsConfiguration = configurationValue.electricityEmissionsConfiguration,
            naturalGasEmissionsConfiguration = configurationValue.naturalGasEmissionsConfiguration,
            floorSpacePriceConfiguration = configurationValue.floorSpacePriceConfiguration,
            costFactorsInterestConfiguration = configurationValue.costFactorsInterestConfiguration,
            aluminiumShareConfiguration = configurationValue.aluminiumShareConfiguration,
            aluminiumEmissionConfiguration = configurationValue.aluminiumEmissionConfiguration,
            castExcipientsPriceConfiguration = configurationValue.castExcipientsPriceConfiguration,
            oxygenPriceConfiguration = configurationValue.oxygenPriceConfiguration,
            countryIdConfiguration = configurationValue.countryIdConfiguration,
            regionConfiguration = configurationValue.regionConfiguration,
            costFactorsConfiguration = configurationValue.costFactorsConfiguration,
            materialPriceConfiguration = configurationValue.materialPriceConfiguration,
            materialCo2Configuration = configurationValue.materialCo2Configuration,
            materialConfiguration =
                MaterialMasterdataConfiguration(
                    materialClassificationTypeKey = configurationValue.materialConfiguration.materialClassificationTypeKey,
                    consumableClassificationKey = configurationValue.materialConfiguration.consumableClassificationKey,
                    electronicComponentClassificationKey =
                        MasterdataTsetConfigurationService.createMaterialConfig().electronicComponentClassificationKey,
                ),
        )

    override fun sourceClass() = MasterdataConfigurationV8::class

    override fun targetClass() = MasterdataConfigurationV9::class
}
