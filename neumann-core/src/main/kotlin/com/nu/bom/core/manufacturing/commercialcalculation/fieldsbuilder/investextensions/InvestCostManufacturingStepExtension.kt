package com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.investextensions

import com.nu.bom.core.manufacturing.annotations.Children
import com.nu.bom.core.manufacturing.annotations.Extends
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.ManufacturingEntityExtension
import com.nu.bom.core.manufacturing.entities.ManufacturingStep
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.extension.PAID_INVEST_COMMERCIAL_CALCULATION_FIELD_EXTENSION
import com.nu.bom.core.manufacturing.fieldTypes.Money
@Suppress("unused")
@Extends([ManufacturingStep::class], PAID_INVEST_COMMERCIAL_CALCULATION_FIELD_EXTENSION)
class InvestCostManufacturingStepExtension(name: String) : ManufacturingEntityExtension(name) {
    @Suppress("unused")
    @ReadOnly
    fun paidTools(
        @Children(Entities.TOOL) directlyPaidInvest: List<Money>?,
    ) = sum(directlyPaidInvest)

    @Suppress("unused")
    @ReadOnly
    fun paidInvestsOfSubcalculations(
        @Children(Entities.BOM_ENTRY) paidInvests: List<Money>?,
    ) = sum(paidInvests)
}
