package com.nu.bom.core.service.masterdata

import com.nu.bom.core.manufacturing.service.InjectableEngineService
import com.nu.bom.core.user.AccessCheck
import com.nu.masterdata.dto.v1.detail.DetailBulkResponseDto
import com.nu.masterdata.dto.v1.detail.DetailDto
import com.nu.masterdata.dto.v1.detail.table.BuiltinLovFilterDto
import com.nu.masterdata.dto.v1.detail.table.DetailQueryDto
import com.nu.masterdata.dto.v1.detail.table.DetailQueryResponseDto
import com.nu.masterdata.dto.v1.header.HeaderDetailQueryResponseDto
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import org.springframework.core.ParameterizedTypeReference
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

/**
 * Service to create and update detail entries in masterdata.
 */
@Service
class MdDetailCrudService(
    private val restService: MasterdataRestService,
) : InjectableEngineService {
    fun postDetailEntries(
        accessCheck: AccessCheck,
        headerTypeKey: SimpleKeyDto,
        details: List<DetailDto>,
    ): Mono<List<DetailBulkResponseDto>> {
        // Due to comment in [NuService::toFlux] we are using a Mono<List<>> and not a Flux<>
        return restService.postToMono(
            uri = { it.path("/api/md/v1/headertypes/{headerTypeKey}/details").build(mapOf("headerTypeKey" to headerTypeKey.key)) },
            requestBody = details,
            responseType = object : ParameterizedTypeReference<List<DetailBulkResponseDto>>() {},
            accessCheck = accessCheck,
        )
    }

    fun postAllDetailEntries(
        accessCheck: AccessCheck,
        headerTypeKey: SimpleKeyDto,
        detailQueryDto: DetailQueryDto,
    ): Mono<DetailQueryResponseDto> {
        return postDetailEntriesSearch(accessCheck, headerTypeKey, detailQueryDto).expand { response ->
            if (response.hasNext) {
                postDetailEntriesSearch(
                    accessCheck,
                    headerTypeKey,
                    detailQueryDto,
                    page = response.number + 1,
                )
            } else {
                Flux.empty()
            }
        }.collectList()
            .map {
                val content = it.map { it.content }.flatten()
                DetailQueryResponseDto(
                    content = content,
                    number = 0,
                    size = content.size,
                    totalElements = content.size,
                )
            }
    }

    private fun postDetailEntriesSearch(
        accessCheck: AccessCheck,
        headerTypeKey: SimpleKeyDto,
        detailQueryDto: DetailQueryDto,
        size: Int = 50,
        page: Int = 0,
    ): Mono<DetailQueryResponseDto> =
        restService.postToMono(
            uri = {
                it.path("/api/md/v1/headertypes/{headerTypeKey}/details/search")
                    .queryParam("size", size)
                    .queryParam("page", page)
                    .build(
                        mapOf("headerTypeKey" to headerTypeKey.key),
                    )
            },
            requestBody = detailQueryDto,
            responseType = object : ParameterizedTypeReference<DetailQueryResponseDto>() {},
            accessCheck = accessCheck,
        )

    fun createDetail(
        accessCheck: AccessCheck,
        headerTypeKey: SimpleKeyDto,
        detailQueryDto: DetailDto,
    ): Mono<DetailDto> =
        restService.postToMono(
            uri = {
                it.path("/api/md/v1/headertypes/{headerTypeKey}/details/create").build(
                    mapOf("headerTypeKey" to headerTypeKey.key),
                )
            },
            requestBody = detailQueryDto,
            responseType = object : ParameterizedTypeReference<DetailDto>() {},
            accessCheck = accessCheck,
        )

    fun postDetailEntriesSearchAsListOfHeaderDto(
        accessCheck: AccessCheck,
        headerTypeKey: SimpleKeyDto,
        detailQueryDto: DetailQueryDto,
    ): Mono<List<HeaderDetailQueryResponseDto>> =
        postAllDetailEntries(
            accessCheck = accessCheck,
            headerTypeKey = headerTypeKey,
            detailQueryDto = detailQueryDto,
        ).map { detailQueryResponseDto ->
            detailQueryResponseDto.content.map { it.headerDto }
        }

    fun getHeaderDetailForHeaderKey(
        accessCheck: AccessCheck,
        headerTypeKey: SimpleKeyDto,
        headerKey: SimpleKeyDto,
    ): Mono<HeaderDetailQueryResponseDto> =
        postDetailEntriesSearchAsListOfHeaderDto(
            accessCheck = accessCheck,
            headerTypeKey = headerTypeKey,
            detailQueryDto =
                DetailQueryDto(
                    filters =
                        mapOf(
                            SimpleKeyDto("_BUILTIN_headerKey") to
                                listOf(BuiltinLovFilterDto(equals = headerKey)),
                        ),
                    sortOrder = null,
                ),
        ).flatMap { result ->
            val distinctResults = result.distinct()
            if (distinctResults.size != 1) {
                Mono.error(
                    IllegalStateException("Found ${distinctResults.size} entries (key=${headerKey.key}). Should be only one"),
                )
            } else {
                Mono.just(distinctResults.first())
            }
        }
}
