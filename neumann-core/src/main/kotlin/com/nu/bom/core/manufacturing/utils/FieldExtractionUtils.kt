package com.nu.bom.core.manufacturing.utils

import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Parents
import com.nu.bom.core.manufacturing.configurablefields.service.DynamicFieldsHandler
import com.nu.bom.core.manufacturing.configurablefields.service.StaticFieldsService
import com.nu.bom.core.manufacturing.entities.BomNodeReference
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.EntityProvided
import com.nu.bom.core.manufacturing.service.CalculationRequest
import com.nu.bom.core.manufacturing.service.Field
import com.nu.bom.core.manufacturing.service.FieldKey
import com.nu.bom.core.manufacturing.service.FieldType
import com.nu.bom.core.manufacturing.service.InputKey
import com.nu.bom.core.manufacturing.service.MatchKey
import com.nu.bom.core.manufacturing.service.RelType
import com.nu.bom.core.manufacturing.service.VersionedResult
import com.nu.bom.core.manufacturing.service.behaviour.DynamicBehaviour
import com.nu.bom.core.manufacturing.service.calculator.DynamicBehaviourCreationResult
import com.nu.bom.core.manufacturing.service.calculator.ManufacturingTreeChangeResult
import com.nu.bom.core.manufacturing.service.virtualfield.VirtualFieldProviderRegistry
import com.nu.bom.core.manufacturing.utils.FieldExtractionUtilsHelper.createFieldObject
import com.nu.bom.core.manufacturing.utils.dependencies.ChildLoadingTriggerDependency
import com.nu.bom.core.manufacturing.utils.dependencies.CurrencyDependencyProvider
import com.nu.bom.core.manufacturing.utils.dependencies.DataSourceDependency
import com.nu.bom.core.manufacturing.utils.dependencies.EntityLinkFieldDependencyProvider
import com.nu.bom.core.manufacturing.utils.dependencies.EntityProviderDependencyProvider
import com.nu.bom.core.manufacturing.utils.dependencies.GroupDependencyProvider
import com.nu.bom.core.manufacturing.utils.dependencies.ParameterDependencyProvider
import com.nu.bom.core.manufacturing.utils.dependencies.RelationalLinkDependencyProvider
import com.nu.bom.core.manufacturing.utils.dependencies.SpecialLinkDependencyProvider
import com.nu.bom.core.manufacturing.utils.dependencies.UnitConversionDependencyProvider
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.findDuplicates
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class FieldExtractionUtils(
    private val entityManager: EntityManager,
    private val entityModelService: EntityModelService,
    private val dynamicFieldsServices: List<DynamicFieldsHandler>,
    private val staticFieldsService: StaticFieldsService,
    private val virtualFieldProviderRegistry: VirtualFieldProviderRegistry,
) {
    companion object {
        private val logger = LoggerFactory.getLogger(FieldExtractionUtils::class.java)
        const val FIELD_TYPE_CHECK = true
        const val RATE_FIELD_NAME = "exchangeRate"
        const val EXCHANGE_RATES_FIELD_NAME = "exchangeRates"
        const val BASE_CURRENCY_FIELD_NAME = "baseCurrency"
        const val MASTERDATA_CURRENCY_FIELD_NAME = "masterdataBaseCurrency"
        const val ORIGINAL_BASE_CURRENCY = "originalBaseCurrency"
    }

    val dependencyProviders =
        listOf(
            ParameterDependencyProvider(),
            SpecialLinkDependencyProvider(),
            RelationalLinkDependencyProvider(),
            CurrencyDependencyProvider(),
            UnitConversionDependencyProvider(),
            EntityProviderDependencyProvider(),
            GroupDependencyProvider(),
            ChildLoadingTriggerDependency(),
            DataSourceDependency(),
            EntityLinkFieldDependencyProvider(),
        )

    fun extractFieldsForRequest(request: CalculationRequest): List<Field> = extractFields(request.entity, request = request)

    fun extractFieldsForConfigBehaviour(
        entityForFieldInjection: ManufacturingEntity,
        executionContext: ManufacturingEntity,
        request: CalculationRequest,
        dynamicFieldModels: Map<String, FieldModel>,
    ): List<Field> {
        val newFields =
            createFieldsFromFieldModel(
                request,
                dynamicFieldModels,
                executionContext,
                entityForFieldInjection,
            )
        return filterExtensionFields(newFields, entityForFieldInjection.getFieldsAsList())
    }

    fun extractFieldsForDynamicBehaviour(
        behaviourEntity: ManufacturingEntity,
        behaviourHostEntity: ManufacturingEntity,
        request: CalculationRequest,
    ): List<Field> {
        val extractFields =
            extractFieldsRecursivly(
                behaviourEntity,
                fieldHostingEntity = behaviourHostEntity,
                isBaseEntity = false,
                request = request,
            )
        return filterExtensionFields(extractFields, behaviourHostEntity.getFieldsAsList())
    }

    fun addFieldsForDynamicBehaviour(
        behaviourHostEntity: ManufacturingEntity,
        newFields: List<Field>,
        request: CalculationRequest,
    ) {
        val currentRequest = request.withParameterRecalculation(setOf(behaviourHostEntity.entityId))
        setupAsBaseEntity(
            newFields.toMutableList(),
            currentRequest,
            behaviourHostEntity,
            behaviourHostEntity,
            behaviourHostEntity.getFieldsAsList(),
            true,
        )
        DependencyCreationUtils.setCreatedByInfoAndDependencies(
            null,
            behaviourHostEntity.getFieldsAsList(),
            behaviourHostEntity,
        )

        propagateChildDeps(behaviourHostEntity, newFields)
        // if a dynamic behaviour created children in a previous calculation run
        // the fields of the children are processed before the dynamic behaviour can
        // add the fields to the host entity
        // because of that the parent links are recreated
        updateParentInputs(behaviourHostEntity, request)
    }

    private fun updateParentInputs(
        behaviourHostEntity: ManufacturingEntity,
        request: CalculationRequest,
    ) {
        behaviourHostEntity.children.forEach { child ->
            child.getFieldsAsList().forEach { field ->
                val newInputs =
                    field.model.parameters
                        .ifEmpty {
                            listOfNotNull(field.model.annotatedDependency)
                        }.filter {
                            it.dependencyAnnotation is Parent ||
                                it.dependencyAnnotation is Parents
                        }.flatMap {
                            DependencyCreationUtils.createDependency(it, field, child, request)
                        }

                // Virtual fields have inputs added by providers, not via model.
                // They are not handled by this function.
                // This condition protects them from having their inputs removed.
                if (newInputs.isNotEmpty()) {
                    val existingParentInputs =
                        field.inputs.filter {
                            it.relType == RelType.PARENTS || it.relType == RelType.PARENT
                        }

                    existingParentInputs.forEach { it.removeSuccessor(field) }
                    field.inputs.removeAll(existingParentInputs)
                    field.addParameters(newInputs)
                }
            }
        }
    }

    /**
     * Usually this is not necessary as children inherit child deps from their parent.
     *
     * However, in the case of dynamic behaviours or child dependencies that search from an ancestor,
     * child dependencies may be added after child creation, so we have to do inherit the new child deps.
     */
    private fun propagateChildDeps(
        root: ManufacturingEntity,
        newFields: List<Field>,
    ) {
        newFields
            .flatMap { field ->
                field.inputs
                    .filter { it.relType == RelType.CHILDREN && it.childDependency.recursive }
                    .map { it to field }
            }.takeUnless { it.isEmpty() }
            ?.let { deps ->
                root.children.forEach { child -> propagateChildDepsRec(child, deps) }
            }
    }

    private fun propagateChildDepsRec(
        entity: ManufacturingEntity,
        recursiveDeps: List<Pair<InputKey, Field>>,
    ) {
        if (entity !is BomNodeReference) {
            entity.childDeps += recursiveDeps
            addAncestralChildCreationDependencies(entity, recursiveDeps)
            entity.children.forEach { propagateChildDepsRec(it, recursiveDeps) }
        }
    }

    private fun extractFields(
        entity: ManufacturingEntity,
        parent: ManufacturingEntity? = null,
        request: CalculationRequest,
        parentFields: List<Field>? = null,
    ): List<Field> {
        val result = extractFieldsRecursivly(entity, request = request, parent = parent, parentFields = parentFields)
        val newFields = collectFieldsForManuallyAddedEntities(entity, emptyList(), request = request)
        return result + newFields
    }

    private fun extractFieldsRecursivly(
        entity: ManufacturingEntity,
        parent: ManufacturingEntity? = null,
        fieldHostingEntity: ManufacturingEntity = entity,
        isBaseEntity: Boolean = true,
        request: CalculationRequest,
        parentFields: List<Field>? = null,
    ): List<Field> {
        entity.ensureParent(parent)

        if (request.skipExtraction(entity, fieldHostingEntity)) {
            return skipExtraction(isBaseEntity, request, entity, fieldHostingEntity, parentFields)
        }

        if (entity.extensionEntities.isEmpty()) {
            addExtensions(entity)
        }

        entity.updateVersion(fieldHostingEntity.version)
        entity.calculationContext = request.context
        entity.calculationServices = request.calculationContextServiceImplReach

        val extensionFields = getExtensionFields(entity, parent, request, fieldHostingEntity)
        val configFields = getConfigFields(entity, request, fieldHostingEntity)

        val entityModel = entityModelService.getEntityModel(entity::class, validateAsBaseEntity = isBaseEntity)
        val functionFields = createFieldsFromFieldModel(request, entityModel, entity, fieldHostingEntity)
        val filteredBaseFields = filterExtensionFields(extensionFields, functionFields)

        val myFields = (functionFields + filteredBaseFields + configFields)

        val myFieldsAndVirtualFields =
            if (isBaseEntity) {
                val virtualFields = getVirtualFields(request, entityModel, fieldHostingEntity, myFields)
                val allFields = myFields + virtualFields

                val fieldsAfterSetup = setupAsBaseEntity(allFields, request, entity, fieldHostingEntity)
                DependencyCreationUtils.setCreatedByInfoAndDependencies(parentFields, allFields, entity)
                fieldsAfterSetup
            } else {
                myFields
            }

        val childFields = getChildFields(entity, request, myFieldsAndVirtualFields)
        return childFields + myFieldsAndVirtualFields
    }

    fun getVirtualFields(
        request: CalculationRequest,
        entityModel: EntityModel,
        entity: ManufacturingEntity,
        newFields: List<Field>,
        existingFields: List<Field> = listOf(),
    ): List<Field> {
        val allFieldModels = mergeFieldLists(entity, existingFields, newFields).associateBy({ it.model.name }, { it.model })

        val newVirtualFields =
            virtualFieldProviderRegistry.providers.mapNotNull { provider ->
                provider.createVirtualFieldIfNotExisting(
                    allFieldModels,
                    request,
                    entityModel,
                    entity,
                    existingFields,
                )
            }
        return newVirtualFields
    }

    private fun getConfigFields(
        entity: ManufacturingEntity,
        request: CalculationRequest,
        fieldHostingEntity: ManufacturingEntity,
    ): List<Field> {
        val fieldModelConfigs = staticFieldsService.getStaticFieldModels()
        return fieldModelConfigs.flatMap { fieldModelConfig ->
            if (fieldModelConfig.hasFieldModel(entity::class)) {
                val entityFieldModels = fieldModelConfig[entity::class].associateBy { fieldModel -> fieldModel.name }
                val executionContext = fieldModelConfig.executionContext

                createFieldsFromFieldModel(request, entityFieldModels, executionContext.toManufacturingEntity(), fieldHostingEntity)
            } else {
                listOf()
            }
        }
    }

    private fun addExtensions(entity: ManufacturingEntity) {
        val extensions = entityManager.getExtensions(entity)
        if (extensions.isNotEmpty()) {
            entity.extensionEntities.addAll(extensions)
        }
        entity.extensionEntities.addAll(entity.behaviours)
    }

    private fun skipExtraction(
        isBaseEntity: Boolean,
        request: CalculationRequest,
        entity: ManufacturingEntity,
        original: ManufacturingEntity,
        parentFields: List<Field>?,
    ): List<Field> =
        if (isBaseEntity && request.parameterRecalculation(entity) && request.entityInContext(original)) {
            logger.info("parameters recalculated for ${entity.toHumanReadableName()}")
            recalculateParameters(request, entity, original, parentFields)
        } else {
            logger.info("extraction skipped ${entity.toHumanReadableName()}")
            listOf()
        }

    private fun setupAsBaseEntity(
        newFields: List<Field>,
        request: CalculationRequest,
        entity: ManufacturingEntity,
        original: ManufacturingEntity,
        existingFields: List<Field> = listOf(),
        keepRelDeps: Boolean = false,
    ): List<Field> {
        entity.setFieldsAndClearCreatedBy(mergeFieldLists(entity, existingFields, newFields))
        addDependencies(newFields, entity, original, request)
        updateLinkedFieldsOfDependencies(newFields, request, original)
        addDepenencyInfoToEntities(entity, newFields, existingFields, keepRelDeps)

        // The DynamicFieldProvider creates Fields outside of this entity, so the addDepenencyInfoToEntities(...)
        // would add incorrect child/sibling dependencies. By luck, these fields won't have any inputs, but it's better
        // to safe than sorry. If in the future, there will be dynamic fields added to a different entity, that need
        // proper sibling/child dependency support, we need to extend addDepenencyInfoToEntities(...)
        val dynamicFields = virtualFieldProviderRegistry.providers.flatMap { it.postProcessVirtualFields(request, entity) }

        if (FIELD_TYPE_CHECK) {
            newFields.forEach(Field::checkFieldTypeUsage)
        }

        return mergeFieldLists(entity, newFields, dynamicFields)
    }

    private fun updateLinkedFieldsOfDependencies(
        newFields: List<Field>,
        request: CalculationRequest,
        entityToUpdateDependenciesWithoutLinkedField: ManufacturingEntity,
    ) {
        newFields.forEach { newField ->
            // check if we have a dependency to the new field and if we can update this dependency
            val fieldsWithDependencyToUpdate =
                request
                    .fieldsWithDependenciesWithoutLinkedField
                    .getFieldsWithDependenciesTo(
                        entityToUpdateDependenciesWithoutLinkedField.entityId,
                        newField.name,
                    )
            fieldsWithDependencyToUpdate?.forEach { (field, inputKey) ->
                // set dependency to new field
                field.inputs.remove(inputKey)
                if (inputKey.relType != RelType.CHILDREN || field.entity.children.contains(newField.entity)) {
                    field.addParameter(inputKey.copy(linkedField = newField))
                }
            }
        }
    }

    private fun updateLinkedFields(
        entity: ManufacturingEntity,
        existingFields: List<Field>,
    ) {
        val fieldsAndDependenciesForUpdates =
            existingFields.flatMap { field ->
                field.inputs
                    .filter { dep ->
                        dep.relType == RelType.SAME && dep.linkedField == null
                    }.map {
                        Pair(field, it)
                    }
            }

        fieldsAndDependenciesForUpdates.forEach { (field, dep) ->
            val sourceField = entity.getFieldObject(dep.name)
            if (sourceField != null) {
                val updatedDep = dep.copy(linkedField = sourceField)
                field.inputs.remove(dep)
                field.inputs.add(updatedDep)
            }
        }
    }

    private fun recalculateParameters(
        request: CalculationRequest,
        entity: ManufacturingEntity,
        original: ManufacturingEntity,
        parentFields: List<Field>?,
    ): List<Field> {
        val entityFields = entity.getFieldsAsList()

        removeDependencies(entityFields)
        addDependencies(entityFields, entity, original, request)

        addDepenencyInfoToEntities(entity, entityFields, listOf(), false)

        val childFields = getChildFields(entity, request, parentFields = entityFields)

        DependencyCreationUtils.setCreatedByInfoAndDependencies(parentFields, entityFields, entity)

        request.prepareNewFields(entityFields)

        return childFields
    }

    private fun addDepenencyInfoToEntities(
        entity: ManufacturingEntity,
        entityFields: List<Field>,
        existingFields: List<Field>,
        keepRelDeps: Boolean,
    ) {
        updateSiblingDependenciesOnEntity(entity, entityFields, keepRelDeps)
        updateChildDependenciesOnEntity(entity, entityFields, keepRelDeps)
        updateLinkedFields(entity, existingFields)
    }

    private fun updateSiblingDependenciesOnEntity(
        entity: ManufacturingEntity,
        fields: List<Field>,
        keepExisting: Boolean,
    ) {
        val mySiblingDeps =
            fields.filter { field ->
                field.inputs.any { dep ->
                    dep.relType == RelType.SIBLING
                }
            }
        entity.siblingDeps = (if (keepExisting) entity.siblingDeps else emptyList()) + mySiblingDeps
    }

    private fun updateChildDependenciesOnEntity(
        entity: ManufacturingEntity,
        fields: List<Field>,
        keepExisting: Boolean,
    ) {
        val childDeps =
            fields.flatMap { field ->
                field.inputs
                    .filter { dep ->
                        dep.relType == RelType.CHILDREN
                    }.map {
                        it to field
                    }.toList()
            }

        val (myChildDeps, childDepsFromAncestors) =
            childDeps.partition { (dep, _) ->
                dep.childDependency.startFromFirstAncestor == null
            }

        val parentChildDeps =
            entity.parents
                .filterNot { it is BomNodeReference }
                // Do not recursively propagate child deps over manufacturings
                .flatMap { parentEntity ->
                    parentEntity.childDeps.filter { (childDep, _) ->
                        childDep.childDependency.recursive
                        //  && entity.getEntityTypeAnnotation() == it.first.stopAt
                    }
                }

        entity.childDeps = (if (keepExisting) entity.childDeps else emptyList()) + myChildDeps + parentChildDeps
        updateAncestralChildDependencies(entity, childDepsFromAncestors)
    }

    // These dependencies start their search from an ancestor.
    // Find the ancestor and propagate recursively.
    fun updateAncestralChildDependencies(
        entity: ManufacturingEntity,
        childDepsFromAncestors: List<Pair<InputKey, Field>>,
    ) {
        childDepsFromAncestors
            .groupBy { (dep, _) -> dep.childDependency.startFromFirstAncestor!! }
            .forEach { (entityType, childDeps) ->
                entity.findParentWithEntityType(listOf(entityType))?.let { ancestor ->
                    ancestor.childDeps += childDeps
                    addAncestralChildCreationDependencies(ancestor, childDeps)

                    childDeps
                        .filter { (dep, _) -> dep.childDependency.recursive }
                        .takeUnless { it.isEmpty() }
                        ?.let { propagateChildDepsRec(ancestor, it) }
                }
            }
    }

    private fun addAncestralChildCreationDependencies(
        ancestor: ManufacturingEntity,
        dependencies: List<Pair<InputKey, Field>>,
    ) {
        val creationFields = ancestor.getFieldsAsList().filter { it.model.fieldType.isCreationField() }
        dependencies
            .groupBy { (dep, _) -> dep.entityType }
            .forEach { (entityType, deps) ->
                val filteredCreationFields = creationFields.filter { it.model.fieldCanCreateType(entityType) }

                deps.forEach { (dep, field) ->
                    filteredCreationFields.forEach { creationField ->
                        field.addCreationDependency(dep, creationField)
                    }
                    ancestor.children
                        .filter { it.getEntityType() == entityType }
                        .flatMap { child ->
                            child.getFieldsAsList().filter { it.model.fieldType == FieldType.BehaviourCreation }
                        }.forEach { field.addCreationDependency(dep, it) }
                }
            }
    }

    private fun collectFieldsForManuallyAddedEntities(
        entity: ManufacturingEntity,
        stepCreatorEntityStack: List<Pair<ManufacturingEntity, Field>>,
        root: Boolean = true,
        request: CalculationRequest,
    ): List<Field> {
        var result = emptyList<Field>()
        val entityIsNotCreatedByFieldOrIsMocked = entity.createdBy == null || entity.createdByMocked
        if (entityIsNotCreatedByFieldOrIsMocked &&
            !root &&
            entity.getEntityTypeAnnotation() == Entities.MANUFACTURING_STEP
        ) {
            logger.debug("manually created entity: {}", entity.toHumanReadableName())
            val mostRecentParent = stepCreatorEntityStack.lastOrNull()
            if (mostRecentParent != null) {
                val newMockField = createField(mostRecentParent.first, entity, request)
                mostRecentParent.first.addFieldObject(newMockField)
                EntityOrdering(mostRecentParent.second, entity, newMockField).insertParameter()
                result = listOf(newMockField)
                entity.createdBy = mostRecentParent.second.calcKey
                entity.createdByMocked = true
                entity.createdByField = mostRecentParent.second
                addCreatedByDependenciesForManuallyCreatedEntities(mostRecentParent.second, entity)
            }
        }
        val orderedEntityCreatorField =
            entity.getFieldsAsList().find { it.model.fieldType == FieldType.OrderedEntityCreation }
        val newStack =
            if (orderedEntityCreatorField != null) {
                stepCreatorEntityStack + (entity to orderedEntityCreatorField)
            } else {
                stepCreatorEntityStack
            }
        val childFields =
            entity.children
                .flatMap { child ->
                    /**
                     * when stepping over a BomEntry we reset the stack so we do not associate generated entities to parents
                     * beyond bomentry borders
                     */
                    val stackForChild =
                        if (child.getEntityTypeAnnotation() == Entities.BOM_ENTRY) {
                            emptyList()
                        } else {
                            newStack
                        }
                    collectFieldsForManuallyAddedEntities(child, stackForChild, false, request)
                }
        return mergeFieldLists(entity, childFields, result)
    }

    private fun addCreatedByDependenciesForManuallyCreatedEntities(
        creatorField: Field,
        entity: ManufacturingEntity,
    ) {
        DependencyCreationUtils.addCreatedByDependencies(creatorField.calcKey, creatorField, entity.getFieldsAsList())
        entity.children.forEach { addCreatedByDependenciesForManuallyCreatedEntities(creatorField, it) }
    }

    data class EntityOrdering(
        val field: Field,
        val entityToPosition: ManufacturingEntity,
        val mockFieldToPosition: Field,
    ) {
        private val orderedEntities =
            field.inputs.mapNotNull {
                val result = it.linkedField?.getCurrentVersionedResult()?.result
                if (result != null && result is EntityProvided) {
                    it to result.res
                } else {
                    null
                }
            }

        private fun findPosition(entity: ManufacturingEntity): InputKey? =
            orderedEntities
                .find {
                    entity.entityId == it.second
                }?.first

        fun insertParameter() {
            val insertParameter = mockFieldToPosition.toInputKey(RelType.SAME)
            var current = entityToPosition
            var run = true
            do {
                val newCurrent = current.parents.firstOrNull()
                if (newCurrent != null) {
                    val position = findPosition(newCurrent)
                    if (position != null) {
                        val index = field.inputs.indexOf(position)
                        field.addParameterAt(index, insertParameter)
                        return
                    }
                    current = newCurrent
                } else {
                    run = false
                }
            } while (run)
            field.inputs.add(insertParameter)
        }
    }

    private fun createField(
        parent: ManufacturingEntity,
        entity: ManufacturingEntity,
        request: CalculationRequest,
    ): Field {
        val model = createMockFieldModel(entity)
        val key = parent.createKeyFor(model.name, model.returnTypeCouldBeNonFieldResult)
        val oldResult = request.getOldResult(key.fieldIdKey())
        return Field(
            model = model,
            calcKey = key,
            entity = parent,
            executionContext = parent,
            oldResult =
                VersionedResult(
                    result = EntityProvided.withEntity(entity),
                    version = oldResult?.version ?: entity.version,
                    newVersion = oldResult?.newVersion ?: entity.version,
                ),
            initialResult = null,
            oldInput = null,
        )
    }

    private fun createMockFieldModel(entity: ManufacturingEntity): FieldModel =
        FieldModel(
            name = "manual-entity-creator-" + entity.entityId,
            FieldType.ManualEntityCreator,
            returnTypeCouldBeNonFieldResult = entity.javaClass.canonicalName,
            function = this::mockCallable,
            childrenOrder = listOf(),
            modelInput = false,
            parameters = emptyList(),
            isNocalcField = false,
        )

    private fun mockCallable(): Unit = throw IllegalAccessException("no-no")

    private fun createFieldsFromFieldModel(
        request: CalculationRequest,
        entityModel: EntityModel,
        executionContext: ManufacturingEntity,
        fieldHostingEntity: ManufacturingEntity,
    ): List<Field> = createFieldsFromFieldModel(request, entityModel.fields, executionContext, fieldHostingEntity)

    private fun createFieldsFromFieldModel(
        request: CalculationRequest,
        fieldModels: Map<String, FieldModel>,
        executionContext: ManufacturingEntity,
        fieldHostingEntity: ManufacturingEntity,
    ): List<Field> =
        fieldModels
            .asSequence()
            .filter { request.executeEntityCreation || !it.value.fieldType.isCreationField() }
            .map { createFieldObject(request, it.value, executionContext, fieldHostingEntity) }
            .toList()

    private fun filterExtensionFields(
        extensionFields: List<Field>,
        functionFields: List<Field>,
    ): List<Field> {
        val functionCalcKeys = functionFields.map { inh -> inh.toMatchKey() }.toSet()
        val filteredExtensionFields =
            extensionFields.filter { ext ->
                !functionCalcKeys.contains(ext.toMatchKey())
            }
        val (interfaceDeclaringFields, normalFields) =
            filteredExtensionFields.partition {
                it.model.isInterfaceField
            }

        val duplicateFields = normalFields.groupBy { it.model.name }.filter { it.value.size > 1 }
        if (duplicateFields.isNotEmpty()) {
            val msg =
                "There are duplicate extension fields:\n" +
                    duplicateFields.entries.joinToString(separator = "\n") { fields ->
                        "\tfield name: '${fields.key}' -\n" +
                            fields.value.joinToString(separator = "\n") {
                                "\t\ton entity '${it.entity.javaClass.simpleName}' " +
                                    "from the '${it.executionContext.javaClass.simpleName}' extension"
                            }
                    }
            error(msg)
        }

        val missingInterfaceImplementingFields =
            interfaceDeclaringFields.map { it.model.name } subtract
                normalFields.map { it.model.name }.toSet()
        if (missingInterfaceImplementingFields.isNotEmpty()) {
            val msg = "There are some missing interface extension fields: $missingInterfaceImplementingFields"
            error(msg)
        }

        return normalFields
    }

    private fun getChildFields(
        entity: ManufacturingEntity,
        request: CalculationRequest,
        parentFields: List<Field>?,
    ): List<Field> =
        entity.children.flatMap {
            if (request.entityInContext(it)) {
                it.updateVersion(entity.version)
            }
            extractFieldsRecursivly(it, entity, request = request, parentFields = parentFields)
        }

    private fun getExtensionFields(
        entity: ManufacturingEntity,
        parent: ManufacturingEntity?,
        request: CalculationRequest,
        original: ManufacturingEntity,
    ): List<Field> {
        val extensionFields: List<Field> =
            if (entity.extensionEntities.isEmpty()) {
                listOf()
            } else {
                entity.extensionEntities.flatMap { extension ->
                    extractFieldsRecursivly(extension, parent, fieldHostingEntity = original, isBaseEntity = false, request = request)
                }
            }

        val baseFieldlist =
            if (entity.extends != null) {
                val baseFields =
                    extractFieldsRecursivly(
                        entity.extends!!,
                        parent,
                        fieldHostingEntity = original,
                        isBaseEntity = false,
                        request = request,
                    )
                if (extensionFields.isNotEmpty()) {
                    filterExtensionFields(baseFields, extensionFields)
                } else {
                    baseFields
                }
            } else {
                listOf()
            }

        return extensionFields + baseFieldlist
    }

    fun extractGeneratedFields(
        calculationResults: List<ManufacturingTreeChangeResult>,
        request: CalculationRequest,
    ): List<Field> {
        val entityIds =
            calculationResults
                .flatMap { calculationResult ->
                    calculationResult.childDependencyRecalculationNeeded()
                }.toSet()

        val currentRequest = request.withParameterRecalculation(entityIds)

        return calculationResults.flatMap { manufacturingTreeChangeResult ->
            manufacturingTreeChangeResult.forAllNewEntities { parent, entity ->
                extractFields(
                    entity = entity,
                    parent = parent,
                    request = currentRequest,
                    parentFields = parent.getFieldsAsList(),
                )
            }
        }
    }

    private fun dynamicFieldHandler(dynamicBehaviour: DynamicBehaviour): DynamicFieldsHandler? {
        return dynamicFieldsServices.firstOrNull { it.canHandleDynamicBehaviour(dynamicBehaviour) }
    }

    fun extractGeneratedFieldsForDynamicBehaviour(
        calculationResults: List<DynamicBehaviourCreationResult>,
        request: CalculationRequest,
    ): Map<ManufacturingEntity, List<Field>> {
        val newDynamicFields = mutableMapOf<ManufacturingEntity, List<Field>>()
        calculationResults.forEach {
            val (entity, newBehaviourFields) =
                it.dynamicBehaviour.getFields(
                    this,
                    entityManager,
                    dynamicFieldHandler(it.dynamicBehaviour),
                    request,
                )
            if (newBehaviourFields.isNotEmpty()) {
                newDynamicFields[entity] = mergeFieldLists(entity, newDynamicFields[entity] ?: emptyList(), newBehaviourFields)
            }
        }

        // New dynamic fields might need virtual fields which are not yet present.
        // Evaluate, if new virtual fields need to be created
        newDynamicFields.forEach { (entity, newFields) ->
            val existingFields = entity.getFieldsAsList()
            val entityModel = entityModelService.getEntityModel(entity::class, validateAsBaseEntity = false)
            val newVirtualFields = getVirtualFields(request, entityModel, entity, newFields, existingFields)
            if (newVirtualFields.isNotEmpty()) {
                newDynamicFields[entity] = mergeFieldLists(entity, newDynamicFields[entity] ?: emptyList(), newVirtualFields)
            }
        }

        return newDynamicFields
    }

    private fun mergeFieldLists(
        entity: ManufacturingEntity,
        vararg fieldLists: List<Field>,
    ): List<Field> {
        val duplicates = fieldLists.flatMap { fields -> fields.map { field -> field.model.name } }.findDuplicates()
        if (duplicates.isNotEmpty()) {
            logger.warn("There are duplicated field definitions that are combined in the entity: '${entity.name}: $duplicates")
        }
        return fieldLists.fold(emptyList()) { acc, fields -> acc + fields }
    }

    private fun removeDependencies(fields: List<Field>) {
        fields.forEach { it.removeDependencies() }
    }

    private fun addDependencies(
        newFields: List<Field>,
        entity: ManufacturingEntity,
        original: ManufacturingEntity,
        request: CalculationRequest,
    ) {
        dependencyProviders.forEach {
            it.addDependencies(entity = entity, original = original, newFields = newFields, request = request)
        }
    }
}

object FieldExtractionUtilsHelper {
    fun createFieldObject(
        request: CalculationRequest,
        model: FieldModel,
        executionContext: ManufacturingEntity,
        fieldHostingEntity: ManufacturingEntity,
    ): Field {
        val calcKey =
            FieldKey(
                name = model.name,
                entityId = fieldHostingEntity.entityId,
                entityRef = fieldHostingEntity.entityRefForFieldKey(),
                entityType = fieldHostingEntity.getEntityType(),
                type = model.returnTypeCouldBeNonFieldResult,
                version = fieldHostingEntity.version,
            )
        val matchKey =
            MatchKey(
                name = model.name,
                entityType = fieldHostingEntity.getEntityType(),
                entityId = fieldHostingEntity.entityId,
            )

        val key = calcKey.fieldIdKey()
        val oldResult = request.getOldResult(key)
        return model.create(
            calcKey = calcKey,
            entity = fieldHostingEntity,
            oldInput = request.getInput(key),
            oldResult = oldResult,
            initialResult = fieldHostingEntity.getMatchingInitialField(matchKey, model.modelInput),
            executionContext = executionContext,
        )
    }
}
