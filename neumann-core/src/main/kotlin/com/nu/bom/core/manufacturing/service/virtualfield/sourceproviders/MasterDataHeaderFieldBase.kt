package com.nu.bom.core.manufacturing.service.virtualfield.sourceproviders

import com.nu.bom.core.manufacturing.fieldTypes.MdHeaderTypeAndHeaderKeyFieldData
import com.nu.bom.core.manufacturing.fieldTypes.masterdata.HeaderNaturalKey
import com.nu.bom.core.service.masterdata.MdDetailCrudService
import com.nu.bom.core.user.AccessCheck
import com.nu.masterdata.dto.v1.detail.table.BuiltinLovFilterDto
import com.nu.masterdata.dto.v1.detail.table.DetailQueryDto
import com.nu.masterdata.dto.v1.header.HeaderDto
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import reactor.core.publisher.Flux

abstract class MasterDataHeaderFieldBase(
    private val mdDetailCrudService: MdDetailCrudService,
) : SourceProvider {
    protected fun fetchAllHeaders(
        accessCheck: AccessCheck,
        allHeaderKeys: List<MdHeaderTypeAndHeaderKeyFieldData>,
    ): Flux<HeaderDto> {
        val groupedHeaders = allHeaderKeys.groupBy { it.headerTypeKey }
        val searchResults =
            groupedHeaders.map { (headerTypeKey, headers) ->
                val query = buildQuery(headers.map { it.headerKey })
                search(accessCheck, headerTypeKey, query)
            }

        val merged = Flux.merge(searchResults)
        return merged
    }

    private fun search(
        accessCheck: AccessCheck,
        headerTypeKey: String,
        query: DetailQueryDto,
    ): Flux<HeaderDto> =
        mdDetailCrudService
            .postAllDetailEntries(
                accessCheck,
                SimpleKeyDto(headerTypeKey),
                query,
            ).map { resultList ->
                resultList.content.map { headerAndDetail ->
                    val header = headerAndDetail.headerDto
                    HeaderDto(
                        key = header.key,
                        name = header.name,
                        headerTypeKey = header.headerTypeKey,
                        active = header.active,
                        detailValueSchema = header.detailValueSchema,
                        classifications = header.classifications?.map { (clType, clList) -> clType to clList.map { it.key } }?.toMap(),
                        classificationFieldValues = header.classificationFieldValues,
                    )
                }.distinct()
            }
            .flatMapIterable { list -> list }

    private fun buildQuery(headerKeys: List<HeaderNaturalKey>): DetailQueryDto =
        DetailQueryDto(
            filters =
                mapOf(
                    SimpleKeyDto("_BUILTIN_headerKey") to
                        headerKeys.map { headerKey ->
                            BuiltinLovFilterDto(
                                equals = headerKey.toDto(),
                            )
                        },
                ),
            classificationFieldFilters = emptyMap(),
            classificationFilters = emptyMap(),
            showStateOf = null,
            showInactive = false,
            sortOrder = emptyList(),
        )
}
