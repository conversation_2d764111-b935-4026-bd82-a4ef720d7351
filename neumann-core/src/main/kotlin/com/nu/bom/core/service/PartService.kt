package com.nu.bom.core.service

import com.google.common.annotations.VisibleForTesting
import com.nu.bom.core.exception.readable.PartNotFoundException
import com.nu.bom.core.manufacturing.entities.Attachment
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.ThreeDbVersionedPart
import com.nu.bom.core.messagebus.MessageBusService
import com.nu.bom.core.messagebus.MessageBusServiceConfig.Companion.PART_CHANGES_ROUTE
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.Part
import com.nu.bom.core.model.PartId
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.QPart
import com.nu.bom.core.repository.PartRepository
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.exceptionOnEmpty
import com.nu.bom.core.utils.subscribeWithContextCapture
import com.querydsl.core.types.Predicate
import com.tset.core.module.bom.EventSourcingModule
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.util.Locale
import java.util.Optional

@Service
class PartService(
    private val partRepository: PartRepository,
    private val messageBusService: MessageBusService,
    private val eventSourcingModule: EventSourcingModule,
) {
    // TODO: check feasibility of getting currentProject into accessCheck

    // TODO: check & cleanup methods that are only used by tests, or not used anymore by FE

    companion object {
        private val LOG = LoggerFactory.getLogger(PartService::class.java)

        private val qPart = QPart("part")
    }

    // This is only used in tests, should be moved to the test package
    fun newPart(
        designation: String,
        number: String,
        accessCheck: AccessCheck,
        projectId: ProjectId,
        projectKey: String = "PROJECTKEY",
    ): Mono<Part> {
        return savePart(accessCheck, designation, number, emptyList(), projectId, false, mapOf())
    }

    fun getOrCreatePart(
        designation: String,
        number: String,
        images: List<String>?,
        accessCheck: AccessCheck,
        projectId: ProjectId,
        projectKey: String,
    ): Mono<Part> =
        partRepository
            .findByNumberAndProjectIdAndAccountIdAndDeletedIsFalse(
                number = number,
                projectId = projectId,
                accountId = accessCheck.toAccountId(),
            ).next()
            .switchIfEmpty(
                savePart(accessCheck, designation, number, images, projectId, attachmentToVersionedPart = mapOf()),
            )

    private fun savePart(
        accessCheck: AccessCheck,
        designation: String,
        number: String,
        images: List<String>?,
        projectId: ProjectId,
        hasMainImage: Boolean = false,
        attachmentToVersionedPart: Map<String, ThreeDbVersionedPart>,
    ) = save(
        accessCheck,
        Part(
            designation = designation,
            number = number,
            images = images ?: emptyList(),
            projectId = projectId,
            hasMainImage = hasMainImage,
            attachmentToVersionedPart = attachmentToVersionedPart,
        ).apply { accountId = accessCheck.toAccountId() },
    )

    fun createPart(
        accessCheck: AccessCheck,
        designation: String,
        number: String,
        images: List<String>,
        projectId: ProjectId,
        projectKey: String,
        hasMainImage: Boolean = false,
        attachmentToVersionedPart: Map<String, ThreeDbVersionedPart>,
    ): Mono<Part> {
        return savePart(accessCheck, designation, number, images, projectId, hasMainImage, attachmentToVersionedPart)
    }

    fun getWithoutAccessCheck(partId: PartId): Mono<Part> = partRepository.findBy_idAndDeletedIsFalse(id = partId)

    private fun query(
        accessCheck: AccessCheck,
        partId: PartId,
    ): Predicate =
        qPart.accountId
            .eq(accessCheck.toAccountId())
            .and(qPart._id.eq(partId))
            .and(qPart.deleted.isFalse)

    /**
     * Return a Part or Mono.empty() if part is not found.
     */
    fun getPartOrEmpty(
        partId: PartId,
        accessCheck: AccessCheck,
    ): Mono<Part> = partRepository.findOne(query(accessCheck, partId))

    /**
     * @return [true] if part exists, [false] otherwise
     */
    fun existsById(
        partId: PartId,
        accessCheck: AccessCheck,
    ): Mono<Boolean> = partRepository.exists(query(accessCheck, partId))

    /**
     * Return a Part or Mono.error(PART_NOT_FOUND) if part is not found.
     */
    fun getPart(
        partId: PartId,
        accessCheck: AccessCheck,
    ): Mono<Part> =
        getPartOrEmpty(partId, accessCheck)
            .exceptionOnEmpty { PartNotFoundException(partId.toHexString()) }

    fun updatePart(
        part: Part,
        accessCheck: AccessCheck,
    ): Mono<Part> =
        getPartOrEmpty(part._id!!, accessCheck)
            .exceptionOnEmpty { PartNotFoundException(part._id!!.toHexString()) }
            .flatMap {
                save(accessCheck, part)
            }

    private fun getOptionalPart(
        accessCheck: AccessCheck,
        partId: PartId,
    ): Mono<Optional<Part>> =
        getPartOrEmpty(partId, accessCheck)
            .map {
                Optional.of(it)
            }.switchIfEmpty(Mono.just(Optional.empty()))

    private fun getOptionalPart(partId: PartId): Mono<Optional<Part>> =
        partRepository
            .findBy_idAndDeletedIsFalse(id = partId)
            .map {
                Optional.of(it)
            }.switchIfEmpty(Mono.just(Optional.empty()))

    fun findPartOrEmpty(
        designation: String,
        number: String,
        accessCheck: AccessCheck,
        projectId: ProjectId,
    ): Mono<Part> =
        partRepository
            .findByDesignationAndNumberAndProjectIdAndAccountIdAndDeletedIsFalse(
                designation = designation,
                number = number,
                projectId = projectId,
                accountId = accessCheck.toAccountId(),
            ).next()

    @VisibleForTesting
    fun getPartsFiltered(
        filterTerm: String?,
        accessCheck: AccessCheck,
        projectId: ProjectId,
    ): Flux<Part> {
        val isInProject = qPart.projectId.eq(projectId).and(qPart.accountId.eq(accessCheck.toAccountId()))

        return if (filterTerm.isNullOrEmpty()) {
            partRepository.findAll(isInProject)
        } else {
            val nameOrNumberContains =
                qPart.designationNormalized
                    .contains(filterTerm.lowercase(Locale.getDefault()))
                    .or(
                        qPart.number.contains(filterTerm),
                    )
            partRepository.findAll(isInProject.and(nameOrNumberContains))
        }
    }

    fun addPartInfo(
        accessCheck: AccessCheck?,
        snapshot: BomNodeSnapshot,
    ): Mono<BomNodeSnapshot> {
        val baseManufacturing = snapshot.getBaseManufacturing()
        return if (baseManufacturing != null) {
            addPartInfo(baseManufacturing, accessCheck).map { snapshot }
        } else {
            Mono.just(snapshot)
        }
    }

    fun addPartInfo(
        baseManufacturing: BaseManufacturing,
        accessCheck: AccessCheck? = null,
    ): Mono<BaseManufacturing> =
        if (baseManufacturing.partInfo.isPartModelConverted) {
            baseManufacturing.findByEntityType(Entities.PART)?.let { partEntity ->
                val partInfo =
                    Part(
                        designation = (partEntity.getFieldResult("designation") as Text?)?.res ?: "",
                        number = (partEntity.getFieldResult("number") as Text?)?.res ?: "",
                        projectId = null,
                        images =
                            baseManufacturing
                                .getAttachments()
                                .sortedByDescending { attachment ->
                                    (attachment.getFieldResult(Attachment::isMainImage.name) as Bool?)?.res == true
                                }.mapNotNull { attachment -> (attachment.getFieldResult(Attachment::fileId.name) as Text?)?.res },
                        hasMainImage =
                            baseManufacturing
                                .getAttachments()
                                .any { (it.getFieldResult(Attachment::isMainImage.name) as Bool?)?.res == true },
                    )
                baseManufacturing.part = partInfo
            }
            Mono.just(baseManufacturing)
        } else {
            baseManufacturing
                .getOptionalPartId()
                .map { partId ->
                    if (accessCheck == null) {
                        getOptionalPart(partId = partId)
                    } else {
                        getOptionalPart(accessCheck = accessCheck, partId = partId)
                    }
                }.orElse(Mono.just(Optional.empty()))
                .map {
                    it.ifPresent { part ->
                        LOG.debug("loaded part " + (part._id ?: "N/A"))
                        baseManufacturing.part = part
                    }
                    baseManufacturing
                }
        }

    fun delete(
        accessCheck: AccessCheck,
        partId: PartId,
    ): Mono<Void> =
        getPartOrEmpty(accessCheck = accessCheck, partId = partId)
            .flatMap { part ->
                part.deleted = true
                save(accessCheck, part)
            }.doOnSuccess { part ->
                LOG.debug("Deleted part=${part._id!!.toHexString()} in project=${part.projectId?.toHexString()}")
            }.then()

    private fun save(
        accessCheck: AccessCheck,
        part: Part,
    ): Mono<Part> =
        partRepository
            .save(part)
            .doOnSuccess { savedPart ->
                messageBusService
                    .send(savedPart._id!!, PART_CHANGES_ROUTE)
                    .then(
                        eventSourcingModule.publishPartUpdated(
                            accountId = accessCheck.accountId,
                            accountName = accessCheck.accountName,
                            projectId = part.projectId!!.toHexString(),
                            partId = part._id!!.toHexString(),
                        ),
                    ).subscribeWithContextCapture()
            }
}
