package com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel

import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.StandardCalculationValue
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.CalculationOperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.OperationWithStandardCalculationValue
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.SumProdOperation
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.InternalCommercialCalculationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.TopLevelUiCardBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.helper.ColumnCreator
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.CommercialFieldNameAndLookupCreatorHelper
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.CommercialFieldTableHelper
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.CommercialUiCardBuilderHelper
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel.CollapsingHelper.isDirectAddendOfEntryPoint
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableOption
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

@Service
@Profile("!test", "fullCalculationTreeCardBuilderTest")
class FullCalculationTreeCardBuilder : TopLevelUiCardBuilder {
    override val cardIdentifier = "fullCalculationTree"

    private val maxDepth = Int.MAX_VALUE
    private val fieldAndLookupHelper =
        CommercialFieldNameAndLookupCreatorHelper(
            allowedAggregationLevels = setOf(AggregationLevel.MANUFACTURED_MATERIAL, AggregationLevel.SOLD_MATERIAL),
            fallbackAggregationLevel = AggregationLevel.MANUFACTURED_MATERIAL,
        )
    private val cardHelper = CommercialUiCardBuilderHelper(::getEntryPoint, { maxDepth }, fieldAndLookupHelper)
    private val tableHelper = CommercialFieldTableHelper(::getEntryPoint, ::getColumnCreators, ::getIsCollapsed)

    override fun getTitles(vararg configs: InternalCommercialCalculationConfiguration) = cardHelper.getTitles(*configs)

    override fun getKpis(vararg configs: InternalCommercialCalculationConfiguration) = cardHelper.getKpis(*configs)

    override fun getCardFields(vararg configs: InternalCommercialCalculationConfiguration) = null

    override fun getTablesVariations(vararg configs: InternalCommercialCalculationConfiguration) = cardHelper.getTablesVariations(*configs)

    override fun getTableConfig(
        valueType: ValueType,
        tableOption: TableOption,
        procurementType: ManufacturingType,
        vararg configs: InternalCommercialCalculationConfiguration,
    ) = tableHelper.getTableConfig(valueType, tableOption, procurementType, *configs)

    private fun getIsCollapsed(
        opConfig: CalculationOperationConfiguration,
        operation: SumProdOperation,
    ): Boolean {
        val expandedStandardValues =
            setOf(
                StandardCalculationValue.TOTAL_MATERIAL_VALUE,
                StandardCalculationValue.TOTAL_MANUFACTURING_VALUE,
                StandardCalculationValue.TOTAL_PRODUCTION_VALUE,
                StandardCalculationValue.TOTAL_OVERHEAD_VALUE,
                StandardCalculationValue.TOTAL_SALE_VALUE,
            )

        return when {
            // Direct addends should be expanded
            isDirectAddendOfEntryPoint(getEntryPoint(opConfig), operation) -> false
            // Some important standard values should be expanded
            operation.standardCalculationValue in expandedStandardValues -> false
            // On the default view every standard calculation value must be seen => the parent must be expanded
            operation.addends.any {
                opConfig.getAllStandardCalculationOperations().map { op -> op.destinationElementKey }.contains(it.elementKey)
            } -> false
            else -> true
        }
    }

    private fun getEntryPoint(opConfig: CalculationOperationConfiguration): OperationWithStandardCalculationValue =
        opConfig.getOperationOfStandardValue(StandardCalculationValue.TOTAL_SALE_VALUE)

    private fun getColumnCreators(config: InternalCommercialCalculationConfiguration): List<ColumnCreator> {
        return listOfNotNull(
            fieldAndLookupHelper.displayValueColumnCreator(config),
            fieldAndLookupHelper.baseLookupColumnCreator(config.valueType),
            fieldAndLookupHelper.interestTimeLookupColumnCreator(config.opConfig),
            fieldAndLookupHelper.rateLookupColumnCreator(config.opConfig),
            fieldAndLookupHelper.roleThisLookupColumnCreator(config.valueType),
            fieldAndLookupHelper.roleInHouseLookupColumnCreator(config.valueType),
            fieldAndLookupHelper.roleTotalLookupColumnCreator(config.valueType),
        )
    }
}
