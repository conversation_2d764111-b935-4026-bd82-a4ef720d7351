package com.nu.bom.core.user

import com.nu.bom.core.exception.InternalServerException
import com.nu.bom.core.exception.readable.ErrorCode
import com.nu.bom.core.exception.userException.AccessDeniedException
import com.nu.bom.core.exception.userException.AccountIdNotFoundException
import com.nu.bom.core.exception.userException.AccountNameNotFoundException
import com.nu.bom.core.model.Account
import com.nu.bom.core.service.AccountService
import com.nu.security.config.MultitenancyConfig
import org.slf4j.LoggerFactory
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.core.env.Environment
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono

interface AccessCheckProvider {
    fun createAccessCheck(jwt: Jwt?): Mono<AccessCheck>

    fun getDefaultAccessCheckForAccount(accountId: String): Mono<AccessCheck>

    fun defaultAccount(): Mono<AccessCheck>

    fun impersonateAccount(
        accessCheck: AccessCheck,
        newAccountName: String,
    ): Mono<AccessCheck>

    fun impersonateAdminAccountById(
        accessCheck: AccessCheck,
        newAccountId: String,
    ): Mono<AccessCheck>

    fun <T> doAsOut(
        jwt: Jwt?,
        fnc: (AccessCheck) -> Mono<out T>,
    ): Mono<out T> {
        return createAccessCheck(jwt).flatMap { accessCheck ->
            Mono.just(accessCheck).flatMap {
                fnc(it)
            }.withAccessCheck(accessCheck)
        }
    }

    fun <T> doAs(
        jwt: Jwt?,
        fnc: (AccessCheck) -> Mono<T>,
    ): Mono<T> {
        return createAccessCheck(jwt).flatMap { accessCheck ->
            Mono.just(accessCheck).flatMap {
                fnc(it)
            }.withAccessCheck(accessCheck)
        }
    }

    fun <T> doAsReturnMany(
        jwt: Jwt?,
        fnc: (AccessCheck) -> Flux<T>,
    ): Flux<T> {
        return createAccessCheck(jwt).flatMapMany { accessCheck ->
            execToFlux(accessCheck, fnc)
        }
    }

    private fun <T> execToFlux(
        accessCheck: AccessCheck,
        fnc: (AccessCheck) -> Flux<T>,
    ): Flux<T> {
        return Mono.just(accessCheck).flatMapMany {
            fnc(it)
        }.withAccessCheck(accessCheck)
    }

    fun <T> doAndImpersonateAs(
        jwt: Jwt?,
        switchAccountTo: String?,
        fnc: (AccessCheck) -> Mono<T>,
    ): Mono<T> {
        return createAccessCheck(jwt).flatMap { accessCheck ->
            if (switchAccountTo != null && switchAccountTo != accessCheck.accountName) {
                impersonateAccount(accessCheck, switchAccountTo).flatMap { impersonatedAccount ->
                    Mono.just(impersonatedAccount).flatMap {
                        fnc(it)
                    }.withAccessCheck(impersonatedAccount)
                }
            } else {
                Mono.just(accessCheck).flatMap {
                    fnc(it)
                }.withAccessCheck(accessCheck)
            }
        }
    }

    fun <T> doAndImpersonateAsAdminOnly(
        jwt: Jwt?,
        switchAccountTo: String?,
        fnc: (AccessCheck) -> Mono<T>,
    ): Mono<T> {
        return adminOnly(jwt, doAndImpersonateAs(jwt, switchAccountTo, fnc))
    }

    fun <T> doAndImpersonateAsMany(
        jwt: Jwt?,
        switchAccountTo: String?,
        fnc: (AccessCheck) -> Flux<T>,
    ): Flux<T> {
        return createAccessCheck(jwt).flatMapMany { accessCheck ->
            if (switchAccountTo != null && switchAccountTo != accessCheck.accountName) {
                impersonateAccount(accessCheck, switchAccountTo).flatMapMany { impersonatedAccount ->
                    execToFlux(impersonatedAccount, fnc)
                }
            } else {
                execToFlux(accessCheck, fnc)
            }
        }
    }

    fun <T> adminOnly(
        jwt: Jwt?,
        body: () -> Mono<T>,
    ): Mono<T> =
        this.doAs(jwt) { accessCheck ->
            if (accessCheck.isAdmin()) {
                body()
            } else {
                Mono.error(InternalServerException(ErrorCode.NOT_ADMIN_USER))
            }
        }

    /**
     * checks if user has the given role
     */
    fun <T> doAsRequireRole(
        role: AccessCheck.Role,
        jwt: Jwt?,
        body: (accessCheck: AccessCheck) -> Mono<T>,
    ): Mono<T> =
        this.doAs(jwt) { accessCheck ->
            if (accessCheck.hasRole(role)) {
                body(accessCheck)
            } else {
                Mono.error(AccessDeniedException())
            }
        }

    fun <T> adminOnlyWithAccessCheck(
        jwt: Jwt?,
        body: (accessCheck: AccessCheck) -> Mono<T>,
    ): Mono<T> =
        this.doAs(jwt) { accessCheck ->
            if (accessCheck.isAdmin()) {
                body(accessCheck)
            } else {
                Mono.error(InternalServerException(ErrorCode.NOT_ADMIN_USER))
            }
        }

    fun <T> adminOnly(
        jwt: Jwt?,
        body: Mono<T>,
    ): Mono<T> =
        this.doAs(jwt) { accessCheck ->
            if (accessCheck.isAdmin()) {
                body
            } else {
                Mono.error(InternalServerException(ErrorCode.NOT_ADMIN_USER))
            }
        }

    fun <T> adminOnlyFlux(
        jwt: Jwt?,
        body: (AccessCheck) -> Flux<T>,
    ): Flux<T> =
        this.doAsReturnMany(jwt) { accessCheck ->
            if (accessCheck.isAdmin()) {
                body(accessCheck)
            } else {
                Flux.error(InternalServerException(ErrorCode.NOT_ADMIN_USER))
            }
        }

    companion object {
        fun getCurrentAccessCheck(): Mono<AccessCheck> {
            return Mono.deferContextual { ctx -> Mono.just(ctx.get(ACCESS_CHECK_CONTEXT_KEY)) }
        }
    }
}

private const val ACCESS_CHECK_CONTEXT_KEY = "accessCheck"

/**
 * Puts the given [AccessCheck] in the subscriber context of this [Mono] (propagating it upstream).
 *
 * --
 *
 * **It can be used to test services that do not accept an [AccessCheck] in their method parameters,
 * but rely on [AccessCheckProvider.getCurrentAccessCheck].**
 *
 * @see Mono.contextWrite
 * */
fun <T> Mono<T>.withAccessCheck(accessCheck: AccessCheck): Mono<T> =
    this.contextWrite { ctx ->
        ctx.put(ACCESS_CHECK_CONTEXT_KEY, accessCheck)
    }

/**
 * Puts the [AccessCheck] in the subscriber context of this [Flux] (propagating it upstream).
 *
 * --
 *
 * **It can be used to test services that do not accept an [AccessCheck] in their method parameters,
 * but rely on [AccessCheckProvider.getCurrentAccessCheck].**
 *
 * @see Flux.contextWrite
 * */
fun <T> Flux<T>.withAccessCheck(accessCheck: AccessCheck): Flux<T> =
    this.contextWrite { ctx ->
        ctx.put(ACCESS_CHECK_CONTEXT_KEY, accessCheck)
    }

private const val DEFAULT_REALM = "default"

@Service
@EnableConfigurationProperties(MultitenancyConfig::class)
class DefaultAccessCheckProvider(
    private val accountService: AccountService,
    private val config: MultitenancyConfig,
    private val environment: Environment,
) : AccessCheckProvider {
    val logger = LoggerFactory.getLogger(DefaultAccessCheckProvider::class.java)!!

    override fun createAccessCheck(jwt: Jwt?): Mono<AccessCheck> {
        val (subject, accountName, groupMembership) = extractClaims(jwt)
        val realm = extractRealm(jwt)
        val roles = extractRoles(jwt)
        val username = jwt?.getClaim<String>("preferred_username")

        return getAccount(accountName)
            .map { account ->
                createAccessCheck(jwt, account, username, subject, groupMembership, roles, realm)
            }
    }

    private fun createAccessCheck(
        jwt: Jwt?,
        account: Account,
        username: String?,
        subject: String?,
        groupMembership: List<String>?,
        roles: List<String>,
        realm: String?,
    ): AccessCheck {
        val token = jwt?.tokenValue ?: AccessCheck.EMPTY_TOKEN
        return AccessCheck(
            accountId = account.getAccountIdStr(),
            accountName = account.externalRoleName,
            token = token,
            userId = getUserId(subject),
            groups = groupMembership ?: emptyList(),
            roles = roles,
            accountLabel = account.getAccountLabel(),
            isLocal = environment.activeProfiles.contains("local"),
            userName = username,
            claims = AccessCheckClaims(filterStringClaims(jwt) ?: mapOf()),
            realm = realm ?: DEFAULT_REALM,
        )
    }

    private fun filterStringClaims(jwt: Jwt?) = jwt?.claims?.filter { it.value is String }?.mapValues { it.value as String }

    override fun impersonateAccount(
        accessCheck: AccessCheck,
        newAccountName: String,
    ): Mono<AccessCheck> =
        if (accessCheck.isAdmin()) {
            getAccount(newAccountName).map {
                fromAccount(it, accessCheck.userId, accessCheck.realm)
            }
        } else if (accessCheck.accountName == newAccountName) {
            accessCheck.toMono()
        } else {
            Mono.error(AccessDeniedException())
        }

    override fun impersonateAdminAccountById(
        accessCheck: AccessCheck,
        newAccountId: String,
    ): Mono<AccessCheck> =
        accountService.getOne(newAccountId)
            .switchIfEmpty(
                Mono.error(AccountIdNotFoundException(newAccountId)),
            )
            .map {
                fromAccount(it, accessCheck.userId, accessCheck.realm)
            }

    fun fromAccount(
        account: Account,
        userId: String = AccessCheck.DEFAULT_USER,
        realm: String = DEFAULT_REALM,
    ): AccessCheck {
        return AccessCheck(
            accountId = account.getAccountIdStr(),
            accountName = account.externalRoleName,
            token = "from-account-${account._id}-$userId",
            userId = userId,
            accountLabel = account.getAccountLabel(),
            realm = realm,
        )
    }

    override fun getDefaultAccessCheckForAccount(accountId: String): Mono<AccessCheck> =
        accountService.getOne(accountId).map { account ->
            AccessCheck(
                accountId = accountId,
                accountName = account.externalRoleName,
                token = "default-access-check-for-account-$accountId",
                userId = AccessCheck.DEFAULT_USER,
                accountLabel = account.getAccountLabel(),
                realm = DEFAULT_REALM,
            )
        }

    override fun defaultAccount(): Mono<AccessCheck> {
        return accountService.getDefaultAccount().map { account ->
            fromAccount(account)
        }
    }

    private fun extractClaims(jwt: Jwt?): Triple<String?, String?, List<String>?> {
        val subject = jwt?.subject
        val accountName = jwt?.getClaim<String>(config.accountClaim)
        val groupMembership = jwt?.getClaimAsStringList(config.groupMembership)

        if (!config.defaultFallbackEnabled) {
            if (accountName.isNullOrEmpty()) {
                throw InternalServerException(
                    ErrorCode.CLAIM_NOT_FOUND,
                    "${config.accountClaim}",
                )
            }
            if (subject.isNullOrEmpty()) {
                throw InternalServerException(ErrorCode.CLAIM_NOT_FOUND, "sub")
            }
            if (groupMembership.isNullOrEmpty()) {
                throw InternalServerException(
                    ErrorCode.CLAIM_NOT_FOUND,
                    "${config.groupMembership}",
                )
            }
        }

        return Triple(subject, accountName, groupMembership)
    }

    private fun extractRealm(jwt: Jwt?): String? {
        val realm = jwt?.getClaim<String>(config.realmClaim)
        if (!config.defaultFallbackEnabled && realm == null) {
            throw InternalServerException(ErrorCode.CLAIM_NOT_FOUND, config.realmClaim.toString())
        }
        return realm
    }

    private fun extractRoles(jwt: Jwt?): List<String> {
        return (
            if (config.userRoles != null && jwt != null) {
                val realAccess = jwt.getClaimAsMap(config.userRoles)?.get("roles")
                if (realAccess is List<*>) {
                    realAccess as List<String>
                } else {
                    null
                }
            } else {
                null
            }
        ) ?: emptyList()
    }

    private fun getAccount(accountName: String?): Mono<Account> =
        if (accountName != null) {
            accountService.getAccountFromExternalRoleName(accountName)
                .switchIfEmpty(
                    Mono.error(AccountNameNotFoundException(accountName)),
                )
        } else {
            logger.debug("No account specified, fallback to the default account:{}", config.defaultFallbackEnabled)
            if (config.defaultFallbackEnabled) {
                accountService.getDefaultAccount()
            } else {
                Mono.empty()
            }
        }

    private fun getUserId(subject: String?): String =
        subject ?: AccessCheck.DEFAULT_USER.also {
            logger.debug("No subject specified, fallback to the default user:{}", it)
        }
}
