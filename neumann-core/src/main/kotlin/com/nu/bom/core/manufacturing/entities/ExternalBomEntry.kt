package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.annotations.DontSortOptions
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.IgnoreForOverwrittenState
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.TranslationSection
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.EntityRef

@EntityType(Entities.BOM_ENTRY)
class ExternalBomEntry(
    name: String,
) : ManufacturingEntity(name),
    BomNodeReference {
    override val extends = BomEntry(name)

    @ReadOnly
    fun isExternalBomEntry(): Bool = Bool(true)

    @DontSortOptions
    @Path("/api/man/{bomNodeId}/steps?branch={branchId}&stepless=true&includeExternal=true", getDisplayNameFromPathQuery = true)
    @TranslationSection("fields")
    @IgnoreForOverwrittenState
    @ReadOnly
    fun parentStepRef(): EntityRef {
        val findStepParent = findParentWithEntityType(Entities.MANUFACTURING_STEP, 1)
        val directParent = parents.firstOrNull()
        val parent = findStepParent ?: directParent
        return parent?.let { parentEntity ->
            EntityRef(parentEntity._id).apply {
                entity = parentEntity
            }
        } ?: EntityRef()
    }
}
