package com.nu.bom.core.manufacturing.entities

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer
import com.nu.bom.core.manufacturing.annotations.DisableEntityCopyInModularizedContext
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.MasterDataCalculation
import com.nu.bom.core.manufacturing.annotations.Modularized
import com.nu.bom.core.manufacturing.annotations.Nocalc
import com.nu.bom.core.manufacturing.entities.masterdata.material.MaterialConsumerExtension
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.ClassSelector
import com.nu.bom.core.manufacturing.fieldTypes.EntityRef
import com.nu.bom.core.manufacturing.fieldTypes.ExchangeRatesField
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.FieldWithResult
import com.nu.bom.core.manufacturing.fieldTypes.KnowledgeEntityData
import com.nu.bom.core.manufacturing.fieldTypes.KnowledgeFieldData
import com.nu.bom.core.manufacturing.fieldTypes.NoCalcFieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Null
import com.nu.bom.core.manufacturing.fieldTypes.NumericFieldResult
import com.nu.bom.core.manufacturing.fieldTypes.NumericFieldResultWithUnit
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.PiecesUnits
import com.nu.bom.core.manufacturing.fieldTypes.SkillTypeConsumerHelper
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TypeUnit
import com.nu.bom.core.manufacturing.service.EntityClassOrName
import com.nu.bom.core.manufacturing.service.ExternalStorageService
import com.nu.bom.core.manufacturing.service.Field
import com.nu.bom.core.manufacturing.service.FieldIdKey
import com.nu.bom.core.manufacturing.service.FieldKey
import com.nu.bom.core.manufacturing.service.InputKey
import com.nu.bom.core.manufacturing.service.MatchKey
import com.nu.bom.core.manufacturing.service.VersionedResult
import com.nu.bom.core.manufacturing.service.asEntityClass
import com.nu.bom.core.manufacturing.service.asEntityClassName
import com.nu.bom.core.manufacturing.utils.CalculationContextServiceImplReach
import com.nu.bom.core.manufacturing.utils.FieldExtractionUtils
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.CalculationContextData
import com.nu.bom.core.model.EntityContext
import com.nu.bom.core.model.EntityCreation
import com.nu.bom.core.model.EntityDeletion
import com.nu.bom.core.model.EntityPathElement
import com.nu.bom.core.model.MasterDataSelector
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import com.nu.bom.core.utils.Either
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.FieldFactory
import com.nu.bom.core.utils.FieldResultUtils
import com.nu.bom.core.utils.changelog.EntityHash
import com.nu.bom.core.utils.findInTree
import com.nu.bom.core.utils.replaceFieldResultValue
import com.nu.bom.core.utils.replaceFirst
import com.nu.bom.core.utils.tryCast
import com.nu.bom.core.utils.visitTree
import com.nu.bom.core.utils.visitTree2
import com.tset.common.util.NOT_NULL_STYLE
import com.tset.core.module.bom.exchangerates.ExchangeRateMap
import com.tset.core.service.domain.Currency
import org.apache.commons.lang3.builder.ToStringBuilder
import org.bson.types.ObjectId
import org.slf4j.LoggerFactory
import org.springframework.data.annotation.Id
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.math.BigDecimal
import java.math.BigInteger
import java.util.Optional
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentMap
import kotlin.reflect.KClass
import kotlin.reflect.full.findAnnotation
import kotlin.reflect.full.isSubclassOf

fun MutableList<FieldWithResult>.getFieldResultMap() = filter { it.result !is Null }.associateBy({ it.name.name }, { it.result })

fun MutableList<FieldWithResult>.replaceFieldResult(
    fieldName: String,
    replaceWith: (FieldResultStar) -> FieldResultStar,
) = replaceFirst({ it.name.name == fieldName }) { oldFieldResult ->
    FieldWithResult(
        name = oldFieldResult.name,
        result = replaceWith(oldFieldResult.result),
    )
}

fun MutableList<FieldWithResult>.addFieldResult(
    fieldName: String,
    replaceWith: (FieldResultStar?) -> FieldResultStar,
    createKeyFor: (String, FieldResultStar) -> FieldKey,
): FieldWithResult {
    val newResult = replaceWith(null)
    val freshValue = FieldWithResult(name = createKeyFor(fieldName, newResult), result = newResult)
    add(freshValue)
    return freshValue
}

fun MutableList<FieldWithResult>.addFieldResultWithOriginalVersion(
    fieldName: String,
    replaceWith: (FieldResultStar?) -> FieldResultStar,
    createKeyFor: (String, FieldResultStar, Int?, Int?) -> FieldKey,
    oldFieldWithResults: List<FieldWithResult> = emptyList(),
): FieldWithResult {
    val version =
        oldFieldWithResults
            .filter { it.name.name == fieldName }
            .firstOrNull()
            ?.name
            ?.version
    val newVersion =
        oldFieldWithResults
            .filter { it.name.name == fieldName }
            .firstOrNull()
            ?.name
            ?.newVersion

    val newResult = replaceWith(null)
    val freshValue = FieldWithResult(name = createKeyFor(fieldName, newResult, version, newVersion), result = newResult)

    add(freshValue)
    return freshValue
}

fun MutableList<FieldWithResult>.replaceOrAddFieldResult(
    fieldName: String,
    replaceWith: (FieldResultStar?) -> FieldResultStar,
    createKeyFor: (String, FieldResultStar) -> FieldKey,
): FieldWithResult = replaceFieldResult(fieldName, replaceWith) ?: addFieldResult(fieldName, replaceWith, createKeyFor)

@JsonIgnoreProperties(value = ["services", "dependencyFields"])
open class ManufacturingEntity(
    val name: String,
    var fieldWithResults: MutableList<FieldWithResult> = mutableListOf(),
    open var children: MutableList<ManufacturingEntity> = mutableListOf(),
    var initialFieldsWithResults: MutableList<FieldWithResult> = mutableListOf(),
) {
    companion object {
        private val logger = LoggerFactory.getLogger(ManufacturingEntity::class.java)
        val entityTypMapping: ConcurrentMap<KClass<*>, Optional<Entities>> =
            ConcurrentHashMap(1000)
    }

    // ONLY USE IN TESTING
    var idOverwrite: String? = null

    @Id
    @JsonSerialize(using = ToStringSerializer::class)
    @Suppress("ktlint:standard:backing-property-naming")
    var _id: ObjectId = ObjectId()
        set(newId) {
            field = newId
            idAsString = newId.toString().intern()
        }

    private var idAsString = _id.toString().intern()

    val entityId: String
        get() {
            return idOverwrite ?: idAsString
        }

    var version = 0

    var oldVersion: Int? = null
    var initVersion: Int? = 0

    /** True iff this entity is used in a modular context */
    var isolated: Boolean = false

    /**
     * The hash from the last calculation of this entity
     * null until first calculation happened
     */
    var previouslyCalculatedVersionHash: EntityHash? = null

    /**
     * Always set to the hash of the currently loaded implementation
     */
    var currentImplementationVersionHash: EntityHash? = null

    /**
     * Hashes of all extension classes involved in the last calculation
     */
    var savedExtensionHashes: Map<KClass<out ManufacturingEntity>, EntityHash>? = null

    var entityRef: String? = null

    var createdOnBranch: BranchId? = null

    // planned to be used in the future for dynamically defined links
    // add mapping in ManufacturingModelEntity when it becomes relevant
    open var links: MutableMap<String, InputKey> = mutableMapOf()

    @JsonInclude(JsonInclude.Include.NON_NULL)
    var masterDataSelector: MasterDataSelector? = null

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonSerialize(using = ToStringSerializer::class)
    var masterDataObjectId: ObjectId? = null

    var masterDataVersion: Int? = null

    var parents: MutableList<ManufacturingEntity> = mutableListOf()

    open val extends: ManufacturingEntity? = null

    open val behaviours: List<ManufacturingEntity> = emptyList()

    val extensionEntities: MutableList<ManufacturingEntity> = mutableListOf()

    var calculationServices: CalculationContextServiceImplReach? = null

    val services: CalculationContextServiceImplReach
        get() =
            calculationServices
                ?: calculationContextEntity?.calculationServices!!

    var calculationContext: CalculationContextData? = null

    var snapshot: BomNodeSnapshot? = null

    var bomNodeId: BomNodeId? = null

    var rootBomNodeId: Optional<BomNodeId>? = null

    var extracted: Boolean = false

    private var fields: Map<String, Field> = mapOf()

    var childDeps: List<Pair<InputKey, Field>> = listOf()

    var siblingDeps: List<Field> = listOf()

    var createdByField: Field? = null

    var calculationContextEntity: ManufacturingEntity? = null

    var createdBy: FieldKey? = null

    var createdByMocked: Boolean = false

    var providerField: FieldIdKey? = null

    var dirtyChildLoading: Boolean = false

    // id of the last acknowledged migration for this entity
    var lastMigrationChangeSetId: MigrationChangeSetId? = null

    var dynamicFields: MutableMap<String, DynamicField> = mutableMapOf()

    data class DynamicField(
        val entityClass: String?,
        val entityFieldName: String,
    )

    @Nocalc
    fun extensionSet(): Set<Class<out ManufacturingEntity>> {
        fun extensionListImpl(entity: ManufacturingEntity): MutableSet<Class<out ManufacturingEntity>> {
            val head = entity::class.java

            return entity.extends?.let {
                val tail = extensionListImpl(it)
                tail.add(head)
                tail
            } ?: mutableSetOf(head)
        }

        return extensionListImpl(this)
    }

    @Nocalc
    fun getFullPath(): List<EntityPathElement> = getAllFullPaths().firstOrNull() ?: emptyList()

    /** Resolves all paths across all parents until a top level element is reached.
     *
     * @return list of all paths from this node to a top level element.
     * */
    @Nocalc
    fun getAllFullPaths(): List<List<EntityPathElement>> {
        val currentSegment =
            EntityPathElement(
                displayDesignation = Text(displayName.orEmpty()),
                type = getEntityTypeAnnotation() ?: Entities.NONE,
                entityClass = getEntityClass(),
                entityId = _id,
            )

        return parents
            .flatMap { parent -> parent.getAllFullPaths() }
            .map { parentPath ->
                parentPath + listOf(currentSegment)
            }.ifEmpty {
                listOf(listOf(currentSegment))
            }
    }

    /** Gets a parent path until a top level element. */
    @Nocalc
    fun getPath(): List<EntityPathElement> = getAllPaths().reversed()

    /** Resolves all paths across all parents until a top level element is reached.
     *
     * @return list of all paths from this node to a top level element.
     * */
    @Nocalc
    fun getAllPaths(currentPath: List<EntityPathElement> = listOf()): List<EntityPathElement> {
        val entityType = getEntityTypeAnnotation() ?: return currentPath

        // Path is only to navigate within a manufacturing, so manufacturing itself should not be part of it
        if (entityType == Entities.MANUFACTURING) {
            return currentPath
        }

        val newPath =
            if (
                // Only add Step if there is no step at the path yet (don't duplicate steps)
                (entityType != Entities.MANUFACTURING_STEP || currentPath.lastOrNull()?.type != Entities.MANUFACTURING_STEP) &&
                // Don't show Processed materials in the path
                entityType != Entities.PROCESSED_MATERIAL
            ) {
                currentPath +
                    EntityPathElement(
                        displayDesignation = Text(displayName.orEmpty()),
                        type = entityType,
                        entityClass = getEntityClass(),
                        entityId = _id,
                    )
            } else {
                currentPath
            }

        return when (parents.size) {
            0 -> newPath
            1 -> parents[0].getAllPaths(newPath)
            else -> throw java.lang.IllegalArgumentException("Multi Parent case not supported for paths within manufacturing")
        }
    }

    @Nocalc
    fun calculationContext() = calculationContext ?: error("CalculationContext not loaded")

    @Nocalc
    fun getFieldObject(fieldName: String?): Field? =
        if (fieldName != null) {
            fields[fieldName]
        } else {
            null
        }

    @Nocalc
    fun getRequiredFieldObject(fieldName: String): Field = getFieldObject(fieldName) ?: error("No '$fieldName' is defined in $this!")

    @Nocalc
    fun getFieldsAsList(): List<Field> = fields.values.toList()

    @Nocalc
    fun addFieldObject(
        field: Field,
        fieldName: String = field.name,
    ) {
        fields = fields + (fieldName to field)
    }

    @Nocalc
    fun getMatchingInitialField(
        key: MatchKey,
        isModelInput: Boolean,
    ): VersionedResult? {
        val fieldWithResult =
            initialFieldsWithResults.find { key.isMatching(it.name) }
                ?: (
                    // So this is actually a hack for RAWT.
                    // Since For RAWT, the model gets created as a subcalculation,
                    // we have to pass down the model inputs to the subcalculation.
                    // However, we currently don't want this behavior for the
                    // versionedPart and threeDbResourceTracker, as that would
                    // mean that both the main and the sub calculation have their
                    // own threeDb part. Maybe we will actually change that in the
                    // future, when we release 3d support for RAWT, because then
                    // we can decide again how everything should behave.
                    if (isModelInput &&
                        !listOf(
                            "versionedPart",
                            "threeDbResourceTracker",
                        ).contains(key.name)
                    ) {
                        getMatchingInitialFieldFromParents(key)
                    } else {
                        null
                    }
                )
        return fieldWithResult?.toVersionedResult()
    }

    @Nocalc
    fun getMatchingInitialFieldFromParents(key: MatchKey): FieldWithResult? =
        if (this.getEntityTypeAnnotation() == Entities.MANUFACTURING) {
            this.parents
                .map {
                    it.getMatchingInitialFieldForChildren(key)
                }.singleOrNull()
        } else {
            null
        }

    @Nocalc
    fun getMatchingInitialFieldForChildren(key: MatchKey): FieldWithResult? =
        (
            if (getEntityTypeAnnotation() == Entities.MANUFACTURING) {
                initialFieldsWithResults.find { it.name.name == key.name }
            } else {
                null
            }
        ) ?: this.parents
            .map {
                it.getMatchingInitialFieldForChildren(key)
            }.singleOrNull()

    @Nocalc
    fun setFieldsAndClearCreatedBy(fields: List<Field>) {
        this.fields = fields.associateBy { it.name }
        this.createdByField = null
    }

    @Nocalc
    inline fun <reified T : NumericFieldResultWithUnit<*, *>> sum(
        list: List<T>?,
        default: T,
    ): T {
        if (list.isNullOrEmpty()) {
            return default
        }
        val unit = list[0].unit
        return createField(
            T::class,
            list
                .map {
                    it.toResExt(unit)
                }.reduce { a, b ->
                    a + b
                },
            unit,
        )
    }

    @Nocalc
    inline fun <reified T : NumericFieldResultWithUnit<*, *>> max(
        list: List<T>?,
        default: T,
    ): T {
        if (list.isNullOrEmpty()) {
            return default
        }

        val unit = list[0].unit
        return createField(
            T::class,
            list
                .map {
                    it.toResExt(unit)
                }.reduce { a, b ->
                    if (a > b) a else b
                },
            unit,
        )
    }

    @Nocalc
    fun sum(list: List<Pieces>?): Pieces =
        Pieces(
            list
                ?.map {
                    it.res
                }?.reduceOrNull { a, b ->
                    a + b
                } ?: BigDecimal(0),
            PiecesUnits.PIECE,
        )

    @Nocalc
    inline fun <reified T : NumericFieldResult<*>> sum(list: List<T>?): T {
        if (T::class.isSubclassOf(NumericFieldResultWithUnit::class)) {
            throw IllegalArgumentException(
                "Illegal input type: NumericFieldResultWithUnit. " +
                    "Use the designated sum() method for fields with units.",
            )
        }
        return createField(
            type = T::class,
            value =
                list
                    ?.map {
                        it.res
                    }?.reduceOrNull { a, b ->
                        a + b
                    } ?: BigDecimal(0),
        )
    }

    @Nocalc
    inline fun <reified T : NumericFieldResult<*>> max(list: List<T>?): T {
        if (T::class.isSubclassOf(NumericFieldResultWithUnit::class)) {
            throw IllegalArgumentException(
                "Illegal input type: NumericFieldResultWithUnit. " +
                    "Use the designated max() method for fields with units.",
            )
        }
        return createField(
            T::class,
            list
                ?.map {
                    it.res
                }?.reduce { a, b ->
                    max(a, b)
                } ?: BigDecimal.ZERO,
        )
    }

    @Nocalc
    fun max(
        a: BigDecimal,
        b: BigDecimal,
    ): BigDecimal = a.max(b)

    @Nocalc
    fun <T : NumericFieldResult<T>> max(
        a: T,
        b: T,
    ): T = if (a.res < b.res) b else a

    @Nocalc
    fun max(
        a: BigInteger,
        b: BigInteger,
    ): BigInteger = a.max(b)

    @Nocalc
    fun <T : NumericFieldResult<*>> max(vararg values: T): T = values.maxBy { it.res }

    @Nocalc
    fun max(vararg values: BigDecimal): BigDecimal = values.maxOrNull()!!

    @Nocalc
    fun min(
        a: BigDecimal,
        b: BigDecimal,
    ): BigDecimal = a.min(b)

    @Nocalc
    fun <T : NumericFieldResult<T>> min(
        a: T,
        b: T,
    ): T = if (a.res < b.res) a else b

    @Nocalc
    fun min(vararg values: BigDecimal): BigDecimal = values.minOrNull()!!

    @Nocalc
    fun getMaxVersion(): Int = (children.map { it.getMaxVersion() } + version).maxOrNull() ?: 0

    @Nocalc
    fun createKeyFor(
        name: String,
        value: FieldResultStar,
        version: Int? = null,
        newVersion: Int? = null,
    ): FieldKey = createKeyFor(name, value::class.qualifiedName!!, version, newVersion)

    @Nocalc
    fun createKeyFor(
        name: String,
        fieldType: String,
        version2: Int? = null,
        newVersion2: Int? = null,
    ): FieldKey =
        FieldKey(
            name = name,
            entityId = entityId,
            entityRef = entityRefForFieldKey(),
            entityType = getEntityType(),
            type = fieldType,
            version = version2 ?: version,
            newVersion = newVersion2 ?: version,
        )

    @Nocalc
    fun createKeyWithOriginalVersion(
        name: String,
        value: FieldResultStar,
        version: Int?,
        newVersion: Int?,
    ): FieldKey = createKeyWithOriginalVersion(name, value::class.qualifiedName!!, version, newVersion)

    @Nocalc
    fun createKeyWithOriginalVersion(
        name: String,
        fieldType: String,
        version2: Int?,
        newVersion2: Int?,
    ): FieldKey =
        FieldKey(
            name = name,
            entityId = entityId,
            entityRef = entityRefForFieldKey(),
            entityType = getEntityType(),
            type = fieldType,
            version = version2 ?: version,
            newVersion = newVersion2 ?: version,
        )

    @Nocalc
    fun mockField(): Unit = throw NotImplementedError("This method is never called, exists only for making the virtual/mock fields easier")

    open fun extractArgs(): Map<String, Any?> = hashMapOf()

    fun entityDesignation(): Text = Text(name)

    @MasterDataCalculation
    fun masterDataTypeInternal(): Text = Text((masterDataSelector?.type ?: MasterDataType.NONE).name)

    @Nocalc
    fun <K : ManufacturingEntity> createEntity(
        name: String,
        entityType: Entities,
        clazz: KClass<K>? = null,
        args: Map<String, Any> = emptyMap(),
        entityRef: String? = null,
        fields: Map<String, FieldResult<*, *>> = emptyMap(),
        overwrites: Map<String, FieldResult<*, *>> = emptyMap(),
    ): K =
        services.createEntity(
            name = name,
            entityType = entityType,
            clazz = EntityClassOrName.firstOrNull(clazz),
            args = args,
            fields = fields,
            overwrites = overwrites,
            entityRef = entityRef,
            version = this.version,
        ) as K

    @Nocalc
    fun <K : ManufacturingEntity> createEntity(
        name: String,
        entityType: Entities,
        clazz: EntityClassOrName,
        args: Map<String, Any> = emptyMap(),
        entityRef: String? = null,
        fields: Map<String, FieldResult<*, *>> = emptyMap(),
        overwrites: Map<String, FieldResult<*, *>> = emptyMap(),
        entityId: ObjectId? = null,
    ): K =
        services.createEntity(
            name = name,
            entityType = entityType,
            clazz = clazz,
            args = args,
            fields = fields,
            overwrites = overwrites,
            entityRef = entityRef,
            version = this.version,
            entityId = entityId,
        ) as K

    @Nocalc
    fun createManufacturing(
        name: String,
        clazz: KClass<out ManufacturingEntity>,
        args: Map<String, Any?>,
        masterDataSelector: MasterDataSelector? = null,
        masterDataObjectId: ObjectId? = null,
        fields: Map<String, FieldResult<*, *>> = emptyMap(),
        overwrites: Map<String, FieldResult<*, *>> = emptyMap(),
    ): BaseManufacturing =
        services.createManufacturing(
            name = name,
            clazz = clazz.asEntityClass(),
            args = args,
            masterDataSelector = masterDataSelector,
            masterDataObjectId = masterDataObjectId,
            fields =
                fields +
                    overwrites.mapValues {
                        it.value.source = FieldResult.SOURCE.I
                        it.value
                    },
            version = this.version,
        )

    @Nocalc
    fun <K : ManufacturingEntity> createBomEntryForCalculationModule(
        clazz: KClass<K>,
        quantity: Int,
    ): ManufacturingEntity = createBomEntryForCalculationModule(clazz, quantity)

    @Nocalc
    fun <K : ManufacturingEntity> createBomEntryForCalculationModule(
        clazz: KClass<K>,
        quantity: Pieces = Pieces.ONE,
    ): ManufacturingEntity =
        createEntity(
            name = clazz.simpleName!!,
            clazz = clazz,
            fields = mapOf("quantity" to quantity),
            entityType = Entities.BOM_ENTRY,
        )

    @Nocalc
    fun <K : ManufacturingEntity> createStepForCalculationModule(
        clazz: KClass<K>,
        name: String? = null,
    ) = createEntity(
        name = name ?: clazz.java.simpleName,
        clazz = clazz,
        entityType = Entities.MANUFACTURING_STEP,
    )

    @Nocalc
    fun createMachinesFromTemplate(templateName: Text): Flux<ManufacturingEntity> {
        val name = "${templateName.res}_Machine"
        return createEntitiesFromTemplate(
            name = name,
            location = "Global",
        )
    }

    @Nocalc
    fun createLaborFromTemplate(
        templateName: Text,
        designation: Text,
    ): Flux<ManufacturingEntity> {
        val name = "${templateName.res}_Labor"
        return createEntitiesFromTemplate(
            name = name,
            location = designation.res,
        )
    }

    @Nocalc
    fun createSetupFromTemplate(
        templateName: Text,
        designation: Text,
    ): Flux<ManufacturingEntity> {
        val name = "${templateName.res}_Setup"
        return createEntitiesFromTemplate(
            name = name,
            location = designation.res,
        )
    }

    @Nocalc
    fun createToolFromTemplate(templateName: Text): Flux<ManufacturingEntity> {
        val name = "${templateName.res}_Tools"
        return createEntitiesFromTemplate(
            name = name,
            location = "Global",
        )
    }

    @Nocalc
    fun createConsumableFromTemplate(templateName: Text): Flux<ManufacturingEntity> {
        val name = "${templateName.res}_Consumable"
        return createEntitiesFromTemplate(
            name = name,
            location = "Global",
        )
    }

    @Nocalc
    fun <K : ManufacturingEntity> createCycleTimeStep(clazz: KClass<K>): K = createCycleTimeStep(clazz.simpleName!!, clazz)

    @Nocalc
    fun <K : ManufacturingEntity> createCycleTimeStep(
        name: String,
        clazz: KClass<K>,
    ): K =
        createEntity(
            name = name,
            clazz = clazz,
            entityType = Entities.CYCLETIME_STEP,
        )

    @Nocalc
    fun <K : ManufacturingEntity> createMaterialFromMasterdata(
        clazz: KClass<K>,
        masterDataType: MasterDataType,
        masterDataKey: String,
        fields: Map<String, FieldResult<*, *>> = emptyMap(),
    ): Mono<ManufacturingEntity> =
        createEntityWithMasterdata(
            name = clazz.simpleName!!,
            entityType = Entities.MATERIAL,
            clazz = clazz.simpleName!!,
            masterDataType = masterDataType,
            masterDataKey = masterDataKey,
            fields = fields + (MaterialConsumerExtension::headerKey.name to Text(masterDataKey)),
        )

    @Nocalc
    fun createEntitiesFromTemplate(
        name: String,
        location: String,
        additionalArgs: Map<String, Any> = mapOf(),
    ): Flux<ManufacturingEntity> {
        val calcContext = calculationContext()
        return services
            .createEntitiesFromTemplate(
                accessCheck = calcContext.accessCheck,
                name = name,
                location = location,
                version = this.version,
                year = calcContext.year,
                additionalArgs = additionalArgs,
            ).onErrorResume {
                logger.error("Error creating entities from template", it)
                Flux.empty()
            }
    }

    @Nocalc
    fun <T : ManufacturingEntity> createSystemParameter(
        masterData: MasterDataType,
        clazz: KClass<out T>,
        system: String,
        year: Int,
        location: String,
    ): Mono<T> =
        services.createSystemParameter(
            accessCheck = calculationContext().accessCheck,
            type = Entities.SYSTEM_PARAMETER,
            masterData = masterData,
            clazz = clazz,
            system = system,
            year = year,
            location = location,
            version = this.version,
        )

    @Nocalc
    fun createEntityWithMasterdata(
        name: String,
        entityType: Entities,
        clazz: EntityClassOrName,
        args: Map<String, Any> = emptyMap(),
        masterDataSelector: MasterDataSelector,
        overwrites: Map<String, FieldResult<*, *>> = emptyMap(),
        entityRef: String? = null,
        fields: Map<String, FieldResult<*, *>> = emptyMap(),
        entityId: ObjectId? = null,
    ): Mono<ManufacturingEntity> =
        services.createEntityWithMasterdata(
            accessCheck = calculationContext().accessCheck,
            name = name,
            entityType = entityType,
            clazz = clazz,
            masterDataSelector = masterDataSelector,
            version = version,
            args = args,
            overwrites = overwrites,
            entityRef = entityRef,
            fields = fields,
            entityId = entityId,
        )

    @Nocalc
    fun createEntityWithMasterdata(
        name: String,
        entityType: Entities,
        clazz: String,
        args: Map<String, Any> = emptyMap(),
        masterDataType: MasterDataType,
        masterDataKey: String,
        overwrites: Map<String, FieldResult<*, *>> = emptyMap(),
        entityRef: String? = null,
        fields: Map<String, FieldResult<*, *>> = emptyMap(),
        entityId: ObjectId? = null,
    ): Mono<ManufacturingEntity> =
        createEntityWithMasterdata(
            name = name,
            entityType = entityType,
            entityRef = entityRef,
            clazz = clazz.asEntityClassName(),
            args = args,
            masterDataSelector =
                MasterDataSelector(
                    type = masterDataType,
                    key = masterDataKey,
                ),
            overwrites = overwrites,
            fields = fields,
            entityId = entityId,
        )

    @Nocalc
    fun createEntityWithNewMasterdata(
        name: String,
        entityType: Entities,
        clazz: String,
        args: Map<String, Any> = emptyMap(),
        masterDataType: MasterDataType,
        masterDataKey: String,
        overwrites: Map<String, FieldResult<*, *>> = emptyMap(),
        entityRef: String? = null,
        fields: Map<String, FieldResult<*, *>> = emptyMap(),
        entityId: ObjectId? = null,
    ): Mono<ManufacturingEntity> =
        createEntityWithMasterdata(
            name = name,
            entityType = entityType,
            entityRef = entityRef,
            clazz = clazz.asEntityClassName(),
            args = args,
            masterDataSelector =
                MasterDataSelector(
                    type = masterDataType,
                    key = masterDataKey,
                ),
            overwrites = overwrites,
            fields = fields + mapOf(MaterialConsumerExtension::headerKey.name to Text(masterDataKey)),
            entityId = entityId,
        )

    @Nocalc
    fun <K : FieldResultStar> createField(
        type: KClass<K>,
        value: Any,
        unit: TypeUnit? = null,
    ): K = FieldFactory.createField(type, value, unit)

    @Nocalc
    fun getField(fieldKey: String?): FieldWithResult? =
        if (fieldKey == null) {
            null
        } else {
            fieldWithResults.find {
                it.name.name == fieldKey
            }
        }

    @Nocalc
    fun getFields(vararg fieldKeys: String): Map<String, FieldWithResult> {
        val fieldKeySet = fieldKeys.toMutableSet()
        val result = mutableMapOf<String, FieldWithResult>()
        for (element in fieldWithResults) {
            if (fieldKeySet.contains(element.name.name)) {
                fieldKeySet.remove(element.name.name)
                result[element.name.name] = element
            }
            if (fieldKeySet.isEmpty()) {
                return result
            }
        }
        return result
    }

    /**
     * Sets [FieldResult.SOURCE.R] in the given field, triggering a recalculation for this field and its dependencies
     * in the next calculation request.
     * */
    @Nocalc
    fun resetField(fieldKey: String?): FieldWithResult? =
        if (fieldKey == null) {
            null
        } else {
            fieldWithResults
                .find {
                    it.name.name == fieldKey
                }?.apply {
                    result.source = FieldResult.SOURCE.R
                }
        }

    fun removeFieldResult(name: String) {
        fieldWithResults.removeIf { it.name.name == name }
    }

    @Nocalc
    fun getFieldResultMap(): Map<String, FieldResultStar> = fieldWithResults.getFieldResultMap()

    @Nocalc
    fun getInitialFieldResultMap(): Map<String, FieldResultStar> = initialFieldsWithResults.getFieldResultMap()

    @Nocalc
    fun getFieldResult(fieldKey: String?): FieldResultStar? = getField(fieldKey)?.result

    @Nocalc
    fun getFieldResults(vararg fieldKeys: String): Map<String, FieldResultStar> = getFields(*fieldKeys).mapValues { it.value.result }

    /**
     * Return the result as FieldResultStar and convert to null, if it's the internal null value
     */
    @Nocalc
    fun getFieldResultSafe(fieldKey: String?): FieldResultStar? {
        val res = getFieldResult(fieldKey)
        return if (res !is Null) {
            res
        } else {
            null
        }
    }

    /**
     * Return the result as FieldResultStar and convert to null, if it's the internal null value
     */
    @Nocalc
    inline fun <reified T : FieldResultStar> getFieldResultSafeCast(fieldKey: String?): T? {
        val res = getFieldResult(fieldKey)
        return if (res !is Null && res is T) {
            res
        } else {
            null
        }
    }

    /**
     * Replace the result for the named field, with the help of the replaceWith function.
     * @return the newly created FieldWithResult or null if there were no value in the fieldWithResults list.
     */
    @Nocalc
    fun replaceFieldResult(
        fieldName: String,
        replaceWith: (FieldResultStar) -> FieldResultStar,
    ) = fieldWithResults.replaceFieldResult(fieldName, replaceWith)

    @Nocalc
    inline fun <A, reified B : FieldResult<A, B>> replaceTypedFieldResult(
        fieldName: String,
        replaceWith: (B) -> FieldResult<A, B>,
    ) = fieldWithResults.replaceFirst({ it.name.name == fieldName }) { oldFieldResult ->
        if (oldFieldResult.result is B) {
            FieldWithResult(
                name = oldFieldResult.name,
                result = replaceWith(oldFieldResult.result),
            )
        } else {
            oldFieldResult
        }
    }

    @Nocalc
    fun replaceInitialFieldResult(
        fieldName: String,
        replaceWith: (FieldResultStar) -> FieldResultStar,
    ) = initialFieldsWithResults.replaceFieldResult(fieldName, replaceWith)

    /**
     * Replace the internal res of the named field result, to the given value.
     */
    @Nocalc
    fun <S, T : FieldResult<S, T>> replaceFieldResultValue(
        name: String,
        value: S,
    ) = fieldWithResults.replaceFieldResultValue<S, T>(name, value)

    @Nocalc
    fun replaceOrAddFieldWithResult(
        fieldName: String,
        replaceWith: (FieldWithResult?) -> FieldWithResult,
    ) = fieldWithResults.replaceFirst({ it.name.name == fieldName }, replaceWith) ?: fieldWithResults.add(
        replaceWith(null),
    )

    @Nocalc
    fun replaceOrAddFieldWithResult(
        fieldName: String,
        fieldWithResult: FieldWithResult,
    ) = replaceOrAddFieldWithResult(fieldName) { _ -> fieldWithResult }

    /**
     * Replace the result for the named field, with the help of the replaceWith function, or create new FieldWithResult if there were no previous result.
     * @return the newly created FieldWithResult
     */
    @Nocalc
    fun replaceOrAddFieldResult(
        fieldName: String,
        replaceWith: (FieldResultStar?) -> FieldResultStar,
    ) = fieldWithResults.replaceOrAddFieldResult(fieldName, replaceWith, ::createKeyFor)

    /**
     * Replace the result for the named field and set the version of the field to the current entity version.
     * If the field did not yet exist, a new field is created.
     *
     * @return The new [FieldWithResult]
     */
    @Nocalc
    fun addOrUpdateField(
        fieldName: String,
        replaceWith: (FieldResultStar?) -> FieldResultStar,
    ): FieldWithResult =
        fieldWithResults
            .replaceFirst({ it.name.name == fieldName }) {
                FieldWithResult(replaceWith(it.result), it.name.copy(version = this.version))
            } ?: addFieldResult(fieldName, replaceWith)

    @Nocalc
    fun replaceOrAddInitialFieldResult(
        fieldName: String,
        replaceWith: (FieldResultStar?) -> FieldResultStar,
    ) = initialFieldsWithResults.replaceOrAddFieldResult(fieldName, replaceWith, ::createKeyFor)

    @Nocalc
    fun addFieldResult(
        fieldName: String,
        replaceWith: (FieldResultStar?) -> FieldResultStar,
    ) = fieldWithResults.addFieldResult(fieldName, replaceWith, ::createKeyFor)

    @Nocalc
    fun addFieldResultWithOriginalVersion(
        fieldName: String,
        oldFieldWithResults: List<FieldWithResult> = emptyList(),
        replaceWith: (FieldResultStar?) -> FieldResultStar,
    ) = fieldWithResults.addFieldResultWithOriginalVersion(
        fieldName,
        replaceWith,
        ::createKeyWithOriginalVersion,
        oldFieldWithResults,
    )

    @Nocalc
    fun addInitialFieldResult(
        fieldName: String,
        replaceWith: (FieldResultStar?) -> FieldResultStar,
    ) = initialFieldsWithResults.addFieldResult(fieldName, replaceWith, ::createKeyFor)

    @Nocalc
    fun getInitialField(fieldKey: String): FieldWithResult? =
        initialFieldsWithResults.find {
            it.name.name == fieldKey
        }

    @Nocalc
    fun getInitialFieldResult(fieldKey: String): FieldResultStar? = getInitialField(fieldKey)?.result

    @Nocalc
    fun getFieldOrInitialFieldResult(fieldKey: String): FieldResultStar? = getFieldResult(fieldKey) ?: getInitialFieldResult(fieldKey)

    @Nocalc
    fun getEntityType() = getEntityTypeAnnotationOrThrow().toString()

    @Nocalc
    fun getEntityClass(): String = this.javaClass.simpleName

    @Nocalc
    fun getEntityTypeAnnotation(): Entities? = cachedEntityTypeAnnotation

    private val cachedEntityTypeAnnotation: Entities? = getEntityTypeAnnotationInternal()

    private fun getEntityTypeAnnotationInternal(): Entities? {
        val maybeEntities =
            entityTypMapping.getOrPut(this::class) { Optional.ofNullable(this::class.findAnnotation<EntityType>()?.name) }
        return maybeEntities.orElse(null)
    }

    @Nocalc
    fun isModularized(): Boolean = this::class.findAnnotation<Modularized>() != null

    @Nocalc
    fun isCopyDisabledInModularizedContext(): Boolean = this::class.findAnnotation<DisableEntityCopyInModularizedContext>() != null

    @Nocalc
    fun getEntityTypeAnnotationOrThrow() =
        getEntityTypeAnnotation()
            ?: throw Exception("entity type missing for '${this::class}'.")

    @Nocalc
    fun findChild(func: (ManufacturingEntity) -> Boolean): ManufacturingEntity? {
        if (func(this)) {
            return this
        } else {
            for (child in children) {
                val result = child.findChild(func)
                if (result != null) {
                    return result
                }
            }
            return null
        }
    }

    @Nocalc
    fun findChild(
        func: (ManufacturingEntity) -> Boolean,
        filter: (ManufacturingEntity) -> Boolean,
    ): ManufacturingEntity? {
        if (func(this)) {
            return this
        } else {
            for (child in children.filter(filter)) {
                val result = child.findChild(func, filter)
                if (result != null) {
                    return result
                }
            }
            return null
        }
    }

    /**
     * Return a list of entity ids, for the current and all the children entities.
     */
    @Nocalc
    fun flatChildrenIds(): List<String> = children.flatMap { it.flatChildrenIds() } + entityId

    @Nocalc
    fun findByEntityId(entityId: String): ManufacturingEntity? = findChild { it.entityId == entityId }

    @Nocalc
    fun findByEntityName(entityName: String): ManufacturingEntity? = findChild { it.name == entityName }

    @Nocalc
    fun findByEntityRef(entityRef: String): ManufacturingEntity? = findChild { it.entityRef == entityRef }

    @Nocalc
    fun findByEntityType(entityType: Entities): ManufacturingEntity? = findChild { it.getEntityTypeAnnotation() == entityType }

    @Nocalc
    fun findLowestByEntityType(entityType: Entities): ManufacturingEntity? {
        val x = ArrayDeque(listOf(this))
        var result: ManufacturingEntity? = null
        while (x.isNotEmpty()) {
            val next = x.removeFirst()
            x.addAll(next.children)
            if (next.getEntityTypeAnnotationOrThrow() == entityType) {
                result = next
            }
        }
        return result
    }

    /**
     * Returns either the entityRef or the name, if entityRef is not present.
     */
    @Nocalc
    fun entityRefForFieldKey(): String = entityRef ?: name

    /**
     * return a list of ManufacturingEntity provided by the given field - only the direct children are considered.
     */
    @Nocalc
    fun findChildrenProvidedBy(providerField: FieldIdKey): List<ManufacturingEntity> = children.filter { providerField == it.providerField }

    @Nocalc
    fun findChildrenByProvidedByOrCreatedBy(either: Either<FieldIdKey, FieldKey>): List<ManufacturingEntity> =
        either.fold(
            {
                findChildrenProvidedBy(it)
            },
            {
                findChildrenCreatedBy(it.toMatchKey())
            },
        )

    /**
     * return a list of ManufacturingEntity ids provided by the given field - recursively.
     */
    @Nocalc
    fun findCollectRecursivelyEntitiesProvidedBy(
        provider: FieldIdKey,
        allChildren: Boolean = false,
    ): List<String> =
        when {
            allChildren -> collectChildrenIds()
            (providerField == provider) ->
                listOf(entityId) +
                    children.flatMap {
                        it.findCollectRecursivelyEntitiesProvidedBy(
                            provider,
                            allChildren = false,
                        )
                    }

            (providerField != null) -> emptyList()
            else -> collectChildrenIds()
        }

    @Nocalc
    private fun collectChildrenIds(): List<String> =
        listOf(entityId) +
            children.flatMap {
                it.collectChildrenIds()
            }

    /**
     * return a list of ManufacturingEntity created by the given field - only the direct children are considered.
     */
    @Nocalc
    fun findChildrenCreatedBy(createdBy: MatchKey): List<ManufacturingEntity> = children.filter { createdBy.isMatching(it.createdBy) }

    val displayName: String?
        get() =
            (this.getFieldOrInitialFieldResult("displayDesignation")?.res) as String?

    @Nocalc
    fun <X> visitChildren(func: (ManufacturingEntity, Int) -> X?): List<X> = this.visitTree(func) { it.children }

    @Nocalc
    fun <X, Y> visitChildren2(func: (ManufacturingEntity, Int) -> Pair<X?, Y?>): Pair<List<X>, List<Y>> =
        this.visitTree2(func) { it.children }

    @Nocalc
    fun getRootBomNodeId(): BomNodeId? {
        synchronized(this) {
            val rbm = rootBomNodeId
            if (rbm == null) {
                val resolved =
                    if (bomNodeId != null) {
                        bomNodeId
                    } else if (parents.isNullOrEmpty()) {
                        null
                    } else {
                        parents[0].getRootBomNodeId()
                    }
                rootBomNodeId = Optional.ofNullable(resolved)
            }
            return rootBomNodeId!!.orElse(null)
        }
    }

    @Nocalc
    fun getLocalManufacturingId(): ObjectId? = getLocalManufacturing()?._id

    @Nocalc
    fun getLocalManufacturing(): ManufacturingEntity? {
        synchronized(this) {
            return if (getEntityTypeAnnotation() == Entities.MANUFACTURING) {
                this
            } else if (parents.isEmpty()) {
                null
            } else {
                parents[0].getLocalManufacturing()
            }
        }
    }

    @Nocalc
    fun getBomNodeSnapshot(): BomNodeSnapshot? = snapshot ?: parents.firstOrNull()?.getBomNodeSnapshot()

    @Nocalc
    fun findParent(pred: (ManufacturingEntity) -> Boolean): ManufacturingEntity? =
        if (pred(this)) {
            this
        } else {
            parents
                .asSequence()
                .mapNotNull { it.findParent(pred) }
                .firstOrNull()
        }

    @Nocalc
    fun getLinkedStep(entityClasses: List<KClass<*>>? = null): EntityRef? =
        if (isolated) {
            null
        } else {
            fun predicate(entity: ManufacturingEntity): Boolean =
                if (entityClasses == null) {
                    entity.getEntityType() == Entities.MANUFACTURING_STEP.name
                } else {
                    entity.getEntityClass() in entityClasses.map { clazz -> clazz.java.simpleName }
                }

            val linkedStep = findParent { predicate(it) } ?: findInTree({ predicate(it) }, { it.children })
            linkedStep?.let { EntityRef(it.entityId) }
        }

    @Nocalc
    fun getLinkedStep(entityClass: KClass<*>): EntityRef? = getLinkedStep(listOf(entityClass))

    /**
     * @return all the direct parents, or the parents that are isolated too.
     */
    @Nocalc
    fun parentList(justIsolated: Boolean = false): List<ManufacturingEntity> =
        if (justIsolated) {
            parents.filter { it.isolated }
        } else {
            parents
        }

    @Nocalc
    fun findParentWithEntityId(manufacturingEntityId: String): ManufacturingEntity? {
        return if (this.entityId == manufacturingEntityId) {
            this
        } else {
            for (parent in parents) {
                val found = parent.findParentWithEntityId(manufacturingEntityId)
                if (found != null) {
                    return found
                }
            }
            null
        }
    }

    @Nocalc
    private fun findParentWithEntityType(
        entityTypes: List<Entities>,
        currentDepth: Int,
        maxDepth: Int,
    ): ManufacturingEntity? {
        val maybeEntity: Entities? = entityTypes.find { this.getEntityTypeAnnotation() == it }
        return if (maybeEntity != null) {
            this
        } else if (currentDepth >= maxDepth) {
            null
        } else {
            for (parent in parents) {
                val found = parent.findParentWithEntityType(entityTypes, currentDepth + 1, maxDepth)
                if (found != null) {
                    return found
                }
            }
            null
        }
    }

    @Nocalc
    fun findParentWithEntityType(entityTypes: List<Entities>): ManufacturingEntity? =
        findParentWithEntityType(entityTypes, 0, Int.MAX_VALUE)

    @Nocalc
    fun findParentWithEntityType(
        entityType: Entities,
        maxDepth: Int,
    ): ManufacturingEntity? = findParentWithEntityType(listOf(entityType), 0, maxDepth)

    @Nocalc
    fun createEntitiesFromKnowledgeTemplate(
        externalStorageService: ExternalStorageService,
        templateWithSkillType: KnowledgeEntityData,
        entityType: Entities,
    ): Flux<ManufacturingEntity> {
        // migrate template: replace SkillType by Text for all fields
        val template = templateWithSkillType.replaceSkillTypeBecauseWeHaveNoLazyMigrationForNoCalcData()

        fun getClassNameForEntity(template: KnowledgeEntityData): String {
            val classNamne =
                template.fields.values
                    .find { it.type == ClassSelector::class.java.simpleName }
                    ?.value
            if (classNamne != null) {
                return classNamne.toString()
            }

            val entityType = Entities.valueOf(template.type)

            if (entityType == Entities.CYCLETIME_STEP_GROUP) {
                return CycleTimeStepGroupFromKnowledge::class.simpleName!!
            }

            return entityType.clazz?.java?.simpleName!!
        }

        return Flux
            .fromIterable(
                template.children.filter {
                    it.type == entityType.toString()
                },
            ).concatMap {
                externalStorageService.persist(it).map { id -> Pair(id, it) }
            }.concatMap { (id, template) ->

                val entityId = ObjectId()
                val templateEntityField =
                    mapOf(
                        "templateEntity" to
                            NoCalcFieldResult(id, template)
                                .withSource(FieldResult.SOURCE.M),
                    )

                val createdEntity =
                    if (template.masterDataKey != null) {
                        createEntityWithMasterdata(
                            name = template.key,
                            entityType = Entities.valueOf(template.type),
                            masterDataKey = template.masterDataKey.key,
                            masterDataType = template.masterDataKey.type,
                            fields = toFieldMap(template.fields) + templateEntityField,
                            clazz = getClassNameForEntity(template),
                            entityId = entityId,
                        )
                    } else {
                        val fields = toFieldMap(template.fields) + templateEntityField
                        Mono.just(
                            createEntity(
                                name = template.key,
                                entityType = Entities.valueOf(template.type),
                                fields = fields + FieldResultUtils.overrideBaseCurrency(fields, fields),
                                clazz = EntityClassOrName.second(getClassNameForEntity(template)),
                                entityId = entityId,
                            ),
                        )
                    }
                createdEntity.flatMap {
                    // this is a hack and is accepted for now
                    // with normal inheritance the ManufacturingEntity should get a virtual method
                    // and Labor could override the method and do some special stuff
                    // and all inherited classes will behave like the Labor
                    // ManufacturingEntity <- Labor <- PlasticPaintingLabor
                    //
                    // in nbk the inheritance is different
                    // ManufacturingEntity <- Labor      <---------------------|
                    //                     <- PlasticPaintingLabor---extends---|
                    // because of this homegrown inheritance concept the pattern of virtual methods is not working
                    SkillTypeConsumerHelper.afterTemplateCreated(
                        it,
                        services.calculationContextService,
                        services.accessCheck,
                    )
                }
            }
    }

    @Nocalc
    fun <T> getFieldFromKnowledgeTemplate(
        template: KnowledgeEntityData,
        fieldName: String,
    ): T? =
        template.fields[fieldName]?.let {
            services.fieldFactoryService().toFieldResult(it)
        } as T

    @Nocalc
    private fun toFieldMap(fields: Map<String, KnowledgeFieldData>): Map<String, FieldResultStar> =
        fields
            .mapValues { (_, field) ->
                services.fieldFactoryService().toFieldResult(field)
            }.toMap()

    @Nocalc
    fun addChild(
        entityToInsert: ManufacturingEntity,
        insertBeforeEntity: ManufacturingEntity? = null,
        insertAfterEntity: ManufacturingEntity? = null,
    ) {
        if (children.contains(entityToInsert)) {
            error(
                "duplicate: ${entityToInsert.toHumanReadableName()} in ${children.joinToString(", ") { it.toHumanReadableName() }}",
            )
        }
        if (insertBeforeEntity == null && insertAfterEntity == null) {
            children.add(entityToInsert)
        } else if (insertBeforeEntity != null) {
            addChild(children.indexOf(insertBeforeEntity), entityToInsert)
        } else if (insertAfterEntity != null) {
            addChild(children.indexOf(insertAfterEntity) + 1, entityToInsert)
        }
    }

    @Nocalc
    fun containChild(child: ManufacturingEntity) = children.contains(child)

    @Nocalc
    fun addChild(
        at: Int,
        entityToInsert: ManufacturingEntity,
    ) {
        children.add(at, entityToInsert)
    }

    @Nocalc
    fun addChildren(entities: Collection<ManufacturingEntity>) {
        children.addAll(entities)
    }

    @Nocalc
    fun removeChild(entity: ManufacturingEntity) {
        children.remove(entity)
    }

    @Nocalc
    fun removeChildDepsToEntities(entityIds: Collection<String>) {
        childDeps =
            childDeps.filter { (_, field) ->
                !entityIds.contains(field.entity.entityId)
            }
        parents.forEach {
            it.removeChildDepsToEntities(entityIds = entityIds)
        }
    }

    @Nocalc
    fun addParent(newParent: ManufacturingEntity) {
        synchronized(this) {
            parents.add(newParent)
            invalidateChilRootBomNodeId()
        }
    }

    /**
     * Gets an information if entity has a parent
     *
     * @return true for any existing parent
     */
    @Nocalc
    fun hasParent() = parents.isNotEmpty()

    /**
     * If the entity doesn't have a parent, and newParent is not null, set as a parent.
     */
    @Nocalc
    fun ensureParent(newParent: ManufacturingEntity?) {
        synchronized(this) {
            if (newParent != null && parents.isEmpty()) {
                parents.add(newParent)
                invalidateChilRootBomNodeId()
            }
        }
    }

    @Nocalc
    fun addParents(newParents: Collection<ManufacturingEntity>) {
        synchronized(this) {
            parents.addAll(newParents)
            invalidateChilRootBomNodeId()
        }
    }

    @Nocalc
    private fun invalidateChilRootBomNodeId() {
        rootBomNodeId = null
        for (child in children) {
            child.invalidateChilRootBomNodeId()
        }
    }

    @Nocalc
    open fun getOptionalPartId(): Optional<ObjectId> = Optional.empty<ObjectId>()

    @Nocalc
    fun treeStructure(): String =
        this
            .visitChildren { entity, depth ->
                "  ".repeat(depth) + entity.toHumanReadableName()
            }.joinToString(separator = "\n")

    @Nocalc
    fun toHumanReadableName(): String =
        ToStringBuilder(this, NOT_NULL_STYLE)
            .append("id", entityId)
            .append("ref", entityRef)
            .append("name", name)
            .append("type", getEntityTypeAnnotation())
            .append("displayName", displayName)
            .append("createdBy", createdBy?.toMatchKey())
            .append("providerField", providerField)
            .toString()

    fun updateVersion(newVersion: Int) {
        if (oldVersion == null) {
            oldVersion = version
        }
        version = newVersion
    }

    @Nocalc
    fun getAttachments() = children.filter { it.getEntityTypeAnnotation() == Entities.ATTACHMENT }

    @Nocalc
    fun getImage() =
        getAttachments()
            .filter { attachment ->
                (attachment.getFieldResult(Attachment::isMainImage.name) as Bool?)?.res == true
            }.mapNotNull { attachment -> (attachment.getFieldResult(Attachment::fileId.name) as Text?)?.res }

    @Nocalc
    fun getExchangeRates(): ManufacturingEntity? = findByEntityType(Entities.EXCHANGE_RATES)

    /**
     * Returns the exchange rate map local to this bom node.
     * Ensure that parent links are set when not calling it on a manufacturing.
     */
    @Nocalc
    fun getExchangeRateMap(ccyFilter: List<Currency>? = null): ExchangeRateMap {
        check(getEntityTypeAnnotation() == Entities.MANUFACTURING || parents.isNotEmpty()) {
            "Exchange rates are never present on a non-manufacturing without parent links"
        }
        val manu = findParentWithEntityType(listOf(Entities.MANUFACTURING))
        val filter =
            if (ccyFilter != null && manu != null) {
                val baseCurrencies =
                    manu.visitChildren { child, _ ->
                        child
                            .getFieldResult(
                                FieldExtractionUtils.BASE_CURRENCY_FIELD_NAME,
                            )?.res
                            ?.toString()
                            ?.let { Currency(it) }
                    }
                baseCurrencies + ccyFilter
            } else {
                null
            }
        return manu
            ?.getFieldResult(FieldExtractionUtils.EXCHANGE_RATES_FIELD_NAME)
            ?.tryCast<ExchangeRatesField>()
            ?.res
            .let(ExchangeRateMap::fromMongo)
            .filter { filter?.contains(it) ?: true }
    }

    /**
     * Copies this entity to the base entity (retains links, non-recursive).
     * It means, e.g. [ManufacturingDieCasting] -> [ManualManufacturing] or [RawMaterialPowder] -> [Material] ...
     * All the fields from [fieldWithResults] are marked as input and moved to [initialFieldsWithResults].
     * The [_id] in newly created object is preserved.
     *
     * @param args new entity arguments (optional)
     * @return new instance of base entity with copied values
     */
    @Nocalc
    open fun copyToBase(
        args: Map<String, Any?> = mapOf(),
        hashProvider: ((c: Class<out ManufacturingEntity>) -> EntityHash?)? = null,
    ): ManufacturingEntity {
        val entityType = getEntityTypeAnnotationOrThrow()
        val clazz = entityType.manualClazz?.java ?: entityType.clazz?.java ?: this::class.java

        return copy(clazz, _id, hashProvider, args).apply {
            val inputFields =
                fieldWithResults
                    .map { it.withSource(FieldResult.SOURCE.I) }
                    .filterNot { resultField -> initialFieldsWithResults.any { it.name.name == resultField.name.name } }
            initialFieldsWithResults.addAll(inputFields)
        }
    }

    /**
     * Copies this entity (retains links, non-recursive).
     *
     * @param args overrides (optional)
     * @return new instance of entity with copied values
     */
    @Nocalc
    open fun copy(
        args: Map<String, Any?> = mapOf(),
        newId: ObjectId? = null,
    ): ManufacturingEntity = copy(this::class.java, newId, null, args)

    @Nocalc
    private fun copy(
        clazz: Class<out ManufacturingEntity>,
        newId: ObjectId? = null,
        hashProvider: ((c: Class<out ManufacturingEntity>) -> EntityHash?)? = null,
        args: Map<String, Any?>? = null,
    ): ManufacturingEntity {
        val entityType =
            this.getEntityTypeAnnotationOrThrow()

        val oldArgs = this.extractArgs()
        val newArgs = if (args != null) oldArgs + args else oldArgs

        val copiedEntity = entityType.factory.create(clazz, this.name, newArgs)

        if (newId != null) {
            copiedEntity._id = newId
        }

        copiedEntity.version = this.version
        copiedEntity.initVersion = this.initVersion
        copiedEntity.previouslyCalculatedVersionHash = this.previouslyCalculatedVersionHash
        copiedEntity.dynamicFields = this.dynamicFields.toMutableMap()
        copiedEntity.isolated = this.isolated
        copiedEntity.setVersionHashes(
            hashProvider ?: { c -> findCurrentImplementationHashInTree(c) },
            this.savedExtensionHashes,
        )

        copiedEntity.fieldWithResults =
            this.fieldWithResults
                .map { fieldWithResult ->
                    fieldWithResult.copy(entity = copiedEntity)
                }.toMutableList()
        copiedEntity.initialFieldsWithResults =
            this.initialFieldsWithResults
                .map { it.copy(entity = copiedEntity) }
                .filter { it.name.name != "exchangeRates" }
                .toMutableList()

        copiedEntity.parents = this.parents
        copiedEntity.children = this.children

        copiedEntity.masterDataObjectId = this.masterDataObjectId
        copiedEntity.masterDataSelector = this.masterDataSelector
        copiedEntity.masterDataVersion = this.masterDataVersion

        copiedEntity.createdBy = this.createdBy
        copiedEntity.entityRef = this.entityRef
        copiedEntity.createdByMocked = this.createdByMocked
        copiedEntity.providerField = this.providerField
        copiedEntity.extracted = this.extracted

        return copiedEntity
    }

    private fun findCurrentImplementationHashInTree(c: Class<out ManufacturingEntity>): EntityHash? =
        if (this::class.java == c) {
            currentImplementationVersionHash
        } else {
            (extensionEntities + behaviours + extends).firstNotNullOfOrNull {
                it?.findCurrentImplementationHashInTree(
                    c,
                )
            }
        }

    /**
     * Sets all parent links of this entity to [parent].
     * */
    @Nocalc
    open fun setParent(parent: ManufacturingEntity?) {
        this.parents = if (parent != null) mutableListOf(parent) else mutableListOf()
    }

    /**
     * Advises if an entity is deletable and copiable.
     *
     * Generated cost module entities are tightly coupled to their environment and thus can neither be deleted nor copied
     * */
    @Nocalc
    fun canBeCopied(): Boolean =
        if (isolated || isModularized()) {
            true
        } else {
            canBeDeleted()
        }

    /**
     * Advises if an entity is deletable.
     *
     * Generated cost module entities are tightly coupled to their environment and thus can neither be deleted nor copied
     * */
    @Nocalc
    fun canBeDeleted(): Boolean {
        // certain entity types instantiate their children via the entity creation mechanism without being restricted
        // to a cost module context, therefore these entities can be copied and deleted freely
        val generatedByNonCostModuleParent =
            when (this.parents.find { this.createdBy?.fieldIdKey()?.entityId == it.entityId }) {
                null -> false
                is KnowledgeManufacturingStep,
                is CycleTimeStepGroupFromKnowledge,
                is ManualManufacturingStep,
                -> true

                else -> false
            }

        return if (generatedByNonCostModuleParent) {
            true
        } else {
            // manually added manufacturing steps in cost module calculations are act as generated entities and have
            // createdBy != null && createdByMocked - and these can be copied and deleted as well
            // all other generated entities i.e. createdBy != null && !createdByMocked cannot be deleted or copied
            this.createdBy == null || this.createdByMocked
        }
    }

    @Nocalc
    fun findCreatedByRecursivly(): FieldKey? {
        if (createdBy != null) return createdBy
        return parents.firstNotNullOfOrNull { it.findCreatedByRecursivly() }
    }

    // region entity version hashes

    data class EntityToHash(
        val entityKClass: KClass<out ManufacturingEntity>,
        val entityHash: EntityHash,
    ) {
        fun toPersistentRepresentation(): Pair<String, String> = Pair(entityKClass.simpleName!!, entityHash.hash)

        companion object {
            fun create(
                className: String,
                hash: String,
                kClass: (className: String) -> KClass<out ManufacturingEntity>,
            ): EntityToHash =
                EntityToHash(
                    kClass(className),
                    EntityHash(hash),
                )
        }
    }

    @Nocalc
    fun setVersionHashes(
        entityManager: EntityManager? = null,
        savedHashes: Map<KClass<out ManufacturingEntity>, EntityHash>? = null,
    ) {
        setVersionHashes({ c -> entityManager?.getEntityHash(c) }, savedHashes)
    }

    private fun setVersionHashes(
        hashProvider: ((c: Class<out ManufacturingEntity>) -> EntityHash?),
        savedHashes: Map<KClass<out ManufacturingEntity>, EntityHash>? = null,
    ) {
        this.savedExtensionHashes = savedHashes
        this.currentImplementationVersionHash = currentImplementationVersionHash
            ?: hashProvider(this::class.java)
            ?: error("Entity ${this::class.simpleName} must be part of entitymanager's entityinfo")

        this.previouslyCalculatedVersionHash = this.savedExtensionHashes?.get(this::class)

        this.extends?.setVersionHashes(hashProvider, this.savedExtensionHashes)
        this.behaviours.forEach { it.setVersionHashes(hashProvider, this.savedExtensionHashes) }
    }

    fun getAllHashesToSave(): List<EntityToHash> {
        val behaviourHashes = this.behaviours.flatMap { it.getAllHashesToSave() }
        val extensionHashes = this.extensionEntities.flatMap { it.getAllHashesToSave() }
        val extendsHash = this.extends?.getAllHashesToSave() ?: listOf()

        val previouslyCalculatedVersionHash = this.previouslyCalculatedVersionHash

        val myHash =
            previouslyCalculatedVersionHash?.let {
                listOf(EntityToHash(this::class, previouslyCalculatedVersionHash))
            } ?: listOf()

        return (myHash + extendsHash + extensionHashes + behaviourHashes)
    }

    @Nocalc
    fun updateHashesAfterCalculation() {
        this.visitChildren { entity, _ ->
            entity.behaviours.forEach { it.updateHashesAfterCalculation() }
            entity.extensionEntities.forEach { it.updateHashesAfterCalculation() }
            entity.extends?.updateHashesAfterCalculation()
            entity.previouslyCalculatedVersionHash = entity.currentImplementationVersionHash
        }
    }

    // endregion

    @Nocalc
    fun toEntityContext(
        entityId: ObjectId = _id,
        ext: EntityCreation.Ext? = null,
    ): EntityContext =
        EntityContext(
            entityName = name,
            entityClass = this.javaClass.simpleName,
            entityId = entityId,
            displayName = ext?.displayName ?: this.displayName,
            entityPath = getPath(),
            partInfo = ext?.partInfo,
            parentEntityType = ext?.parentEntityType,
            parentDisplayName = ext?.parentDisplayName,
        )

    @Nocalc
    fun createEntityDeletionExt(parentId: ObjectId?): EntityDeletion.Ext =
        EntityDeletion.Ext(
            entityName = name,
            entityClass = this.javaClass.simpleName,
            displayName = displayName,
            entityPath = getPath(),
            entityType = getEntityType(),
            parentId = parentId,
        )

    @Nocalc
    fun manufacturingEntityHierarchy(): List<ManufacturingEntity> {
        val entityHierarchy = ArrayDeque<ManufacturingEntity>()
        var entity: ManufacturingEntity? = this
        while (entity != null) {
            entityHierarchy.addFirst(entity)
            entity = entity.extends
        }
        return entityHierarchy
    }

    @Nocalc
    inline fun <reified T : ManufacturingEntity> isSubTypeOf(): Boolean =
        manufacturingEntityHierarchy()
            .map {
                it.getEntityClass()
            }.contains(T::class.simpleName)

    @Nocalc
    fun getParentFieldIdentifier(): Text? = getInitialFieldResult(MaterialGeometry::parentFieldIdentifier.name) as Text?

    @Nocalc
    fun getResponsibleUser(): Text? = getFieldResultSafeCast<Text>("responsible")
}
