package com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.fieldmapping

import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.entityfieldmetainfo.DynamicDenominatorUnitEntityFieldMetaInfo
import com.nu.bom.core.manufacturing.annotations.entityfieldmetainfo.EntityFieldMetaInfo
import com.nu.bom.core.manufacturing.annotations.entityfieldmetainfo.ObjectViewEntityFieldMetaInfo
import com.nu.bom.core.manufacturing.annotations.entityfieldmetainfo.ReadOnlyEntityFieldMetaInfo
import com.nu.bom.core.manufacturing.commercialcalculation.common.AggregationLevelAndRole
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationRole
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.legacyfieldnames.BaseToolLegacyFieldNames
import com.nu.bom.core.manufacturing.commercialcalculation.legacyfieldnames.BomEntryLegacyFieldNames
import com.nu.bom.core.manufacturing.commercialcalculation.legacyfieldnames.ConsumablesLegacyFieldNames
import com.nu.bom.core.manufacturing.commercialcalculation.legacyfieldnames.ElectronicComponentLegacyFieldNames
import com.nu.bom.core.manufacturing.commercialcalculation.legacyfieldnames.MachineLegacyFieldNames
import com.nu.bom.core.manufacturing.commercialcalculation.legacyfieldnames.ManualBaseMaterialLegacyFieldNames
import com.nu.bom.core.manufacturing.commercialcalculation.legacyfieldnames.ManualMasterDataMaterialLegacyFieldNames
import com.nu.bom.core.manufacturing.commercialcalculation.legacyfieldnames.ManualMaterialLegacyFieldNames
import com.nu.bom.core.manufacturing.commercialcalculation.legacyfieldnames.ManufacturingLegacyFieldNames
import com.nu.bom.core.manufacturing.commercialcalculation.legacyfieldnames.ManufacturingStepLegacyFieldNames
import com.nu.bom.core.manufacturing.commercialcalculation.legacyfieldnames.RoughMachineLegacyFieldNames
import com.nu.bom.core.manufacturing.commercialcalculation.legacyfieldnames.RoughToolLegacyFieldNames
import com.nu.bom.core.manufacturing.commercialcalculation.legacyfieldnames.SpecialDirectCostLegacyFieldNames
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetActivityCalculationElementType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCO2eCalculationElementType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCalculationElementType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.tsetDefaultConfiguration.TsetCostCalculationElementType
import com.nu.bom.core.manufacturing.entities.BaseTool
import com.nu.bom.core.manufacturing.entities.BomEntry
import com.nu.bom.core.manufacturing.entities.Consumable
import com.nu.bom.core.manufacturing.entities.ElectronicComponent
import com.nu.bom.core.manufacturing.entities.Machine
import com.nu.bom.core.manufacturing.entities.ManualBaseMaterial
import com.nu.bom.core.manufacturing.entities.ManualMasterdataMaterial
import com.nu.bom.core.manufacturing.entities.ManualMaterial
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ManufacturingStep
import com.nu.bom.core.manufacturing.entities.RoughMachine
import com.nu.bom.core.manufacturing.entities.RoughManufacturing
import com.nu.bom.core.manufacturing.entities.RoughManufacturingStep
import com.nu.bom.core.manufacturing.entities.SpecialDirectCost
import com.nu.bom.core.manufacturing.entities.ToolRough
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import org.springframework.stereotype.Service
import kotlin.reflect.KClass

/***
 * This service creates the field configs to back map the dynamic fields to the old static fields.
 * This is a temporary needed service for backward compatibility until all downstream functions are capable to work
 * with the dynamic fields and will be removed later.
 */
@Suppress("DEPRECATION", "LongMethod", "TooManyFunctions")
@Service
class BackMappingFieldsBuilder : BaseMappingFieldsBuilder(
    createMappings(),
) {
    companion object {
        private fun createMappings(): Map<KClass<out ManufacturingEntity>, List<BackMapping>> =
            mapOf(
                Consumable::class to backMappingConsumable(),
                ManualMaterial::class to backMappingManualMaterial(),
                ManualMasterdataMaterial::class to backMappingManualMasterdataMaterial(),
                ElectronicComponent::class to backMappingElectronicComponent(),
                ManualBaseMaterial::class to backMappingManualBaseMaterial(),
                Machine::class to backMappingMachineCost() + backMappingMachineCO2(),
                RoughMachine::class to backMappingRoughMachine(),
                BaseTool::class to backMappingBaseTool(),
                ToolRough::class to backMappingRoughTool(),
                BomEntry::class to backMappingBomEntry(),
                Manufacturing::class to backMappingNotRoughManufacturing(),
                RoughManufacturing::class to backMappingRoughManufacturing(),
                ManufacturingStep::class to backMappingDetailedManufacturingStep(),
                RoughManufacturingStep::class to backMappingRoughManufacturingStep(),
                SpecialDirectCost::class to backMappingSpecialDirectCost(),
            )

        // region leaf-materials

        private fun backMappingConsumable(): List<BackMapping> =
            listOf(
                CostBackMapping(
                    ConsumablesLegacyFieldNames.COST_PER_PART.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MATERIAL_USAGE,
                        AggregationRole.THIS,
                    ),
                    TsetCostCalculationElementType.DIRECT_MATERIAL_COSTS,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        @Suppress("MagicNumber")
                        (
                            ObjectViewEntityFieldMetaInfo(
                                ObjectView.MATERIAL,
                                60,
                            )
                        ),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
                CO2BackMapping(
                    ConsumablesLegacyFieldNames.CO2_PER_PART.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MATERIAL_USAGE,
                        AggregationRole.THIS,
                    ),
                    TsetCO2eCalculationElementType.DIRECT_MATERIAL_CO2E,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
            )

        private fun backMappingManualMaterial(): List<BackMapping> =
            listOf(
                CostBackMapping(
                    ManualMaterialLegacyFieldNames.COST_PER_PART.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MATERIAL_USAGE,
                        AggregationRole.THIS,
                    ),
                    TsetCostCalculationElementType.DIRECT_MATERIAL_COSTS,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        @Suppress("MagicNumber")
                        (
                            ObjectViewEntityFieldMetaInfo(
                                ObjectView.MATERIAL,
                                70,
                            )
                        ),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
                CO2BackMapping(
                    ManualMaterialLegacyFieldNames.CO2_PER_PART.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MATERIAL_USAGE,
                        AggregationRole.THIS,
                    ),
                    TsetCO2eCalculationElementType.DIRECT_MATERIAL_CO2E,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
            )

        private fun backMappingManualMasterdataMaterial(): List<BackMapping> =
            listOf(
                CostBackMapping(
                    ManualMasterDataMaterialLegacyFieldNames.COST_PER_PART.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MATERIAL_USAGE,
                        AggregationRole.THIS,
                    ),
                    TsetCostCalculationElementType.DIRECT_MATERIAL_COSTS,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        @Suppress("MagicNumber")
                        (
                            ObjectViewEntityFieldMetaInfo(
                                ObjectView.MATERIAL,
                                70,
                            )
                        ),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
                CO2BackMapping(
                    ManualMasterDataMaterialLegacyFieldNames.CO2_PER_PART.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MATERIAL_USAGE,
                        AggregationRole.THIS,
                    ),
                    TsetCO2eCalculationElementType.DIRECT_MATERIAL_CO2E,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
            )

        private fun backMappingElectronicComponent(): List<BackMapping> =
            listOf(
                CostBackMapping(
                    ElectronicComponentLegacyFieldNames.COST_PER_PART.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MATERIAL_USAGE,
                        AggregationRole.THIS,
                    ),
                    TsetCostCalculationElementType.DIRECT_MATERIAL_COSTS,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
                CO2BackMapping(
                    ElectronicComponentLegacyFieldNames.CO2_PER_PART.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MATERIAL_USAGE,
                        AggregationRole.THIS,
                    ),
                    TsetCO2eCalculationElementType.DIRECT_MATERIAL_CO2E,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
            )

        private fun backMappingManualBaseMaterial(): List<BackMapping> =
            listOf(
                CostBackMapping(
                    ManualBaseMaterialLegacyFieldNames.COST_PER_PART.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MATERIAL_USAGE,
                        AggregationRole.THIS,
                    ),
                    TsetCostCalculationElementType.DIRECT_MATERIAL_COSTS,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
                CO2BackMapping(
                    ManualBaseMaterialLegacyFieldNames.CO2_PER_PART.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MATERIAL_USAGE,
                        AggregationRole.THIS,
                    ),
                    TsetCO2eCalculationElementType.DIRECT_MATERIAL_CO2E,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
            )

        // endregion

        // region tool

        private fun backMappingBaseTool(): List<BackMapping> =
            listOf(
                CostBackMapping(
                    BaseToolLegacyFieldNames.COST_PER_PART.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.TOOL,
                        AggregationRole.THIS,
                    ),
                    TsetCostCalculationElementType.TOOL_COSTS,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        @Suppress("MagicNumber")
                        (
                            ObjectViewEntityFieldMetaInfo(
                                ObjectView.TOOL,
                                120,
                            )
                        ),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
                CostBackMapping(
                    BaseToolLegacyFieldNames.ALLOCATED_COST_PER_PART.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.TOOL,
                        AggregationRole.THIS,
                    ),
                    TsetCostCalculationElementType.TOOL_ALLOCATION_AND_INTEREST_COSTS,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        @Suppress("MagicNumber")
                        (
                            ObjectViewEntityFieldMetaInfo(
                                ObjectView.TOOL,
                                150,
                            )
                        ),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
            )

        private fun backMappingRoughTool(): List<BackMapping> =
            listOf(
                CostBackMapping(
                    RoughToolLegacyFieldNames.COST_PER_PART.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.TOOL,
                        AggregationRole.THIS,
                    ),
                    TsetCostCalculationElementType.TOOL_COSTS,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        @Suppress("MagicNumber")
                        (
                            ObjectViewEntityFieldMetaInfo(
                                ObjectView.TOOL,
                                120,
                            )
                        ),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
                CO2BackMapping(
                    RoughToolLegacyFieldNames.CO2_PER_PART.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.TOOL,
                        AggregationRole.THIS,
                    ),
                    TsetCO2eCalculationElementType.TOOL_CO2E,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
            )

        // endregion

        // region machine

        private fun backMappingMachineCost(): List<CostBackMapping> {
            val metaInfo =
                listOf(
                    ReadOnlyEntityFieldMetaInfo(),
                    DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                )

            return listOf(
                CostBackMapping(
                    MachineLegacyFieldNames.SETUP_COST_PER_PART.fieldName,
                    AggregationLevelAndRole(AggregationLevel.MACHINE, AggregationRole.THIS),
                    TsetActivityCalculationElementType.SETUP_MACHINE_ACTIVITY,
                    metaInfo,
                ),
                CostBackMapping(
                    MachineLegacyFieldNames.COST_PER_PART.fieldName,
                    AggregationLevelAndRole(AggregationLevel.MACHINE, AggregationRole.THIS),
                    TsetActivityCalculationElementType.PRODUCTION_MACHINE_ACTIVITY,
                    metaInfo,
                ),
                // non occupancy
                createMachineFixedCostBackMapping(
                    MachineLegacyFieldNames.FIXED_COST_NON_OCCUPANCY_PER_PART,
                    TsetActivityCalculationElementType.NON_OCCUPANCY_MACHINE_ACTIVITY,
                    metaInfo,
                ),
                createMachineVariableCostBackMapping(
                    MachineLegacyFieldNames.VARIABLE_COST_NON_OCCUPANCY_PER_PART,
                    TsetActivityCalculationElementType.NON_OCCUPANCY_MACHINE_ACTIVITY,
                    metaInfo,
                ),
                createMachineCostBackMapping(
                    MachineLegacyFieldNames.COST_DURING_NON_OCCUPANCY_PER_PART,
                    TsetActivityCalculationElementType.NON_OCCUPANCY_MACHINE_ACTIVITY,
                    metaInfo,
                ),
                // production
                createMachineFixedCostBackMapping(
                    MachineLegacyFieldNames.FIXED_COST_DURING_PRODUCTION_PER_PART,
                    TsetActivityCalculationElementType.OCCUPANCY_MACHINE_ACTIVITY,
                    metaInfo,
                ),
                createMachineVariableCostBackMapping(
                    MachineLegacyFieldNames.VARIABLE_COST_DURING_PRODUCTION_PER_PART,
                    TsetActivityCalculationElementType.OCCUPANCY_MACHINE_ACTIVITY,
                    metaInfo,
                ),
                createMachineCostBackMapping(
                    MachineLegacyFieldNames.COST_DURING_PRODUCTION_PER_PART,
                    TsetActivityCalculationElementType.OCCUPANCY_MACHINE_ACTIVITY,
                    metaInfo,
                ),
                // setup
                createMachineFixedCostBackMapping(
                    MachineLegacyFieldNames.FIXED_COST_DURING_SETUP_PER_PART,
                    TsetActivityCalculationElementType.SETUP_MACHINE_ACTIVITY,
                    metaInfo,
                ),
                createMachineVariableCostBackMapping(
                    MachineLegacyFieldNames.VARIABLE_COST_DURING_SETUP_PER_PART,
                    TsetActivityCalculationElementType.SETUP_MACHINE_ACTIVITY,
                    metaInfo,
                ),
                createMachineCostBackMapping(
                    MachineLegacyFieldNames.COST_DURING_SETUP_PER_PART,
                    TsetActivityCalculationElementType.SETUP_MACHINE_ACTIVITY,
                    metaInfo,
                ),
            )
        }

        private fun createMachineFixedCostBackMapping(
            legacyFieldName: MachineLegacyFieldNames,
            activity: TsetActivityCalculationElementType,
            metaInfo: List<EntityFieldMetaInfo>,
        ) = CostBackMapping(
            legacyFieldName.fieldName,
            AggregationLevelAndRole(
                AggregationLevel.MACHINE,
                AggregationRole.THIS,
            ),
            activity,
            listOf(
                TsetCostCalculationElementType.MACHINE_DEPRECIATION_COSTS,
                TsetCostCalculationElementType.MACHINE_INTEREST_COSTS,
                TsetCostCalculationElementType.MACHINE_AREA_COSTS,
            ),
            metaInfo,
        )

        private fun createMachineVariableCostBackMapping(
            legacyFieldName: MachineLegacyFieldNames,
            activity: TsetActivityCalculationElementType,
            metaInfo: List<EntityFieldMetaInfo>,
        ) = CostBackMapping(
            legacyFieldName.fieldName,
            AggregationLevelAndRole(
                AggregationLevel.MACHINE,
                AggregationRole.THIS,
            ),
            activity,
            listOf(
                TsetCostCalculationElementType.MACHINE_ENERGY_COSTS,
                TsetCostCalculationElementType.MACHINE_OPERATION_SUPPLY_COSTS,
                TsetCostCalculationElementType.MACHINE_MAINTENANCE_COSTS,
            ),
            metaInfo,
        )

        private fun createMachineCostBackMapping(
            legacyFieldName: MachineLegacyFieldNames,
            activity: TsetActivityCalculationElementType,
            metaInfo: List<EntityFieldMetaInfo>,
        ) = CostBackMapping(
            legacyFieldName.fieldName,
            AggregationLevelAndRole(AggregationLevel.MACHINE, AggregationRole.THIS),
            activity,
            metaInfo,
        )

        private fun backMappingRoughMachine(): List<BackMapping> =
            listOf(
                CostBackMapping(
                    RoughMachineLegacyFieldNames.COST_PER_PART.fieldName,
                    AggregationLevelAndRole(AggregationLevel.MACHINE, AggregationRole.THIS),
                    TsetActivityCalculationElementType.PRODUCTION_MACHINE_ACTIVITY,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
                CO2BackMapping(
                    RoughMachineLegacyFieldNames.CO2_PER_PART.fieldName,
                    AggregationLevelAndRole(AggregationLevel.MACHINE, AggregationRole.THIS),
                    TsetActivityCalculationElementType.PRODUCTION_MACHINE_ACTIVITY,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
            )

        private fun backMappingMachineCO2(): List<CO2BackMapping> {
            val metaInfo =
                listOf(
                    ReadOnlyEntityFieldMetaInfo(),
                    DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                )

            return listOf(
                CO2BackMapping(
                    MachineLegacyFieldNames.CO2_PER_PART.fieldName,
                    AggregationLevelAndRole(AggregationLevel.MACHINE, AggregationRole.THIS),
                    TsetActivityCalculationElementType.PRODUCTION_MACHINE_ACTIVITY,
                    metaInfo,
                ),
                CO2BackMapping(
                    MachineLegacyFieldNames.FIXED_CO2_NON_OCCUPANCY_PER_PART.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MACHINE,
                        AggregationRole.THIS,
                    ),
                    TsetActivityCalculationElementType.NON_OCCUPANCY_MACHINE_ACTIVITY,
                    listOf(
                        TsetCO2eCalculationElementType.MACHINE_DEPRECIATION_CO2E,
                    ),
                    metaInfo,
                ),
                CO2BackMapping(
                    MachineLegacyFieldNames.FIXED_CO2_DURING_PRODUCTION_PER_PART.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MACHINE,
                        AggregationRole.THIS,
                    ),
                    TsetActivityCalculationElementType.OCCUPANCY_MACHINE_ACTIVITY,
                    listOf(
                        TsetCO2eCalculationElementType.MACHINE_DEPRECIATION_CO2E,
                    ),
                    metaInfo,
                ),
                CO2BackMapping(
                    MachineLegacyFieldNames.VARIABLE_CO2_DURING_PRODUCTION_PER_PART.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MACHINE,
                        AggregationRole.THIS,
                    ),
                    TsetCO2eCalculationElementType.MACHINE_VARIABLE_CO2E,
                    metaInfo,
                ),
                CO2BackMapping(
                    MachineLegacyFieldNames.CO2_DURING_NON_OCCUPANCY_PER_PART.fieldName,
                    AggregationLevelAndRole(AggregationLevel.MACHINE, AggregationRole.THIS),
                    TsetActivityCalculationElementType.NON_OCCUPANCY_MACHINE_ACTIVITY,
                    metaInfo,
                ),
                CO2BackMapping(
                    MachineLegacyFieldNames.CO2_DURING_PRODUCTION_PER_PART.fieldName,
                    AggregationLevelAndRole(AggregationLevel.MACHINE, AggregationRole.THIS),
                    TsetActivityCalculationElementType.OCCUPANCY_MACHINE_ACTIVITY,
                    metaInfo,
                ),
            )
        }

        // endregion

        // region bomEntry
        private fun backMappingBomEntry(): List<BackMapping> =
            listOf(
                CostBackMapping(
                    BomEntryLegacyFieldNames.MATERIAL_COSTS_FOR_VIEW.fieldName,
                    listOf(
                        ValueFieldNameBuilderInput(
                            AggregationLevel.SUB_MATERIAL,
                            AggregationRole.THIS,
                            TsetCostCalculationElementType.DIRECT_MATERIAL_COSTS.fieldName,
                        ),
                        ValueFieldNameBuilderInput(
                            AggregationLevel.SUB_MATERIAL,
                            AggregationRole.INHOUSE,
                            TsetCostCalculationElementType.MATERIAL_COSTS.fieldName,
                        ),
                    ),
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CO2BackMapping(
                    BomEntryLegacyFieldNames.MATERIAL_CO2.fieldName,
                    listOf(
                        ValueFieldNameBuilderInput(
                            AggregationLevel.SUB_MATERIAL,
                            AggregationRole.THIS,
                            TsetCO2eCalculationElementType.DIRECT_MATERIAL_CO2E.fieldName,
                        ),
                        ValueFieldNameBuilderInput(
                            AggregationLevel.SUB_MATERIAL,
                            AggregationRole.INHOUSE,
                            TsetCO2eCalculationElementType.MATERIAL_CO2E.fieldName,
                        ),
                    ),
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CostBackMapping(
                    BomEntryLegacyFieldNames.MANUFACTURING_COSTS_FOR_VIEW.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.SUB_MATERIAL,
                        AggregationRole.INHOUSE,
                    ),
                    TsetCostCalculationElementType.MANUFACTURING_COSTS_3,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CO2BackMapping(
                    BomEntryLegacyFieldNames.MANUFACTURING_CO2.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.SUB_MATERIAL,
                        AggregationRole.INHOUSE,
                    ),
                    TsetCO2eCalculationElementType.MANUFACTURING_CO2E,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CostBackMapping(
                    BomEntryLegacyFieldNames.PRICE_PER_UNIT.fieldName,
                    listOf(
                        ValueFieldNameBuilderInput(
                            AggregationLevel.SUB_MATERIAL,
                            AggregationRole.THIS,
                            TsetCostCalculationElementType.DIRECT_MATERIAL_COSTS.fieldName,
                        ),
                        ValueFieldNameBuilderInput(
                            AggregationLevel.SUB_MATERIAL,
                            AggregationRole.INHOUSE,
                            TsetCostCalculationElementType.DIRECT_MATERIAL_COSTS.fieldName,
                        ),
                    ),
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        @Suppress("MagicNumber")
                        (
                            ObjectViewEntityFieldMetaInfo(
                                ObjectView.MATERIAL,
                                50,
                            )
                        ),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT),
                    ),
                ),
                CostBackMapping(
                    BomEntryLegacyFieldNames.COST_PER_PART.fieldName,
                    listOf(
                        ValueFieldNameBuilderInput(
                            AggregationLevel.MATERIAL_USAGE,
                            AggregationRole.THIS,
                            TsetCostCalculationElementType.DIRECT_MATERIAL_COSTS.fieldName,
                        ),
                        ValueFieldNameBuilderInput(
                            AggregationLevel.MATERIAL_USAGE,
                            AggregationRole.INHOUSE,
                            TsetCostCalculationElementType.DIRECT_MATERIAL_COSTS.fieldName,
                        ),
                    ),
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        @Suppress("MagicNumber")
                        (
                            ObjectViewEntityFieldMetaInfo(
                                ObjectView.MATERIAL,
                                60,
                            )
                        ),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
                CO2BackMapping(
                    BomEntryLegacyFieldNames.CO2_PER_PART.fieldName,
                    listOf(
                        ValueFieldNameBuilderInput(
                            AggregationLevel.MATERIAL_USAGE,
                            AggregationRole.THIS,
                            TsetCO2eCalculationElementType.DIRECT_MATERIAL_CO2E.fieldName,
                        ),
                        ValueFieldNameBuilderInput(
                            AggregationLevel.MATERIAL_USAGE,
                            AggregationRole.INHOUSE,
                            TsetCO2eCalculationElementType.DIRECT_MATERIAL_CO2E.fieldName,
                        ),
                    ),
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
            )

        // endregion

        // region manufacturing
        private fun backMappingRoughManufacturing(): List<BackMapping> =
            backMappingManufacturing() +
                CostBackMapping(
                    ManufacturingLegacyFieldNames.COST_PER_PART.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.SOLD_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCostCalculationElementType.SALES_PRICE,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(false),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT),
                    ),
                ) +
                CO2BackMapping(
                    ManufacturingLegacyFieldNames.CO2_PER_PART.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.SOLD_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCO2eCalculationElementType.TOTAL_CO2E,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(false),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT),
                    ),
                )

        private fun backMappingNotRoughManufacturing(): List<BackMapping> =
            backMappingManufacturing() +
                CostBackMapping(
                    ManufacturingLegacyFieldNames.COST_PER_PART.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.SOLD_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCostCalculationElementType.SALES_PRICE,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT),
                    ),
                ) +
                CO2BackMapping(
                    ManufacturingLegacyFieldNames.CO2_PER_PART.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.SOLD_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCO2eCalculationElementType.TOTAL_CO2E,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT),
                    ),
                )

        private fun backMappingManufacturing(): List<BackMapping> =
            listOf(
                CostBackMapping(
                    ManufacturingLegacyFieldNames.MATERIAL_OVERHEAD_COSTS.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCostCalculationElementType.MATERIAL_OVERHEAD_COSTS,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT),
                    ),
                ),
                CO2BackMapping(
                    ManufacturingLegacyFieldNames.MATERIAL_OVERHEAD_CO2.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCO2eCalculationElementType.MATERIAL_OVERHEAD_CO2E,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT),
                    ),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.INTEREST_ON_MATERIAL_STOCKS.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCostCalculationElementType.MATERIAL_INTEREST_COSTS,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT),
                    ),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.MATERIAL_SCRAP_COSTS.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCostCalculationElementType.MATERIAL_SCRAP_COSTS,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT),
                    ),
                ),
                CO2BackMapping(
                    ManufacturingLegacyFieldNames.MATERIAL_SCRAP_CO2.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCO2eCalculationElementType.MATERIAL_SCRAP_CO2E,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT),
                    ),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.MATERIAL_SCRAP_OVERHEAD_INTEREST_COSTS.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    listOf(
                        TsetCostCalculationElementType.MATERIAL_SCRAP_COSTS,
                        TsetCostCalculationElementType.MATERIAL_OVERHEAD_COSTS,
                        TsetCostCalculationElementType.MATERIAL_INTEREST_COSTS,
                    ),
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT),
                    ),
                ),
                CO2BackMapping(
                    ManufacturingLegacyFieldNames.MATERIAL_SCRAP_OVERHEAD_INTEREST_CO2.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    listOf(
                        TsetCO2eCalculationElementType.MATERIAL_SCRAP_CO2E,
                        TsetCO2eCalculationElementType.MATERIAL_OVERHEAD_CO2E,
                    ),
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT),
                    ),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.DIRECT_MATERIAL_COSTS.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCostCalculationElementType.DIRECT_MATERIAL_COSTS,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT),
                    ),
                ),
                CO2BackMapping(
                    ManufacturingLegacyFieldNames.DIRECT_MATERIAL_CO2.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCO2eCalculationElementType.DIRECT_MATERIAL_CO2E,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT),
                    ),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.MATERIAL_COSTS.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCostCalculationElementType.MATERIAL_COSTS,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT),
                    ),
                ),
                CO2BackMapping(
                    ManufacturingLegacyFieldNames.MATERIAL_CO2.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCO2eCalculationElementType.MATERIAL_CO2E,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT),
                    ),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.COST_PER_PART_SETUP_SYSTEM.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetActivityCalculationElementType.SETUP_MACHINE_ACTIVITY,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.COST_PER_PART_SETUP_DIRECT_LABOR.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetActivityCalculationElementType.SETUP_OPERATOR_ACTIVITY,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.COST_PER_PART_SETUP_LABOR.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetActivityCalculationElementType.SETUP_SETUP_WORKER_ACTIVITY,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.COST_PER_PART_SETUP.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetActivityCalculationElementType.SETUP_ACTIVITY,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.COST_PER_PART_MACHINE.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetActivityCalculationElementType.PRODUCTION_MACHINE_ACTIVITY,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CO2BackMapping(
                    ManufacturingLegacyFieldNames.CO2_PER_PART_MACHINE.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetActivityCalculationElementType.PRODUCTION_MACHINE_ACTIVITY,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.COST_PER_PART_LABOR.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetActivityCalculationElementType.PRODUCTION_LABOR_ACTIVITY,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.MAINTENANCE_COST_PER_PART_TOOL.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCostCalculationElementType.TOOL_MAINTENANCE_COSTS,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.COST_PER_PART_MANUFACTURING.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCostCalculationElementType.DIRECT_MANUFACTURING_COSTS,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CO2BackMapping(
                    ManufacturingLegacyFieldNames.CO2_PER_PART_MANUFACTURING.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCO2eCalculationElementType.DIRECT_MANUFACTURING_CO2E,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.ALLOCATION_INTEREST_COST_PER_PART_TOOL.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCostCalculationElementType.TOOL_ALLOCATION_AND_INTEREST_COSTS,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CO2BackMapping(
                    ManufacturingLegacyFieldNames.CO2_PER_PART_TOOL.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCO2eCalculationElementType.TOOL_CO2E,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.COST_PER_PART_RMOC.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCostCalculationElementType.MANUFACTURING_OVERHEAD_COSTS,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CO2BackMapping(
                    ManufacturingLegacyFieldNames.CO2_PER_PART_RMOC.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCO2eCalculationElementType.MANUFACTURING_OVERHEAD_CO2E,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.COST_PER_PART_MANUFACTURING_SCRAP.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCostCalculationElementType.MANUFACTURING_SCRAP_COSTS,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CO2BackMapping(
                    ManufacturingLegacyFieldNames.CO2_PER_PART_MANUFACTURING_SCRAP.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCO2eCalculationElementType.MANUFACTURING_SCRAP_CO2E,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.MANUFACTURING_COSTS_2_STEP.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.THIS,
                    ),
                    TsetCostCalculationElementType.MANUFACTURING_COSTS_2,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CO2BackMapping(
                    ManufacturingLegacyFieldNames.MANUFACTURING_CO2_2_STEP.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.THIS,
                    ),
                    TsetCO2eCalculationElementType.MANUFACTURING_CO2E,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.MANUFACTURING_COSTS_2_SUB_MANUFACTURING.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.INHOUSE,
                    ),
                    TsetCostCalculationElementType.MANUFACTURING_COSTS_2,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CO2BackMapping(
                    ManufacturingLegacyFieldNames.MANUFACTURING_CO2_2_SUB_MANUFACTURING.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.INHOUSE,
                    ),
                    TsetCO2eCalculationElementType.MANUFACTURING_CO2E,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.INTEREST_ON_WORK_IN_PROGRESS_STEP.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCostCalculationElementType.MANUFACTURING_INTEREST_COSTS,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.COST_PER_PART_ON_WORK_IN_PROCESS.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCostCalculationElementType.MANUFACTURING_INTEREST_COSTS,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.MANUFACTURING_COSTS_3.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCostCalculationElementType.MANUFACTURING_COSTS_3,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CO2BackMapping(
                    ManufacturingLegacyFieldNames.MANUFACTURING_CO2_3.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCO2eCalculationElementType.MANUFACTURING_CO2E,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.PRODUCTION_COSTS.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCostCalculationElementType.PRODUCTION_COSTS,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CO2BackMapping(
                    ManufacturingLegacyFieldNames.PRODUCTION_CO2.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCO2eCalculationElementType.PRODUCTION_CO2E,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.SPECIAL_DIRECT_COSTS.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCostCalculationElementType.ALLOCATED_AND_INTEREST_COSTS_FOR_INVEST,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CO2BackMapping(
                    ManufacturingLegacyFieldNames.SPECIAL_DIRECT_CO2.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.MANUFACTURED_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCO2eCalculationElementType.TOTAL_CO2E_FOR_INVEST,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CostBackMapping(
                    ManufacturingLegacyFieldNames.OVERHEAD_COSTS.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.SOLD_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCostCalculationElementType.INDIRECT_COSTS_AFTER_PRODUCTION,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
                CO2BackMapping(
                    ManufacturingLegacyFieldNames.OVERHEAD_CO2.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.SOLD_MATERIAL,
                        AggregationRole.TOTAL,
                    ),
                    TsetCO2eCalculationElementType.INDIRECT_CO2E_AFTER_PRODUCTION,
                    listOf(ReadOnlyEntityFieldMetaInfo(), DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.COST_UNIT)),
                ),
            )

        // endregion

        // region steps
        private fun backMappingRoughManufacturingStep(): List<BackMapping> =
            listOf(
                createStepCostBackMapping(
                    ManufacturingStepLegacyFieldNames.COST_PER_PART.fieldName,
                    TsetCostCalculationElementType.ROUGH_PROCESS_COSTS,
                    TsetCostCalculationElementType.MANUFACTURING_SCRAP_COSTS,
                    TsetCostCalculationElementType.MANUFACTURING_OVERHEAD_COSTS,
                ),
                createStepCO2BackMapping(
                    ManufacturingStepLegacyFieldNames.CO2_PER_PART.fieldName,
                    TsetCO2eCalculationElementType.ROUGH_PROCESS_CO2E,
                    TsetCO2eCalculationElementType.MANUFACTURING_SCRAP_CO2E,
                    TsetCO2eCalculationElementType.MANUFACTURING_OVERHEAD_CO2E,
                ),
            ) + backMappingManufacturingStep()

        private fun backMappingDetailedManufacturingStep(): List<BackMapping> =
            listOf(
                CostBackMapping(
                    ManufacturingStepLegacyFieldNames.COST_PER_PART.fieldName,
                    listOf(
                        ValueFieldNameBuilderInput(
                            AggregationLevel.MANUFACTURING_STEP,
                            AggregationRole.THIS,
                            TsetCostCalculationElementType.PRODUCTION_COSTS.fieldName,
                        ),
                        ValueFieldNameBuilderInput(
                            AggregationLevel.MANUFACTURING_STEP,
                            AggregationRole.INHOUSE,
                            TsetCostCalculationElementType.PRODUCTION_COSTS.fieldName,
                        ),
                        ValueFieldNameBuilderInput(
                            AggregationLevel.MANUFACTURING_STEP,
                            AggregationRole.PURCHASE,
                            TsetCostCalculationElementType.PRODUCTION_COSTS.fieldName,
                        ),
                    ),
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
                CO2BackMapping(
                    ManufacturingStepLegacyFieldNames.CO2_PER_PART.fieldName,
                    listOf(
                        ValueFieldNameBuilderInput(
                            AggregationLevel.MANUFACTURING_STEP,
                            AggregationRole.THIS,
                            TsetCO2eCalculationElementType.PRODUCTION_CO2E.fieldName,
                        ),
                        ValueFieldNameBuilderInput(
                            AggregationLevel.MANUFACTURING_STEP,
                            AggregationRole.INHOUSE,
                            TsetCO2eCalculationElementType.PRODUCTION_CO2E.fieldName,
                        ),
                        ValueFieldNameBuilderInput(
                            AggregationLevel.MANUFACTURING_STEP,
                            AggregationRole.PURCHASE,
                            TsetCO2eCalculationElementType.PRODUCTION_CO2E.fieldName,
                        ),
                    ),
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
            ) + backMappingManufacturingStep()

        private fun backMappingManufacturingStep(): List<BackMapping> =
            listOf(
                createStepCostBackMapping(
                    ManufacturingStepLegacyFieldNames.COST_PER_PART_SETUP_LABOR.fieldName,
                    TsetActivityCalculationElementType.SETUP_SETUP_WORKER_ACTIVITY,
                ),
                createStepCostBackMapping(
                    ManufacturingStepLegacyFieldNames.COST_PER_PART_SETUP_DIRECT_LABOR.fieldName,
                    TsetActivityCalculationElementType.SETUP_OPERATOR_ACTIVITY,
                ),
                createStepCostBackMapping(
                    ManufacturingStepLegacyFieldNames.COST_PER_PART_SETUP_SYSTEM.fieldName,
                    TsetActivityCalculationElementType.SETUP_MACHINE_ACTIVITY,
                ),
                createStepCostBackMapping(
                    ManufacturingStepLegacyFieldNames.COST_PER_PART_SETUP.fieldName,
                    TsetActivityCalculationElementType.SETUP_ACTIVITY,
                ),
                createStepCostBackMapping(
                    ManufacturingStepLegacyFieldNames.COST_PER_PART_MACHINE.fieldName,
                    TsetActivityCalculationElementType.OCCUPANCY_MACHINE_ACTIVITY,
                    TsetActivityCalculationElementType.NON_OCCUPANCY_MACHINE_ACTIVITY,
                ),
                createStepCO2BackMapping(
                    ManufacturingStepLegacyFieldNames.CO2_PER_PART_MACHINE.fieldName,
                    TsetActivityCalculationElementType.OCCUPANCY_MACHINE_ACTIVITY,
                    TsetActivityCalculationElementType.NON_OCCUPANCY_MACHINE_ACTIVITY,
                ),
                createStepCostBackMapping(
                    ManufacturingStepLegacyFieldNames.MAINTENANCE_COST_PER_PART_TOOL.fieldName,
                    TsetActivityCalculationElementType.TOOL_MAINTENANCE_ACTIVITY,
                ),
                createStepCostBackMapping(
                    ManufacturingStepLegacyFieldNames.COST_PER_PART_LABOR.fieldName,
                    TsetActivityCalculationElementType.PRODUCTION_LABOR_ACTIVITY,
                ),
                createStepCostBackMapping(
                    ManufacturingStepLegacyFieldNames.COST_PER_PART_MANUFACTURING.fieldName,
                    TsetCostCalculationElementType.DIRECT_MANUFACTURING_COSTS,
                ),
                createStepCO2BackMapping(
                    ManufacturingStepLegacyFieldNames.CO2_PER_PART_MANUFACTURING.fieldName,
                    TsetCO2eCalculationElementType.DIRECT_MANUFACTURING_CO2E,
                ),
                createStepCostBackMapping(
                    ManufacturingStepLegacyFieldNames.ALLOCATION_INTEREST_COST_PER_PART_TOOL.fieldName,
                    TsetCostCalculationElementType.TOOL_ALLOCATION_AND_INTEREST_COSTS,
                ),
                createStepCostBackMapping(
                    ManufacturingStepLegacyFieldNames.COST_PER_PART_RMOC.fieldName,
                    TsetCostCalculationElementType.MANUFACTURING_OVERHEAD_COSTS,
                ),
                createStepCostBackMapping(
                    ManufacturingStepLegacyFieldNames.COST_PER_PART_MANUFACTURING_SCRAP.fieldName,
                    TsetCostCalculationElementType.MANUFACTURING_SCRAP_COSTS,
                ),
                createStepCostBackMapping(
                    ManufacturingStepLegacyFieldNames.RMOC_AND_SCRAP.fieldName,
                    TsetCostCalculationElementType.MANUFACTURING_SCRAP_COSTS,
                    TsetCostCalculationElementType.MANUFACTURING_OVERHEAD_COSTS,
                ),
                createStepCO2BackMapping(
                    ManufacturingStepLegacyFieldNames.CO2_PER_PART_RMOC.fieldName,
                    TsetCO2eCalculationElementType.MANUFACTURING_OVERHEAD_CO2E,
                ),
                createStepCO2BackMapping(
                    ManufacturingStepLegacyFieldNames.CO2_PER_PART_MANUFACTURING_SCRAP.fieldName,
                    TsetCO2eCalculationElementType.MANUFACTURING_SCRAP_CO2E,
                ),
                createStepCO2BackMapping(
                    ManufacturingStepLegacyFieldNames.CO2_RMOC_AND_SCRAP.fieldName,
                    TsetCO2eCalculationElementType.MANUFACTURING_SCRAP_CO2E,
                    TsetCO2eCalculationElementType.MANUFACTURING_OVERHEAD_CO2E,
                ),
                createStepCostBackMapping(
                    ManufacturingStepLegacyFieldNames.MANUFACTURING_COSTS_2_STEP.fieldName,
                    TsetCostCalculationElementType.MANUFACTURING_COSTS_2,
                ),
                createStepCO2BackMapping(
                    ManufacturingStepLegacyFieldNames.MANUFACTURING_CO2_2_STEP.fieldName,
                    TsetCO2eCalculationElementType.MANUFACTURING_CO2E,
                ),
                CostBackMapping(
                    ManufacturingStepLegacyFieldNames.MANUFACTURING_COSTS_3.fieldName,
                    listOf(
                        ValueFieldNameBuilderInput(
                            AggregationLevel.MANUFACTURING_STEP,
                            AggregationRole.THIS,
                            TsetCostCalculationElementType.MANUFACTURING_COSTS_3.fieldName,
                        ),
                        ValueFieldNameBuilderInput(
                            AggregationLevel.MANUFACTURING_STEP,
                            AggregationRole.INHOUSE,
                            TsetCostCalculationElementType.MANUFACTURING_COSTS_3.fieldName,
                        ),
                        ValueFieldNameBuilderInput(
                            AggregationLevel.MANUFACTURING_STEP,
                            AggregationRole.PURCHASE,
                            TsetCostCalculationElementType.MANUFACTURING_COSTS_3.fieldName,
                        ),
                    ),
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
                CO2BackMapping(
                    ManufacturingStepLegacyFieldNames.MANUFACTURING_CO2_3.fieldName,
                    listOf(
                        ValueFieldNameBuilderInput(
                            AggregationLevel.MANUFACTURING_STEP,
                            AggregationRole.THIS,
                            TsetCO2eCalculationElementType.MANUFACTURING_CO2E.fieldName,
                        ),
                        ValueFieldNameBuilderInput(
                            AggregationLevel.MANUFACTURING_STEP,
                            AggregationRole.INHOUSE,
                            TsetCO2eCalculationElementType.MANUFACTURING_CO2E.fieldName,
                        ),
                        ValueFieldNameBuilderInput(
                            AggregationLevel.MANUFACTURING_STEP,
                            AggregationRole.PURCHASE,
                            TsetCO2eCalculationElementType.MANUFACTURING_CO2E.fieldName,
                        ),
                    ),
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
                CostBackMapping(
                    ManufacturingStepLegacyFieldNames.PRODUCTION_COSTS.fieldName,
                    listOf(
                        ValueFieldNameBuilderInput(
                            AggregationLevel.MANUFACTURING_STEP,
                            AggregationRole.THIS,
                            TsetCostCalculationElementType.PRODUCTION_COSTS.fieldName,
                        ),
                        ValueFieldNameBuilderInput(
                            AggregationLevel.MANUFACTURING_STEP,
                            AggregationRole.INHOUSE,
                            TsetCostCalculationElementType.PRODUCTION_COSTS.fieldName,
                        ),
                        ValueFieldNameBuilderInput(
                            AggregationLevel.MANUFACTURING_STEP,
                            AggregationRole.PURCHASE,
                            TsetCostCalculationElementType.PRODUCTION_COSTS.fieldName,
                        ),
                    ),
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
                CO2BackMapping(
                    ManufacturingStepLegacyFieldNames.PRODUCTION_CO2.fieldName,
                    listOf(
                        ValueFieldNameBuilderInput(
                            AggregationLevel.MANUFACTURING_STEP,
                            AggregationRole.THIS,
                            TsetCO2eCalculationElementType.PRODUCTION_CO2E.fieldName,
                        ),
                        ValueFieldNameBuilderInput(
                            AggregationLevel.MANUFACTURING_STEP,
                            AggregationRole.INHOUSE,
                            TsetCO2eCalculationElementType.PRODUCTION_CO2E.fieldName,
                        ),
                        ValueFieldNameBuilderInput(
                            AggregationLevel.MANUFACTURING_STEP,
                            AggregationRole.PURCHASE,
                            TsetCO2eCalculationElementType.PRODUCTION_CO2E.fieldName,
                        ),
                    ),
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
                createStepCostBackMapping(
                    ManufacturingStepLegacyFieldNames.COST_PER_PART_TOOL.fieldName,
                    TsetCostCalculationElementType.TOOL_COSTS,
                ),
                createStepCO2BackMapping(
                    ManufacturingStepLegacyFieldNames.CO2_PER_PART_TOOL.fieldName,
                    TsetCO2eCalculationElementType.TOOL_CO2E,
                ),
            )

        private fun createStepCostBackMapping(
            backMappingFieldName: String,
            vararg elementTypes: TsetCalculationElementType,
        ): BackMapping {
            return createStepBackMapping(
                backMappingFieldName,
                ValueType.COST,
                *elementTypes,
            )
        }

        private fun createStepCO2BackMapping(
            backMappingFieldName: String,
            vararg elementTypes: TsetCalculationElementType,
        ): BackMapping {
            return createStepBackMapping(
                backMappingFieldName,
                ValueType.CO2,
                *elementTypes,
            )
        }

        private fun createStepBackMapping(
            backMappingFieldName: String,
            valueType: ValueType,
            vararg elementTypes: TsetCalculationElementType,
        ): BackMapping {
            return BackMapping(
                backMappingFieldName,
                valueType,
                elementTypes.map {
                    ValueFieldNameBuilderInput(AggregationLevel.MANUFACTURING_STEP, AggregationRole.THIS, it.fieldName)
                },
                listOf(
                    ReadOnlyEntityFieldMetaInfo(),
                    DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                ),
            )
        }

        // endregion

        // region special direct cost
        private fun backMappingSpecialDirectCost(): List<BackMapping> =
            listOf(
                CostBackMapping(
                    SpecialDirectCostLegacyFieldNames.COST_PER_QUANTITY.fieldName,
                    AggregationLevelAndRole(
                        AggregationLevel.INVEST,
                        AggregationRole.THIS,
                    ),
                    TsetCostCalculationElementType.ALLOCATED_AND_INTEREST_COSTS_FOR_INVEST,
                    listOf(
                        ReadOnlyEntityFieldMetaInfo(),
                        DynamicDenominatorUnitEntityFieldMetaInfo(DynamicUnitOverride.MANUFACTURING_COST_UNIT),
                    ),
                ),
            )

        // endregion
    }
}
