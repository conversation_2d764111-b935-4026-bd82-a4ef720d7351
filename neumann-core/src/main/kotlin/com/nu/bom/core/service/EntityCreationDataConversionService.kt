package com.nu.bom.core.service

import com.google.common.annotations.VisibleForTesting
import com.nu.bom.core.api.dtos.EntityCreationDto
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntityContext
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.factories.FieldFactoryService
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.service.EntityClassOrName
import com.nu.bom.core.manufacturing.service.asString
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.BranchId
import com.nu.bom.core.model.MasterDataSelector
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.createBranchId
import com.nu.bom.core.service.EntityCreationHelperService.Companion.getEntityCreationAnnotationInfo
import com.nu.bom.core.service.validation.EntityCreationValidation
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.InterpolationData
import com.nu.bom.core.utils.getOptional
import com.nu.bom.core.utils.hasValue
import com.nu.bom.core.utils.simpleName
import com.nu.bom.core.utils.toObjectId
import com.tset.bom.clients.common.FieldStructure
import com.tset.core.service.domain.calculation.CalculationType
import org.bson.types.ObjectId
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono
import kotlin.reflect.KClass

@Service
class EntityCreationDataConversionService(
    private val fieldFactoryService: FieldFactoryService,
    private val entityManager: EntityManager,
    private val entityCreationValidation: EntityCreationValidation,
) {
    data class EntityCreationData(
        val bomNodeId: BomNodeId,
        val branchId: BranchId?,
        val parentId: ObjectId,
        val projectId: ProjectId?,
        val childBomNodeId: BomNodeId?,
        val items: List<ItemData>,
        val insertBeforeEntityId: ObjectId? = null,
        val insertAfterEntityId: ObjectId? = null,
        val entityLink: EntityLinkInformation? = null,
        val technology: Model? = null,
    ) {
        data class ItemData(
            val entityType: Entities,
            val entityClass: KClass<out ManufacturingEntity>?,
            val masterDataSelector: MasterDataSelector?,
            val sourceMasterDataSelector: MasterDataSelector? = null,
            val fields: Map<String, FieldResultStar>,
            val overwrites: Map<String, FieldResultStar>,
            val isolated: Boolean,
        )

        data class EntityLinkInformation(
            val entityLinkField: String,
            val linkEntityId: ObjectId,
        )
    }

    fun <X : FieldStructure> convert(
        accessCheck: AccessCheck,
        snapshot: BomNodeSnapshot,
        creationDto: EntityCreationDto<X>,
        bomNodeId: String,
        branchId: String?,
        projectId: ProjectId?,
    ): Mono<EntityCreationData> {
        val parentId =
            creationDto.parentId
                ?: creationDto.fields
                    .getOptional("stepId", Text::class)
                    ?.takeIf { it.hasValue() }
                    ?.value as String?
                ?: error("parentId is missing")

        val fieldResults = convertToFieldResult(creationDto)
        val masterDataSelector = MasterDataSelector.fromNullableDto(creationDto.masterDataKey)
        val sourceMasterDataSelector = sourceMasterDataSelector(creationDto, fieldResults, masterDataSelector)
        val (initial, overwrites) = getCreationFields(creationDto, fieldResults, masterDataSelector)
        val entityClass = creationDto.entityClass?.let { clazz -> entityManager.getClass(clazz).kotlin }
        val creationData =
            EntityCreationData(
                bomNodeId = BomNodeId(bomNodeId),
                branchId = createBranchId(branchId),
                parentId = ObjectId(parentId),
                childBomNodeId = creationDto.childBomNodeId?.let { BomNodeId(it) },
                projectId = projectId,
                insertBeforeEntityId = creationDto.insertBeforeEntityId?.takeIf { ObjectId.isValid(it) }?.toObjectId(),
                insertAfterEntityId = creationDto.insertAfterEntityId?.takeIf { ObjectId.isValid(it) }?.toObjectId(),
                entityLink =
                    creationDto.linkEntityField?.let { entityLinkField ->
                        creationDto.linkEntityId?.takeIf { ObjectId.isValid(it) }?.toObjectId()?.let { linkEntityId ->
                            EntityCreationData.EntityLinkInformation(
                                entityLinkField = entityLinkField,
                                linkEntityId = linkEntityId,
                            )
                        }
                    },
                items =
                    listOf(
                        EntityCreationData.ItemData(
                            entityType = creationDto.entityType,
                            entityClass = entityClass,
                            masterDataSelector = masterDataSelector,
                            sourceMasterDataSelector = sourceMasterDataSelector,
                            fields = initial,
                            overwrites = overwrites,
                            isolated = false,
                        ),
                    ),
                technology = creationDto.technology?.let { Model.fromEntity(it) },
            )
        entityCreationValidation.checkEligible(
            snapshot.manufacturing!!,
            creationData.items.map {
                EntityCreationValidation.ManualCalcValidation(
                    it.entityType,
                    it.entityClass,
                    it.isolated,
                )
            },
        )
        return setModularized(creationData, snapshot.manufacturing::class).toMono()
    }

    @VisibleForTesting
    fun setModularized(
        creationData: EntityCreationData,
        rootClass: KClass<out ManufacturingEntity>?,
    ): EntityCreationData =
        if (rootClass == CalculationType.MANUAL_CALCULATION.entityClass || rootClass == Manufacturing::class) {
            creationData.copy(
                items =
                    creationData.items.map { item ->
                        if (item.entityClass?.let(entityManager::canBeUsedAsModularized) == true) {
                            item.copy(isolated = true)
                        } else {
                            item
                        }
                    },
            )
        } else {
            creationData
        }

    private fun <X : FieldStructure> sourceMasterDataSelector(
        creationDto: EntityCreationDto<X>,
        fieldResults: Map<String, FieldResultStar>,
        masterDataSelector: MasterDataSelector?,
    ) = entityManager.getManualMasterDataSelector(
        entityManager.getActualCreationClass(
            creationDto.entityType,
            creationDto.entityClass?.simpleName(),
            fieldResults,
            masterDataSelector,
        ),
        fieldResults,
    )

    private fun <X : FieldStructure> convertToFieldResult(creationDto: EntityCreationDto<X>): Map<String, FieldResultStar> {
        val fields = creationDto.fields.filterNot { it.name == "stepId" }
        // !!! Do NOT convert Money fields here, they will be converted later
        return fieldFactoryService.toFieldResultMap(
            fields,
            creationDto.currency,
        )
    }

    private fun <X : FieldStructure> getCreationFields(
        creationDto: EntityCreationDto<X>,
        fields: Map<String, FieldResultStar>,
        masterDataSelector: MasterDataSelector?,
    ): Pair<Map<String, FieldResultStar>, Map<String, FieldResultStar>> {
        var fieldResults = fields
        val clazz =
            entityManager.getActualCreationClass(
                creationDto.entityType,
                creationDto.entityClass,
                fieldResults,
                masterDataSelector,
            )
        val computed = fieldResults.keys.filter { isComputed(creationDto, it, clazz) }.toSet()

        // Discard `computed` fields unless they are input
        fieldResults = fieldResults.filter { it.value.source == FieldResult.SOURCE.I || it.key !in computed }

        // Fields that are calculated in the modal are still "inputs" for the created entity.
        // Otherwise, changing default values that were not overwritten during creation would
        // also change existing entities!
        fieldResults.map { (_, res) ->
            res.takeUnless { it.source == FieldResult.SOURCE.C } ?: res.withSource(FieldResult.SOURCE.I)
        }
        creationDto.technology?.let {
            fieldResults += "technologyModel" to Text(it)
        }

        // Computed fields with user input will be used for the calculation, but not added to the list of initial fields
        // (it.name in computed && it.source != FieldResult.SOURCE.I.name)
        val (initial, overwrites) = fieldResults.entries.partition { it.key !in computed }
        return Pair(initial.associate { it.key to it.value }, overwrites.associate { it.key to it.value })
    }

    private fun <X : FieldStructure> isComputed(
        creationDto: EntityCreationDto<X>,
        fieldName: String,
        classOverride: EntityClassOrName,
    ): Boolean {
        val metaInfo = entityManager.getMetaInfo(classOverride.asString(), fieldName, InterpolationData())

        val preferredContext =
            when {
                creationDto.masterDataKey != null -> MandatoryForEntityContext.CREATE_FROM_MASTERDATA
                creationDto.fields.find { it.name == "externalPartId" }?.value != null -> MandatoryForEntityContext.CREATE_FROM_EXTERNALDATA
                else -> MandatoryForEntityContext.CREATE_MANUAL
            }

        val info = getEntityCreationAnnotationInfo(metaInfo, preferredContext)

        return info.isSourceData || info.isMandatoryForEntityComputed
    }
}
