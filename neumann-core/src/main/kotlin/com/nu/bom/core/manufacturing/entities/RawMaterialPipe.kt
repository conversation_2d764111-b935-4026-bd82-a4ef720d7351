package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MasterDataType
import com.nu.bom.core.manufacturing.annotations.Technologies
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Diffusivity
import com.nu.bom.core.manufacturing.fieldTypes.DiffusivityUnits
import com.nu.bom.core.manufacturing.fieldTypes.MaterialSubstances
import com.nu.bom.core.manufacturing.fieldTypes.Text

@EntityType(Entities.MATERIAL)
@MasterDataType(com.nu.bom.core.manufacturing.enums.MasterDataType.RAW_MATERIAL_PIPE)
@Technologies([Model.RSWA])
class RawMaterialPipe(name: String) : ManufacturingEntity(name) {

    override val extends = RawMaterial(name)

    @Input
    fun designation(): Text? = null

    @Input
    @DefaultUnit(DefaultUnit.QMM_PER_SECONDS)
    fun specificThermalCapacity(): Diffusivity = Diffusivity(0.134.toBigDecimal(), DiffusivityUnits.QMM_PER_SECONDS)

    @Input
    fun materialSubstances(): MaterialSubstances = MaterialSubstances(emptyList())
}
