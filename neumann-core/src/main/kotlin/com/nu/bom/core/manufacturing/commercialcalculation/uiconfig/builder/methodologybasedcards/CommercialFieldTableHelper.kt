package com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards

import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.CalculationOperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.UniqueOperationIdentifier
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.InternalConfigurationOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.OperationWithStandardCalculationValue
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.ScrapOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.SumProdOperation
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.InternalCommercialCalculationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.helper.ColumnCreator
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.fieldtable.FieldTableBuilder.createFieldTable
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.fieldtable.FieldTableRowStructure
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.fieldtable.FieldTableRowStructure.Companion.withActivity
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.fieldtable.FieldTableRowStructure.Companion.withoutActivity
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.fieldtable.OperationWithAdditionalInfo
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableOption
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityLocator
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableConfigFeDto

class CommercialFieldTableHelper(
    private val entryPointFromConfig: (CalculationOperationConfiguration) -> OperationWithStandardCalculationValue,
    private val columnCreators: (InternalCommercialCalculationConfiguration) -> List<ColumnCreator>,
    // This will be moved to the user-specific view configuration
    private val isCollapsed: (CalculationOperationConfiguration, SumProdOperation) -> Boolean,
    private val includeTopLevel: Boolean = false,
) {
    fun getTableConfig(
        valueType: ValueType,
        tableOption: TableOption,
        procurementType: ManufacturingType,
        vararg configs: InternalCommercialCalculationConfiguration,
        navigatorsForRows: (CalculationOperationConfiguration, UniqueOperationIdentifier) -> EntityLocator? = { _, _ -> null },
    ): FieldTableConfigFeDto {
        val config =
            checkNotNull(configs.singleOrNull { it.valueType == valueType }) {
                "Non unique definition of the operation Configuration for `$valueType`"
            }
        val fieldTableRowStructure =
            when (TableOptionEnum.fromDisplayableOptionName(tableOption)) {
                TableOptionEnum.ACTIVITY ->
                    withActivity(entryPointFromConfig(config.opConfig), Int.MAX_VALUE, includeTopLevel, config.opConfig) {
                        isCollapsed(config.opConfig, it)
                    }
                TableOptionEnum.PRODUCTION ->
                    withoutActivity(entryPointFromConfig(config.opConfig), Int.MAX_VALUE, includeTopLevel) {
                        isCollapsed(config.opConfig, it)
                    }
            }

        return createFieldTable(
            fieldTableRowStructure = fieldTableRowStructure,
            columnCreators = columnCreators(config),
            allOperationsForTableIncludingEntryPoint =
                allOperationsForTableIncludingEntryPoint(
                    procurementType,
                    config,
                    fieldTableRowStructure,
                ),
            navigatorsForRows = { navigatorsForRows(config.opConfig, it) },
        )
    }

    private fun allOperationsForTableIncludingEntryPoint(
        procurementType: ManufacturingType,
        config: InternalCommercialCalculationConfiguration,
        fieldTableRowStructure: FieldTableRowStructure,
    ): List<OperationWithAdditionalInfo> =
        OperationConfigurationInformationExtractor.expandOperationNestedWithRowStructure(
            config.opConfig,
            fieldTableRowStructure,
        ) { op: InternalConfigurationOperation ->
            val aggregationLevelIsAvailable = !(procurementType == ManufacturingType.INHOUSE && op.origin == AggregationLevel.SOLD_MATERIAL)
            val hasSeveralEntries = config.opConfig.getAllOperations<ScrapOperation>(op.destinationElementKey).size > 1
            val isInhouseMaterialScrapOfManufacturingScrap =
                op.origin == AggregationLevel.MATERIAL_USAGE && hasSeveralEntries

            !isInhouseMaterialScrapOfManufacturingScrap && aggregationLevelIsAvailable
        }
}
