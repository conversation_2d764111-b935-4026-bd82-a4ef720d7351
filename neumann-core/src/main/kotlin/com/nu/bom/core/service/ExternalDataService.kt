package com.nu.bom.core.service

import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.nu.bom.core.api.dtos.ExternalDataSupplement
import com.nu.bom.core.api.dtos.ThreeDbAttachmentSupplement
import com.nu.bom.core.manufacturing.annotations.ExternalData
import com.nu.bom.core.milldrill.MillingDrillingService
import com.nu.bom.core.milldrill.model.MillingDrillingSketch
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.TurningProfile
import com.nu.bom.core.model.toMongoID
import com.nu.bom.core.service.file.SecureFileService
import com.nu.bom.core.service.file.UploadResponse
import com.nu.bom.core.service.file.UploadType
import com.nu.bom.core.service.file.UploadablePayload
import com.nu.bom.core.service.imports.CalculationImportExportService
import com.nu.bom.core.service.imports.VersionedPartDataMapping
import com.nu.bom.core.turn.TurningService
import com.nu.bom.core.user.AccessCheck
import org.bson.types.ObjectId
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.util.Base64

@Service
class ExternalDataService(
    private val turningService: TurningService,
    private val millingDrillingService: MillingDrillingService,
    private val secureFileService: SecureFileService,
) {
    @JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, include = JsonTypeInfo.As.PROPERTY, property = "@class")
    interface ExternalDto

    fun getExternalData(
        accessCheck: AccessCheck,
        type: ExternalData.EXTERNAL_DATA_TYPE,
        id: String,
        supplement: ExternalDataSupplement,
    ): Mono<ExternalDto> =
        when (type) {
            ExternalData.EXTERNAL_DATA_TYPE.TURNING_PROFILE ->
                turningService.getProfile(ObjectId(id), accessCheck).map { it.toExportSketch() }

            ExternalData.EXTERNAL_DATA_TYPE.MILLING_PROFILE ->
                millingDrillingService.getSketch(ObjectId(id)).map { it }

            ExternalData.EXTERNAL_DATA_TYPE.PART_IMAGE,
            ExternalData.EXTERNAL_DATA_TYPE.THREE_DB_ATTACHMENT,
            ExternalData.EXTERNAL_DATA_TYPE.SMF_PROFILE,
            ->
                secureFileService
                    .download(accessCheck, id)
                    .onErrorResume { Mono.empty() }
                    .map {
                        val base64content =
                            if (type == ExternalData.EXTERNAL_DATA_TYPE.SMF_PROFILE) {
                                ""
                            } else {
                                Base64.getEncoder().encodeToString(it.payload)
                            }

                        CalculationImportExportService.FileUpload(
                            name = it.filename,
                            base64Content = base64content,
                            pureContent = it.payload,
                            zipEntryName = it.hashCode().toString() + "-" + it.filename,
                            supplement = supplement,
                        )
                    }
        }

    open class ExtData(
        val id: ObjectId,
    ) {
        class PartImageExtData(
            id: ObjectId,
            val uploadResponse: UploadResponse,
        ) : ExtData(id)
    }

    private fun toUploadablePayload(data: ExternalDto): UploadablePayload? =
        when {
            data is CalculationImportExportService.FileUpload && data.base64Content.isNotBlank() ->
                UploadablePayload.fromBase64(data.name, data.base64Content)
            data is CalculationImportExportService.FileUpload && data.pureContent != null ->
                UploadablePayload.fromByteArray(data.name, data.pureContent)
            else -> null
        }

    fun saveExternalData(
        type: ExternalData.EXTERNAL_DATA_TYPE,
        data: ExternalDto,
        accessCheck: AccessCheck,
        projectId: ObjectId,
        versionedPartDataMapping: VersionedPartDataMapping,
    ): Mono<ExtData> =
        when (type) {
            ExternalData.EXTERNAL_DATA_TYPE.TURNING_PROFILE ->
                turningService
                    .fromExportSketch(data as TurningProfile.TurningProfileExport, accessCheck, projectId)
                    .map { ExtData(it._id!!) }

            ExternalData.EXTERNAL_DATA_TYPE.MILLING_PROFILE ->
                millingDrillingService
                    .importSketch(data as MillingDrillingSketch, accessCheck, projectId)
                    .map { ExtData(it.id) }

            ExternalData.EXTERNAL_DATA_TYPE.SMF_PROFILE -> {
                val file = toUploadablePayload(data) ?: error("Unexpected step file content")
                secureFileService
                    .upload(
                        accessCheck = accessCheck,
                        uploadType = UploadType.STEP_FILE,
                        file = file,
                        ownerId = null,
                    ).map { ExtData.PartImageExtData(ObjectId(it.id), it) }
            }
            ExternalData.EXTERNAL_DATA_TYPE.PART_IMAGE -> {
                val file = toUploadablePayload(data) ?: error("Unexpected part image content")
                secureFileService
                    .upload(
                        accessCheck = accessCheck,
                        uploadType = UploadType.BOM_NODE_ATTACHMENT,
                        file = file,
                        ownerId = null,
                    ).map { ExtData.PartImageExtData(it.id.toMongoID(), it) }
            }
            ExternalData.EXTERNAL_DATA_TYPE.THREE_DB_ATTACHMENT -> {
                require(data is CalculationImportExportService.FileUpload)
                require(data.supplement is ThreeDbAttachmentSupplement)
                val file = toUploadablePayload(data) ?: error("Unexpected threeDb attachment content")
                val oldVersionedPart = data.supplement.versionedPart
                val maybeMappedPart =
                    versionedPartDataMapping.entries.find { (key, _) ->
                        // the version_id inside the manufacturing has evolved, so we just check for the partId
                        key.part_id == oldVersionedPart.part_id
                    }?.value

                secureFileService
                    .uploadThreeDbAttachment(
                        accessCheck = accessCheck,
                        ownerId = null,
                        file = file,
                        SecureFileService.ThreeDbAttachmentUploadInfo(
                            oldVersionedPart = oldVersionedPart,
                            newInitialVersionedPart = maybeMappedPart?.newInitialVersionedPart,
                        ),
                    ).map { ExtData.PartImageExtData(it.id.toMongoID(), it) }
            }
        }

    fun deleteExternalData(
        accessCheck: AccessCheck,
        id: String,
    ): Mono<Void> =
        secureFileService.delete(
            accessCheck = accessCheck,
            id = id,
        )

    fun assignExternalDataUploadsToBomNode(
        accessCheck: AccessCheck,
        idsTypes: List<CalculationImportExportService.ExternalIdTypeMapping>,
        bomNodeId: BomNodeId,
    ): Flux<UploadResponse> {
        val idsToPatch =
            idsTypes
                .groupBy { it.type }
                .filterKeys {
                    when (it) {
                        ExternalData.EXTERNAL_DATA_TYPE.PART_IMAGE,
                        ExternalData.EXTERNAL_DATA_TYPE.THREE_DB_ATTACHMENT,
                        ExternalData.EXTERNAL_DATA_TYPE.SMF_PROFILE,
                        -> true
                        ExternalData.EXTERNAL_DATA_TYPE.MILLING_PROFILE,
                        ExternalData.EXTERNAL_DATA_TYPE.TURNING_PROFILE,
                        -> false
                    }
                }.values
                .flatten()
                .map { it.id.toHexString() }

        return secureFileService.patch(accessCheck, idsToPatch, bomNodeId.toHexString())
    }
}
