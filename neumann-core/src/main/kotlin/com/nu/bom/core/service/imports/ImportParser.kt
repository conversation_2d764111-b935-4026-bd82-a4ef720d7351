package com.nu.bom.core.service.imports

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.jayway.jsonpath.Configuration
import com.jayway.jsonpath.DocumentContext
import com.jayway.jsonpath.JsonPath
import com.jayway.jsonpath.Option
import com.nu.bom.core.config.JacksonConfig
import com.nu.bom.core.exception.ErrorCodedException
import com.nu.bom.core.exception.readable.ErrorCode
import com.nu.bom.core.manufacturing.fieldTypes.ThreeDbVersionedPart
import com.nu.bom.core.model.toObjectId
import com.nu.bom.core.model.toUUID
import com.nu.bom.core.service.exports.ExportNameUtil
import com.nu.bom.core.threedb.ThreeDbService
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.nu.bom.core.utils.toObjectId
import com.nu.bomrads.nuxt.ProjectHelper
import net.minidev.json.JSONArray
import org.bson.types.ObjectId
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toFlux
import java.io.File
import java.nio.charset.StandardCharsets
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentMap
import java.util.regex.Pattern

private val UUID_PATTERN =
    Pattern.compile("[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}")
private val UUID_OR_MONGO_DB_PATTERN =
    Pattern.compile("[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}|[0-9a-fA-F]{24}")

data class IdEntry<out T>(
    val id: ObjectId,
    val data: T,
) {
    fun <R> map(mapper: (T) -> R) = IdEntry(id, mapper(data))
}

data class AttachmentEntry<out T>(
    val id: String,
    val data: T,
) {
    fun <R> map(mapper: (T) -> R) = AttachmentEntry(id, mapper(data))
}

data class ProjectImportContentsLight(
    val idMap: ConcurrentMap<String, String>,
    val projectDto: ProjectHelper.ProjectDto,
    val oldProjectKey: String,
    val newProjectKey: String,
    val accountId: String,
    val bomNodesFiles: List<IdEntry<File>>,
    val snapshotFiles: List<IdEntry<File>>,
    val branchFiles: List<IdEntry<File>>,
    val turningProfilesFiles: List<IdEntry<File>>,
    val noCalcDataFiles: List<IdEntry<File>>,
    val millingProfilesFiles: List<IdEntry<File>>,
    val partsFiles: List<IdEntry<File>>,
    val documentsFiles: List<IdEntry<File>>,
    val attachmentFiles: List<AttachmentEntry<File>>,
    val deletedBomNodesKeys: MutableList<String>,
) {
    fun bomNodes(): Flux<IdEntry<String>> = getEntitiesContent(bomNodesFiles)

    fun snapshots(): Flux<IdEntry<String>> = getEntitiesContent(snapshotFiles)

    fun branches(): Flux<IdEntry<String>> = getEntitiesContent(branchFiles)

    fun turningProfiles(): Flux<IdEntry<String>> = getEntitiesContent(turningProfilesFiles)

    fun noCalcData(): Flux<IdEntry<String>> = getEntitiesContent(noCalcDataFiles)

    fun millingProfiles(): Flux<IdEntry<String>> = getEntitiesContent(millingProfilesFiles)

    // TODO kw get rid of this
    fun parts(): Flux<IdEntry<String>> = getEntitiesContent(partsFiles)

    fun fileDocuments(): List<IdEntry<String>> = getFileDocumentsContent(documentsFiles)

    fun attachments(): Flux<AttachmentEntry<ByteArray>> = getAttachmentsContent(attachmentFiles)

    fun deletedBomNodesKeys(): Flux<String> = Flux.fromIterable(deletedBomNodesKeys)

    fun newProjectName(): String {
        // the name column is 255 characters, so we need to make sure we don't overflow
        return if (projectDto.name.length < 250) {
            projectDto.name + newProjectKey
        } else {
            projectDto.name.substring(0, 249) + newProjectKey
        }
    }

    @TsetSuppress("tset:reactive:flux-flatmap") // whitelisted already existing calls
    private fun getEntitiesContent(files: List<IdEntry<File>>): Flux<IdEntry<String>> =
        Flux.fromIterable(files).log("Import files").flatMap({ file ->
            Mono.just(importReplacements(file))
        }, IMPORT_DEFAULT_BATCH_SIZE)

    private fun getFileDocumentsContent(files: List<IdEntry<File>>): List<IdEntry<String>> =
        files.map { file ->
            processDeletedBomNodes(file)
            importReplacements(file)
        }

    private fun processDeletedBomNodes(file: IdEntry<File>) {
        file.map { data ->
            val ownerId: String? =
                JacksonConfig.staticMapper
                    .readTree(String(data.readBytes()))
                    .get("ownerId")
                    .asText()
            ownerId.let {
                if (!idMap.containsKey(it)) {
                    val newId = ObjectId.get().toHexString()
                    idMap[it!!] = newId
                    deletedBomNodesKeys.add(newId)
                }
            }
        }
    }

    private fun importReplacements(file: IdEntry<File>): IdEntry<String> =
        file.map { data ->
            val replacedIds = replaceIds(data.readBytes(), idMap)
            val replacedAccount =
                replaceAccount(
                    replacedIds,
                    this.accountId,
                )
            replaceProjectKey(replacedAccount, oldProjectKey, newProjectKey)
        }

    private fun getAttachmentsContent(files: List<AttachmentEntry<File>>): Flux<AttachmentEntry<ByteArray>> =
        Flux.fromIterable(files).log("Attachments Files").map { file ->
            file.map { it.readBytes() }
        }
}

fun entityContent(
    fileContents: List<File>,
    filename: String,
): File {
    val fileContentMap = fileContents.associateBy { it.name }
    return fileContentMap[filename] ?: throw ErrorCodedException.badRequest(
        ErrorCode.INVALID_INPUT,
        "Required file '$filename' is missing from export",
    )
}

private fun filesWithObjectIdPattern(
    fileContents: List<File>,
    pattern: Regex,
): List<IdEntry<File>> =
    fileContents.mapNotNull { file ->
        pattern.find(file.path.replace(File.separator, "/"))?.let { match ->
            match.groupValues.getOrNull(1)?.let { idString ->
                when {
                    ObjectId.isValid(idString) -> IdEntry(ObjectId(idString), file)
                    else -> null
                }
            }
        }
    }

private fun filesWithVersionedPartPattern(
    fileContents: List<File>,
    pattern: Regex,
): Map<ThreeDbVersionedPart, File> =
    fileContents
        .mapNotNull { file ->
            pattern.find(file.path.replace(File.separator, "/"))?.let { match ->
                val groups = match.groupValues
                val partId = groups.getOrNull(1)?.toUUID()
                val versionId = groups.getOrNull(2)?.toUUID()

                if (partId == null || versionId == null) {
                    null
                } else {
                    ThreeDbVersionedPart(partId, versionId) to file
                }
            }
        }.toMap()

@TsetSuppress("tset:reactive:flux-flatmap") // threeDb can handle it
private fun partIdMap(
    versionedParts: Iterable<ThreeDbVersionedPart>,
    threeDbService: ThreeDbService,
    accessCheck: AccessCheck,
) = versionedParts
    .map { it.part_id }
    .toSet()
    .toFlux()
    .flatMap { partId ->
        threeDbService.exists(partId, accessCheck).flatMap { exists ->
            if (exists) {
                Mono.empty()
            } else {
                threeDbService.createPart(accessCheck).map { partId to it }
            }
        }
    }.collectMap({ it.first }) { it.second }

@TsetSuppress("tset:reactive:flux-flatmap") // threeDb can handle it
private fun threeDbIdMap(
    threeDbFiles: Map<ThreeDbVersionedPart, File>,
    threeDbService: ThreeDbService,
    accessCheck: AccessCheck,
    objectMapper: ObjectMapper,
) = partIdMap(threeDbFiles.keys, threeDbService, accessCheck).flatMap { partIdMap ->
    threeDbFiles
        .toList()
        .toFlux()
        .flatMap { (versionedPart, file) ->
            val newVersionedPartInitial =
                partIdMap[versionedPart.part_id] ?: return@flatMap Mono.just(versionedPart to versionedPart)

            val data = objectMapper.readValue<Map<String, ByteArray>>(file)
            threeDbService
                .uploadEverything(
                    data,
                    accessCheck,
                    newVersionedPartInitial,
                ).map { newVersionedPart ->
                    require(newVersionedPartInitial.part_id == newVersionedPart.part_id)
                    versionedPart to newVersionedPart
                }
        }.collectMap({ it.first }) { it.second }
}

private fun attachmentsFiles(
    fileContents: List<File>,
    pattern: Regex,
): List<AttachmentEntry<File>> =
    fileContents.mapNotNull { file ->
        pattern.find(file.path.replace(File.separator, "/"))?.let { match ->
            match.groupValues.getOrNull(1)?.let { idString ->
                AttachmentEntry(idString, file)
            }
        }
    }

private fun replaceIds(
    content: ByteArray,
    idMap: Map<String, String>,
): String {
    val text = String(content, StandardCharsets.UTF_8)
    val matcher = UUID_OR_MONGO_DB_PATTERN.matcher(text)

    return matcher.replaceAll {
        val id = it.group()

        if (idMap.containsKey(id)) {
            // removing false positives from regex from the keys to replace. The idMap should have all keys.
            if (UUID_PATTERN.matcher(id).matches()) {
                idMap[id]!!.toUUID().toString()
            } else {
                idMap[id]!!
            }
        } else {
            id
        }
    }
}

private fun replaceProjectKey(
    jsonFile: String,
    oldProjectKey: String,
    newProjectKey: String,
): String {
    // needed because we don't want exception when things don't exist, we want null
    val document: DocumentContext =
        JsonPath.parse(jsonFile, Configuration.defaultConfiguration().addOptions(Option.DEFAULT_PATH_LEAF_TO_NULL))

    // we want to know if we have a refKey element
    val search = document.read<JSONArray>("$..refKey").filterNotNull()
    if (search.isNotEmpty()) {
        val element = search[0]
        if (element is String) { // this is for bomnodes, since refKey points to a string that is <key>-<number>
            document.set("$..refKey", element.replace(oldProjectKey, newProjectKey, false))
        } else { // we are in a refKey map
            document.set("$..refKey.key", newProjectKey)
            val searchText = document.read<JSONArray>("$..refKey.searchText")
            document.set(
                "$..refKey.searchText",
                (searchText[0] as String).replace(oldProjectKey, newProjectKey, false),
            )
        }
    }
    return document.jsonString()
}

private fun replaceAccount(
    jsonFile: String,
    accountId: String,
): String {
    // needed because we don't want exception when things don't exist, we want null
    val document: DocumentContext =
        JsonPath.parse(jsonFile, Configuration.defaultConfiguration().addOptions(Option.DEFAULT_PATH_LEAF_TO_NULL))

    // we want to know if we have an accountId element that follows our accepted structure
    val search = document.read<JSONArray>("$..accountId").filterNotNull()
    if (search.isNotEmpty()) {
        val element = search[0]
        if (element is Map<*, *>) {
            document.set("accountId", element.mapValues { accountId })
        }
    }
    return document.jsonString()
}

object ImportParser {
    // The returned ProjectImportContentsLight doesn't contain versioned part files, as this function already
    // processes and potentially uploads them.
    fun archiveContents(
        files: List<File>,
        projectKey: String,
        accountId: String,
        threeDbService: ThreeDbService,
        accessCheck: AccessCheck,
        objectMapper: ObjectMapper,
    ): Mono<ProjectImportContentsLight> {
        val threeDbFiles = filesWithVersionedPartPattern(files, ExportNameUtil.versionedPartsRegex)

        return threeDbIdMap(threeDbFiles, threeDbService, accessCheck, objectMapper).map { threeDbIdMap ->
            val idMap = ConcurrentHashMap<String, String>()

            threeDbIdMap.forEach { (old, new) ->
                idMap[old.version_id] = new.version_id
                idMap[old.part_id] = new.part_id
            }

            JacksonConfig.staticMapper.readValue(entityContent(files, ExportNameUtil.idsFile), List::class.java).toSet().forEach { key ->
                val stringKey = key as String
                val objectId = ObjectId.get().toHexString()
                if (UUID_PATTERN.matcher(key).matches() && !idMap.containsKey(stringKey)) {
                    val objectIdKey = UUID.fromString(stringKey).toObjectId().toHexString()
                    idMap[stringKey] = objectId
                    idMap[objectIdKey] = objectId
                } else if (!UUID_PATTERN.matcher(key).matches() && !idMap.containsKey(stringKey)) {
                    val objectIdKey = stringKey.toObjectId()!!.toHexString()
                    val uuidKey = stringKey.toUUID().toString()
                    idMap[uuidKey] = objectId
                    idMap[objectIdKey] = objectId
                }
            }

            val project = replaceIds(entityContent(files, ExportNameUtil.projectFile).readBytes(), idMap)
            val projectDto = JacksonConfig.staticMapper.readValue(project, ProjectHelper.ProjectDto::class.java)

            ProjectImportContentsLight(
                idMap = idMap,
                projectDto = projectDto,
                newProjectKey = projectKey,
                oldProjectKey = projectDto.key,
                accountId = accountId,
                bomNodesFiles = filesWithObjectIdPattern(files, ExportNameUtil.bomNodesRegex),
                snapshotFiles = filesWithObjectIdPattern(files, ExportNameUtil.snapshotRegex),
                branchFiles = filesWithObjectIdPattern(files, ExportNameUtil.branchRegex),
                turningProfilesFiles = filesWithObjectIdPattern(files, ExportNameUtil.turningProfilesRegex),
                noCalcDataFiles = filesWithObjectIdPattern(files, ExportNameUtil.noCalcDataRegex),
                millingProfilesFiles = filesWithObjectIdPattern(files, ExportNameUtil.millingProfileRegex),
                partsFiles = filesWithObjectIdPattern(files, ExportNameUtil.partsRegex),
                documentsFiles = filesWithObjectIdPattern(files, ExportNameUtil.filesRegex),
                attachmentFiles = attachmentsFiles(files, ExportNameUtil.attachmentsRegex),
                deletedBomNodesKeys = mutableListOf(),
            )
        }
    }
}
