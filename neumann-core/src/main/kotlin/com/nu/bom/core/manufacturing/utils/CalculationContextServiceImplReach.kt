package com.nu.bom.core.manufacturing.utils

import com.nu.bom.core.exception.userException.ShapeNotFoundException
import com.nu.bom.core.machining.model.MillingRequest
import com.nu.bom.core.machining.model.MillingResponse
import com.nu.bom.core.machining.model.TurningRequest
import com.nu.bom.core.machining.model.TurningResponse
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.CalculationOperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.rollupconfiguration.RollUpConfiguration
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataCategory
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.extension.classificationcalculator.ClassificationCalculatorRestriction
import com.nu.bom.core.manufacturing.extension.lookups.CO2SteelLookup
import com.nu.bom.core.manufacturing.factories.FieldFactoryService
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.CO2CalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.ClassificationResponse
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.ConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.CostCalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.Diffusivity
import com.nu.bom.core.manufacturing.fieldTypes.FieldResult
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.MaterialFurnaceType
import com.nu.bom.core.manufacturing.fieldTypes.MaterialSubstances
import com.nu.bom.core.manufacturing.fieldTypes.Pressure
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Rz
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TurningStep
import com.nu.bom.core.manufacturing.fieldTypes.configuration.ConfigurationField
import com.nu.bom.core.manufacturing.fieldTypes.configuration.VersionedCostModuleField
import com.nu.bom.core.manufacturing.service.CalculationContextService
import com.nu.bom.core.manufacturing.service.EntityClassOrName
import com.nu.bom.core.model.IMasterData
import com.nu.bom.core.model.Lookup
import com.nu.bom.core.model.MasterData
import com.nu.bom.core.model.MasterDataSelector
import com.nu.bom.core.model.Part
import com.nu.bom.core.model.TurningProfile
import com.nu.bom.core.model.configurations.ConfigurationValue
import com.nu.bom.core.model.configurations.CostModulesConfiguration
import com.nu.bom.core.model.configurations.VersionedCostModule
import com.nu.bom.core.service.configurations.ConfigType
import com.nu.bom.core.service.configurations.ConfigurationGroup
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService.Companion.MATERIAL_GROUP_FIELD_KEY
import com.nu.bom.core.service.configurations.MasterdataTsetConfigurationService.Companion.SURFACE_FINISH_FIELD_KEY
import com.nu.bom.core.service.fti.FtiService
import com.nu.bom.core.service.nexar.NexarQueryService
import com.nu.bom.core.service.nuledge.NuLedgeService
import com.nu.bom.core.smf.SmfUnfoldedPartScalingService
import com.nu.bom.core.smf.model.NestorRequest
import com.nu.bom.core.smf.model.NestorResponse
import com.nu.bom.core.smf.model.WsiUnfolderResponse
import com.nu.bom.core.turn.model.InductiveHardeningGroup
import com.nu.bom.core.turn.model.Technology
import com.nu.bom.core.turn.model.TurningOperationCycleTimeRequestData
import com.nu.bom.core.turn.model.TurningOperationResponse
import com.nu.bom.core.turn.model.TurningStepsRequest
import com.nu.bom.core.turn.model.TurningStepsResponse
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.ShapesData
import com.nu.bomrads.dto.FileUploadDto
import com.tset.bom.clients.tsetdel.model.TsetDelFileLocation
import org.bson.types.ObjectId
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.math.BigDecimal
import kotlin.reflect.KClass

const val CONFIG_TYPE_MUST_HAVE_INSTANCE_MESSAGE = "Classes of ConfigType must have an object instance"

class CalculationContextServiceImplReach(
    val calculationContextService: CalculationContextService,
    val accessCheck: AccessCheck,
) {
    fun getMasterData(
        accessCheck: AccessCheck,
        type: MasterDataType,
        key: String,
        year: Int,
        location: String,
    ): Mono<MasterData> = calculationContextService.getMasterData(accessCheck, type, key, year, location)

    fun getMasterData(
        accessCheck: AccessCheck,
        category: MasterDataCategory,
        key: String,
        year: Int,
        location: String,
    ): Mono<MasterData> = calculationContextService.getMasterData(accessCheck, category, key, year, location)

    fun getShapeInfo(
        technology: String,
        shapeId: String,
        onlyActive: Boolean = false,
    ): ShapesData.ShapeInfo? = calculationContextService.getShapeInfo(accessCheck, technology, shapeId, onlyActive)

    fun getShapeInfoOrThrow(
        technology: String,
        shapeId: String,
        onlyActive: Boolean = false,
    ): ShapesData.ShapeInfo =
        calculationContextService.getShapeInfo(accessCheck, technology, shapeId, onlyActive)
            ?: throw ShapeNotFoundException(accessCheck.accountId, technology, shapeId, onlyActive)

    fun createEntity(
        name: String,
        entityType: Entities,
        clazz: EntityClassOrName? = null,
        args: Map<String, Any> = emptyMap(),
        fields: Map<String, FieldResult<*, *>> = emptyMap(),
        overwrites: Map<String, FieldResult<*, *>> = emptyMap(),
        entityRef: String? = null,
        version: Int,
        entityId: ObjectId? = null,
    ): ManufacturingEntity =
        calculationContextService.createEntity(
            name,
            entityType,
            clazz,
            args,
            fields,
            overwrites,
            entityRef,
            version,
            entityId,
        )

    fun createManufacturing(
        name: String,
        clazz: EntityClassOrName,
        args: Map<String, Any?>,
        masterDataSelector: MasterDataSelector?,
        masterDataObjectId: ObjectId?,
        fields: Map<String, FieldResult<*, *>>,
        version: Int,
    ): BaseManufacturing =
        calculationContextService.createManufacturing(
            name,
            clazz,
            args,
            masterDataSelector,
            masterDataObjectId,
            fields,
            version,
        )

    fun getLookupTable(key: String): Mono<Map<String, String>> = calculationContextService.getLatestLookupTable(key, accessCheck)

    fun <T> getLookupTable(
        key: String,
        rowParser: (List<String>) -> T,
    ): Flux<T> =
        calculationContextService.getLatestLookupTable(
            key,
            accessCheck,
            rowParser,
        )

    fun <T> getLookupRows(
        key: String,
        rowParser: (Lookup.Row) -> T,
    ): Flux<T> = calculationContextService.getLookupRows(key, accessCheck, rowParser)

    fun lookupValue(
        lookupTableKey: String,
        lookupKey: String,
    ): Mono<String?> = calculationContextService.lookupValue(lookupTableKey, lookupKey)

    fun predictNum(
        technology: String,
        variable: String,
        inputs: Map<String, Any>,
    ): Mono<BigDecimal> = calculationContextService.predictNum(technology, variable, inputs)

    fun predictString(
        technology: String,
        variable: String,
        inputs: Map<String, Any>,
    ): Mono<String> = calculationContextService.predictString(technology, variable, inputs)

    fun <T> predict(
        technology: String,
        variable: String,
        inputs: Map<String, Any>,
    ): Mono<T> = calculationContextService.predict(technology, variable, inputs)

    fun getPart(
        accessCheck: AccessCheck,
        partId: ObjectId,
    ): Mono<Part> = calculationContextService.getPart(accessCheck, partId)

    fun createEntitiesFromTemplate(
        accessCheck: AccessCheck,
        name: String,
        location: String,
        year: Int,
        additionalArgs: Map<String, Any>,
        version: Int,
    ): Flux<ManufacturingEntity> =
        calculationContextService.createEntitiesFromTemplate(
            accessCheck,
            name,
            location,
            year,
            additionalArgs,
            version,
        )

    fun <T : ManufacturingEntity> createSystemParameter(
        accessCheck: AccessCheck,
        type: Entities,
        masterData: MasterDataType,
        clazz: KClass<out T>,
        system: String,
        year: Int,
        location: String,
        version: Int,
    ): Mono<T> =
        calculationContextService.createSystemParameter(
            accessCheck,
            type,
            masterData,
            clazz,
            system,
            year,
            location,
            version,
        )

    fun createEntityWithMasterdata(
        accessCheck: AccessCheck,
        name: String,
        entityType: Entities,
        clazz: EntityClassOrName,
        masterDataSelector: MasterDataSelector,
        version: Int,
        args: Map<String, Any> = emptyMap(),
        overwrites: Map<String, FieldResult<*, *>> = mapOf(),
        fields: Map<String, FieldResult<*, *>> = mapOf(),
        entityRef: String? = null,
        entityId: ObjectId? = null,
    ): Mono<ManufacturingEntity> =
        calculationContextService.createEntityWithMasterdata(
            accessCheck,
            name,
            entityType,
            clazz,
            masterDataSelector,
            version,
            args,
            overwrites,
            fields,
            entityRef,
            entityId,
        )

    fun getTurningProfile(turningProfileId: Text): Mono<TurningProfile> =
        calculationContextService.getTurningProfile(turningProfileId, accessCheck)

    fun <T> getTurningSequence(
        step: TurningStep,
        turningProfileId: Text,
        input: CalculationContextService.TurningProfileInputFields<T>,
        accessCheck: AccessCheck,
    ): Mono<Pair<TurningRequest<T>, TurningResponse>> =
        calculationContextService.getTurningSequence(step, turningProfileId, input, accessCheck)

    fun getTurningOperationCycleTime(request: TurningOperationCycleTimeRequestData): Mono<TurningOperationResponse> =
        calculationContextService.getTurningOperationCycleTime(request)

    fun nuLedge(): NuLedgeService = calculationContextService.nuLedge()

    fun octoPartService(): NexarQueryService = calculationContextService.octoPartService()

    fun fieldFactoryService(): FieldFactoryService = calculationContextService.fieldFactoryService()

    fun getSmfUnfoldedPartScalingService(): SmfUnfoldedPartScalingService = calculationContextService.getSmfUnfoldedPartScalingService()

    fun getFile(fileId: String): Mono<ByteArray> = calculationContextService.getFile(accessCheck, fileId)

    fun getFileInfo(fileId: String): Mono<FileUploadDto> = calculationContextService.getFileInfo(accessCheck, fileId)

    fun getClassificationCalculator(
        materialSubstances: MaterialSubstances,
        steel: CO2SteelLookup,
        materialFurnaceType: MaterialFurnaceType?,
    ): Mono<ClassificationResponse> = calculationContextService.getClassificationCalculator(materialSubstances, steel, materialFurnaceType)

    fun getClassificationCalculator(
        materialSubstances: MaterialSubstances,
        restriction: List<Pair<String, ClassificationCalculatorRestriction>>,
        materialFurnaceType: MaterialFurnaceType?,
    ): Mono<ClassificationResponse> =
        calculationContextService.getClassificationCalculator(
            materialSubstances,
            restriction,
            materialFurnaceType,
        )

    fun getNestingResponse(
        wizardId: String,
        request: NestorRequest,
        uploadNestingImage: Boolean,
    ): Mono<Pair<String?, NestorResponse>> =
        calculationContextService.getNestingResponse(wizardId, request, accessCheck, uploadNestingImage)

    fun getUnfolderResponse(
        wizardId: String,
        fileContent: String,
    ): Mono<WsiUnfolderResponse> = calculationContextService.getUnfolderResponse(wizardId, fileContent)

    fun getMillingCalculation(
        input: CalculationContextService.MillingDrillingInputFields,
        accessCheck: AccessCheck,
    ): Mono<Pair<MillingRequest, MillingResponse>> = calculationContextService.getMillingCalculation(input, accessCheck)

    fun <T> getTurningCalculationSteps(
        turningProfileId: Text,
        technology: Technology<T>,
        accessCheck: AccessCheck,
    ): Mono<Pair<TurningStepsRequest<T>, TurningStepsResponse>> =
        calculationContextService.getTurningCalculationSteps(turningProfileId, technology, accessCheck)

    fun getInductiveHardeningGroups(turningProfileId: Text): Mono<List<InductiveHardeningGroup>> =
        calculationContextService.getInductiveHardeningGroups(turningProfileId, accessCheck)

    fun getTsetDelFileLocation(
        technologyKey: String,
        shapeId: String,
    ): Mono<TsetDelFileLocation> =
        calculationContextService.getTsetDelFileLocation(
            accessCheck,
            technologyKey,
            shapeId,
        )

    fun findMachiningTool(
        accessCheck: AccessCheck,
        type: String,
        materialGroup: String?,
        entityClass: String,
        toolDiameter: BigDecimal?,
        selectedTool: String?,
        allowEqual: Boolean,
    ): Mono<List<IMasterData>> =
        calculationContextService.findMachiningTool(
            accessCheck,
            type,
            materialGroup,
            entityClass,
            toolDiameter,
            selectedTool,
            allowEqual,
        )

    fun getInjectionUtilsService(): InjectionUtilsService = calculationContextService.getInjectionUtilsService()

    fun getCostModuleConfiguration(
        type: String,
        id: ConfigurationIdentifier,
    ): Mono<VersionedCostModule> =
        calculationContextService
            .getCostModuleConfiguration(
                accessCheck,
                ConfigurationGroup.COST_MODULES_VERSION.value,
                type,
                id,
            ).onErrorResume {
                Mono.just(CostModulesConfiguration(""))
            }

    fun getCostModuleConfigurationField(
        type: String,
        id: ConfigurationIdentifier,
    ): Mono<VersionedCostModuleField> = getCostModuleConfiguration(type, id).map { VersionedCostModuleField(it) }

    inline fun <T : ConfigurationValue, reified C : ConfigType<T>> getConfiguration(key: ConfigurationKey<C>): Mono<T> =
        calculationContextService.getConfiguration(
            accessCheck,
            checkNotNull(C::class.objectInstance) { CONFIG_TYPE_MUST_HAVE_INSTANCE_MESSAGE },
            key,
        )

    inline fun <T : ConfigurationValue, reified C : ConfigType<T>, F : ConfigurationField<T>> getConfiguration(
        key: ConfigurationKey<C>,
        crossinline transform: (T) -> F,
    ): Mono<F> =
        calculationContextService
            .getConfiguration(
                accessCheck,
                checkNotNull(C::class.objectInstance) { CONFIG_TYPE_MUST_HAVE_INSTANCE_MESSAGE },
                key,
            ).map { transform(it) }

    inline fun <K : ConfigurationKey<C>, reified C : ConfigType<*>> getDefaultConfigurationKey(
        crossinline cons: (ConfigurationIdentifier) -> K,
    ): Mono<K> {
        val type = checkNotNull(C::class.objectInstance) { CONFIG_TYPE_MUST_HAVE_INSTANCE_MESSAGE }
        return getDefaultConfigurationId(type).map { cons(it) }
    }

    fun getDefaultConfigurationId(type: ConfigType<*>): Mono<ConfigurationIdentifier> =
        calculationContextService.getDefaultConfigurationKey(accessCheck, type)

    fun getDefaultCostModuleConfigurationKey(type: String): Mono<ConfigurationIdentifier> =
        calculationContextService.getDefaultCostModuleConfigurationKey(accessCheck, Model.valueOf(type))

    fun getCostCalculationConfiguration(
        costCalculationOperationsConfigurationKey: CostCalculationOperationsConfigurationKey,
        rollUpConfiguration: RollUpConfiguration,
    ): Mono<CalculationOperationConfiguration> =
        calculationContextService
            .getCostCalculationConfiguration(
                accessCheck,
                costCalculationOperationsConfigurationKey,
                rollUpConfiguration,
            )

    fun getCO2CalculationConfiguration(
        co2CalculationOperationsConfigurationKey: CO2CalculationOperationsConfigurationKey,
        rollUpConfiguration: RollUpConfiguration,
    ): Mono<CalculationOperationConfiguration> =
        calculationContextService
            .getCO2CalculationConfiguration(
                accessCheck,
                co2CalculationOperationsConfigurationKey,
                rollUpConfiguration,
            )

    fun getFtiService(): FtiService = calculationContextService.getFtiService()

    fun findMaterialDensity(
        accessCheck: AccessCheck,
        materialKey: String,
    ): Mono<Density> = calculationContextService.findMaterialDensity(accessCheck, materialKey)

    fun findMaterialDensityOrNull(
        accessCheck: AccessCheck,
        materialKey: String,
    ): Mono<Density> = calculationContextService.findMaterialDensityOrNull(accessCheck, materialKey)

    fun findMaterialGroup(
        accessCheck: AccessCheck,
        materialKey: String,
    ): Mono<Text> =
        calculationContextService.findMaterialField(
            accessCheck,
            materialKey,
            MATERIAL_GROUP_FIELD_KEY,
        )

    fun findMaterialGroupOrNull(
        accessCheck: AccessCheck,
        materialKey: String,
    ): Mono<Text> =
        calculationContextService.findMaterialFieldOrNull(
            accessCheck,
            materialKey,
            MATERIAL_GROUP_FIELD_KEY,
        )

    fun findTensileStrengthOrNull(
        accessCheck: AccessCheck,
        materialKey: String,
    ): Mono<Pressure> =
        calculationContextService.findMaterialFieldOrNull(
            accessCheck,
            materialKey,
            "tset.ref.field.tensileStrength",
        )

    fun findPreheatedMaterial(
        accessCheck: AccessCheck,
        materialKey: String,
    ): Mono<Bool> =
        calculationContextService
            .findMaterialField(
                accessCheck,
                materialKey,
                "tset.ref.field.preheated",
            )

    fun findSpecificThermalCapacity(
        accessCheck: AccessCheck,
        materialKey: String,
    ): Mono<Diffusivity> =
        calculationContextService.findMaterialField(
            accessCheck,
            materialKey,
            "tset.ref.field.specificThermalCapacity",
        )

    fun findSurfaceFinish(
        accessCheck: AccessCheck,
        materialKey: String,
    ): Mono<Rz> =
        calculationContextService.findMaterialField(
            accessCheck,
            materialKey,
            SURFACE_FINISH_FIELD_KEY,
        )

    fun findStackingFactor(
        accessCheck: AccessCheck,
        materialKey: String,
    ): Mono<Rate> =
        calculationContextService.findMaterialField(
            accessCheck,
            materialKey,
            "tset.ref.field.stackingFactor",
        )

    fun findLamellaThickness(
        accessCheck: AccessCheck,
        materialKey: String,
    ): Mono<Length> =
        calculationContextService.findMaterialField(
            accessCheck,
            materialKey,
            "tset.ref.field.lamellaThickness",
        )

    fun findMaterialFields(
        accessCheck: AccessCheck,
        materialKey: String,
    ): Mono<Map<String, FieldResultStar>> =
        calculationContextService.findMaterialFields(
            accessCheck,
            materialKey,
        )

    fun findMaterialName(
        accessCheck: AccessCheck,
        materialKey: String,
    ): Mono<String> =
        calculationContextService.findMaterialName(
            accessCheck,
            materialKey,
        )
}
