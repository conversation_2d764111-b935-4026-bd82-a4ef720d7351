package com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel

import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationRole
import com.nu.bom.core.manufacturing.commercialcalculation.enums.StandardCalculationValue
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.CalculationOperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.OperationWithStandardCalculationValue
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.SumProdOperation
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.InternalCommercialCalculationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.TopLevelUiCardBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.helper.ColumnCreator
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.CommercialFieldNameAndLookupCreatorHelper
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.CommercialFieldTableHelper
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.CommercialUiCardBuilderHelper
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel.CollapsingHelper.isCollapsedManufacturingTables
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableOption
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

@Service
@Profile("!test", "manufacturingStepBreakdownCardBuilderTest")
class ManufacturingStepBreakdownCardBuilder : TopLevelUiCardBuilder {
    override val cardIdentifier = "rmocScrap"

    private val aggregationLevel = AggregationLevel.MANUFACTURING_STEP
    private val maxDepth = Int.MAX_VALUE
    private val fieldAndLookupHelper = CommercialFieldNameAndLookupCreatorHelper(aggregationLevel)
    private val cardHelper = CommercialUiCardBuilderHelper(::getEntryPoint, maxDepth, fieldAndLookupHelper)
    private val tableHelper = CommercialFieldTableHelper(::getEntryPoint, ::getColumnCreators, ::getIsCollapsed)

    override fun getTitles(vararg configs: InternalCommercialCalculationConfiguration) = configs.associate { it.valueType to "Breakdown" }

    override fun getKpis(vararg configs: InternalCommercialCalculationConfiguration) =
        cardHelper.getKpis(configs = configs, AggregationRole.THIS)

    override fun getCardFields(vararg configs: InternalCommercialCalculationConfiguration) = null

    override fun getTablesVariations(vararg configs: InternalCommercialCalculationConfiguration) = cardHelper.getTablesVariations(*configs)

    override fun getTableConfig(
        valueType: ValueType,
        tableOption: TableOption,
        procurementType: ManufacturingType,
        vararg configs: InternalCommercialCalculationConfiguration,
    ) = tableHelper.getTableConfig(valueType, tableOption, procurementType, *configs)

    private fun getIsCollapsed(
        opConfig: CalculationOperationConfiguration,
        operation: SumProdOperation,
    ): Boolean = isCollapsedManufacturingTables(getEntryPoint(opConfig), operation)

    private fun getEntryPoint(opConfig: CalculationOperationConfiguration): OperationWithStandardCalculationValue =
        opConfig.getOperationOfStandardValue(StandardCalculationValue.TOTAL_MANUFACTURING_VALUE)

    private fun getColumnCreators(config: InternalCommercialCalculationConfiguration): List<ColumnCreator> {
        return listOfNotNull(
            fieldAndLookupHelper.displayValueColumnCreator(config),
            fieldAndLookupHelper.interestTimeLookupColumnCreator(config.opConfig),
            fieldAndLookupHelper.rateLookupColumnCreator(config.opConfig),
            fieldAndLookupHelper.roleThisLookupColumnCreator(config.valueType, fieldAndLookupHelper.getTotalDisplayName(config.valueType)),
        )
    }
}
