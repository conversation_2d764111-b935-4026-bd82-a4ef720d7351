package com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel

import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.StandardCalculationValue
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.CalculationOperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.UniqueOperationIdentifier
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.IndirectOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.OperationWithStandardCalculationValue
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.InternalCommercialCalculationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.TopLevelUiCardBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.helper.EntityTableColumns.commercialColumn
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.helper.EntityTableColumns.mainColumn
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.helper.EntityTableColumns.staticColumn
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.CommercialEntityTableHelper
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.CommercialFieldNameAndLookupCreatorHelper
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.CommercialUiCardBuilderHelper
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.OperationConfigurationInformationExtractor.getAddendsOfOperation
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.OperationWithAdditionalInfoForTable
import com.nu.bom.core.manufacturing.entities.ManufacturingStep
import com.nu.bom.core.manufacturing.entities.ManufacturingStepLineFields
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.CardIdentifier
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableOption
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityLocator
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableActionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableColumnDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableRowDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EqualCriteria
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.LocatorType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.options.ColumnOptionsFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.options.TableOptionsFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.tableactiondefinitions.GroupTableActionDefinition
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.tableactiondefinitions.TrivialTableActionDefinition
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

@Service
class ManufacturingStepEntityCardBuilder : TopLevelUiCardBuilder {
    override val cardIdentifier: CardIdentifier = "manufacturingStepsTable"

    private val aggregationLevel = AggregationLevel.MANUFACTURING_STEP
    private val allowedAggregationLevels =
        setOf(
            AggregationLevel.MANUFACTURING_STEP,
            AggregationLevel.MACHINE,
            AggregationLevel.LABOR,
            AggregationLevel.TOOL,
            AggregationLevel.ROUGH_PROCESS,
        )

    private val groupActionDefinition =
        GroupTableActionDefinition(
            groupIdentifierField = ManufacturingStep::groupId.name,
            inconsistentFieldsCollectionField = ManufacturingStepLineFields::outOfSyncGroupParams.name,
        )
    private val rows =
        listOf(
            EntityTableRowDefinitionFeDto(
                "step",
                EntityLocator(
                    listOf(EqualCriteria("type", Entities.MANUFACTURING_STEP.name)),
                    LocatorType.ANCESTOR,
                ),
                actionKeys =
                    mapOf(
                        EntityTableActionFeDto.GROUP to groupActionDefinition,
                        EntityTableActionFeDto.UNGROUP to groupActionDefinition,
                        EntityTableActionFeDto.OPEN_IN_NEW_TAB to TrivialTableActionDefinition,
                        EntityTableActionFeDto.COPY_LINK_TO_CLIPBOARD to TrivialTableActionDefinition,
                        EntityTableActionFeDto.GO_TO_MASTERDATA to TrivialTableActionDefinition,
                        EntityTableActionFeDto.DUPLICATE to TrivialTableActionDefinition,
                        EntityTableActionFeDto.COPY_TABLE to TrivialTableActionDefinition,
                        EntityTableActionFeDto.DELETE to TrivialTableActionDefinition,
                    ),
            ),
        )

    private val fieldAndLookupHelper = CommercialFieldNameAndLookupCreatorHelper(aggregationLevel)
    private val cardHelper = CommercialUiCardBuilderHelper(::getEntryPoint, Int.MAX_VALUE, fieldAndLookupHelper)
    private val tableHelper =
        CommercialEntityTableHelper(
            entryPointFromConfig = ::getEntryPoint,
            includeTopLevel = true,
            takeOnlyLeaves = false,
            maximalExpansionDepth = Int.MAX_VALUE,
            allowedAggregationLevels = allowedAggregationLevels,
            columns = ::getColumns,
            TableOptionsFeDto(reverseRows = true, isTableDraggable = true),
        ) { _, _, _ -> rows }

    override fun getTitles(vararg configs: InternalCommercialCalculationConfiguration) = ValueType.entries.associateWith { "Steps" }

    override fun getKpis(vararg configs: InternalCommercialCalculationConfiguration) = null

    override fun getCardFields(vararg configs: InternalCommercialCalculationConfiguration) = null

    override fun getTablesVariations(vararg configs: InternalCommercialCalculationConfiguration) = cardHelper.getTablesVariations(*configs)

    override fun getTableConfig(
        valueType: ValueType,
        tableOption: TableOption,
        procurementType: ManufacturingType,
        vararg configs: InternalCommercialCalculationConfiguration,
    ) = tableHelper.getTableConfig(valueType, tableOption, *configs)

    private fun getEntryPoint(opConfig: CalculationOperationConfiguration): OperationWithStandardCalculationValue =
        opConfig.getOperationOfStandardValue(StandardCalculationValue.TOTAL_MANUFACTURING_VALUE)

    private fun getColumns(
        config: InternalCommercialCalculationConfiguration,
        operationsWithAdditionalInfoForTable: List<OperationWithAdditionalInfoForTable>,
    ): List<EntityTableColumnDefinitionFeDto> {
        val staticColumns =
            listOf(
                EntityTableConfigFeDto.checkBoxField,
                mainColumn(sortable = null),
                staticColumn(ManufacturingStep::templateName, ColumnOptionsFeDto(isHidden = true)),
                staticColumn(ManufacturingStep::cycleTime),
                staticColumn(ManufacturingStep::partsPerCycle),
                staticColumn(ManufacturingStep::throughput, ColumnOptionsFeDto(editable = false, isHidden = true)),
                staticColumn(ManufacturingStep::theoreticalThroughput, ColumnOptionsFeDto(editable = false, isHidden = true)),
            )

        return staticColumns + getCommercialColumns(config, operationsWithAdditionalInfoForTable)
    }

    private fun getCommercialColumns(
        config: InternalCommercialCalculationConfiguration,
        operationsWithAdditionalInfoForTable: List<OperationWithAdditionalInfoForTable>,
    ): List<EntityTableColumnDefinitionFeDto> =
        operationsWithAdditionalInfoForTable.mapNotNull { opWithDepth ->

            val options =
                when (opWithDepth.depth) {
                    0 -> ColumnOptionsFeDto(hasTotal = true)
                    1 -> {
                        val isLeaf = getAddendsOfOperation(config.opConfig, opWithDepth.operation).isEmpty()
                        if (isLeaf) ColumnOptionsFeDto(hasTotal = true) else null
                    }
                    2 -> ColumnOptionsFeDto(hasTotal = true)
                    3 -> ColumnOptionsFeDto(hasTotal = true, isHidden = true)
                    else -> null
                }

            options?.let {
                val rateColumn =
                    commercialColumn(config, opWithDepth.operation, ColumnOptionsFeDto(isHidden = true)) {
                        fieldAndLookupHelper.rateColumnName(config.opConfig, UniqueOperationIdentifier.fromOperation(it))
                    }.takeIf { opWithDepth.operation is IndirectOperation }

                val valueColumn =
                    commercialColumn(config, opWithDepth.operation, options) {
                        fieldAndLookupHelper.roleThisFieldName(config.valueType, UniqueOperationIdentifier.fromOperation(it))
                    }

                listOfNotNull(rateColumn, valueColumn)
            }
        }.flatten()
}
