package com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards

import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.CalculationOperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.InternalConfigurationOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.OperationWithStandardCalculationValue
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.InternalCommercialCalculationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.OperationConfigurationInformationExtractor.expandOperationNested
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableOption
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableColumnDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableRowDefinitionFeDto

class CommercialEntityTableHelper(
    private val entryPointFromConfig: (CalculationOperationConfiguration) -> OperationWithStandardCalculationValue,
    private val includeTopLevel: Boolean,
    private val maximalExpansionDepth: (ValueType) -> Int,
    private val allowedAggregationLevels: Set<AggregationLevel>,
    private val columns: (
        InternalCommercialCalculationConfiguration,
        List<InternalConfigurationOperation>,
    ) -> List<EntityTableColumnDefinitionFeDto>,
    private val rowsForTableOption: (TableOptionEnum, CalculationOperationConfiguration, ValueType) -> List<EntityTableRowDefinitionFeDto>,
) {
    fun getTableConfig(
        valueType: ValueType,
        tableOption: TableOption,
        vararg configs: InternalCommercialCalculationConfiguration,
    ): EntityTableConfigFeDto {
        val config =
            checkNotNull(configs.singleOrNull { it.valueType == valueType }) {
                "Non unique definition of the operation Configuration for `$valueType`"
            }
        val tableOptionEnum = TableOptionEnum.fromDisplayableOptionName(tableOption)

        val tableRowDefinitions = rowsForTableOption(tableOptionEnum, config.opConfig, valueType)

        return EntityTableConfigFeDto(
            rows = tableRowDefinitions.map { it.id },
            rowDefinitions = tableRowDefinitions.associateBy { it.id },
            columns = columns(config, leafAddendsForEntryPoint(config.opConfig, tableOptionEnum)),
        )
    }

    fun leafAddendsForEntryPoint(
        opConfig: CalculationOperationConfiguration,
        tableOption: TableOptionEnum,
    ): List<InternalConfigurationOperation> {
        val entryPoint = entryPointFromConfig(opConfig)
        val tableOperationExpansionLogic =
            when (tableOption) {
                TableOptionEnum.PRODUCTION ->
                    TableOperationExpansionLogic.withoutActivity(
                        entryPoint,
                        maximalExpansionDepth(opConfig.valueType),
                        includeTopLevel,
                    )

                TableOptionEnum.ACTIVITY ->
                    TableOperationExpansionLogic.withActivity(
                        entryPoint,
                        maximalExpansionDepth(opConfig.valueType),
                        includeTopLevel,
                        opConfig,
                    )
            }
        return expandOperationNested(opConfig, tableOperationExpansionLogic).filter { it.origin in allowedAggregationLevels }
    }
}
