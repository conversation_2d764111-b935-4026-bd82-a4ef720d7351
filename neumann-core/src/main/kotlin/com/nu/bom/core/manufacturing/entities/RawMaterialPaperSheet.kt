package com.nu.bom.core.manufacturing.entities

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.CompositeMandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntity
import com.nu.bom.core.manufacturing.annotations.MandatoryForEntityContext
import com.nu.bom.core.manufacturing.annotations.MasterDataCalculation
import com.nu.bom.core.manufacturing.annotations.MasterDataType
import com.nu.bom.core.manufacturing.annotations.Technologies
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.SurfaceDensity
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.nu.bom.core.manufacturing.fieldTypes.pbox.PaperCategory
import com.nu.bom.core.manufacturing.fieldTypes.pbox.PaperCategoryPaperSheet

@EntityType(Entities.MATERIAL)
@MasterDataType(com.nu.bom.core.manufacturing.enums.MasterDataType.RAW_MATERIAL_PAPER_SHEET)
@Technologies([Model.PBOX])
@Suppress("unused")
class RawMaterialPaperSheet(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = RawMaterial(name)

    fun costUnit() = Text(WeightUnits.TON.name)

    // Paper sheets do not have a density, but due to annotation-inheritance
    // we have to "overwrite" the annotations that could make this field show up.
    // Introducing an "extends" RawMaterial-type without a density instead would
    // either make for a lot of code-duplication or require a refactor of RawMaterial.
    @Input(false)
    @CompositeMandatoryForEntity(
        [
            MandatoryForEntity(context = MandatoryForEntityContext.CREATE_FOR_MASTERDATA, value = false),
            MandatoryForEntity(context = MandatoryForEntityContext.CREATE_FROM_MASTERDATA, value = false),
        ],
    )
    fun density(): Density? = null

    @Input
    @MandatoryForEntity(index = 11, context = MandatoryForEntityContext.CREATE_FOR_MASTERDATA)
    fun paperQuality(): SurfaceDensity = throw MissingInputError()

    @Input
    @MandatoryForEntity(index = 10, context = MandatoryForEntityContext.CREATE_FOR_MASTERDATA)
    fun paperCategoryPaperSheet(): PaperCategoryPaperSheet = throw MissingInputError()

    @MasterDataCalculation
    fun paperCategory(paperCategoryPaperSheet: PaperCategoryPaperSheet) = PaperCategory(paperCategoryPaperSheet)
}
