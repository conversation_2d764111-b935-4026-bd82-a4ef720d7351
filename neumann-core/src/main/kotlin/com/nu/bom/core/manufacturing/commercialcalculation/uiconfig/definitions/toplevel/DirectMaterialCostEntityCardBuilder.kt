package com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel

import com.nu.bom.core.api.dtos.ManufacturingDto
import com.nu.bom.core.manufacturing.commercialcalculation.enums.AggregationLevel
import com.nu.bom.core.manufacturing.commercialcalculation.enums.StandardCalculationValue
import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostMaterialUsage
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostPurchasedMaterial
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.CalculationConfigurationException
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.CalculationOperationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.UniqueOperationIdentifier
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.IndirectOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.InternalConfigurationOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.MaterialScrapOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.OperationWithStandardCalculationValue
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.SumProdOperation
import com.nu.bom.core.manufacturing.commercialcalculation.operationConfiguration.internalConfigurationOperations.TransferOperation
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.InternalCommercialCalculationConfiguration
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.TopLevelUiCardBuilder
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.helper.CriteriaHelper
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.helper.EntityTableColumns.commercialColumn
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.helper.EntityTableColumns.mainColumn
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.helper.EntityTableColumns.staticColumn
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.CommercialEntityTableHelper
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.CommercialFieldNameAndLookupCreatorHelper
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.CommercialUiCardBuilderHelper
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.OperationConfigurationInformationExtractor.expandOperationNested
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.OperationWithAdditionalInfoForTable
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.TableOperationExpansionLogic
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.builder.methodologybasedcards.TableOptionEnum
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel.MaterialEntityTableCriteria.EditableConfig
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel.MaterialEntityTableCriteria.bomEntryCriteria
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel.MaterialEntityTableCriteria.criteriaOfProcurementAndMaterialType
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel.MaterialEntityTableCriteria.readOnlyParentStepCriteria
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.toplevel.MaterialEntityTableCriteria.readOnlyQuantityAndOverridePurchasePriceCriteria
import com.nu.bom.core.manufacturing.entities.BaseEntityFields
import com.nu.bom.core.manufacturing.entities.ManualBaseMaterial
import com.nu.bom.core.manufacturing.extension.BaseCO2MaterialProcessing
import com.nu.bom.core.manufacturing.fieldTypes.ManufacturingType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.CardIdentifier
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableOption
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityLocator
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableActionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableColumnDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableFieldDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityTableRowDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EqualCriteria
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.LocatorType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.options.ColumnOptionsFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.tableactiondefinitions.RearrangeTableActionDefinition
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.tableactiondefinitions.TrivialTableActionDefinition
import org.springframework.stereotype.Service

@Service
class DirectMaterialCostEntityCardBuilder : TopLevelUiCardBuilder {
    override val cardIdentifier: CardIdentifier = "directMaterialCost"

    private val aggregationLevel = AggregationLevel.MATERIAL_USAGE
    private val allowedAggregationLevels = setOf(AggregationLevel.MATERIAL_USAGE, AggregationLevel.SUB_MATERIAL)
    private val maximalExpansionDepth = 1

    private val fieldAndLookupHelper = CommercialFieldNameAndLookupCreatorHelper(aggregationLevel)
    private val cardHelper =
        CommercialUiCardBuilderHelper(::getEntryPoint, maximalExpansionDepth, fieldAndLookupHelper)
    private val tableHelper =
        CommercialEntityTableHelper(
            entryPointFromConfig = ::getEntryPoint,
            includeTopLevel = false,
            takeOnlyLeaves = true,
            maximalExpansionDepth = maximalExpansionDepth,
            allowedAggregationLevels = allowedAggregationLevels,
            columns = ::defineColumns,
            tableOptionsFeDto = null,
            rowsForTableOption = ::defineRows,
        )

    override fun getTitles(vararg configs: InternalCommercialCalculationConfiguration) = cardHelper.getTitles(*configs)

    override fun getKpis(vararg configs: InternalCommercialCalculationConfiguration) = null

    override fun getCardFields(vararg configs: InternalCommercialCalculationConfiguration) = null

    override fun getTablesVariations(vararg configs: InternalCommercialCalculationConfiguration) = cardHelper.getTablesVariations(*configs)

    override fun getTableConfig(
        valueType: ValueType,
        tableOption: TableOption,
        procurementType: ManufacturingType,
        vararg configs: InternalCommercialCalculationConfiguration,
    ) = tableHelper.getTableConfig(valueType, tableOption, *configs)

    private fun getEntryPoint(opConfig: CalculationOperationConfiguration): OperationWithStandardCalculationValue =
        opConfig.getOperationOfStandardValue(StandardCalculationValue.TOTAL_MATERIAL_VALUE)

    private fun defineColumns(
        config: InternalCommercialCalculationConfiguration,
        operationsWithInfo: List<OperationWithAdditionalInfoForTable>,
    ): List<EntityTableColumnDefinitionFeDto> {
        val nameColumn = mainColumn()
        val itemNumberColumn = staticColumn(ManualBaseMaterial::itemNumber, ColumnOptionsFeDto(editable = false))
        val parentStepColumn = staticColumn(BaseEntityFields::parentStepRef)
        val customProcurementTypeColumn = staticColumn(CommercialCalculationCostMaterialUsage::customProcurementType)
        val quantityColumn = staticColumn(CommercialCalculationCostMaterialUsage::purchasedQuantity)
        val emissionPerUnitColumn = staticColumn(BaseCO2MaterialProcessing::cO2PerUnit)
        val purchasePriceColumn = staticColumn(CommercialCalculationCostPurchasedMaterial::purchasePrice)

        val staticColumns =
            when (config.valueType) {
                ValueType.CO2 ->
                    listOf(
                        nameColumn,
                        itemNumberColumn,
                        parentStepColumn,
                        customProcurementTypeColumn,
                        quantityColumn,
                        emissionPerUnitColumn,
                    )

                ValueType.COST ->
                    listOf(
                        nameColumn,
                        itemNumberColumn,
                        parentStepColumn,
                        customProcurementTypeColumn,
                        quantityColumn,
                        purchasePriceColumn,
                    )
            }
        val commercialColumns =
            operationsWithInfo.map { it.operation }.flatMap { op ->
                val subOps =
                    expandOperationNested(
                        config.opConfig,
                        TableOperationExpansionLogic(op, Int.MAX_VALUE, takeOnlyLeaves = false, includeTopLevel = true),
                    )

                listOfNotNull(
                    staticColumn(CommercialCalculationCostMaterialUsage::reuseOfScrap)
                        .takeIf { subOps.any { it.operation is MaterialScrapOperation } },
                    commercialColumn(config, op, ColumnOptionsFeDto()) {
                        fieldAndLookupHelper.rateColumnName(
                            config.opConfig,
                            UniqueOperationIdentifier.fromOperation(it),
                        )
                    }.takeIf { subOps.any { it.operation is IndirectOperation } },
                    commercialColumn(config, op, ColumnOptionsFeDto(hasTotal = true)) {
                        fieldAndLookupHelper.roleThisFieldName(
                            config.valueType,
                            UniqueOperationIdentifier.fromOperation(it),
                        )
                    },
                )
            }
        return staticColumns + commercialColumns
    }

    /**
     * Depending on procurementType, materialClass, bomEntry existence, cost module creation etc.
     * several columns might be editable or not. Therefore, for each combination of those options we provide one row definition.
     */
    private fun defineRows(
        tableOption: TableOptionEnum,
        opConfig: CalculationOperationConfiguration,
        valueType: ValueType,
    ): List<EntityTableRowDefinitionFeDto> {
        val transferConfig = transferCriteria(opConfig, tableOption)
        val inHouseConfig = inHouseCriteria(opConfig, tableOption)

        return (transferConfig + inHouseConfig).flatMap { procTypeConfigWithId ->
            readOnlyQuantityAndOverridePurchasePriceCriteria(valueType).flatMap { quantityConfigWithId ->
                readOnlyParentStepCriteria().flatMap { parentStepConfigWithId ->
                    bomEntryCriteria().map { designationConfigIdWithId ->
                        val locationCriteria =
                            CriteriaHelper.and(
                                procTypeConfigWithId.value.criteria,
                                parentStepConfigWithId.value.criteria,
                                designationConfigIdWithId.value.criteria,
                                quantityConfigWithId.value.criteria,
                            )

                        EntityTableRowDefinitionFeDto(
                            id =
                                listOfNotNull(
                                    procTypeConfigWithId.key,
                                    parentStepConfigWithId.key,
                                    designationConfigIdWithId.key,
                                    quantityConfigWithId.key,
                                ).joinToString("_"),
                            collectBy = EntityLocator(listOf(locationCriteria), LocatorType.ANCESTOR),
                            specialColumns =
                                procTypeConfigWithId.value.specialColumnDefinition +
                                    parentStepConfigWithId.value.specialColumnDefinition +
                                    designationConfigIdWithId.value.specialColumnDefinition +
                                    quantityConfigWithId.value.specialColumnDefinition,
                            actionKeys =
                                mapOf(
                                    EntityTableActionFeDto.OPEN_IN_NEW_TAB to TrivialTableActionDefinition,
                                    EntityTableActionFeDto.COPY_LINK_TO_CLIPBOARD to TrivialTableActionDefinition,
                                    EntityTableActionFeDto.GO_TO_MASTERDATA to TrivialTableActionDefinition,
                                    EntityTableActionFeDto.COPY_TABLE to TrivialTableActionDefinition,
                                    EntityTableActionFeDto.DELETE to TrivialTableActionDefinition,
                                    EntityTableActionFeDto.REARRANGE to
                                        RearrangeTableActionDefinition(BaseEntityFields::parentStepRef.name),
                                ),
                        )
                    }
                }
            }
        }
    }

    private fun transferCriteria(
        opConfig: CalculationOperationConfiguration,
        tableOption: TableOptionEnum,
    ): Map<String, EditableConfig> =
        opConfig.getOperations<TransferOperation>().associate {
            val specialColumns =
                specialColumnsDependingOnTheTransferOperationWithoutInHouse(opConfig, tableOption, it.destinationElementKey)
            val procurementTypeCriteria = criteriaOfProcurementAndMaterialType(it.procurementAndMaterialClasses)

            it.destinationElementKey to EditableConfig(procurementTypeCriteria, specialColumns)
        }

    private fun inHouseCriteria(
        opConfig: CalculationOperationConfiguration,
        tableOption: TableOptionEnum,
    ): Pair<String, EditableConfig> {
        val inHouseDestinationKey = "INHOUSE"
        val specialColumns = specialInHouseColumns(opConfig, tableOption)
        val customProcurementTypeAsString = opConfig.procurementTypeConfiguration.getDefaultInhouseProcurementType().value
        val criteria = EqualCriteria(ManufacturingDto::customProcurementType.name, customProcurementTypeAsString)

        return inHouseDestinationKey to EditableConfig(criteria, specialColumns)
    }

    /**
     *  Due to the fact that the direct addends are shown in the material table
     *  we override here with the correct values depending on the transfer of each row.
     */
    private fun specialColumnsDependingOnTheTransferOperationWithoutInHouse(
        opConfig: CalculationOperationConfiguration,
        tableOption: TableOptionEnum,
        transferOperationDestinationElementKey: String,
    ): Map<String, EntityTableFieldDefinitionFeDto> {
        val (_, opsAfterDirectMaterialOp) =
            tableHelper.addendsForEntryPoint(opConfig, tableOption).partition {
                (it.operation as? SumProdOperation)?.standardCalculationValue == StandardCalculationValue.TOTAL_DIRECT_MATERIAL_VALUE
            }

        return opsAfterDirectMaterialOp
            .flatMap {
                val columnOperationId = UniqueOperationIdentifier.fromOperation(it.operation)

                val overrideOperationId =
                    getIdOfIndirectOperationForThatTransfer(opConfig, it.operation, transferOperationDestinationElementKey)
                val valueOverride = fieldAndLookupHelper.roleThisFieldName(opConfig.valueType, overrideOperationId)
                val rateOverride = fieldAndLookupHelper.maybeRateFieldName(opConfig, overrideOperationId)

                listOfNotNull(
                    rateOverride?.let {
                        fieldAndLookupHelper.rateColumnName(
                            opConfig,
                            columnOperationId,
                        ) to EntityTableFieldDefinitionFeDto(rateOverride)
                    },
                    fieldAndLookupHelper.roleThisFieldName(
                        opConfig.valueType,
                        columnOperationId,
                    ) to EntityTableFieldDefinitionFeDto(valueOverride),
                )
            }.toMap()
    }

    private fun specialInHouseColumns(
        opConfig: CalculationOperationConfiguration,
        tableOption: TableOptionEnum,
    ): Map<String, EntityTableFieldDefinitionFeDto> {
        val (directMaterialOp, opsAfterDirectMaterialOp) =
            tableHelper.addendsForEntryPoint(opConfig, tableOption).partition {
                (it.operation as? SumProdOperation)?.standardCalculationValue == StandardCalculationValue.TOTAL_DIRECT_MATERIAL_VALUE
            }

        val transferSpecificOverrides =
            opsAfterDirectMaterialOp.associate {
                val columnOperationId = UniqueOperationIdentifier.fromOperation(it.operation)
                val valueOverride = fieldAndLookupHelper.roleInHouseFieldName(opConfig.valueType, columnOperationId)

                fieldAndLookupHelper.roleThisFieldName(
                    opConfig.valueType,
                    columnOperationId,
                ) to EntityTableFieldDefinitionFeDto(valueOverride)
            }

        val id = UniqueOperationIdentifier.fromOperation(directMaterialOp.single().operation)
        val inhouseDirectFieldName =
            EntityTableFieldDefinitionFeDto(fieldAndLookupHelper.roleInHouseFieldName(opConfig.valueType, id))
        val directMaterialInhouseOverride =
            mapOf(fieldAndLookupHelper.roleThisFieldName(opConfig.valueType, id) to inhouseDirectFieldName)

        return directMaterialInhouseOverride + transferSpecificOverrides
    }

    private fun getIdOfIndirectOperationForThatTransfer(
        opConfig: CalculationOperationConfiguration,
        operation: InternalConfigurationOperation,
        transferOperationDestinationElementKey: String,
    ): UniqueOperationIdentifier {
        val ops =
            expandOperationNested(
                opConfig,
                TableOperationExpansionLogic(operation, Int.MAX_VALUE, takeOnlyLeaves = true, includeTopLevel = true),
            ).map { it.operation }.filterIsInstance<IndirectOperation>()
        val operationComingFromThatTransferTree =
            ops.filter { op ->
                op.baseAddends.any { baseAddend -> baseAddend.elementKey == transferOperationDestinationElementKey }
            }
        return when (operationComingFromThatTransferTree.size) {
            0 -> UniqueOperationIdentifier("THIS_FIELD_DOESNT_EXISTS", AggregationLevel.MATERIAL_USAGE)
            1 -> UniqueOperationIdentifier.fromOperation(operationComingFromThatTransferTree.single())
            else -> throw CalculationConfigurationException(
                CalculationConfigurationException.ErrorCode.TOO_MANY_OPERATIONS,
                "For $transferOperationDestinationElementKey there are more that one operation contributing " +
                    "to costs of ${operation.destinationElementKey}." +
                    "Please create a higher level operation contributing to the total material costs.",
            )
        }
    }
}
