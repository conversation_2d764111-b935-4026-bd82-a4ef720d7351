package com.nu.bom.core.service.file

import com.nu.bom.core.user.AccessCheck
import com.nu.bomrads.dto.FileUploadDto
import com.nu.bomrads.id.FileUploadId
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

/**
 * Secure file service with access checks, virus checks and file upload metadata management.
 * */
interface SecureFileService {
    /**
     * Securely uploads the payload to the configured storage without creating a FileDocument.
     * @return the [String] representing the unique identifier.
     * */
    fun save(
        uploadType: UploadType,
        file: UploadablePayload,
        accessCheck: AccessCheck,
    ): Mono<String>

    /**
     * Securely uploads the payload to the configured storage.
     * @return the [UploadResponse].
     * */
    fun upload(
        accessCheck: AccessCheck,
        uploadType: UploadType,
        ownerId: String?,
        file: UploadablePayload,
    ): Mono<UploadResponse>

    /**
     * Securely downloads the payload corresponding of the given [id] (nbk internal id).
     * @return the [DownloadResponse] containing the payload.
     * */
    fun download(
        accessCheck: AccessCheck,
        id: String,
    ): Mono<DownloadResponse>

    /**
     * Securely lists downloadable resources of the given owner.
     * */
    fun list(
        accessCheck: AccessCheck,
        uploadType: UploadType,
        ownerId: String,
    ): Flux<DownloadListResponse>

    /**
     * Securely deletes the upload of the given [id]
     * */
    fun delete(
        accessCheck: AccessCheck,
        id: String,
    ): Mono<Void>

    /**
     * Securely assigns user uploads to an owner.
     * */
    fun patch(
        accessCheck: AccessCheck,
        ids: List<String>,
        ownerId: String,
    ): Flux<UploadResponse>

    /**
     * Securely migrates the upload from one owner to another.
     * */
    fun changeOwner(
        accessCheck: AccessCheck,
        id: String,
        ownerId: String,
    ): Mono<UploadResponse>

    /**
     * Securely copies the upload to another owner and type.
     * */
    fun copy(
        accessCheck: AccessCheck,
        id: String,
        ownerId: String,
        uploadType: String?,
    ): Mono<UploadResponse>

    /**
     * Adds owner to existing file, since currently we don't update snapshots with new files after copy past
     */
    fun addOwner(
        accessCheck: AccessCheck,
        id: String,
        ownerId: String,
    ): Mono<UploadResponse>

    /**
     * Get file upload by file upload id
     */
    fun get(
        accessCheck: AccessCheck,
        id: FileUploadId,
    ): Mono<FileUploadDto>

    /**
     * Creates FileUploadId pointing at an already existing file on S3
     * */
    fun createFileUploadFromS3uuid(
        accessCheck: AccessCheck,
        s3uuid: String,
        fileName: String,
        uploadType: UploadType,
        mimeType: String,
        ownerId: String?,
    ): Mono<UploadResponse>
}
