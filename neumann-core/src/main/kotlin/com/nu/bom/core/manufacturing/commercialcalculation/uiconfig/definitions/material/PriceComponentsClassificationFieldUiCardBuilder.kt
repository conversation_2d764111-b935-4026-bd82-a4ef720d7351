package com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.definitions.material

import com.nu.bom.core.manufacturing.commercialcalculation.enums.ValueType
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.MaterialUiConfigurationIdentifiers
import com.nu.bom.core.manufacturing.commercialcalculation.uiconfig.structure.identifier.values.ClassificationFieldInfo
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.TableOption
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.CardOptionsFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.cardconfig.FieldConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EntityLocator
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.EqualCriteria
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableColumnDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableConfigFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.FieldTableRowDefinitionFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.LocatorType
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.LookupCellFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.ValueCellFeDto
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.ValueCellField
import com.nu.bom.core.publicapi.dtos.configurations.uiconfiguration.tableConfig.options.ColumnOptionsFeDto
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service

const val PRICE_COMPONENTS_CLASSIFICATION_FIELD_DISPLAY_DESIGNATION = "Price components classification fields"

@Service
@Profile("!test", "materialPriceCardBuilderTest")
class PriceComponentsClassificationFieldUiCardBuilder : BaseMaterialUiCardBuilder() {
    override val cardIdentifier = "priceComponentsclassificationfields"

    override fun getViews(identifiers: MaterialUiConfigurationIdentifiers): Set<ValueType> = setOf(ValueType.COST)

    override fun getCardOptions(identifiers: MaterialUiConfigurationIdentifiers): Map<ValueType, CardOptionsFeDto>? =
        mapOf(ValueType.COST to CardOptionsFeDto(isCollapsible = true))

    override fun getTitleMappings() =
        mapOf(
            ValueType.COST to PRICE_COMPONENTS_CLASSIFICATION_FIELD_DISPLAY_DESIGNATION,
        )

    override fun getCardFields(identifiers: MaterialUiConfigurationIdentifiers): Map<ValueType, FieldConfigFeDto>? = null

    override fun getTablesVariations(identifiers: MaterialUiConfigurationIdentifiers): Map<ValueType, List<TableOption>> =
        mapOf(
            ValueType.COST to listOf("classification-fields"),
        )

    override fun getTableConfig(
        valueType: ValueType,
        tableOption: TableOption,
        identifiers: MaterialUiConfigurationIdentifiers,
    ): FieldTableConfigFeDto {
        val classificationFields = identifiers.classificationFieldsIdentifierKey()

        val rows = classificationFields.map { it.fieldName }

        val rowDefinitions = createRowDefinitions(classificationFields)

        val columns = createColumns()

        val tableConfig =
            FieldTableConfigFeDto(
                rows = rows,
                rowDefinitions = rowDefinitions,
                columns = columns,
                options = null,
            )

        return tableConfig
    }

    private fun createRowDefinitions(classificationFields: List<ClassificationFieldInfo>) =
        classificationFields.associate { (rowId, displayName) ->
            rowId to
                FieldTableRowDefinitionFeDto(
                    id = rowId,
                    cells =
                        listOf(
                            ValueCellFeDto(
                                columnId = "name",
                                field =
                                    ValueCellField(
                                        name = "name",
                                        value = displayName,
                                        type = "Text",
                                    ),
                            ),
                            LookupCellFeDto(
                                columnId = "value",
                                collectFrom =
                                    EntityLocator(
                                        criteria =
                                            listOf(
                                                EqualCriteria(
                                                    "type",
                                                    Entities.MATERIAL_PRICE_COMPOSITION_CLASSIFICATION.name,
                                                ),
                                            ),
                                        type = LocatorType.CHILD,
                                    ),
                                fieldName = rowId,
                            ),
                        ),
                    rows = null,
                    navigateToEntityLocator = null,
                    isCollapsed = null,
                    options = null,
                )
        }

    private fun createColumns() =
        listOf(
            FieldTableColumnDefinitionFeDto(
                id = "name",
                options =
                    ColumnOptionsFeDto(
                        mainColumn = true,
                        editable = false,
                        displayDesignation = "Name",
                    ),
            ),
            FieldTableColumnDefinitionFeDto(
                id = "value",
                options =
                    ColumnOptionsFeDto(
                        editable = null,
                        displayDesignation = "Value",
                    ),
            ),
        )
}
