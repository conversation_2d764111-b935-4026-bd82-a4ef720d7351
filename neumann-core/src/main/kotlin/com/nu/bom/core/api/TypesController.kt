package com.nu.bom.core.api

import com.nu.bom.core.api.dtos.AutocompleteContainer
import com.nu.bom.core.api.dtos.AutocompleteResponse
import com.nu.bom.core.exception.InternalServerException
import com.nu.bom.core.exception.readable.ErrorCode
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.enums.MasterDataCategory
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.service.ManufacturingModelsUtils
import com.nu.bom.core.service.SelectionService
import com.nu.bom.core.utils.EntityManager
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Mono
import java.util.Locale

@RestController
@RequestMapping("/api/type")
class TypesController(
    private val entityManager: EntityManager,
    private val selectionService: SelectionService,
) {
    @GetMapping("/material")
    fun getMaterials(
        @RequestParam(required = false) isMasterDataTab: Boolean = false,
        @RequestParam(required = false) showAll: Boolean = false,
    ): AutocompleteContainer {
        val allMaterials = MasterDataType.ofCategory(MasterDataCategory.MATERIAL)
        val result =
            if (showAll) {
                allMaterials
            } else if (isMasterDataTab) {
                allMaterials.filterNot {
                    // exclude Paint and Metallic coating for now, see COST-34544
                    materialTypesExcludedFromMasterDataTab.contains(it)
                }
            } else {
                // exclude Lamella and Sheet for now, see COST-29869
                allMaterials.filterNot {
                    materialsNotToBeShownInAddMaterialModal.contains(it)
                }
            }
        return AutocompleteContainer(sections = null, result.map(Companion::toAutocompleteResponse))
    }

    @GetMapping("/technologies")
    fun getTechnologiesForMasterdata(
        @RequestParam masterDataType: MasterDataType?,
    ): AutocompleteContainer =
        AutocompleteContainer(
            sections = null,
            items =
                getTechnologyModels(masterDataType)
                    .filter {
                        it.masterdataManageAllowed
                    }.map(Companion::toAutocompleteResponse),
        )

    @GetMapping("/selectable/{type}")
    fun getSelectableOptions(
        @PathVariable type: String,
    ): Mono<AutocompleteContainer> =
        selectionService
            .getSelections(type)
            .map {
                AutocompleteContainer(
                    sections = null,
                    items = it.map { it.toDto() },
                )
            }.onErrorResume {
                if (it is InternalServerException && it.errorCode == ErrorCode.LOOKUP_NOT_FOUND) {
                    Mono.empty()
                } else {
                    Mono.error(it)
                }
            }

    @GetMapping("/units")
    fun getSelectableUnits(
        @RequestParam dimension: String,
    ): AutocompleteContainer =
        AutocompleteContainer(
            sections = null,
            items =
                entityManager.getUnits(dimension).map {
                    AutocompleteResponse(
                        key = it.toString(),
                        name = it.toString(),
                        section = null,
                    )
                },
        )

    private fun getTechnologyModels(masterDataType: MasterDataType?): List<Model> =
        if (masterDataType == null) {
            ManufacturingModelsUtils.findAllModels()
        } else {
            val entity = entityManager.getEntityForMasterDataType(masterDataType)
            entityManager.getTechnologies(entity)
        }

    companion object {
        // exclude Paint and Metallic coating for now, see COST-34544
        val materialTypesExcludedFromMasterDataTab =
            listOf(
                MasterDataType.RAW_MATERIAL_PAINT,
                MasterDataType.RAW_MATERIAL_METALLIC_COATING,
                MasterDataType.RAW_MATERIAL_INK,
                MasterDataType.RAW_MATERIAL_VARNISH,
            )

        // exclude Lamella and Sheet for now, see COST-29869
        val materialsNotToBeShownInAddMaterialModal =
            listOf(
                MasterDataType.RAW_MATERIAL_LAMELLA,
                MasterDataType.RAW_MATERIAL_SHEET,
            )

        fun toAutocompleteResponse(model: Model) =
            AutocompleteResponse(
                model.entity,
                model.path.uppercase(Locale.getDefault()),
                null,
            )

        fun <E : Enum<E>> toAutocompleteResponse(enum: Enum<E>) =
            AutocompleteResponse(
                enum.name,
                enum.name,
                null,
            )
    }
}
