package com.nu.bom.core.model

import com.google.common.annotations.VisibleForTesting
import com.nu.bom.core.api.CO2_PER_PART
import com.nu.bom.core.api.COST_PER_PART
import com.nu.bom.core.manufacturing.entities.BaseManufacturing
import com.nu.bom.core.manufacturing.entities.BomNodeReference
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import com.nu.bom.core.utils.findInTree
import com.nu.bom.core.version.PlatformVersion
import com.nu.bomrads.dto.BranchViewDTO
import com.nu.bomrads.dto.NodeSnapshotDTO
import com.tset.common.util.NOT_NULL_MULTILINE_STYLE
import com.tset.common.util.NOT_NULL_STYLE
import com.tset.core.module.bom.exchangerates.ExchangeRateMap
import com.tset.core.service.domain.Currency
import org.apache.commons.lang3.builder.ToStringBuilder
import org.bson.types.ObjectId
import org.slf4j.LoggerFactory
import java.math.BigDecimal
import java.time.Instant
import java.util.Date

data class BomNodeSnapshot(
    val name: String,
    val year: Int,
    val title: String,
    val accountId: AccountId?,
    val manufacturing: ManufacturingEntity? = null,
    val generated: Boolean = false,
    var deleted: Boolean = false,
    /**
     * Key performance indicators to ease the access in the db or in the frontend.
     */
    var kpi: KeyPerformanceIndicator =
        KeyPerformanceIndicator(
            cO2PerPart = Indicator(null, null, true),
            costPerPart = Indicator(null, null, true),
            branchId = null,
        ),
) : HasPartName,
    BaseEntity {
    @Suppress("ktlint:standard:backing-property-naming")
    var _id: SnapshotId? = null

    // only mutable, to be able to store it
    var partName: String? = null

    // getter only (manufacturing tree is still mutated on recalculation)
    val dirtyChildLoading: Boolean
        get() = hasDirtyChildren(manufacturing)

    /**
     * The last known migration id at the time of reading this snapshot from the database.
     *
     * Cached snapshots that are behind the latest id must be invalidated
     * and refetched from the database to ensure that the lazy migration sequence can be applied.
     * */
    var lastMigrationChangeSetId: MigrationChangeSetId? = null

    var createdBy: String? = null

    var createdDate: Date? = null

    var lastModifiedBy: String? = null

    var lastModifiedDate: Date? = null

    var createdWithSystemVersion: PlatformVersion? = null

    /**
     * This will always be non-null, as after loading a snapshot, a service will fill in.
     * Call node() for the compile safe, non-null variant.
     *
     * Setting this variable will also set the [bomNodeId] and [projectId].
     */
    var bomNode: BomNode? = null
        set(value) {
            field = value
            bomNodeId = value?._id
            projectId = value?.projectId
        }

    var previousSnapshot: BomNodeSnapshot? = null

    private var persisted: Boolean = false

    private var lazyRelations: Boolean = false

    @VisibleForTesting
    var branch: BranchId? = null

    @VisibleForTesting
    var branchEntity: Branch? = null

    var mergeState: Merge? = null

    var openMerges: List<Merge>? = null

    var originalRootSource: BranchViewDTO? = null

    var originalSource: NodeSnapshotDTO? = null

    var root: Boolean? = null

    /**
     * Points to a list of nodes, which function as a parent, so changes in this node could propagate there.
     * If this is a checked out node, it will contain the original parent node.
     */
    var parentsToUse: List<BomEntryRelation> = listOf()

    /**
     * Points to a list of child nodes, without pointing to checked-out version of child nodes.
     */
    var subNodes: List<BomEntryRelation> = listOf()

    /**
     * Points to a BomNode, from where every calculation should start.
     * Set only for specific manufacturing scenarios (eg: Sand casting with 'Core' as a child BomNode)
     * During checkout, this is also updated to the checked out 'calculation root'.
     */
    var calculationRoot: BomNodeId? = null

    /**
     * This will always be non-null, as after loading a snapshot, a service will fill in.
     * Call bomNodeId() for the compile safe, non-null variant.
     *
     * Setting the [bomNode] will implicitly set this variable to its Id.
     */
    var bomNodeId: BomNodeId? = null

    /**
     * After loading a snapshot, a service will fill the value in.
     * In case we make a mistake and don't set it - we default to null. This will not cause data corruption
     * because node is still protected on a bomrads side.
     */
    var protectedAt: Instant? = null

    var protectedBy: String? = null

    /**
     * This will always be non-null, as after loading a snapshot, a service will fill in.
     * Call projectId() for the compile safe, non-null variant.
     *
     * Setting the [bomNode] will implicitly set this variable to its project Id.
     */
    var projectId: ProjectId? = null

    override fun getId() = _id!!

    override fun isDeleted() = deleted

    // Null safe variant
    fun node() = this.bomNode!!

    fun bomNodeId() = this.bomNodeId!!

    fun projectId() = this.projectId!!

    fun isRoot(): Boolean = root ?: originalSource?.root() ?: node().isRoot()

    fun getBaseManufacturing(): BaseManufacturing? {
        val man = manufacturing
        return if (man is BaseManufacturing) man else null
    }

    override fun readablePartName(): String =
        getBaseManufacturing()?.let {
            val partNumber = it.getFieldResultSafe("partNumber")?.res?.toString()
            val partDesignation = it.getFieldResultSafe("partDesignation")?.res?.toString()
            "$partNumber $partDesignation"
        } ?: "null null" // just for backward compatibility

    fun setBranchId(branchId: BranchId?): BomNodeSnapshot {
        this.branch = branchId
        return this
    }

    fun isMainBranch(): Boolean = originalRootSource?.branch?.main ?: node().isMain(branch)

    fun branchId(): BranchId = bomradBranchId().toMongoBranchId()

    /**
     * @return the branchId in Mongo-compatible format.
     */
    fun branchIdStr(): String = bomradBranchId().toMongoFormatStr()

    fun bomradBranchId(): com.nu.bomrads.id.BranchId = this.originalRootSource!!.branch.id

    fun bomradChangesetId(): com.nu.bomrads.id.ChangesetId? = this.originalRootSource?.changeset?.id

    fun bomradBomNodeId(): com.nu.bomrads.id.BomNodeId? = this.originalSource?.bomNodeId

    /**
     * @return true if this snapshot came from the main branch in Bomrads.
     */
    fun bomradMainBranch(): Boolean = originalRootSource!!.branch.main

    fun branchEntity() = branchEntity ?: error("branchEntity must not be null")

    fun isPersisted(): Boolean = persisted

    fun setTransientState(
        bomNode: BomNode?,
        branchId: BranchId?,
        branchEntity: Branch? = null,
        merge: Merge? = null,
    ): BomNodeSnapshot {
        this.bomNode = bomNode
        this.branch = branchId
        this.branchEntity = branchEntity
        this.mergeState = merge
        return this
    }

    fun replaceChildRelation(
        bomEntryId: ObjectId,
        newRelation: BomEntryRelation,
    ) {
        subNodes = subNodes.filter { it.bomEntryId != bomEntryId } + newRelation
    }

    fun setBranchEntityRecursively(branchEntity: Branch?) {
        this.branchEntity = branchEntity
        visitChildren(ignoreUnfetched = true) { it.branchEntity = branchEntity }
    }

    fun markAsPersisted(): BomNodeSnapshot {
        this.persisted = true
        return this
    }

    fun unsafeMarkAsSafeToPersist(): BomNodeSnapshot {
        this.persisted = false
        return this
    }

    fun checkNotYetPersisted(): BomNodeSnapshot {
        if (persisted) {
            error(
                "BomNodeSnapshot is already persisted, persisting again could overwrite the previous state ($this)!",
            )
        }
        return this
    }

    fun newSnapshot(
        manufacturing: ManufacturingEntity? = null,
        title: String? = null,
    ): BomNodeSnapshot {
        val new =
            copy(
                manufacturing = manufacturing ?: this.manufacturing,
                title = title ?: this.title,
            )
        new._id = SnapshotId()
        new.deleted = false
        new.partName = this.partName
        new.previousSnapshot = this
        new.lazyRelations = this.lazyRelations
        new.setBranchView(originalRootSource, originalSource)
        new.lastModifiedDate = this.lastModifiedDate
        new.calculationRoot = this.calculationRoot
        new.subNodes = this.subNodes
        new.parentsToUse = this.parentsToUse
        new.protectedAt = this.protectedAt
        new.protectedBy = this.protectedBy
        return new.setTransientState(this.bomNode, this.branch, this.branchEntity, this.mergeState)
    }

    fun setAsHeadVersion(): BomNodeSnapshot {
        node().setBranchHead(this.branch, this._id!!)
        return this
    }

    fun setExternalParentHead(targetBranchId: BranchId): BomNodeSnapshot {
        node().setExternalParentHead(targetBranchId, _id!!)
        return this
    }

    fun newSnapshotRecursively(
        newRootManufacturing: ManufacturingEntity? = null,
        title: String? = null,
        customCopy: CustomCopy? = null,
        ignoreUnfetched: Boolean,
    ): BomNodeSnapshot {
        val newCustomSnapshot = customCopy?.invoke(this)
        val newSnapshot = newCustomSnapshot ?: this.newSnapshot(manufacturing = newRootManufacturing, title = title)
        newSnapshot.subNodes =
            subNodes.map { rel ->
                if (rel.snapshot != null) {
                    updateRelation(rel, newSnapshot, customCopy, ignoreUnfetched)
                } else if (ignoreUnfetched) {
                    rel
                } else {
                    error("unfetched relation")
                }
            }

        // update the BomNode's branch to point to the newly created snapshots, as a head
        newSnapshot.setAsHeadVersion()
        return newSnapshot
    }

    private fun updateRelation(
        rel: BomEntryRelation,
        newSnapshot: BomNodeSnapshot,
        customCopy: CustomCopy?,
        ignoreUnfetched: Boolean,
    ): BomEntryRelation =
        if (rel.snapshot == null && lazyRelations) {
            rel.copy()
        } else {
            val newChild =
                rel.snapshot!!.newSnapshotRecursively(customCopy = customCopy, ignoreUnfetched = ignoreUnfetched)
            newChild.parentsToUse =
                newChild.parentsToUse.map { parentRel ->
                    if (parentRel.snapshot === this) {
                        parentRel.copy().setSnapshot(newSnapshot)
                    } else {
                        parentRel
                    }
                }
            rel.copy().setSnapshot(newChild)
        }

    /**
     * Replace the parent relation with the given relation, if it's exists, or add a new one, if not.
     */
    fun replaceParentRelation(rel: BomEntryRelation) {
        if (parentsToUse.any { it.bomNodeId == rel.bomNodeId }) {
            this.parentsToUse =
                parentsToUse.map {
                    if (it.bomNodeId == rel.bomNodeId) rel else it
                }
        } else {
            this.parentsToUse = parentsToUse + rel
        }
    }

    /**
     * Replace the child relation with the given relation, if it's exists, or add a new one, if not.
     */
    fun replaceChildRelation(rel: BomEntryRelation) {
        if (subNodes.any { it.bomNodeId == rel.bomNodeId }) {
            this.subNodes =
                subNodes.map {
                    if (it.bomNodeId == rel.bomNodeId) rel else it
                }
        } else {
            this.subNodes = subNodes + rel
        }
    }

    fun findChildrenBy(nodeId: BomNodeId): BomNodeSnapshot? =
        findInTree({ it.bomNodeId == nodeId }, { it.subNodes.mapNotNull { subnode -> subnode.snapshot } })

    fun findChildrenByEntityId(entityId: ObjectId): BomEntryRelation? = subNodes.find { it.bomEntryId == entityId }

    fun findByEntityId(entityId: String): ManufacturingEntity? = manufacturing?.findByEntityId(entityId)

    fun updateKpi() {
        val cO2PerPart = manufacturing?.getFieldResult(CO2_PER_PART)?.res as BigDecimal?
        val costPerPartValue = manufacturing?.getFieldResult(COST_PER_PART)?.res as BigDecimal?
        val totalTargetCostsValue = manufacturing?.getFieldResult("totalTargetCosts")?.res as BigDecimal?
        val totalCurrentCostsValue = manufacturing?.getFieldResult("totalCurrentCosts")?.res as BigDecimal?
        val additionalTargetCosts = manufacturing?.getFieldResult("additionalTargetCosts")?.res as BigDecimal?
        val additionalCurrentCosts = manufacturing?.getFieldResult("additionalCurrentCosts")?.res as BigDecimal?
        val exchangeRateMap = manufacturing?.getExchangeRateMap() ?: ExchangeRateMap.empty()
        val baseCurrency = (manufacturing?.getFieldResult(Manufacturing::baseCurrency.name)?.res as String?)?.let { Currency(it) }
        val costPerPartInBaseCurrency = baseCurrency?.let { exchangeRateMap.ccyToCcy(costPerPartValue, Currency.EUR, it) }
        val updateOldValues =
            (
                costPerPartValue != null ||
                    totalCurrentCostsValue != null ||
                    totalTargetCostsValue != null ||
                    additionalTargetCosts != null ||
                    additionalCurrentCosts != null
            )

        this.kpi =
            kpi.createNew(
                onBranch = branch,
                updateOldValues = updateOldValues,
                normalizedCO2PerPart = cO2PerPart?.stripTrailingZeros(),
                normalizedCostPerPart = costPerPartValue?.stripTrailingZeros(),
                normalizedCostPerPartInBaseCurrency = costPerPartInBaseCurrency?.stripTrailingZeros(),
                normalizedTotalCurrentCosts = totalCurrentCostsValue?.stripTrailingZeros(),
                normalizedTotalTargetCosts = totalTargetCostsValue?.stripTrailingZeros(),
                normalizedAdditionalTargetCosts = additionalTargetCosts?.stripTrailingZeros(),
                normalizedAdditionalCurrentCosts = additionalCurrentCosts?.stripTrailingZeros(),
                baseCurrency = baseCurrency,
            )
    }

    private fun getRelationSnapshot(
        relation: BomEntryRelation,
        ignoreUnfetched: Boolean = false,
    ): BomNodeSnapshot? =
        if (lazyRelations || ignoreUnfetched) {
            relation.snapshot
        } else {
            relation.snapshot
                ?: error(
                    "Node not fetched in $relation for $this - either 'lazyRelations' is not set true correctly, " +
                        "or the loading was not totally correct",
                )
        }

    fun getManufacturingIdToBomNodeIdMap(ignoreUnfetched: Boolean = false): Map<ObjectId, BomNodeId> =
        visitChildren(ignoreUnfetched = ignoreUnfetched) { snapshot ->
            snapshot.manufacturing?._id?.let { manufacturingId ->
                manufacturingId to snapshot.node().id()
            }
        }.toMap()

    fun getManufacturingIdToBomNodeMap(): Map<ObjectId, BomNodeSnapshot> =
        visitChildren { snapshot ->
            snapshot.manufacturing?._id?.let { manufacturingId ->
                manufacturingId to snapshot
            }
        }.toMap()

    enum class BomNodeRelationType {
        PARENT,
        CHILD,
        EXT,
        SAME,
    }

    fun setBranchViewRecursively(branchViewDTO: BranchViewDTO): BomNodeSnapshot {
        visitChildren(true) {
            it.setBranchView(branchViewDTO)
        }
        return this
    }

    fun setBranchView(branchViewDTO: BranchViewDTO) {
        setBranchView(branchViewDTO, branchViewDTO.snapshots().find { it.currentOrPreviousTreeId().toMongoSnapshotId() == this.id() })
    }

    fun setBranchView(
        branchViewDTO: BranchViewDTO?,
        nodeDto: NodeSnapshotDTO?,
    ) {
        this.originalRootSource = branchViewDTO
        this.originalSource = nodeDto
        if (nodeDto != null) {
            this.bomNodeId = nodeDto.bomNodeId.toMongoBomNodeId()
            this.projectId = branchViewDTO?.projectId?.toMongoProjectId()
            this.protectedAt = nodeDto.protectedAt
            this.protectedBy = nodeDto.protectedBy
        }
    }

    fun <X> visitChildren(
        ignoreUnfetched: Boolean = false,
        func: (BomNodeSnapshot) -> (X?),
    ): List<X> {
        val mutableList: MutableList<X> = mutableListOf()
        visitChildren(ignoreUnfetched, func, mutableList)
        return mutableList
    }

    fun collectChildren(ignoreUnfetched: Boolean = false): List<BomNodeSnapshot> = visitChildren(ignoreUnfetched) { it }

    fun collectChildrenBomNodeIds(): List<BomNodeId> =
        visitChildren(ignoreUnfetched = true) {
            it.bomNodeId
        }

    private fun <X> visitChildren(
        ignoreUnfetched: Boolean = false,
        func: (BomNodeSnapshot) -> (X?),
        result: MutableList<X>,
    ) {
        val value = func(this)
        if (value != null) {
            result.add(value)
        }
        this.subNodes.forEach { bomEntryRelation ->
            getRelationSnapshot(bomEntryRelation, ignoreUnfetched)?.visitChildren(ignoreUnfetched, func, result)
        }
    }

    override fun toString(): String {
        val manufacturingStructure = manufacturing?.treeStructure()
        return ToStringBuilder(this, NOT_NULL_MULTILINE_STYLE)
            .append("id", _id)
            .append("bomNodeId", bomNodeId)
            .append("parentsToUse", parentsToUse)
            .append("subNodes", subNodes)
            .append("calculationRoot", calculationRoot)
            .append("manufacturing", manufacturingStructure)
            .append("deleted", deleted)
            .append("persisted", persisted)
            .append("name", name)
            .append("year", year)
            .append("branch", branch)
            .append("source", originalSource)
            .append("bomNode", bomNode)
            .build()
    }

    fun toShortString(): String =
        ToStringBuilder(this, NOT_NULL_STYLE)
            .append("id", _id)
            .append("bomNodeId", bomNodeId)
            .append("branch", branch)
            .append("parentsToUse", parentsToUse.map { it.bomNodeId })
            .append("subNodes", subNodes.map { it.bomNodeId })
            .build()

    fun id(): SnapshotId = _id!!

    val firstParent: BomNodeSnapshot?
        get() = parentsToUse.firstOrNull()?.snapshot

    val partDesignation: String?
        get() = getBaseManufacturing()?.getFieldResultSafe("partDesignation")?.res?.toString()

    companion object {
        const val COLLECTION_NAME = "Snapshots"
        val logger = LoggerFactory.getLogger(BomNodeSnapshot::class.java)!!
    }

    fun interface CustomCopy {
        fun invoke(snapshot: BomNodeSnapshot): BomNodeSnapshot?
    }

    fun getFieldResult(key: String) = manufacturing?.getFieldResult(key)
}

private fun hasDirtyChildren(manufacturing: ManufacturingEntity?): Boolean =
    manufacturing?.findInTree(
        predicate = { it is BomNodeReference && it.dirtyChildLoading },
        children = { it.children },
    ) != null
