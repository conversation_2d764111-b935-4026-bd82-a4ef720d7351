package com.nu.bom.core.service.migration.lazy.mappers

import com.nu.bom.core.model.manufacturing.legacyModel.FieldResultModel
import com.nu.bom.core.model.manufacturing.legacyModel.ManufacturingModelEntity
import com.nu.bom.core.service.migration.lazy.ManufacturingModelEntityMapper
import com.nu.bom.core.service.migration.lazy.MigrationChangeSetId
import org.springframework.stereotype.Service

@Service
class ManualCircularGrooveMillingMigration : ManufacturingModelEntityMapper {
    override val changeSetId = MigrationChangeSetId("2023-03-09-manual-circular-groove-milling-migration")

    override fun map(entity: ManufacturingModelEntity): ManufacturingModelEntity =
        entity.copyAll(
            initialFieldWithResults = initialFlipWidthAndDepthFields(entity.initialFieldWithResults.toMutableMap()),
            fieldWithResults = flipWidthAndDepthFields(entity.fieldWithResults),
        )

    private fun initialFlipWidthAndDepthFields(fields: MutableMap<String, FieldResultModel>): Map<String, FieldResultModel> {
        val existingWidth = fields["width"]
        val existingDepth = fields["depth"]
        fields.remove("width")
        fields.remove("depth")
        existingWidth?.let { fields.put("depth", it) }
        existingDepth?.let { fields.put("width", it) }
        return fields
    }

    private fun flipWidthAndDepthFields(fields: Map<String, FieldResultModel>): Map<String, FieldResultModel> {
        val existingWidth = fields["width"]
        val existingDepth = fields["depth"]
        return fields.mapValues { (fieldName, fieldResult) ->
            when (fieldName) {
                "width" -> fieldResult.copyAll(value = existingDepth!!.value)
                "depth" -> fieldResult.copyAll(value = existingWidth!!.value)
                else -> fieldResult
            }
        }
    }
}
