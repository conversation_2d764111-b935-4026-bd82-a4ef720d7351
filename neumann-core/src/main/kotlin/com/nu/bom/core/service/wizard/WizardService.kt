package com.nu.bom.core.service.wizard

import com.nu.bom.core.api.dtos.CalculationCreationDto
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.BomNodeSnapshot
import com.nu.bom.core.model.ProjectId
import com.nu.bom.core.model.Wizard
import com.nu.bom.core.model.WizardData
import com.nu.bom.core.model.WizardData.Companion.fromCalculationUpdatePayload
import com.nu.bom.core.model.createBranchId
import com.nu.bom.core.repository.MillingDrillingProfileRepository
import com.nu.bom.core.repository.WizardRepository
import com.nu.bom.core.service.BomNodeService
import com.nu.bom.core.service.CalculationCreationDataConversionService
import com.nu.bom.core.service.ManufacturingModelsUtils
import com.nu.bom.core.service.bomnode.BomNodeStrippedService
import com.nu.bom.core.service.wizard.steps.StepPathMapping
import com.nu.bom.core.service.wizard.steps.WizardTechStep
import com.nu.bom.core.turn.TurningService
import com.nu.bom.core.turn.TurningService.Companion.RAW_PART_TECHNOLOGY
import com.nu.bom.core.user.AccessCheck
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.Maybe
import com.nu.bom.core.utils.component1
import com.nu.bom.core.utils.component2
import com.nu.bom.core.utils.takeWhileInclusive
import com.tset.core.api.calculation.CalculationUpdateModalService
import com.tset.core.api.calculation.dto.CalculationCreationModalMode.CALCULATION_MODE_CHANGE_TYPE
import com.tset.core.api.calculation.dto.CalculationCreationModalMode.CALCULATION_MODE_EDIT
import com.tset.core.api.calculation.dto.CalculationCreationModalMode.CALCULATION_MODE_NEW
import com.tset.core.api.calculation.dto.CalculationPosition.ROOT
import com.tset.core.api.calculation.dto.CalculationPosition.SUB
import com.tset.core.api.calculation.dto.CalculationUpdateAction
import com.tset.core.api.calculation.dto.CalculationUpdatePayloadDto
import org.bson.types.ObjectId
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toFlux
import java.time.LocalDate
import java.util.Optional

@Service
class WizardService(
    private val wizardRepository: WizardRepository,
    private val bomNodeService: BomNodeService,
    private val bomNodeStrippedService: BomNodeStrippedService,
    private val calculationDataConversionService: CalculationCreationDataConversionService,
    private val wizardManager: WizardManager,
    private val calculationUpdateModalService: CalculationUpdateModalService,
    private val millingDrillingProfileRepository: MillingDrillingProfileRepository,
    private val entityManager: EntityManager,
    // private val calculationInputPartConverter: CalculationInputPartConverter,
    private val turningService: TurningService,
) {
    private fun getStepsUntil(
        modelId: String?,
        step: StepPathMapping,
    ): List<StepPathMapping> =
        ManufacturingModelsUtils.findByEntity(modelId)?.let { model ->
            model.cardNames.takeWhileInclusive { it != step.name }.map { StepPathMapping.valueOf(it) }
        } ?: emptyList()

    fun startWizard(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        calculationUpdatePayload: CalculationUpdatePayloadDto,
        step: StepPathMapping?,
    ): Mono<Wizard> =
        when (calculationUpdatePayload.input.mode) {
            CALCULATION_MODE_NEW ->
                startWizardWithCreate(accessCheck, projectId, calculationUpdatePayload)

            CALCULATION_MODE_CHANGE_TYPE ->
                startWizardWithChangeType(accessCheck, projectId, calculationUpdatePayload)

            CALCULATION_MODE_EDIT ->
                startWizardWithEdit(accessCheck, projectId, calculationUpdatePayload, step ?: StepPathMapping.TECH)
        }

    private fun startWizardWithEdit(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        calculationUpdatePayload: CalculationUpdatePayloadDto,
        step: StepPathMapping,
    ): Mono<Wizard> =
        getUpdatedFieldsForPosition(calculationUpdatePayload, accessCheck, projectId).flatMap { updatedPayload ->
            val wizardData: WizardData = fromCalculationUpdatePayload(updatedPayload, entityManager)
            getParentPartName(accessCheck = accessCheck, wizardData = wizardData)
                .flatMap {
                    val parentPartName = it.orElse(null)

                    bomNodeService
                        .getBomNode(
                            accessCheck,
                            BomNodeId(
                                wizardData.calculationUpdateData
                                    ?.input
                                    ?.context
                                    ?.bomNodeId!!,
                            ),
                            createBranchId(wizardData.calculationUpdateData.input.context.branchId),
                        ).flatMap { snapshot ->

                            val copiedTurningProfileIdMono =
                                getFieldAsId(snapshot, "turningProfileId")?.let { turningProfileId ->
                                    turningService.copyTurningProfile(turningProfileId, accessCheck)
                                } ?: Mono.just(Maybe())
                            val copiedMillingProfileIdMono =
                                getFieldAsId(snapshot, "millingProfileId")?.let { millingProfileId ->
                                    copyMillingDrillingProfile(millingProfileId)
                                } ?: Mono.just(Maybe())
                            val rawPartTechnology = snapshot.manufacturing!!.getFieldResult(RAW_PART_TECHNOLOGY)?.res as String?

                            copiedTurningProfileIdMono
                                .zipWith(copiedMillingProfileIdMono)
                                .flatMap { (maybeTurningProfileId, maybeMillingProfileId) ->
                                    // calculationInputPartConverter
                                    //     .convertPartFieldsToPartId(
                                    //         accessCheck,
                                    //         projectId,
                                    //         "",
                                    //         updatedPayload.data.fields,
                                    //     ).flatMap { maybePart ->
                                            val wizard =
                                                Wizard(
                                                    projectId = projectId,
                                                    // TODO kw check if partId was of any use and find out how to replace it...
                                                    standardCalculationData = wizardData,//.copy(partId = maybePart.value),
                                                    parentPartName = parentPartName,
                                                    accountId = accessCheck.asAccountId(),
                                                )
                                            wizard.turningProfileId = maybeTurningProfileId.value
                                            wizard.millingProfileId = maybeMillingProfileId.value
                                            wizard.rawPartTechnology = rawPartTechnology
                                            // TODO enhance the condition, currently precompute type and model are not available on this stage
                                            if (snapshot.manufacturing.getEntityClass() in
                                                listOf(
                                                    "ManufacturingProgressiveDieStamping",
                                                    "ManufacturingTransferDieStamping",
                                                )
                                            ) {
                                                wizard.manufacturingEntity = snapshot.manufacturing
                                                wizard.editInitialStep = step
                                            }

                                            // first save empty, to get the id populated
                                            wizardRepository.save(wizard)
                                        // }
                                }
                        }
                }.flatMap { savedWizard ->
                    val wizardId = savedWizard._id!!

                    // populate TECH field first, always
                    wizardManager
                        .getStep(accessCheck, wizardId, StepPathMapping.TECH.clazz, CALCULATION_MODE_EDIT)
                        .flatMap { techStep ->

                            // save TECH
                            wizardManager
                                .saveStep(accessCheck, wizardId, techStep, calculationUpdatePayload.input.currency)
                                .flatMap { wizardWithTechStep ->
                                    val modelId: String? = (techStep as WizardTechStep).modelId()?.value?.toString()

                                    getStepsUntil(modelId, step)
                                        .toFlux()
                                        .concatMap { currentStep: StepPathMapping ->
                                            wizardManager
                                                .getStep(
                                                    accessCheck,
                                                    wizardId,
                                                    currentStep.clazz,
                                                    CALCULATION_MODE_EDIT,
                                                ).flatMap { currentWizardStep ->
                                                    wizardManager.saveStep(
                                                        accessCheck,
                                                        wizardId,
                                                        currentWizardStep,
                                                        calculationUpdatePayload.input.currency,
                                                    )
                                                }
                                        }.collectList()
                                        .map { savedWizards: MutableList<Wizard> ->
                                            savedWizards.last()
                                        }.switchIfEmpty(Mono.just(wizardWithTechStep))
                                }
                        }
                }
        }

    private fun getFieldAsId(
        snapshot: BomNodeSnapshot,
        fieldName: String,
    ): ObjectId? =
        (snapshot.manufacturing?.getFieldResult(fieldName)?.res as String?)?.let { idText ->
            ObjectId(idText)
        }

    private fun copyMillingDrillingProfile(id: ObjectId): Mono<Maybe<ObjectId>> =
        millingDrillingProfileRepository.findById(id).flatMap { millingProfile ->
            val copiedMillingProfileId = ObjectId.get()
            millingProfile.id = copiedMillingProfileId
            millingProfile.sketch?.sketchId = copiedMillingProfileId
            millingDrillingProfileRepository.save(millingProfile).map { copiedMillingProfile ->
                Maybe(copiedMillingProfile.id)
            }
        }

    private fun startWizardWithChangeType(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        calculationUpdatePayload: CalculationUpdatePayloadDto,
    ): Mono<Wizard> =
        getUpdatedFieldsForPosition(calculationUpdatePayload, accessCheck, projectId).flatMap { updatedPayload ->
            val wizardData: WizardData = fromCalculationUpdatePayload(updatedPayload, entityManager)
            getParentPartName(accessCheck = accessCheck, wizardData = wizardData)
                .flatMap {
                    val parentPartName = it.orElse(null)
                    // calculationInputPartConverter
                    //     .convertPartFieldsToPartId(
                    //         accessCheck,
                    //         projectId,
                    //         "",
                    //         updatedPayload.data.fields,
                    //     ).flatMap { maybePart ->
                    wizardRepository.save(
                        Wizard(
                            projectId = projectId,
                            // TODO kw check what to do with missing partId
                            standardCalculationData = wizardData,//.copy(partId = maybePart.value),
                            parentPartName = parentPartName,
                            accountId = accessCheck.asAccountId(),
                        ),
                    )
                    // }
                }.flatMap { savedWizard ->
                    val wizardId = savedWizard._id!!
                    wizardManager
                        .getStep(accessCheck, wizardId, StepPathMapping.TECH.clazz, CALCULATION_MODE_CHANGE_TYPE)
                        .flatMap { techStep ->
                            wizardManager.saveStep(accessCheck, wizardId, techStep, calculationUpdatePayload.input.currency)
                        }
                }
        }

    private fun getUpdatedFieldsForPosition(
        calculationUpdatePayload: CalculationUpdatePayloadDto,
        accessCheck: AccessCheck,
        projectId: ProjectId,
    ) = when (calculationUpdatePayload.input.position) {
        ROOT ->
            calculationUpdateModalService.createRootWithCostModuleProcessor(
                accessCheck,
                projectId.toHexString(),
                calculationUpdatePayload,
                CalculationUpdateAction.SAVE_AND_START_WIZARD,
            )

        SUB ->
            calculationUpdateModalService.createSubWithCostModuleProcessor(
                accessCheck,
                projectId.toHexString(),
                calculationUpdatePayload,
                CalculationUpdateAction.SAVE_AND_START_WIZARD,
            )
    }.getUpdatedFields()
        .map(::fixDate)

    // This horrible hack has to exist because we store FieldParameters in the database.
    // The fields are either taken from user input (which is fine), but can also be filled from a snapshot.
    // These two cases produce DIFFERENT TYPES!
    // When filling a date field from user input, the value is a string. When being filled from the snapshot,
    // it's a [LocalDate] as that is the [res] of the FieldResult.
    // This is needed in the subcalc creation equality check, so I can't just fix it.
    // But it leads to issues when storing the LocalDate in mongo, as it's stored as a shitty java legacy [Date] with a time zone
    // So... it was either this or rewriting the wizard store.
    private fun fixDate(payload: CalculationUpdatePayloadDto): CalculationUpdatePayloadDto {
        val fields =
            payload.data.fields.map { field ->
                if (field.value is LocalDate) {
                    field.copy(value = field.value.toString())
                } else {
                    field
                }
            }
        return payload.copy(data = payload.data.copy(fields = fields))
    }

    private fun startWizardWithCreate(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        calculationUpdatePayload: CalculationUpdatePayloadDto,
    ): Mono<Wizard> {
        val wizardData: WizardData = fromCalculationUpdatePayload(calculationUpdatePayload, entityManager)

        return getParentPartName(accessCheck = accessCheck, wizardData = wizardData).flatMap {
            val parentPartName = it.orElse(null)

            wizardRepository.save(
                Wizard(
                    projectId = projectId,
                    standardCalculationData = wizardData,//.copy(partId = partId.value),
                    parentPartName = parentPartName,
                    accountId = accessCheck.asAccountId(),
                ),
            )
        }
    }

    fun startWizardLegacy(
        accessCheck: AccessCheck,
        projectId: ProjectId,
        creationDto: CalculationCreationDto,
    ): Mono<Wizard> {
        val creationData: WizardData = calculationDataConversionService.convert(creationDto, entityManager)

        return getParentPartName(accessCheck = accessCheck, wizardData = creationData).flatMap {
            val parentPartName = it.orElse(null)

            wizardRepository.save(
                Wizard(
                    projectId = projectId,
                    standardCalculationData = creationData,
                    parentPartName = parentPartName,
                    accountId = accessCheck.asAccountId(),
                ),
            )
        }
    }

    fun getWizard(
        accessCheck: AccessCheck,
        wizardId: ObjectId,
    ): Mono<Wizard> = wizardRepository.findByIdAndAccountId(wizardId, accessCheck.toAccountId())

    private fun getParentPartName(
        accessCheck: AccessCheck,
        wizardData: WizardData,
    ): Mono<Optional<String>> {
        val bomEntry = wizardData.bomEntry
        return if (bomEntry != null) {
            bomNodeStrippedService.getReadablePartName(
                accessCheck = accessCheck,
                nodeId = bomEntry.bomNodeId,
                branchId = bomEntry.branchId,
            )
        } else {
            Mono.just(Optional.empty())
        }
    }
}
