package com.nu.bom.core.turn

import com.nu.bom.core.api.dtos.FieldParameter
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.toMetaInfoEntry
import com.nu.bom.core.manufacturing.enums.dynamicUnitStringToDenominatorUnit
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.PiecesUnits
import com.nu.bom.core.manufacturing.fieldTypes.Volume
import com.nu.bom.core.manufacturing.fieldTypes.VolumeUnits
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.model.MasterData
import com.nu.bom.core.service.MasterDataService
import com.nu.bom.core.turn.model.RawProfileResponse
import com.nu.bom.core.turn.model.Section
import com.nu.bom.core.turn.model.Sketch
import com.nu.bom.core.user.AccessCheck
import org.bson.types.ObjectId
import reactor.core.publisher.Mono
import java.math.BigDecimal

abstract class TurningParameters {
    abstract fun fillParameters(
        sketch: Sketch,
        material: String,
        accessCheck: AccessCheck,
        sketchId: ObjectId,
        rawModel: Model,
        partInputGroup: String?,
        marginWidth: Length?,
        netWeightPerPart: Weight,
        barDiameterOverride: Length? = null,
    ): Mono<List<Section>>

    protected fun getWeightSection(
        response: RawProfileResponse,
        netWeightPerPart: Weight,
        density: Density,
    ): Section {
        val chipWeightPerPart =
            run {
                val netVolumePerPart = response.turned_part.volume
                val rawVolumePerPart = response.raw_part.volume

                Weight.create(
                    Volume(BigDecimal(rawVolumePerPart - netVolumePerPart), VolumeUnits.CMM),
                    density,
                )
            }

        val rawWeightPerPart = netWeightPerPart + chipWeightPerPart

        val netWeightPerPartNotForCalculationFieldFP =
            createWeightFieldParameter(
                // Not for calculation, because otherwise, this field would be there twice.
                fieldName = "netWeightPerPartNotForCalculationField",
                value = netWeightPerPart,
            )
        val chipWeightPerPartNotForCalculationFieldFP =
            createWeightFieldParameter(
                // Not for calculation, because in the calculation, this has to be dynamically calculated from netWeight and rawWeight.
                fieldName = "chipWeightPerPartNotForCalculationField",
                value = chipWeightPerPart,
            )
        val rawWeightPerPartFP =
            createWeightFieldParameter(
                fieldName = "rawWeightPerPart",
                value = rawWeightPerPart,
            )

        return Section(
            title = "Weight",
            fields =
                listOf(
                    netWeightPerPartNotForCalculationFieldFP,
                    chipWeightPerPartNotForCalculationFieldFP,
                    rawWeightPerPartFP,
                ),
        )
    }

    companion object {
        fun getTurningQualifiedMaterialMasterData(
            masterDataService: MasterDataService,
            material: String,
            accessCheck: AccessCheck,
        ): Mono<MasterData> =
            masterDataService
                .getMaterialMasterdata(material, accessCheck)

        private fun createWeightFieldParameter(
            fieldName: String,
            value: Weight,
        ): FieldParameter =
            FieldParameter(
                name = fieldName,
                value = value.inGram,
                valueInDefaultUnit = value.inGram,
                type = Weight::class.simpleName!!,
                unit = DefaultUnit.GRAM,
                metaInfo = mapOf(toMetaInfoEntry(DefaultUnit.GRAM), ReadOnly.META_INFO to true),
                denominatorUnit = dynamicUnitStringToDenominatorUnit(PiecesUnits.PIECE.toString()),
            )

        fun createLengthFieldParameterReadOnly(
            fieldName: String,
            valueInMm: BigDecimal,
        ): FieldParameter =
            FieldParameter(
                name = fieldName,
                value = valueInMm,
                valueInDefaultUnit = valueInMm,
                type = Length::class.simpleName!!,
                unit = DefaultUnit.MILLIMETER,
                metaInfo = mapOf(toMetaInfoEntry(DefaultUnit.MILLIMETER), ReadOnly.META_INFO to true),
                denominatorUnit = null,
            )

        fun createLengthFieldParameterReadOnly(
            fieldName: String,
            valueInMm: Double,
        ) = createLengthFieldParameterReadOnly(fieldName, BigDecimal(valueInMm))
    }
}
