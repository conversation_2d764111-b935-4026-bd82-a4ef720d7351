from pathlib import Path
import subprocess
import itertools
import argparse
import os

from git import Repo
from typing import NoReturn


def branch_to_feature_environment(branch: str) -> str:
    prefix = "feature/"
    if branch.startswith(prefix):
        return branch.removeprefix(prefix)

    return ""


def error(msg: str, submessages: list[str] | None = None) -> NoReturn:
    print(f"ERROR: {msg}")
    if submessages:
        for submessage in submessages:
            print(f"\t{submessage}")
    exit(1)


def pull_docker(url: str, current_environment: str) -> str:
    docker_url = f"684712464887.dkr.ecr.eu-central-1.amazonaws.com/{url}"

    def run_docker_pull(suffix: str) -> int:
        cmd = [
            "docker",
            "pull",
            f"{docker_url}:{suffix}"
        ]
        res = subprocess.run(cmd, stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL)
        return res.returncode

    if current_environment:
        feature_suffix = f"feature-{current_environment}"
        feature_result = run_docker_pull(feature_suffix)
        if feature_result == 0:
            return feature_suffix

    latest_suffix = "latest"
    latest_result = run_docker_pull(latest_suffix)
    if latest_result == 0:
        return latest_suffix

    msg = f"could not pull '{docker_url}':"
    submessages = []
    if current_environment:
        submessages.append(f"{feature_suffix} returned {feature_result}")
    submessages.append(f"{latest_suffix} returned {latest_result}")
    error(msg, submessages)


def yaml_start(ymls):
    cmd = list(itertools.chain(
        ("docker", "--log-level", "ERROR", "compose"),
        itertools.chain.from_iterable(("-f", yml) for yml in ymls),
        ("up", "-d")
    ))

    res = subprocess.run(cmd)

    if res.returncode != 0:
        print(f"something went wrong with {cmd}")


def prepare_env(env_vals: dict):
    content = "\n".join(f"{var}={val}" for var, val in env_vals.items())
    with open(".env", "w") as f:
        f.write(content)


def get_environment_from_repo() -> str:
    try:
        repo_root = Path(__file__).resolve().parent.parent
        current_branch = Repo(str(repo_root)).active_branch.name
        return branch_to_feature_environment(current_branch)
    except:
        return ""


def validate_environment_name(environment_name: str) -> None:
    invalid_prefix = "feature"
    if environment_name.startswith(invalid_prefix):
        error(f"feature branch environment name should not start with '{invalid_prefix}'")

    for invalid_char in [' ', '/']:
        if invalid_char in environment_name:
            error(f"feature branch environment name should not contain '{invalid_char}'")


def main():
    docker_paths_and_env_vars = [
        ("nuxt-cost-frontend", "FEATURE_FRONTEND"),
        ("static-assets", "FEATURE_STATIC"),
        ("nu-bomrads", "FEATURE_BOMRADS"),
        ("nu-masterdata", "FEATURE_MASTERDATA"),
        ("gattierungsrechner", "FEATURE_GATTIERUNGSRECHNER"),
        ("tset-del", "FEATURE_TSETDEL"),
        ("tset-milling", "FEATURE_MILLING"),
        ("tset-turning", "FEATURE_TURNING"),
        ("nu-cost-models", "FEATURE_MODEL"),
        ("three-db", "FEATURE_THREEDB"),
        ("friedel", "FEATURE_FRIEDEL"),
    ]

    yamls = [
        "frontend.yml",
        "bomrads.yml",
        "calculation.yml",
        "static.yml",
        "masterdata.yml"
    ]

    parser = argparse.ArgumentParser(description='Services Starter')

    parser.add_argument('--feature', dest='env',
                        help="use the specified feature environment, instead of deducing it from the git branch")
    parser.add_argument('--no-feature', action='store_const', const="", dest='env',
                        help="use the default environment, instead of deducing it from the git branch")

    args = parser.parse_args()

    if args.env is None:
        environment_name = get_environment_from_repo()
    else:
        environment_name = args.env

    if environment_name:
        print(f"working with feature branch environment {environment_name}")
        validate_environment_name(environment_name)
    else:
        print("working with the default environment")

    print("\n======== pulling containers ========\n")

    env_vals = dict()
    for path, env_var in docker_paths_and_env_vars:
        env_val = pull_docker(path, environment_name)
        print(f"pulled {env_val} from {path}")
        env_vals[env_var] = env_val

    if environment_name:
        env_vals["FEATURE_BRANCH_AWARENESS"] = "true"
        env_vals["FEATURE_ENVIRONMENT"] = environment_name
        env_vals["AWS_ENVIRONMENT_FALLBACK"] = f"feature-{environment_name}"

    prepare_env(env_vals)

    print("\n======== starting services ========\n")

    yaml_start(yamls)


if __name__ == "__main__":
    main()
