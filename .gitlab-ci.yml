image: 684712464887.dkr.ecr.eu-central-1.amazonaws.com/maven:latest

stages:
  - validate
  - schema
  - build
  - package
  - verify
  - release
  - cleanup
  - deploy
  - debug
  - publish
  - post-release
  - qa

include:
  - project: devops/devenvs/pipeline-base
    ref: main
    file:
      - maven/gitlab-ci.yml
      - helm/gitlab-ci.yml
      - docker/gitlab-ci.yml
      - release/gitlab-ci.yml
      - datadog/gitlab-ci.yml
      - workflow.yml

variables:
  SERVICE_NAME: nu-bom-kotlin
  PROJECT_NAME: nu-bom-kotlin
  SERVICE_VERSION: $CI_PIPELINE_ID
  COMMIT_ID: $CI_COMMIT_SHORT_SHA
  MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository --add-opens java.base/java.io=ALL-UNNAMED"
  WSOPTICS_VERSION: latest
  APP_BUNDLE: cost
  GITLAB_URL: "https://git.tset.cloud"

check:prepareEnv:
  stage: validate
  dependencies: [ ]
  script:
    - export FEATURE_RAW=$(echo ${CI_COMMIT_REF_SLUG##feature-})
    - echo "FEATURE_RAW=${FEATURE_RAW}" > subdomain.env
  artifacts:
    reports:
      dotenv:
        - subdomain.env
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH
      when: never
    - if: '$CI_COMMIT_REF_NAME =~ /^feature(-|\/)*.*/'
      when: always
  tags:
    - k8s-small
  interruptible: true

check:version:
  stage: validate
  script:
    - task set-version
  artifacts:
    paths:
      - "**/pom.xml"
  tags:
    - k8s-small
  interruptible: true

check:project:
  stage: validate
  script:
    - task check-project
  dependencies:
    - check:version
  tags:
    - k8s-small
  interruptible: true

helm:lint:
  stage: validate
  extends: .helm:lint

check:nu-lib-version:master:
  stage: validate
  script:
    - task verify-nulib
  dependencies:
    - check:version
  rules:
    - if: '$CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH'
  tags:
    - k8s-small
  interruptible: true

# Prepares the Docker image for the other schema jobs.
schema:prepare:
  stage: schema
  image: 684712464887.dkr.ecr.eu-central-1.amazonaws.com/docker:281994
  extends: .docker:ship-cached
  variables:
    DOCKERFILE: .gitlab/pipeline/Dockerfile
    DOCKER_IMAGE: $ECR_HOST/nbk-pipeline
    DOCKER_BUILD_FOLDER: .gitlab/pipeline

# Validates schema file changes in merge requests for the current branch:
# - Have any schema files been deleted?
# - Have any schema files been modified?
# - Has more than one single new schema file been added?
# Creates a discussion thread on the merge request for each of the above conditions if they are true.
# The discussion must be resolved by the merge request author, or is auto-deleted if the condition no longer applies.
schema:check:
  stage: schema
  needs:
    - schema:prepare
  image: 684712464887.dkr.ecr.eu-central-1.amazonaws.com/nbk-pipeline:$CI_PIPELINE_ID
  script:
    - cd /app; python -u -m scripts.process_schema_changes
  tags:
    - k8s-tiny

# Diffs the newest schema with the previous one for each merge request of the current branch if the merge
# request contains NEW schema file(s).
# The diff will shown in a new discussion thread that the merge request author must check and verify.
# If the diff contains the expected changes, the author may resolve the discussion thread.
schema:diff:
  stage: schema
  needs:
    - schema:prepare
  image: 684712464887.dkr.ecr.eu-central-1.amazonaws.com/nbk-pipeline:$CI_PIPELINE_ID
  script:
    - cd /app; python -u -m scripts.process_schema_diff
  tags:
    - k8s-tiny

# Compiles and runs EntitiesChangelogTest to generate entity-changelog.log file, then packages jars.
compile:
  stage: build
  extends: .mvn:test-compile
  script:
    - task compile
    - task package
  needs:
    - "check:version"
  variables:
    GIT_STRATEGY: "clone"
    GIT_DEPTH: "0"
  artifacts:
    paths:
      - lit/target
      - neumann-app-bundle/target
      - neumann-clients/target
      - neumann-core/target
      - rough-part-technologies/target
      - neumann-core/src/main/resources/entity-changelog.log
    expire_in: 3 hours
  tags:
    - k8s-large

fuse:helm:
  stage: package
  extends: .helm:package
  dependencies:
    - helm:lint

# Executes only unit tests, excluding integration tests. Integration tests run in their own job.
check:unit:master:
  stage: verify
  extends: .mvn:verify
  dependencies:
    - compile
  variables:
    # We explicitly exclude the 'integration-tests' module here.
    # 'coverage-report-aggregate' also does not need to be built, it is only useful to the 'qa:sonar' job.
    # Also exclude the 'EntitiesChangelogTest' as that is already executed as part of the 'compile' job.
    MVN_CUSTOM_ARGS: |
      -pl '!integration-tests,!coverage-report-aggregate' \
      -Dsurefire.excludes='**/EntitiesChangelogTest*' \
      -Dkotlin.compiler.incremental=true \
      -Dproject.version=$SERVICE_VERSION \
      -Dspring.profiles.active=ci \
      -Dktlint-format.skip=true \
      -Dsurefire.skipAfterFailureCount=1 \
      -Dlogging.config=classpath:logback-ci.xml \
      -Dlogback.configurationFile=logback-ci.xml \
  artifacts:
    paths:
      - test.log
      - "*/target/surefire-reports/*-output.txt"
      - "*/target/site"
      - "*/target/detekt.xml"
      - "*/target/classes"
      - "*/target/jacoco.exec"
    when: always
    reports:
      junit:
        - "*/target/surefire-reports/TEST-*.xml"
  timeout: 1h
  rules:
    - if: '$CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH'
  interruptible: true
  cache:
    policy: pull
  tags:
    - k8s-large

# Executes only integration tests.
check:integration:master:
  stage: verify
  extends: .mvn:verify
  dependencies:
    - "check:version"
    - "compile"
  script:
    - mvn install -pl integration-tests --also-make -DskipTests --no-transfer-progress
    - task maven:verify
  variables:
    MVN_CUSTOM_ARGS: |
      -pl 'integration-tests' \
      -Dkotlin.compiler.incremental=true \
      -Dproject.version=$SERVICE_VERSION \
      -Dspring.profiles.active=ci \
      -Dktlint-format.skip=true \
      -Dsurefire.skipAfterFailureCount=1 \
      -Dlogging.config=classpath:logback-ci.xml \
      -Dlogback.configurationFile=logback-ci.xml \
  services:
    - name: docker:24-dind
      alias: docker
    - 684712464887.dkr.ecr.eu-central-1.amazonaws.com/clamav:latest
    - 684712464887.dkr.ecr.eu-central-1.amazonaws.com/wsoptics:${WSOPTICS_VERSION}
  artifacts:
    paths:
      - test.log
      - "*/target/surefire-reports/*-output.txt"
      - "*/target/site"
      - "*/target/detekt.xml"
      - "integration-tests/logs/*.log"
      - "*/target/jacoco.exec"
    when: always
    reports:
      junit:
        - "*/target/surefire-reports/TEST-*.xml"
  timeout: 2h
  rules:
    - if: '$CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH'
  interruptible: true
  cache:
    policy: pull
  tags:
    - k8s-huge

ship:docker:
  stage: release
  extends: .docker:ship
  dependencies:
    - compile

datadog:metadata:
  stage: release
  extends: .datadog:metadata
  dependencies:
    - compile

scan:ECR:
  stage: release
  extends: .scan-ECR
  needs:
    - ship:docker

scan:grype:
  stage: release
  extends: .scan-grype
  needs:
    - ship:docker

ship:chart:
  stage: release
  extends: .helm:release
  dependencies:
    - fuse:helm
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH
    - if: '$CI_COMMIT_REF_NAME =~ /^feature(-|\/)*.*/'

deploy:feature:
  stage: deploy
  extends: .deploy.feature
  needs:
    - check:prepareEnv
    - fuse:helm
    - ship:docker
  variables:
    HELM_CHART_NAME: nu-bom-kotlin
    HELM_CUSTOM_ARGS: |
      --set mongodb.db_name=feature-database \
      --set mongodb.hosted=true \
      --set rabbitmq.name=$FEATURE_NAME \
      --set rabbitmq.drop=true \
      --set elasticsearch.name=$FEATURE_NAME \
      --set redis.name=develop \
      --set spring.zipkin.enabled=false \
      --set s3.restore=false \
      --set s3.environment= \
      --set pod_request_memory=8Gi \
      --set pod_limit_memory=12Gi \
      --set feature.base=bct
  environment:
    name: FEATURES/$CI_COMMIT_REF_SLUG
    url: "https://${FEATURE_RAW}.cost.feature.tset.cloud"
    auto_stop_in: "one week"
  before_script:
    - |
      set +x
      HELM_RELEASE_NAME="${SERVICE_NAME}-${FEATURE_NAME}"
      echo "Datadog logs for this job: https://app.datadoghq.eu/logs?query=service:${HELM_RELEASE_NAME}"
  timeout: 45m

deploy:bct:
  stage: deploy
  extends: .deploy.feature
  needs:
    - check:prepareEnv
    - fuse:helm
    - ship:docker
  variables:
    HELM_CUSTOM_ARGS: |
      --set mongodb.db_name=feature-database \
      --set mongodb.hosted=true \
      --set rabbitmq.name=$FEATURE_NAME \
      --set rabbitmq.drop=true \
      --set elasticsearch.name=$FEATURE_NAME \
      --set redis.name=develop \
      --set spring.zipkin.enabled=false \
      --set s3.restore=false \
      --set s3.environment= \
      --set pod_request_memory=8Gi \
      --set pod_limit_memory=12Gi
  environment:
    name: FEATURES/$CI_COMMIT_REF_SLUG
    url: "https://${FEATURE_RAW}.cost.feature.tset.cloud"
  rules:
    - if: $CI_COMMIT_REF_NAME == "feature/bct"

deploy:restore:
  stage: deploy
  extends: .deploy.feature
  needs:
    - check:prepareEnv
    - fuse:helm
    - ship:docker
  variables:
    HELM_CHART_NAME: nu-bom-kotlin
    HELM_CUSTOM_ARGS: |
      --set mongodb.db_name=$DATABASE \
      --set mongodb.hosted=true \
      --set mongodb.memory_limit=$MEMORY_LIMIT \
      --set rabbitmq.name=$FEATURE_NAME \
      --set rabbitmq.drop=true \
      --set elasticsearch.name=$FEATURE_NAME \
      --set redis.name=develop \
      --set spring.zipkin.enabled=false \
      --set s3.restore=true \
      --set s3.environment=$S3_ENVIRONMENT \
      --set pod_request_memory=35Gi \
      --set pod_limit_memory=40Gi \
      --set nu.generic.disable_view_refresh=true \
      --set feature.base=restore \
      --set java.memory_opts="-XX:+UseContainerSupport -XX:MaxRAMPercentage=85" \
      --set datadog.environment=develop
    MEMORY_LIMIT: 4G
  rules:
    - if: '$CI_COMMIT_REF_NAME =~ /^feature\/restore-prod*.*/'
      variables:
        S3_ENVIRONMENT: production
        DATABASE: production
        KUBE_NAMESPACE: production
    - if: '$CI_COMMIT_REF_NAME =~ /^feature\/restore-tc*.*/'
      variables:
        S3_ENVIRONMENT: tc
        DATABASE: tc
        KUBE_NAMESPACE: tc
    - if: '$CI_COMMIT_REF_NAME =~ /^feature\/restore-zf*.*/'
      variables:
        S3_ENVIRONMENT: zf
        DATABASE: zf
        KUBE_NAMESPACE: zf
    - if: '$CI_COMMIT_REF_NAME =~ /^feature\/restore-bmw*.*/'
      variables:
        S3_ENVIRONMENT: bmw
        DATABASE: bmw
        KUBE_NAMESPACE: bmw
  timeout: 6h

deploy:develop:
  stage: deploy
  extends: .deploy
  needs:
    - fuse:helm
    - ship:docker
  variables:
    HELM_CHART_NAME: nu-bom-kotlin
    REPLICAS: "2"
    HELM_CUSTOM_ARGS: |
      --set mongodb.db_name=develop \
      --set mongodb.hosted=true \
      --set rabbitmq.name=develop \
      --set rabbitmq.drop=false \
      --set elasticsearch.name=develop \
      --set redis.name=develop \
      --set spring.zipkin.enabled=false \
      --set s3.restore=false \
      --set s3.environment="" \
      --set pod_request_memory=15Gi \
      --set pod_limit_memory=15Gi \
      --set nu.generic.disable_view_refresh=false
  environment:
    name: DEV
    url: "https://cost.develop.tset.cloud"
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH
  timeout: 1h

publish:develop:
  stage: publish
  extends: .publish-develop
  needs:
    - deploy:develop

publish:production:
  stage: publish
  extends: .publish-production

publish:release:
  stage: publish
  extends: .publish-release

publish:trigger-client-sdk-build:
  stage: publish
  trigger:
    project: backend/client-sdk
    branch: main
  variables:
    API_URL: "https://cost.develop.tset.cloud"
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  allow_failure: true

check:trigger-client-sdk-build:feature:
  stage: post-release
  needs: ["check:prepareEnv", "deploy:feature"]
  trigger:
    project: backend/client-sdk
    branch: main
    forward:
      pipeline_variables: true
    strategy: depend
  variables:
    API_URL: "https://${FEATURE_RAW}.cost.feature.tset.cloud"
  rules:
    - if: '$CI_COMMIT_REF_NAME =~ /^feature\/restore*.*/'
      when: never
    - if: '$CI_COMMIT_REF_NAME =~ /^feature\/bct/'
      when: never
    - if: '$CI_COMMIT_REF_NAME =~ /^feature(-|\/)*.*/'
      when: on_success
  allow_failure: false

check:ktlint:
  stage: post-release
  script:
    - task ktlint
  dependencies:
    - check:version
    - compile
  rules:
    - if: '$CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH'
    - if: '$CI_COMMIT_REF_NAME =~ /^feature(-|\/)*.*/'
  tags:
    - k8s-medium

# Executes only unit tests, excluding integration tests. Integration tests run in their own job.
check:unit:non-master:
  stage: post-release
  extends: .mvn:verify
  dependencies:
    - compile
  variables:
    # We explicitly exclude the 'integration-tests' module here.
    # 'coverage-report-aggregate' also does not need to be built, it is only useful to the 'qa:sonar' job.
    # Also exclude the 'EntitiesChangelogTest' as that is already executed as part of the 'compile' job.
    MVN_CUSTOM_ARGS: |
      -pl '!integration-tests,!coverage-report-aggregate' \
      -Dsurefire.excludes='**/EntitiesChangelogTest*' \
      -Dkotlin.compiler.incremental=true \
      -Dproject.version=$SERVICE_VERSION \
      -Dspring.profiles.active=ci \
      -Dktlint-format.skip=true \
      -Dsurefire.skipAfterFailureCount=1 \
      -Dlogging.config=classpath:logback-ci.xml \
      -Dlogback.configurationFile=logback-ci.xml \
  artifacts:
    paths:
      - "test.log"
      - "*/target/surefire-reports/*-output.txt"
      - "*/target/site"
      - "*/target/detekt.xml"
      - "*/target/classes"
      - "*/target/jacoco.exec"
    when: always
    reports:
      junit:
        - "*/target/surefire-reports/TEST-*.xml"
  timeout: 1h
  rules:
    - if: $CI_COMMIT_REF_NAME != $CI_DEFAULT_BRANCH
  interruptible: true
  cache:
    policy: pull
  tags:
    - k8s-large

# Executes only integration tests.
check:integration:non-master:
  stage: post-release
  extends: .mvn:verify
  dependencies:
    - check:version
    - compile
  script:
    - mvn install -pl integration-tests --also-make -DskipTests --no-transfer-progress
    - task maven:verify
  variables:
    MVN_CUSTOM_ARGS: |
      -pl 'integration-tests' \
      -Dkotlin.compiler.incremental=true \
      -Dproject.version=$SERVICE_VERSION \
      -Dspring.profiles.active=ci \
      -Dktlint-format.skip=true \
      -Dsurefire.skipAfterFailureCount=1 \
      -Dlogging.config=classpath:logback-ci.xml \
      -Dlogback.configurationFile=logback-ci.xml \
  services:
    - name: docker:24-dind
      alias: docker
    - 684712464887.dkr.ecr.eu-central-1.amazonaws.com/clamav:latest
    - 684712464887.dkr.ecr.eu-central-1.amazonaws.com/wsoptics:${WSOPTICS_VERSION}
  artifacts:
    paths:
      - "test.log"
      - "*/target/surefire-reports/*-output.txt"
      - "*/target/site"
      - "*/target/detekt.xml"
      - "integration-tests/logs/*.log"
      - "*/target/jacoco.exec"
    when: always
    reports:
      junit:
        - "*/target/surefire-reports/TEST-*.xml"
  timeout: 2h
  rules:
    - if: $CI_COMMIT_REF_NAME != $CI_DEFAULT_BRANCH
  interruptible: true
  cache:
    policy: pull
  tags:
    - k8s-huge

debug:enable-datadog:
  stage: debug
  extends: .enable-datadog

debug:disable-datadog:
  stage: debug
  extends: .disable-datadog

check:validationbot-cost:
  stage: qa
  needs: [ "check:prepareEnv" ]
  trigger:
    project: backend/validation-bot/cost-modules-test
    branch: master
    strategy: depend
  variables:
    FEATURE_BRANCH_ENVIRONMENT_NAME: $FEATURE_RAW
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_REF_NAME =~ /^feature(-|\/)*.*/'
      when: manual
  allow_failure: true

check:validationbot-bct:
  stage: qa
  needs: [ "check:prepareEnv" ]
  trigger:
    project: backend/validation-bot/backwards-compatibility-test
    branch: master
    strategy: depend
  variables:
    FEATURE_BRANCH_ENVIRONMENT_NAME: $FEATURE_RAW
  rules:
    - if: '$CI_COMMIT_REF_NAME =~ /^feature(-|\/)*.*/'
      when: manual
  allow_failure: true

check:regression:
  stage: qa
  needs: [ "check:prepareEnv" ]
  trigger:
    project: qa/nu-ui-test
    branch: master
  variables:
    ENV: "https://${FEATURE_RAW}.cost.feature.tset.cloud/"
    TESTTYPE: "test"
    THREAD_NUM: "3"
    TEST_PLAN: ""
    TEST_PROJECT: ""
  rules:
    - if: '$CI_COMMIT_REF_NAME =~ /^feature(-|\/)*.*/'
      when: manual
  allow_failure: true

check:api:
  stage: qa
  needs: [ "check:prepareEnv" ]
  trigger:
    project: qa/nu-api-test
    branch: master
  variables:
    ENV: "https://${FEATURE_RAW}.cost.feature.tset.cloud/"
    TESTTYPE: "test"
    THREAD_NUM: "1"
  rules:
    - if: '$CI_COMMIT_REF_NAME =~ /^feature(-|\/)*.*/'
      when: manual
  allow_failure: true

qa:sonar:
  stage: qa
  extends: .mvn:sonar
  script:
    # Both unit and integration test jobs will produce class files and jacoco .exec files as artifacts.
    # jacoco:report-aggregate combines them into a single report in the 'coverage-report-aggregate' module,
    # which we pass on to Sonar.
    - mvn jacoco:report-aggregate
    - task maven:sonar
  needs:
    - job: check:version
    - job: check:unit:master
      optional: true
    - job: check:unit:non-master
      optional: true
    - job: check:integration:master
      optional: true
    - job: check:integration:non-master
      optional: true
  variables:
    MVN_CUSTOM_ARGS: "-Dsonar.qualitygate.wait=true"
  allow_failure: true
  rules:
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH
      variables:
        MVN_CUSTOM_ARGS: "-Dsonar.qualitygate.wait=false"
    - if: $CI_COMMIT_REF_NAME != $CI_DEFAULT_BRANCH
      variables:
        MVN_CUSTOM_ARGS: "-Dsonar.qualitygate.wait=true"
  artifacts:
    paths:
      - "*/target/site"
      - "*/target/classes"
      - "*/target/jacoco.exec"
  cache: [] # Sonar job doesn't need anything that's in the cache, just requires job artifacts.
  tags:
    - k8s-medium

qa:vulnerability:
  stage: qa
  extends: .mvn:dependency-check
  variables:
    MVN_PROFILE: ci
  needs:
    - compile
  allow_failure: true

run:calculation-engine-tests:
  stage: qa
  needs: [ "check:prepareEnv" ]
  trigger:
    project: backend/calculation-engine-tests
    branch: main
  variables:
    API_URL: "https://${FEATURE_RAW}.cost.feature.tset.cloud/"
  rules:
    - if: '$CI_COMMIT_REF_NAME =~ /^feature(-|\/)*.*/'
      when: manual
  allow_failure: true
