image: 684712464887.dkr.ecr.eu-central-1.amazonaws.com/gradle:latest

default:
  retry:
    max: 2
    when:
      - "unknown_failure"
      - "runner_system_failure"
      - "stuck_or_timeout_failure"
      - "scheduler_failure"
      - "job_execution_timeout"

stages:
  - init
  - test
  - build
  - package
  - execution
  - comparison
  - update
  - postrun

setup:
  stage: init
  script:
    - env | grep VB_ | sort
  tags:
    - k8s-tiny

include:
  - project: devops/devenvs/pipeline-base
    ref: main
    file:
      - gradle-ci.yml
      - security-ci.yml

variables:
  VB_BASELINE_ENVIRONMENT_URL:
    value: "https://cost.develop.tset.cloud"
    description: "The URL you want to generate the baseline from"
  VB_BASELINE_MODE:
    value: "Simulate"
    description: "Should be one of: Simulate, <PERSON><PERSON>, <PERSON>cal<PERSON>, ForceRecalc, Recalc<PERSON>nlyNewFieldsAndSave, CreateAndPersist"
  VB_BASELINE_PROJECT_KEY:
    value: ""
    description: "For execution-mode Simulate/CreateAndPersist, set to empty string. For Fetch/Recalc/ForceRecalc/RecalcOnlyNewFieldsAndSave, either set to the key of the project to operate on (e.g. 'AKEY'), or leave empty to operate on all projects"
  VB_BASELINE_WORKSPACE_NAME:
    value: ""
    description: "Workspace to load calculations from. It needs the VB_FOLDER_NAME provided as well"
  VB_BASELINE_FOLDER_NAME:
    value: ""
    description: "Folder to load calculations from. It needs the VB_WORKSPACE_NAME provided as well"
  VB_BASELINE_COST_MODULES_ALLOWED_LIST:
    value: "AFOR, ALEX, BART, CEXT, CHAT, CHILL, CORES, CROL, CUBE, DCA, DFOR, INJ2, LAST, MAGN, MINJ, PBOX, PCB, PCBA, PREC, RINJ, RROL, RSWA, SAND, SINT, VPREC, WHAT,"
    description: "Comma-separated list of tested technologies for execution-mode Simulate/CreateAndPersist"
  VB_BASELINE_ACCOUNT_NAME:
    value: "tset-logicbot"
    description: "The NBK account name. Account used via google SSO is 'account_default'. Set to empty string to operate on all accounts"
  VB_ALTERNATIVE_ENVIRONMENT_URL:
    value: "https://BRANCHNAME.cost.feature.tset.cloud"
    description: "The URL of the feature stage you want to compare to the baseline"
  VB_ALTERNATIVE_MODE:
    value: "Simulate"
    description: "See VB_BASELINE_MODE"
  VB_ALTERNATIVE_PROJECT_KEY:
    value: ""
    description: "See VB_BASELINE_PROJECT_KEY"
  VB_ALTERNATIVE_WORKSPACE_NAME:
    value: ""
    description: "See VB_BASELINE_WORKSPACE_NAME"
  VB_ALTERNATIVE_FOLDER_NAME:
    value: ""
    description: "See VB_BASELINE_FOLDER_NAME"
  VB_ALTERNATIVE_COST_MODULES_ALLOWED_LIST:
    value: "AFOR, ALEX, BART, CEXT, CHAT, CHILL, CORES, CROL, CUBE, DCA, DFOR, INJ2, LAST, MAGN, MINJ, PBOX, PCB, PCBA, PREC, RINJ, RROL, RSWA, SAND, SINT, VPREC, WHAT,"
    description: "See VB_BASELINE_COST_MODULES_ALLOWED_LIST"
  VB_ALTERNATIVE_ACCOUNT_NAME:
    value: "tset-logicbot"
    description: "See VB_BASELINE_ACCOUNT_NAME"
  VB_MAX_CONCURRENT_CALCULATIONS:
    value: "2"
    description: "Non-negative integer expected"
  VB_MAX_SPINUP_WAIT_TIME_SECONDS:
    value: "1000"
    description: "Integer >= 10. Maximum time to wait before aborting if a spinup is detected. Check happens every 10 seconds."
  VB_OUTPUT_SLACK_CHANNEL_NAME:
    value: "#validation-bot"
    description: "The channel name of the target slack channel (validation bot needs to be a member)"
  VB_SLACK_MESSAGE_TITLE:
    value: ""
    description: "An optional title to help identifying your particular run among all others"
  VB_LIMIT_PER_TECHNOLOGY:
    value: "-1"
    description: "When creating calculations, how many per technology (negative value for unlimited)"
  VB_ACCURACY_THRESHOLD:
    value: "0.*********"
    description: "Float between 0 and 1. Minimum relation between numeric fields for a field-comparison to be flagged as successful"
  VB_OLD_BASELINE_RESULT:
    value: ""
    description: "Use old execution run if you want, or leave empty for new run"
  VB_OLD_ALTERNATIVE_RESULT:
    value: ""
    description: "Use old execution run if you want, leave empty for new run"
  VB_UPDATE_REFERENCE_RESULT:
    value: "false"
    description: "Boolean. If true, allows to set the result of the alternative run as the new reference-result for the given VB_REFERENCE_RESULT_IDENTIFIER. Happens automatically if the comparison job succeeded."
  VB_FORCE_UPDATE_REFERENCE_RESULT:
    value: "false"
    description: "Boolean. If true, sets the VB_OLD_ALTERNATIVE_RESULT as the new reference-result for the set VB_REFERENCE_RESULT_IDENTIFIER without any comparison job."
  VB_REFERENCE_RESULT_IDENTIFIER:
    value: ""
    description: "If not empty, baseline will refer to this reference result."
  VB_OLD_REFERENCE_RESULT_IDENTIFIER:
    value: ""
    description: "If VB_FORCE_UPDATE_REFERENCE_RESULT, but no VB_OLD_ALTERNATIVE_RESULT was provided, the underlying execution run of this reference result identifier will be used instead"
  VB_RETRIES:
    value: "5"
    description: "How many retries to do when a request fails due to infrastructure instability"
  VB_REQUEST_TIMEOUT_SECONDS:
    value: "300"
    description: "How long to wait for a response from the public-api, in seconds."
  VB_CONCURRENCY:
    value: "100"
    description: "How many calculation can be in the internal validation bot pipeline at the same time"
  VB_PARTIAL_RESULT_BATCH_SIZE:
    value: "20"
    description: "When set to N, partial results are saved every N calculations. A re-run of a failed job will continue from the last saved batch."
  VB_EAGER_MIGRATION:
    value: "false"
    description: "Boolean. If true, migrates all projects for the EAGER_USER on the EAGER_BRANCH via calling the VB_EAGER_MIGRATION_ENDPOINT."
  VB_EAGER_ENVIRONMENT_URL:
    value: ""
    description: "If not empty, EAGER_MIGRATION will migrate this environment."
  VB_EAGER_MIGRATION_ENDPOINT:
    value: ""
    description: "If not empty, EAGER_MIGRATION will call that endpoint."
  VB_EAGER_HTTP_METHOD:
    value: ""
    description: "If not empty, EAGER_MIGRATION will use that method. Allowed methods are: POST, GET, DELETE. For POST an empty body is sent."
  VB_EAGER_ACCOUNT_NAME:
    value: ""
    description: "If not empty, EAGER_MIGRATION will use that account. Otherwise the whole environment will be migrated."
  VB_EAGER_SPECIFIC_BOM_NODE_IDS:
    value: ""
    description: "Comma-separated list of bomNodeIds. If none are provided, all ids will be migrated."
  VB_MDM_MIGRATION:
    value: "false"
    description: "Boolean. If true, Masterdata Migration will be run"


  PROJECT_NAME: validation-bot
  SERVICE_NAME: validation-bot
  SERVICE_VERSION: $CI_PIPELINE_ID
  RUN_ID: $CI_PIPELINE_ID
  S3_USER_NAME: op://tset_platform-develop/_validation-bot.user.iam/username
  S3_USER_PASSWORD: op://tset_platform-develop/_validation-bot.user.iam/password
  S3_REGION: $AWS_DEFAULT_REGION
  SLACK_TOKEN: op://tset_platform-develop/_validation-bot.slack/password
  VB_PIPELINE_TRIGGERING_USER: $GITLAB_USER_EMAIL
  VB_SLACK_USER_READ_TOKEN: op://DevOps/_slack.qa.user_read/password

test:unit:
  stage: test
  extends: .gradle:test
  script:
    gradle test --info
  tags:
    - k8s-medium

check:ktlint:
  stage: test
  extends: .gradle:kotlin-linter
  script:
    gradle ktlintCheck --info
  tags:
    - k8s-medium

run:fetch_reference:
  stage: execution
  variables:
    VB_MODUS_OPERANDI: FetchReference
    VB_OUTPUT_VARIABLE: VB_REFERENCE_RESULT
  rules:
    - if: '$CI_PIPELINE_SOURCE != "web" && $CI_PIPELINE_SOURCE != "schedule" && $CI_PIPELINE_SOURCE != "pipeline"'
      when: never
    - if: '$VB_FORCE_UPDATE_REFERENCE_RESULT == "true"'
      when: never
    - if: '$VB_EAGER_MIGRATION == "true"'
      when: never
    - if: '$VB_MDM_MIGRATION == "true"'
      when: never
    - if: '$VB_REFERENCE_RESULT_IDENTIFIER != ""'
  script: task run
  timeout: 30m
  artifacts:
    reports:
      dotenv:
        - build/fetch_reference.env
  interruptible: true
  tags:
    - k8s-medium

run:baseline:
  stage: execution
  variables:
    VB_MODUS_OPERANDI: Execute
    VB_ENVIRONMENT_URL: $VB_BASELINE_ENVIRONMENT_URL
    VB_MODE: $VB_BASELINE_MODE
    VB_PROJECT_KEY: $VB_BASELINE_PROJECT_KEY
    VB_WORKSPACE_NAME: $VB_BASELINE_WORKSPACE_NAME
    VB_FOLDER_NAME: $VB_BASELINE_FOLDER_NAME
    VB_COST_MODULES_ALLOWED_LIST: $VB_BASELINE_COST_MODULES_ALLOWED_LIST
    VB_ACCOUNT_NAME: $VB_BASELINE_ACCOUNT_NAME
    VB_OUTPUT_VARIABLE: VB_NEW_BASELINE_RESULT
  script: task run
  timeout: 48h
  artifacts:
    reports:
      dotenv:
        - build/exec_result.env
  rules:
    - if: '$CI_PIPELINE_SOURCE != "web" && $CI_PIPELINE_SOURCE != "schedule" && $CI_PIPELINE_SOURCE != "pipeline"'
      when: never
    - if: '$VB_FORCE_UPDATE_REFERENCE_RESULT == "true"'
      when: never
    - if: '$VB_EAGER_MIGRATION == "true"'
      when: never
    - if: '$VB_MDM_MIGRATION == "true"'
      when: never
    - if: '$VB_REFERENCE_RESULT_IDENTIFIER == "" && $VB_OLD_BASELINE_RESULT == ""'
  interruptible: true
  tags:
    - k8s-medium

run:alternative:
  stage: execution
  variables:
    VB_MODUS_OPERANDI: Execute
    VB_ENVIRONMENT_URL: $VB_ALTERNATIVE_ENVIRONMENT_URL
    VB_MODE: $VB_ALTERNATIVE_MODE
    VB_PROJECT_KEY: $VB_ALTERNATIVE_PROJECT_KEY
    VB_WORKSPACE_NAME: $VB_ALTERNATIVE_WORKSPACE_NAME
    VB_FOLDER_NAME: $VB_ALTERNATIVE_FOLDER_NAME
    VB_COST_MODULES_ALLOWED_LIST: $VB_ALTERNATIVE_COST_MODULES_ALLOWED_LIST
    VB_ACCOUNT_NAME: $VB_ALTERNATIVE_ACCOUNT_NAME
    VB_OUTPUT_VARIABLE: VB_NEW_ALTERNATIVE_RESULT
  script: task run
  timeout: 48h
  artifacts:
    reports:
      dotenv:
        - build/exec_result.env
  rules:
    - if: '$CI_PIPELINE_SOURCE != "web" && $CI_PIPELINE_SOURCE != "schedule" && $CI_PIPELINE_SOURCE != "pipeline"'
      when: never
    - if: '$VB_FORCE_UPDATE_REFERENCE_RESULT == "true"'
      when: never
    - if: '$VB_EAGER_MIGRATION == "true"'
      when: never
    - if: '$VB_MDM_MIGRATION == "true"'
      when: never
    - if: '$VB_OLD_ALTERNATIVE_RESULT == ""'
  interruptible: true
  tags:
    - k8s-medium

run:comparison:
  stage: comparison
  before_script:
    - export VB_BASELINE_RESULT=$(echo ${VB_REFERENCE_RESULT:-${VB_NEW_BASELINE_RESULT:-$VB_OLD_BASELINE_RESULT}})
    - export VB_ALTERNATIVE_RESULT=$(echo ${VB_NEW_ALTERNATIVE_RESULT:-$VB_OLD_ALTERNATIVE_RESULT})
  variables:
    VB_MODUS_OPERANDI: Compare
  script: task run
  artifacts:
    reports:
      junit: test-results/TEST-*.xml
    paths:
      - reference_id.txt
      - report-urls/
      - test-results/
    when: always
  rules:
    - if: '$CI_PIPELINE_SOURCE != "web" && $CI_PIPELINE_SOURCE != "schedule" && $CI_PIPELINE_SOURCE != "pipeline"'
      when: never
    - if: '$VB_FORCE_UPDATE_REFERENCE_RESULT == "true"'
      when: never
    - if: '$VB_EAGER_MIGRATION == "true"'
      when: never
    - if: '$VB_MDM_MIGRATION == "true"'
      when: never
    - if: '$VB_REFERENCE_RESULT != "" || $VB_NEW_BASELINE_RESULT != "" || $VB_OLD_BASELINE_RESULT != ""'
  interruptible: true
  tags:
    - k8s-medium

run:update_reference_automatic:
  stage: update
  trigger:
    strategy: depend
    include:
      - local: /ci-helper/update_reference_automatic.yml
    forward:
      pipeline_variables: true
      yaml_variables: true
  variables:
    VB_PARENT_PIPELINE_ID: $CI_PIPELINE_ID
  rules:
    - if: '$CI_PIPELINE_SOURCE != "web" && $CI_PIPELINE_SOURCE != "schedule" && $CI_PIPELINE_SOURCE != "pipeline"'
      when: never
    - if: '$VB_FORCE_UPDATE_REFERENCE_RESULT == "true"'
      when: never
    - if: '$VB_EAGER_MIGRATION == "true"'
      when: never
    - if: '$VB_MDM_MIGRATION == "true"'
      when: never
    - if: '$VB_UPDATE_REFERENCE_RESULT == "true"'
      when: on_success

run:update_reference_manual:
  stage: update
  trigger:
    include:
      - local: /ci-helper/update_reference_manual.yml
    forward:
      pipeline_variables: true
      yaml_variables: true
  variables:
    VB_PARENT_PIPELINE_ID: $CI_PIPELINE_ID
  rules:
    - if: '$CI_PIPELINE_SOURCE != "web" && $CI_PIPELINE_SOURCE != "schedule" && $CI_PIPELINE_SOURCE != "pipeline"'
      when: never
    - if: '$VB_FORCE_UPDATE_REFERENCE_RESULT == "true"'
      when: never
    - if: '$VB_EAGER_MIGRATION == "true"'
      when: never
    - if: '$VB_MDM_MIGRATION == "true"'
      when: never
    - if: '$VB_UPDATE_REFERENCE_RESULT == "true"'
      when: on_failure

run:force_update_reference:
  stage: update
  variables:
    VB_MODUS_OPERANDI: UpdateReference
    VB_ALTERNATIVE_RESULT: $VB_OLD_ALTERNATIVE_RESULT
  script: task run
  timeout: 30m
  interruptible: true
  rules:
    - if: '$CI_PIPELINE_SOURCE != "web" && $CI_PIPELINE_SOURCE != "schedule" && $CI_PIPELINE_SOURCE != "pipeline"'
      when: never
    - if: '$VB_EAGER_MIGRATION == "true"'
      when: never
    - if: '$VB_MDM_MIGRATION == "true"'
      when: never
    - if: '$VB_FORCE_UPDATE_REFERENCE_RESULT == "true"'
  tags:
    - k8s-medium

run:activate_post_run_persist:
  stage: postrun
  needs:
    - run:comparison
  when: on_failure
  script:
    - echo "Oh no, comparison failed, let's figure out why!"
  rules:
    - if: '$VB_FORCE_UPDATE_REFERENCE_RESULT == "true"'
      when: never
    - if: '$VB_EAGER_MIGRATION == "true"'
      when: never
    - if: '$VB_MDM_MIGRATION == "true"'
      when: never
    - if: '$CI_PIPELINE_SOURCE == "web" || $CI_PIPELINE_SOURCE == "schedule" || $CI_PIPELINE_SOURCE == "pipeline"'
  tags:
    - k8s-tiny

run:baseline_post_run_persist:
  stage: postrun
  needs:
    - job: run:activate_post_run_persist
    # needs to have dotenv artifacts available
    - job: run:baseline
      optional: true
    - job: run:alternative
      optional: true
    - job: run:fetch_reference
      optional: true
  before_script:
    - export VB_BASELINE_RESULT=$(echo ${VB_REFERENCE_RESULT:-${VB_NEW_BASELINE_RESULT:-$VB_OLD_BASELINE_RESULT}})
    - export VB_ALTERNATIVE_RESULT=$(echo ${VB_NEW_ALTERNATIVE_RESULT:-$VB_OLD_ALTERNATIVE_RESULT})
  variables:
    VB_MODUS_OPERANDI: PostRunPersist
    VB_RESULT_TO_PERSIST_ON: Baseline
  script: task run
  rules:
    - if: '$VB_FORCE_UPDATE_REFERENCE_RESULT == "true"'
      when: never
    - if: '$VB_EAGER_MIGRATION == "true"'
      when: never
    - if: '$VB_MDM_MIGRATION == "true"'
      when: never
    - if: '$CI_PIPELINE_SOURCE == "web" || $CI_PIPELINE_SOURCE == "schedule" || $CI_PIPELINE_SOURCE == "pipeline"'
      when: manual
  allow_failure: true # do not affect pipeline result
  timeout: 2h
  interruptible: true
  tags:
    - k8s-medium

run:alternative_post_run_persist:
  stage: postrun
  needs:
    - job: run:activate_post_run_persist
    # needs to have dotenv artifacts available
    - job: run:baseline
      optional: true
    - job: run:alternative
      optional: true
    - job: run:fetch_reference
      optional: true
  before_script:
    - export VB_BASELINE_RESULT=$(echo ${VB_REFERENCE_RESULT:-${VB_NEW_BASELINE_RESULT:-$VB_OLD_BASELINE_RESULT}})
    - export VB_ALTERNATIVE_RESULT=$(echo ${VB_NEW_ALTERNATIVE_RESULT:-$VB_OLD_ALTERNATIVE_RESULT})
  variables:
    VB_MODUS_OPERANDI: PostRunPersist
    VB_RESULT_TO_PERSIST_ON: Alternative
  script: task run
  rules:
    - if: '$VB_FORCE_UPDATE_REFERENCE_RESULT == "true"'
      when: never
    - if: '$VB_EAGER_MIGRATION == "true"'
      when: never
    - if: '$VB_MDM_MIGRATION == "true"'
      when: never
    - if: '$CI_PIPELINE_SOURCE == "web" || $CI_PIPELINE_SOURCE == "schedule" || $CI_PIPELINE_SOURCE == "pipeline"'
      when: manual
  allow_failure: true # do not affect pipeline result
  timeout: 2h
  interruptible: true
  tags:
    - k8s-medium

run:eager_migration:
  stage: execution
  variables:
    VB_MODUS_OPERANDI: EagerMigration
  script: task run
  timeout: 48h
  interruptible: true
  rules:
    - if: '$CI_PIPELINE_SOURCE != "web" && $CI_PIPELINE_SOURCE != "schedule" && $CI_PIPELINE_SOURCE != "pipeline"'
      when: never
    - if: '$VB_FORCE_UPDATE_REFERENCE_RESULT == "true"'
      when: never
    - if: '$VB_MDM_MIGRATION == "true"'
      when: never
    - if: '$VB_EAGER_MIGRATION == "true"'
  tags:
    - k8s-medium


run:masterdata_migration:
  stage: execution
  variables:
    VB_MODUS_OPERANDI: MasterdataMigration
  script: task run
  timeout: 8h
  interruptible: true
  rules:
    - if: '$CI_PIPELINE_SOURCE != "web" && $CI_PIPELINE_SOURCE != "schedule" && $CI_PIPELINE_SOURCE != "pipeline"'
      when: never
    - if: '$VB_FORCE_UPDATE_REFERENCE_RESULT == "true"'
      when: never
    - if: '$VB_EAGER_MIGRATION == "true"'
      when: never
    - if: '$VB_MDM_MIGRATION == "true"'
  tags:
    - k8s-medium
