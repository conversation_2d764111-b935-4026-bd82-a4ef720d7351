# Compiled class file
*.class

# Log file
*.log

# BlueJ files
*.ctxt

# Package Files #
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*

# Allure results
allure-results
.allure

# Pitaya results
PitayaReport.html

# Gradle
.gradle
build

# Cache of project
.gradletasknamecache

# Idea
.idea
out
target
/test-output/import.json

# Ignore suites containing tests for local rerun
src/test/resources/testSuites/*fail*
src/test/resources/environments/*fail*

/exportedAfterUpdate.csv
/originalExported.csv
/updatedToImport.csv

env.sh

# used only in setting up secrets
.env
