package com.nu.bom.core.fields

import com.nu.bom.core.exception.readable.NumericInputExceedsLimitException
import com.nu.bom.core.manufacturing.FieldTestBase
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.PiecesUnits
import com.nu.bom.core.technologies.manufacturings.pcba.ManufacturingPrintedCircuitBoardAssembly
import org.junit.jupiter.api.Nested

class PrintedCircuitBoardAssemblyFieldTests {
    @Nested
    inner class CoreSettingRequiredLaborFieldTest : FieldTestBase() {
        override fun getEntity(): ManufacturingEntity = ManufacturingPrintedCircuitBoardAssembly("test")

        override fun getFieldName(): String = "panelWidth"

        override fun testCases(): List<TestCase> =
            listOf(
                TestCase(
                    inputs =
                        listOf(
                            Length(0.003, LengthUnits.MILLIMETER),
                            Length(1.0, LengthUnits.MILLIMETER),
                            Length(1.0, LengthUnits.MILLIMETER),
                            Pieces(5.0, PiecesUnits.PIECE),
                        ),
                    result = NumericInputExceedsLimitException::class,
                ),
                TestCase(
                    inputs =
                        listOf(
                            Length(2.0, LengthUnits.MILLIMETER),
                            Length(3.0, LengthUnits.MILLIMETER),
                            Length(1.0, LengthUnits.MILLIMETER),
                            Pieces(5.0, PiecesUnits.PIECE),
                        ),
                    result = NumericInputExceedsLimitException::class,
                ),
                TestCase(
                    inputs =
                        listOf(
                            Length(3.0, LengthUnits.MILLIMETER),
                            Length(3.0, LengthUnits.MILLIMETER),
                            Length(3.0, LengthUnits.MILLIMETER),
                            Pieces(5.0, PiecesUnits.PIECE),
                        ),
                    result = Length(0.015, LengthUnits.METER),
                ),
                TestCase(
                    inputs =
                        listOf(
                            Length(5.0, LengthUnits.MILLIMETER),
                            Length(3.0, LengthUnits.MILLIMETER),
                            Length(2.0, LengthUnits.MILLIMETER),
                            Pieces(5.0, PiecesUnits.PIECE),
                        ),
                    result = Length(0.01, LengthUnits.METER),
                ),
            )
    }
}
