package com.nu.bom.core.technologies.steps.pboxmanualsplitting

import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.SingleGroupManufacturingStep
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.ManufacturingStepType
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text

@EntityType(Entities.MANUFACTURING_STEP)
@Suppress("unused")
class ManufacturingStepPboxManualSplitting(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = SingleGroupManufacturingStep(name)

    companion object {
        val DEFAULT_TEMPLATE_NAME = Text("ManualSplitting")
    }

    // region Fields from MANUFACTURING

    @Parent(Entities.MANUFACTURING)
    fun key(): Text? = null

    // endregion

    fun manufacturingStepType(): Text = Text(ManufacturingStepType.STANDARD.name)

    fun partsPerCycle() = QuantityUnit(1.0)

    fun utilizationRate() = Rate(0.80)

    fun templateName() = DEFAULT_TEMPLATE_NAME

    @EntityCreation(Entities.LABOR)
    fun createLabor(
        templateName: Text,
        locationName: Text,
    ) = createLaborFromTemplate(templateName, locationName)
}
