package com.nu.bom.core.technologies.manufacturings.pcb

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.Condition
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EngineTransient
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Precompute
import com.nu.bom.core.manufacturing.annotations.TranslationSection
import com.nu.bom.core.manufacturing.annotations.WizardField
import com.nu.bom.core.manufacturing.entities.MissingInputError
import com.nu.bom.core.manufacturing.entities.RoughManufacturing
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.ConfigIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.Layers
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.PcbType
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Surface
import com.nu.bom.core.manufacturing.fieldTypes.TG
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.configuration.VersionedCostModuleField
import com.nu.bom.core.technologies.lookups.pcbPriceReader
import reactor.core.publisher.Mono

@EntityType(Entities.MANUFACTURING)
class ManufacturingPrintedCircuitBoard(
    name: String,
) : RoughManufacturing(name) {
    override val extends = RoughManufacturing(name)
    val model: Model
        get() = Model.PCB

    @Input
    override fun costModuleConfigurationIdentifier(): ConfigIdentifier = throw MissingInputError()

    @Input
    override fun configurationTechnology(): Text = Text(Model.PCB.name)

    /**
     * Returns object that holds versions for cost module for this entity.
     * Most of the time extensions belong to only one cost module.
     */
    @EngineTransient
    fun costModuleVersionProvider(
        configurationTechnology: Text,
        costModuleConfigurationIdentifier: ConfigIdentifier,
    ): Mono<VersionedCostModuleField> =
        if (costModuleConfigurationIdentifier.res.data.isEmpty()) {
            // If we are outside of cost module - we don't provide any version.
            // Consumers have to handle it properly
            Mono.empty()
        } else {
            services.getCostModuleConfigurationField(
                configurationTechnology.res,
                costModuleConfigurationIdentifier.res.data,
            )
        }

    // override to resolve open dependencies
    @Input(active = false)
    fun partLength(): Length? = null

    @Input(active = false)
    fun partHeight(): Length? = null

    @Input(active = false)
    fun partWidth(): Length? = null

    @Input(active = false)
    fun partOuterDiameter(): Length? = null

    @Input(active = false)
    fun partInnerDiameter(): Length? = null

    fun internalCallsPerYear() = Num(12.toBigDecimal())

    fun shapeTechnologyGroup(): Text? = null

    fun projectedAreaPerPart(): Length? = null

    fun materialName() = Text("referalPrice")

    fun reuseOfScrap(): Bool = Bool(false)

    fun technology() = Text("PCB")

    @TranslationSection("models")
    fun technologyModel() = Text(model.entity)

    @WizardField(index = 1)
    @Input
    fun layers(): Layers? = null

    @Input
    fun pcbTypeBool() = Bool(false)

    @Input
    @Precompute
    @WizardField
    @Condition(field = "pcbTypeBool", value = "true", operator = Condition.EQUALS)
    fun hdiBool(layers: Layers): Bool = Bool(layers == Layers.SIX || layers == Layers.EIGHT)

    @WizardField(index = 2)
    @Input
    @Precompute
    @Condition(field = "hdiBool", value = "true", operator = Condition.EQUALS)
    fun pcbType(): PcbType = PcbType.RIGID

    @WizardField(index = 5)
    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun pcbLength(): Length? = null

    @WizardField(index = 6)
    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun pcbWidth(): Length? = null

    @Input
    @Precompute
    @WizardField(index = 3)
    fun tG(): TG? = null

    @WizardField(index = 4)
    @Input
    @Precompute
    fun surface(): Surface? = null

    @DynamicDenominatorUnit(DynamicUnitOverride.COST_UNIT)
    fun pricePerUnit(
        pcbType: PcbType,
        layers: Layers,
        tG: TG,
        surface: Surface,
    ): Mono<Money> {
        // 2022-11 exchangerate from USD in EUR
        val exchangeRate = Rate(0.95)
        return services
            .getLookupTable(
                "ManufacturingPrintedCircuitBoard_PricePerUnit",
                pcbPriceReader,
            ).filter {
                pcbType == it.pcbType && layers == it.layers && tG == it.tg && surface == it.surface
            }.single()
            .map {
                it.costPerArea * exchangeRate
            }
    }

    @Input(active = false)
    fun costPerPart(
        pcbLength: Length,
        pcbWidth: Length,
        pricePerUnit: Money,
    ): Money = Money(pricePerUnit.res * pcbLength.inDecimeter * pcbWidth.inDecimeter)
}
