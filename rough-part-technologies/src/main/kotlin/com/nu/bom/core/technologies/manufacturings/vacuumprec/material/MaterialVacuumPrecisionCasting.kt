package com.nu.bom.core.technologies.manufacturings.vacuumprec.material

import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.MaterialCostMode
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.technologies.manufacturings.prec.material.MaterialPrecisionCasting

@EntityType(Entities.MATERIAL)
class MaterialVacuumPrecisionCasting(name: String) : ManufacturingEntity(name) {
    override val extends = MaterialPrecisionCasting(name)

    @Input
    @SpecialLink("ManufacturingStepVacuumPrecisionCasting", "partsPerCycle")
    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 6)
    val partsPerCycle: QuantityUnit? = null

    @Input
    @ObjectView(ObjectView.MATERIAL, 12)
    val irretrievableLossRate: Rate = Rate(0.0.toBigDecimal())

    @Input
    @ObjectView(ObjectView.MATERIAL, 20)
    val reuseOfScrap: Bool = Bool(false)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun scrapWeightPerPart(): QuantityUnit? = null

    /****** RawMaterials override **********/
    val materialCostMode: MaterialCostMode = MaterialCostMode.SELL_RETRIEVABLE
}
