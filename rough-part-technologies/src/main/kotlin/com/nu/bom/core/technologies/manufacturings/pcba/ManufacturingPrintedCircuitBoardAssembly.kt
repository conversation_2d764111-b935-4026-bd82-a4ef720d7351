package com.nu.bom.core.technologies.manufacturings.pcba

import com.nu.bom.core.exception.readable.LimitType
import com.nu.bom.core.exception.readable.NumericInputExceedsLimitException
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.Condition
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Nocalc
import com.nu.bom.core.manufacturing.annotations.Precompute
import com.nu.bom.core.manufacturing.annotations.WizardField
import com.nu.bom.core.manufacturing.entities.BaseModelManufacturing
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeCoating
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.compareTo
import com.nu.bom.core.manufacturing.fieldTypes.quot
import com.nu.bom.core.service.wizard.steps.WizardElcoFieldStep
import com.nu.bom.core.service.wizard.steps.WizardSpecFieldStep
import com.nu.bom.core.technologies.manufacturings.pcba.material.PrintedCircuitBoardAssembledMaterial
import com.nu.bom.core.technologies.steps.bootloader.ManufacturingStepBootloader
import com.nu.bom.core.technologies.steps.ict.ManufacturingStepInCircuitTest
import com.nu.bom.core.technologies.steps.lasermarking.ManufacturingStepLaserMarking
import com.nu.bom.core.technologies.steps.selectivesoldering.ManufacturingStepSelectiveSoldering
import com.nu.bom.core.technologies.steps.smdline.ManufacturingStepSMDLine
import com.nu.bom.core.technologies.steps.tht.ManufacturingStepTHTLine
import com.nu.bom.core.technologies.steps.wavesoldering.ManufacturingStepWaveSoldering
import com.nu.bom.core.technologies.steps.xray.ManufacturingStepXray
import reactor.core.publisher.Mono
import java.math.BigDecimal
import kotlin.reflect.KClass

@EntityType(Entities.MANUFACTURING)
class ManufacturingPrintedCircuitBoardAssembly(
    name: String,
) : BaseModelManufacturing(name) {
    override val extends = Manufacturing(name)

    override val model: Model
        get() = Model.PCBA

    override val modelSteps: List<KClass<out ManufacturingEntity>>
        get() =
            listOf(
                ManufacturingStepLaserMarking::class,
                ManufacturingStepSMDLine::class,
                ManufacturingStepXray::class,
                ManufacturingStepInCircuitTest::class,
                ManufacturingStepBootloader::class,
                ManufacturingStepTHTLine::class,
                ManufacturingStepWaveSoldering::class,
                ManufacturingStepSelectiveSoldering::class,
            )

    private companion object {
        const val LOOKUP_NAME = "PrintedCircuitBoardAssembledMaterial_SolderingSelection"

        class TemplateLookupEntry(
            val smdTop: Bool,
            val smdBottom: Bool,
            val thtTop: Bool,
            val solderingTop: String,
            val thtBottom: Bool,
            val solderingBottom: String,
        )

        val templateLookupEntryReader: (row: List<String>) -> TemplateLookupEntry = {
            TemplateLookupEntry(
                smdTop = Bool(it[0]),
                smdBottom = Bool(it[1]),
                thtTop = Bool(it[2]),
                solderingTop = it[3],
                thtBottom = Bool(it[4]),
                solderingBottom = it[5],
            )
        }
    }

    @Input
    fun reuseOfScrap(): Bool = Bool(false)

    @Input
    @WizardField(WizardElcoFieldStep::class, 0)
    @Precompute
    @DynamicDenominatorUnit(DynamicUnitOverride.QUANTITY_UNIT)
    fun smdPartsTopSide(): Pieces = Pieces.ZERO

    @Input
    @WizardField(WizardElcoFieldStep::class, 1)
    @Precompute
    @DynamicDenominatorUnit(DynamicUnitOverride.QUANTITY_UNIT)
    fun smdPartsBottomSide(): Pieces = Pieces.ZERO

    @Input
    @WizardField(WizardElcoFieldStep::class, 2)
    @Precompute
    @DynamicDenominatorUnit(DynamicUnitOverride.QUANTITY_UNIT)
    fun thtPartsTopSide(): Pieces = Pieces.ZERO

    @Input
    @WizardField(WizardElcoFieldStep::class, 3)
    @Precompute
    @DynamicDenominatorUnit(DynamicUnitOverride.QUANTITY_UNIT)
    fun thtPartsBottomSide(): Pieces = Pieces.ZERO

    @Input
    fun technology() = Text(model.name)

    @WizardField(WizardSpecFieldStep::class, 0)
    @Input
    fun pcbsPerPanel(): Pieces? = null

    @WizardField(WizardSpecFieldStep::class, 1)
    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun panelLength(): Length? = null

    fun panelWidth(
        panelLength: Length,
        pcbLength: Length,
        pcbWidth: Length,
        pcbsPerPanel: Pieces,
    ): Length {
        if (panelLength < pcbLength) {
            throw NumericInputExceedsLimitException("panelLength", LimitType.LESS, "pcbLength")
        }
        val partsInLength = quot(panelLength, pcbLength).floor()
        val partsInWidth = (pcbsPerPanel / partsInLength).ceil()
        return pcbWidth * partsInWidth
    }

    @WizardField(WizardSpecFieldStep::class, 3)
    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun pcbLength(): Length? = null

    @WizardField(WizardSpecFieldStep::class, 4)
    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun pcbWidth(): Length? = null

    @WizardField(WizardSpecFieldStep::class, 5)
    @Input
    @Precompute
    fun coating(): SelectableBoolean = SelectableBoolean.FALSE

    @WizardField(WizardSpecFieldStep::class, 6)
    @Input
    @Precompute
    @Condition(field = "coating", value = "true", operator = Condition.EQUALS)
    fun coatingAfterSingulation(): SelectableBoolean = SelectableBoolean.FALSE

    @WizardField(WizardSpecFieldStep::class, 7)
    @Input
    @Condition(field = "coating", value = "true", operator = Condition.EQUALS)
    fun stepSubTypeCoating() = StepSubTypeCoating.ACRYLIC

    @WizardField(WizardSpecFieldStep::class, 8)
    @Input
    @Condition(field = "coating", value = "true", operator = Condition.EQUALS)
    @Precompute
    fun coatingAOI(): Bool = Bool(false)

    @WizardField(WizardSpecFieldStep::class, 9)
    @Input
    @Condition(field = "coating", value = "true", operator = Condition.EQUALS)
    @Precompute
    fun coatingAreaTopSide(): Rate = Rate(0.7)

    @WizardField(WizardSpecFieldStep::class, 10)
    @Input
    @Condition(field = "coating", value = "true", operator = Condition.EQUALS)
    @Precompute
    fun coatingAreaBottomSide(): Rate = Rate.ZERO

    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @Condition(field = "coating", value = "true", operator = Condition.EQUALS)
    fun coatingThickness(): Length = Length(0.0, LengthUnits.MILLIMETER)

    @Input
    @Condition(field = "coating", value = "true", operator = Condition.EQUALS)
    fun volatileRateOfCoating(): Rate = Rate.ZERO

    @WizardField(WizardSpecFieldStep::class, 11)
    @Input
    @Precompute
    fun xray(): SelectableBoolean = SelectableBoolean.FALSE

    @WizardField(WizardSpecFieldStep::class, 12)
    @Input
    @Precompute
    fun separatedBootloader(): SelectableBoolean = SelectableBoolean.FALSE

    @WizardField(WizardSpecFieldStep::class, 13)
    @Input
    @Precompute
    fun singulationAfterIctBootlader(): SelectableBoolean = SelectableBoolean.FALSE

    @Nocalc
    private fun solderingTemplateSelection(
        thtPartsTopSide: Pieces,
        thtPartsBottomSide: Pieces,
        smdPartsTopSide: Pieces,
        smdPartsBottomSide: Pieces,
    ): Mono<TemplateLookupEntry> {
        val hasSmdTop = Bool(smdPartsTopSide.res > BigDecimal.ZERO)
        val hasSmdBottom = Bool(smdPartsBottomSide.res > BigDecimal.ZERO)
        val hasThtTopBool = Bool(thtPartsTopSide.res > BigDecimal.ZERO)
        val hasThtBottomBool = Bool(thtPartsBottomSide.res > BigDecimal.ZERO)

        return services
            .getLookupTable(LOOKUP_NAME, templateLookupEntryReader)
            .filter {
                val diffSmdTop = hasSmdTop == it.smdTop
                val diffSmdBottom = hasSmdBottom == it.smdBottom
                val diffThtTop = hasThtTopBool == it.thtTop
                val diffThtBottom = hasThtBottomBool == it.thtBottom
                diffSmdTop && diffSmdBottom && diffThtTop && diffThtBottom
            }.single()
    }

    fun solderingTopSide(
        thtPartsTopSide: Pieces,
        thtPartsBottomSide: Pieces,
        smdPartsTopSide: Pieces,
        smdPartsBottomSide: Pieces,
    ): Mono<Text> =
        solderingTemplateSelection(thtPartsTopSide, thtPartsBottomSide, smdPartsTopSide, smdPartsBottomSide)
            .map { Text(it.solderingTop) }

    fun solderingBottomSide(
        thtPartsTopSide: Pieces,
        thtPartsBottomSide: Pieces,
        smdPartsTopSide: Pieces,
        smdPartsBottomSide: Pieces,
    ): Mono<Text> =
        solderingTemplateSelection(thtPartsTopSide, thtPartsBottomSide, smdPartsTopSide, smdPartsBottomSide)
            .map { Text(it.solderingBottom) }

    @EntityCreation(Entities.PROCESSED_MATERIAL, childCreations = [Entities.MANUFACTURING_STEP])
    fun createMaterial(): ManufacturingEntity =
        createEntity(
            name = "PrintedCircuitBoardAssembledMaterial",
            clazz = PrintedCircuitBoardAssembledMaterial::class,
            entityType = Entities.MATERIAL,
        )
}
