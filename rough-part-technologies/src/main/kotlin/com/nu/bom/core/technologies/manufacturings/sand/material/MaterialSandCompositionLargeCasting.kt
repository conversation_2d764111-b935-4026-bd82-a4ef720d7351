package com.nu.bom.core.technologies.manufacturings.sand.material

import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.FreezeImplementation
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.entities.ComponentMaterial
import com.nu.bom.core.manufacturing.entities.ManualBaseMaterial
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.PriceComponentEntity
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.DensityUnits
import com.nu.bom.core.manufacturing.fieldTypes.DynamicQuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Emission
import com.nu.bom.core.manufacturing.fieldTypes.EmissionUnits
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.MaterialCostMode
import com.nu.bom.core.manufacturing.fieldTypes.MaterialEmissionType
import com.nu.bom.core.manufacturing.fieldTypes.MaterialPriceType
import com.nu.bom.core.manufacturing.fieldTypes.MaterialViewConfig
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Volume
import com.nu.bom.core.manufacturing.fieldTypes.VolumeUnits
import reactor.core.publisher.Flux
import java.math.BigDecimal

@EntityType(Entities.MATERIAL)
class MaterialSandCompositionLargeCasting(name: String) : ManufacturingEntity(name) {
    override val extends = ManualBaseMaterial(name)

    @Input
    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 40)
    fun partsPerCycle(
        @Parent(Entities.MANUFACTURING)
        partPerCycleForMaterial: DynamicQuantityUnit,
    ): DynamicQuantityUnit = partPerCycleForMaterial

    fun densityNewMoldingSand() = Density(1200.0, DensityUnits.KILOGRAM_PER_CM)

    @ReadOnly
    @SpecialLink("Mold upper moldbox", "frameLengthAdjusted")
    fun frameLengthAdjustedUpperMold(): Length? = null

    @ReadOnly
    @SpecialLink("Mold upper moldbox", "frameWidthAdjusted")
    fun frameWidthAdjustedUpperMold(): Length? = null

    @ReadOnly
    @SpecialLink("Mold upper moldbox", "frameHeightAdjusted")
    fun frameHeightAdjustedUpperMold(): Length? = null

    @ReadOnly
    @SpecialLink("Mold lower moldbox", "frameLengthAdjusted")
    fun frameLengthAdjustedLowerMold(): Length? = null

    @ReadOnly
    @SpecialLink("Mold lower moldbox", "frameWidthAdjusted")
    fun frameWidthAdjustedLowerMold(): Length? = null

    @ReadOnly
    @SpecialLink("Mold lower moldbox", "frameHeightAdjusted")
    fun frameHeightAdjustedLowerMold(): Length? = null

    fun moldingBoxVolumeUpperMold(
        frameLengthAdjustedUpperMold: Length,
        frameWidthAdjustedUpperMold: Length,
        frameHeightAdjustedUpperMold: Length,
    ): Volume =
        Volume(
            frameLengthAdjustedUpperMold.inMeter * frameWidthAdjustedUpperMold.inMeter * frameHeightAdjustedUpperMold.inMeter,
            VolumeUnits.CM,
        )

    fun moldingBoxVolumeLowerMold(
        frameLengthAdjustedLowerMold: Length,
        frameWidthAdjustedLowerMold: Length,
        frameHeightAdjustedLowerMold: Length,
    ): Volume =
        Volume(
            frameLengthAdjustedLowerMold.inMeter * frameWidthAdjustedLowerMold.inMeter * frameHeightAdjustedLowerMold.inMeter,
            VolumeUnits.CM,
        )

    fun moldingBoxVolume(
        moldingBoxVolumeUpperMold: Volume,
        moldingBoxVolumeLowerMold: Volume,
    ): Volume = moldingBoxVolumeLowerMold + moldingBoxVolumeUpperMold

    @SpecialLink("MaterialCasting", "shotVolumePerCycle")
    fun shotVolumePerCycleCasting(): Volume? = null

    fun compressionFactor() = Rate(0.3)

    fun sandVolumeUpperBox(
        moldingBoxVolumeUpperMold: Volume,
        shotVolumePerCycleCasting: Volume,
        compressionFactor: Rate,
    ): Volume = (moldingBoxVolumeUpperMold - shotVolumePerCycleCasting * BigDecimal(0.5)) * (BigDecimal.ONE + compressionFactor.res)

    fun sandVolumeLowerBox(
        moldingBoxVolumeLowerMold: Volume,
        shotVolumePerCycleCasting: Volume,
        compressionFactor: Rate,
    ): Volume = (moldingBoxVolumeLowerMold - shotVolumePerCycleCasting * BigDecimal(0.5)) * (BigDecimal.ONE + compressionFactor.res)

    fun sandVolumePerBox(
        moldingBoxVolume: Volume,
        shotVolumePerCycleCasting: Volume,
        compressionFactor: Rate,
    ): Volume = (moldingBoxVolume - shotVolumePerCycleCasting) * (BigDecimal.ONE + compressionFactor.res)

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun sandWeightUpperMold(
        sandVolumeUpperBox: Volume,
        density: Density,
    ): QuantityUnit = QuantityUnit(sandVolumeUpperBox.inCm * density.inKgPerCm)

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun sandWeightLowerMold(
        sandVolumeLowerBox: Volume,
        density: Density,
    ): QuantityUnit = QuantityUnit(sandVolumeLowerBox.inCm * density.inKgPerCm)

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun sandWeightPerMold(
        sandVolumePerBox: Volume,
        density: Density,
    ): QuantityUnit = QuantityUnit(sandVolumePerBox.inCm * density.inKgPerCm)

    fun netWeightPerPart(
        sandWeightPerMold: QuantityUnit,
        partsPerCycle: QuantityUnit,
    ) = sandWeightPerMold / partsPerCycle

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun scrapWeightPerPart(): QuantityUnit? = null

    @SpecialLink("MaterialCasting", "deployedWeightPerCycle")
    fun deployedWeightPerCycle(): QuantityUnit? = null

    @ReadOnly
    fun castToSandRatio(
        sandWeightPerMold: QuantityUnit,
        deployedWeightPerCycle: QuantityUnit,
    ): Rate = Rate((deployedWeightPerCycle / sandWeightPerMold).res)

    /****** RawMaterials override **********/

    val materialCostMode: MaterialCostMode = MaterialCostMode.SELL_NOTHING

    fun materialWastePrice(pricePerUnit: Money): Money = Money(0.2.toBigDecimal() * pricePerUnit.res)

    fun materialRecyclingPrice(pricePerUnit: Money): Money = Money(0.4.toBigDecimal() * pricePerUnit.res)

    fun quantityForExport(deployedWeightPerPart: QuantityUnit): QuantityUnit = deployedWeightPerPart

    @EntityCreation(Entities.PRICE_COMPONENT)
    @FreezeImplementation
    fun createDefaultPriceComponent(): PriceComponentEntity {
        return createEntity(
            "Price component",
            Entities.PRICE_COMPONENT,
            clazz = PriceComponentEntity::class,
            fields =
                mapOf(
                    PriceComponentEntity::displayDesignation.name to Text("Depositing cost (5%, 0.025€/kg)"),
                    PriceComponentEntity::priceComponentPricePerUnit.name to Money(BigDecimal(0.00125)),
                ),
        )
    }

    @Input
    fun materialPriceType(): MaterialPriceType = MaterialPriceType.COMPOSED_PRICE

    fun materialEmissionType(): MaterialEmissionType = MaterialEmissionType.COMPOSED_EMISSION

    @Input
    fun materialBaseCO2(): Emission = Emission(0.0, EmissionUnits.KILOGRAM_CO2E)

    @EntityCreation(Entities.COMPONENT_MATERIAL)
    fun createComponentMaterial(): Flux<ManufacturingEntity> =
        createEntityWithNewMasterdata(
            name = "Reconditioned sand",
            entityType = Entities.COMPONENT_MATERIAL,
            clazz = ComponentMaterial::class.simpleName!!,
            fields =
                mapOf(
                    "ratio" to Rate(0.95),
                    "cO2PerUnit" to Emission(0.0, unit = EmissionUnits.KILOGRAM_CO2E),
                ),
            masterDataKey = "Reconditioned sand-RAW_MATERIAL_SAND",
            masterDataType = MasterDataType.RAW_MATERIAL_SAND,
        ).concatWith(
            createEntityWithNewMasterdata(
                name = "New molding sand",
                entityType = Entities.COMPONENT_MATERIAL,
                clazz = ComponentMaterial::class.simpleName!!,
                fields =
                    mapOf(
                        "pricePerUnit" to Money(0.04),
                        "density" to Density(1.4, DensityUnits.GRAM_PER_CCM),
                        "ratio" to Rate(0.05),
                        "cO2PerUnit" to Emission(0.0, unit = EmissionUnits.KILOGRAM_CO2E),
                    ),
                masterDataKey = "New molding sand-RAW_MATERIAL_SAND",
                masterDataType = MasterDataType.RAW_MATERIAL_SAND,
            ),
        ).concatWith(
            createEntityWithNewMasterdata(
                name = "Furan resin binder",
                entityType = Entities.COMPONENT_MATERIAL,
                clazz = ComponentMaterial::class.simpleName!!,
                fields =
                    mapOf(
                        "pricePerUnit" to Money(2.0),
                        "density" to Density(1.1, DensityUnits.GRAM_PER_CCM),
                        "ratio" to Rate(0.03),
                        "cO2PerUnit" to Emission(0.0, unit = EmissionUnits.KILOGRAM_CO2E),
                    ),
                masterDataKey = "Furan resin binder-RAW_MATERIAL_SAND",
                masterDataType = MasterDataType.RAW_MATERIAL_SAND,
            ),
        ).concatWith(
            createEntityWithNewMasterdata(
                name = "Furan resin hardener",
                entityType = Entities.COMPONENT_MATERIAL,
                clazz = ComponentMaterial::class.simpleName!!,
                fields =
                    mapOf(
                        "pricePerUnit" to Money(4.0),
                        "density" to Density(1.1, DensityUnits.GRAM_PER_CCM),
                        "ratio" to Rate(0.03),
                        "cO2PerUnit" to Emission(0.0, unit = EmissionUnits.KILOGRAM_CO2E),
                    ),
                masterDataKey = "Furan resin hardener-RAW_MATERIAL_SAND",
                masterDataType = MasterDataType.RAW_MATERIAL_SAND,
            ),
        )

    fun materialView(): MaterialViewConfig = MaterialViewConfig.MATERIAL_SAND_COMPOSITION_LARGE_CASTING
}
