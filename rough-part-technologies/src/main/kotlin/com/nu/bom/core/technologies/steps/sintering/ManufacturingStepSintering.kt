package com.nu.bom.core.technologies.steps.sintering

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DisableEntityCreationInModularizedEntity
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.ExpectedParents
import com.nu.bom.core.manufacturing.annotations.ExternalDependency
import com.nu.bom.core.manufacturing.annotations.FieldIndex
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Modularized
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.Selectable
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.annotations.StaticDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.SubLabel
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.SingleGroupManufacturingStep
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.ManufacturingStepType
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.enums.StaticUnitOverride
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.technologies.lookups.sinterTemplateLookupReader
import com.nu.bom.core.technologies.lookups.sinteringSystemSelectionReader
import com.nu.bom.core.technologies.manufacturings.sint.ManufacturingSintering
import com.nu.bom.core.technologies.manufacturings.sint.material.SinteredMaterial
import com.nu.bom.core.technologies.service.ShapeLookupReaderService
import com.nu.bom.core.technologies.steps.sintering.systemparameter.SystemParameterSintering
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.math.BigDecimal
import java.math.RoundingMode
import kotlin.math.sqrt

@EntityType(Entities.MANUFACTURING_STEP)
@Modularized(
    technologies = [Model.SINT],
    parents = [
        ExpectedParents(model = Model.SINT, type = Entities.MANUFACTURING, klass = ManufacturingSintering::class),
        ExpectedParents(model = Model.SINT, type = Entities.PROCESSED_MATERIAL, klass = SinteredMaterial::class),
    ],
    dimensions = [Dimension.Selection.NUMBER],
    userCreatable = true,
)
class ManufacturingStepSintering(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = SingleGroupManufacturingStep(name)

    @Input
    @SubLabel
    @ReadOnly
    @Selectable(value = "manufacturingStepType", getDisplayNameFromPathQuery = true)
    fun manufacturingStepType(): Text = Text(ManufacturingStepType.DEBURRING.name)

    @StaticDenominatorUnit(StaticUnitOverride.YEAR)
    fun internalCallsPerYear(
        lookupReaderService: ShapeLookupReaderService,
        // Todo: Might need to be changed to average volume. See COST-50914
        peakUsableProductionVolumePerYear: QuantityUnit,
        shapeId: Text,
    ): Mono<Num> =
        lookupReaderService.getSintShapeData(calculationContext!!.accessCheck, shapeId).map {
            Num(
                max(
                    6.toBigDecimal(),
                    (peakUsableProductionVolumePerYear.res / it.manufacturingLotSizePerYear).setScale(0, RoundingMode.UP),
                ),
            )
        }

    fun callsPerYear(
        // Todo: Might need to be changed to average volume. See COST-50914
        peakUsableProductionVolumePerYear: QuantityUnit,
        internalCallsPerYear: Num,
    ) = Num(
        max(
            BigDecimal.ONE,
            min(peakUsableProductionVolumePerYear.res, internalCallsPerYear.res.setScale(0, RoundingMode.UP)),
        ),
    )

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 20)
    @Path("/api/shapes/autocomplete?tech=SINT")
    fun shapeId(): Text? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 10)
    fun netWeightPerPart(): QuantityUnit? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 40)
    fun partLength(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 50)
    fun partWidth(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 60)
    fun partOuterDiameter(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 70)
    @ReadOnly
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    fun rapidCooling(): Bool? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    @StaticDenominatorUnit(StaticUnitOverride.YEAR)
    fun peakUsableProductionVolumePerYear(): QuantityUnit? = null

    @Input
    @SpecialLink("SystemParameterSintering", "furnaceBeltWidth")
    fun furnaceBeltWidth(): Length? = null

    @Input
    @SpecialLink("SystemParameterSintering", "furnaceBeltSpeed")
    fun furnaceBeltSpeed(): Length? = null

    @Input
    @SpecialLink("SystemParameterSintering", "capacityPerHour")
    @StaticDenominatorUnit(StaticUnitOverride.HOUR)
    fun capacityPerHour(): Num? = null

    @Input
    fun utilizationRate(): Rate = Rate(0.9.toBigDecimal())

    fun scrapRate(): Rate = Rate(0.toBigDecimal())

    @Input
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 5)
    fun numberOfPiecesInHeight(): Pieces = Pieces.ONE

    fun inputGroup(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
    ) = lookupReaderService.getInputGroupFromShapeLookup(calculationContext!!.accessCheck, shapeId, Model.SINT)

    fun triangular(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
    ): Mono<Bool> =
        lookupReaderService.getSintShapeData(calculationContext!!.accessCheck, shapeId).map {
            Bool(it.triangular)
        }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 3)
    @SummaryView(SummaryView.PROCESS, 40, "needsCeramicPlateSintering")
    fun needsCeramicPlate(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
    ): Mono<SelectableBoolean> =
        lookupReaderService.getSintShapeData(calculationContext!!.accessCheck, shapeId).map {
            it.needsCeramicPlate
        }

    @Input
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 4)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun plateLengthWidth() = Length(0.2, LengthUnits.METER)

    @Input
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 2)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun partGap() = Length(4.0, LengthUnits.MILLIMETER)

    fun partsPerCycle(internalPartsPerCycle: QuantityUnit) = internalPartsPerCycle

    @SummaryView(SummaryView.PROCESS, 60, "partsPerCycleSintering")
    fun internalPartsPerCycle(
        capacityPerHour: Num,
        netWeightPerPart: QuantityUnit,
        needsCeramicPlate: SelectableBoolean,
        inputGroup: Text,
        partLength: Length,
        partWidth: Length,
        partOuterDiameter: Length,
        furnaceBeltWidth: Length,
        furnaceBeltSpeed: Length,
        plateLengthWidth: Length,
        partGap: Length,
        numberOfPiecesInHeight: Pieces,
        triangular: Bool,
    ): QuantityUnit {
        fun calculatePiecesInSurface(
            relevantLengthA: Length,
            relevantLengthB: Length,
        ) = ((relevantLengthA - partGap.times(2.0)) / (relevantLengthB + partGap)).inMeter

        val a = calculatePiecesInSurface(if (needsCeramicPlate.toBoolean()) plateLengthWidth else furnaceBeltWidth, partWidth)
        val b = calculatePiecesInSurface(if (needsCeramicPlate.toBoolean()) plateLengthWidth else furnaceBeltSpeed, partLength)

        val c =
            when (needsCeramicPlate.toBoolean()) {
                true -> calculatePiecesInSurface(plateLengthWidth, partOuterDiameter).setScale(0, RoundingMode.DOWN)
                false -> calculatePiecesInSurface(furnaceBeltSpeed, partOuterDiameter)
            }

        val diag =
            ((if (needsCeramicPlate.toBoolean()) plateLengthWidth else furnaceBeltWidth).times(sqrt(2.0)) / (partOuterDiameter + partGap))
                .inMeter
                .setScale(0, RoundingMode.DOWN)

        val e =
            when (needsCeramicPlate.toBoolean()) {
                true ->
                    (furnaceBeltWidth / plateLengthWidth).inMeter.setScale(0, RoundingMode.DOWN) *
                        furnaceBeltSpeed.inMeter * (BigDecimal.ONE / plateLengthWidth.inMeter)
                false -> BigDecimal.ONE
            }

        val x = Pieces((capacityPerHour / netWeightPerPart).res.setScale(0, RoundingMode.DOWN)).res
        val y =
            Pieces(
                when {
                    inputGroup.res == "cuboid" && triangular.isFalse() -> (a * b).setScale(0, RoundingMode.DOWN)
                    inputGroup.res == "cuboid" && triangular.isTrue() ->
                        ((a + (a - BigDecimal.ONE)) * b).setScale(0, RoundingMode.DOWN)
                    else ->
                        c * diag -
                            if (diag == 2.toBigDecimal()) BigDecimal.ZERO else (diag * 0.5.toBigDecimal()).setScale(0, RoundingMode.DOWN)
                },
            ).res.times(e).times(numberOfPiecesInHeight.res).setScale(0, RoundingMode.DOWN)

        return QuantityUnit(min(x, y))
    }

    // Todo: Do not overwrite the lot size here or also the callsPerYear (See COST-50914)
    fun peakLotSize(
        // Todo: Might need to be changed to average volume. See COST-50914
        peakUsableProductionVolumePerYear: QuantityUnit,
        callsPerYear: Num,
    ): QuantityUnit =
        when (peakUsableProductionVolumePerYear.res < 10_000.toBigDecimal()) {
            true -> QuantityUnit(min(4.toBigDecimal(), (peakUsableProductionVolumePerYear / callsPerYear).res))
            false -> QuantityUnit((peakUsableProductionVolumePerYear / callsPerYear).res)
        }

    @SummaryView(SummaryView.PROCESS, 50, "configurationSintering")
    fun templateName(rapidCooling: Bool): Mono<Text> =
        services
            .getLookupTable(
                "ManufacturingStepSintering_Templates",
                sinterTemplateLookupReader,
            ).filter {
                val diffRapidCooling =
                    (it.templateName.contains("0_RapidCooling") && rapidCooling.res) or
                        (it.templateName.contains("0_NoRapidCooling") && !rapidCooling.res)
                val diffrapidCool = it.rapidCool == rapidCooling.res

                diffRapidCooling && diffrapidCool
            }.filter {
                it.defalt
            }.single()
            .map { Text(it.templateName) }

    fun system(templateName: Text): Mono<Text> =
        services
            .getLookupTable(
                "ManufacturingStepSintering_SystemSelection",
                sinteringSystemSelectionReader,
            ).filter {
                it.templateName == templateName.res
            }.single()
            .map { Text(it.system) }

    @EntityCreation(Entities.SYSTEM_PARAMETER)
    fun createSystemParameter(system: Text): Mono<SystemParameterSintering> =
        createSystemParameter(
            masterData = MasterDataType.SYSTEM_PARAMETER_SINTERING,
            clazz = SystemParameterSintering::class,
            system = system.res,
            year = calculationContext?.year!!,
            location = "Global",
        )

    @EntityCreation(Entities.MACHINE)
    fun createMachines(templateName: Text): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Machine",
            location = "Global",
        )

    @EntityCreation(Entities.LABOR)
    fun createLabor(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Labor",
            location = locationName.res,
        )

    @EntityCreation(Entities.SETUP)
    fun createSetup(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Setup",
            location = locationName.res,
        )

    @DisableEntityCreationInModularizedEntity
    @EntityCreation(Entities.TOOL)
    fun createTools(templateName: Text): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Tools",
            location = "Global",
        )

    @SummaryView(SummaryView.PROCESS, 80, fieldName = "manufacturingScrapRateSintering")
    fun manufacturingScrapRateSummaryView(manufacturingScrapRate: Rate): Rate = manufacturingScrapRate
}
