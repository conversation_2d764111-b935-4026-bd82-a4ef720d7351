package com.nu.bom.core.technologies.manufacturings.dca.material

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityLinkField
import com.nu.bom.core.manufacturing.annotations.EntityLinkProvider
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.ExpectedParents
import com.nu.bom.core.manufacturing.annotations.ExternalDependency
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Modularized
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.RequiredFor
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.RawMaterialCastingAlloy
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.DynamicQuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.EntityRef
import com.nu.bom.core.manufacturing.fieldTypes.MaterialCostMode
import com.nu.bom.core.manufacturing.fieldTypes.MaterialViewConfig
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.model.NET_WEIGHT_PER_PART_PREDICTION_SERVICE
import com.nu.bom.core.technologies.steps.dca.ManufacturingStepDieCasting
import reactor.core.publisher.Mono

@EntityType(Entities.MATERIAL)
@Modularized(
    technologies = [Model.DCA],
    parents = [
        ExpectedParents(model = Model.DCA, type = Entities.MANUFACTURING),
        ExpectedParents(model = Model.DCA, type = Entities.PROCESSED_MATERIAL),
    ],
    dimensions = [Dimension.Selection.NUMBER],
    userCreatable = true,
)
class MaterialCasting(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = RawMaterialCastingAlloy(name)

    @EntityLinkProvider(["ManufacturingStepDieCasting"], entityClasses = [ManufacturingStepDieCasting::class])
    @ExternalDependency(section = ExternalDependency.SECTIONS.STEP)
    @Path("/api/link{bomPath}{branchPath}?entityClasses=ManufacturingStepDieCasting")
    fun linkedStep(): EntityRef? = null

    @Input
    @ObjectView(ObjectView.MATERIAL, 20)
    fun reuseOfScrap(): Bool = Bool(true)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 7)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun netWeightPerPart(): QuantityUnit? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun scrapWeightPerPart(): QuantityUnit? = null

    @Input
    @EntityLinkField(providerField = "linkedStep", "partPerCycleFamilyToolingForMaterial")
    @ExternalDependency(section = ExternalDependency.SECTIONS.STEP)
    fun partPerCycleFamilyToolingForMaterial(): DynamicQuantityUnit? = null

    @Input
    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 6)
    fun partsPerCycle(partPerCycleFamilyToolingForMaterial: DynamicQuantityUnit) = partPerCycleFamilyToolingForMaterial

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun shapeTechnologyGroup(): Text? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    @DefaultUnit(DefaultUnit.QCM)
    fun projectedAreaPerPart(): Area? = null

    // BCT
    fun projectedAreaPerCycle(): Area? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @ObjectView(ObjectView.MATERIAL, 1)
    @ReadOnly
    fun materialName(): Text? = null

    @Input
    @ObjectView(ObjectView.MATERIAL, 21)
    fun sprueAndOverflowLossRate(hasMagnesium: Bool): Rate =
        if (hasMagnesium.isTrue()) {
            Rate(0.03)
        } else {
            Rate(0.01)
        }

    @Input
    @ObjectView(ObjectView.MATERIAL, 12)
    fun irretrievableLossRate(hasMagnesium: Bool): Rate =
        if (hasMagnesium.isTrue()) {
            Rate(0.07)
        } else {
            Rate(0.03)
        }

    @ObjectView(ObjectView.MATERIAL, 9)
    @SummaryView(SummaryView.PROCESS, 50)
    fun sprueAndOverflowRatePerCycle(
        shapeTechnologyGroup: Text,
        netWeightPerPart: QuantityUnit,
        partsPerCycle: QuantityUnit,
        projectedAreaPerPart: Area,
        materialName: Text?,
        displayDesignation: Text,
    ): Mono<Rate> =
        services
            .predictNum(
                "dca",
                "SprueRate_DieCasting",
                mapOf(
                    "Shape_L1" to shapeTechnologyGroup,
                    NET_WEIGHT_PER_PART_PREDICTION_SERVICE to netWeightPerPart,
                    "PartsPerCycle" to partsPerCycle,
                    "ProjectedAreaPerPart" to projectedAreaPerPart,
                    // Hack needed since nu-cost service expects old material names
                    "MaterialName" to (materialName?.let { Text(it.res.substringBefore("-RAW")) } ?: displayDesignation),
                ),
            ).map {
                Rate(it)
            }

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 8)
    fun netWeightPerCycle(
        netWeightPerPart: QuantityUnit,
        partsPerCycle: QuantityUnit,
    ): QuantityUnit = netWeightPerPart * partsPerCycle

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 11)
    fun sprueAndOverflowWeightPerCycle(
        sprueAndOverflowWeightPerPart: QuantityUnit,
        partsPerCycle: QuantityUnit,
    ): QuantityUnit = sprueAndOverflowWeightPerPart * partsPerCycle

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 10)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun sprueAndOverflowWeightPerPart(
        netWeightPerPart: QuantityUnit,
        sprueAndOverflowRatePerCycle: Rate,
    ): QuantityUnit = netWeightPerPart * sprueAndOverflowRatePerCycle

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 16)
    fun shotWeightPerCycle(
        netWeightPerCycle: QuantityUnit,
        sprueAndOverflowWeightPerCycle: QuantityUnit,
    ): QuantityUnit = netWeightPerCycle + sprueAndOverflowWeightPerCycle

    @ObjectView(ObjectView.MATERIAL, 15)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun shotWeightPerPart(
        shotWeightPerCycle: QuantityUnit,
        partsPerCycle: QuantityUnit,
    ): QuantityUnit = shotWeightPerCycle / partsPerCycle

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 20)
    fun materialInCirculationPerCycle(
        sprueAndOverflowWeightPerCycle: QuantityUnit,
        sprueAndOverflowLossWeightPerCycle: QuantityUnit,
    ): QuantityUnit = sprueAndOverflowWeightPerCycle - sprueAndOverflowLossWeightPerCycle

    @ObjectView(ObjectView.MATERIAL, 19)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun materialInCirculationPerPart(
        materialInCirculationPerCycle: QuantityUnit,
        partsPerCycle: QuantityUnit,
    ): QuantityUnit = materialInCirculationPerCycle / partsPerCycle

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 23)
    fun sprueAndOverflowLossWeightPerCycle(
        sprueAndOverflowWeightPerCycle: QuantityUnit,
        sprueAndOverflowLossRate: Rate,
    ): QuantityUnit = sprueAndOverflowWeightPerCycle * sprueAndOverflowLossRate

    @ObjectView(ObjectView.MATERIAL, 22)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun sprueAndOverflowLossWeightPerPart(
        sprueAndOverflowLossWeightPerCycle: QuantityUnit,
        partsPerCycle: QuantityUnit,
    ): QuantityUnit = sprueAndOverflowLossWeightPerCycle / partsPerCycle

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun negativeSprueAndOverflowLossWeightPerPart(sprueAndOverflowLossWeightPerPart: QuantityUnit): QuantityUnit =
        sprueAndOverflowLossWeightPerPart * (-1.0)

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 14)
    fun irretrievableLossWeightPerCycle(
        shotWeightPerCycle: QuantityUnit,
        irretrievableLossRate: Rate,
    ): QuantityUnit = shotWeightPerCycle * irretrievableLossRate

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 13)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun irretrievableLossWeightPerPart(
        irretrievableLossWeightPerCycle: QuantityUnit,
        partsPerCycle: QuantityUnit,
    ): QuantityUnit = irretrievableLossWeightPerCycle / partsPerCycle

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 25)
    fun deployedWeightPerCycle(
        netWeightPerCycle: QuantityUnit,
        irretrievableLossWeightPerCycle: QuantityUnit,
        sprueAndOverflowLossWeightPerCycle: QuantityUnit,
    ): QuantityUnit = netWeightPerCycle + irretrievableLossWeightPerCycle + sprueAndOverflowLossWeightPerCycle

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 24)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun deployedWeightPerPart(
        netWeightPerPart: QuantityUnit,
        sprueAndOverflowLossWeightPerPart: QuantityUnit,
        irretrievableLossWeightPerPart: QuantityUnit,
    ): QuantityUnit = netWeightPerPart + sprueAndOverflowLossWeightPerPart + irretrievableLossWeightPerPart

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun irretrievableScrapPerPart(
        sprueAndOverflowLossWeightPerPart: QuantityUnit,
        irretrievableLossWeightPerPart: QuantityUnit,
    ): QuantityUnit = sprueAndOverflowLossWeightPerPart + irretrievableLossWeightPerPart

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun retrievableScrapPerPart(
        sprueAndOverflowWeightPerPart: QuantityUnit,
        negativeSprueAndOverflowLossWeightPerPart: QuantityUnit,
    ): QuantityUnit = sprueAndOverflowWeightPerPart + negativeSprueAndOverflowLossWeightPerPart

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 18)
    fun meltingShotWeightPerCycle(
        shotWeightPerCycle: QuantityUnit,
        irretrievableLossWeightPerCycle: QuantityUnit,
    ): QuantityUnit = shotWeightPerCycle + irretrievableLossWeightPerCycle

    @ObjectView(ObjectView.MATERIAL, 17)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun meltingShotWeightPerPart(
        meltingShotWeightPerCycle: QuantityUnit,
        partsPerCycle: QuantityUnit,
    ): QuantityUnit = meltingShotWeightPerCycle / partsPerCycle

    fun materialView(): MaterialViewConfig = MaterialViewConfig.MATERIAL_CASTING

    /****** RawMaterials override **********/

    fun materialCostMode(): MaterialCostMode = MaterialCostMode.SELL_NOTHING

    fun materialWastePrice(pricePerUnit: Money): Money = Money(0.2.toBigDecimal() * pricePerUnit.res)

    fun materialRecyclingPrice(pricePerUnit: Money): Money = Money(0.4.toBigDecimal() * pricePerUnit.res)

    fun quantityForExport(deployedWeightPerPart: QuantityUnit): QuantityUnit = deployedWeightPerPart
}
