package com.nu.bom.core.technologies.steps.pboxfoldergluer

import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.SingleGroupManufacturingStep
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.ManufacturingStepType
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Speed
import com.nu.bom.core.manufacturing.fieldTypes.SpeedUnits
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.technologies.steps.pboxfoldergluer.systemparameter.SystemParameterPboxFolderGluer
import com.nu.bom.core.technologies.utils.PboxReaders
import reactor.core.publisher.Mono

@EntityType(Entities.MANUFACTURING_STEP)
@Suppress("unused")
class ManufacturingStepPboxFolderGluer(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = SingleGroupManufacturingStep(name)

    companion object {
        const val TEMPLATE_LOOKUP_NAME = "ManufacturingStepPboxFolderGluer_Templates"
    }

    // region Fields from MANUFACTURING

    @Parent(Entities.MANUFACTURING)
    fun key(): Text? = null

    @Parent(Entities.MANUFACTURING)
    fun number(): Text? = null

    @Parent(Entities.MANUFACTURING)
    fun unfoldedBoxLength(): Length? = null

    @Parent(Entities.MANUFACTURING)
    fun unfoldedBoxWidthW2(): Length? = null

    // endregion

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 20)
    fun processingSpeed() = Speed(300.0, SpeedUnits.M_PER_MIN)

    fun manufacturingStepType(): Text = Text(ManufacturingStepType.ASSEMBLY_ADHESIVE_BONDING.name)

    fun partsPerCycle() = QuantityUnit(1.0)

    fun utilizationRate() = Rate(0.886)

    fun setupScrapParts() = QuantityUnit(51.0)

    fun templateName(processingSpeed: Speed): Mono<Text> =
        PboxReaders.maxProcessingSpeedTemplateNameImpl(
            processingSpeed,
            this::processingSpeed.name,
            TEMPLATE_LOOKUP_NAME,
            services,
        )

    @EntityCreation(Entities.MACHINE)
    fun createMachines(templateName: Text) = createMachinesFromTemplate(templateName)

    @EntityCreation(Entities.LABOR)
    fun createLabor(
        templateName: Text,
        locationName: Text,
    ) = createLaborFromTemplate(templateName, locationName)

    @EntityCreation(Entities.SYSTEM_PARAMETER)
    fun createSystemParameter(templateName: Text) =
        createSystemParameter(
            masterData = MasterDataType.SYSTEM_PARAMETER_PBOX_FOLDER_GLUER,
            clazz = SystemParameterPboxFolderGluer::class,
            system = templateName.res,
            year = calculationContext?.year!!,
            location = "Global",
        )
}
