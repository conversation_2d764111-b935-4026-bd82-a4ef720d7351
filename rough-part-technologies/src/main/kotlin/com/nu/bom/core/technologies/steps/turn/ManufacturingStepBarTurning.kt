package com.nu.bom.core.technologies.steps.turn

import com.nu.bom.core.machining.model.GenericCutOffTool
import com.nu.bom.core.machining.model.MachiningCutOffCycleTimeStep
import com.nu.bom.core.manufacturing.annotations.CalculationPreview
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.ListOfStrings
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TurningExchange
import com.nu.bom.core.manufacturing.fieldTypes.TurningStep
import com.nu.bom.core.technologies.steps.turn.systemparameter.SystemParameterBarTurning
import com.nu.bom.core.utils.allMinBy
import com.nu.bom.core.utils.exceptionOnEmpty
import reactor.core.publisher.Mono

@EntityType(Entities.MANUFACTURING_STEP)
class ManufacturingStepBarTurning(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = ManufacturingStepTurning(name)

    val step = TurningStep.soft

    @Input
    val scrapRate: Rate = Rate(0.002.toBigDecimal())

    fun partingOffWidth(turningExchange: TurningExchange): Length {
        // TODO. How? --jgr/21/02

        val cutOffToolIdentifier =
            turningExchange.response.cycle_time_step_groups
                .flatMap { it.cycle_time_steps }
                .filterIsInstance<MachiningCutOffCycleTimeStep<*>>()
                .map { it.getMaybeSpecificToolId() }

        if (cutOffToolIdentifier.isEmpty()) {
            throw IllegalArgumentException("no Cutoff Tool found")
        }
        if (cutOffToolIdentifier.size > 1) {
            throw IllegalArgumentException("too many cut off tools found")
        }

        val genericTool =
            turningExchange.request
                .genericAndSpecificTool(
                    cutOffToolIdentifier.first(),
                    turningExchange.response.generated_tools,
                ).first as GenericCutOffTool
        return Length(genericTool.cutting_width_mm, LengthUnits.MILLIMETER)
    }

    @EntityCreation(Entities.SYSTEM_PARAMETER)
    fun createSystemParameter(selectedTemplateName: Text): Mono<ManufacturingEntity> =
        createSystemParameter(
            masterData = MasterDataType.SYSTEM_PARAMETER_BAR_TURNING,
            clazz = SystemParameterBarTurning::class,
            system = selectedTemplateName.res,
            year = calculationContext?.year!!,
            location = "Global",
        )

    @EntityCreation(Entities.MATERIAL)
    fun createMaterial(
        @Parent(Entities.PROCESSED_MATERIAL)
        materialName: Text,
        @Parent(Entities.PROCESSED_MATERIAL)
        materialClass: Text,
    ): Mono<ManufacturingEntity> =
        createEntityWithMasterdata(
            name = "MaterialTurningBart",
            entityType = Entities.MATERIAL,
            clazz = materialClass.res,
            masterDataType = MasterDataType.RAW_MATERIAL_BAR,
            masterDataKey = materialName.res,
            fields = mapOf("readOnlyBarInnerDiameterBehaviour" to Bool(true)),
        )

    @SummaryView(SummaryView.PROCESS, 20, fieldName = "cycleTimeSoftTurning")
    @CalculationPreview(4, "cycleTimeSoftTurning")
    fun cycleTimeSummaryView(cycleTime: CycleTime) = cycleTime

    @SummaryView(SummaryView.PROCESS, 30, fieldName = "manufacturingScrapRateSoftTurning")
    fun manufacturingScrapRateSummaryView(manufacturingScrapRate: Rate): Rate = manufacturingScrapRate

    private class TemplateLookupEntry(
        val templateName: String,
        val maxLength: Length,
        val maxBarDiameter: Length,
        val maxRevolutionsPerMin: Int,
        val investBase: Int,
    )

    private val templateLookupReader: (row: List<String>) -> TemplateLookupEntry = {
        TemplateLookupEntry(
            templateName = it[0],
            maxLength = Length(it[1].toBigDecimal(), LengthUnits.METER),
            maxBarDiameter = Length(it[2].toBigDecimal(), LengthUnits.METER),
            maxRevolutionsPerMin = it[3].toInt(),
            investBase = it[4].toInt(),
        )
    }

    fun templateNames(
        @Parent(Entities.MANUFACTURING) lengthForLookup: Length,
        @Parent(Entities.MANUFACTURING) diameterForLookup: Length,
    ): Mono<ListOfStrings> =
        services
            .getLookupTable("ManufacturingStepBarTurning_Templates", templateLookupReader)
            .filter {
                val diffBarLength = lengthForLookup.res <= it.maxLength.res
                val diffBarDiameter = diameterForLookup.res <= it.maxBarDiameter.res
                diffBarLength && diffBarDiameter
            }.collectList()
            .mapNotNull { capableMachines ->
                val machinesClosestToInput =
                    capableMachines.allMinBy { currentMin, item ->
                        when {
                            currentMin.investBase > item.investBase -> 1
                            currentMin.investBase < item.investBase -> -1
                            currentMin.maxBarDiameter.res > item.maxBarDiameter.res -> 1
                            currentMin.maxBarDiameter.res < item.maxBarDiameter.res -> -1
                            currentMin.maxLength.res > item.maxLength.res -> 1
                            currentMin.maxLength.res < item.maxLength.res -> -1
                            else -> 0
                        }
                    }
                val fastestMachineClosestToInputOrNull = machinesClosestToInput.maxByOrNull { it.maxRevolutionsPerMin }?.templateName
                fastestMachineClosestToInputOrNull
            }.exceptionOnEmpty {
                NoSuchElementException(
                    "No bar turning machine could be selected for length: $lengthForLookup, diameter: $diameterForLookup",
                )
            }.map { fastestMachineClosestToInput ->
                ListOfStrings(listOfNotNull(fastestMachineClosestToInput))
            }
}
