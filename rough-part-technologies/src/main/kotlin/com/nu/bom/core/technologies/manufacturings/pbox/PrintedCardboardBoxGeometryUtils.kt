package com.nu.bom.core.technologies.manufacturings.pbox

import com.nu.bom.core.exception.readable.LimitType
import com.nu.bom.core.exception.readable.NumericInputExceedsLimitException
import com.nu.bom.core.manufacturing.fieldTypes.CardboardBoxType
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.smf.model.FriedelNestingCoord
import com.nu.bom.core.smf.model.FriedelNestingCurve
import com.nu.bom.core.smf.model.FriedelNestingLine
import com.nu.bom.core.technologies.utils.FieldValidationUtils.checkGreaterThanZero
import com.tset.bom.clients.geometry.Point2D
import java.math.BigDecimal
import java.math.RoundingMode

object PrintedCardboardBoxGeometryUtils {
    fun createPolygon(
        boxType: CardboardBoxType,
        w1: Double,
        w2: Double,
        length: Double,
        flapSize: Double,
    ): FriedelNestingCurve {
        val points =
            when (boxType.res) {
                CardboardBoxType.Selection.LP -> {
                    val extents = convertToExtentsLPBox(w1, w2, length, flapSize)
                    createSimplePolygonPointsLPBox(extents, flapSize)
                }
                CardboardBoxType.Selection.MA -> {
                    val extents = convertToExtentsMABox(w1, w2, length, flapSize)
                    createSimplePolygonPointsMABox(extents, flapSize)
                }
            }

        return createFriedelCurve(points)
    }

    private fun createFriedelCurve(points: List<Point2D<Double>>): FriedelNestingCurve {
        val lines = mutableListOf<FriedelNestingLine>()

        for (i in points.indices) {
            val start = points[i]
            val end = points[(i + 1) % points.size]
            val friedelStart = FriedelNestingCoord(start.x, start.y)
            val friedelEnd = FriedelNestingCoord(end.x, end.y)
            lines.add(FriedelNestingLine(friedelStart, friedelEnd))
        }

        return FriedelNestingCurve(lines)
    }

    private data class Extents3D(
        val x: Double,
        val y: Double,
        val z: Double,
    )

    private fun checkCardboardBoxInputRequirements(
        w1: Double,
        w2: Double,
        length: Double,
        flapSize: Double,
        computedZ: Double,
    ) {
        checkGreaterThanZero(w1, ManufacturingPrintedCardboardBox::unfoldedBoxWidthW1.name)
        checkGreaterThanZero(w2, ManufacturingPrintedCardboardBox::unfoldedBoxWidthW2.name)
        checkGreaterThanZero(length, ManufacturingPrintedCardboardBox::unfoldedBoxLength.name)

        if (flapSize < 0.0) {
            throw NumericInputExceedsLimitException(
                "flapSize", // currently not a field, but could be in the future.
                LimitType.LESS,
                BigDecimal.ZERO,
            )
        }

        if (w1 >= w2) {
            throw NumericInputExceedsLimitException(
                ManufacturingPrintedCardboardBox::unfoldedBoxWidthW1.name,
                LimitType.GREATER_EQ,
                ManufacturingPrintedCardboardBox::unfoldedBoxWidthW2.name,
            )
        }
        if (length <= computedZ) {
            throw NumericInputExceedsLimitException(
                ManufacturingPrintedCardboardBox::unfoldedBoxLength.name,
                LimitType.LESS_EQ,
                computedZ.toBigDecimal().setScale(2, RoundingMode.HALF_UP),
                LengthUnits.MILLIMETER.name,
            )
        }
    }

    private fun convertToExtentsLPBox(
        w1: Double,
        w2: Double,
        length: Double,
        flapSize: Double,
    ): Extents3D {
        val y = w1
        val zWithFlap = (w2 - w1) / 2
        val z = zWithFlap - flapSize
        val computedZ = (2 * z + flapSize)
        val x = (length - computedZ) / 2

        checkCardboardBoxInputRequirements(w1, w2, length, flapSize, computedZ)
        if (zWithFlap <= flapSize) {
            throw NumericInputExceedsLimitException(
                ManufacturingPrintedCardboardBox::differenceBetweenW2andW1.name,
                LimitType.LESS_EQ,
                ManufacturingPrintedCardboardBox.DEFAULT_FLAP_SIZE.times(2.0).inMillimeter,
                LengthUnits.MILLIMETER.name,
            )
        }
        return Extents3D(x, y, z)
    }

    private fun convertToExtentsMABox(
        w1: Double,
        w2: Double,
        length: Double,
        flapSize: Double,
    ): Extents3D {
        val y = w1
        val z = (w2 - w1) / 2
        val computedZ = (2 * z + flapSize)
        val x = (length - computedZ) / 2

        checkCardboardBoxInputRequirements(w1, w2, length, flapSize, computedZ)
        return Extents3D(x, y, z)
    }

    private fun createSimplePolygonPointsLPBox(
        extents3D: Extents3D,
        flapSize: Double,
    ): List<Point2D<Double>> {
        val bottom =
            listOf(
                Point2D(0.0, 0.0),
                Point2D(extents3D.x, 0.0),
                Point2D(extents3D.x, -flapSize),
                Point2D(extents3D.x + extents3D.z, -flapSize),
                Point2D(extents3D.x + extents3D.z, -(extents3D.z + flapSize)),
                Point2D(2 * extents3D.x + extents3D.z, -(extents3D.z + flapSize)),
                Point2D(2 * extents3D.x + extents3D.z, -flapSize),
                Point2D(2 * extents3D.x + 2 * extents3D.z, -flapSize),
                Point2D(2 * extents3D.x + 2 * extents3D.z, 0.0),
                Point2D(2 * extents3D.x + 2 * extents3D.z + flapSize, 0.0),
            )

        val top =
            bottom
                .map {
                    Point2D(it.x, -it.y + extents3D.y)
                }.reversed()

        return bottom + top
    }

    private fun createSimplePolygonPointsMABox(
        extents3D: Extents3D,
        flapSize: Double,
    ): List<Point2D<Double>> {
        val bottom =
            listOf(
                Point2D(0.0, 0.0),
                Point2D(flapSize, 0.0),
                Point2D(flapSize, -extents3D.z),
                Point2D(flapSize + extents3D.z, -extents3D.z),
                Point2D(flapSize + extents3D.z + extents3D.x, -extents3D.z),
                Point2D(flapSize + 2.0 * extents3D.z + extents3D.x, -extents3D.z),
                Point2D(flapSize + 2.0 * extents3D.z + 2.0 * extents3D.x, -extents3D.z),
                Point2D(flapSize + 2.0 * extents3D.z + 2.0 * extents3D.x, 0.0),
            )

        val top =
            bottom
                .map {
                    Point2D(it.x, -it.y + extents3D.y)
                }.reversed()

        return bottom + top
    }
}
