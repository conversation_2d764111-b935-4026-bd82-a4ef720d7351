package com.nu.bom.core.technologies.manufacturings.waxmodel.material

import com.nu.bom.core.manufacturing.annotations.CalculationPreview
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.RawMaterialWax
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.DynamicQuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.MaterialCostMode
import com.nu.bom.core.manufacturing.fieldTypes.MaterialViewConfig
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text

@EntityType(Entities.MATERIAL)
class MaterialWaxModel(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = RawMaterialWax(name)

    @Input
    fun reuseOfScrap(): Bool = Bool(false)

    @Input
    @SpecialLink("ManufacturingStepInjection", "partPerCycleForMaterial")
    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 140)
    fun partsPerCycle(): DynamicQuantityUnit? = null

    @Input
    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 150)
    @Parent(Entities.PROCESSED_MATERIAL)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun netWeightPerPart(): QuantityUnit? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun scrapWeightPerPart(): QuantityUnit? = null

    @Input
    @Path("/api/model/waxmodel/materialName")
    fun materialName(): Text? = null

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 160)
    fun netWeightPerCycle(
        netWeightPerPart: QuantityUnit,
        partsPerCycle: DynamicQuantityUnit,
    ): QuantityUnit = netWeightPerPart * partsPerCycle

    @ObjectView(ObjectView.MATERIAL, 170)
    @SummaryView(SummaryView.PROCESS, 10)
    @CalculationPreview(4)
    fun sprueRate(): Rate = Rate(0.1)

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 180)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun sprueWeightPerPart(
        netWeightPerPart: QuantityUnit,
        sprueRate: Rate,
    ): QuantityUnit = netWeightPerPart * sprueRate

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 190)
    fun sprueWeightPerCycle(
        partsPerCycle: QuantityUnit,
        sprueWeightPerPart: QuantityUnit,
    ): QuantityUnit = sprueWeightPerPart * partsPerCycle

    @ObjectView(ObjectView.MATERIAL, 200)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun shotWeightPerPart(
        netWeightPerPart: QuantityUnit,
        sprueWeightPerPart: QuantityUnit,
    ): QuantityUnit = netWeightPerPart + sprueWeightPerPart

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 210)
    @SummaryView(SummaryView.PROCESS, 20)
    fun shotWeightPerCycle(
        shotWeightPerPart: QuantityUnit,
        partsPerCycle: QuantityUnit,
    ): QuantityUnit = shotWeightPerPart * partsPerCycle

    @Input
    @ObjectView(ObjectView.MATERIAL, 220)
    fun recyclingRate() = Rate(0.9.toBigDecimal())

    @ObjectView(ObjectView.MATERIAL, 230)
    @ReadOnly
    fun retrievableScrapPerPart(
        sprueWeightPerPart: QuantityUnit,
        recyclingRate: Rate,
    ): QuantityUnit = sprueWeightPerPart * recyclingRate

    @ReadOnly
    fun irretrievableScrapPerPart(sprueLossWeight: QuantityUnit): QuantityUnit = sprueLossWeight

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 250)
    fun sprueLossWeight(
        sprueWeightPerPart: QuantityUnit,
        recyclingRate: Rate,
    ): QuantityUnit = sprueWeightPerPart * (1.toBigDecimal() - recyclingRate.res)

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 270)
    @SummaryView(SummaryView.PART, 200)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun deployedWeightPerPart(
        netWeightPerPart: QuantityUnit,
        sprueLossWeight: QuantityUnit,
    ): QuantityUnit = netWeightPerPart + sprueLossWeight

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 280)
    fun deployedWeightPerCycle(
        deployedWeightPerPart: QuantityUnit,
        partsPerCycle: QuantityUnit,
    ): QuantityUnit = deployedWeightPerPart * partsPerCycle

    /****** RawMaterials override **********/

    fun materialCostMode(): MaterialCostMode = MaterialCostMode.SELL_IRRETRIEVABLE

    fun materialWastePrice(pricePerUnit: Money): Money = Money(0.2.toBigDecimal() * pricePerUnit.res)

    fun materialRecyclingPrice(pricePerUnit: Money): Money = Money(0.4.toBigDecimal() * pricePerUnit.res)

    fun quantityForExport(deployedWeightPerPart: QuantityUnit): QuantityUnit = deployedWeightPerPart

    fun materialView(): MaterialViewConfig = MaterialViewConfig.MATERIAL_WAX_MODEL
}
