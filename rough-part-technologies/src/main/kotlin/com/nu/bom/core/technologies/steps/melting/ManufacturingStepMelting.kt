package com.nu.bom.core.technologies.steps.melting

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.Children
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DisableEntityCreationInModularizedEntity
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityLinkField
import com.nu.bom.core.manufacturing.annotations.EntityLinkProvider
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.ExpectedParents
import com.nu.bom.core.manufacturing.annotations.ExternalDependency
import com.nu.bom.core.manufacturing.annotations.FieldIndex
import com.nu.bom.core.manufacturing.annotations.FieldName
import com.nu.bom.core.manufacturing.annotations.FilteredChildren
import com.nu.bom.core.manufacturing.annotations.IgnoreForOverwrittenState
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Modularized
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.Selectable
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.annotations.StaticDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.SubLabel
import com.nu.bom.core.manufacturing.defaults.NoMoney
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.SingleGroupManufacturingStep
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_COUNTRY_ID_ENTITY_NAME
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.ManufacturingStepType
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.enums.StaticUnitOverride
import com.nu.bom.core.manufacturing.fieldTypes.CastingAlloyMaterialGroup
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.DynamicQuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.EntityRef
import com.nu.bom.core.manufacturing.fieldTypes.FieldResultStar
import com.nu.bom.core.manufacturing.fieldTypes.FurnaceType
import com.nu.bom.core.manufacturing.fieldTypes.MaterialSubstances
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.technologies.lookups.meltingTemplatesReader
import com.nu.bom.core.technologies.lookups.meltingWeightPerHourRateReader
import com.nu.bom.core.technologies.manufacturings.dca.material.MaterialCasting
import com.nu.bom.core.technologies.manufacturings.prec.material.MaterialPrecisionCasting
import com.nu.bom.core.technologies.manufacturings.vacuumprec.material.MaterialVacuumPrecisionCasting
import com.nu.bom.core.technologies.steps.melting.systemparameter.SystemParameterMelting
import com.nu.bom.core.utils.CostFactorUtils.getCostFactorForCurrentStepId
import com.nu.bom.core.utils.annotations.TsetSuppress
import org.bson.types.ObjectId
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono
import java.math.BigDecimal
import java.math.RoundingMode

@EntityType(Entities.MANUFACTURING_STEP)
@Modularized(
    technologies = [Model.DCA],
    parents = [
        ExpectedParents(model = Model.DCA, type = Entities.MANUFACTURING),
        ExpectedParents(model = Model.DCA, type = Entities.PROCESSED_MATERIAL),
    ],
    dimensions = [Dimension.Selection.NUMBER],
    userCreatable = true,
)
class ManufacturingStepMelting(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = SingleGroupManufacturingStep(name)

    @Input
    @SubLabel
    @ReadOnly
    @Selectable(value = "manufacturingStepType", getDisplayNameFromPathQuery = true)
    fun manufacturingStepType(): Text = Text(ManufacturingStepType.CASTING.name)

    @ExternalDependency(ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 0)
    @EntityLinkProvider(["MaterialCasting"], entityClasses = [MaterialCasting::class])
    @Path("/api/link{bomPath}{branchPath}?entityClasses=MaterialCasting")
    @IgnoreForOverwrittenState
    fun linkedMaterial(): EntityRef? = null

    // region Fields from MANUFACTURING

    @Parent(Entities.MANUFACTURING)
    fun key(): Text? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    @TsetSuppress("tset:engine:field-override-configuration-value")
    fun technology(): Text? = null

    // wizard override input
    @Input
    @Parent(Entities.MANUFACTURING)
    @Default(NoMoney::class)
    fun materialPriceOverride(): Money? = null

    // endregion

    // region Fields from MATERIAL

    @Input
    @EntityLinkField("linkedMaterial", "meltingShotWeightPerPart")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 10)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun meltingShotWeightPerPart(): QuantityUnit? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterial", "materialGroup")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 20)
    fun materialGroup(): CastingAlloyMaterialGroup? = null

    // endregion

    // region Fields from PROCESSED_MATERIAL

    @Parent(Entities.PROCESSED_MATERIAL)
    fun materialName(): Text? = null

    @Parent(Entities.PROCESSED_MATERIAL)
    fun materialClass(): Text? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 10)
    fun netWeightPerPart(): QuantityUnit? = null

    // endregion

    fun technologyForModularization(
        technology: Text?,
        technologyModel: Text?,
    ): Text? = technology ?: technologyModel?.let { Model.fromEntity(it.res)?.name?.let { name -> Text(name) } }

    @Input
    fun scrapRate(): Rate = Rate(BigDecimal.ZERO)

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 20)
    @StaticDenominatorUnit(StaticUnitOverride.HOUR)
    fun meltingCapacityPerHour(
        @Children(Entities.SYSTEM_PARAMETER)
        meltingWeightPerHour: DynamicQuantityUnit,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_COUNTRY_INFO,
            nameFilter = TSET_COST_COUNTRY_ID_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        ) value: Map<ObjectId, Text>,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_COUNTRY_INFO,
            nameFilter = TSET_COST_COUNTRY_ID_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        @FieldName("location")
        costFactorLocations: Map<ObjectId, Text>,
        location: Text,
        @Children(Entities.SYSTEM_PARAMETER)
        meltingType: FurnaceType,
        materialGroup: CastingAlloyMaterialGroup,
    ): Mono<DynamicQuantityUnit> {
        val costFactorForCurrentStepId = getCostFactorForCurrentStepId(costFactorLocations, location)
        return when (meltingType) {
            FurnaceType.GAS_FURNACE -> {
                val countryId =
                    requireNotNull(value[costFactorForCurrentStepId]) {
                        "CountryId for $costFactorForCurrentStepId not found"
                    }
                services
                    .getLookupTable(
                        "ManufacturingStepMelting_MeltingWeightPerHourRate",
                        meltingWeightPerHourRateReader,
                    ).filter {
                        countryId.res == it.countryId
                    }.single()
                    .map {
                        val baseCapacity = meltingWeightPerHour * it.meltingWeightPerHourRate
                        if (materialGroup.isMagnesiumBasedAlloy) baseCapacity.div(1.4) else baseCapacity
                    }
            }

            else -> meltingWeightPerHour.toMono()
        }
    }

    fun partsPerCycle(internalPartsPerCycle: QuantityUnit) = internalPartsPerCycle

    fun internalPartsPerCycle(
        meltingCapacityPerHour: QuantityUnit,
        meltingShotWeightPerPart: QuantityUnit,
    ): QuantityUnit {
        val ppc = (meltingCapacityPerHour.res / meltingShotWeightPerPart.res).setScale(0, RoundingMode.DOWN)
        return QuantityUnit(max(BigDecimal.ONE, ppc))
    }

    @Input
    fun utilizationRate(): Rate = Rate(0.85.toBigDecimal())

    fun furnaceType(
        materialGroup: CastingAlloyMaterialGroup,
        technologyForModularization: Text,
    ): FurnaceType =
        when {
            technologyForModularization.res == "DCA" ||
                technologyForModularization.res == "CHILL" ||
                (technologyForModularization.res == "SAND" && materialGroup.alloyContainsAluminum) -> FurnaceType.GAS_FURNACE
            technologyForModularization.res == "PREC" -> FurnaceType.INDUCTION_FURNACE
            materialGroup.isGJSorGJV || materialGroup.isSteelAlloy || materialGroup.isHeatResistantAlloy -> FurnaceType.TANDEM_FURNACE
            else -> FurnaceType.CUPOLA_FURNACE
        }

    fun templateName(
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_COUNTRY_INFO,
            nameFilter = TSET_COST_COUNTRY_ID_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        ) value: Map<ObjectId, Text>,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_COUNTRY_INFO,
            nameFilter = TSET_COST_COUNTRY_ID_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        @FieldName("location")
        costFactorLocations: Map<ObjectId, Text>,
        location: Text,
        furnaceType: FurnaceType,
        materialGroup: CastingAlloyMaterialGroup,
    ): Mono<Text> {
        val costFactorForCurrentStepId = getCostFactorForCurrentStepId(costFactorLocations, location)
        val countryId =
            requireNotNull(value[costFactorForCurrentStepId]) {
                "CountryId for $costFactorForCurrentStepId not found"
            }
        val isChinaOrIndia = countryId.res == "10" || countryId.res == "18"
        return services
            .getLookupTable("ManufacturingStepMelting_Templates", meltingTemplatesReader)
            .filter {
                val diffFurnace = furnaceType == FurnaceType.valueOf(it.furnaceType)
                val diffChinaOrIndia = isChinaOrIndia == it.lowCostCountry
                val diffMagnesium = materialGroup.isMagnesiumBasedAlloy == it.magnesium
                diffChinaOrIndia && diffFurnace && diffMagnesium
            }.single()
            .map { Text(it.templateName) }
    }

    // region Entity creation

    @EntityCreation(Entities.MACHINE)
    fun createMachines(templateName: Text): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Machine",
            location = "Global",
        )

    @EntityCreation(Entities.LABOR)
    fun createLabor(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Labor",
            location = locationName.res,
        )

    @EntityCreation(Entities.SETUP)
    fun createSetup(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> = createSetupFromTemplate(templateName, locationName)

    @DisableEntityCreationInModularizedEntity
    @EntityCreation(Entities.TOOL)
    fun createTools(templateName: Text): Flux<ManufacturingEntity> = createToolFromTemplate(templateName)

    @EntityCreation(Entities.CONSUMABLE)
    fun createConsumables(templateName: Text): Flux<ManufacturingEntity> = createConsumableFromTemplate(templateName)

    @EntityCreation(Entities.SYSTEM_PARAMETER)
    fun createSystemParameter(templateName: Text): Mono<SystemParameterMelting> =
        createSystemParameter(
            masterData = MasterDataType.SYSTEM_PARAMETER_MELTING,
            clazz = SystemParameterMelting::class,
            system = templateName.res,
            year = calculationContext?.year!!,
            location = "Global",
        )

    @DisableEntityCreationInModularizedEntity
    @EntityCreation(Entities.MATERIAL)
    fun createMaterial(
        materialName: Text?,
        materialClass: Text?,
        materialPriceOverride: Money?,
    ): Mono<ManufacturingEntity> {
        if (materialClass == null || materialName == null) return Mono.empty()
        // this is a hack to enable PREC and VPREC material purchase price editability - a general solution requires wizard rework
        return if (materialPriceOverride != null && materialPriceOverride.res > BigDecimal.ZERO) {
            when (materialClass.res) {
                MaterialPrecisionCasting::class.qualifiedName!!,
                MaterialVacuumPrecisionCasting::class.qualifiedName!!,
                -> mapOf("materialBasePrice" to materialPriceOverride)
                else -> emptyMap()
            }
        } else {
            emptyMap<String, FieldResultStar>()
        }.let { overwrites ->
            createEntityWithNewMasterdata(
                name = "MaterialCasting",
                entityType = Entities.MATERIAL,
                clazz = materialClass.res,
                masterDataType = MasterDataType.RAW_MATERIAL_CASTING_ALLOY,
                masterDataKey = materialName.res,
                overwrites = overwrites,
            )
        }
    }

    // endregion

    // region BCT-only fields

    @Input
    @SpecialLink("MaterialCasting", "materialSubstances")
    fun materialSubstances(): MaterialSubstances? = null

    @Input
    @SpecialLink("SystemParameterMelting", "meltingWeightPerHour")
    @StaticDenominatorUnit(StaticUnitOverride.HOUR)
    fun meltingWeightPerHour(): QuantityUnit? = null

    // endregion
}
