package com.nu.bom.core.technologies.steps.inj

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.Children
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DisableEntityCreationInModularizedEntity
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityLinkField
import com.nu.bom.core.manufacturing.annotations.EntityLinkProvider
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.ExpectedParents
import com.nu.bom.core.manufacturing.annotations.ExternalDependency
import com.nu.bom.core.manufacturing.annotations.FieldIndex
import com.nu.bom.core.manufacturing.annotations.FieldName
import com.nu.bom.core.manufacturing.annotations.FilteredChildren
import com.nu.bom.core.manufacturing.annotations.IgnoreForOverwrittenState
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Modularized
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.RequiredFor
import com.nu.bom.core.manufacturing.annotations.Selectable
import com.nu.bom.core.manufacturing.annotations.SubLabel
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.SingleGroupManufacturingStep
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_COUNTRY_ID_ENTITY_NAME
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.ManufacturingStepType
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.Diffusivity
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.DynamicQuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.EntityRef
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Temperature
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.Volume
import com.nu.bom.core.manufacturing.fieldTypes.VolumeUnits
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.nu.bom.core.technologies.manufacturings.inj.ManufacturingInjection2.Companion.DEFAULT_INJECTION_NUMBER_OF_ADDITIONAL_FAMILY_PARTS
import com.nu.bom.core.technologies.manufacturings.inj.material.MaterialPlastic2
import com.nu.bom.core.technologies.steps.inj.systemparameter.SystemParameterInjection
import com.nu.bom.core.technologies.steps.inj.tool.MoldInjectionTool2
import com.nu.bom.core.utils.CostFactorUtils.getCostFactorForCurrentStepId
import org.bson.types.ObjectId
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.math.BigDecimal
import kotlin.math.ln
import kotlin.math.pow

@EntityType(Entities.MANUFACTURING_STEP)
@Modularized(
    technologies = [Model.INJ2],
    parents = [
        ExpectedParents(model = Model.INJ2, type = Entities.MANUFACTURING),
        ExpectedParents(model = Model.INJ2, type = Entities.PROCESSED_MATERIAL),
    ],
    dimensions = [Dimension.Selection.NUMBER],
    userCreatable = false,
)
class ManufacturingStepInjection2(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = SingleGroupManufacturingStep(name)

    @ExternalDependency(ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 0)
    @EntityLinkProvider(["MaterialPlastic2"], entityClasses = [MaterialPlastic2::class])
    @Path("/api/link{bomPath}{branchPath}?entityClasses=MaterialPlastic2")
    @IgnoreForOverwrittenState
    fun linkedMaterial(): EntityRef? = null

    @Input
    @SubLabel
    @ReadOnly
    @Selectable(value = "manufacturingStepType", getDisplayNameFromPathQuery = true)
    fun manufacturingStepType(): Text = Text(ManufacturingStepType.CHEMICALS_INJECTION_MOLDING.name)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 20)
    @Path("/api/shapes/autocomplete?tech=INJ2")
    fun shapeId(): Text? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 10)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    fun netWeightPerPart(): QuantityUnit? = null

    @Parent(Entities.PROCESSED_MATERIAL)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 50)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    fun maxWallThickness(): Length? = null

    fun countryId(
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_COUNTRY_INFO,
            nameFilter = TSET_COST_COUNTRY_ID_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        ) value: Map<ObjectId, Text>,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_COUNTRY_INFO,
            nameFilter = TSET_COST_COUNTRY_ID_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        @FieldName("location")
        costFactorLocations: Map<ObjectId, Text>,
        location: Text,
    ): Text? {
        val costFactorForCurrentStepId = getCostFactorForCurrentStepId(costFactorLocations, location)
        return Text(value[costFactorForCurrentStepId]?.res.toString())
    }

    @EntityLinkField(providerField = "linkedMaterial", "shotWeightPerCycle")
    fun shotWeightPerCycle(): DynamicQuantityUnit? = DynamicQuantityUnit(BigDecimal.ZERO, WeightUnits.KILOGRAM, null, defaultValue = true)

    @Parent(Entities.PROCESSED_MATERIAL)
    fun materialName(): Text? = null

    @Parent(Entities.PROCESSED_MATERIAL)
    fun materialClass(): Text? = null

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 38)
    @DefaultUnit(DefaultUnit.CCM)
    fun partVolume(
        netWeightPerPart: DynamicQuantityUnit,
        density: Density,
    ): DynamicQuantityUnit {
        val quantityUnit = netWeightPerPart.denominatorUnit
        val resultIn =
            when {
                quantityUnit != null -> netWeightPerPart.resultIn(WeightUnits.KILOGRAM, quantityUnit)
                netWeightPerPart.numeratorUnit == null -> netWeightPerPart.res
                else -> netWeightPerPart.resultIn(WeightUnits.KILOGRAM)
            }
        return DynamicQuantityUnit(
            resultIn / density.inKgPerCm,
            VolumeUnits.CM,
            quantityUnit,
        )
    }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 50)
    @DefaultUnit(DefaultUnit.SECOND)
    fun coolingTimeWithoutAdditions(
        maxWallThickness: Length,
        thermalDiffusivity: Diffusivity,
        injectingTemperature: Temperature,
        moldTemperature: Temperature,
        moldSeparationTemperature: Temperature,
    ): Time {
        val conversionTerm = maxWallThickness.inMeter.pow(2) / (Math.PI.pow(2).toBigDecimal() * thermalDiffusivity.inQmPerSecond)

        val logTerm =
            (injectingTemperature.inCelsius - moldTemperature.inCelsius) * 8.0.toBigDecimal() /
                ((moldSeparationTemperature.inCelsius - moldTemperature.inCelsius) * Math.PI.pow(2).toBigDecimal())

        return Time(conversionTerm * ln(logTerm.toDouble()).toBigDecimal(), TimeUnits.SECOND)
    }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 13)
    @DefaultUnit(DefaultUnit.SECOND)
    fun injectionTime(
        partVolume: DynamicQuantityUnit,
        @Children(Entities.SYSTEM_PARAMETER) injectionVolumePerTime: Volume,
        partsPerCycle: DynamicQuantityUnit,
    ): Time {
        val injectionVolumePerTimeQ = DynamicQuantityUnit(injectionVolumePerTime.inCm, VolumeUnits.CM, TimeUnits.SECOND)
        val partsPerCycleQ = DynamicQuantityUnit(partsPerCycle.res, partVolume.denominatorUnit, null)
        val seconds =
            when (partVolume.res) {
                BigDecimal.ZERO -> 2.04.toBigDecimal()
                else -> {
                    val times: DynamicQuantityUnit = partVolume.times(partsPerCycleQ)
                    times.div(injectionVolumePerTimeQ).resultIn(TimeUnits.SECOND)
                }
            }

        return Time(seconds, TimeUnits.SECOND)
    }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 80)
    @DefaultUnit(DefaultUnit.SECOND)
    fun plasticizingTime(
        shotWeightPerCycle: DynamicQuantityUnit,
        @Children(Entities.SYSTEM_PARAMETER)
        plasticisingMassPerTime: Weight,
    ): Time = Time((shotWeightPerCycle.resultIn(WeightUnits.KILOGRAM) / plasticisingMassPerTime.inKg), TimeUnits.SECOND)

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 17)
    @DefaultUnit(DefaultUnit.SECOND)
    fun coolingTimeIncludingAdditions(
        coolingTimeWithoutAdditions: Time,
        injectionTime: Time,
        plasticizingTime: Time,
    ): Time = coolingTimeWithoutAdditions + injectionTime + plasticizingTime

    @EntityCreation(Entities.MACHINE)
    fun createMachines(templateName: Text): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Machine",
            location = "Global",
        )

    @EntityCreation(Entities.LABOR)
    fun createLabor(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Labor",
            location = locationName.res,
        )

    @EntityCreation(Entities.SETUP)
    fun createSetup(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Setup",
            location = locationName.res,
        )

    @DisableEntityCreationInModularizedEntity
    @EntityCreation(Entities.TOOL)
    fun createTools(): Mono<ManufacturingEntity> =
        createEntityWithMasterdata(
            name = "Injection mold",
            entityType = Entities.TOOL,
            clazz = MoldInjectionTool2::class.simpleName!!,
            masterDataKey = "Injection mold",
            masterDataType = MasterDataType.TOOL,
        )

    @DisableEntityCreationInModularizedEntity
    @EntityCreation(Entities.MATERIAL)
    fun createMaterial(
        materialName: Text?,
        materialClass: Text?,
    ): Mono<ManufacturingEntity> {
        if (materialClass == null || materialName == null) return Mono.empty()
        return createEntityWithNewMasterdata(
            name = "MaterialPlastic2",
            entityType = Entities.MATERIAL,
            clazz = materialClass.res,
            masterDataType = MasterDataType.RAW_MATERIAL_PLASTIC_GRANULATE,
            masterDataKey = materialName.res,
        )
    }

    @EntityCreation(Entities.SYSTEM_PARAMETER)
    fun createSystemParameter(templateName: Text): Mono<SystemParameterInjection> =
        createSystemParameter(
            masterData = MasterDataType.SYSTEM_PARAMETER_INJECTION,
            clazz = SystemParameterInjection::class,
            system = templateName.res,
            year = calculationContext?.year!!,
            location = "Global",
        )

    @Parent(Entities.MANUFACTURING)
    @ObjectView(ObjectView.PRODUCTION, 2)
    fun numberOfAdditionalFamilyParts() = DEFAULT_INJECTION_NUMBER_OF_ADDITIONAL_FAMILY_PARTS
}
