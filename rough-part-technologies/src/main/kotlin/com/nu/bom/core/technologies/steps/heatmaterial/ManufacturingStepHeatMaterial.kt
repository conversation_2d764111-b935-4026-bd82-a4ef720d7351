package com.nu.bom.core.technologies.steps.heatmaterial

import com.nu.bom.core.exception.readable.TemplateNotFoundCauseType
import com.nu.bom.core.exception.readable.TemplateNotFoundException
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DisableEntityCreationInModularizedEntity
import com.nu.bom.core.manufacturing.annotations.DontSortOptions
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.DynamicTranslationLabel
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityLinkField
import com.nu.bom.core.manufacturing.annotations.EntityLinkProvider
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.ExpectedParents
import com.nu.bom.core.manufacturing.annotations.ExternalDependency
import com.nu.bom.core.manufacturing.annotations.FieldIndex
import com.nu.bom.core.manufacturing.annotations.FieldName
import com.nu.bom.core.manufacturing.annotations.FilteredChildren
import com.nu.bom.core.manufacturing.annotations.IgnoreForOverwrittenState
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Modularized
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.Selectable
import com.nu.bom.core.manufacturing.annotations.SubLabel
import com.nu.bom.core.manufacturing.defaults.NoTolerance
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ManufacturingStep
import com.nu.bom.core.manufacturing.entities.MaterialCalculatorForging
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_ELECTRICITY_PRICE_ENTITY_NAME
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.ManufacturingStepType
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.CastingAlloyMaterialGroup
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.Diffusivity
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.DynamicQuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Energy
import com.nu.bom.core.manufacturing.fieldTypes.EnergyUnits
import com.nu.bom.core.manufacturing.fieldTypes.EntityRef
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Power
import com.nu.bom.core.manufacturing.fieldTypes.PowerUnits
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeMaterial
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeTolerancesDfor
import com.nu.bom.core.manufacturing.fieldTypes.Temperature
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.Volume
import com.nu.bom.core.manufacturing.fieldTypes.VolumeUnits
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.nu.bom.core.technologies.manufacturings.dfor.ManufacturingDieForging
import com.nu.bom.core.technologies.manufacturings.dfor.material.DieForgedMaterial
import com.nu.bom.core.technologies.manufacturings.dfor.material.MaterialForgingDfor
import com.nu.bom.core.technologies.steps.ManufacturingStepUtils
import com.nu.bom.core.technologies.steps.afor.ManufacturingStepDieForgingAlu
import com.nu.bom.core.technologies.steps.burfreeforg.ManufacturingStepBurrFreeDieForging
import com.nu.bom.core.technologies.steps.coldextrusion.ManufacturingStepColdExtrusion
import com.nu.bom.core.technologies.steps.crol.ManufacturingStepCotterKeyRolling
import com.nu.bom.core.technologies.steps.dfor.ManufacturingStepDieForging
import com.nu.bom.core.technologies.steps.heatmaterial.systemparameter.SystemParameterHeatMaterial
import com.nu.bom.core.technologies.steps.pfor.ManufacturingStepPrecisionForging
import com.nu.bom.core.technologies.steps.rotaryswaging.ManufacturingStepRotarySwaging
import com.nu.bom.core.technologies.steps.rrol.ManufacturingStepRingRolling
import com.nu.bom.core.utils.doOnEmpty
import com.nu.bom.core.utils.getResultWithSelectedAttributes
import org.bson.types.ObjectId
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.math.BigDecimal

@EntityType(Entities.MANUFACTURING_STEP)
@Modularized(
    technologies = [Model.DFOR],
    parents = [
        ExpectedParents(model = Model.DFOR, type = Entities.MANUFACTURING, klass = ManufacturingDieForging::class),
        ExpectedParents(model = Model.DFOR, type = Entities.PROCESSED_MATERIAL, klass = DieForgedMaterial::class),
    ],
    dimensions = [Dimension.Selection.NUMBER],
    userCreatable = true,
)
class ManufacturingStepHeatMaterial(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = ManufacturingStep(name)

    companion object {
        val ENERGY_DENSITY = 10.toBigDecimal()
        val THERMAL_EFFICIENCY = 0.41205.toBigDecimal()
    }

    fun technologyForModularization(
        @Parent(Entities.MANUFACTURING)
        technology: Text?,
        technologyModel: Text?,
    ): Text? = ManufacturingStepUtils.getTechnologyForModularizedEntities(technology, technologyModel, isolated)

    @Input
    @SubLabel
    @ReadOnly
    @Selectable(value = "manufacturingStepType", getDisplayNameFromPathQuery = true)
    fun manufacturingStepType(): Text = Text(ManufacturingStepType.FORGING.name)

    @Input
    fun isLinkedToParent(): Bool = Bool(!isolated)

    @Input
    @EntityLinkField(providerField = "linkedStep", "internalCycleTime")
    @ExternalDependency(section = ExternalDependency.SECTIONS.STEP)
    fun linkedStepInternalCycleTime(): CycleTime? = null

    // we do not have cycle time steps, but always take as long as our parent does
    // if for some reason we are not linked to a parent, that sucks, since we are not a GroupManufacturingStep,
    // see https://tsetplatform.atlassian.net/browse/COST-80836?focusedCommentId=52129
    @DynamicTranslationLabel("cycleTimeLabel")
    fun internalCycleTime(
        @Parent(Entities.MANUFACTURING_STEP) internalCycleTime: CycleTime?,
        linkedStepInternalCycleTime: CycleTime?,
    ): CycleTime? = if (isolated) linkedStepInternalCycleTime else internalCycleTime

    @Input
    fun internalPartsPerCycle() = QuantityUnit(BigDecimal.ONE)

    @Input
    fun utilizationRate(): Rate = Rate(0.85.toBigDecimal())

    fun scrapRate(): Rate = Rate(BigDecimal.ZERO)

    fun expectedMaterialClass(technologyForModularization: Text) =
        ManufacturingStepUtils.getExpectedMaterialClass(technologyForModularization)

    // region Fields from MATERIAL

    @ExternalDependency(ExternalDependency.SECTIONS.MATERIAL)
    @EntityLinkProvider(["MaterialForging"], entityClasses = [MaterialCalculatorForging::class, MaterialForgingDfor::class])
    @FieldIndex(index = 0)
    @Path("/api/link{bomPath}{branchPath}?entityClasses={field:expectedMaterialClass}")
    fun linkedMaterial(): EntityRef? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterial", fieldName = "weightBeforeScrapPerPart")
    @ExternalDependency(ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 10)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    @DefaultUnit(DefaultUnit.KILOGRAM)
    fun weightBeforeScrapPerPart(): DynamicQuantityUnit? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterial", "materialGroup")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 20)
    @DontSortOptions
    fun materialGroup(): CastingAlloyMaterialGroup? = null

    fun stepSubTypeMaterial(materialGroup: CastingAlloyMaterialGroup): StepSubTypeMaterial = StepSubTypeMaterial(materialGroup)

    @Input
    @EntityLinkField(providerField = "linkedMaterial", fieldName = "specificThermalCapacity")
    @ExternalDependency(ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 30)
    @DefaultUnit(DefaultUnit.QMM_PER_SECONDS)
    fun specificThermalCapacity(): Diffusivity? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterial", fieldName = "temperatureDifferenceForging")
    @ExternalDependency(ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 40)
    fun temperatureDifferenceForging(): Temperature? = null

    // endregion

    // region Fields from PROCESSED_MATERIAL

    @Input
    @Parent(Entities.MANUFACTURING)
    fun technology(): Text? = null

    @Input
    @EntityLinkProvider(
        entityRef = ["ManufacturingStepDieForging", "ManufacturingStepPrecisionForging", "ManufacturingStepBurrFreeDieForging"],
        entityClasses = [
            ManufacturingStep::class,
            ManufacturingStepDieForging::class,
            ManufacturingStepPrecisionForging::class,
            ManufacturingStepBurrFreeDieForging::class,
        ],
    )
    @ExternalDependency(section = ExternalDependency.SECTIONS.STEP)
    @Path("/api/link{bomPath}{branchPath}?entityClasses=ManufacturingStepDieForging")
    @FieldIndex(index = 10)
    @IgnoreForOverwrittenState
    fun linkedStep(): EntityRef? =
        getLinkedStep(
            listOf(
                ManufacturingStepDieForging::class,
                ManufacturingStepPrecisionForging::class,
                ManufacturingStepBurrFreeDieForging::class,
                // below is a workaround since @Default(NoTolerance::class) doesn't work with @EntityLinkField
                // tolerance now set at each step, will be reworked in future
                ManufacturingStepDieForgingAlu::class,
                ManufacturingStepRingRolling::class,
                ManufacturingStepColdExtrusion::class,
                ManufacturingStepRotarySwaging::class,
                ManufacturingStepCotterKeyRolling::class,
            ),
        )

    @Input
    @ExternalDependency(section = ExternalDependency.SECTIONS.STEP)
    @EntityLinkField(providerField = "linkedStep", "tolerance")
    @FieldIndex(index = 20)
    fun tolerance(): StepSubTypeTolerancesDfor? = null

    // endregion

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 10)
    @DefaultUnit(DefaultUnit.KILOWATT)
    fun powerNeeded(
        energyConsumptionPerHourNet: Energy,
        weightBeforeScrapPerPart: QuantityUnit,
        theoreticalCapacityBeforeUtilizationRatio: QuantityUnit,
        stepSubTypeMaterial: StepSubTypeMaterial,
    ): Power =
        when (stepSubTypeMaterial.res) {
            StepSubTypeMaterial.Selection.ALLOYED_STEEL ->
                Power(
                    energyConsumptionPerHourNet.inkWHour / THERMAL_EFFICIENCY,
                    PowerUnits.KILOWATT,
                )
            else -> Power(weightBeforeScrapPerPart.res * theoreticalCapacityBeforeUtilizationRatio.res, PowerUnits.WATT)
        }

    private class TemplateLookupEntry(
        val templateName: String,
        val power: Power,
        val burrFreeForging: SelectableBoolean,
        val oven: String,
    )

    private val machineTemplateLookupReader: (row: List<String>) -> TemplateLookupEntry = {
        TemplateLookupEntry(
            templateName = it[0],
            power = Power(it[1].toBigDecimal(), PowerUnits.WATT),
            burrFreeForging = SelectableBoolean.valueOf(it[2].toBoolean()),
            oven = it[3],
        )
    }

    fun internalSystemDownTime() = Time(BigDecimal.ZERO, TimeUnits.SECOND)

    fun templateName(
        powerNeeded: Power,
        @Default(NoTolerance::class)
        tolerance: StepSubTypeTolerancesDfor,
        stepSubTypeMaterial: StepSubTypeMaterial,
    ): Mono<Text> {
        val lookupName = "ManufacturingStepHeatMaterial_Templates"
        return services
            .getLookupTable(
                lookupName,
                machineTemplateLookupReader,
            ).filter {
                val diffPower =
                    when (stepSubTypeMaterial) {
                        StepSubTypeMaterial.ALLOYED_STEEL -> it.power.res * 0.75.toBigDecimal() > powerNeeded.inWatt
                        StepSubTypeMaterial.ALUMINIUM_MAGNESIUM -> true
                        else -> false
                    }

                val diffTonPerHour =
                    when (stepSubTypeMaterial) {
                        StepSubTypeMaterial.ALUMINIUM_MAGNESIUM -> powerNeeded.inkW < it.power.inWatt
                        StepSubTypeMaterial.ALLOYED_STEEL -> true
                        else -> false
                    }

                val diffType =
                    when {
                        it.burrFreeForging == SelectableBoolean.TRUE && tolerance == StepSubTypeTolerancesDfor.BURRFREE_DFOR -> true
                        it.burrFreeForging == SelectableBoolean.FALSE && tolerance !== StepSubTypeTolerancesDfor.BURRFREE_DFOR -> true
                        else -> false
                    }
                val diffMaterial =
                    when {
                        it.oven == "GAS" && stepSubTypeMaterial == StepSubTypeMaterial.ALUMINIUM_MAGNESIUM -> true
                        it.oven == "INDUCTION" && stepSubTypeMaterial == StepSubTypeMaterial.ALLOYED_STEEL -> true
                        else -> false
                    }

                diffPower && diffTonPerHour && diffType && diffMaterial
            }.doOnEmpty {
                throw TemplateNotFoundException(
                    causeType = TemplateNotFoundCauseType.NOT_SPECIFIED_CAUSE,
                    lookupName = lookupName,
                    lookupInputs =
                        mapOf(
                            this::powerNeeded.name to powerNeeded,
                            DieForgedMaterial::tolerance.name to tolerance,
                            this::stepSubTypeMaterial.name to stepSubTypeMaterial,
                        ),
                )
            }.collectList()
            .mapNotNull { templateLookupEntries ->
                templateLookupEntries.minByOrNull { it.power.res }
            }.map {
                Text(it!!.templateName)
            }
    }

    fun throughputPerHour(
        weightBeforeScrapPerPart: QuantityUnit,
        theoreticalCapacityBeforeUtilizationRatio: QuantityUnit,
    ): Weight =
        Weight(
            weightBeforeScrapPerPart.res * theoreticalCapacityBeforeUtilizationRatio.res,
            WeightUnits.KILOGRAM,
        )

    fun energyConsumptionPerHourNet(
        throughputPerHour: Weight,
        specificThermalCapacity: Diffusivity,
        temperatureDifferenceForging: Temperature,
    ): Energy {
        val temperatureKelvin = temperatureDifferenceForging + 273.15.toBigDecimal()
        return Energy(
            specificThermalCapacity.inQmmPerSecond * temperatureKelvin.res * throughputPerHour.inKg / 1000.toBigDecimal(),
            EnergyUnits.KILOWATTHOUR,
        )
    }

    fun gasConsumptionInVolume(powerNeeded: Power) = Volume((powerNeeded.inkW / ENERGY_DENSITY), VolumeUnits.CM)

    fun heatingEnergyCostsElectricityPerKwh(
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_ELECTRICITY_PRICE,
            nameFilter = TSET_COST_ELECTRICITY_PRICE_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        ) value: Map<ObjectId, Money>?,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_ELECTRICITY_PRICE,
            nameFilter = TSET_COST_ELECTRICITY_PRICE_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        @FieldName("location")
        costFactorLocations: Map<ObjectId, Text>,
        location: Text,
    ): Money? {
        return getResultWithSelectedAttributes(value, costFactorLocations, location)
    }

    @EntityCreation(Entities.SYSTEM_PARAMETER)
    fun createSystemParameter(templateName: Text): Mono<SystemParameterHeatMaterial> =
        createSystemParameter(
            masterData = MasterDataType.SYSTEM_PARAMETER_HEAT_MATERIAL,
            clazz = SystemParameterHeatMaterial::class,
            system = templateName.res,
            year = calculationContext?.year!!,
            location = "Global",
        )

    @EntityCreation(Entities.MACHINE)
    fun createMachines(templateName: Text): Flux<ManufacturingEntity> = createMachinesFromTemplate(templateName)

    @EntityCreation(Entities.LABOR)
    fun createLabor(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> = createLaborFromTemplate(templateName, locationName)

    @EntityCreation(Entities.SETUP)
    fun createSetup(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> = createSetupFromTemplate(templateName, locationName)

    @DisableEntityCreationInModularizedEntity
    @EntityCreation(Entities.TOOL)
    fun createTools(templateName: Text): Flux<ManufacturingEntity> = createToolFromTemplate(templateName)
}
