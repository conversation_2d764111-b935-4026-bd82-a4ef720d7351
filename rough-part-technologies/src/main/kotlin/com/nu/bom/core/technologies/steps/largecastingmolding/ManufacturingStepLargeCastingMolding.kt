package com.nu.bom.core.technologies.steps.largecastingmolding

import com.nu.bom.core.exception.userException.TemplateNotFoundException
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.Selectable
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.annotations.SubLabel
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.SingleGroupManufacturingStep
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.ManufacturingStepType
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.technologies.lookups.largeCastingMoldingTemplatesReader
import com.nu.bom.core.technologies.lookups.sandShapeReader
import com.nu.bom.core.technologies.steps.largecastingmolding.cycletimestep.LargeCastingCycleTimeStepGroup
import com.nu.bom.core.technologies.steps.largecastingmolding.systemparameter.SystemParameterLargeCastingMolding
import com.nu.bom.core.utils.doOnEmpty
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.math.BigDecimal
import java.math.RoundingMode

@EntityType(Entities.MANUFACTURING_STEP)
class ManufacturingStepLargeCastingMolding(name: String) : ManufacturingEntity(name) {
    override val extends = SingleGroupManufacturingStep(name)

    @Input
    @SubLabel
    @ReadOnly
    @Selectable(value = "manufacturingStepType", getDisplayNameFromPathQuery = true)
    fun manufacturingStepType(): Text = Text(ManufacturingStepType.CASTING_SAND_CASTING.name)

    @Input
    fun scrapRate(): Rate = Rate(BigDecimal.ZERO)

    @Input
    fun utilizationRate(): Rate = Rate(0.85.toBigDecimal())

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun shapeId(): Text? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    fun partLength(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    fun partHeight(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    fun partWidth(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    fun partOuterDiameter(): Length? = null

    @Input
    @SpecialLink("SystemParameterLargeCastingMolding", "frameLength")
    fun frameLength(): Length? = null

    @Input
    @SpecialLink("SystemParameterLargeCastingMolding", "frameWidth")
    fun frameWidth(): Length? = null

    @Input
    @SpecialLink("SystemParameterLargeCastingMolding", "frameHeight")
    fun frameHeight(): Length? = null

    fun inputGroup(
        shapeId: Text,
        @Parent(Entities.PROCESSED_MATERIAL)
        technology: Text,
    ): Mono<Text> {
        return services.getLookupTable("SAND_Shape", sandShapeReader).filter {
            it.shapeId == shapeId.res && it.tech == technology.res
        }.single().map {
            Text(it.inputGroup)
        }
    }

    fun frameGap() = Length(0.05, LengthUnits.METER)

    fun internalPartsPerCycle(
        partsPerFrameLengthMaxPerLength: QuantityUnit,
        partsPerFrameLengthMinPerLength: QuantityUnit,
        partsPerFrameWidthPerWidth: QuantityUnit,
        partsPerFrameLengthMaxPerWidth: QuantityUnit,
        partsPerFrameLengthMinPerWidth: QuantityUnit,
        partsPerFrameWidthPerLength: QuantityUnit,
    ): QuantityUnit {
        val partsPerCycleCalc1 = partsPerFrameLengthMaxPerLength * partsPerFrameWidthPerWidth
        val partsPerCycleCalc2 = partsPerFrameLengthMinPerLength * partsPerFrameWidthPerWidth
        val partsPerCycleCalc3 = partsPerFrameLengthMaxPerWidth * partsPerFrameWidthPerLength
        val partsPerCycleCalc4 = partsPerFrameLengthMinPerWidth * partsPerFrameWidthPerLength

        val partsPerCycleSelected = (max(partsPerCycleCalc1.res, partsPerCycleCalc2.res, partsPerCycleCalc3.res, partsPerCycleCalc4.res))
        return QuantityUnit(partsPerCycleSelected)
    }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 10)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun frameLengthAdjusted(
        partsPerFrameLengthMaxPerLength: QuantityUnit,
        partsPerFrameLengthMinPerLength: QuantityUnit,
        partsPerFrameWidthPerWidth: QuantityUnit,
        partsPerFrameLengthMaxPerWidth: QuantityUnit,
        partsPerFrameLengthMinPerWidth: QuantityUnit,
        partsPerFrameWidthPerLength: QuantityUnit,
        @Parent(Entities.MANUFACTURING)
        newLength: Length,
        @Parent(Entities.MANUFACTURING)
        newWidth: Length,
    ): Length {
        val partsPerCycleCalc1 = partsPerFrameLengthMaxPerLength * partsPerFrameWidthPerWidth
        val partsPerCycleCalc2 = partsPerFrameLengthMinPerLength * partsPerFrameWidthPerWidth
        val partsPerCycleCalc3 = partsPerFrameLengthMaxPerWidth * partsPerFrameWidthPerLength
        val partsPerCycleCalc4 = partsPerFrameLengthMinPerWidth * partsPerFrameWidthPerLength

        val partsPerCycleSelected =
            QuantityUnit(max(partsPerCycleCalc1.res, partsPerCycleCalc2.res, partsPerCycleCalc3.res, partsPerCycleCalc4.res))

        val resultInMm =
            when (partsPerCycleSelected) {
                partsPerCycleCalc1 -> (newLength + 0.1).times(partsPerFrameLengthMaxPerLength).inMillimeter
                partsPerCycleCalc2 -> (newLength + 0.1).times(partsPerFrameLengthMinPerLength).inMillimeter
                partsPerCycleCalc3 -> (newWidth + 0.1).times(partsPerFrameLengthMaxPerWidth).inMillimeter
                partsPerCycleCalc4 -> (newWidth + 0.1).times(partsPerFrameLengthMinPerWidth).inMillimeter
                else -> BigDecimal.ZERO
            }

        val resRoundedToNearestHundredth = resultInMm.setScale(-2, RoundingMode.UP)
        return Length(resRoundedToNearestHundredth, LengthUnits.MILLIMETER)
    }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 20)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun frameWidthAdjusted(frameWidth: Length) = frameWidth

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 30)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun frameHeightAdjusted(
        @Parent(Entities.MANUFACTURING)
        newHeight: Length,
    ): Length {
        val adjustedInMm = max(BigDecimal(300.0), newHeight.plus(0.15).inMillimeter)
        val resRoundedToNearestHundredth = adjustedInMm.setScale(-2, RoundingMode.UP)
        return Length(resRoundedToNearestHundredth, LengthUnits.MILLIMETER)
    }

    fun partsPerFrameLengthMaxPerLength(
        @Parent(Entities.MANUFACTURING)
        newLength: Length,
        frameLengthMax: Length,
        frameGap: Length,
    ) = partsPerLength(frameLengthMax, frameGap, newLength)

    fun partsPerFrameLengthMinPerLength(
        @Parent(Entities.MANUFACTURING)
        newLength: Length,
        frameLengthMax: Length,
        frameGap: Length,
    ) = partsPerLength(frameLengthMax, frameGap, newLength)

    fun partsPerFrameWidthPerWidth(
        @Parent(Entities.MANUFACTURING)
        newWidth: Length,
        frameWidth: Length,
        frameGap: Length,
    ) = partsPerLength(frameWidth, frameGap, newWidth)

    fun partsPerFrameLengthMaxPerWidth(
        @Parent(Entities.MANUFACTURING)
        newWidth: Length,
        frameLengthMax: Length,
        frameGap: Length,
    ) = partsPerLength(frameLengthMax, frameGap, newWidth)

    fun partsPerFrameLengthMinPerWidth(
        @Parent(Entities.MANUFACTURING)
        newWidth: Length,
        frameLengthMin: Length,
        frameGap: Length,
    ) = partsPerLength(frameLengthMin, frameGap, newWidth)

    fun partsPerFrameWidthPerLength(
        @Parent(Entities.MANUFACTURING)
        newLength: Length,
        frameWidth: Length,
        frameGap: Length,
    ) = partsPerLength(frameWidth, frameGap, newLength)

    fun frameLengthMax(frameLength: Length) = frameLength * 1.25

    fun frameLengthMin(frameLength: Length) = frameLength * 0.75

    fun partsPerLength(
        frameLengthMax: Length,
        frameGap: Length,
        @Parent(Entities.MANUFACTURING)
        newLength: Length,
    ): QuantityUnit {
        return QuantityUnit(
            ((frameLengthMax.res - 2.toBigDecimal() * frameGap.res + 0.1.toBigDecimal()) / (newLength.res + 0.1.toBigDecimal())).setScale(
                0,
                RoundingMode.DOWN,
            ),
        )
    }

    fun template() = Text("ManufacturingStepLargeCastingMolding1_Templates")

    fun templateName(
        template: Text,
        @Parent(Entities.MANUFACTURING)
        newLength: Length,
        @Parent(Entities.MANUFACTURING)
        newWidth: Length,
        @Parent(Entities.MANUFACTURING)
        newHeight: Length,
        frameGap: Length,
    ): Mono<Text> {
        return services.getLookupTable(template.res, largeCastingMoldingTemplatesReader).filter {
            val frameLengthMax = it.frameLength * 1.25
            val frameLengthMin = it.frameLength * 0.75

            val partsPerFrameLengthMaxPerLength = partsPerLength(frameLengthMax, frameGap, newLength)
            val partsPerFrameLengthMinPerLength = partsPerLength(frameLengthMin, frameGap, newLength)
            val partsPerFrameWidthPerWidth = partsPerLength(it.frameWidth, frameGap, newWidth)
            val partsPerFrameLengthMaxPerWidth = partsPerLength(frameLengthMax, frameGap, newWidth)
            val partsPerFrameLengthMinPerWidth = partsPerLength(frameLengthMin, frameGap, newWidth)
            val partsPerFrameWidthPerLength = partsPerLength(it.frameWidth, frameGap, newLength)

            val partsPerCycleCalc1 = partsPerFrameLengthMaxPerLength * partsPerFrameWidthPerWidth
            val partsPerCycleCalc2 = partsPerFrameLengthMinPerLength * partsPerFrameWidthPerWidth
            val partsPerCycleCalc3 = partsPerFrameLengthMaxPerWidth * partsPerFrameWidthPerLength
            val partsPerCycleCalc4 = partsPerFrameLengthMinPerWidth * partsPerFrameWidthPerLength

            val partsPerCycleSelected = max(partsPerCycleCalc1.res, partsPerCycleCalc2.res, partsPerCycleCalc3.res, partsPerCycleCalc4.res)

            val diffPartsPerCycleSelected = partsPerCycleSelected > BigDecimal.ZERO
            val diffHeight = newHeight.res + BigDecimal(0.15) < it.frameHeight.res
            diffPartsPerCycleSelected && diffHeight
        }
            .doOnEmpty {
                throw TemplateNotFoundException(
                    lookupName = template.res,
                    lookupInputs =
                        mapOf(
                            "newLength" to newLength.toString(),
                            "newWidth" to newWidth.toString(),
                            "newHeight" to newHeight.toString(),
                            "frameGap" to frameGap.toString(),
                        ),
                )
            }
            .elementAt(0).map { Text(it.templateName) }
    }

    @EntityCreation(Entities.CYCLETIME_STEP_GROUP)
    fun createCycleTimeStepGroup() =
        createEntity(
            name = "Large casting",
            clazz = LargeCastingCycleTimeStepGroup::class,
            entityType = Entities.CYCLETIME_STEP_GROUP,
        )

    @EntityCreation(Entities.MACHINE)
    fun createMachines(templateName: Text): Flux<ManufacturingEntity> {
        return createEntitiesFromTemplate(
            name = templateName.res + "_Machine",
            location = "Global",
        )
    }

    @EntityCreation(Entities.LABOR)
    fun createLabor(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> {
        return createEntitiesFromTemplate(
            name = templateName.res + "_Labor",
            location = locationName.res,
        )
    }

    @EntityCreation(Entities.SETUP)
    fun createSetup(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> {
        return createEntitiesFromTemplate(
            name = templateName.res + "_Setup",
            location = locationName.res,
        )
    }

    @EntityCreation(Entities.TOOL)
    fun createTools(templateName: Text): Flux<ManufacturingEntity> {
        return createEntitiesFromTemplate(
            name = templateName.res + "_Tools",
            location = "Global",
        )
    }

    @EntityCreation(Entities.SYSTEM_PARAMETER)
    fun createSystemParameter(templateName: Text): Mono<SystemParameterLargeCastingMolding> {
        return createSystemParameter(
            masterData = MasterDataType.SYSTEM_PARAMETER_LARGE_CASTING_MOLDING,
            clazz = SystemParameterLargeCastingMolding::class,
            system = templateName.res,
            year = calculationContext?.year!!,
            location = "Global",
        )
    }
}
