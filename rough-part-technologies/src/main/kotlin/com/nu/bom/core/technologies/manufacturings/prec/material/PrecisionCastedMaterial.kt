package com.nu.bom.core.technologies.manufacturings.prec.material

import com.nu.bom.core.manufacturing.annotations.EntityProvider
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.OrderedEntityCreation
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ShapedMaterial
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeCleanness
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeHeatTreatmentPrec
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Volume
import com.nu.bom.core.manufacturing.fieldTypes.VolumeUnits
import com.nu.bom.core.technologies.behaviours.CleaningBehaviour
import com.nu.bom.core.technologies.manufacturings.vacuumprec.material.MaterialVacuumPrecisionCasting
import com.nu.bom.core.technologies.steps.casehardening.ManufacturingStepCaseHardeningTemperingPrec
import com.nu.bom.core.technologies.steps.ceramicdemolding.ManufacturingStepCeramicDemolding
import com.nu.bom.core.technologies.steps.ceramicmoldburning.ManufacturingStepCeramicMoldBurning
import com.nu.bom.core.technologies.steps.cutting.ManufacturingStepCutting
import com.nu.bom.core.technologies.steps.hardening.ManufacturingStepHardeningTemperingPrec
import com.nu.bom.core.technologies.steps.melting.ManufacturingStepMelting
import com.nu.bom.core.technologies.steps.normalizing.ManufacturingStepNormalizingPrec
import com.nu.bom.core.technologies.steps.precisioncasting.ManufacturingStepPrecisionCasting
import com.nu.bom.core.technologies.steps.precisionfettling.ManufacturingStepPrecisionFettling
import com.nu.bom.core.technologies.steps.precisionfettlingrough.ManufacturingStepSprueGrinding
import com.nu.bom.core.technologies.steps.precisionshotblasting.ManufacturingStepPrecisionShotBlasting
import com.nu.bom.core.technologies.steps.vacuumprecisioncasting.ManufacturingStepVacuumPrecisionCasting
import reactor.core.publisher.Mono
import java.math.BigDecimal
import java.math.RoundingMode

@EntityType(Entities.PROCESSED_MATERIAL)
class PrecisionCastedMaterial(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = ShapedMaterial(name)

    override val behaviours: List<ManufacturingEntity> =
        listOf(
            CleaningBehaviour("CleaningBehaviour"),
        )

    @ObjectView(ObjectView.NONE, 0)
    fun displayDesignation(
        designation: Text?,
        entityDesignation: Text,
    ): Text = designation ?: entityDesignation

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun cleanness(): StepSubTypeCleanness? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @SummaryView(SummaryView.PROCESS, 300, "cleaning")
    fun cleaningNeeded(): SelectableBoolean = SelectableBoolean.FALSE

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    val materialName: Text? = null

    fun density(materialName: Text): Mono<Density> =
        services
            .findMaterialDensity(
                accessCheck = calculationContext().accessCheck,
                materialKey = materialName.res,
            )

    @Input
    fun materialClass(useVacuum: Bool): Text {
        if (useVacuum.isTrue()) {
            return Text(MaterialVacuumPrecisionCasting::class.qualifiedName!!)
        } else {
            return Text(MaterialPrecisionCasting::class.qualifiedName!!)
        }
    }

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun castingWeight(): QuantityUnit? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    val yieldRate: Rate? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    val partLength: Length = Length(BigDecimal.ZERO, LengthUnits.METER)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    val partHeight: Length = Length(BigDecimal.ZERO, LengthUnits.METER)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    val partWidth: Length = Length(BigDecimal.ZERO, LengthUnits.METER)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    val partOuterDiameter: Length = Length(BigDecimal.ZERO, LengthUnits.METER)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun maxWallThickness(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    val useVacuum: Bool = Bool(false)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    val stepSubTypeHeatTreatmentPrec: StepSubTypeHeatTreatmentPrec? = null

    @ObjectView(ObjectView.MODEL, 10)
    @ReadOnly
    fun numberOfPartsPerTree(
        castingWeight: QuantityUnit,
        yieldRate: Rate,
        netWeightPerPart: QuantityUnit,
    ): Pieces = Pieces((yieldRate * castingWeight / netWeightPerPart).res.setScale(0, RoundingMode.DOWN))

    fun actualYieldRate(
        numberOfPartsPerTree: Pieces,
        castingWeight: QuantityUnit,
        netWeightPerPart: QuantityUnit,
    ): Rate = Rate(numberOfPartsPerTree.res * netWeightPerPart.res / castingWeight.res)

    fun partVolume(
        netWeightPerPart: QuantityUnit,
        density: Density,
    ): Volume = Volume(netWeightPerPart.res / density.res, VolumeUnits.CM)

    fun runnerSystemVolume(
        castingWeight: QuantityUnit,
        yieldRate: Rate,
        density: Density,
    ): Volume = Volume((1.toBigDecimal() - yieldRate.res) * castingWeight.res / density.res, VolumeUnits.CM)

    @OrderedEntityCreation
    fun createSteps() =
        arrayOf(
            "ceramicMoldBurning",
            "vacuumPrecisionCasting",
            "melting",
            "precisionCasting",
            "ceramicDemolding",
            "shotBlasting",
            "cutting",
            "sprueGrinding",
            "normalizing",
            "hardening",
            "caseHardening",
            "fettling",
            "cleaning",
        )

    @EntityProvider
    fun ceramicMoldBurning() =
        createEntity(
            name = "ManufacturingStepCeramicMoldBurning",
            clazz = ManufacturingStepCeramicMoldBurning::class,
            entityType = Entities.MANUFACTURING_STEP,
        )

    @EntityProvider
    fun vacuumPrecisionCasting(
        @Parent(Entities.PROCESSED_MATERIAL) useVacuum: Bool,
    ) = if (useVacuum.isTrue()) {
        createEntity(
            name = "ManufacturingStepVacuumPrecisionCasting",
            clazz = ManufacturingStepVacuumPrecisionCasting::class,
            entityType = Entities.MANUFACTURING_STEP,
        )
    } else {
        null
    }

    @EntityProvider
    fun melting(
        @Parent(Entities.PROCESSED_MATERIAL) useVacuum: Bool,
    ) = if (useVacuum.isFalse()) {
        createEntity(
            name = "ManufacturingStepMelting",
            clazz = ManufacturingStepMelting::class,
            entityType = Entities.MANUFACTURING_STEP,
        )
    } else {
        null
    }

    @EntityProvider
    fun precisionCasting(
        @Parent(Entities.PROCESSED_MATERIAL) useVacuum: Bool,
    ) = if (useVacuum.isFalse()) {
        createEntity(
            name = "ManufacturingStepPrecisionCasting",
            clazz = ManufacturingStepPrecisionCasting::class,
            entityType = Entities.MANUFACTURING_STEP,
        )
    } else {
        null
    }

    @EntityProvider
    fun ceramicDemolding() =
        createEntity(
            name = "ManufacturingStepCeramicDemolding",
            clazz = ManufacturingStepCeramicDemolding::class,
            entityType = Entities.MANUFACTURING_STEP,
        )

    @EntityProvider
    fun shotBlasting() =
        createEntity(
            name = "ManufacturingStepShotBlasting",
            clazz = ManufacturingStepPrecisionShotBlasting::class,
            entityType = Entities.MANUFACTURING_STEP,
        )

    @EntityProvider
    fun cutting() =
        createEntity(
            name = "ManufacturingStepCutting",
            clazz = ManufacturingStepCutting::class,
            entityType = Entities.MANUFACTURING_STEP,
        )

    @EntityProvider
    fun sprueGrinding() =
        createEntity(
            name = "ManufacturingStepSprueGrinding",
            clazz = ManufacturingStepSprueGrinding::class,
            entityType = Entities.MANUFACTURING_STEP,
        )

    @EntityProvider
    fun normalizing(
        @Parent(Entities.PROCESSED_MATERIAL) useVacuum: Bool,
        stepSubTypeHeatTreatmentPrec: StepSubTypeHeatTreatmentPrec,
    ) = if (useVacuum.isFalse() && stepSubTypeHeatTreatmentPrec == StepSubTypeHeatTreatmentPrec.NORMALIZING) {
        createEntity(
            name = "ManufacturingStepNormalizing",
            clazz = ManufacturingStepNormalizingPrec::class,
            entityType = Entities.MANUFACTURING_STEP,
        )
    } else {
        null
    }

    @EntityProvider
    fun hardening(
        @Parent(Entities.PROCESSED_MATERIAL) useVacuum: Bool,
        stepSubTypeHeatTreatmentPrec: StepSubTypeHeatTreatmentPrec,
    ) = if (useVacuum.isFalse() && stepSubTypeHeatTreatmentPrec == StepSubTypeHeatTreatmentPrec.HARDENING_TEMPERING) {
        createEntity(
            name = "ManufacturingStepHardeningTempering",
            clazz = ManufacturingStepHardeningTemperingPrec::class,
            entityType = Entities.MANUFACTURING_STEP,
        )
    } else {
        null
    }

    @EntityProvider
    fun caseHardening(
        @Parent(Entities.PROCESSED_MATERIAL) useVacuum: Bool,
        stepSubTypeHeatTreatmentPrec: StepSubTypeHeatTreatmentPrec,
    ) = if (useVacuum.isFalse() && stepSubTypeHeatTreatmentPrec == StepSubTypeHeatTreatmentPrec.CASE_HARDENING_TEMPERING) {
        createEntity(
            name = "ManufacturingStepCaseHardeningTempering",
            clazz = ManufacturingStepCaseHardeningTemperingPrec::class,
            entityType = Entities.MANUFACTURING_STEP,
        )
    } else {
        null
    }

    @EntityProvider
    fun fettling() =
        createEntity(
            name = "ManufacturingStepFettling",
            clazz = ManufacturingStepPrecisionFettling::class,
            entityType = Entities.MANUFACTURING_STEP,
        )
}
