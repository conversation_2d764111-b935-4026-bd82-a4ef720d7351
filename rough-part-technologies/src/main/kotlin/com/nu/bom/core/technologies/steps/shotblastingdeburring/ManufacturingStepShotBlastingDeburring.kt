package com.nu.bom.core.technologies.steps.shotblastingdeburring

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.Children
import com.nu.bom.core.manufacturing.annotations.Condition
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DontExport
import com.nu.bom.core.manufacturing.annotations.DontSortOptions
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EngineTransient
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityLinkField
import com.nu.bom.core.manufacturing.annotations.EntityLinkProvider
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.ExpectedParents
import com.nu.bom.core.manufacturing.annotations.ExternalDependency
import com.nu.bom.core.manufacturing.annotations.FieldIndex
import com.nu.bom.core.manufacturing.annotations.FieldName
import com.nu.bom.core.manufacturing.annotations.FilteredChildren
import com.nu.bom.core.manufacturing.annotations.IgnoreForOverwrittenState
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Modularized
import com.nu.bom.core.manufacturing.annotations.Nocalc
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.RequiredFor
import com.nu.bom.core.manufacturing.annotations.Selectable
import com.nu.bom.core.manufacturing.annotations.SubLabel
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.MaterialCalculatorForging
import com.nu.bom.core.manufacturing.entities.SingleGroupManufacturingStep
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_COUNTRY_ID_ENTITY_NAME
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.ManufacturingStepType
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.CastingAlloyMaterialGroup
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.EntityRef
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.MaterialSubstances
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SandCastingConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.SheetMaterialGroup
import com.nu.bom.core.manufacturing.fieldTypes.SystemType
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.Volume
import com.nu.bom.core.manufacturing.fieldTypes.VolumeUnits
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.nu.bom.core.manufacturing.fieldTypes.compareTo
import com.nu.bom.core.manufacturing.fieldTypes.configuration.SandCastingConfigurationField
import com.nu.bom.core.model.configurations.SandCastingConfiguration
import com.nu.bom.core.technologies.lookups.coefficientBulkMaterialReader
import com.nu.bom.core.technologies.lookups.heatPartsPerCycleReader
import com.nu.bom.core.technologies.lookups.shotBlastingDeburringTemplatesReader
import com.nu.bom.core.technologies.manufacturings.dca.material.MaterialCasting
import com.nu.bom.core.technologies.service.ShapeLookupReaderService
import com.nu.bom.core.technologies.steps.ManufacturingStepUtils
import com.nu.bom.core.technologies.steps.shotblastingdeburring.systemparameter.SystemParameterShotBlastingDeburring
import com.nu.bom.core.utils.CostFactorUtils.getCostFactorForCurrentStepId
import com.nu.bom.core.utils.annotations.TsetSuppress
import org.bson.types.ObjectId
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono
import java.math.BigDecimal
import java.math.RoundingMode

private const val UNSUPPORTED_TYPE_ERROR_MESSAGE = "Not supported type: %s"

@EntityType(Entities.MANUFACTURING_STEP)
@Modularized(
    technologies = [Model.DCA, Model.DFOR],
    parents = [
        ExpectedParents(model = Model.DCA, type = Entities.MANUFACTURING),
        ExpectedParents(model = Model.DCA, type = Entities.PROCESSED_MATERIAL),
        ExpectedParents(model = Model.DFOR, type = Entities.MANUFACTURING),
        ExpectedParents(model = Model.DFOR, type = Entities.PROCESSED_MATERIAL),
    ],
    dimensions = [Dimension.Selection.NUMBER],
    userCreatable = true,
)
class ManufacturingStepShotBlastingDeburring(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = SingleGroupManufacturingStep(name)

    companion object {
        val GAP = Length(0.1, LengthUnits.METER)
    }

    @Input
    @SubLabel
    @ReadOnly
    @Selectable(value = "manufacturingStepType", getDisplayNameFromPathQuery = true)
    fun manufacturingStepType(): Text = Text(ManufacturingStepType.DEBURRING.name)

    fun expectedMaterialClass(technologyForModularization: Text) =
        ManufacturingStepUtils.getExpectedMaterialClass(technologyForModularization)

    @ExternalDependency(ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 0)
    @EntityLinkProvider(
        ["MaterialCasting", "MaterialForging", "MaterialBending", "MaterialDieStamping", "MaterialTransferStamping"],
        entityClasses = [MaterialCasting::class, MaterialCalculatorForging::class],
    )
    @Path("/api/link{bomPath}{branchPath}?entityClasses={field:expectedMaterialClass}")
    @IgnoreForOverwrittenState
    fun linkedMaterial(): EntityRef? = null

    // region Fields from MANUFACTURING

    @Parent(Entities.MANUFACTURING)
    fun key(): Text? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    @TsetSuppress("tset:engine:field-override-configuration-value")
    fun technology(): Text? = null

    // endregion

    // region Fields from MATERIAL

    @Input
    @EntityLinkField(providerField = "linkedMaterial", "density")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 10)
    fun density(): Density? = null

    // BCT material group
    fun materialGroup(): Text? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterial", "materialGroupSheet")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 20)
    @Condition(field = "isPdsOrTdsOrCubeTechnology", value = "TRUE", operator = Condition.EQUALS)
    fun materialGroupSheet(): SheetMaterialGroup? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterial", "materialGroupCasting")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 20)
    @Condition(field = "isPdsOrTdsOrCubeTechnology", value = "TRUE", operator = Condition.NOT_EQUALS)
    fun materialGroupCasting(): CastingAlloyMaterialGroup? = null

    // endregion

    // region Fields from PROCESSED_MATERIAL

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun shapeTechnologyGroup(): Text? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 10)
    fun netWeightPerPart(): QuantityUnit? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 20)
    @Path("/api/shapes/autocomplete?tech={field:technologyForModularization}")
    @Condition(field = "isPdsOrTdsTechnology", value = "TRUE", operator = Condition.NOT_EQUALS)
    fun shapeId(): Text? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 50)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @Condition(field = "isPdsOrTdsTechnology", value = "TRUE", operator = Condition.NOT_EQUALS, removeForDynamicFields = true)
    fun partLength(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 80)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @Condition(field = "isPdsOrTdsTechnology", value = "TRUE", operator = Condition.NOT_EQUALS, removeForDynamicFields = true)
    fun partWidth(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 40)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @Condition(field = "isPdsOrTdsTechnology", value = "TRUE", operator = Condition.NOT_EQUALS, removeForDynamicFields = true)
    fun partHeight(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 60)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @Condition(field = "isPdsOrTdsTechnology", value = "TRUE", operator = Condition.NOT_EQUALS, removeForDynamicFields = true)
    fun partOuterDiameter(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 70)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @Condition(field = "isPdsOrTdsTechnology", value = "TRUE", operator = Condition.NOT_EQUALS, removeForDynamicFields = true)
    fun partInnerDiameter(): Length? = null

    // BCT
    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun materialSubstances(): MaterialSubstances? = null

    // endregion

    // region Fields from SYSTEM_PARAMETER

    @Input
    @Children(Entities.SYSTEM_PARAMETER)
    fun shotBlastingType(): SystemType? = null

    // endregion

    fun technologyForModularization(
        technology: Text?,
        technologyModel: Text?,
    ): Text? = ManufacturingStepUtils.getTechnologyForModularizedEntities(technology, technologyModel)

    fun isPdsOrTdsTechnology(technologyForModularization: Text) =
        Bool(Model.valueOf(technologyForModularization.res) in listOf(Model.FTIPDS, Model.FTITDS))

    fun isPdsOrTdsOrCubeTechnology(technologyForModularization: Text) =
        Bool(Model.valueOf(technologyForModularization.res) in listOf(Model.FTIPDS, Model.FTITDS, Model.CUBE))

    fun inputGroup(
        shapeLookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
        technologyForModularization: Text,
    ): Mono<Text> =
        shapeLookupReaderService.getInputGroupFromShapeLookup(
            calculationContext!!.accessCheck,
            shapeId,
            Model.valueOf(technologyForModularization.res),
        )

    fun utilizationRate(technologyForModularization: Text) =
        Rate(if (Model.valueOf(technologyForModularization.res) == Model.DCA) 0.9.toBigDecimal() else 0.8.toBigDecimal())

    fun scrapRate(
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_COUNTRY_INFO,
            nameFilter = TSET_COST_COUNTRY_ID_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        ) value: Map<ObjectId, Text>,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_COUNTRY_INFO,
            nameFilter = TSET_COST_COUNTRY_ID_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        @FieldName("location")
        costFactorLocations: Map<ObjectId, Text>,
        location: Text,
        technologyForModularization: Text,
    ): Rate {
        val costFactorForCurrentStepId = getCostFactorForCurrentStepId(costFactorLocations, location)
        val countryId =
            requireNotNull(value[costFactorForCurrentStepId]) {
                "CountryId for $costFactorForCurrentStepId not found"
            }
        val dca =
            when (countryId.res) {
                "10", "11" -> Rate(0.01.toBigDecimal())
                else -> Rate(0.005.toBigDecimal())
            }
        return if (Model.valueOf(technologyForModularization.res) == Model.DCA) dca else Rate(BigDecimal.ZERO)
    }

    fun toolComplexity(
        shapeLookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
        technologyForModularization: Text,
    ): Mono<Num> =
        if (Model.valueOf(technologyForModularization.res) == Model.DCA) {
            shapeLookupReaderService
                .getDcaShapeData(calculationContext!!.accessCheck, shapeId)
                .map { Num(it.toolComplexity) }
        } else {
            Num(1.0).toMono()
        }

    fun beltOrChamber(
        shapeLookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
        technologyForModularization: Text,
    ): Mono<Bool> =
        if (Model.valueOf(technologyForModularization.res) == Model.DCA) {
            shapeLookupReaderService
                .getDcaShapeData(calculationContext!!.accessCheck, shapeId)
                .map { Bool(it.beltOrChamber) }
        } else {
            Bool(true).toMono()
        }

    fun roboticCell(
        shapeLookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
        technologyForModularization: Text,
    ): Mono<Bool> =
        if (Model.valueOf(technologyForModularization.res) == Model.DCA) {
            shapeLookupReaderService
                .getDcaShapeData(calculationContext!!.accessCheck, shapeId)
                .map { Bool(it.roboticCell) }
        } else {
            Bool(false).toMono()
        }

    fun maxPartDimension(
        newPartLength: Length,
        newPartWidth: Length,
        newPartHeight: Length,
    ): Length {
        val max1 = if (newPartLength.res < newPartWidth.res) newPartWidth.res else newPartLength.res
        return Length(max(max1, newPartHeight.res), LengthUnits.METER)
    }

    @Input
    fun stepSubTypeShotBlasting(
        netWeightPerPart: QuantityUnit,
        beltOrChamber: Bool,
        roboticCell: Bool,
        technologyForModularization: Text,
        maxPartDimension: Length,
        materialGroupCasting: CastingAlloyMaterialGroup?,
    ): SystemType? {
        val tech = Model.valueOf(technologyForModularization.res)
        val tumbleBeltMaxDiam = Length(0.35, LengthUnits.METER)
        val aluminiumCastingBoolean =
            when (tech) {
                Model.DCA, Model.CHILL, Model.SAND -> {
                    if (materialGroupCasting == null) {
                        return null
                    }
                    materialGroupCasting.isAluminumAlloy && netWeightPerPart.res > Weight(4.0, WeightUnits.KILOGRAM).res
                }
                else -> false
            }

        return when {
            maxPartDimension.res < tumbleBeltMaxDiam.res && !aluminiumCastingBoolean -> SystemType.TUMBLEBELT
            aluminiumCastingBoolean ||
                (netWeightPerPart.res >= 0.5.toBigDecimal() && roboticCell.isFalse()) ||
                maxPartDimension.res > tumbleBeltMaxDiam.res &&
                (netWeightPerPart.res < 0.5.toBigDecimal() && roboticCell.isFalse()) -> SystemType.CHAMBER
            netWeightPerPart.res <= 0.5.toBigDecimal() &&
                beltOrChamber.isFalse() &&
                (tech == Model.CUBE || tech == Model.DCA) &&
                roboticCell.isFalse() -> SystemType.TROWAL
            netWeightPerPart.res >= 0.5.toBigDecimal() &&
                netWeightPerPart.res <= 15.toBigDecimal() &&
                roboticCell.isTrue() &&
                tech == Model.DCA -> SystemType.ROBOTIC_CELL_SHOT_BLASTING_SMALL
            netWeightPerPart.res > 15.toBigDecimal() && roboticCell.isTrue() && tech == Model.DCA ->
                SystemType.ROBOTIC_CELL_SHOT_BLASTING_LARGE
            else -> throw IllegalArgumentException("Unsupported system type")
        }
    }

    @Input
    fun newPartLength(
        inputGroup: Text,
        partLength: Length,
        partOuterDiameter: Length,
    ): Length =
        when (inputGroup.res) {
            "cuboid", "hollowCuboid", "shaft", "hollowCycle" -> partLength
            "cylinder", "hollowRing", "pipe", "disc", "sheet" -> partOuterDiameter
            else -> throw IllegalArgumentException(String.format(UNSUPPORTED_TYPE_ERROR_MESSAGE, inputGroup.res))
        }

    @Input
    fun newPartWidth(
        inputGroup: Text,
        partLength: Length,
        partWidth: Length,
        partOuterDiameter: Length,
    ): Length =
        when (inputGroup.res) {
            "cuboid", "hollowCuboid" -> partWidth
            "cylinder", "hollowRing", "shaft", "hollowCycle", "disc", "sheet" -> partOuterDiameter
            "pipe" -> partLength
            else -> throw IllegalArgumentException(String.format(UNSUPPORTED_TYPE_ERROR_MESSAGE, inputGroup.res))
        }

    @Input
    fun newPartHeight(
        inputGroup: Text,
        partHeight: Length,
        partOuterDiameter: Length,
    ): Length =
        when (inputGroup.res) {
            "cuboid", "hollowCuboid", "cylinder", "hollowRing", "disc" -> partHeight
            "shaft", "hollowCycle", "pipe", "sheet" -> partOuterDiameter
            else -> throw IllegalArgumentException(String.format(UNSUPPORTED_TYPE_ERROR_MESSAGE, inputGroup.res))
        }

    fun cycleTimeChamber(
        netWeightPerPart: QuantityUnit,
        sandCastingConfiguration: SandCastingConfigurationField,
    ): Time {
        val prodVolumeBasedTime =
            when {
                netWeightPerPart.res < 0.5.toBigDecimal() -> Time(6.0, TimeUnits.SECOND)
                netWeightPerPart.res < 2.5.toBigDecimal() -> Time(15.0, TimeUnits.SECOND)
                netWeightPerPart.res < 5.0.toBigDecimal() -> Time(20.0, TimeUnits.SECOND)
                netWeightPerPart.res < 10.0.toBigDecimal() -> Time(30.0, TimeUnits.SECOND)
                netWeightPerPart.res < 20.0.toBigDecimal() -> Time(45.0, TimeUnits.SECOND)
                netWeightPerPart.res < 50.0.toBigDecimal() -> Time(60.0, TimeUnits.SECOND)
                else -> Time(120.0, TimeUnits.SECOND)
            }
        val defaultTime =
            when {
                netWeightPerPart.res < 0.5.toBigDecimal() -> Time(6.0, TimeUnits.SECOND)
                netWeightPerPart.res < 2.5.toBigDecimal() -> Time(15.0, TimeUnits.SECOND)
                else -> Time(20.0, TimeUnits.SECOND)
            }
        return when (sandCastingConfiguration.res.chamberShotBlastingCycleTimeBehaviour) {
            SandCastingConfiguration.ChamberShotBlastingCycleTimeBehaviourType.DEFAULT -> defaultTime
            SandCastingConfiguration.ChamberShotBlastingCycleTimeBehaviourType.PRODUCTION_VOLUME_BASED -> prodVolumeBasedTime
        }
    }

    @Nocalc
    private fun diameterChamber(
        templateName: String,
        prodVolumeDiameter: Length,
    ): Length =
        when {
            templateName.contains("1600mmx1600mm") -> Length(1.6, LengthUnits.METER)
            templateName.contains("1200mmx1600mm") -> Length(1.2, LengthUnits.METER)
            templateName.contains("1500mmx1600mm") -> prodVolumeDiameter
            templateName.contains("10000mmx4000mm") -> Length(10.0, LengthUnits.METER)
            else -> Length(1.6, LengthUnits.METER)
        }

    @Nocalc
    private fun heightChamber(
        templateName: String,
        prodVolumeHeight: Length,
    ): Length =
        when {
            templateName.contains("1600mmx1600mm") -> Length(1.6, LengthUnits.METER)
            templateName.contains("1600mmx2600mm") -> Length(2.6, LengthUnits.METER)
            templateName.contains("1500mmx1600mm") -> prodVolumeHeight
            templateName.contains("10000mmx4000mm") -> Length(4.0, LengthUnits.METER)
            else -> Length(1.6, LengthUnits.METER)
        }

    @Nocalc
    private fun tumbleBeltForTemplate(
        templateName: String,
        maxPartDimension: Length,
        stepSubTypeShotBlasting: SystemType,
    ): Boolean =
        when {
            maxPartDimension < Length(0.1, LengthUnits.METER) && templateName.contains("100mm") -> true
            maxPartDimension < Length(0.12, LengthUnits.METER) && templateName.contains("120mm") -> true
            maxPartDimension < Length(0.25, LengthUnits.METER) && templateName.contains("250mm") -> true
            maxPartDimension < Length(0.35, LengthUnits.METER) && templateName.contains("350mm") -> true
            stepSubTypeShotBlasting != SystemType.TUMBLEBELT -> true
            else -> false
        }

    @Nocalc
    private fun partsPerCycleCalcForTemplateName(
        diameterChamber: Length,
        heightChamber: Length,
        newPartLength: Length,
        newPartWidth: Length,
    ): Pieces {
        val partsPerEtagePerHeight =
            max(
                2.toBigDecimal(),
                ((diameterChamber * Math.PI / (newPartLength + GAP)).res).setScale(0, RoundingMode.DOWN),
            )
        val partsPerEtagePerWidth =
            max(
                2.toBigDecimal(),
                ((diameterChamber * Math.PI / (newPartWidth + GAP)).res).setScale(0, RoundingMode.DOWN),
            )
        val floorsPerHeight =
            Pieces(((heightChamber - GAP) / (GAP + newPartLength)).res.setScale(0, RoundingMode.DOWN))
        val floorsPerWidth =
            Pieces(((heightChamber - GAP) / (newPartWidth + GAP)).res.setScale(0, RoundingMode.DOWN))
        val partsVersion1 = Pieces((partsPerEtagePerHeight * floorsPerWidth.res).setScale(0, RoundingMode.DOWN))
        val partsVersion2 = Pieces((partsPerEtagePerWidth * floorsPerHeight.res).setScale(0, RoundingMode.DOWN))
        return Pieces(max(partsVersion1.res, partsVersion2.res))
    }

    fun partsPerCycleChamber(
        newPartLength: Length,
        newPartWidth: Length,
        internalCycleTime: Time,
        cycleTimeChamber: Time,
        templateName: Text,
        @Children(Entities.SYSTEM_PARAMETER)
        maxWeight: QuantityUnit,
        netWeightPerPart: QuantityUnit,
        sandCastingConfiguration: SandCastingConfigurationField,
        prodVolumeDiameter: Length,
        prodVolumeHeight: Length,
    ): Pieces {
        val diameterChamber = diameterChamber(templateName.res, prodVolumeDiameter)
        val heightChamber = heightChamber(templateName.res, prodVolumeHeight)
        val partsPerCycleCalc = partsPerCycleCalcForTemplateName(diameterChamber, heightChamber, newPartLength, newPartWidth)

        val prodVolumeBasedFactor =
            when (sandCastingConfiguration.res.chamberShotBlastingCycleTimeBehaviour) {
                SandCastingConfiguration.ChamberShotBlastingCycleTimeBehaviourType.DEFAULT -> Rate(2.0)
                SandCastingConfiguration.ChamberShotBlastingCycleTimeBehaviourType.PRODUCTION_VOLUME_BASED -> Rate(1.0)
            }
        val partsPerCycleMax =
            Pieces(prodVolumeBasedFactor.res * (internalCycleTime / cycleTimeChamber).res.setScale(0, RoundingMode.DOWN))

        val partsPerCycleMaxWeight = (maxWeight / netWeightPerPart).res.setScale(0, RoundingMode.DOWN)
        val output = min(partsPerCycleCalc.res, partsPerCycleMax.res)

        return Pieces(min(output, partsPerCycleMaxWeight))
    }

    @Input
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 5)
    fun shapeBulkMaterial(shapeId: Text): Mono<Bool> =
        services
            .getLookupTable("ManufacturingStepHeatTreatment_PartsPerCycle", heatPartsPerCycleReader)
            .filter { shapeId.res == it.shapeId }
            .single()
            .map { Bool(it.bulkMaterial) }
            .switchIfEmpty(Bool(false).toMono())

    // number of boxes in tumble belt machine - expert know how
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 10)
    fun numberOfBoxes(shapeBulkMaterial: Bool) = Pieces(if (shapeBulkMaterial.isTrue()) 6.0 else 4.0)

    fun partsPerCycle(internalPartsPerCycle: QuantityUnit) = internalPartsPerCycle

    fun internalPartsPerCycle(
        @Children(Entities.SYSTEM_PARAMETER)
        maxWeight: QuantityUnit,
        netWeightPerPart: QuantityUnit,
        coefficientBulkMaterial: Rate,
        shotBlastingType: SystemType,
        density: Density,
        numberOfBoxes: Pieces,
        partsPerCycleChamber: Pieces,
    ): QuantityUnit {
        val partsPerCycle =
            when (shotBlastingType.res) {
                SystemType.Selection.DRUM ->
                    (maxWeight * coefficientBulkMaterial / netWeightPerPart).res.setScale(0, RoundingMode.DOWN)
                SystemType.Selection.TUMBLEBELT ->
                    (maxWeight * coefficientBulkMaterial / netWeightPerPart).res.setScale(0, RoundingMode.DOWN) * numberOfBoxes.res
                SystemType.Selection.BELT ->
                    (
                        (Volume(0.085 * 0.4, VolumeUnits.CM).res * coefficientBulkMaterial.res) /
                            (netWeightPerPart.res / density.res)
                    ).setScale(0, RoundingMode.DOWN)
                SystemType.Selection.CHAMBER -> partsPerCycleChamber.res
                SystemType.Selection.TROWAL,
                SystemType.Selection.ROBOTIC_CELL_SHOT_BLASTING_LARGE,
                SystemType.Selection.ROBOTIC_CELL_SHOT_BLASTING_SMALL,
                SystemType.Selection.CONTINUOUS,
                -> BigDecimal.ONE
            }
        return QuantityUnit(partsPerCycle)
    }

    @Input
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 1)
    fun coefficientBulkMaterial(
        shapeTechnologyGroup: Text,
        shapeId: Text,
    ): Mono<Rate> =
        if (shapeId == Text("CUBE_S1")) {
            Mono.just(Rate(0.5))
        } else {
            services
                .getLookupTable("Shape_CoefficientBulkMaterial", coefficientBulkMaterialReader)
                .filter { it.shapeId == shapeId.res && it.shapeTechnologyGroup == shapeTechnologyGroup.res }
                .collectList()
                .map { Rate(it.first().result * 0.8.toBigDecimal()) }
        }

    fun prodVolumeDiameter(sandCastingConfiguration: SandCastingConfigurationField): Length =
        when (sandCastingConfiguration.res.chamberShotBlastingCycleTimeBehaviour) {
            SandCastingConfiguration.ChamberShotBlastingCycleTimeBehaviourType.DEFAULT -> Length(1.5, LengthUnits.METER)
            SandCastingConfiguration.ChamberShotBlastingCycleTimeBehaviourType.PRODUCTION_VOLUME_BASED -> Length(2.0, LengthUnits.METER)
        }

    fun prodVolumeHeight(sandCastingConfiguration: SandCastingConfigurationField): Length =
        when (sandCastingConfiguration.res.chamberShotBlastingCycleTimeBehaviour) {
            SandCastingConfiguration.ChamberShotBlastingCycleTimeBehaviourType.DEFAULT -> Length(1.6, LengthUnits.METER)
            SandCastingConfiguration.ChamberShotBlastingCycleTimeBehaviourType.PRODUCTION_VOLUME_BASED -> Length(4.0, LengthUnits.METER)
        }

    fun isProductionVolumeBased(sandCastingConfiguration: SandCastingConfigurationField): Bool =
        when (sandCastingConfiguration.res.chamberShotBlastingCycleTimeBehaviour) {
            SandCastingConfiguration.ChamberShotBlastingCycleTimeBehaviourType.DEFAULT -> Bool(false)
            SandCastingConfiguration.ChamberShotBlastingCycleTimeBehaviourType.PRODUCTION_VOLUME_BASED -> Bool(true)
        }

    fun templateName(
        stepSubTypeShotBlasting: SystemType,
        maxPartDimension: Length,
        newPartLength: Length,
        newPartWidth: Length,
        netWeightPerPart: QuantityUnit,
        prodVolumeDiameter: Length,
        prodVolumeHeight: Length,
        isProductionVolumeBased: Bool,
        isPdsOrTdsTechnology: Bool,
    ): Mono<Text> =
        if (isPdsOrTdsTechnology.isTrue()) {
            Text("ShotBlastingDeburring_Trowal_AV515X41").toMono()
        } else {
            services
                .getLookupTable(
                    "ManufacturingStepShotBlastingDeburring_Templates",
                    shotBlastingDeburringTemplatesReader,
                ).filter {
                    val diameterChamber = diameterChamber(it.templateName, prodVolumeDiameter)
                    val heightChamber = heightChamber(it.templateName, prodVolumeHeight)
                    val partsPerCycleCalc =
                        partsPerCycleCalcForTemplateName(diameterChamber, heightChamber, newPartLength, newPartWidth)

                    val diffStepSubType = stepSubTypeShotBlasting == it.stepSubTypeShotBlasting
                    val diffTumbleBelt =
                        tumbleBeltForTemplate(it.templateName, maxPartDimension, stepSubTypeShotBlasting)

                    val diffPartsPerCycle = partsPerCycleCalc.res != BigDecimal.ZERO

                    val maxWeightWithinLimit = netWeightPerPart.res < it.maxWeight.res
                    val diffMaxWeight =
                        when {
                            (stepSubTypeShotBlasting == SystemType.CHAMBER) &&
                                ((partsPerCycleCalc * netWeightPerPart).res < it.maxWeight.res) -> true

                            stepSubTypeShotBlasting != SystemType.CHAMBER -> true
                            it.templateName.contains("1500mmx1600mm") && !isProductionVolumeBased.res && maxWeightWithinLimit -> true
                            it.templateName.contains("1500mmx1600mm") &&
                                isProductionVolumeBased.res &&
                                netWeightPerPart.res >= BigDecimal(500.0) &&
                                maxWeightWithinLimit -> true

                            it.templateName.contains("1600mmx2600mm") &&
                                isProductionVolumeBased.res &&
                                netWeightPerPart.res < BigDecimal(500.0) &&
                                maxWeightWithinLimit -> true

                            it.templateName.contains("10000mmx4000mm") &&
                                netWeightPerPart.res >= BigDecimal(4000.0) &&
                                maxWeightWithinLimit -> true

                            else -> false
                        }

                    diffStepSubType && diffTumbleBelt && diffPartsPerCycle && diffMaxWeight
                }.elementAt(0)
                .map { Text(it.templateName) }
        }

    @DontSortOptions
    @DontExport
    @Input
    fun sandCastingConfigurationKey(): Mono<SandCastingConfigurationKey> =
        services.getDefaultConfigurationKey(::SandCastingConfigurationKey)

    @EngineTransient
    fun sandCastingConfiguration(sandCastingConfigurationKey: SandCastingConfigurationKey) =
        services.getConfiguration(sandCastingConfigurationKey, ::SandCastingConfigurationField)

    // region Entity creation

    @EntityCreation(Entities.SYSTEM_PARAMETER)
    fun createSystemParameter(templateName: Text): Mono<SystemParameterShotBlastingDeburring> =
        createSystemParameter(
            masterData = MasterDataType.SYSTEM_PARAMETER_SHOT_BLASTING_DEBURRING,
            clazz = SystemParameterShotBlastingDeburring::class,
            system = templateName.res,
            year = calculationContext?.year!!,
            location = "Global",
        )

    @EntityCreation(Entities.MACHINE)
    fun createMachines(templateName: Text): Flux<ManufacturingEntity> = createMachinesFromTemplate(templateName)

    @EntityCreation(Entities.LABOR)
    fun createLabor(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> = createLaborFromTemplate(templateName, locationName)

    // endregion
}
