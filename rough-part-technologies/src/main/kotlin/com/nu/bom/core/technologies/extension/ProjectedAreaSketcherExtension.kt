package com.nu.bom.core.technologies.extension

import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.Extends
import com.nu.bom.core.manufacturing.annotations.Hidden
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Precompute
import com.nu.bom.core.manufacturing.annotations.WizardField
import com.nu.bom.core.manufacturing.defaults.NullProvider
import com.nu.bom.core.manufacturing.entities.ManufacturingEntityExtension
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.extension.TSET_DEL_EXTENSION_PACKAGE
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.AreaUnits
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.service.wizard.steps.WizardSpecFieldStep
import com.nu.bom.core.smf.FeUnfolded
import com.nu.bom.core.technologies.manufacturings.dca.ManufacturingDieCasting
import com.nu.bom.core.technologies.manufacturings.inj.ManufacturingInjection
import com.nu.bom.core.technologies.manufacturings.inj.ManufacturingInjection2
import com.nu.bom.core.technologies.manufacturings.minj.ManufacturingMicroInjection
import com.nu.bom.core.technologies.manufacturings.rinj.ManufacturingRubberInjection
import com.nu.bom.core.technologies.manufacturings.sint.ManufacturingSintering
import com.nu.bom.core.tsetdel.TsetDelApiService
import com.nu.bom.core.tsetdel.TsetDelFrontendFormatService
import com.tset.bom.clients.tsetdel.model.TsetDelFileFormat
import com.tset.bom.clients.tsetdel.model.TsetDelPolygonUnionAreaRequest
import com.tset.bom.clients.tsetdel.model.TsetDelProjectedAreaRequest
import reactor.core.publisher.Mono

@Extends(
    [
        ManufacturingDieCasting::class,
        ManufacturingInjection::class,
        ManufacturingInjection2::class,
        ManufacturingRubberInjection::class,
        ManufacturingMicroInjection::class,
        ManufacturingSintering::class,
    ],
    TSET_DEL_EXTENSION_PACKAGE,
)
class ProjectedAreaSketcherExtension(name: String) : ManufacturingEntityExtension(name) {
    @Input
    @Precompute
    @WizardField(WizardSpecFieldStep::class)
    @Hidden
    fun projectedAreaAsPolygons(
        tsetDelApiService: TsetDelApiService,
        tsetDelFrontendFormatService: TsetDelFrontendFormatService,
        technologyKey: Text,
        shapeId: Text,
        partLength: Length?,
        partWidth: Length?,
        partHeight: Length?,
        partOuterDiameter: Length?,
    ): Mono<FeUnfolded> {
        // backwards compatibility for deactivated shapes
        val partInputGroup =
            services.getShapeInfoOrThrow(
                technology = technologyKey.res,
                shapeId = shapeId.res,
                onlyActive = false,
            ).inputGroup
        return services
            .getTsetDelFileLocation(
                technologyKey.res,
                shapeId.res,
                partInputGroup,
            )
            .flatMap { fileLocation ->
                tsetDelApiService.projectedArea(
                    id = entityId,
                    TsetDelProjectedAreaRequest(
                        file_format = TsetDelFileFormat.stl_ascii,
                        file_location = fileLocation,
                        shape_data =
                            tsetDelFrontendFormatService.getTsetDelShapeData(
                                partInputGroup,
                                partLength,
                                partWidth,
                                partHeight,
                                partOuterDiameter,
                            ),
                    ),
                ).map {
                    tsetDelFrontendFormatService.tsetDelPolygonsToFeUnfolded(it.polygons)
                }
            }
    }

    @Input
    @WizardField(WizardSpecFieldStep::class, index = Int.MAX_VALUE)
    @Precompute
    @DefaultUnit(DefaultUnit.QCM)
    fun projectedAreaPerPart(
        tsetDelApiService: TsetDelApiService,
        tsetDelFrontendFormatService: TsetDelFrontendFormatService,
        @Parent(Entities.PROCESSED_MATERIAL)
        @Default(NullProvider::class)
        projectedAreaPerPart: Area?,
        projectedAreaAsPolygons: FeUnfolded,
    ): Mono<Area> =
        if (projectedAreaPerPart != null) {
            Mono.just(projectedAreaPerPart)
        } else {
            val polygons =
                tsetDelFrontendFormatService.feUnfoldedToTsetDelPolygons(
                    projectedAreaAsPolygons,
                )
            tsetDelApiService.polygonUnionArea(
                entityId,
                TsetDelPolygonUnionAreaRequest(
                    polygons = polygons,
                ),
            ).map { Area(it.polygon_union_area, AreaUnits.QMM) }
        }
}
