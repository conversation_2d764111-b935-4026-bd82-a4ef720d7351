package com.nu.bom.core.technologies.steps.aluextrusion.tool

import com.nu.bom.core.exception.userException.TemplateNotFoundCauseType
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.Tool
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.AlexDieTypeTool
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.PiecesUnits
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.ToolDetailViewConfig
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.nu.bom.core.technologies.steps.ManufacturingStepUtils.minByOrTemplateNotFoundException
import reactor.core.publisher.Mono
import java.math.BigDecimal

@Suppress("unused")
@EntityType(Entities.TOOL)
class AluExtrusionTool(
    name: String,
) : ManufacturingEntity(name) {
    override val extends: ManufacturingEntity = Tool(name)

    // region specific logic

    companion object {
        private class ToolTemplateLookupEntry(
            val toolName: String,
            val baseCost: BigDecimal,
            val configuration: String,
            val diameterInMm: BigDecimal,
        )

        private val toolTemplateLookupReader: (row: List<String>) -> ToolTemplateLookupEntry = {
            ToolTemplateLookupEntry(
                toolName = it[0],
                baseCost = it[1].toBigDecimal(),
                configuration = it[2],
                diameterInMm = it[3].toBigDecimal(),
            )
        }

        private class HoleTemplateLookupEntry(
            val numberOfHoles: BigDecimal,
            val factor: BigDecimal,
        )

        private val holeTemplateLookupReader: (row: List<String>) -> HoleTemplateLookupEntry = {
            HoleTemplateLookupEntry(
                numberOfHoles = it[0].toBigDecimal(),
                factor = it[1].toBigDecimal(),
            )
        }
    }

    @Input
    @Parent(Entities.MANUFACTURING_STEP)
    fun partsPerCycle(): QuantityUnit? = null

    fun numberOfHolesFactor(
        @Parent(Entities.MANUFACTURING_STEP)
        numberOfHoles: Num,
    ): Mono<QuantityUnit> =
        services
            .getLookupTable(
                "ManufacturingStepAluExtrusion_AlexHoles",
                holeTemplateLookupReader,
            ).filter {
                numberOfHoles.res.compareTo(it.numberOfHoles) == 0
            }.single()
            .map {
                QuantityUnit(it.factor)
            }

    fun cavityFactor(partsPerCycle: QuantityUnit): Num = Num(1.05.toBigDecimal().pow(partsPerCycle.ceilToBigInteger.toInt() - 1))

    fun baseCost(
        @Parent(Entities.MANUFACTURING_STEP)
        requiredToolDiameter: Length,
    ): Mono<Money> {
        val lookupName = "ManufacturingStepAluExtrusion_AluExtrusionTool"
        return services
            .getLookupTable(lookupName, toolTemplateLookupReader)
            .filter {
                it.diameterInMm > requiredToolDiameter.inMillimeter
            }.minByOrTemplateNotFoundException(
                selector = { it.baseCost },
                lookupName = lookupName,
                lookupInputs = mapOf("requiredToolDiameter" to requiredToolDiameter.toString()),
                causeType = TemplateNotFoundCauseType.NO_FITTING_TOOL,
            ).map { Money(it.baseCost) }
    }

    fun toolDiameter(baseCost: Money): Mono<Length> =
        services
            .getLookupTable(
                "ManufacturingStepAluExtrusion_AluExtrusionTool",
                toolTemplateLookupReader,
            ).filter {
                it.baseCost.compareTo(baseCost.res) == 0
            }.single()
            .map {
                Length(it.diameterInMm, LengthUnits.MILLIMETER)
            }

    @Input
    fun dieType(
        @Parent(Entities.MANUFACTURING_STEP)
        numberOfHoles: Num,
    ): AlexDieTypeTool =
        if (numberOfHoles.res.compareTo(BigDecimal.ZERO) == 0) {
            AlexDieTypeTool.FLAT
        } else {
            AlexDieTypeTool.HOLLOW
        }

    @Input
    @ObjectView(ObjectView.TOOL, 1)
    fun numberOfPlatesPerTool(dieType: AlexDieTypeTool): QuantityUnit =
        if (dieType == AlexDieTypeTool.FLAT) {
            QuantityUnit(BigDecimal.ONE)
        } else {
            QuantityUnit(2.toBigDecimal())
        }

    // total, not per cavity
    fun toolLifeTimeInMeter(): Length = Length(50000.0, LengthUnits.METER)

    @ReadOnly
    fun lifetimePerToolWeight(
        @Parent(Entities.MANUFACTURING_STEP)
        weightPerMeter: QuantityUnit,
        toolLifeTimeInMeter: Length,
    ): Weight = Weight(weightPerMeter.res * toolLifeTimeInMeter.res, WeightUnits.KILOGRAM)

    @Input
    @SpecialLink("MaterialAluExtrusion", "actualExtrudedNetLengthPerCavity")
    fun actualExtrudedNetLengthPerCavity(): Length? = null

    @Input
    @SpecialLink("MaterialAluExtrusion", "numberOfPartsFromOnBillet")
    fun numberOfPartsFromOnBillet(): Pieces? = null

    @ReadOnly
    fun lifetimePerTool(
        toolLifeTimeInMeter: Length,
        actualExtrudedNetLengthPerCavity: Length,
        numberOfPartsFromOnBillet: Pieces,
        partsPerCycle: QuantityUnit,
    ): Pieces {
        val extrudedLengthOverAllCavities = actualExtrudedNetLengthPerCavity * partsPerCycle.res
        val extrudedLengthPerPart = extrudedLengthOverAllCavities / numberOfPartsFromOnBillet.res

        return Pieces(toolLifeTimeInMeter.inMeter / extrudedLengthPerPart.inMeter, PiecesUnits.PIECE)
    }

    fun extrudedLengthPerCavityBtw2Maintenances(): Length = Length(2000.toBigDecimal(), LengthUnits.METER)

    fun batchSize(
        @Parent(Entities.MANUFACTURING_STEP)
        weightPerMofProfile: QuantityUnit,
        extrudedLengthPerCavityBtw2Maintenances: Length,
        partsPerCycle: QuantityUnit,
    ): QuantityUnit = QuantityUnit(weightPerMofProfile.res * extrudedLengthPerCavityBtw2Maintenances.res * partsPerCycle.res)

    // endregion
    // region override of core logic

    fun toolDetailView(): ToolDetailViewConfig = ToolDetailViewConfig.ALEX_TOOL

    fun cO2ToolDetailView(): ToolDetailViewConfig = ToolDetailViewConfig.ALEX_TOOL

    fun investPerTool(
        numberOfHolesFactor: QuantityUnit,
        numberOfPlatesPerTool: QuantityUnit,
        cavityFactor: Num,
        baseCost: Money,
    ): Money = Money(numberOfHolesFactor.res * numberOfPlatesPerTool.res * cavityFactor.res * baseCost.res)

    fun serviceLifeInCycles(
        lifetimePerTool: QuantityUnit,
        partsPerCycle: QuantityUnit,
    ): Num = Num(lifetimePerTool.res / partsPerCycle.res)

    // endregion
}
