package com.nu.bom.core.technologies.manufacturings.last

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.Condition
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DontSortOptions
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Hidden
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.Precompute
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.annotations.WizardField
import com.nu.bom.core.manufacturing.defaults.NullProvider
import com.nu.bom.core.manufacturing.entities.BaseModelManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ShapeBasedCostModuleManufacturing
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Quantity
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.RotorOrStator
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeCleanness
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeLaminationStack
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeLaminationStackAssemblyType
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Volume
import com.nu.bom.core.technologies.manufacturings.last.material.LaminationStackedMaterial
import com.nu.bom.core.technologies.service.ShapeLookupReaderService
import com.nu.bom.core.technologies.steps.baking.ManufacturingStepBaking
import com.nu.bom.core.technologies.steps.lasermarkinglast.ManufacturingStepLaserMarkingLast
import com.nu.bom.core.technologies.steps.stamping.ManufacturingStepStamping
import com.nu.bom.core.technologies.steps.welding.ManufacturingStepWelding
import com.nu.bom.core.utils.annotations.TsetSuppress
import reactor.core.publisher.Mono
import kotlin.reflect.KClass

@EntityType(Entities.MANUFACTURING)
class ManufacturingLaminationStack(
    name: String,
) : BaseModelManufacturing(name) {
    override val extends = ShapeBasedCostModuleManufacturing(name)

    override val model: Model
        get() = Model.LAST

    override val modelSteps: List<KClass<out ManufacturingEntity>>
        get() =
            listOf(
                ManufacturingStepStamping::class,
                ManufacturingStepBaking::class,
                ManufacturingStepWelding::class,
                ManufacturingStepLaserMarkingLast::class,
            )

    fun inputGroupInternal(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
    ) = lookupReaderService.getInputGroupFromShapeLookup(calculationContext!!.accessCheck, shapeId, Model.LAST)

    @Input
    @TsetSuppress("tset:engine:field-override-configuration-value")
    fun technology() = Text("LAST")

    fun internalCallsPerYear() = Num(12.0)

    @Input
    @Path("/api/model/last/materialName")
    fun materialName(): Text? = null

    @Input
    fun materialClass(): Text = Text("MaterialLaminationStack")

    @Input
    @WizardField(index = 10)
    @SummaryView(SummaryView.PROCESS, 110)
    fun assemblyType(): StepSubTypeLaminationStackAssemblyType? = null

    @Input
    @WizardField
    @Hidden
    @Precompute
    fun rotorAndStatorWeldingPossible(
        laminationStackType: StepSubTypeLaminationStack,
        assemblyType: StepSubTypeLaminationStackAssemblyType,
    ) = Bool(
        laminationStackType == StepSubTypeLaminationStack.ROTOR_STATOR_NESTED &&
            assemblyType == StepSubTypeLaminationStackAssemblyType.WELDING,
    )

    @Input
    @WizardField(index = 20)
    @Condition(field = "rotorAndStatorWeldingPossible", value = "TRUE", operator = Condition.EQUALS)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @DontSortOptions
    fun weldRotorOrStator() = RotorOrStator.ROTOR_AND_STATOR

    @Input
    @Condition(field = "assemblyType", value = "WELDING", operator = Condition.EQUALS)
    @WizardField(index = 30)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun numberOfWelds(): Quantity? = null

    @WizardField
    @Input
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @Condition(field = "assemblyType", value = "BAKING", operator = Condition.EQUALS)
    fun maxWallThickness(
        @Parent(Entities.PROCESSED_MATERIAL) @Default(NullProvider::class)
        maxWallThickness: Length?,
    ) = maxWallThickness ?: Length(2.0, LengthUnits.MILLIMETER)

    @Input
    @WizardField(index = 40)
    fun laserMarking(): SelectableBoolean? = null

    @Input
    @WizardField(index = 60)
    @Condition(field = "cleaningNeeded", value = "true", operator = Condition.EQUALS)
    fun cleanness(): StepSubTypeCleanness = StepSubTypeCleanness.NORMAL

    @Input
    @WizardField(index = 50)
    fun cleaningNeeded(): SelectableBoolean = SelectableBoolean.FALSE

    fun stackingFactor(materialName: Text): Mono<Rate> =
        services
            .findStackingFactor(
                calculationContext().accessCheck,
                materialName.res,
            )

    fun lamellaThickness(materialName: Text): Mono<Length> =
        services
            .findLamellaThickness(
                calculationContext().accessCheck,
                materialName.res,
            )

    fun laminationStackType(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
    ): Mono<StepSubTypeLaminationStack> =
        lookupReaderService.getLastShapeData(calculationContext!!.accessCheck, shapeId).map {
            it.laminationStackType
        }

    @Input
    @Precompute
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun partUpperWidth(
        @Parent(Entities.PROCESSED_MATERIAL) @Default(NullProvider::class)
        partUpperWidth: Length?,
        technologyKey: Text,
        shapeId: Text,
        volume: Volume,
    ): Length =
        partUpperWidth ?: scaleLengthWithVolume(technologyKey = technologyKey, shapeId = shapeId, volume = volume) {
            it.partUpperWidth
        }

    @Input
    @Precompute
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun partLowerWidth(
        @Parent(Entities.PROCESSED_MATERIAL) @Default(NullProvider::class)
        partLowerWidth: Length?,
        technologyKey: Text,
        shapeId: Text,
        volume: Volume,
    ): Length =
        partLowerWidth ?: scaleLengthWithVolume(technologyKey = technologyKey, shapeId = shapeId, volume = volume) {
            it.partLowerWidth
        }

    fun blankDiameter(
        laminationStackType: StepSubTypeLaminationStack,
        partOuterDiameter: Length,
        partLength: Length,
    ): Length =
        when {
            laminationStackType == StepSubTypeLaminationStack.T_SEGMENT -> partLength
            else -> partOuterDiameter
        }

    @EntityCreation(Entities.PROCESSED_MATERIAL, childCreations = [Entities.MANUFACTURING_STEP])
    fun createMaterial(): ManufacturingEntity =
        createEntity(
            name = "LaminationStackedMaterial",
            clazz = LaminationStackedMaterial::class,
            entityType = Entities.MATERIAL,
        )
}
