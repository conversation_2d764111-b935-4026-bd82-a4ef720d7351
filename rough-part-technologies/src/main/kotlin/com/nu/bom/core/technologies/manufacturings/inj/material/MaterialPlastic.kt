package com.nu.bom.core.technologies.manufacturings.inj.material

import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.RawMaterialPlasticGranulate
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.MaterialCostMode
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.PartQuality
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.RunnerSystems
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.model.NET_WEIGHT_PER_PART_PREDICTION_SERVICE
import com.nu.bom.core.technologies.service.ShapeLookupReaderService
import reactor.core.publisher.Mono
import java.math.BigDecimal

@EntityType(Entities.MATERIAL)
class MaterialPlastic(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = RawMaterialPlasticGranulate(name)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    val materialName: Text? = null

    @Input
    val reuseOfScrap = Bool(false)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    val shapeTechnologyGroup: Text? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    val projectedAreaPerPart: Area? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    val partQuality: PartQuality? = null

    @Input
    @SpecialLink("ManufacturingStepInjection", "partsPerCycle")
    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 140)
    val partsPerCycle: QuantityUnit? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 150)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun netWeightPerPart(): QuantityUnit? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun scrapWeightPerPart(): QuantityUnit? = null

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 160)
    fun netWeightPerCycle(
        netWeightPerPart: QuantityUnit,
        partsPerCycle: QuantityUnit,
    ): QuantityUnit = netWeightPerPart * partsPerCycle

    @ObjectView(ObjectView.MATERIAL, 165)
    fun runnerSystems(
        lookupReaderService: ShapeLookupReaderService,
        @Parent(Entities.PROCESSED_MATERIAL)
        shapeId: Text,
        // Todo: Might need to be changed to average volume. See COST-50914
        @Parent(Entities.MANUFACTURING)
        peakUsableProductionVolumePerYear: QuantityUnit,
        partQuality: PartQuality,
    ): Mono<RunnerSystems> =
        lookupReaderService.getInjShapeData(calculationContext!!.accessCheck, shapeId).mapNotNull {
            when {
                it.runnerSystems == RunnerSystems.HOT_RUNNER && peakUsableProductionVolumePerYear.res < 1000000.toBigDecimal() ->
                    RunnerSystems.HOT_COLD_RUNNER
                it.runnerSystems == RunnerSystems.HOT_COLD_RUNNER && peakUsableProductionVolumePerYear.res < 1000000.toBigDecimal() ->
                    RunnerSystems.COLD_RUNNER
                it.runnerSystems == RunnerSystems.HOT_COLD_RUNNER && partQuality == PartQuality.PRECISE ->
                    RunnerSystems.HOT_COLD_RUNNER
                it.runnerSystems == RunnerSystems.HOT_COLD_RUNNER && partQuality == PartQuality.PRECISE_VERY_TIGHT_TOLERANCES ->
                    RunnerSystems.HOT_COLD_RUNNER
                else -> it.runnerSystems
            }
        }

    @ObjectView(ObjectView.MATERIAL, 170)
    @SummaryView(SummaryView.PROCESS, 10)
    fun sprueRate(
        netWeightPerPart: QuantityUnit,
        shapeTechnologyGroup: Text,
        projectedAreaPerPart: Area,
        runnerSystems: RunnerSystems,
    ): Mono<Rate> {
        val runnerSystemsFactor =
            when (runnerSystems) {
                RunnerSystems.HOT_RUNNER -> 0.toBigDecimal()
                RunnerSystems.HOT_COLD_RUNNER -> 0.75.toBigDecimal()
                RunnerSystems.COLD_RUNNER -> 1.toBigDecimal()
                else -> 1.toBigDecimal()
            }
        return services
            .predictNum(
                "inj",
                "SprueRate",
                mapOf(
                    NET_WEIGHT_PER_PART_PREDICTION_SERVICE to netWeightPerPart,
                    "Shape_L1" to shapeTechnologyGroup,
                    "ProjectedAreaPerPart" to projectedAreaPerPart,
                ),
            ).map { Rate(it) * runnerSystemsFactor }
    }

    fun sprueRateForInjectionTime(
        netWeightPerPart: QuantityUnit,
        shapeTechnologyGroup: Text,
        projectedAreaPerPart: Area,
    ): Mono<Rate> =
        services
            .predictNum(
                "inj",
                "SprueRate",
                mapOf(
                    NET_WEIGHT_PER_PART_PREDICTION_SERVICE to netWeightPerPart,
                    "Shape_L1" to shapeTechnologyGroup,
                    "ProjectedAreaPerPart" to projectedAreaPerPart,
                ),
            ).map { Rate(it) + 1.toBigDecimal() }

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 180)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun sprueWeightPerPart(
        netWeightPerPart: QuantityUnit,
        sprueRate: Rate,
    ): QuantityUnit = netWeightPerPart * sprueRate

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 190)
    fun sprueWeightPerCycle(
        partsPerCycle: QuantityUnit,
        sprueWeightPerPart: QuantityUnit,
    ): QuantityUnit = sprueWeightPerPart * partsPerCycle

    @ObjectView(ObjectView.MATERIAL, 200)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun shotWeightPerPart(
        netWeightPerPart: QuantityUnit,
        sprueWeightPerPart: QuantityUnit,
    ): QuantityUnit = netWeightPerPart + sprueWeightPerPart

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 210)
    fun shotWeightPerCycle(
        shotWeightPerPart: QuantityUnit,
        partsPerCycle: QuantityUnit,
    ): QuantityUnit = shotWeightPerPart * partsPerCycle

    @Input
    @ObjectView(ObjectView.MATERIAL, 220)
    val recyclingRate = Rate(BigDecimal.ZERO)

    @ObjectView(ObjectView.MATERIAL, 230)
    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun retrievableScrapPerPart(
        sprueWeightPerPart: QuantityUnit,
        recyclingRate: Rate,
    ): QuantityUnit = sprueWeightPerPart * recyclingRate

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun irretrievableScrapPerPart(sprueLossWeight: QuantityUnit): QuantityUnit = sprueLossWeight

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 250)
    fun sprueLossWeight(
        sprueWeightPerPart: QuantityUnit,
        recyclingRate: Rate,
    ): QuantityUnit = sprueWeightPerPart * (1.toBigDecimal() - recyclingRate.res)

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 270)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun deployedWeightPerPart(
        netWeightPerPart: QuantityUnit,
        sprueLossWeight: QuantityUnit,
    ): QuantityUnit = netWeightPerPart + sprueLossWeight

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 280)
    fun deployedWeightPerCycle(
        deployedWeightPerPart: QuantityUnit,
        partsPerCycle: QuantityUnit,
    ): QuantityUnit = deployedWeightPerPart * partsPerCycle

    /****** RawMaterials override **********/

    val materialCostMode: MaterialCostMode = MaterialCostMode.SELL_IRRETRIEVABLE

    fun materialWastePrice(pricePerUnit: Money): Money = Money(0.2.toBigDecimal() * pricePerUnit.res)

    fun materialRecyclingPrice(pricePerUnit: Money): Money = Money(0.4.toBigDecimal() * pricePerUnit.res)

    fun quantityForExport(deployedWeightPerPart: QuantityUnit): QuantityUnit = deployedWeightPerPart
}
