package com.nu.bom.core.technologies.steps.turn

import com.nu.bom.core.exception.TurningPartTooBigForMachineException
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.CalculationPreview
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.EnginePrivateFieldMetaInfo
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.CustomProcurementTypeHelper
import com.nu.bom.core.manufacturing.commercialcalculation.fieldsbuilder.entityextensions.commercialcalculation.cost.CommercialCalculationCostMaterialUsage
import com.nu.bom.core.manufacturing.defaults.NullProvider
import com.nu.bom.core.manufacturing.entities.BaseManufacturingFields
import com.nu.bom.core.manufacturing.entities.BomEntry
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.CO2CalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.ConfigIdentifier
import com.nu.bom.core.manufacturing.fieldTypes.CostCalculationOperationsConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.CustomProcurementType
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.ListOfStrings
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TurningStep
import com.nu.bom.core.model.NET_WEIGHT_PER_PART
import com.nu.bom.core.technologies.steps.turn.systemparameter.SystemParameterRawTurning
import com.nu.bom.core.utils.EntityManager
import com.nu.bom.core.utils.exceptionOnEmpty
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toFlux

@EntityType(Entities.MANUFACTURING_STEP)
class ManufacturingStepRawTurning(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = ManufacturingStepTurning(name)

    val step = TurningStep.soft

    val turning: ManufacturingStepTurning
        get() = extends

    @Input
    val scrapRate: Rate = Rate(0.002.toBigDecimal())

    @EntityCreation(Entities.MATERIAL)
    fun createMaterial(
        entityManager: EntityManager,
        @Parent(Entities.PROCESSED_MATERIAL)
        materialName: Text,
        @Parent(Entities.PROCESSED_MATERIAL)
        materialClass: Text,
        @Parent(Entities.MANUFACTURING)
        rawPartTechnology: Text?,
    ): Mono<ManufacturingEntity> {
        if (rawPartTechnology == null) {
            throw Exception("Raw part technology missing in the manufacturing of the manufacturing step raw turning.")
        }

        // assumption: raw part technologies have exactly one materialType
        val materialType = entityManager.materialTypesForTechnology(rawPartTechnology.toString()).single()

        return createEntityWithNewMasterdata(
            name = "MaterialTurningRawt",
            entityType = Entities.MATERIAL,
            clazz = materialClass.res,
            masterDataType = materialType,
            masterDataKey = materialName.res,
        ).switchIfEmpty(Mono.error(java.lang.IllegalArgumentException("PROBLEM MAT")))
    }

    @EntityCreation(Entities.SYSTEM_PARAMETER)
    fun createSystemParameter(selectedTemplateName: Text): Mono<ManufacturingEntity> =
        createSystemParameter(
            masterData = MasterDataType.SYSTEM_PARAMETER_RAW_TURNING,
            clazz = SystemParameterRawTurning::class,
            system = selectedTemplateName.res,
            year = calculationContext?.year!!,
            location = "Global",
        )

    @EnginePrivateFieldMetaInfo(false)
    fun inhouseProcurementType(
        costCalculationOperationKey: CostCalculationOperationsConfigurationKey,
        @Default(NullProvider::class)
        cO2CalculationOperationKey: CO2CalculationOperationsConfigurationKey?,
    ): Mono<CustomProcurementType> =
        CustomProcurementTypeHelper
            .getDefaultInhouseProcurementType(costCalculationOperationKey, cO2CalculationOperationKey, services)

    @EntityCreation(Entities.BOM_ENTRY, childCreations = [Entities.MANUFACTURING])
    fun createRawPart(
        entityManager: EntityManager,
        @Parent(Entities.MANUFACTURING)
        rawPartTechnology: Text,
        @Parent(Entities.MANUFACTURING)
        rawPartLength: Length,
        @Parent(Entities.MANUFACTURING)
        rawPartInnerDiameter: Length,
        @Parent(Entities.MANUFACTURING)
        rawPartOuterDiameter: Length,
        @Parent(Entities.MANUFACTURING)
        rawPartMaxWallThickness: Length,
        @Parent(Entities.PROCESSED_MATERIAL)
        materialName: Text,
        @Parent(Entities.PROCESSED_MATERIAL)
        rawWeightPerPart: QuantityUnit,
        @Parent(Entities.PROCESSED_MATERIAL)
        partInputGroup: Text,
        @Parent(Entities.MANUFACTURING)
        rawCostModuleConfigurationIdentifier: ConfigIdentifier?,
        inhouseProcurementType: CustomProcurementType,
    ): Flux<ManufacturingEntity> {
        val bomEntry =
            createEntity(
                name = BomEntry::class.simpleName!!,
                clazz = RawtBomEntry::class,
                fields = mapOf("quantity" to Pieces(1.toBigDecimal())),
                entityType = Entities.BOM_ENTRY,
            )

        val entityClass = entityManager.getClass(Model.valueOf(rawPartTechnology.res).entity)

        val partLengthOrHeight =
            when (partInputGroup.res) {
                "hollowRing" -> "partHeight"
                else -> "partLength"
            }
        // For compatibility with old calcs
        val type =
            if (rawPartTechnology.res == Model.DFORT.name) {
                Model.DFOR.name
            } else {
                rawPartTechnology.res
            }

        return services
            .getDefaultCostModuleConfigurationKey(type)
            .flatMapMany {
                val configIdentifier = rawCostModuleConfigurationIdentifier ?: ConfigIdentifier(it)
                val subManufacturing =
                    createEntity(
                        name = entityClass.simpleName,
                        clazz = entityClass.kotlin,
                        fields =
                            mapOf(
                                partLengthOrHeight to rawPartLength,
                                "partInnerDiameter" to rawPartInnerDiameter,
                                "partOuterDiameter" to rawPartOuterDiameter,
                                "maxWallThickness" to rawPartMaxWallThickness,
                                "materialName" to materialName,
                                NET_WEIGHT_PER_PART to rawWeightPerPart,
                                CommercialCalculationCostMaterialUsage::customProcurementType.name to inhouseProcurementType,
                                BaseManufacturingFields::costModuleConfigurationIdentifier.name to configIdentifier,
                                BaseManufacturingFields::configurationTechnology.name to rawPartTechnology,
                            ),
                        args =
                            mapOf(
                                "isPart" to false,
                            ),
                        entityType = Entities.MANUFACTURING,
                    )

                bomEntry.addChild(subManufacturing)
                listOf(bomEntry).toFlux()
            }
    }

    @SummaryView(SummaryView.PROCESS, 20, fieldName = "cycleTimeSoftTurning")
    @CalculationPreview(4, "cycleTimeSoftTurning")
    fun cycleTimeSummaryView(cycleTime: CycleTime) = cycleTime

    @SummaryView(SummaryView.PROCESS, 30, fieldName = "manufacturingScrapRateSoftTurning")
    fun manufacturingScrapRateSummaryView(manufacturingScrapRate: Rate): Rate = manufacturingScrapRate

    fun templateNames(
        @Parent(Entities.MANUFACTURING) lengthForLookup: Length,
        @Parent(Entities.MANUFACTURING) diameterForLookup: Length,
    ): Mono<ListOfStrings> {
        val rawTurningMachines =
            services.getLookupTable("ManufacturingStepRawTurning_Templates", rawAndHardTemplateLookupReader)
        return commonRawAndHardMachineSelection(rawTurningMachines, lengthForLookup, diameterForLookup)
            .exceptionOnEmpty { TurningPartTooBigForMachineException(TurningStep.soft, lengthForLookup, diameterForLookup) }
            .map { fastestMachinesClosestToInput ->
                ListOfStrings(fastestMachinesClosestToInput)
            }
    }
}
