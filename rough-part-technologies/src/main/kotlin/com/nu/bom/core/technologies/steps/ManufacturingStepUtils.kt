package com.nu.bom.core.technologies.steps

import com.nu.bom.core.exception.InternalServerException
import com.nu.bom.core.exception.readable.ErrorCode
import com.nu.bom.core.exception.userException.TemplateNotFoundCauseType
import com.nu.bom.core.exception.userException.TemplateNotFoundException
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.technologies.manufacturings.afor.material.MaterialForgingAfor
import com.nu.bom.core.technologies.manufacturings.alex.material.MaterialAluExtrusion
import com.nu.bom.core.technologies.manufacturings.cext.material.MaterialForgingCext
import com.nu.bom.core.technologies.manufacturings.chat.material.MaterialForgingChat
import com.nu.bom.core.technologies.manufacturings.chill.material.MaterialChillCasting
import com.nu.bom.core.technologies.manufacturings.core.material.MaterialStandardSand
import com.nu.bom.core.technologies.manufacturings.corestandalone.material.MaterialCoreSand
import com.nu.bom.core.technologies.manufacturings.crol.material.MaterialForgingCrol
import com.nu.bom.core.technologies.manufacturings.cube.material.MaterialBending
import com.nu.bom.core.technologies.manufacturings.dca.material.MaterialCasting
import com.nu.bom.core.technologies.manufacturings.dfor.material.MaterialForgingDfor
import com.nu.bom.core.technologies.manufacturings.fti.material.MaterialDieStamping
import com.nu.bom.core.technologies.manufacturings.fti.material.MaterialTransferStamping
import com.nu.bom.core.technologies.manufacturings.inj.material.MaterialPlastic
import com.nu.bom.core.technologies.manufacturings.inj.material.MaterialPlastic2
import com.nu.bom.core.technologies.manufacturings.last.material.MaterialLaminationStack
import com.nu.bom.core.technologies.manufacturings.magn.material.MaterialMagnet
import com.nu.bom.core.technologies.manufacturings.mill.material.MaterialMilling
import com.nu.bom.core.technologies.manufacturings.minj.material.MaterialMicroPlastic
import com.nu.bom.core.technologies.manufacturings.prec.material.MaterialPrecisionCasting
import com.nu.bom.core.technologies.manufacturings.rinj.material.MaterialRubber
import com.nu.bom.core.technologies.manufacturings.rrol.material.MaterialForgingRrol
import com.nu.bom.core.technologies.manufacturings.rswa.material.MaterialForgingRswa
import com.nu.bom.core.technologies.manufacturings.sand.material.MaterialSandCasting
import com.nu.bom.core.technologies.manufacturings.sint.material.MaterialSintering
import com.nu.bom.core.technologies.manufacturings.turn.material.MaterialTurningBart
import com.nu.bom.core.technologies.manufacturings.turn.material.MaterialTurningRawt
import com.nu.bom.core.technologies.manufacturings.vacuumprec.material.MaterialVacuumPrecisionCasting
import com.nu.bom.core.technologies.manufacturings.waxmodel.material.MaterialWaxModel
import com.nu.bom.core.technologies.manufacturings.waxrunnersystem.material.MaterialWaxRunnerSystem
import com.nu.bom.core.technologies.manufacturings.what.material.MaterialForgingWhat
import com.nu.bom.core.utils.doOnEmpty
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

object ManufacturingStepUtils {
    fun getTechnologyForModularizedEntities(
        technologyKey: Text?,
        technologyModel: Text?,
        isolated: Boolean? = null,
    ): Text? {
        val isIsolated = isolated ?: technologyKey?.res.isNullOrBlank()
        return if (isIsolated) {
            requireNotNull(technologyModel) { "If entity is isolated, technologyModel field is required." }
            val model = Model.fromEntity(technologyModel.res)?.name
            Text(requireNotNull(model) { "Could not find model for technology model $technologyModel." })
        } else {
            technologyKey
        }
    }

    fun getExpectedMaterialClass(technology: Text): Text {
        val expectedMaterialClassName =
            when (Model.valueOf(technology.res)) {
                Model.AFOR -> MaterialForgingAfor::class
                Model.ALEX -> MaterialAluExtrusion::class
                Model.BART -> MaterialTurningBart::class
                Model.CEXT -> MaterialForgingCext::class
                Model.CHAT -> MaterialForgingChat::class
                Model.CHILL -> MaterialChillCasting::class
                Model.CORE -> MaterialStandardSand::class
                Model.CORES -> MaterialCoreSand::class
                Model.CROL -> MaterialForgingCrol::class
                Model.CUBE -> MaterialBending::class
                Model.DCA -> MaterialCasting::class
                Model.DFOR -> MaterialForgingDfor::class
                Model.DFORT -> MaterialForgingDfor::class
                Model.FTIPDS -> MaterialDieStamping::class
                Model.FTITDS -> MaterialTransferStamping::class
                Model.INJ -> MaterialPlastic::class
                Model.INJ2 -> MaterialPlastic2::class
                Model.LAST -> MaterialLaminationStack::class
                Model.MAGN -> MaterialMagnet::class
                Model.MILL -> MaterialMilling::class
                Model.MINJ -> MaterialMicroPlastic::class
                Model.PREC -> MaterialPrecisionCasting::class
                Model.RAWT -> MaterialTurningRawt::class
                Model.RINJ -> MaterialRubber::class
                Model.RROL -> MaterialForgingRrol::class
                Model.RSWA -> MaterialForgingRswa::class
                Model.SAND -> MaterialSandCasting::class
                Model.SINT -> MaterialSintering::class
                Model.VPREC -> MaterialVacuumPrecisionCasting::class
                Model.WAXMODEL -> MaterialWaxModel::class
                Model.WAXRUNNERSYS, Model.TEST -> MaterialWaxRunnerSystem::class
                Model.WHAT -> MaterialForgingWhat::class
                Model.MANUAL, Model.CERAMIC_MOLD, Model.CERA, Model.WAXCLUSTER, Model.PCB, Model.PCBA, Model.PBOX ->
                    throw InternalServerException(
                        ErrorCode.FIELD_CALCULATION_ERROR,
                        "No expected material class for technology ${technology.res}",
                    )
            }
        return Text(expectedMaterialClassName.java.simpleName)
    }

    fun <T, R : Comparable<R>> Flux<T>.minByOrTemplateNotFoundException(
        selector: (T) -> R,
        lookupName: String,
        lookupInputs: Map<String, Any>,
        causeType: TemplateNotFoundCauseType = TemplateNotFoundCauseType.NOT_SPECIFIED_CAUSE,
    ): Mono<T> =
        this
            .doOnEmpty {
                throw TemplateNotFoundException(lookupName, lookupInputs, causeType)
            }.collectList()
            .map {
                it.minBy(selector)
            }

    fun <T> Flux<T>.singleLookupEntryOrError(
        lookupName: String,
        lookupKey: String,
    ): Mono<T> =
        this
            .single()
            .doOnError { error ->
                when (error) {
                    is NoSuchElementException ->
                        throw InternalServerException(ErrorCode.LOOKUP_ENTRY_NOT_FOUND, "Entry $lookupKey was found on $lookupName")
                    is IndexOutOfBoundsException ->
                        throw InternalServerException(
                            ErrorCode.MULTIPLE_LOOKUP_ENTRIES_FOUND,
                            "Multiple matches for $lookupKey on $lookupName",
                        )
                    else -> throw error
                }
            }
}
