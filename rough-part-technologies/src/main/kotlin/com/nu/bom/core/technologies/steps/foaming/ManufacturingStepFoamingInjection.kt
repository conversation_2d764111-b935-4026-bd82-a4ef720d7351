package com.nu.bom.core.technologies.steps.foaming

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityLinkField
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.ExpectedParents
import com.nu.bom.core.manufacturing.annotations.ExternalDependency
import com.nu.bom.core.manufacturing.annotations.FieldIndex
import com.nu.bom.core.manufacturing.annotations.Modularized
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.Diffusivity
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.FoamingType
import com.nu.bom.core.manufacturing.fieldTypes.InjectionType
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.PartQuality
import com.nu.bom.core.manufacturing.fieldTypes.Pressure
import com.nu.bom.core.manufacturing.fieldTypes.PressureUnits
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.RiskOfInternalStress
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.configuration.InjectionConfigurationField
import com.nu.bom.core.technologies.lookups.foamingInjectionTemplatesReader
import com.nu.bom.core.technologies.steps.plasticinj.ManufacturingStepPlasticInjection2
import com.nu.bom.core.technologies.steps.plasticinj.consumable.FoamingAgent
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.math.BigDecimal

@EntityType(Entities.MANUFACTURING_STEP)
@Modularized(
    technologies = [Model.INJ2],
    parents = [
        ExpectedParents(model = Model.INJ2, type = Entities.MANUFACTURING),
        ExpectedParents(model = Model.INJ2, type = Entities.PROCESSED_MATERIAL),
    ],
    dimensions = [Dimension.Selection.NUMBER],
    userCreatable = true,
)
class ManufacturingStepFoamingInjection(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = ManufacturingStepPlasticInjection2(name)

    companion object {
        const val CLASS_NAME = "ManufacturingStepFoamingInjection"
    }

    // region Fields from MANUFACTURING

    @Parent(Entities.MANUFACTURING)
    @ExternalDependency(section = ExternalDependency.SECTIONS.SELF)
    @FieldIndex(index = 10)
    fun foaming(): FoamingType? = null

    @Parent(Entities.MANUFACTURING)
    @ExternalDependency(section = ExternalDependency.SECTIONS.SELF)
    @FieldIndex(index = 20)
    fun foamingDensity(): Density? = null

    // endregion

    // region Fields from MATERIAL

    @EntityLinkField(providerField = "linkedMaterial", "adjustedThermalDiffusivityForFoaming")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 161)
    @DefaultUnit(DefaultUnit.QMM_PER_SECONDS)
    fun adjustedThermalDiffusivityForFoaming(): Diffusivity? = null

    @EntityLinkField(providerField = "linkedMaterial", "density")
    @FieldIndex(index = 50)
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    fun density(): Density? = null

    // endregion

    @ReadOnly
    fun injectionType(): InjectionType = InjectionType.FOAMING

    fun minInternalMoldPressureCalc(foaming: FoamingType): Pressure =
        when (foaming.res) {
            FoamingType.Selection.PHYSICAL -> 30.0
            FoamingType.Selection.CHEMICAL -> 4.0
        }.let { Pressure(it.toBigDecimal(), PressureUnits.BAR) }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 70)
    @DefaultUnit(DefaultUnit.SECOND)
    fun holdingPressureTime(): Time = Time(0.5, TimeUnits.SECOND)

    fun maxInternalMoldPressureCalc(maxInternalMoldPressure: Pressure): Pressure {
        val foamingFactor = Rate(0.5)
        return maxInternalMoldPressure * foamingFactor
    }

    fun internalThermalDiffusivity(adjustedThermalDiffusivityForFoaming: Diffusivity?): Diffusivity? = adjustedThermalDiffusivityForFoaming

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 39)
    @DefaultUnit(DefaultUnit.BAR)
    fun internalMoldPressure(
        injectionConfiguration: InjectionConfigurationField,
        ratioFlowPathToWall: Num,
        maxWallThickness: Length,
        materialFactor: Rate,
        minInternalMoldPressureCalc: Pressure,
        maxInternalMoldPressureCalc: Pressure,
        partQuality: PartQuality,
        riskOfInternalStress: RiskOfInternalStress,
        injectionType: InjectionType,
    ): Pressure =
        services.getInjectionUtilsService().internalMoldPressureImpl(
            injectionConfiguration,
            ratioFlowPathToWall,
            maxWallThickness,
            materialFactor,
            minInternalMoldPressureCalc,
            maxInternalMoldPressureCalc,
            partQuality,
            riskOfInternalStress,
            injectionType,
        )

    fun templateNameExtension(
        foaming: FoamingType,
        shotWeightPerCycle: List<QuantityUnit>?,
    ): Mono<Text> {
        val shotWeightPerCycleSum = sum(shotWeightPerCycle)
        return services
            .getLookupTable(
                "ManufacturingFoamingHelper_Template",
                foamingInjectionTemplatesReader,
            ).filter {
                val foamingType = it.foaming == foaming
                val shotWeight =
                    when {
                        shotWeightPerCycleSum.res >= BigDecimal(1) && it.templateName.contains("large") -> true
                        shotWeightPerCycleSum.res <= BigDecimal(0.25) && it.templateName.contains("small") -> true
                        shotWeightPerCycleSum.res >
                            BigDecimal(
                                0.25,
                            ) &&
                            shotWeightPerCycleSum.res < BigDecimal(1) &&
                            it.templateName.contains("medium") -> true
                        else -> false
                    }
                foamingType && shotWeight
            }.single()
            .map {
                Text(it.templateName)
            }
    }

    // region Entity creation

    @EntityCreation(Entities.MACHINE)
    fun createMachinesExtension(
        injectionType: InjectionType,
        templateNameExtension: Text?,
    ): Flux<ManufacturingEntity>? =
        when (injectionType.res) {
            InjectionType.Selection.FOAMING ->
                createEntitiesFromTemplate(
                    name = templateNameExtension?.res + "_Machine",
                    location = "Global",
                )
            InjectionType.Selection.NORMAL_INJECTION,
            InjectionType.Selection.INSERT,
            -> null
        }

    @EntityCreation(Entities.CONSUMABLE)
    fun createConsumables(foaming: FoamingType): Mono<ManufacturingEntity> =
        when (foaming.res) {
            FoamingType.Selection.CHEMICAL -> {
                createEntityWithNewMasterdata(
                    name = "Foaming agent",
                    entityType = Entities.CONSUMABLE,
                    clazz = FoamingAgent::class.toString(),
                    masterDataType = MasterDataType.CONSUMABLE,
                    masterDataKey = "Foaming agent",
                )
            }
            FoamingType.Selection.PHYSICAL -> {
                createEntityWithNewMasterdata(
                    name = "Nitrogen",
                    entityType = Entities.CONSUMABLE,
                    clazz = FoamingAgent::class.toString(),
                    masterDataType = MasterDataType.CONSUMABLE,
                    masterDataKey = "Nitrogen",
                )
            }
        }

    // endregion

    @SummaryView(SummaryView.PROCESS, 40, fieldName = "cycleTimePlasticInjection")
    fun cycleTimeSummaryView(cycleTime: CycleTime) = cycleTime

    @SummaryView(SummaryView.PROCESS, 50, fieldName = "manufacturingScrapRatePlasticInjection")
    fun manufacturingScrapRateSummaryView(manufacturingScrapRate: Rate): Rate = manufacturingScrapRate
}
