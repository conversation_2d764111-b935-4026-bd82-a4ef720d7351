package com.nu.bom.core.technologies.manufacturings.sand.material

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityProvider
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.OrderedEntityCreation
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ShapedMaterial
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataCategory
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.CoreToPartRatio
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.DensityUnits
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.MaterialSubstances
import com.nu.bom.core.manufacturing.fieldTypes.NumberOfMoldsSandCasting
import com.nu.bom.core.manufacturing.fieldTypes.ObjectIdField
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeCleanness
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeLargeCasting
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeSandFettling
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.nu.bom.core.technologies.behaviours.CleaningBehaviour
import com.nu.bom.core.technologies.service.ShapeLookupReaderService
import com.nu.bom.core.technologies.steps.largecasting.ManufacturingStepLargeCasting
import com.nu.bom.core.technologies.steps.largecastingcooling.ManufacturingStepLargeCastingCooling
import com.nu.bom.core.technologies.steps.largecastingfacing.ManufacturingStepLargeCastingFacingCoating
import com.nu.bom.core.technologies.steps.largecastingmolding.ManufacturingStepLargeCastingMolding1
import com.nu.bom.core.technologies.steps.largecastingmolding.ManufacturingStepLargeCastingMolding2
import com.nu.bom.core.technologies.steps.melting.ManufacturingStepSandMelting
import com.nu.bom.core.technologies.steps.moldboxassembly.ManufacturingStepLargeCastingMoldBoxAssembly
import com.nu.bom.core.technologies.steps.sandcasting.ManufacturingStepSandCasting
import com.nu.bom.core.technologies.steps.sandfettling.ManufacturingStepSandFettling
import com.nu.bom.core.technologies.steps.shotblastingdeburring.ManufacturingStepShotBlastingDeburring
import com.nu.bom.core.technologies.steps.unpacking.ManufacturingStepLargeCastingUnpacking
import com.nu.bom.core.technologies.steps.visualinspection.ManufacturingStepVisualInspection
import com.nu.bom.core.utils.annotations.TsetSuppress
import reactor.core.publisher.Mono
import java.math.BigDecimal

@EntityType(Entities.PROCESSED_MATERIAL)
class SandCastedMaterial(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = ShapedMaterial(name)

    override val behaviours: List<ManufacturingEntity> =
        listOf(
            CleaningBehaviour("CleaningBehaviour"),
        )

    @ObjectView(ObjectView.NONE, 0)
    fun displayDesignation(
        designation: Text?,
        entityDesignation: Text,
    ): Text = designation ?: entityDesignation

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun relativeCoreSize(): Rate = Rate(0.0)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun hasCore(): SelectableBoolean? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun coreToPartRatio(): CoreToPartRatio = CoreToPartRatio(CoreToPartRatio.Selection.NORMAL_PACKAGE)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun coreAssembly(): SelectableBoolean = SelectableBoolean.FALSE

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun optionalVisualInspection(): SelectableBoolean = SelectableBoolean.FALSE

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun sandFettlingType(): StepSubTypeSandFettling = StepSubTypeSandFettling.NO_FETTLING

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun sandCastingLine(): StepSubTypeLargeCasting? = null

    @Input
    fun materialClass() = Text(MaterialSandCasting::class.qualifiedName!!)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @TsetSuppress("tset:engine:field-override-configuration-value")
    fun technology() = Text("SAND")

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun burrFree(): SelectableBoolean? = null

    fun needCoreSizing(
        @Parent(Entities.MANUFACTURING)
        materialName: Text,
    ): Bool = Bool(!materialName.res.contains("Al"))

    @SummaryView(SummaryView.PROCESS, 15)
    fun coreCount(
        coreToPartRatio: CoreToPartRatio,
        hasCore: SelectableBoolean,
    ): Pieces =
        when {
            hasCore.toBoolean() -> Pieces(1.0 / coreToPartRatio.res.value)
            else -> Pieces(BigDecimal.ZERO)
        }

    @Input
    fun coreLengthRatio(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
        relativeCoreSize: Rate,
    ): Mono<Rate> =
        lookupReaderService
            .getSandShapeData(calculationContext!!.accessCheck, shapeId)
            .map {
                Rate(it.coreToPartRatioLength)
            }.map {
                if (it.res.compareTo(BigDecimal.ZERO) == 0) {
                    relativeCoreSize
                } else {
                    it
                }
            }

    @Input
    fun coreWidthRatio(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
        relativeCoreSize: Rate,
    ): Mono<Rate> =
        lookupReaderService
            .getSandShapeData(calculationContext!!.accessCheck, shapeId)
            .map {
                Rate(it.coreToPartRatioWidth)
            }.map {
                if (it.res.compareTo(BigDecimal.ZERO) == 0) {
                    relativeCoreSize
                } else {
                    it
                }
            }

    @Input
    fun coreHeightRatio(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
        relativeCoreSize: Rate,
    ): Mono<Rate> =
        lookupReaderService
            .getSandShapeData(calculationContext!!.accessCheck, shapeId)
            .map {
                Rate(it.coreToPartRatioHeight)
            }.map {
                if (it.res.compareTo(BigDecimal.ZERO) == 0) {
                    relativeCoreSize
                } else {
                    it
                }
            }

    @Input
    fun coreLengthBase(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
    ): Mono<Text> =
        lookupReaderService.getSandShapeData(calculationContext!!.accessCheck, shapeId).map {
            Text(it.coreLengthBase)
        }

    @Input
    fun coreWidthBase(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
    ): Mono<Text> =
        lookupReaderService.getSandShapeData(calculationContext!!.accessCheck, shapeId).map {
            Text(it.coreWidthBase)
        }

    @Input
    fun coreHeightBase(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
    ): Mono<Text> =
        lookupReaderService.getSandShapeData(calculationContext!!.accessCheck, shapeId).map {
            Text(it.coreHeightBase)
        }

    fun shapeInputGroup(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
    ) = lookupReaderService.getInputGroupFromShapeLookup(calculationContext!!.accessCheck, shapeId, Model.SAND)

    fun numberOfMolds(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
    ) = lookupReaderService.getSandShapeData(calculationContext!!.accessCheck, shapeId).map { (it.numberOfMolds) }

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun cleanness(): StepSubTypeCleanness? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @SummaryView(SummaryView.PROCESS, 300, "cleaning")
    fun cleaningNeeded(): SelectableBoolean = SelectableBoolean.FALSE

    @OrderedEntityCreation
    fun steps() =
        arrayOf(
            "largeCastingMoldingTop",
            "largeCastingMoldingTop2",
            "largeCastingMoldingBottom",
            "largeCastingMoldingBottom2",
            "largeCastingFacingTop",
            "largeCastingFacingBottom",
            "largeCastingMoldBoxAssembly",
            "melting",
            "sandCasting",
            "largeCastingCooling",
            "largeCastingUnpacking",
            "shotBlasting",
            "fettling",
            "cleaning",
            "visualInspection",
        )

    fun largeCastingGroupId() = ObjectIdField()

    @EntityProvider
    fun largeCastingMoldingTop(
        sandCastingLine: StepSubTypeLargeCasting,
        largeCastingGroupId: ObjectIdField,
    ) = if (sandCastingLine == StepSubTypeLargeCasting.SEMI_AUTOMATED) {
        createEntity(
            name = "Mold upper moldbox",
            clazz = ManufacturingStepLargeCastingMolding1::class,
            entityType = Entities.MANUFACTURING_STEP,
            fields = mapOf("groupId" to largeCastingGroupId),
        )
    } else {
        null
    }

    @EntityProvider
    fun largeCastingMoldingTop2(
        sandCastingLine: StepSubTypeLargeCasting,
        largeCastingGroupId: ObjectIdField,
        numberOfMolds: NumberOfMoldsSandCasting,
    ) = if (sandCastingLine == StepSubTypeLargeCasting.SEMI_AUTOMATED && numberOfMolds == NumberOfMoldsSandCasting.FOUR) {
        createEntity(
            name = "Mold upper moldbox 2",
            clazz = ManufacturingStepLargeCastingMolding1::class,
            entityType = Entities.MANUFACTURING_STEP,
            fields = mapOf("groupId" to largeCastingGroupId),
        )
    } else {
        null
    }

    @EntityProvider
    fun largeCastingMoldingBottom(
        sandCastingLine: StepSubTypeLargeCasting,
        largeCastingGroupId: ObjectIdField,
    ) = if (sandCastingLine == StepSubTypeLargeCasting.SEMI_AUTOMATED) {
        createEntity(
            name = "Mold lower moldbox",
            clazz = ManufacturingStepLargeCastingMolding2::class,
            entityType = Entities.MANUFACTURING_STEP,
            fields = mapOf("groupId" to largeCastingGroupId),
        )
    } else {
        null
    }

    @EntityProvider
    fun largeCastingMoldingBottom2(
        sandCastingLine: StepSubTypeLargeCasting,
        largeCastingGroupId: ObjectIdField,
        numberOfMolds: NumberOfMoldsSandCasting,
    ) = if (sandCastingLine == StepSubTypeLargeCasting.SEMI_AUTOMATED && numberOfMolds == NumberOfMoldsSandCasting.FOUR) {
        createEntity(
            name = "Mold lower moldbox 2",
            clazz = ManufacturingStepLargeCastingMolding2::class,
            entityType = Entities.MANUFACTURING_STEP,
            fields = mapOf("groupId" to largeCastingGroupId),
        )
    } else {
        null
    }

    @EntityProvider
    fun largeCastingFacingTop(
        sandCastingLine: StepSubTypeLargeCasting,
        largeCastingGroupId: ObjectIdField,
    ) = if (sandCastingLine == StepSubTypeLargeCasting.SEMI_AUTOMATED) {
        createEntity(
            name = "Facing /coating upper moldbox",
            clazz = ManufacturingStepLargeCastingFacingCoating::class,
            entityType = Entities.MANUFACTURING_STEP,
            fields = mapOf("groupId" to largeCastingGroupId),
        )
    } else {
        null
    }

    @EntityProvider
    fun largeCastingFacingBottom(
        sandCastingLine: StepSubTypeLargeCasting,
        largeCastingGroupId: ObjectIdField,
    ) = if (sandCastingLine == StepSubTypeLargeCasting.SEMI_AUTOMATED) {
        createEntity(
            name = "Facing / coating lower moldbox",
            clazz = ManufacturingStepLargeCastingFacingCoating::class,
            entityType = Entities.MANUFACTURING_STEP,
            fields = mapOf("groupId" to largeCastingGroupId),
        )
    } else {
        null
    }

    @EntityProvider
    fun largeCastingMoldBoxAssembly(
        sandCastingLine: StepSubTypeLargeCasting,
        largeCastingGroupId: ObjectIdField,
    ) = if (sandCastingLine == StepSubTypeLargeCasting.SEMI_AUTOMATED) {
        createEntity(
            name = "Mold box assembly",
            clazz = ManufacturingStepLargeCastingMoldBoxAssembly::class,
            entityType = Entities.MANUFACTURING_STEP,
            fields = mapOf("groupId" to largeCastingGroupId),
        )
    } else {
        null
    }

    @EntityProvider
    fun melting() =
        createEntity(
            name = "ManufacturingStepSandMelting",
            clazz = ManufacturingStepSandMelting::class,
            entityType = Entities.MANUFACTURING_STEP,
        )

    @EntityProvider
    fun sandCasting(
        sandCastingLine: StepSubTypeLargeCasting,
        largeCastingGroupId: ObjectIdField,
    ) = if (sandCastingLine == StepSubTypeLargeCasting.SEMI_AUTOMATED) {
        createEntity(
            name = "ManufacturingStepSandCasting",
            clazz = ManufacturingStepLargeCasting::class,
            entityType = Entities.MANUFACTURING_STEP,
            fields = mapOf("groupId" to largeCastingGroupId),
        )
    } else {
        createEntity(
            name = "ManufacturingStepSandCasting",
            clazz = ManufacturingStepSandCasting::class,
            entityType = Entities.MANUFACTURING_STEP,
        )
    }

    @EntityProvider
    fun largeCastingCooling(sandCastingLine: StepSubTypeLargeCasting) =
        if (sandCastingLine == StepSubTypeLargeCasting.SEMI_AUTOMATED) {
            createEntity(
                name = "Cooling",
                clazz = ManufacturingStepLargeCastingCooling::class,
                entityType = Entities.MANUFACTURING_STEP,
            )
        } else {
            null
        }

    @EntityProvider
    fun largeCastingUnpacking(
        sandCastingLine: StepSubTypeLargeCasting,
        largeCastingGroupId: ObjectIdField,
    ) = if (sandCastingLine == StepSubTypeLargeCasting.SEMI_AUTOMATED) {
        createEntity(
            name = "Unpacking",
            clazz = ManufacturingStepLargeCastingUnpacking::class,
            entityType = Entities.MANUFACTURING_STEP,
            fields = mapOf("groupId" to largeCastingGroupId),
        )
    } else {
        null
    }

    @EntityProvider
    fun shotBlasting() =
        createEntity(
            name = "ManufacturingStepShotBlasting",
            clazz = ManufacturingStepShotBlastingDeburring::class,
            entityType = Entities.MANUFACTURING_STEP,
        )

    @EntityProvider
    fun fettling(sandFettlingType: StepSubTypeSandFettling) =
        if (sandFettlingType != StepSubTypeSandFettling.NO_FETTLING) {
            createEntity(
                name = "ManufacturingStepFettling",
                clazz = ManufacturingStepSandFettling::class,
                entityType = Entities.MANUFACTURING_STEP,
            )
        } else {
            null
        }

    @EntityProvider
    fun visualInspection(optionalVisualInspection: SelectableBoolean) =
        if (optionalVisualInspection == SelectableBoolean.TRUE) {
            createEntity(
                name = "ManufacturingStepVisualInspection",
                clazz = ManufacturingStepVisualInspection::class,
                entityType = Entities.MANUFACTURING_STEP,
            )
        } else {
            null
        }

    fun shapeName(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
    ): Mono<Text> =
        lookupReaderService.getSandShapeData(calculationContext!!.accessCheck, shapeId).map {
            Text(it.displayShape)
        }

    @Input
    fun coreLength(
        partLength: Length,
        coreLengthRatio: Rate,
        partOuterDiameter: Length,
        partWidth: Length,
        partHeight: Length,
        coreLengthBase: Text,
    ): Length =
        when (coreLengthBase.res) {
            "partHeight" -> partHeight * coreLengthRatio
            "partWidth" -> partWidth * coreLengthRatio
            "partOuterDiameter" -> partOuterDiameter * coreLengthRatio
            else -> partLength * coreLengthRatio
        }

    @Input
    fun coreHeight(
        partHeight: Length,
        coreHeightRatio: Rate,
        partOuterDiameter: Length,
        partLength: Length,
        partWidth: Length,
        coreHeightBase: Text,
    ): Length =
        when (coreHeightBase.res) {
            "partHeight" -> partHeight * coreHeightRatio
            "partWidth" -> partWidth * coreHeightRatio
            "partOuterDiameter" -> partOuterDiameter * coreHeightRatio
            else -> partLength * coreHeightRatio
        }

    @Input
    fun coreWidth(
        partWidth: Length,
        coreWidthRatio: Rate,
        partOuterDiameter: Length,
        partLength: Length,
        partHeight: Length,
        coreWidthBase: Text,
    ): Length =
        when (coreWidthBase.res) {
            "partHeight" -> partHeight * coreWidthRatio
            "partWidth" -> partWidth * coreWidthRatio
            "partOuterDiameter" -> partOuterDiameter * coreWidthRatio
            else -> partLength * coreWidthRatio
        }

    // TODO -> lookup?
    @Input
    val density: Density = Density(1600.toBigDecimal(), DensityUnits.KILOGRAM_PER_CM)

    fun materialSubstances(materialName: Text): Mono<MaterialSubstances> =
        services
            .getMasterData(
                accessCheck = calculationContext().accessCheck,
                category = MasterDataCategory.MATERIAL,
                key = materialName.res,
                location = "Global",
                year = calculationContext!!.year,
            ).map {
                when (it.hasField("materialSubstances")) {
                    true -> it.getField<MaterialSubstances>("materialSubstances")
                    false -> MaterialSubstances(emptyList())
                }
            }

    @Input
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun coreNetWeightPerPart(
        shapeInputGroup: Text,
        partLength: Length,
        partWidth: Length,
        partHeight: Length,
        partOuterDiameter: Length,
        relativeCoreSize: Rate,
        coreToPartRatio: CoreToPartRatio,
        density: Density,
    ): Weight {
        val boxLength =
            when (shapeInputGroup.res) {
                "cylinder" -> partOuterDiameter
                "hollowRing" -> partOuterDiameter
                else -> partLength
            }
        val boxWidth =
            when (shapeInputGroup.res) {
                "cuboid" -> partWidth
                "hollowCuboid" -> partWidth
                else -> partOuterDiameter
            }
        val boxHeight =
            when (shapeInputGroup.res) {
                "pipe" -> partOuterDiameter
                "hollowCycle" -> partOuterDiameter
                else -> partHeight
            }
        return Weight(
            (boxLength * boxWidth * boxHeight * density * relativeCoreSize * coreToPartRatio.res.value.toBigDecimal()).res,
            WeightUnits.KILOGRAM,
        )
    }
}
