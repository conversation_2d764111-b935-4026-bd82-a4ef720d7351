package com.nu.bom.core.technologies.manufacturings.sint.material

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.ExpectedParents
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Modularized
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.RawMaterialPowder
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.MaterialCostMode
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.technologies.manufacturings.sint.ManufacturingSintering
import java.math.BigDecimal

@EntityType(Entities.MATERIAL)
@Modularized(
    technologies = [Model.SINT],
    parents = [
        ExpectedParents(model = Model.SINT, type = Entities.MANUFACTURING, klass = ManufacturingSintering::class),
        ExpectedParents(model = Model.SINT, type = Entities.PROCESSED_MATERIAL, klass = SinteredMaterial::class),
    ],
    dimensions = [Dimension.Selection.NUMBER],
    userCreatable = false,
)
class MaterialSintering(name: String) : ManufacturingEntity(name) {
    override val extends = RawMaterialPowder(name)

    @Input
    fun reuseOfScrap() = Bool(false)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @ObjectView(ObjectView.MATERIAL, 1)
    @ReadOnly
    fun materialName(): Text? = null

    @Input
    @ObjectView(ObjectView.MATERIAL, 6)
    fun lossRate() = Rate(0.03)

    @Input
    fun recyclingRate() = Rate(0.0)

    fun surchargeWeight(): QuantityUnit = QuantityUnit(BigDecimal.ZERO)

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun surchargeWeightPerPart(surchargeWeight: QuantityUnit): QuantityUnit {
        return surchargeWeight
    }

    @Input
    @ObjectView(ObjectView.MATERIAL, 4)
    @ReadOnly
    @Parent(Entities.PROCESSED_MATERIAL)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun netWeightPerPart(): QuantityUnit? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun scrapWeightPerPart(): QuantityUnit? = null

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 5)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun weightBeforeScrapPerPart(
        netWeightPerPart: QuantityUnit,
        surchargeWeight: QuantityUnit,
    ): QuantityUnit {
        return netWeightPerPart + surchargeWeight
    }

    @ReadOnly
    fun lossWeight(
        weightBeforeScrapPerPart: QuantityUnit,
        lossRate: Rate,
    ): QuantityUnit {
        return weightBeforeScrapPerPart * lossRate
    }

    @ReadOnly
    @ObjectView(ObjectView.MATERIAL, 70)
    fun pressWeight(
        weightBeforeScrapPerPart: QuantityUnit,
        lossWeight: QuantityUnit,
    ): QuantityUnit {
        return weightBeforeScrapPerPart + lossWeight
    }

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun surchargeLossWeight(
        surchargeWeightPerPart: QuantityUnit,
        recyclingRate: Rate,
    ): QuantityUnit {
        return surchargeWeightPerPart * (1.toBigDecimal() - recyclingRate.res)
    }

    @ReadOnly
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun deployedWeightPerPart(
        netWeightPerPart: QuantityUnit,
        surchargeLossWeight: QuantityUnit,
        lossWeight: QuantityUnit,
    ): QuantityUnit {
        return netWeightPerPart + surchargeLossWeight + lossWeight
    }

    @ReadOnly
    fun irretrievableScrapPerPart(
        surchargeLossWeight: QuantityUnit,
        lossWeight: QuantityUnit,
    ): QuantityUnit {
        return surchargeLossWeight + lossWeight
    }

    @ReadOnly
    fun retrievableScrapPerPart(
        surchargeWeightPerPart: QuantityUnit,
        recyclingRate: Rate,
    ): QuantityUnit {
        return surchargeWeightPerPart * recyclingRate
    }

    /****** RawMaterials override **********/

    fun materialCostMode(): MaterialCostMode = MaterialCostMode.SELL_NOTHING

    fun materialWastePrice(pricePerUnit: Money): Money {
        return Money(0.2.toBigDecimal() * pricePerUnit.res)
    }

    fun materialRecyclingPrice(pricePerUnit: Money): Money {
        return Money(0.4.toBigDecimal() * pricePerUnit.res)
    }

    fun quantityForExport(deployedWeightPerPart: QuantityUnit): QuantityUnit {
        return deployedWeightPerPart
    }
}
