package com.nu.bom.core.technologies.steps.cuttolength

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DisableEntityCreationInModularizedEntity
import com.nu.bom.core.manufacturing.annotations.DontSortOptions
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityLinkField
import com.nu.bom.core.manufacturing.annotations.EntityLinkProvider
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.ExpectedParents
import com.nu.bom.core.manufacturing.annotations.ExternalDependency
import com.nu.bom.core.manufacturing.annotations.FieldIndex
import com.nu.bom.core.manufacturing.annotations.IgnoreForOverwrittenState
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Modularized
import com.nu.bom.core.manufacturing.annotations.Nocalc
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.RequiredFor
import com.nu.bom.core.manufacturing.annotations.Selectable
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.annotations.SubLabel
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.defaults.NoTolerance
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ManufacturingStep
import com.nu.bom.core.manufacturing.entities.MaterialCalculatorForging
import com.nu.bom.core.manufacturing.entities.SingleGroupManufacturingStep
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.ManufacturingStepType
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.AreaUnits
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.CastingAlloyMaterialGroup
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.EntityRef
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeCutToLength
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeCutToLengthProfile
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeCutToLengthProfileGeometry
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeMaterial
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeSaw
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeTolerancesDfor
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.technologies.lookups.sawingBladeDivisionFullMaterialReader
import com.nu.bom.core.technologies.lookups.sawingBladeDivisionHollowMaterialReader
import com.nu.bom.core.technologies.lookups.sawingCycleTimeReader
import com.nu.bom.core.technologies.manufacturings.dfor.material.MaterialForgingDfor
import com.nu.bom.core.technologies.service.ShapeLookupReaderService
import com.nu.bom.core.technologies.steps.ManufacturingStepUtils
import com.nu.bom.core.technologies.steps.afor.ManufacturingStepDieForgingAlu
import com.nu.bom.core.technologies.steps.burfreeforg.ManufacturingStepBurrFreeDieForging
import com.nu.bom.core.technologies.steps.coldextrusion.ManufacturingStepColdExtrusion
import com.nu.bom.core.technologies.steps.crol.ManufacturingStepCotterKeyRolling
import com.nu.bom.core.technologies.steps.cuttolength.systemparameter.SystemParameterCutToLength
import com.nu.bom.core.technologies.steps.dfor.ManufacturingStepDieForging
import com.nu.bom.core.technologies.steps.pfor.ManufacturingStepPrecisionForging
import com.nu.bom.core.technologies.steps.rotaryswaging.ManufacturingStepRotarySwaging
import com.nu.bom.core.technologies.steps.rrol.ManufacturingStepRingRolling
import com.nu.bom.core.utils.annotations.TsetSuppress
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono
import java.math.BigDecimal
import java.math.RoundingMode
import kotlin.math.sqrt

@EntityType(Entities.MANUFACTURING_STEP)
@Modularized(
    technologies = [Model.DFOR],
    parents = [
        ExpectedParents(model = Model.DFOR, type = Entities.MANUFACTURING),
        ExpectedParents(model = Model.DFOR, type = Entities.PROCESSED_MATERIAL),
    ],
    dimensions = [Dimension.Selection.NUMBER],
    userCreatable = true,
)
class ManufacturingStepCutToLength(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = SingleGroupManufacturingStep(name)

    private companion object {
        const val TEMPLATE_LOOKUP_NAME = "ManufacturingStepCutToLength_Templates"
        const val TEMPLATE_LOOKUP_NAME_CYCLE_TIME_SAWING = "ManufacturingStepCutToLength_CycleTimeSawing"
        const val TEMPLATE_LOOKUP_NAME_SAWING_BLADE_HOLLOW_MATERIAL = "ManufacturingStepCutToLength_SawingBladeDivision_HollowMaterial"
        const val TEMPLATE_LOOKUP_NAME_SAWING_BLADE_FULL_MATERIAL = "ManufacturingStepCutToLength_SawingBladeDivision_FullMaterial"
    }

    @Input
    @SubLabel
    @ReadOnly
    @Selectable(value = "manufacturingStepType", getDisplayNameFromPathQuery = true)
    fun manufacturingStepType(): Text = Text(ManufacturingStepType.FORGING_DIE_FORGING.name)

    @Input
    fun isLinkedToParent(
        @Default(NoTolerance::class)
        tolerance: StepSubTypeTolerancesDfor?,
    ): Bool = Bool(!isolated && tolerance == StepSubTypeTolerancesDfor.BURRFREE_DFOR)

    @Input
    fun internalPartsPerCycle() = QuantityUnit(BigDecimal.ONE)

    @Input
    fun utilizationRate(): Rate = Rate(0.85.toBigDecimal())

    @Input
    fun scrapRate(): Rate = Rate(0.002.toBigDecimal())

    fun technologyForModularization(
        technology: Text?,
        technologyModel: Text?,
    ): Text? = ManufacturingStepUtils.getTechnologyForModularizedEntities(technology, technologyModel, isolated)

    @Input
    @ReadOnly
    fun stepSubTypeSaw() = StepSubTypeSaw.HARD_METAL_CIRCULAR_SAW

    @Input
    @SpecialLink("SystemParameterCutToLength", "systemDownTime")
    @DefaultUnit(DefaultUnit.MINUTE)
    fun internalSystemDownTime(): Time? = null

    fun stepSubTypeCutToLengthProfile(technologyForModularization: Text): StepSubTypeCutToLengthProfile =
        when (technologyForModularization.res) {
            Model.RSWA.name -> StepSubTypeCutToLengthProfile.HOLLOW_MATERIAL
            else -> StepSubTypeCutToLengthProfile.FULL_MATERIAL
        }

    fun stepSubTypeCutToLengthProfileGeometry() = StepSubTypeCutToLengthProfileGeometry.ROUND_MATERIAL

    // region Fields from MATERIAL

    @ExternalDependency(ExternalDependency.SECTIONS.MATERIAL)
    @EntityLinkProvider(["MaterialForging"], entityClasses = [MaterialCalculatorForging::class, MaterialForgingDfor::class])
    @FieldIndex(index = 0)
    @Path("/api/link{bomPath}{branchPath}?entityClasses=MaterialCalculatorForging")
    fun linkedMaterial(): EntityRef? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterial", fieldName = "blankDiameter")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @FieldIndex(index = 20)
    fun blankDiameter(): Length? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterial", "materialGroup")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 25)
    @DontSortOptions
    fun materialGroup(): CastingAlloyMaterialGroup? = null

    fun stepSubTypeMaterial(materialGroup: CastingAlloyMaterialGroup): StepSubTypeMaterial = StepSubTypeMaterial(materialGroup)

    @Input
    @EntityLinkField(providerField = "linkedMaterial", fieldName = "blankLength")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @FieldIndex(index = 30)
    fun blankLength(): Length? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterial", fieldName = "barLength")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @DefaultUnit(DefaultUnit.METER)
    @FieldIndex(index = 40)
    fun barLength(): Length? = null

    // endregion
    // region Fields from PROCESSED_MATERIAL

    @Parent(Entities.MANUFACTURING)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 70)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun maxWallThickness(): Length? = null

    @EntityLinkProvider(
        entityRef = ["ManufacturingStepDieForging", "ManufacturingStepPrecisionForging", "ManufacturingStepBurrFreeDieForging"],
        entityClasses = [
            ManufacturingStep::class,
            ManufacturingStepDieForging::class,
            ManufacturingStepPrecisionForging::class,
            ManufacturingStepBurrFreeDieForging::class,
        ],
    )
    @ExternalDependency(section = ExternalDependency.SECTIONS.STEP)
    @Path("/api/link{bomPath}{branchPath}?entityClasses=ManufacturingStepDieForging")
    @FieldIndex(index = 10)
    @IgnoreForOverwrittenState
    fun linkedStep(): EntityRef? =
        getLinkedStep(
            listOf(
                ManufacturingStepDieForging::class,
                ManufacturingStepPrecisionForging::class,
                ManufacturingStepBurrFreeDieForging::class,
                // below is a workaround since @Default(NoTolerance::class) doesn't work with @EntityLinkField
                // tolerance now set at each step, will be reworked in future
                ManufacturingStepDieForgingAlu::class,
                ManufacturingStepRingRolling::class,
                ManufacturingStepColdExtrusion::class,
                ManufacturingStepRotarySwaging::class,
                ManufacturingStepCotterKeyRolling::class,
            ),
        )

    @Input
    @ExternalDependency(section = ExternalDependency.SECTIONS.STEP)
    @EntityLinkField(providerField = "linkedStep", "tolerance")
    @FieldIndex(index = 20)
    fun tolerance(): StepSubTypeTolerancesDfor? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @Path("/api/shapes/autocomplete?tech={field:technologyForModularization}")
    @FieldIndex(index = 10)
    fun shapeId(): Text? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    @TsetSuppress("tset:engine:field-override-configuration-value")
    fun technology(): Text? = null

    @Parent(Entities.PROCESSED_MATERIAL)
    fun materialName(): Text? = null

    @Parent(Entities.PROCESSED_MATERIAL)
    fun materialClass(): Text? = null

    // endregion

    @Input
    fun blankWidth() = Length(BigDecimal.ZERO, LengthUnits.METER)

    @Input
    fun blankHeight() = Length(BigDecimal.ZERO, LengthUnits.METER)

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 30)
    fun cuttingSpeed(
        stepSubTypeMaterial: StepSubTypeMaterial,
        stepSubTypeSaw: StepSubTypeSaw,
    ): Mono<Length> =
        services
            .getLookupTable(TEMPLATE_LOOKUP_NAME_CYCLE_TIME_SAWING, sawingCycleTimeReader)
            .filter {
                stepSubTypeSaw == it.stepSubTypeSaw && stepSubTypeMaterial == it.stepSubTypeMaterial
            }.single()
            .map { Length(it.cuttingSpeed.res, LengthUnits.METER) }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 35)
    fun feedPerTooth(
        stepSubTypeMaterial: StepSubTypeMaterial,
        stepSubTypeSaw: StepSubTypeSaw,
    ): Mono<Length> =
        services
            .getLookupTable(TEMPLATE_LOOKUP_NAME_CYCLE_TIME_SAWING, sawingCycleTimeReader)
            .filter {
                stepSubTypeSaw == it.stepSubTypeSaw && stepSubTypeMaterial == it.stepSubTypeMaterial
            }.single()
            .map { Length(it.feedPerTooth.inMillimeter, LengthUnits.MILLIMETER) }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 40)
    fun bundling(): Pieces = Pieces(1.0)

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 45)
    @DefaultUnit(DefaultUnit.QCM)
    fun cuttingSurface(
        stepSubTypeCutToLengthProfile: StepSubTypeCutToLengthProfile,
        stepSubTypeCutToLengthProfileGeometry: StepSubTypeCutToLengthProfileGeometry,
        blankDiameter: Length,
        blankWidth: Length,
        blankHeight: Length,
        maxWallThickness: Length,
        bundling: Pieces,
    ): Area =
        when {
            stepSubTypeCutToLengthProfile == StepSubTypeCutToLengthProfile.FULL_MATERIAL &&
                stepSubTypeCutToLengthProfileGeometry == StepSubTypeCutToLengthProfileGeometry.ROUND_MATERIAL ->
                Area(
                    Math.PI.toBigDecimal() * 0.25.toBigDecimal() * blankDiameter.inMillimeter.pow(2) * bundling.res,
                    AreaUnits.QMM,
                )

            stepSubTypeCutToLengthProfile == StepSubTypeCutToLengthProfile.FULL_MATERIAL &&
                stepSubTypeCutToLengthProfileGeometry == StepSubTypeCutToLengthProfileGeometry.SQUARE_MATERIAL ->
                Area(
                    blankWidth.inMillimeter * blankHeight.inMillimeter * bundling.res,
                    AreaUnits.QMM,
                )

            stepSubTypeCutToLengthProfile == StepSubTypeCutToLengthProfile.HOLLOW_MATERIAL &&
                stepSubTypeCutToLengthProfileGeometry == StepSubTypeCutToLengthProfileGeometry.ROUND_MATERIAL ->
                Area(
                    Math.PI.toBigDecimal() * 0.25.toBigDecimal() * (
                        blankDiameter.inMillimeter.pow(2) -
                            (blankDiameter.inMillimeter - 2.toBigDecimal() * maxWallThickness.inMillimeter).pow(
                                2,
                            )
                    ) * bundling.res,
                    AreaUnits.QMM,
                )

            stepSubTypeCutToLengthProfile == StepSubTypeCutToLengthProfile.HOLLOW_MATERIAL &&
                stepSubTypeCutToLengthProfileGeometry == StepSubTypeCutToLengthProfileGeometry.SQUARE_MATERIAL ->
                Area(
                    (
                        blankHeight.inMillimeter * blankWidth.inMillimeter -
                            (blankHeight.inMillimeter - 2.toBigDecimal() * maxWallThickness.inMillimeter) *
                            (blankWidth.inMillimeter - 2.toBigDecimal() * maxWallThickness.inMillimeter)
                    ) * bundling.res,
                    AreaUnits.QMM,
                )

            else -> throw IllegalArgumentException("not supported partinput")
        }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 50)
    @DefaultUnit(DefaultUnit.QCM)
    fun specificCuttingSurface(
        cuttingSurface: Area,
        sawingTimePerBundling: Time,
    ): Area = Area(cuttingSurface.inQmm / sawingTimePerBundling.inSeconds * 60.toBigDecimal(), AreaUnits.QMM)

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 60)
    @DefaultUnit(DefaultUnit.QCM)
    fun maxCuttingSurface(
        stepSubTypeMaterial: StepSubTypeMaterial,
        stepSubTypeSaw: StepSubTypeSaw,
    ): Mono<Area> =
        services
            .getLookupTable(TEMPLATE_LOOKUP_NAME_CYCLE_TIME_SAWING, sawingCycleTimeReader)
            .filter {
                stepSubTypeSaw == it.stepSubTypeSaw && stepSubTypeMaterial == it.stepSubTypeMaterial
            }.single()
            .map { Area((it.maxCuttingSurface.inQmm), AreaUnits.QMM) }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 70)
    fun sawingBladeDivision(
        maxWallThickness: Length,
        blankDiameter: Length,
        blankWidth: Length,
        stepSubTypeCutToLengthProfile: StepSubTypeCutToLengthProfile,
        stepSubTypeCutToLengthProfileGeometry: StepSubTypeCutToLengthProfileGeometry,
    ): Mono<Num> {
        val divisionHollowMaterial =
            services
                .getLookupTable(TEMPLATE_LOOKUP_NAME_SAWING_BLADE_HOLLOW_MATERIAL, sawingBladeDivisionHollowMaterialReader)
                .filter {
                    when (stepSubTypeCutToLengthProfileGeometry) {
                        StepSubTypeCutToLengthProfileGeometry.ROUND_MATERIAL ->
                            blankDiameter.res < it.blankDiameterOrWidth.res &&
                                maxWallThickness.res < it.maxWallThickness.res
                        else -> blankWidth.res < it.blankDiameterOrWidth.res && maxWallThickness.res < it.maxWallThickness.res
                    }
                }.elementAt(0)
                .map { it.sawingBladeDivision }

        val divisionFullMaterial =
            services
                .getLookupTable(TEMPLATE_LOOKUP_NAME_SAWING_BLADE_FULL_MATERIAL, sawingBladeDivisionFullMaterialReader)
                .filter {
                    when {
                        stepSubTypeCutToLengthProfileGeometry == StepSubTypeCutToLengthProfileGeometry.ROUND_MATERIAL &&
                            blankDiameter.inMillimeter > 19.toBigDecimal() ->
                            blankDiameter.res < it.maxBlankDiameter.res
                        else -> blankWidth.res < it.maxBlankDiameter.res
                    }
                }.elementAt(0)
                .map { it.sawingBladeDivision }

        return when (stepSubTypeCutToLengthProfile) {
            StepSubTypeCutToLengthProfile.HOLLOW_MATERIAL -> divisionHollowMaterial
            else -> divisionFullMaterial
        }
    }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 75)
    fun feedSpeed(
        sawingBladeDivision: Num,
        cuttingSpeed: Length,
        feedPerTooth: Length,
    ): Num = Num(sawingBladeDivision.res * cuttingSpeed.res * feedPerTooth.inMillimeter * 39.4.toBigDecimal())

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 80)
    fun cuttingLengthPerBundling(
        bundling: Pieces,
        blankDiameter: Length,
        blankWidth: Length,
        stepSubTypeCutToLengthProfileGeometry: StepSubTypeCutToLengthProfileGeometry,
    ): Length =
        when (stepSubTypeCutToLengthProfileGeometry) {
            StepSubTypeCutToLengthProfileGeometry.ROUND_MATERIAL ->
                Length(
                    blankDiameter.inMillimeter *
                        kotlin.math
                            .sqrt(bundling.res.toDouble())
                            .toBigDecimal()
                            .setScale(0, RoundingMode.UP),
                    LengthUnits.MILLIMETER,
                )

            else ->
                Length(
                    blankWidth.inMillimeter *
                        sqrt(bundling.res.toDouble())
                            .toBigDecimal()
                            .setScale(0, RoundingMode.UP),
                    LengthUnits.MILLIMETER,
                )
        }

    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun sawingBladeMinDiameter(
        stepSubTypeCutToLengthProfileGeometry: StepSubTypeCutToLengthProfileGeometry,
        blankDiameter: Length,
        blankWidth: Length,
        blankHeight: Length,
    ): Length =
        when {
            stepSubTypeCutToLengthProfileGeometry == StepSubTypeCutToLengthProfileGeometry.SQUARE_MATERIAL &&
                blankHeight.res > blankWidth.res ->
                Length(
                    (0.25.toBigDecimal() * blankHeight.inMillimeter).setScale(0, RoundingMode.UP) * 10.toBigDecimal(),
                    LengthUnits.MILLIMETER,
                )

            stepSubTypeCutToLengthProfileGeometry == StepSubTypeCutToLengthProfileGeometry.SQUARE_MATERIAL &&
                blankHeight.res <= blankWidth.res ->
                Length(
                    (0.25.toBigDecimal() * blankWidth.inMillimeter).setScale(0, RoundingMode.UP) * 10.toBigDecimal(),
                    LengthUnits.MILLIMETER,
                )

            else ->
                Length(
                    (0.25.toBigDecimal() * blankDiameter.inMillimeter).setScale(
                        0,
                        RoundingMode.UP,
                    ) * 10.toBigDecimal(),
                    LengthUnits.MILLIMETER,
                )
        }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 90)
    @DefaultUnit(DefaultUnit.SECOND)
    fun sawingTimePerBundling(
        cuttingLengthPerBundling: Length,
        feedSpeed: Num,
    ): Time = Time((cuttingLengthPerBundling.inMillimeter / feedSpeed.res) * 60.toBigDecimal(), TimeUnits.SECOND)

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 95)
    @DefaultUnit(DefaultUnit.SECOND)
    fun assemblyTime(blankLength: Length): Time =
        Time(0.00286.toBigDecimal() * blankLength.inMillimeter + 5.088.toBigDecimal(), TimeUnits.SECOND)

    fun timePerBundling(
        specificCuttingSurface: Area,
        cuttingSurface: Area,
        maxCuttingSurface: Area,
        sawingTimePerBundling: Time,
        assemblyTime: Time,
    ): Time {
        val res = (
            assemblyTime +
                when {
                    specificCuttingSurface.res > maxCuttingSurface.res ->
                        Time(
                            (cuttingSurface.inQmm / maxCuttingSurface.inQmm) * 60.toBigDecimal(),
                            TimeUnits.SECOND,
                        )

                    else -> sawingTimePerBundling
                }
        )
        return Time(res.res, TimeUnits.SECOND)
    }

    fun sawing(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
        technologyForModularization: Text,
    ): Mono<Bool> =
        when (technologyForModularization.res) {
            Model.CEXT.name ->
                lookupReaderService
                    .getCextShapeData(calculationContext!!.accessCheck, shapeId)
                    .map {
                        Bool(it.sawing)
                    }
            else -> Bool(false).toMono()
        }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 20)
    @ReadOnly
    @SummaryView(SummaryView.PROCESS, 21, "stepSubTypeCutToLength")
    fun stepSubTypeCutToLength(templateName: Text): StepSubTypeCutToLength =
        when {
            templateName.res.contains("Saw") -> StepSubTypeCutToLength.SAWING
            templateName.res.contains("Shear") -> StepSubTypeCutToLength.SHEARING
            else -> throw IllegalArgumentException("not supported step sub type")
        }

    private class TemplateLookupEntry(
        val templateName: String,
        val maxDiameter: Length,
        val stepSubTypeCutToLength: String,
    )

    private val machineTemplateLookupReader: (row: List<String>) -> TemplateLookupEntry = {
        TemplateLookupEntry(
            templateName = it[0],
            maxDiameter = Length(it[1].toBigDecimal(), LengthUnits.METER),
            stepSubTypeCutToLength = it[2],
        )
    }

    fun templateName(
        blankDiameter: Length,
        technologyForModularization: Text,
        sawing: Bool,
        stepSubTypeMaterial: StepSubTypeMaterial,
        tolerance: StepSubTypeTolerancesDfor?,
    ): Mono<Text> {
        val stepSubTypeCutToLength =
            when (technologyForModularization.res) {
                Model.CEXT.name -> stepSubTypeCutToLengthCEXT(blankDiameter, sawing)
                Model.DFOR.name -> stepSubTypeCutToLengthDFOR(stepSubTypeMaterial, tolerance)
                Model.RSWA.name -> StepSubTypeCutToLength.SAWING
                Model.AFOR.name -> stepSubTypeCutToLengthAFOR(stepSubTypeMaterial, blankDiameter)
                else -> StepSubTypeCutToLength.SHEARING
            }
        return services
            .getLookupTable(TEMPLATE_LOOKUP_NAME, machineTemplateLookupReader)
            .filter {
                val diffDiameter =
                    when {
                        it.maxDiameter.res >= blankDiameter.inMeter -> true
                        else -> false
                    }

                val diffType =
                    when {
                        StepSubTypeCutToLength.valueOf(it.stepSubTypeCutToLength) == stepSubTypeCutToLength -> true
                        else -> false
                    }

                diffDiameter && diffType
            }.collectList()
            .mapNotNull {
                it.minByOrNull { singleTemplate -> singleTemplate.maxDiameter.res }
            }.map {
                Text(it!!.templateName)
            }
    }

    @Nocalc
    private fun stepSubTypeCutToLengthCEXT(
        blankDiameter: Length,
        sawing: Bool,
    ): StepSubTypeCutToLength =
        when {
            blankDiameter.res <= 0.02.toBigDecimal() && sawing.isTrue() -> StepSubTypeCutToLength.SAWING
            blankDiameter.res <= 0.02.toBigDecimal() && sawing.isFalse() -> StepSubTypeCutToLength.SHEARING
            blankDiameter.res > 0.02.toBigDecimal() -> StepSubTypeCutToLength.SAWING
            else -> StepSubTypeCutToLength.SHEARING
        }

    @Nocalc
    private fun stepSubTypeCutToLengthDFOR(
        stepSubTypeMaterial: StepSubTypeMaterial,
        tolerance: StepSubTypeTolerancesDfor?,
    ): StepSubTypeCutToLength =
        when {
            stepSubTypeMaterial == StepSubTypeMaterial.ALLOYED_STEEL && tolerance == StepSubTypeTolerancesDfor.PRECISE_DFOR
            -> StepSubTypeCutToLength.SAWING
            stepSubTypeMaterial == StepSubTypeMaterial.ALLOYED_STEEL && tolerance == StepSubTypeTolerancesDfor.BURRFREE_DFOR
            -> StepSubTypeCutToLength.SAWING
            else -> StepSubTypeCutToLength.SHEARING
        }

    @Nocalc
    private fun stepSubTypeCutToLengthAFOR(
        stepSubTypeMaterial: StepSubTypeMaterial,
        blankDiameter: Length,
    ): StepSubTypeCutToLength =
        when {
            stepSubTypeMaterial == StepSubTypeMaterial.ALUMINIUM_MAGNESIUM && blankDiameter.res <= 0.05.toBigDecimal()
            -> StepSubTypeCutToLength.SHEARING

            stepSubTypeMaterial == StepSubTypeMaterial.ALUMINIUM_MAGNESIUM && blankDiameter.res > 0.05.toBigDecimal()
            -> StepSubTypeCutToLength.SAWING

            else -> StepSubTypeCutToLength.SHEARING
        }

    @EntityCreation(Entities.SYSTEM_PARAMETER)
    fun createSystemParameter(templateName: Text): Mono<SystemParameterCutToLength> =
        createSystemParameter(
            masterData = MasterDataType.SYSTEM_PARAMETER_CUT_TO_LENGTH,
            clazz = SystemParameterCutToLength::class,
            system = templateName.res,
            year = calculationContext?.year!!,
            location = "Global",
        )

    @EntityCreation(Entities.MACHINE)
    fun createMachines(templateName: Text): Flux<ManufacturingEntity> = createMachinesFromTemplate(templateName)

    @EntityCreation(Entities.LABOR)
    fun createLabor(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> = createLaborFromTemplate(templateName, locationName)

    @EntityCreation(Entities.SETUP)
    fun createSetup(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> = createSetupFromTemplate(templateName, locationName)

    @DisableEntityCreationInModularizedEntity
    @EntityCreation(Entities.TOOL)
    fun createTools(templateName: Text): Flux<ManufacturingEntity> = createToolFromTemplate(templateName)

    @DisableEntityCreationInModularizedEntity
    @EntityCreation(Entities.MATERIAL)
    fun createMaterial(
        materialName: Text?,
        materialClass: Text?,
        technology: Text?,
    ): Mono<ManufacturingEntity> {
        val masterDataType =
            when (technology?.res) {
                Model.RSWA.name -> MasterDataType.RAW_MATERIAL_PIPE
                else -> MasterDataType.RAW_MATERIAL_BAR
            }

        return createEntityWithMasterdata(
            name = "MaterialForging",
            entityType = Entities.MATERIAL,
            clazz = requireNotNull(materialClass).res,
            masterDataType = masterDataType,
            masterDataKey = requireNotNull(materialName).res,
        )
    }
}
