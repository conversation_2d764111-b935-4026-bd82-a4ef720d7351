package com.nu.bom.core.technologies.steps.dca.tool

import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.Tool
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.Money
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.ToolComplexityDieCasting
import com.nu.bom.core.technologies.lookups.dieCastingServiceLifeInCyclesReader
import com.nu.bom.core.technologies.lookups.trimDieCastingToolChinaReader
import reactor.core.publisher.Mono

@EntityType(Entities.TOOL)
class TrimDieCastingToolChina(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = Tool(name)

    @Input
    @SpecialLink("MaterialCasting", "hasMagnesium")
    fun hasMagnesium(): Bool? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun shapeTechnologyGroup(): Text? = null

    @Input
    fun toolComplexity(
        @Parent(Entities.MANUFACTURING_STEP)
        toolComplexity: Num?,
    ): ToolComplexityDieCasting? = toolComplexity?.let { ToolComplexityDieCasting(it) }

    @Input
    @Parent(Entities.MANUFACTURING_STEP)
    fun partsPerCycleFamilyTooling(): QuantityUnit? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun projectedAreaPerPart(): Area? = null

    @Input
    @Parent(Entities.MANUFACTURING_STEP)
    fun toolId(): Text? = null

    fun serviceLifeInCycles(
        toolId: Text,
        hasMagnesium: Bool,
    ): Mono<Num> =
        services
            .getLookupTable(
                "ManufacturingStepDieCasting_ServiceLifeInCycles",
                dieCastingServiceLifeInCyclesReader,
            ).filter {
                toolId.res == it.toolId
            }.single()
            .map {
                if (hasMagnesium.isTrue()) {
                    Num(3.toBigDecimal() * it.serviceLifeInCyclesMagnesium)
                } else {
                    Num(3.toBigDecimal() * it.serviceLifeInCycles.res)
                }
            }

    fun investPerTool(
        partsPerCycleFamilyTooling: QuantityUnit,
        toolId: Text,
    ): Mono<Money> =
        services
            .getLookupTable(
                "ManufacturingStepDieCasting_TrimDieCastingToolChina",
                trimDieCastingToolChinaReader,
            ).filter {
                toolId.res == it.toolId
            }.single()
            .map {
                when {
                    partsPerCycleFamilyTooling.res.compareTo(1.toBigDecimal()) == 0 -> Money(it.trimmingToolCost1)
                    else -> Money(it.trimmingToolCost)
                }
            }

    fun maintenanceRate(): Rate = Rate(0.0.toBigDecimal())
}
