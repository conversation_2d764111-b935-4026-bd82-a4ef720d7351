package com.nu.bom.core.technologies.manufacturings.pbox

import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.entities.BomEntry
import com.nu.bom.core.manufacturing.entities.BomNodeReference
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.MissingInputError
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SurfaceDensity
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.utils.annotations.TsetSuppress

@EntityType(Entities.BOM_ENTRY)
@Suppress("unused")
class SubManufacturingPrintedCardboardBoxBomEntry(
    name: String,
) : ManufacturingEntity(name),
    BomNodeReference {
    override val extends = BomEntry(name)

    private companion object {
        const val DEFAULT_SUBPART_NAME = "Outer layer printed"
    }

    @Input
    @Parent(Entities.MANUFACTURING)
    fun internalCallsPerYear(): Num = throw MissingInputError()

    @Input
    @Parent(Entities.MANUFACTURING)
    @TsetSuppress("tset:engine:field-override-configuration-value")
    fun callsPerYear(): Num = throw MissingInputError()

    @Input
    @Parent(Entities.MANUFACTURING)
    fun materialKeyOuterLiner(): Text = throw MissingInputError()

    @Input
    @Parent(Entities.MANUFACTURING)
    fun varnishWeight(): SurfaceDensity = throw MissingInputError()

    @Input
    @Parent(Entities.MANUFACTURING)
    fun cyanShare(): Rate = throw MissingInputError()

    @Input
    @Parent(Entities.MANUFACTURING)
    fun cyanPrintingWeight(): SurfaceDensity = throw MissingInputError()

    @Input
    @Parent(Entities.MANUFACTURING)
    fun magentaShare(): Rate = throw MissingInputError()

    @Input
    @Parent(Entities.MANUFACTURING)
    fun magentaPrintingWeight(): SurfaceDensity = throw MissingInputError()

    @Input
    @Parent(Entities.MANUFACTURING)
    fun yellowShare(): Rate = throw MissingInputError()

    @Input
    @Parent(Entities.MANUFACTURING)
    fun yellowPrintingWeight(): SurfaceDensity = throw MissingInputError()

    @Input
    @Parent(Entities.MANUFACTURING)
    fun blackShare(): Rate = throw MissingInputError()

    @Input
    @Parent(Entities.MANUFACTURING)
    fun blackPrintingWeight(): SurfaceDensity = throw MissingInputError()

    @Input
    @Parent(Entities.MANUFACTURING)
    fun boxSurfaceArea(): Area = throw MissingInputError()

    @Input
    @Parent(Entities.MANUFACTURING)
    fun paintingAreaOutside(): Rate = throw MissingInputError()

    @Input
    @Parent(Entities.MANUFACTURING)
    fun nestingResultSheetLength(): Length = throw MissingInputError()

    @Input
    @Parent(Entities.MANUFACTURING)
    fun nestingResultSheetWidth(): Length = throw MissingInputError()

    @Input
    @Parent(Entities.MANUFACTURING)
    fun nestingResultArea(): Area = throw MissingInputError()

    @Input
    @Parent(Entities.MANUFACTURING)
    fun nestingResultParts(): QuantityUnit = throw MissingInputError()

    @EntityCreation(Entities.MANUFACTURING)
    fun createSub() =
        createManufacturing(
            name = SubManufacturingPrintedCardboardBoxOuterLayerPrinted::class.simpleName!!,
            clazz = SubManufacturingPrintedCardboardBoxOuterLayerPrinted::class,
            args = emptyMap(),
            fields =
                mapOf(
                    "partDesignation" to Text(DEFAULT_SUBPART_NAME),
                ),
        )
}
