package com.nu.bom.core.technologies.steps.dca.tool

import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityCreationChildrenScope
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Sibling
import com.nu.bom.core.manufacturing.annotations.StaticDenominatorUnit
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.Tool
import com.nu.bom.core.manufacturing.entities.toolmaintenance.DetailedToolMaintenanceCalculationEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.StaticUnitOverride
import com.nu.bom.core.manufacturing.fieldTypes.Force
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.ToolDetailViewConfig
import com.nu.bom.core.manufacturing.fieldTypes.ToolMaintenanceType
import java.math.RoundingMode

@EntityType(Entities.TOOL)
class DieCastingTool(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = Tool(name)

    @Input
    @Parent(Entities.MANUFACTURING_STEP)
    fun toolId(): Text? = null

    @Input
    @Parent(Entities.MANUFACTURING_STEP)
    fun partsPerCycleFamilyTooling(): QuantityUnit? = null

    @Parent(Entities.MANUFACTURING)
    @StaticDenominatorUnit(StaticUnitOverride.YEAR)
    fun peakUsableProductionVolumePerYear(): QuantityUnit? = null

    @Sibling(Entities.SYSTEM_PARAMETER)
    fun lockingForce(): Force? = null

    @Parent(Entities.MANUFACTURING_STEP)
    @StaticDenominatorUnit(StaticUnitOverride.YEAR)
    fun callsPerYear(): Num? = null

    fun toolDetailView(): ToolDetailViewConfig = ToolDetailViewConfig.DCA_TOOL

    fun toolMaintenanceType(): ToolMaintenanceType = ToolMaintenanceType.DETAILED_TOOL_MAINTENANCE

    // TODO: Annotations Sibling
    @DefaultUnit(DefaultUnit.HOUR)
    fun smallMaintenanceHours(lockingForce: Force): Time =
        when {
            lockingForce.inKilonewton <= 800.toBigDecimal() * 9.8067.toBigDecimal() ->
                Time(
                    50.toBigDecimal(),
                    TimeUnits.HOUR,
                )
            lockingForce.inKilonewton <= 2000.toBigDecimal() * 9.8067.toBigDecimal() ->
                Time(
                    100.toBigDecimal(),
                    TimeUnits.HOUR,
                )
            else -> Time(150.toBigDecimal(), TimeUnits.HOUR)
        }

    // TODO: Annotations Sibling
    @DefaultUnit(DefaultUnit.HOUR)
    fun bigMaintenanceHours(lockingForce: Force): Time =
        when {
            lockingForce.inKilonewton <= 800.toBigDecimal() * 9.8067.toBigDecimal() ->
                Time(
                    150.toBigDecimal(),
                    TimeUnits.HOUR,
                )
            lockingForce.inKilonewton <= 2000.toBigDecimal() * 9.8067.toBigDecimal() ->
                Time(
                    250.toBigDecimal(),
                    TimeUnits.HOUR,
                )
            else -> Time(350.toBigDecimal(), TimeUnits.HOUR)
        }

    @StaticDenominatorUnit(StaticUnitOverride.YEAR)
    fun smallMaintenance(callsPerYear: Num): Num =
        Num(
            (Num(5.toBigDecimal()).res * callsPerYear.res / Num(6.toBigDecimal()).res).setScale(
                0,
                RoundingMode.UP,
            ),
        )

    @StaticDenominatorUnit(StaticUnitOverride.YEAR)
    fun bigMaintenance(
        callsPerYear: Num,
        smallMaintenance: Num,
    ): Num = Num(callsPerYear.res - smallMaintenance.res)

    @EntityCreation(Entities.DETAILED_TOOL_MAINTENANCE_CALCULATOR, entityCreationChildrenScope = EntityCreationChildrenScope.ALL)
    fun createMaintenances(
        smallMaintenanceHours: Time,
        bigMaintenanceHours: Time,
        smallMaintenance: Num,
        bigMaintenance: Num,
    ): List<DetailedToolMaintenanceCalculationEntity> =
        listOf(
            createEntity(
                "Small Maintenance",
                Entities.DETAILED_TOOL_MAINTENANCE_CALCULATOR,
                fields =
                    mapOf(
                        DetailedToolMaintenanceCalculationEntity::maintenanceHours.name to smallMaintenanceHours,
                        DetailedToolMaintenanceCalculationEntity::numberOfMaintenancePerYear.name to smallMaintenance,
                        DetailedToolMaintenanceCalculationEntity::materialMaintenanceRate.name to Rate(0.02),
                    ),
            ),
            createEntity(
                "Big Maintenance",
                Entities.DETAILED_TOOL_MAINTENANCE_CALCULATOR,
                fields =
                    mapOf(
                        DetailedToolMaintenanceCalculationEntity::maintenanceHours.name to bigMaintenanceHours,
                        DetailedToolMaintenanceCalculationEntity::numberOfMaintenancePerYear.name to bigMaintenance,
                        DetailedToolMaintenanceCalculationEntity::materialMaintenanceRate.name to Rate(0.04),
                    ),
            ),
        )
}
