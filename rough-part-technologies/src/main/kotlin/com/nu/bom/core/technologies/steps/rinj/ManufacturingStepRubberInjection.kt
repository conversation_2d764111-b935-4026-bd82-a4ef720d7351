package com.nu.bom.core.technologies.steps.rinj

import com.nu.bom.core.exception.userException.TemplateNotFoundException
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.CalculationPreview
import com.nu.bom.core.manufacturing.annotations.Children
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DontSortOptions
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.Selectable
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.annotations.SubLabel
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.defaults.NoLength
import com.nu.bom.core.manufacturing.entities.BomEntry
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.RoughManufacturing
import com.nu.bom.core.manufacturing.entities.SingleGroupManufacturingStep
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.ManufacturingStepType
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.CavitiesDisposition
import com.nu.bom.core.manufacturing.fieldTypes.CycleTimeGrouping
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.Force
import com.nu.bom.core.manufacturing.fieldTypes.ForceUnits
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.Pressure
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.RubberType
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.SliderConceptIdInjection
import com.nu.bom.core.manufacturing.fieldTypes.Speed
import com.nu.bom.core.manufacturing.fieldTypes.SpeedUnits
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.ToolOrientation
import com.nu.bom.core.manufacturing.fieldTypes.Volume
import com.nu.bom.core.manufacturing.fieldTypes.VolumeUnits
import com.nu.bom.core.technologies.lookups.rubberInjectionTemplatesReader
import com.nu.bom.core.technologies.service.ShapeLookupReaderService
import com.nu.bom.core.technologies.steps.rinj.cycletimestep.HorizontalRubberInjectionCycleTimeStepGroup
import com.nu.bom.core.technologies.steps.rinj.cycletimestep.VerticalRubberHandlingCycleTimeStepGroup
import com.nu.bom.core.technologies.steps.rinj.cycletimestep.VerticalRubberInjectionCycleTimeStepGroup
import com.nu.bom.core.technologies.steps.rinj.systemparameter.SystemParameterRubberInjection
import com.nu.bom.core.utils.component1
import com.nu.bom.core.utils.component2
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.math.BigDecimal
import java.math.RoundingMode

private const val NOT_SUPPORTED_PART_GROUP = "not supported part input group"

@EntityType(Entities.MANUFACTURING_STEP)
class ManufacturingStepRubberInjection(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = SingleGroupManufacturingStep(name)

    @Input
    @SubLabel
    @ReadOnly
    @Selectable(value = "manufacturingStepType", getDisplayNameFromPathQuery = true)
    fun manufacturingStepType(): Text = Text(ManufacturingStepType.CHEMICALS_INJECTION_MOLDING.name)

    fun scrapRate(): Rate = Rate(0.01.toBigDecimal())

    fun utilizationRate() = Rate(0.83)

    @Input
    fun partLength(
        @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
        @Default(NoLength::class)
        partLength: Length?,
    ) = partLength
        ?: Length(BigDecimal.ZERO, LengthUnits.METER)

    @Input
    fun partWidth(
        @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
        @Default(NoLength::class)
        partWidth: Length?,
    ) = partWidth
        ?: Length(BigDecimal.ZERO, LengthUnits.METER)

    @Input
    fun partHeight(
        @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
        @Default(NoLength::class)
        partHeight: Length?,
    ) = partHeight
        ?: Length(BigDecimal.ZERO, LengthUnits.METER)

    @Input
    fun partOuterDiameter(
        @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
        @Default(NoLength::class)
        partOuterDiameter: Length?,
    ) = partOuterDiameter
        ?: Length(BigDecimal.ZERO, LengthUnits.METER)

    fun inputGroup(
        lookupReaderService: ShapeLookupReaderService,
        @Parent(Entities.PROCESSED_MATERIAL)
        shapeId: Text,
    ) = lookupReaderService.getInputGroupFromShapeLookup(calculationContext!!.accessCheck, shapeId, Model.RINJ)

    fun newLength(
        inputGroup: Text,
        partOuterDiameter: Length,
        partLength: Length,
    ): Length =
        when (inputGroup.res) {
            "cuboid", "hollowCuboid" -> partLength
            "hollowCycle", "hollowRing", "disc", "pipe", "cylinder" -> partOuterDiameter
            else -> throw java.lang.IllegalArgumentException(NOT_SUPPORTED_PART_GROUP)
        }

    fun newWidth(
        inputGroup: Text,
        partOuterDiameter: Length,
        partWidth: Length,
    ): Length =
        when (inputGroup.res) {
            "cuboid", "hollowCuboid" -> partWidth
            "hollowCycle", "hollowRing", "disc", "pipe", "cylinder" -> partOuterDiameter
            else -> throw java.lang.IllegalArgumentException(NOT_SUPPORTED_PART_GROUP)
        }

    fun newHeight(
        inputGroup: Text,
        partHeight: Length,
        partLength: Length,
    ): Length =
        when (inputGroup.res) {
            "cuboid", "hollowCuboid", "hollowRing", "disc", "cylinder" -> partHeight
            "hollowCycle", "pipe" -> partLength
            else -> throw java.lang.IllegalArgumentException(NOT_SUPPORTED_PART_GROUP)
        }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 24)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun gapBetweenParts() = Length(0.02, LengthUnits.METER)

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 25)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun gapToTool(): Length = Length(0.05, LengthUnits.METER)

    fun sliderConceptId() = SliderConceptIdInjection.SLIDER_CONCEPT_ID_4

    fun cavityLength(
        sliderConceptId: SliderConceptIdInjection,
        partsPerCycleFamilyTooling: QuantityUnit,
    ): Mono<Num> =
        services
            .getInjectionUtilsService()
            .cavityLengthImpl(calculationContext!!.accessCheck, sliderConceptId, partsPerCycleFamilyTooling)

    fun cavityWidth(
        sliderConceptId: SliderConceptIdInjection,
        partsPerCycleFamilyTooling: QuantityUnit,
    ): Mono<Num> =
        services
            .getInjectionUtilsService()
            .cavityWidthImpl(calculationContext!!.accessCheck, sliderConceptId, partsPerCycleFamilyTooling)

    @Input
    @SpecialLink("MaterialRubber", "internalMoldPressure")
    fun internalMoldPressure(): Pressure? = null

    @Input
    @SpecialLink("MaterialRubber", "rubberType")
    fun rubberType(): RubberType? = null

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 40)
    @DefaultUnit(DefaultUnit.KILONEWTON)
    fun necessaryClampingForce(
        internalMoldPressure: Pressure,
        @Parent(Entities.PROCESSED_MATERIAL)
        projectedAreaPerPart: Area,
        partsPerCycleFamilyTooling: QuantityUnit,
    ): Force =
        Force(
            internalMoldPressure.inPascal * projectedAreaPerPart.res * partsPerCycleFamilyTooling.res,
            ForceUnits.NEWTON,
        )

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 50)
    @DefaultUnit(DefaultUnit.MM_PER_SEC)
    fun clampSpeed() = Speed(50.0, SpeedUnits.MM_PER_SEC)

    fun initialServiceLifeInCycles() = Num(1000000)

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 38)
    @DefaultUnit(DefaultUnit.CCM)
    fun partVolume(
        @Parent(Entities.PROCESSED_MATERIAL)
        netWeightPerPart: QuantityUnit,
        @Children(Entities.MATERIAL)
        density: Density,
    ): Volume = Volume(1.0, VolumeUnits.CM) / density * netWeightPerPart

    fun maxPartsPerCycle() = QuantityUnit(128.0)

    fun selectedPartsPerCycle(
        averageProcessedVolumeOverLifeTime: QuantityUnit,
        initialServiceLifeInCycles: Num,
        maxPartsPerCycle: QuantityUnit,
    ): QuantityUnit {
        val selectedVal =
            min(
                (averageProcessedVolumeOverLifeTime.res / initialServiceLifeInCycles.res).setScale(0, RoundingMode.UP),
                maxPartsPerCycle.res,
            )
        val possibleValues = listOf(1, 2, 4, 8, 16, 32, 64, 128).sorted()
        val fallback = 1
        return QuantityUnit((possibleValues.find { it >= selectedVal.toInt() } ?: fallback).toBigDecimal())
    }

    @CalculationPreview(4, "partsPerCyclePlasticInjection")
    @SummaryView(SummaryView.PROCESS, 30, "partsPerCyclePlasticInjection")
    fun partPerCyclePreview(partsPerCycle: QuantityUnit): QuantityUnit = partsPerCycle

    fun internalPartsPerCycle(
        selectedPartsPerCycle: QuantityUnit,
        sliderConceptId: SliderConceptIdInjection,
        newLength: Length,
        newWidth: Length,
        gapBetweenParts: Length,
        gapToTool: Length,
        internalMoldPressure: Pressure,
        @Parent(Entities.PROCESSED_MATERIAL)
        projectedAreaPerPart: Area,
        @Parent(Entities.MANUFACTURING_STEP)
        insertsNeeded: SelectableBoolean,
        rubberType: RubberType,
    ): Mono<QuantityUnit> =
        Mono
            .zip(
                cavityLength(sliderConceptId, selectedPartsPerCycle),
                cavityWidth(sliderConceptId, selectedPartsPerCycle),
            ).flatMap { (cavityLength, cavityWidth) ->
                val ncf =
                    necessaryClampingForce(internalMoldPressure, projectedAreaPerPart, selectedPartsPerCycle)
                templateName(
                    newLength,
                    newWidth,
                    gapBetweenParts,
                    gapToTool,
                    ncf,
                    cavityLength,
                    cavityWidth,
                    insertsNeeded,
                    rubberType,
                ).map {
                    selectedPartsPerCycle
                }
            }.onErrorResume {
                if (selectedPartsPerCycle.res < BigDecimal.ONE) {
                    throw TemplateNotFoundException(
                        lookupName = "internalPartsPerCycle (ManufacturingStepRubberInjection)",
                        lookupInputs =
                            mapOf(
                                "selectedPartRangeIndex" to selectedPartsPerCycle.toString(),
                            ),
                    )
                }
                internalPartsPerCycle(
                    QuantityUnit(selectedPartsPerCycle.res / 2.toBigDecimal()),
                    sliderConceptId,
                    newLength,
                    newWidth,
                    gapBetweenParts,
                    gapToTool,
                    internalMoldPressure,
                    projectedAreaPerPart,
                    insertsNeeded,
                    rubberType,
                )
            }

    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 26)
    fun calculatedToolHeight(
        toolOrientation: ToolOrientation,
        cavitiesDisposition: CavitiesDisposition,
        gapToTool: Length,
        gapBetweenParts: Length,
        newLength: Length,
        cavityLength: Num,
        newWidth: Length,
        cavityWidth: Num,
    ): Length =
        services.getInjectionUtilsService().calculatedToolHeightImpl(
            toolOrientation,
            cavitiesDisposition,
            gapToTool,
            gapBetweenParts,
            newLength,
            cavityLength,
            newWidth,
            cavityWidth,
        )

    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 27)
    fun calculatedToolWidth(
        toolOrientation: ToolOrientation,
        cavitiesDisposition: CavitiesDisposition,
        gapToTool: Length,
        gapBetweenParts: Length,
        newLength: Length,
        cavityLength: Num,
        newWidth: Length,
        cavityWidth: Num,
    ): Length =
        services.getInjectionUtilsService().calculatedToolWidthImpl(
            toolOrientation,
            cavitiesDisposition,
            gapToTool,
            gapBetweenParts,
            newLength,
            cavityLength,
            newWidth,
            cavityWidth,
        )

    @DontSortOptions
    @SummaryView(SummaryView.PROCESS, 20, "configurationRubberInjection")
    @CalculationPreview(3, "configurationRubberInjection")
    fun templateName(
        newLength: Length,
        newWidth: Length,
        gapBetweenParts: Length,
        gapToTool: Length,
        necessaryClampingForce: Force,
        cavityLength: Num,
        cavityWidth: Num,
        @Parent(Entities.MANUFACTURING_STEP)
        insertsNeeded: SelectableBoolean,
        rubberType: RubberType,
    ): Mono<Text> {
        val lookupName = "ManufacturingStepRubberInjection_Templates"
        return services
            .getLookupTable(lookupName, rubberInjectionTemplatesReader)
            .filter {
                val diffLockingForce = (it.lockingForce.times(0.85) - necessaryClampingForce).res > BigDecimal.ZERO

                val feasibleCaseNum =
                    services.getInjectionUtilsService().templateLookupFeasibleCaseImpl(
                        it.mountingPlateWidth,
                        it.mountingPlateHeight,
                        gapToTool,
                        gapBetweenParts,
                        newLength,
                        cavityLength,
                        newWidth,
                        cavityWidth,
                    )

                val diffOpeningType =
                    when {
                        insertsNeeded.toBoolean() && it.openingType == Text("Vertical") -> true
                        !insertsNeeded.toBoolean() && it.openingType == Text("Horizontal") -> true
                        else -> false
                    }

                val diffMaterialType =
                    when (rubberType.res) {
                        RubberType.Selection.RUBBER -> it.rubberOrSilicone.res.contains("Rubber")
                        RubberType.Selection.SILICONE -> it.rubberOrSilicone.res.contains("Silicone")
                    }
                diffLockingForce && feasibleCaseNum in 1..4 && diffOpeningType && diffMaterialType
            }.collectList()
            .mapNotNull { rubberInjectionTemplates ->
                if (rubberInjectionTemplates.isEmpty()) {
                    throw TemplateNotFoundException(
                        lookupName = lookupName,
                        lookupInputs =
                            mapOf(
                                "necessaryClampingForce" to necessaryClampingForce.toString(),
                                "gapToTool" to gapToTool.toString(),
                                "gapBetweenParts" to gapBetweenParts.toString(),
                                "newLength" to newLength.toString(),
                                "cavityLength" to cavityLength.toString(),
                                "newWidth" to newWidth.toString(),
                                "cavityWidth" to cavityWidth.toString(),
                                "insertsNeeded" to insertsNeeded.toString(),
                                "rubberType" to rubberType.toString(),
                            ),
                    )
                } else {
                    rubberInjectionTemplates.minByOrNull { it.lockingForce.res }
                }
            }.map { Text(it!!.templateName) }
    }

    fun feasibleCaseNumForTemplate(
        templateName: Text,
        newLength: Length,
        newWidth: Length,
        gapBetweenParts: Length,
        gapToTool: Length,
        cavityLength: Num,
        cavityWidth: Num,
    ): Mono<Num> =
        services
            .getLookupTable(
                "ManufacturingStepRubberInjection_Templates",
                rubberInjectionTemplatesReader,
            ).filter {
                templateName.res == it.templateName
            }.single()
            .map {
                val feasibleCaseNum =
                    services.getInjectionUtilsService().templateLookupFeasibleCaseImpl(
                        it.mountingPlateWidth,
                        it.mountingPlateHeight,
                        gapToTool,
                        gapBetweenParts,
                        newLength,
                        cavityLength,
                        newWidth,
                        cavityWidth,
                    )
                Num(feasibleCaseNum.toDouble())
            }

    fun cavitiesDisposition(feasibleCaseNumForTemplate: Num): CavitiesDisposition =
        services.getInjectionUtilsService().cavitiesDispositionImpl(feasibleCaseNumForTemplate)

    @ReadOnly
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 23)
    fun toolOrientation(feasibleCaseNumForTemplate: Num): ToolOrientation =
        services.getInjectionUtilsService().toolOrientationImpl(feasibleCaseNumForTemplate)

    @EntityCreation(Entities.MACHINE)
    fun createMachines(templateName: Text): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Machine",
            location = "Global",
        )

    @EntityCreation(Entities.LABOR)
    fun createLabor(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Labor",
            location = locationName.res,
        )

    @EntityCreation(Entities.SETUP)
    fun createSetup(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Setup",
            location = locationName.res,
        )

    @EntityCreation(Entities.TOOL)
    fun createTools(templateName: Text): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Tools",
            location = "Global",
        )

    fun grouping(
        @Children(Entities.SYSTEM_PARAMETER)
        openingType: ToolOrientation,
    ): CycleTimeGrouping = if (openingType == ToolOrientation.VERTICAL) CycleTimeGrouping.Parallel else CycleTimeGrouping.Sequential

    @EntityCreation(Entities.CYCLETIME_STEP_GROUP)
    fun createCycleTimeStepGroup(
        @Children(Entities.SYSTEM_PARAMETER)
        openingType: ToolOrientation,
    ): List<ManufacturingEntity> =
        if (openingType == ToolOrientation.VERTICAL) {
            listOfNotNull(
                createEntity(
                    name = "Injection molding",
                    clazz = VerticalRubberInjectionCycleTimeStepGroup::class,
                    entityType = Entities.CYCLETIME_STEP_GROUP,
                ),
                createEntity(
                    name = "Part handling & deburring",
                    clazz = VerticalRubberHandlingCycleTimeStepGroup::class,
                    entityType = Entities.CYCLETIME_STEP_GROUP,
                ),
            )
        } else {
            listOfNotNull(
                createEntity(
                    name = "Injection molding",
                    clazz = HorizontalRubberInjectionCycleTimeStepGroup::class,
                    entityType = Entities.CYCLETIME_STEP_GROUP,
                ),
            )
        }

    @EntityCreation(Entities.MATERIAL)
    fun createMaterial(
        @Parent(Entities.PROCESSED_MATERIAL)
        materialName: Text,
        @Parent(Entities.PROCESSED_MATERIAL)
        materialClass: Text,
    ): Mono<ManufacturingEntity> =
        createEntityWithNewMasterdata(
            name = "MaterialRubber",
            entityType = Entities.MATERIAL,
            clazz = materialClass.res,
            masterDataType = MasterDataType.RAW_MATERIAL_RUBBER,
            masterDataKey = materialName.res,
        )

    @EntityCreation(Entities.SYSTEM_PARAMETER)
    fun createSystemParameter(templateName: Text): Mono<SystemParameterRubberInjection> =
        createSystemParameter(
            masterData = MasterDataType.SYSTEM_PARAMETER_RUBBER_INJECTION,
            clazz = SystemParameterRubberInjection::class,
            system = templateName.res,
            year = calculationContext?.year!!,
            location = "Global",
        )

    @EntityCreation(Entities.BOM_ENTRY)
    fun createSub(
        @Parent(Entities.MANUFACTURING)
        createInsertsAutomatically: SelectableBoolean,
        @Parent(Entities.MANUFACTURING)
        insertsNeeded: SelectableBoolean,
        @Parent(Entities.MANUFACTURING)
        numberOfInserts: Num,
        @Parent(Entities.MANUFACTURING)
        key: Text,
    ): BomEntry? =
        if (insertsNeeded == SelectableBoolean.TRUE && createInsertsAutomatically == SelectableBoolean.TRUE) {
            val bomEntry =
                createEntity(
                    name = BomEntry::class.simpleName!!,
                    clazz = BomEntry::class,
                    fields = mapOf("quantity" to numberOfInserts),
                    entityType = Entities.BOM_ENTRY,
                )
            val manufacturing =
                createManufacturing(
                    name = "Insert",
                    clazz = RoughManufacturing::class,
                    args =
                        mapOf(
                            "key" to key.res,
                            "isPart" to false,
                        ),
                    fields =
                        mapOf(
                            "partDesignation" to Text("Insert"),
                        ),
                )
            bomEntry.addChild(manufacturing)
            bomEntry
        } else {
            null
        }
}
