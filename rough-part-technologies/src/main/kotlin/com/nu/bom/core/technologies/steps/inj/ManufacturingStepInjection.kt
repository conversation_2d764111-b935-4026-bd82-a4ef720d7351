package com.nu.bom.core.technologies.steps.inj

import com.nu.bom.core.manufacturing.annotations.Children
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.Selectable
import com.nu.bom.core.manufacturing.annotations.SubLabel
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.SingleGroupManufacturingStep
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.ManufacturingStepType
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.Diffusivity
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Temperature
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.Volume
import com.nu.bom.core.manufacturing.fieldTypes.VolumeUnits
import com.nu.bom.core.technologies.steps.inj.systemparameter.SystemParameterInjection
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.math.BigDecimal
import kotlin.math.ln
import kotlin.math.pow

@EntityType(Entities.MANUFACTURING_STEP)
class ManufacturingStepInjection(name: String) : ManufacturingEntity(name) {
    override val extends = SingleGroupManufacturingStep(name)

    @Input
    @SubLabel
    @ReadOnly
    @Selectable(value = "manufacturingStepType", getDisplayNameFromPathQuery = true)
    fun manufacturingStepType(): Text = Text(ManufacturingStepType.CHEMICALS_INJECTION_MOLDING.name)

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun shapeId(): Text? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun netWeightPerPart(): QuantityUnit? = null

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 6)
    @DefaultUnit(DefaultUnit.CCM)
    fun partVolume(
        netWeightPerPart: QuantityUnit,
        density: Density,
    ): Volume {
        return Volume(1.0, VolumeUnits.CM) / density * netWeightPerPart
    }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 12)
    @DefaultUnit(DefaultUnit.SECOND)
    fun coolingTimeWithoutAdditions(
        @Parent(Entities.PROCESSED_MATERIAL) maxWallThickness: Length,
        thermalDiffusivity: Diffusivity,
        injectingTemperature: Temperature,
        moldTemperature: Temperature,
        moldSeparationTemperature: Temperature,
    ): Time {
        val conversionTerm = maxWallThickness.inMeter.pow(2) / (Math.PI.pow(2).toBigDecimal() * thermalDiffusivity.inQmPerSecond)

        val logTerm =
            (injectingTemperature.inCelsius - moldTemperature.inCelsius) * 8.0.toBigDecimal() /
                ((moldSeparationTemperature.inCelsius - moldTemperature.inCelsius) * Math.PI.pow(2).toBigDecimal())

        return Time(conversionTerm * ln(logTerm.toDouble()).toBigDecimal(), TimeUnits.SECOND)
    }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 13)
    @DefaultUnit(DefaultUnit.SECOND)
    fun injectionTime(
        partVolume: Volume,
        @Children(Entities.SYSTEM_PARAMETER) injectionVolumePerTime: Volume,
        partsPerCycle: QuantityUnit,
    ): Time {
        val seconds =
            when (partVolume.res) {
                BigDecimal.ZERO -> 2.04.toBigDecimal()
                else -> partVolume.res * partsPerCycle.res / injectionVolumePerTime.res
            }

        return Time(seconds, TimeUnits.SECOND)
    }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 15)
    @DefaultUnit(DefaultUnit.SECOND)
    fun plasticizingTime(
        partVolume: Volume,
        density: Density,
        @Children(Entities.SYSTEM_PARAMETER) plasticisingMassPerTime: QuantityUnit,
    ): Time {
        val seconds = density * partVolume / plasticisingMassPerTime

        return Time(seconds.res, TimeUnits.SECOND)
    }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 17)
    @DefaultUnit(DefaultUnit.SECOND)
    fun coolingTimeIncludingAdditions(
        coolingTimeWithoutAdditions: Time,
        injectionTime: Time,
        plasticizingTime: Time,
    ): Time {
        val seconds = coolingTimeWithoutAdditions + injectionTime - plasticizingTime

        return Time(seconds.res, TimeUnits.SECOND)
    }

    @EntityCreation(Entities.MACHINE)
    fun createMachines(templateName: Text): Flux<ManufacturingEntity> {
        return createEntitiesFromTemplate(
            name = templateName.res + "_Machine",
            location = "Global",
        )
    }

    @EntityCreation(Entities.LABOR)
    fun createLabor(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> {
        return createEntitiesFromTemplate(
            name = templateName.res + "_Labor",
            location = locationName.res,
        )
    }

    @EntityCreation(Entities.SETUP)
    fun createSetup(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> {
        return createEntitiesFromTemplate(
            name = templateName.res + "_Setup",
            location = locationName.res,
        )
    }

    @EntityCreation(Entities.TOOL)
    fun createTools(templateName: Text): Flux<ManufacturingEntity> {
        return createEntitiesFromTemplate(
            name = templateName.res + "_Tools",
            location = "Global",
        )
    }

    @EntityCreation(Entities.MATERIAL)
    fun createMaterial(
        @Parent(Entities.PROCESSED_MATERIAL)
        materialName: Text,
        @Parent(Entities.PROCESSED_MATERIAL)
        materialClass: Text,
    ): Mono<ManufacturingEntity> {
        return createEntityWithNewMasterdata(
            name = "MaterialPlastic",
            entityType = Entities.MATERIAL,
            clazz = materialClass.res,
            masterDataType = MasterDataType.RAW_MATERIAL_PLASTIC_GRANULATE,
            masterDataKey = materialName.res,
        )
    }

    @EntityCreation(Entities.SYSTEM_PARAMETER)
    fun createSystemParameter(templateName: Text): Mono<SystemParameterInjection> {
        return createSystemParameter(
            masterData = MasterDataType.SYSTEM_PARAMETER_INJECTION,
            clazz = SystemParameterInjection::class,
            system = templateName.res,
            year = calculationContext?.year!!,
            location = "Global",
        )
    }
}
