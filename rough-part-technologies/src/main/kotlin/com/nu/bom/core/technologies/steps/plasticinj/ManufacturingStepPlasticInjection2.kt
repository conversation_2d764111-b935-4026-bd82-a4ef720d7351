package com.nu.bom.core.technologies.steps.plasticinj

import com.nu.bom.core.exception.InternalServerException
import com.nu.bom.core.exception.readable.ErrorCode
import com.nu.bom.core.exception.userException.TemplateNotFoundException
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.CalculationPreview
import com.nu.bom.core.manufacturing.annotations.Condition
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DisableEntityCreationInModularizedEntity
import com.nu.bom.core.manufacturing.annotations.DontExport
import com.nu.bom.core.manufacturing.annotations.DontSortOptions
import com.nu.bom.core.manufacturing.annotations.DynamicDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.EngineTransient
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityLinkField
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.ExpectedParents
import com.nu.bom.core.manufacturing.annotations.ExternalDependency
import com.nu.bom.core.manufacturing.annotations.FieldIndex
import com.nu.bom.core.manufacturing.annotations.FieldName
import com.nu.bom.core.manufacturing.annotations.FilteredChildren
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Modularized
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.RequiredFor
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.defaults.NullProvider
import com.nu.bom.core.manufacturing.entities.BomEntry
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.RoughManufacturing
import com.nu.bom.core.manufacturing.entities.Setup
import com.nu.bom.core.manufacturing.entities.masterdata.costFactors.MasterdataCostFactorParent.Companion.TSET_COST_COUNTRY_ID_ENTITY_NAME
import com.nu.bom.core.manufacturing.enums.DynamicUnitOverride
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.CavitiesDisposition
import com.nu.bom.core.manufacturing.fieldTypes.Contour
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.Diffusivity
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.DynamicQuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.FoamingType
import com.nu.bom.core.manufacturing.fieldTypes.Force
import com.nu.bom.core.manufacturing.fieldTypes.InjectionConfigurationKey
import com.nu.bom.core.manufacturing.fieldTypes.InjectionType
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.ListOfStrings
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.PartQuality
import com.nu.bom.core.manufacturing.fieldTypes.PartRemovalType
import com.nu.bom.core.manufacturing.fieldTypes.PartingLine
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.PiecesUnits
import com.nu.bom.core.manufacturing.fieldTypes.Pressure
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.RiskOfInternalStress
import com.nu.bom.core.manufacturing.fieldTypes.RunnerSystems
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.SetupType
import com.nu.bom.core.manufacturing.fieldTypes.ShrinkageBehavior
import com.nu.bom.core.manufacturing.fieldTypes.SliderConceptIdInjection
import com.nu.bom.core.manufacturing.fieldTypes.SliderType
import com.nu.bom.core.manufacturing.fieldTypes.Speed
import com.nu.bom.core.manufacturing.fieldTypes.Temperature
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.ToolOrientation
import com.nu.bom.core.manufacturing.fieldTypes.VolumeFlowRate
import com.nu.bom.core.manufacturing.fieldTypes.VolumeFlowRateUnits
import com.nu.bom.core.manufacturing.fieldTypes.VolumeUnits
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.nu.bom.core.manufacturing.fieldTypes.compareTo
import com.nu.bom.core.manufacturing.fieldTypes.configuration.InjectionConfigurationField
import com.nu.bom.core.model.NET_WEIGHT_PER_PART_PREDICTION_SERVICE
import com.nu.bom.core.model.configurations.InjectionConfigurationTypes
import com.nu.bom.core.technologies.lookups.CountryIdUtil.getCountry
import com.nu.bom.core.technologies.lookups.injection2ScrewDiameterReader
import com.nu.bom.core.technologies.lookups.injection2TemplatesReader
import com.nu.bom.core.technologies.lookups.injectionCycleTimeReader
import com.nu.bom.core.technologies.lookups.injectionTool2InvestReader
import com.nu.bom.core.technologies.service.ShapeLookupReaderService
import com.nu.bom.core.technologies.steps.inj.ManufacturingStepInjection2
import com.nu.bom.core.technologies.steps.plasticinj.setup.SetupNormalInjection
import com.nu.bom.core.utils.CostFactorUtils.getCostFactorForCurrentStepId
import com.nu.bom.core.utils.annotations.TsetSuppress
import com.nu.bom.core.utils.component1
import com.nu.bom.core.utils.component2
import com.nu.bom.core.utils.inRangeEndExclusive
import org.bson.types.ObjectId
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toFlux
import reactor.kotlin.core.publisher.toMono
import java.math.BigDecimal
import java.math.MathContext
import java.math.RoundingMode
import kotlin.math.pow

@EntityType(Entities.MANUFACTURING_STEP)
@Modularized(
    technologies = [Model.INJ2],
    parents = [
        ExpectedParents(model = Model.INJ2, type = Entities.MANUFACTURING),
        ExpectedParents(model = Model.INJ2, type = Entities.PROCESSED_MATERIAL),
    ],
    dimensions = [Dimension.Selection.NUMBER],
    userCreatable = true,
)
@Suppress("unused")
class ManufacturingStepPlasticInjection2(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = ManufacturingStepInjection2(name)

    companion object {
        const val CLASS_NAME = "ManufacturingStepPlasticInjection2"

        val MIN_INTERIM_COOLING_TIME = Time(BigDecimal("15"), TimeUnits.SECOND)
    }

    // region Fields from MANUFACTURING

    @Input
    @Parent(Entities.MANUFACTURING)
    fun key(): Text? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    fun peakUsableProductionVolumePerYear(): DynamicQuantityUnit? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 80)
    @Condition(field = "injectionType", value = "FOAMING", operator = Condition.EQUALS)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    fun foamingDensity(): Density? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 70)
    @Condition(field = "injectionType", value = "FOAMING", operator = Condition.EQUALS)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    fun foaming(): FoamingType? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    fun createInsertsAutomatically(): SelectableBoolean? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    fun numberOfInserts(): Num? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    fun numberOfDifferentInsertTypes(): Pieces? = null

    // endregion

    // region Fields from PROCESSED_MATERIAL

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    fun shapeTechnologyGroup(): Text? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 10)
    fun netWeightPerPart(): QuantityUnit? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 30)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun partHeight(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 40)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun partLength(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 50)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun partWidth(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 60)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun partOuterDiameter(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 130)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    @DefaultUnit(DefaultUnit.QCM)
    fun projectedAreaPerPart(): Area? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 100)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    fun partQuality(): PartQuality? = null

    @Parent(Entities.PROCESSED_MATERIAL)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 110)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun mainWallThickness(): Length? = null

    @Parent(Entities.PROCESSED_MATERIAL)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 120)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun maxWallThickness(): Length? = null

    // endregion

    // region Fields from MATERIAL

    @Input
    @EntityLinkField(providerField = "linkedMaterial", "designation")
    fun materialDesignation(): Text? = null

    @EntityLinkField(providerField = "linkedMaterial", "runnerSystems")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 10)
    fun runnerSystems(): RunnerSystems? = null

    @EntityLinkField(providerField = "linkedMaterial", "recyclingRate")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 20)
    fun recyclingRate(): Rate? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterial", "sprueRate")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 30)
    fun sprueRate(): Rate? = null

    @EntityLinkField(providerField = "linkedMaterial", "injectingTemperature")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, index = 50)
    fun injectingTemperature(): Temperature? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterial", "moldSeparationTemperature")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 60)
    fun moldSeparationTemperature(): Temperature? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterial", "minMoldTemperature")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 70)
    fun minMoldTemperature(): Temperature? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterial", "maxMoldTemperature")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 80)
    fun maxMoldTemperature(): Temperature? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterial", "minInternalMoldPressure")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 90)
    @DefaultUnit(DefaultUnit.BAR)
    fun minInternalMoldPressure(): Pressure? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterial", "maxInternalMoldPressure")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 100)
    @DefaultUnit(DefaultUnit.BAR)
    fun maxInternalMoldPressure(): Pressure? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterial", "averageInjectionVelocity")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 110)
    fun averageInjectionVelocity(): Speed? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterial", "materialFactor")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 130)
    fun materialFactor(): Rate? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterial", "density")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 140)
    fun density(): Density? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterial", "shrinkageBehavior")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 150)
    fun shrinkageBehavior(): ShrinkageBehavior? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterial", "thermalDiffusivity")
    @ExternalDependency(section = ExternalDependency.SECTIONS.MATERIAL)
    @FieldIndex(index = 160)
    @DefaultUnit(DefaultUnit.QMM_PER_SECONDS)
    fun thermalDiffusivity(): Diffusivity? = null

    // endregion

    fun injectionType(): InjectionType = InjectionType.NORMAL_INJECTION

    fun familyTooling(
        @Default(NullProvider::class)
        @Parent(Entities.MANUFACTURING)
        familyTooling: SelectableBoolean?,
    ) = familyTooling ?: SelectableBoolean.FALSE

    fun internalThermalDiffusivity(thermalDiffusivity: Diffusivity) = thermalDiffusivity

    fun sprueWeightPerPart(
        netWeightPerPart: QuantityUnit,
        sprueRate: Rate,
        manufacturingDimension: Dimension,
        manufacturingQuantityUnit: Text,
        dimension: Dimension,
        quantityUnit: Text,
    ): DynamicQuantityUnit =
        services.getInjectionUtilsService().sprueWeightPerPartImpl(
            netWeightPerPart,
            sprueRate,
            manufacturingDimension,
            manufacturingQuantityUnit,
            dimension,
            quantityUnit,
        )

    fun sprueLossWeight(
        sprueWeightPerPart: DynamicQuantityUnit,
        recyclingRate: Rate,
    ): DynamicQuantityUnit = services.getInjectionUtilsService().sprueLossWeightImpl(sprueWeightPerPart, recyclingRate)

    @DynamicDenominatorUnit(DynamicUnitOverride.MANUFACTURING_QUANTITY_UNIT)
    fun deployedWeightPerPart(
        netWeightPerPart: QuantityUnit,
        sprueLossWeight: DynamicQuantityUnit,
    ): DynamicQuantityUnit = services.getInjectionUtilsService().deployedWeightPerPartImpl(netWeightPerPart, sprueLossWeight)

    fun sprueRateForInjectionTime(
        netWeightPerPart: QuantityUnit,
        shapeTechnologyGroup: Text,
        projectedAreaPerPart: Area,
    ): Mono<Rate>? =
        services
            .predictNum(
                "inj",
                "SprueRate",
                mapOf(
                    NET_WEIGHT_PER_PART_PREDICTION_SERVICE to netWeightPerPart,
                    "Shape_L1" to shapeTechnologyGroup,
                    "ProjectedAreaPerPart" to projectedAreaPerPart,
                ),
            ).map { Rate(it) + BigDecimal.ONE }

    fun deployedWeightPerCycle(
        deployedWeightPerPart: DynamicQuantityUnit,
        partsPerCycleFamilyTooling: QuantityUnit,
        manufacturingDimension: Dimension,
        manufacturingQuantityUnit: Text,
    ): DynamicQuantityUnit =
        services.getInjectionUtilsService().deployedWeightPerCycleImp(
            deployedWeightPerPart,
            partsPerCycleFamilyTooling,
            manufacturingDimension,
            manufacturingQuantityUnit,
        )

    fun moldTemperature(
        injectionConfiguration: InjectionConfigurationField,
        minMoldTemperature: Temperature,
        maxMoldTemperature: Temperature,
        // Todo: Might need to be changed to average volume. See COST-50914
        peakUsableProductionVolumePerYear: DynamicQuantityUnit,
        numberOfAdditionalFamilyParts: Num,
        familyTooling: SelectableBoolean,
    ): Temperature {
        val tempDiff = maxMoldTemperature - minMoldTemperature

        val volumeWithFamilyTooling =
            when {
                familyTooling == SelectableBoolean.TRUE && numberOfAdditionalFamilyParts.res > BigDecimal.ZERO ->
                    (peakUsableProductionVolumePerYear * (BigDecimal.ONE + numberOfAdditionalFamilyParts.res))
                else -> peakUsableProductionVolumePerYear
            }

        val defaultMoldTemperature = (minMoldTemperature + maxMoldTemperature) / 2.0

        return when (injectionConfiguration.res.temperatureCorrectionType) {
            InjectionConfigurationTypes.TemperatureCorrectionType.NO_CORRECTION_FACTOR -> defaultMoldTemperature
            InjectionConfigurationTypes.TemperatureCorrectionType.DEFAULT ->
                with(volumeWithFamilyTooling.res) {
                    when {
                        inRangeEndExclusive(0, 50_000) -> maxMoldTemperature - (tempDiff * 0.1)
                        inRangeEndExclusive(50_000, 150_000) -> maxMoldTemperature - (tempDiff * 0.25)
                        inRangeEndExclusive(150_000, 500_000) -> defaultMoldTemperature
                        inRangeEndExclusive(500_000, 1_000_000) -> minMoldTemperature + (tempDiff * 0.25)
                        else -> minMoldTemperature + (tempDiff * 0.1)
                    }
                }
        }
    }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 45)
    @DefaultUnit(DefaultUnit.CELSIUS)
    fun adjustedMoldSeparationTemperature(
        injectionConfiguration: InjectionConfigurationField,
        moldSeparationTemperature: Temperature,
        contour: Contour,
        partQuality: PartQuality,
    ): Temperature =
        when (injectionConfiguration.res.temperatureCorrectionType) {
            InjectionConfigurationTypes.TemperatureCorrectionType.NO_CORRECTION_FACTOR -> moldSeparationTemperature
            InjectionConfigurationTypes.TemperatureCorrectionType.DEFAULT ->
                moldSeparationTemperature * contour.injectionFactor * partQuality.injectionFactor
        }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 44)
    @DefaultUnit(DefaultUnit.CELSIUS)
    fun adjustedMoldTemperature(
        injectionConfiguration: InjectionConfigurationField,
        moldTemperature: Temperature,
        adjustedMoldSeparationTemperature: Temperature,
        riskOfInternalStress: RiskOfInternalStress,
        injectingTemperature: Temperature,
    ): Temperature {
        val riskOfInternalStressFactor =
            when (injectionConfiguration.res.temperatureCorrectionType) {
                InjectionConfigurationTypes.TemperatureCorrectionType.NO_CORRECTION_FACTOR -> 1.0
                InjectionConfigurationTypes.TemperatureCorrectionType.DEFAULT -> {
                    when (riskOfInternalStress.res) {
                        RiskOfInternalStress.Selection.NO_RISK -> 1.0
                        RiskOfInternalStress.Selection.LOW_RISK -> 1.05
                        RiskOfInternalStress.Selection.HIGH_RISK -> 1.1
                        RiskOfInternalStress.Selection.VERY_HIGH_RISK -> 1.15
                    }
                }
            }

        val adjustedMoldTemp = moldTemperature * riskOfInternalStressFactor.toBigDecimal()
        val minTemperatureDiff = 7.toBigDecimal()

        return if (injectingTemperature.res <= adjustedMoldTemp.res ||
            adjustedMoldSeparationTemperature.res <= adjustedMoldTemp.res + minTemperatureDiff
        ) {
            adjustedMoldSeparationTemperature - minTemperatureDiff
        } else {
            adjustedMoldTemp
        }
    }

    fun sliderConceptId(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
    ): Mono<SliderConceptIdInjection> =
        lookupReaderService.getInj2ShapeData(calculationContext!!.accessCheck, shapeId).map {
            it.slilderConceptId
        }

    fun inputGroup(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
    ) = lookupReaderService.getInputGroupFromShapeLookup(calculationContext!!.accessCheck, shapeId, Model.INJ2)

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 60)
    @DefaultUnit(DefaultUnit.SECOND)
    fun injectionTime(
        partVolume: DynamicQuantityUnit,
        injectionFlow: VolumeFlowRate,
        partsPerCycleFamilyTooling: DynamicQuantityUnit,
        sprueRateForInjectionTime: Rate,
    ): Time {
        val seconds =
            when (partVolume.res) {
                BigDecimal.ZERO -> 2.04.toBigDecimal()
                else ->
                    partVolume.resultIn(VolumeUnits.CCM) * sprueRateForInjectionTime.res * partsPerCycleFamilyTooling.res /
                        injectionFlow.inCcmPerSecond
            }

        return Time(seconds, TimeUnits.SECOND)
    }

    fun shotWeightPerPart(
        netWeightPerPart: QuantityUnit,
        sprueWeightPerPart: QuantityUnit,
    ) = netWeightPerPart + sprueWeightPerPart

    fun shotWeightPerCycle(
        shotWeightPerPart: QuantityUnit,
        partsPerCycleFamilyTooling: QuantityUnit,
    ) = shotWeightPerPart * partsPerCycleFamilyTooling

    fun utilizationRate(
        netWeightPerPart: DynamicQuantityUnit,
        shapeTechnologyGroup: Text,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_COUNTRY_INFO,
            nameFilter = TSET_COST_COUNTRY_ID_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        ) value: Map<ObjectId, Text>,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_COUNTRY_INFO,
            nameFilter = TSET_COST_COUNTRY_ID_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        @FieldName("location")
        costFactorLocations: Map<ObjectId, Text>,
        location: Text,
    ): Mono<Rate> {
        val costFactorForCurrentStepId = getCostFactorForCurrentStepId(costFactorLocations, location)
        val countryId =
            requireNotNull(value[costFactorForCurrentStepId]) {
                "CountryId for $costFactorForCurrentStepId not found"
            }
        return getCountry(countryId).flatMap { country ->
            services
                .predictNum(
                    "inj",
                    "UtilizationRate",
                    mapOf(
                        NET_WEIGHT_PER_PART_PREDICTION_SERVICE to netWeightPerPart,
                        "Shape_L1" to shapeTechnologyGroup,
                        "Country" to country,
                    ),
                ).map { Rate(it) }
        }
    }

    fun scrapRate(): Rate = Rate(0.01.toBigDecimal())

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 3)
    fun sliderType(
        netWeightPerPart: DynamicQuantityUnit,
        shapeTechnologyGroup: Text,
        partsPerCycleFamilyTooling: DynamicQuantityUnit,
        sliderConceptId: SliderConceptIdInjection,
    ): Mono<SliderType> {
        val statisticModelResult =
            services
                .predict<Int>(
                    "inj",
                    "GateValveType",
                    mapOf(
                        NET_WEIGHT_PER_PART_PREDICTION_SERVICE to netWeightPerPart,
                        "Shape_L1" to shapeTechnologyGroup,
                        "PartsPerCycle" to partsPerCycleFamilyTooling,
                    ),
                ).map { SliderType.valueOf(it) }
        return if (sliderConceptId == SliderConceptIdInjection.SLIDER_CONCEPT_ID_1) {
            if (statisticModelResult == SliderType.NO_SLIDER.toMono()) statisticModelResult else SliderType.NO_SLIDER.toMono()
        } else {
            if (statisticModelResult != SliderType.NO_SLIDER.toMono()) statisticModelResult else SliderType.MECHANICAL_SLIDER.toMono()
        }
    }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 20)
    fun partRemoval(
        netWeightPerPart: DynamicQuantityUnit,
        shapeTechnologyGroup: Text,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_COUNTRY_INFO,
            nameFilter = TSET_COST_COUNTRY_ID_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        ) value: Map<ObjectId, Text>,
        @FilteredChildren(
            name = Entities.MD_COSTFACTORS_COUNTRY_INFO,
            nameFilter = TSET_COST_COUNTRY_ID_ENTITY_NAME,
            directOnly = false,
            startFromFirstAncestor = Entities.MANUFACTURING,
        )
        @FieldName("location")
        costFactorLocations: Map<ObjectId, Text>,
        location: Text,
        sprueRate: Rate,
    ): Mono<PartRemovalType> {
        val costFactorForCurrentStepId = getCostFactorForCurrentStepId(costFactorLocations, location)
        val netWeightPerPartInRawValue =
            if (netWeightPerPart.numeratorUnit == null && netWeightPerPart.denominatorUnit == null) {
                netWeightPerPart.res
            } else {
                netWeightPerPart.resultIn(WeightUnits.KILOGRAM, PiecesUnits.PIECE)
            }

        return if (netWeightPerPartInRawValue >= 0.3.toBigDecimal()) {
            PartRemovalType.ROBOT_UNLOAD.toMono()
        } else {
            val countryId =
                requireNotNull(value[costFactorForCurrentStepId]) {
                    "CountryId for $costFactorForCurrentStepId not found"
                }
            getCountry(countryId).flatMap { country ->
                services
                    .predict<Int>(
                        "inj",
                        "UnloadingType",
                        mapOf(
                            NET_WEIGHT_PER_PART_PREDICTION_SERVICE to netWeightPerPartInRawValue,
                            "Shape_L1" to shapeTechnologyGroup,
                            "Country" to country,
                            "SprueRate" to sprueRate,
                        ),
                    ).map {
                        // force *_WITH_TWO_STAGE_EJECTOR predictions into their w/o counterpart
                        when (it) {
                            2 -> 1
                            4 -> 3
                            6 -> 5
                            else -> it
                        }
                    }.map { PartRemovalType.valueOf(it) }
            }
        }
    }

    fun projectedAreaPerCycle(
        projectedAreaPerPart: Area,
        partsPerCycleFamilyTooling: DynamicQuantityUnit,
    ): Area = projectedAreaPerPart * partsPerCycleFamilyTooling

    fun ratioFlowPathToWall(
        partLength: Length,
        partWidth: Length,
        partHeight: Length,
        mainWallThickness: Length,
        maxWallThickness: Length,
    ): Num {
        val mc = MathContext(10)

        val term1 = (partLength / 2.0).res.pow(2, mc)
        val term2 = (partWidth / 2.0).res.pow(2, mc)

        val sqrt = (term1 + term2).sqrt(mc)
        val avg = (mainWallThickness + maxWallThickness) / 2.0

        return Num(sqrt + partHeight.res) / avg
    }

    // BCT
    fun minInternalMoldPressureCalc(minInternalMoldPressure: Pressure) = minInternalMoldPressure

    // BCT
    fun maxInternalMoldPressureCalc(maxInternalMoldPressure: Pressure) = maxInternalMoldPressure

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 39)
    @DefaultUnit(DefaultUnit.BAR)
    fun internalMoldPressure(
        injectionConfiguration: InjectionConfigurationField,
        ratioFlowPathToWall: Num,
        maxWallThickness: Length,
        materialFactor: Rate,
        minInternalMoldPressure: Pressure,
        maxInternalMoldPressure: Pressure,
        partQuality: PartQuality,
        riskOfInternalStress: RiskOfInternalStress,
        injectionType: InjectionType,
    ): Pressure =
        services.getInjectionUtilsService().internalMoldPressureImpl(
            injectionConfiguration,
            ratioFlowPathToWall,
            maxWallThickness,
            materialFactor,
            minInternalMoldPressure,
            maxInternalMoldPressure,
            partQuality,
            riskOfInternalStress,
            injectionType,
        )

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 110)
    fun partingLine(shapeId: Text): Mono<PartingLine> =
        services
            .getLookupTable("ManufacturingStepInjection_CycleTime", injectionCycleTimeReader)
            .filter {
                shapeId.res == it.shapeId
            }.single()
            .map { it.partingLine }

    @CalculationPreview(4, "partsPerCyclePlasticInjection")
    @SummaryView(SummaryView.PROCESS, 30, "partsPerCyclePlasticInjection")
    fun partPerCyclePreview(partsPerCycle: QuantityUnit): QuantityUnit = partsPerCycle

    fun internalPartsPerCycle(
        selectedPartsPerCycle: DynamicQuantityUnit,
        injectionConfiguration: InjectionConfigurationField,
        sliderConceptId: SliderConceptIdInjection,
        newLength: Length,
        newWidth: Length,
        gapBetweenParts: Length,
        gapToTool: Length,
        internalMoldPressure: Pressure,
        projectedAreaPerPart: Area,
        partingLine: PartingLine,
        partRemoval: PartRemovalType,
    ): Mono<DynamicQuantityUnit> =
        Mono
            .zip(
                cavityLength(sliderConceptId, selectedPartsPerCycle),
                cavityWidth(sliderConceptId, selectedPartsPerCycle),
            ).flatMap { (cavityLength, cavityWidth) ->
                val ncf =
                    necessaryClampingForce(
                        injectionConfiguration,
                        internalMoldPressure,
                        projectedAreaPerPart,
                        selectedPartsPerCycle,
                        partingLine,
                    )
                templateName(
                    injectionConfiguration,
                    newLength,
                    newWidth,
                    gapBetweenParts,
                    gapToTool,
                    ncf,
                    cavityLength,
                    cavityWidth,
                    partRemoval,
                ).map {
                    selectedPartsPerCycle
                }
            }.onErrorResume {
                if (selectedPartsPerCycle.resultIn(PiecesUnits.PIECE) < BigDecimal.ONE) {
                    throw TemplateNotFoundException(
                        lookupName = "internalPartsPerCycle (ManufacturingStepPlasticInjection2)",
                        lookupInputs =
                            mapOf(
                                "selectedPartRangeIndex" to selectedPartsPerCycle.toString(),
                            ),
                    )
                }
                internalPartsPerCycle(
                    selectedPartsPerCycle / 2,
                    injectionConfiguration,
                    sliderConceptId,
                    newLength,
                    newWidth,
                    gapBetweenParts,
                    gapToTool,
                    internalMoldPressure,
                    projectedAreaPerPart,
                    partingLine,
                    partRemoval,
                )
            }

    fun initialServiceLifeInCycles(): Num = Num(1000000)

    fun possiblePartsPerCycle(): ListOfStrings = ListOfStrings(listOf("1", "2", "4", "8", "16", "32", "64", "128"))

    fun cavityLength(
        sliderConceptId: SliderConceptIdInjection,
        partsPerCycleFamilyTooling: DynamicQuantityUnit,
    ): Mono<Num> =
        services
            .getInjectionUtilsService()
            .cavityLengthImpl(
                calculationContext!!.accessCheck,
                sliderConceptId,
                partsPerCycleFamilyTooling.toQuantityUnit(),
            )

    fun cavityWidth(
        sliderConceptId: SliderConceptIdInjection,
        partsPerCycleFamilyTooling: DynamicQuantityUnit,
    ): Mono<Num> =
        services
            .getInjectionUtilsService()
            .cavityWidthImpl(
                calculationContext!!.accessCheck,
                sliderConceptId,
                partsPerCycleFamilyTooling.toQuantityUnit(),
            )

    @ReadOnly
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 21)
    fun numberOfVerticalCavities(
        cavitiesDisposition: CavitiesDisposition,
        cavityLength: Num,
        cavityWidth: Num,
    ): Num =
        when (cavitiesDisposition.res) {
            CavitiesDisposition.Selection.DEFAULT -> cavityWidth
            CavitiesDisposition.Selection.INVERTED -> cavityLength
        }

    @ReadOnly
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 22)
    fun numberOfHorizontalCavities(
        cavitiesDisposition: CavitiesDisposition,
        cavityLength: Num,
        cavityWidth: Num,
    ): Num =
        when (cavitiesDisposition.res) {
            CavitiesDisposition.Selection.DEFAULT -> cavityLength
            CavitiesDisposition.Selection.INVERTED -> cavityWidth
        }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 40)
    @DefaultUnit(DefaultUnit.KILONEWTON)
    fun necessaryClampingForce(
        injectionConfiguration: InjectionConfigurationField,
        internalMoldPressure: Pressure,
        projectedAreaPerPart: Area,
        partsPerCycleFamilyTooling: DynamicQuantityUnit,
        partingLine: PartingLine,
    ): Force =
        services
            .getInjectionUtilsService()
            .necessaryClampingForceImpl(
                injectionConfiguration,
                internalMoldPressure,
                projectedAreaPerPart,
                partsPerCycleFamilyTooling.toQuantityUnit(),
                partingLine,
            )

    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 41)
    fun screwDiameter(templateName: Text): Mono<Length> {
        val lookupName = "ManufacturingStepInjection2_ScrewDiameter"
        return services
            .getLookupTable(lookupName, injection2ScrewDiameterReader)
            .filter { templateName == it.templateName }
            .singleOrEmpty()
            .map {
                val minScrewDiameter =
                    minOf(
                        it.screw1Diameter.inMillimeter,
                        it.screw2Diameter.inMillimeter,
                        it.screw3Diameter.inMillimeter,
                    )
                Length(minScrewDiameter, LengthUnits.MILLIMETER)
            }.switchIfEmpty(
                Mono.error(
                    InternalServerException(
                        ErrorCode.FIELD_CALCULATION_ERROR,
                        "Could not find $templateName in $lookupName lookup",
                    ),
                ),
            )
    }

    @DefaultUnit(DefaultUnit.CCM_PER_SECONDS)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 42)
    fun injectionFlow(
        injectionConfiguration: InjectionConfigurationField,
        averageInjectionVelocity: Speed,
        screwDiameter: Length,
        maxInjectionFlow: VolumeFlowRate,
    ): VolumeFlowRate {
        val screwRadius = screwDiameter.div(2.0)
        val injectionFlow1 =
            ((screwRadius.inMillimeter * screwRadius.inMillimeter).times(Math.PI.toBigDecimal()) * averageInjectionVelocity.inMMPerSec)
        return VolumeFlowRate(
            min(injectionFlow1, maxInjectionFlow.inCmmPerSecond),
            VolumeFlowRateUnits.CMM_PER_SECONDS,
        ) * injectionConfiguration.res.injectionFlowReductionFactor
    }

    fun maxInjectionFlow(
        templateName: Text,
        screwDiameter: Length,
    ): Mono<VolumeFlowRate> =
        services
            .getLookupTable(
                "ManufacturingStepInjection2_ScrewDiameter",
                injection2ScrewDiameterReader,
            ).filter { templateName == it.templateName }
            .elementAt(0)
            .mapNotNull {
                when {
                    screwDiameter <= it.screw2Diameter -> it.maxInjectionFlow1
                    screwDiameter < it.screw3Diameter -> it.maxInjectionFlow2
                    else -> it.maxInjectionFlow3
                }
            }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 120)
    fun riskOfInternalStress(shapeId: Text): Mono<RiskOfInternalStress> =
        services
            .getLookupTable("ManufacturingStepInjection_CycleTime", injectionCycleTimeReader)
            .filter {
                shapeId.res == it.shapeId
            }.single()
            .map { it.riskOfInternalStress }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 130)
    fun contour(shapeId: Text): Mono<Contour> =
        services
            .getLookupTable("ManufacturingStepInjection_CycleTime", injectionCycleTimeReader)
            .filter {
                shapeId.res == it.shapeId
            }.single()
            .map { it.contour }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 90)
    @DefaultUnit(DefaultUnit.SECOND)
    fun criticalPlasticizingTime(
        plasticizingTime: Time,
        holdingPressureTime: Time,
        coolingTimeWithoutAdditions: Time,
    ): Time {
        val seconds = plasticizingTime + holdingPressureTime - coolingTimeWithoutAdditions

        return Time(seconds.res.max(BigDecimal.ZERO), TimeUnits.SECOND)
    }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 70)
    @DefaultUnit(DefaultUnit.SECOND)
    fun holdingPressureTime(
        coolingTimeWithoutAdditions: Time,
        shrinkageBehavior: ShrinkageBehavior,
    ): Time {
        val shrinkageBehaviorFactor =
            when (shrinkageBehavior.res) {
                ShrinkageBehavior.Selection.LOW_VOLUME_SHRINKAGE -> 0.3
                ShrinkageBehavior.Selection.AVERAGE_VOLUME_SHRINKAGE -> 0.5
                ShrinkageBehavior.Selection.HIGH_VOLUME_SHRINKAGE -> 0.6
            }

        return coolingTimeWithoutAdditions * shrinkageBehaviorFactor
    }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 100)
    @DefaultUnit(DefaultUnit.SECOND)
    fun coolingTimeIncludingAdditions(
        coolingTimeWithoutAdditions: Time,
        injectionTime: Time,
        criticalPlasticizingTime: Time,
    ): Time = coolingTimeWithoutAdditions + injectionTime + criticalPlasticizingTime

    fun maxPartsPerCycle(
        shapeId: Text,
        netWeightPerPart: DynamicQuantityUnit,
        dimension: Dimension,
        quantityUnit: Text,
    ): Mono<DynamicQuantityUnit> =
        services
            .getLookupTable("ManufacturingStepInjection2_Tool", injectionTool2InvestReader)
            .filter {
                shapeId.res == it.shapeId && netWeightPerPart.res <= it.maxWeight.res
            }.elementAt(0)
            .map {
                DynamicQuantityUnit(it.maxPartsPerCycle, PiecesUnits.PIECE, null)
                    .withInputUnit(dimension.res.typeUnit(quantityUnit.res), null)
            }

    fun newLength(
        inputGroup: Text,
        partOuterDiameter: Length,
        partLength: Length,
    ): Length = if (inputGroup.res == "cylinder") partOuterDiameter else partLength

    fun newWidth(
        inputGroup: Text,
        partOuterDiameter: Length,
        partWidth: Length,
    ): Length = if (inputGroup.res == "cuboid") partWidth else partOuterDiameter

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 25)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun gapBetweenParts(
        newLength: Length,
        newWidth: Length,
    ): Length =
        when {
            newWidth.res < 0.05.toBigDecimal() && newLength.res < 0.05.toBigDecimal() -> Length(0.02, LengthUnits.METER)
            newWidth.res < 0.1.toBigDecimal() && newLength.res < 0.1.toBigDecimal() -> Length(0.03, LengthUnits.METER)
            newWidth.res < 0.3.toBigDecimal() && newLength.res < 0.3.toBigDecimal() -> Length(0.05, LengthUnits.METER)
            newWidth.res < 0.8.toBigDecimal() && newLength.res < 0.8.toBigDecimal() -> Length(0.1, LengthUnits.METER)
            else -> Length(0.15, LengthUnits.METER)
        }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 27)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun gapToTool(): Length = Length(0.10, LengthUnits.METER)

    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 28)
    fun calculatedToolHeight(
        toolOrientation: ToolOrientation,
        cavitiesDisposition: CavitiesDisposition,
        gapToTool: Length,
        gapBetweenParts: Length,
        newLength: Length,
        cavityLength: Num,
        newWidth: Length,
        cavityWidth: Num,
    ): Length =
        services.getInjectionUtilsService().calculatedToolHeightImpl(
            toolOrientation,
            cavitiesDisposition,
            gapToTool,
            gapBetweenParts,
            newLength,
            cavityLength,
            newWidth,
            cavityWidth,
        )

    @ReadOnly
    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 29)
    fun calculatedToolWidth(
        toolOrientation: ToolOrientation,
        cavitiesDisposition: CavitiesDisposition,
        gapToTool: Length,
        gapBetweenParts: Length,
        newLength: Length,
        cavityLength: Num,
        newWidth: Length,
        cavityWidth: Num,
    ): Length =
        services.getInjectionUtilsService().calculatedToolWidthImpl(
            toolOrientation,
            cavitiesDisposition,
            gapToTool,
            gapBetweenParts,
            newLength,
            cavityLength,
            newWidth,
            cavityWidth,
        )

    @DontSortOptions
    @SummaryView(SummaryView.PROCESS, 20, "configurationPlasticInjection")
    @CalculationPreview(3, "configurationPlasticInjection")
    fun templateName(
        injectionConfiguration: InjectionConfigurationField,
        newLength: Length,
        newWidth: Length,
        gapBetweenParts: Length,
        gapToTool: Length,
        necessaryClampingForce: Force,
        cavityLength: Num,
        cavityWidth: Num,
        partRemoval: PartRemovalType,
    ): Mono<Text> {
        val lookupName = "ManufacturingStepPlasticInjection2_Templates"
        return services
            .getLookupTable(lookupName, injection2TemplatesReader)
            .filter {
                val subFilter =
                    services.getInjectionUtilsService().templateLookupBasicInfoImpl(
                        it.templateName,
                        it.lockingForce,
                        injectionConfiguration.res.lockingForceSafetyFactor,
                        necessaryClampingForce,
                        partRemoval,
                    )

                val distanceHorizontal = if (it.hasTieBars) it.tieBarDistanceHori else it.mountingTableWidth
                val distanceVertical = it.mountingTableHeight

                val feasibleCaseNum =
                    services.getInjectionUtilsService().templateLookupFeasibleCaseImpl(
                        distanceHorizontal,
                        distanceVertical,
                        gapToTool,
                        gapBetweenParts,
                        newLength,
                        cavityLength,
                        newWidth,
                        cavityWidth,
                    )

                (subFilter && (feasibleCaseNum in 1..4))
            }.collectList()
            .mapNotNull { injectionTemplates ->
                if (injectionTemplates.isEmpty()) {
                    throw TemplateNotFoundException(
                        lookupName = lookupName,
                        lookupInputs =
                            mapOf(
                                "necessaryClampingForce" to necessaryClampingForce.toString(),
                                "partRemoval" to partRemoval.toString(),
                                "gapToTool" to gapToTool.toString(),
                                "gapBetweenParts" to gapBetweenParts.toString(),
                                "newLength" to newLength.toString(),
                                "cavityLength" to cavityLength.toString(),
                                "newWidth" to newWidth.toString(),
                                "cavityWidth" to cavityWidth.toString(),
                            ),
                    )
                } else {
                    injectionTemplates.minByOrNull {
                        it.lockingForce.res
                    }
                }
            }.map { Text(it!!.templateName) }
    }

    fun feasibleCaseNumForTemplate(
        templateName: Text,
        newLength: Length,
        newWidth: Length,
        gapBetweenParts: Length,
        gapToTool: Length,
        cavityLength: Num,
        cavityWidth: Num,
    ): Mono<Num> =
        services
            .getLookupTable("ManufacturingStepPlasticInjection2_Templates", injection2TemplatesReader)
            .filter {
                templateName.res == it.templateName
            }.single()
            .map {
                val distanceHorizontal = if (it.hasTieBars) it.tieBarDistanceHori else it.mountingTableWidth
                val feasibleCaseNum =
                    services.getInjectionUtilsService().templateLookupFeasibleCaseImpl(
                        distanceHorizontal,
                        it.mountingTableHeight,
                        gapToTool,
                        gapBetweenParts,
                        newLength,
                        cavityLength,
                        newWidth,
                        cavityWidth,
                    )
                Num(feasibleCaseNum.toDouble())
            }

    @ReadOnly
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 23)
    fun toolOrientation(feasibleCaseNumForTemplate: Num): ToolOrientation =
        services.getInjectionUtilsService().toolOrientationImpl(feasibleCaseNumForTemplate)

    fun cavitiesDisposition(feasibleCaseNumForTemplate: Num): CavitiesDisposition =
        services.getInjectionUtilsService().cavitiesDispositionImpl(feasibleCaseNumForTemplate)

    @SummaryView(SummaryView.PROCESS, 40, fieldName = "cycleTimePlasticInjection")
    fun cycleTimeSummaryView(cycleTime: CycleTime) = cycleTime

    @SummaryView(SummaryView.PROCESS, 50, fieldName = "manufacturingScrapRatePlasticInjection")
    fun manufacturingScrapRateSummaryView(manufacturingScrapRate: Rate): Rate = manufacturingScrapRate

    @DontSortOptions
    @DontExport
    @Input
    fun injectionConfigurationKey(): Mono<InjectionConfigurationKey> = services.getDefaultConfigurationKey(::InjectionConfigurationKey)

    @EngineTransient
    fun injectionConfiguration(injectionConfigurationKey: InjectionConfigurationKey) =
        services.getConfiguration(injectionConfigurationKey, ::InjectionConfigurationField)

    // TODO: why did @Children(Entities.MATERIAL) not work for the other args? THOHOE
    // TODO: Move logic to behaviour classes when COST-39436 is done
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 50)
    @DefaultUnit(DefaultUnit.SECOND)
    fun coolingTimeWithoutAdditions(
        injectionConfiguration: InjectionConfigurationField,
        maxWallThickness: Length,
        internalThermalDiffusivity: Diffusivity,
        injectingTemperature: Temperature,
        adjustedMoldTemperature: Temperature,
        adjustedMoldSeparationTemperature: Temperature,
        deployedWeightPerCycle: DynamicQuantityUnit,
    ): Time {
        val defaultCoolingTimeWithoutAdditions =
            services
                .getInjectionUtilsService()
                .coolingTimeWithoutAdditionsImpl(
                    maxWallThickness,
                    internalThermalDiffusivity,
                    injectingTemperature,
                    adjustedMoldTemperature,
                    adjustedMoldSeparationTemperature,
                )
        return when (injectionConfiguration.res.coolingTimeBehaviour) {
            InjectionConfigurationTypes.CoolingTimeBehaviourInjectionType.DEFAULT -> defaultCoolingTimeWithoutAdditions
            InjectionConfigurationTypes.CoolingTimeBehaviourInjectionType.WEIGHT_BASED -> {
                val maxWallThicknessFactor =
                    13.635 * maxWallThickness.inMillimeter.toDouble().pow(2) - 84.585 *
                        maxWallThickness.inMillimeter.toDouble() + 117.32
                val interimCoolingTimeRes =
                    max(MIN_INTERIM_COOLING_TIME.inSeconds, (defaultCoolingTimeWithoutAdditions - maxWallThicknessFactor).inSeconds)
                val sizeFactorLIN =
                    deployedWeightPerCycle
                        .times(1000.0)
                        .times(-0.0003)
                        .resultIn(WeightUnits.KILOGRAM)
                        .toDouble() + 1.343
                val sizeFactorPOW =
                    3.696 *
                        deployedWeightPerCycle
                            .times(1000.0)
                            .resultIn(WeightUnits.KILOGRAM)
                            .toDouble()
                            .pow(-0.21)
                val sizeFactor =
                    if (deployedWeightPerCycle.resultIn(
                            WeightUnits.KILOGRAM,
                        ) < 0.3.toBigDecimal()
                    ) {
                        sizeFactorLIN
                    } else {
                        sizeFactorPOW
                    }
                Time(interimCoolingTimeRes, TimeUnits.SECOND) / sizeFactor
            }
        }
    }

    // TODO: Move logic to behaviour classes when COST-39436 is done
    fun selectedPartsPerCycle(
        injectionConfiguration: InjectionConfigurationField,
        averageProcessedVolumeOverLifeTime: QuantityUnit,
        maxPartsPerCycle: DynamicQuantityUnit,
        initialServiceLifeInCycles: Num,
        possiblePartsPerCycle: ListOfStrings,
        netWeightPerPart: QuantityUnit,
        dimension: Dimension,
        quantityUnit: Text,
    ): DynamicQuantityUnit {
        val unit = dimension.res.typeUnit(quantityUnit.res)
        return when (injectionConfiguration.res.partsPerCycleBehaviour) {
            InjectionConfigurationTypes.PartsPerCycleBehaviourInjectionType.DEFAULT -> {
                val selectedVal =
                    min(
                        (averageProcessedVolumeOverLifeTime.res / initialServiceLifeInCycles.res).setScale(
                            0,
                            RoundingMode.UP,
                        ),
                        maxPartsPerCycle.res,
                    )
                val possibleValues =
                    possiblePartsPerCycle.res
                        .map {
                            it.toInt()
                        }.sorted()
                val fallback = 1
                val res = (BigDecimal(possibleValues.find { it >= selectedVal.toInt() } ?: fallback))
                DynamicQuantityUnit(res, unit.baseUnit, null).withInputUnit(unit, null)
            }

            InjectionConfigurationTypes.PartsPerCycleBehaviourInjectionType.WEIGHT_BASED -> {
                val resPartsPerCycle =
                    when {
                        netWeightPerPart.times(1000.0).res < 10.toBigDecimal() -> 8.0
                        netWeightPerPart.times(1000.0).res < 50.toBigDecimal() -> 4.0
                        netWeightPerPart.times(1000.0).res < 200.toBigDecimal() -> 2.0
                        else -> 1.0
                    }
                val selectedVal = min(resPartsPerCycle.toBigDecimal(), maxPartsPerCycle.res)

                DynamicQuantityUnit(selectedVal, unit.baseUnit, null).withInputUnit(unit, null)
            }
        }
    }

    // region Entity creation

    @EntityCreation(Entities.SETUP)
    fun createSetup(): Flux<ManufacturingEntity> =
        listOf(
            createEntity(
                name = "Tool change",
                clazz = SetupNormalInjection::class,
                entityType = Entities.SETUP,
                fields =
                    mapOf(
                        "designation" to Text("Tool change"),
                        "setupType" to SetupType.INTERNAL_SETUP,
                    ),
            ),
        ).toFlux()

    @TsetSuppress("tset:engine:field-override-configuration-value")
    fun setupBehaviour(injectionConfiguration: InjectionConfigurationField) = Text(injectionConfiguration.res.setupBehaviour.name)

    @EntityCreation(Entities.SETUP)
    fun createMachineSizeBasedSetup(setupBehaviour: Text): Flux<ManufacturingEntity>? =
        when (InjectionConfigurationTypes.SetupBehaviourInjectionType.valueOf(setupBehaviour.res)) {
            InjectionConfigurationTypes.SetupBehaviourInjectionType.MACHINE_SIZE_BASED ->
                listOf(
                    createEntity(
                        name = "Tool preparation",
                        clazz = Setup::class,
                        entityType = Entities.SETUP,
                        fields =
                            mapOf(
                                "designation" to Text("Tool preparation"),
                                "requiredLabor" to Num(1.0),
                                "setupTime" to Time(BigDecimal.ONE, TimeUnits.HOUR),
                                "setupType" to SetupType.EXTERNAL_SETUP,
                            ),
                    ),
                ).toFlux()

            InjectionConfigurationTypes.SetupBehaviourInjectionType.DEFAULT -> null
        }

    @DisableEntityCreationInModularizedEntity
    @EntityCreation(Entities.BOM_ENTRY)
    fun createSub(
        createInsertsAutomatically: SelectableBoolean?,
        injectionType: InjectionType?,
        numberOfInserts: Num?,
        numberOfDifferentInsertTypes: Pieces?,
        key: Text,
    ): List<BomEntry>? {
        // Parameters need to be nullable so we would continue calculation also if they are not given which we need as we skip subcreation for modularized entities
        if (createInsertsAutomatically == null ||
            injectionType == null ||
            numberOfInserts == null ||
            numberOfDifferentInsertTypes == null
        ) {
            return emptyList()
        }

        val quantity =
            when {
                numberOfDifferentInsertTypes.res.compareTo(BigDecimal.ONE) == 0 -> Pieces(numberOfInserts)
                else -> Pieces(1.0)
            }
        return if (injectionType == InjectionType.INSERT && createInsertsAutomatically == SelectableBoolean.TRUE) {
            List(numberOfDifferentInsertTypes.res.toInt()) { i ->
                val bomEntry =
                    createEntity(
                        name = BomEntry::class.simpleName!!,
                        clazz = BomEntry::class,
                        fields = mapOf("quantity" to quantity),
                        entityType = Entities.BOM_ENTRY,
                    )
                val manufacturing =
                    createManufacturing(
                        name = "Insert ${i + 1}",
                        clazz = RoughManufacturing::class,
                        args =
                            mapOf(
                                "key" to key.res,
                                "isPart" to false,
                            ),
                        fields =
                            mapOf(
                                "partDesignation" to Text("Insert"),
                            ),
                    )
                bomEntry.addChild(manufacturing)
                bomEntry
            }
        } else {
            null
        }
    }

    // endregion
}
