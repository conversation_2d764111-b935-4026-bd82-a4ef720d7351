package com.nu.bom.core.technologies.steps.plasticcoating

import com.nu.bom.core.exception.userException.IncompatibleUserInputs
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.CalculationPreview
import com.nu.bom.core.manufacturing.annotations.Children
import com.nu.bom.core.manufacturing.annotations.Condition
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DisableEntityCreationInModularizedEntity
import com.nu.bom.core.manufacturing.annotations.DontSortOptions
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityLinkField
import com.nu.bom.core.manufacturing.annotations.EntityLinkProvider
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.ExpectedParents
import com.nu.bom.core.manufacturing.annotations.ExternalDependency
import com.nu.bom.core.manufacturing.annotations.FieldIndex
import com.nu.bom.core.manufacturing.annotations.IgnoreForOverwrittenState
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Modularized
import com.nu.bom.core.manufacturing.annotations.Nocalc
import com.nu.bom.core.manufacturing.annotations.ObjectView
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.Precompute
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.RequiredFor
import com.nu.bom.core.manufacturing.annotations.Selectable
import com.nu.bom.core.manufacturing.annotations.SubLabel
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.entities.Labor
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ManufacturingStep
import com.nu.bom.core.manufacturing.entities.SingleGroupManufacturingStep
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.ManufacturingStepType
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.AreaUnits
import com.nu.bom.core.manufacturing.fieldTypes.Bool
import com.nu.bom.core.manufacturing.fieldTypes.CycleTime
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.EntityRef
import com.nu.bom.core.manufacturing.fieldTypes.FoamingType
import com.nu.bom.core.manufacturing.fieldTypes.InjectionType
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.PaintCoatType
import com.nu.bom.core.manufacturing.fieldTypes.Pieces
import com.nu.bom.core.manufacturing.fieldTypes.PlasticCoatingPostProcessingType
import com.nu.bom.core.manufacturing.fieldTypes.PlasticCoatingSurfaceActivationType
import com.nu.bom.core.manufacturing.fieldTypes.PlasticCoatingSurfacePrepType
import com.nu.bom.core.manufacturing.fieldTypes.PlasticCoatingType
import com.nu.bom.core.manufacturing.fieldTypes.PlasticPaintingBaseCoatType
import com.nu.bom.core.manufacturing.fieldTypes.PlasticPaintingClearCoatType
import com.nu.bom.core.manufacturing.fieldTypes.PlasticPaintingTopCoatType
import com.nu.bom.core.manufacturing.fieldTypes.PlasticPaintingType
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.Speed
import com.nu.bom.core.manufacturing.fieldTypes.SpeedUnits
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.Time
import com.nu.bom.core.manufacturing.fieldTypes.TimeUnits
import com.nu.bom.core.manufacturing.fieldTypes.TotalOrOneSidePaintingType
import com.nu.bom.core.technologies.manufacturings.inj.material.MaterialPlasticPaint
import com.nu.bom.core.technologies.manufacturings.inj.material.MaterialPlasticPaint.Companion.MATERIAL_PAINT_BASE
import com.nu.bom.core.technologies.manufacturings.inj.material.MaterialPlasticPaint.Companion.MATERIAL_PAINT_CLEAR
import com.nu.bom.core.technologies.manufacturings.inj.material.MaterialPlasticPaint.Companion.MATERIAL_PAINT_TOP
import com.nu.bom.core.technologies.service.ShapeLookupReaderService
import com.nu.bom.core.technologies.steps.foaming.ManufacturingStepFoamingInjection
import com.nu.bom.core.technologies.steps.insertinj.ManufacturingStepInsertInjection
import com.nu.bom.core.technologies.steps.plasticcoating.ManufacturingStepPlasticPaintingUtils.areaToPaintNetImpl
import com.nu.bom.core.technologies.steps.plasticcoating.ManufacturingStepPlasticPaintingUtils.areaToPaintNetMaskedImpl
import com.nu.bom.core.technologies.steps.plasticcoating.ManufacturingStepPlasticPaintingUtils.boundingRectanglesImpl
import com.nu.bom.core.technologies.steps.plasticcoating.ManufacturingStepPlasticPaintingUtils.shapeRelatedPaintLossRateImpl
import com.nu.bom.core.technologies.steps.plasticcoating.cycletimesteps.PaintingCycleTimeStepGroup
import com.nu.bom.core.technologies.steps.plasticcoating.labor.PlasticPaintingLabor
import com.nu.bom.core.technologies.steps.plasticcoating.systemparameter.SystemParameterPlasticPainting
import com.nu.bom.core.technologies.steps.plasticinj.ManufacturingStepPlasticInjection2
import com.tset.bom.clients.tsetdel.model.TsetDelCoatingAreaResponse
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toFlux
import java.math.BigDecimal
import java.math.RoundingMode

@EntityType(Entities.MANUFACTURING_STEP)
@Modularized(
    technologies = [Model.INJ2],
    parents = [
        ExpectedParents(model = Model.INJ2, type = Entities.MANUFACTURING),
        ExpectedParents(model = Model.INJ2, type = Entities.PROCESSED_MATERIAL),
    ],
    dimensions = [Dimension.Selection.NUMBER],
    userCreatable = true,
)
class ManufacturingStepPlasticPainting(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = SingleGroupManufacturingStep(name)

    companion object {
        const val CLASS_NAME = "ManufacturingStepPlasticPainting"

        const val GLASURIT_PRIMER_KEY = "Glasurit 934-10 1K PLASTICS PRIMER-RAW_MATERIAL_PAINT"
        const val HS_2K_CV_CLEAR_KEY = "HS 2K CV clear coat-RAW_MATERIAL_PAINT"
        const val HS_MULTI_CLEAR_KEY = "HS Multi Clear-RAW_MATERIAL_PAINT"
        const val GLASURIT_55_KEY = "Glasurit 55 Line-RAW_MATERIAL_PAINT"
    }

    @Input
    @SubLabel
    @ReadOnly
    @Selectable(value = "manufacturingStepType", getDisplayNameFromPathQuery = true)
    fun manufacturingStepType(): Text = Text(ManufacturingStepType.CHEMICALS.name)

    @EntityLinkProvider(
        [
            ManufacturingStepInsertInjection.CLASS_NAME,
            ManufacturingStepPlasticInjection2.CLASS_NAME,
            ManufacturingStepFoamingInjection.CLASS_NAME,
        ],
        entityClasses = [
            ManufacturingStep::class,
            ManufacturingStepInsertInjection::class,
            ManufacturingStepPlasticInjection2::class,
            ManufacturingStepFoamingInjection::class,
        ],
    )
    @ExternalDependency(section = ExternalDependency.SECTIONS.STEP, subSection = "STEP_PLASTIC_INJECTION")
    @Path(
        "/api/link{bomPath}{branchPath}?" +
            "entityClasses=${ManufacturingStepInsertInjection.CLASS_NAME}&" +
            "entityClasses=${ManufacturingStepPlasticInjection2.CLASS_NAME}&" +
            "entityClasses=${ManufacturingStepFoamingInjection.CLASS_NAME}&" +
            "includeExtends=false",
    )
    @FieldIndex(5)
    @IgnoreForOverwrittenState
    fun linkedStep(): EntityRef? =
        getLinkedStep(
            listOf(
                ManufacturingStepPlasticInjection2::class,
                ManufacturingStepInsertInjection::class,
                ManufacturingStepFoamingInjection::class,
            ),
        )

    // region Fields from MANUFACTURING

    @Input
    @Parent(Entities.MANUFACTURING)
    fun technologyKey(): Text? = null

    @ExternalDependency(section = ExternalDependency.SECTIONS.STEP, subSection = "STEP_PLASTIC_INJECTION")
    @EntityLinkField(providerField = "linkedStep", "injectionType")
    @FieldIndex(index = 10)
    fun injectionType(): InjectionType? = null

    @Input
    @Precompute
    @Parent(Entities.MANUFACTURING)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 110)
    @Condition(field = "injectionType", value = "FOAMING", operator = Condition.EQUALS)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    fun foaming(): FoamingType = FoamingType.PHYSICAL

    @Input
    @Parent(Entities.MANUFACTURING)
    @ExternalDependency(section = ExternalDependency.SECTIONS.SELF)
    @FieldIndex(index = 10)
    @DontSortOptions
    fun paintingMethod(): PlasticPaintingType? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    @ExternalDependency(section = ExternalDependency.SECTIONS.SELF)
    @FieldIndex(index = 20)
    fun totalOrOneSidePainting(): TotalOrOneSidePaintingType? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    @ExternalDependency(section = ExternalDependency.SECTIONS.SELF)
    @FieldIndex(index = 30)
    @DontSortOptions
    fun surfacePreparationMethod(): PlasticCoatingSurfacePrepType? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    @ExternalDependency(section = ExternalDependency.SECTIONS.SELF)
    @FieldIndex(index = 40)
    @DontSortOptions
    fun surfaceActivationMethod(): PlasticCoatingSurfaceActivationType? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    @ExternalDependency(section = ExternalDependency.SECTIONS.SELF)
    @FieldIndex(index = 50)
    @DontSortOptions
    fun paintingPostProcessionMethod(): PlasticCoatingPostProcessingType? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    @ExternalDependency(section = ExternalDependency.SECTIONS.SELF)
    @FieldIndex(index = 60)
    @DontSortOptions
    fun paintingBaseCoat(): PlasticPaintingBaseCoatType? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    @ExternalDependency(section = ExternalDependency.SECTIONS.SELF)
    @FieldIndex(index = 70)
    @DefaultUnit(DefaultUnit.MICROMETER)
    @Condition(field = "showBaseCoatFields", value = "TRUE", operator = Condition.EQUALS)
    fun baseCoatThickness(): Length? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    @ExternalDependency(section = ExternalDependency.SECTIONS.SELF)
    @FieldIndex(index = 80)
    @DontSortOptions
    fun paintingTopCoat(): PlasticPaintingTopCoatType? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    @ExternalDependency(section = ExternalDependency.SECTIONS.SELF)
    @DefaultUnit(DefaultUnit.MICROMETER)
    @FieldIndex(index = 90)
    @Condition(field = "showTopCoatFields", value = "TRUE", operator = Condition.EQUALS)
    fun topCoatThickness(): Length = Length(BigDecimal.ONE, LengthUnits.MICROMETER)

    @Input
    @Parent(Entities.MANUFACTURING)
    @ExternalDependency(section = ExternalDependency.SECTIONS.SELF)
    @FieldIndex(index = 100)
    @Condition(field = "showTopCoatFields", value = "TRUE", operator = Condition.EQUALS)
    fun numberOfTopCoatLayers() = Num(BigDecimal.ONE)

    @Input
    @Parent(Entities.MANUFACTURING)
    @ExternalDependency(section = ExternalDependency.SECTIONS.SELF)
    @FieldIndex(index = 110)
    @DontSortOptions
    fun paintingClearCoat(): PlasticPaintingClearCoatType? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    @ExternalDependency(section = ExternalDependency.SECTIONS.SELF)
    @DefaultUnit(DefaultUnit.MICROMETER)
    @FieldIndex(index = 120)
    @Condition(field = "showClearCoatFields", value = "TRUE", operator = Condition.EQUALS)
    fun clearCoatThickness(): Length? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    @ExternalDependency(section = ExternalDependency.SECTIONS.SELF)
    @FieldIndex(index = 130)
    fun needsMasking(): SelectableBoolean? = null

    @Input
    @Parent(Entities.MANUFACTURING)
    @ExternalDependency(section = ExternalDependency.SECTIONS.SELF)
    @FieldIndex(index = 140)
    @Condition(field = "needsMasking", value = "TRUE", operator = Condition.EQUALS)
    fun relativeMaskedArea() = Rate(BigDecimal.ZERO)

    // endregion

    // region Fields from PROCESSED_MATERIAL

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 20)
    @Path("/api/shapes/autocomplete?tech=INJ2")
    fun shapeId(): Text? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 30)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun partHeight(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 40)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun partLength(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 50)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun partWidth(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 60)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun partOuterDiameter(): Length? = null

    // endregion

    // region Fields from MATERIAL

    @Input
    @ExternalDependency(ExternalDependency.SECTIONS.MATERIAL, subSection = "MATERIAL_PAINT_BASE")
    @FieldIndex(index = 10)
    @EntityLinkProvider([MATERIAL_PAINT_BASE], entityClasses = [MaterialPlasticPaint::class])
    @Path("/api/link{bomPath}{branchPath}?entityClasses=${MaterialPlasticPaint.CLASS_NAME}")
    @IgnoreForOverwrittenState
    @Condition(field = "paintingBaseCoat", value = "NO_TYPE", operator = Condition.NOT_EQUALS)
    fun linkedMaterialBase(): EntityRef? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterialBase", "flashOffTime")
    @ExternalDependency(ExternalDependency.SECTIONS.MATERIAL, subSection = "MATERIAL_PAINT_BASE")
    @FieldIndex(index = 20)
    @Condition(field = "paintingBaseCoat", value = "NO_TYPE", operator = Condition.NOT_EQUALS)
    @DefaultUnit(DefaultUnit.MINUTE)
    fun flashOffTimeBase(): Time? = null

    @Input
    @ExternalDependency(ExternalDependency.SECTIONS.MATERIAL, subSection = "MATERIAL_PAINT_CLEAR")
    @FieldIndex(index = 30)
    @EntityLinkProvider([MATERIAL_PAINT_CLEAR], entityClasses = [MaterialPlasticPaint::class])
    @Path("/api/link{bomPath}{branchPath}?entityClasses=${MaterialPlasticPaint.CLASS_NAME}")
    @IgnoreForOverwrittenState
    @Condition(field = "paintingClearCoat", value = "NO_TYPE", operator = Condition.NOT_EQUALS)
    fun linkedMaterialClear(): EntityRef? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterialClear", "flashOffTime")
    @ExternalDependency(ExternalDependency.SECTIONS.MATERIAL, subSection = "MATERIAL_PAINT_CLEAR")
    @FieldIndex(index = 40)
    @Condition(field = "paintingClearCoat", value = "NO_TYPE", operator = Condition.NOT_EQUALS)
    @DefaultUnit(DefaultUnit.MINUTE)
    fun flashOffTimeClear(): Time? = null

    @Input
    @ExternalDependency(ExternalDependency.SECTIONS.MATERIAL, subSection = "MATERIAL_PAINT_TOP")
    @FieldIndex(index = 50)
    @EntityLinkProvider([MATERIAL_PAINT_TOP], entityClasses = [MaterialPlasticPaint::class])
    @Path("/api/link{bomPath}{branchPath}?entityClasses=${MaterialPlasticPaint.CLASS_NAME}")
    @IgnoreForOverwrittenState
    @Condition(field = "paintingTopCoat", value = "NO_TYPE", operator = Condition.NOT_EQUALS)
    fun linkedMaterialTop(): EntityRef? = null

    @Input
    @EntityLinkField(providerField = "linkedMaterialTop", "flashOffTime")
    @ExternalDependency(ExternalDependency.SECTIONS.MATERIAL, subSection = "MATERIAL_PAINT_TOP")
    @FieldIndex(index = 60)
    @Condition(field = "paintingTopCoat", value = "NO_TYPE", operator = Condition.NOT_EQUALS)
    @DefaultUnit(DefaultUnit.MINUTE)
    fun flashOffTimeTop(): Time? = null

    // endregion

    // region Fields from SYSTEM_PARAMETER

    @Input
    @Children(Entities.SYSTEM_PARAMETER)
    fun skidLength(): Length? = null

    @Input
    @Children(Entities.SYSTEM_PARAMETER)
    fun skidWidth(): Length? = null

    @Input
    @Children(Entities.SYSTEM_PARAMETER)
    fun skidHeight(): Length? = null

    @Input
    @Children(Entities.SYSTEM_PARAMETER)
    fun fixedCycleTime(): CycleTime? = null

    // endregion

    fun numberOfBaseCoatLayers() = Num(BigDecimal.ONE)

    fun numberOfClearCoatLayers() = Num(BigDecimal.ONE)

    fun utilizationRate() = Rate(0.9)

    fun plasticCoatingType(): PlasticCoatingType = PlasticCoatingType.PAINTING

    fun scrapRate(
        injectionType: InjectionType,
        foaming: FoamingType?,
    ): Rate =
        when (injectionType.res) {
            InjectionType.Selection.NORMAL_INJECTION -> Rate(0.05)
            InjectionType.Selection.INSERT -> Rate(0.06)
            InjectionType.Selection.FOAMING ->
                when (foaming?.res) {
                    FoamingType.Selection.PHYSICAL -> Rate(0.075)
                    FoamingType.Selection.CHEMICAL ->
                        throw IncompatibleUserInputs(
                            Pair("plasticCoatingType", PlasticCoatingType.PAINTING.toString()),
                            Pair("foaming", foaming.res.toString()),
                        )
                    else -> Rate(BigDecimal.ZERO)
                }
        }

    @Nocalc
    private fun getNumberOfFlashOffStations(
        flashOffTime: Time?,
        flashOffSkidLength: Length,
        conveyorSpeed: Speed,
    ): Pieces =
        if (flashOffTime != null) {
            val flashOffLengthNeeded = Length(conveyorSpeed.inMPerMin * flashOffTime.inMinutes, LengthUnits.METER)
            Pieces((flashOffLengthNeeded / flashOffSkidLength).res.setScale(0, RoundingMode.UP))
        } else {
            Pieces.ZERO
        }

    fun totalNumberOfFlashOffStations(
        flashOffTimeBase: Time?,
        flashOffTimeClear: Time?,
        flashOffTimeTop: Time?,
        flashOffSkidLength: Length,
        conveyorSpeed: Speed,
    ): Pieces {
        val numberOfFlashOffStationsBase = getNumberOfFlashOffStations(flashOffTimeBase, flashOffSkidLength, conveyorSpeed)
        val numberOfFlashOffStationsClear = getNumberOfFlashOffStations(flashOffTimeClear, flashOffSkidLength, conveyorSpeed)
        val numberOfFlashOffStationsTop = getNumberOfFlashOffStations(flashOffTimeTop, flashOffSkidLength, conveyorSpeed)
        return numberOfFlashOffStationsBase + numberOfFlashOffStationsClear + numberOfFlashOffStationsTop
    }

    @Precompute
    fun showBaseCoatFields(paintingBaseCoat: PlasticPaintingBaseCoatType?): Bool =
        if (paintingBaseCoat == null) {
            Bool(false)
        } else {
            Bool(paintingBaseCoat != PlasticPaintingBaseCoatType.NO_TYPE)
        }

    @Precompute
    fun showTopCoatFields(
        paintingTopCoat: PlasticPaintingTopCoatType?,
        plasticCoatingType: PlasticCoatingType?,
    ): Bool =
        if (paintingTopCoat == null || plasticCoatingType == null) {
            Bool(false)
        } else {
            Bool(paintingTopCoat != PlasticPaintingTopCoatType.NO_TYPE && plasticCoatingType == PlasticCoatingType.PAINTING)
        }

    @Precompute
    fun showClearCoatFields(
        paintingClearCoat: PlasticPaintingClearCoatType?,
        plasticCoatingType: PlasticCoatingType?,
    ): Bool =
        if (paintingClearCoat == null || plasticCoatingType == null) {
            Bool(false)
        } else {
            Bool(paintingClearCoat != PlasticPaintingClearCoatType.NO_TYPE && plasticCoatingType == PlasticCoatingType.PAINTING)
        }

    fun inputGroup(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
    ) = lookupReaderService.getInputGroupFromShapeLookup(calculationContext!!.accessCheck, shapeId, Model.INJ2)

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 70)
    fun gapBetweenParts() = Length(100.0, LengthUnits.MILLIMETER)

    fun newLength(
        inputGroup: Text,
        partOuterDiameter: Length,
        partLength: Length,
    ) = if (inputGroup.res == "cylinder") partOuterDiameter else partLength

    fun newWidth(
        inputGroup: Text,
        partOuterDiameter: Length,
        partWidth: Length,
    ) = if (inputGroup.res == "cuboid") partWidth else partOuterDiameter

    fun newHeight(
        inputGroup: Text,
        partOuterDiameter: Length,
        partHeight: Length,
    ) = if (inputGroup.res == "pipe") partOuterDiameter else partHeight

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 75)
    fun partsPerLength(
        gapBetweenParts: Length,
        skidLength: Length,
        newLength: Length,
    ) = Pieces(skidLength.div(newLength + gapBetweenParts).res.setScale(0, RoundingMode.DOWN))

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 80)
    fun partsPerWidth(
        gapBetweenParts: Length,
        skidWidth: Length,
        newWidth: Length,
        paintingMethod: PlasticPaintingType,
    ): Pieces {
        val parts = Pieces(skidWidth.div(newWidth + gapBetweenParts).res.setScale(0, RoundingMode.DOWN))
        return when (paintingMethod.res) {
            PlasticPaintingType.Selection.FLATBED -> parts
            PlasticPaintingType.Selection.MANUAL_SPRAY_PAINTING,
            PlasticPaintingType.Selection.AUTOMATIC_SPRAY_PAINTING,
            -> Pieces(max(parts.res, BigDecimal(2)))
        }
    }

    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 85)
    fun partsPerHeight(
        gapBetweenParts: Length,
        skidHeight: Length,
        newHeight: Length,
        paintingMethod: PlasticPaintingType,
    ): Pieces {
        val parts = Pieces(skidHeight.div(newHeight + gapBetweenParts).res.setScale(0, RoundingMode.DOWN))
        return when (paintingMethod.res) {
            PlasticPaintingType.Selection.FLATBED -> Pieces.ONE
            PlasticPaintingType.Selection.MANUAL_SPRAY_PAINTING,
            PlasticPaintingType.Selection.AUTOMATIC_SPRAY_PAINTING,
            -> parts
        }
    }

    fun internalPartsPerCycle(
        partsPerLength: Pieces,
        partsPerWidth: Pieces,
        partsPerHeight: Pieces,
    ) = partsPerLength * partsPerWidth * partsPerHeight

    fun totalNumberOfLayers(
        paintingBaseCoat: PlasticPaintingBaseCoatType,
        paintingTopCoat: PlasticPaintingTopCoatType,
        numberOfTopCoatLayers: Num,
        paintingClearCoat: PlasticPaintingClearCoatType,
    ): Num {
        var result = BigDecimal.ZERO
        if (paintingBaseCoat != PlasticPaintingBaseCoatType.NO_TYPE) {
            result += BigDecimal.ONE
        }
        if (paintingTopCoat != PlasticPaintingTopCoatType.NO_TYPE) {
            result += numberOfTopCoatLayers.res
        }
        if (paintingClearCoat != PlasticPaintingClearCoatType.NO_TYPE) {
            result += BigDecimal.ONE
        }
        return Num(result)
    }

    fun coatingAreaFromTopTotal(coatingAreas: TsetDelCoatingAreaResponse) = Area(coatingAreas.coating_areas.from_top.total, AreaUnits.QMM)

    fun coatingAreaFromTopPart(coatingAreas: TsetDelCoatingAreaResponse) = Area(coatingAreas.coating_areas.from_top.part, AreaUnits.QMM)

    fun coatingAreaFromRotationalFirstTotal(coatingAreas: TsetDelCoatingAreaResponse) =
        Area(coatingAreas.coating_areas.rotational_first.total, AreaUnits.QMM)

    fun coatingAreaFromRotationalFirstPart(coatingAreas: TsetDelCoatingAreaResponse) =
        Area(coatingAreas.coating_areas.rotational_first.part, AreaUnits.QMM)

    fun coatingAreaFromRotationalSecondTotal(coatingAreas: TsetDelCoatingAreaResponse) =
        Area(coatingAreas.coating_areas.rotational_second.total, AreaUnits.QMM)

    fun coatingAreaFromRotationalSecondPart(coatingAreas: TsetDelCoatingAreaResponse) =
        Area(coatingAreas.coating_areas.rotational_second.part, AreaUnits.QMM)

    fun boundingRectangles(
        totalOrOneSidePainting: TotalOrOneSidePaintingType,
        coatingAreaFromTopTotal: Area,
        coatingAreaFromRotationalFirstTotal: Area,
    ): Area = boundingRectanglesImpl(totalOrOneSidePainting, coatingAreaFromTopTotal, coatingAreaFromRotationalFirstTotal)

    @ReadOnly
    @DefaultUnit(DefaultUnit.QCM)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 35)
    fun areaToPaintNet(
        totalOrOneSidePainting: TotalOrOneSidePaintingType,
        coatingAreaFromTopPart: Area,
        coatingAreaFromRotationalFirstPart: Area,
    ): Area = areaToPaintNetImpl(totalOrOneSidePainting, coatingAreaFromTopPart, coatingAreaFromRotationalFirstPart)

    @Input
    @DefaultUnit(DefaultUnit.QCM)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 40)
    fun areaToPaintNetMasked(
        relativeMaskedArea: Rate,
        areaToPaintNet: Area,
    ) = areaToPaintNetMaskedImpl(relativeMaskedArea, areaToPaintNet)

    @ReadOnly
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 130)
    @DefaultUnit(DefaultUnit.QCM)
    fun surfaceArea(coatingAreas: TsetDelCoatingAreaResponse) = Area(coatingAreas.surface_area.toBigDecimal(), AreaUnits.QMM)

    @ReadOnly
    @DefaultUnit(DefaultUnit.QCM)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 45)
    fun maskedSurfaceArea(
        relativeMaskedArea: Rate,
        surfaceArea: Area,
    ) = surfaceArea.times(relativeMaskedArea)

    @Input
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 55)
    fun shapeRelatedPaintLossRate(
        boundingRectangles: Area,
        areaToPaintNetMasked: Area,
    ): Rate = shapeRelatedPaintLossRateImpl(boundingRectangles, areaToPaintNetMasked)

    @Input
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 50)
    fun applicationRelatedLossRate(paintingMethod: PlasticPaintingType): Rate =
        ManufacturingStepPlasticPaintingUtils.applicationRelatedLossRateImpl(paintingMethod)

    @ReadOnly
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 15)
    fun numberOfPaintingStations(
        totalOrOneSidePainting: TotalOrOneSidePaintingType,
        paintingMethod: PlasticPaintingType,
        totalNumberOfLayers: Num,
    ): Num {
        val isFlatbedAndTotalPainting =
            paintingMethod == PlasticPaintingType.FLATBED &&
                totalOrOneSidePainting == TotalOrOneSidePaintingType.TOTAL
        return if (isFlatbedAndTotalPainting) totalNumberOfLayers.times(2.0) else totalNumberOfLayers
    }

    @ReadOnly
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 65)
    fun conveyorSpeed(
        skidLength: Length,
        fixedCycleTime: CycleTime,
    ) = Speed(skidLength.inMeter / fixedCycleTime.inMinutes, SpeedUnits.M_PER_MIN)

    @DefaultUnit(DefaultUnit.MILLIMETER)
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 60)
    fun flashOffSkidLength() = Length(3.0, LengthUnits.METER)

    @Input
    @ObjectView(ObjectView.MANUFACTURING_STEP_SPECIFIC, 20)
    fun paintRate() = Rate(BigDecimal.ONE)

    private class TemplateLookupEntry(
        val templateName: String,
        val paintingMethod: PlasticPaintingType,
    )

    @SummaryView(SummaryView.PROCESS, 40, "configurationPlasticPainting")
    @CalculationPreview(3, "configurationPlasticPainting")
    fun templateName(paintingMethod: PlasticPaintingType): Mono<Text> =
        services
            .getLookupTable("ManufacturingStepPlasticPainting_Templates") {
                TemplateLookupEntry(templateName = it[0], paintingMethod = PlasticPaintingType.valueOf(it[1]))
            }.filter { paintingMethod == it.paintingMethod }
            .single()
            .map { Text(it.templateName) }

    private class SurfacePrepTemplateLookupEntry(
        val templateName: String,
        val surfacePreparationMethod: PlasticCoatingSurfacePrepType,
    )

    fun templateNameSurfacePreparation(surfacePreparationMethod: PlasticCoatingSurfacePrepType): Mono<Text>? =
        if (surfacePreparationMethod != PlasticCoatingSurfacePrepType.NO_TYPE) {
            services
                .getLookupTable("ManufacturingStepPlasticPainting_SurfacePreparation_Templates") {
                    SurfacePrepTemplateLookupEntry(
                        templateName = it[0],
                        surfacePreparationMethod = PlasticCoatingSurfacePrepType.valueOf(it[1]),
                    )
                }.filter { surfacePreparationMethod == it.surfacePreparationMethod }
                .elementAt(0)
                .mapNotNull { it?.templateName?.let { it1 -> Text(it1) } }
        } else {
            null
        }

    private class SurfaceActivationTemplateLookupEntry(
        val templateName: String,
        val surfaceActivationMethod: PlasticCoatingSurfaceActivationType,
    )

    fun templateNameSurfaceActivation(surfaceActivationMethod: PlasticCoatingSurfaceActivationType): Mono<Text>? =
        if (surfaceActivationMethod != PlasticCoatingSurfaceActivationType.NO_TYPE) {
            services
                .getLookupTable("ManufacturingStepPlasticPainting_SurfaceActivation_Templates") {
                    SurfaceActivationTemplateLookupEntry(
                        templateName = it[0],
                        surfaceActivationMethod = PlasticCoatingSurfaceActivationType.valueOf(it[1]),
                    )
                }.filter { surfaceActivationMethod == it.surfaceActivationMethod }
                .elementAt(0)
                .mapNotNull { it?.templateName?.let { it1 -> Text(it1) } }
        } else {
            null
        }

    private class PostProcessTemplateLookupEntry(
        val templateName: String,
        val paintingPostProcessionMethod: PlasticCoatingPostProcessingType,
    )

    fun templateNamePaintingPostProcess(paintingPostProcessionMethod: PlasticCoatingPostProcessingType): Mono<Text>? =
        if (paintingPostProcessionMethod != PlasticCoatingPostProcessingType.NO_TYPE) {
            services
                .getLookupTable("ManufacturingStepPlasticPainting_PaintingPostProcess_Templates") {
                    PostProcessTemplateLookupEntry(
                        templateName = it[0],
                        paintingPostProcessionMethod = PlasticCoatingPostProcessingType.valueOf((it[1])),
                    )
                }.filter { paintingPostProcessionMethod == it.paintingPostProcessionMethod }
                .elementAt(0)
                .mapNotNull { it?.templateName?.let { it1 -> Text(it1) } }
        } else {
            null
        }

    fun numberOfCycleTimeSteps(
        numberOfPaintingStations: Num,
        needsMasking: SelectableBoolean,
        surfacePreparationMethod: PlasticCoatingSurfacePrepType,
        surfaceActivationMethod: PlasticCoatingSurfaceActivationType,
        paintingPostProcessionMethod: PlasticCoatingPostProcessingType,
    ): Num {
        val baseNumberOfCycleTimeSteps = 3 // Loading, unloading and visual inspection
        var resNumberOfSteps = 0
        if (surfacePreparationMethod != PlasticCoatingSurfacePrepType.NO_TYPE) resNumberOfSteps += 1
        if (surfaceActivationMethod != PlasticCoatingSurfaceActivationType.NO_TYPE) resNumberOfSteps += 1
        if (paintingPostProcessionMethod != PlasticCoatingPostProcessingType.NO_TYPE) resNumberOfSteps += 1
        if (needsMasking.toBoolean()) resNumberOfSteps += 2 // Masking and unmasking
        return numberOfPaintingStations.times(2.0).plus(baseNumberOfCycleTimeSteps.toDouble()).plus(resNumberOfSteps.toDouble())
    }

    // region Entity creation

    @Nocalc
    private fun createPaintingEntity(
        key: String,
        paintCoatType: PaintCoatType,
    ) = createEntityWithNewMasterdata(
        name =
            when (paintCoatType.res) {
                PaintCoatType.Selection.BASE -> "MaterialPaintBase"
                PaintCoatType.Selection.CLEAR -> "MaterialPaintClear"
                PaintCoatType.Selection.TOP -> "MaterialPaintTop"
            },
        entityType = Entities.MATERIAL,
        clazz = "MaterialPlasticPaint",
        masterDataType = MasterDataType.RAW_MATERIAL_PAINT,
        masterDataKey = key,
        fields = mapOf("paintCoatType" to paintCoatType),
    )

    @DisableEntityCreationInModularizedEntity
    @EntityCreation(Entities.MATERIAL)
    fun createMaterialBase(paintingBaseCoat: PlasticPaintingBaseCoatType?): Flux<ManufacturingEntity> {
        if (paintingBaseCoat == null) return Flux.empty()
        return when (paintingBaseCoat.res) {
            PlasticPaintingBaseCoatType.Selection.GLASURIT_PRIMER ->
                createPaintingEntity(key = GLASURIT_PRIMER_KEY, paintCoatType = PaintCoatType.BASE)
            PlasticPaintingBaseCoatType.Selection.NO_TYPE -> Mono.empty()
        }.toFlux()
    }

    @DisableEntityCreationInModularizedEntity
    @EntityCreation(Entities.MATERIAL)
    fun createMaterialClear(paintingClearCoat: PlasticPaintingClearCoatType?): Flux<ManufacturingEntity> {
        if (paintingClearCoat == null) return Flux.empty()
        return when (paintingClearCoat.res) {
            PlasticPaintingClearCoatType.Selection.CV_CLEAR ->
                createPaintingEntity(key = HS_2K_CV_CLEAR_KEY, paintCoatType = PaintCoatType.CLEAR)
            PlasticPaintingClearCoatType.Selection.MULTI_CLEAR ->
                createPaintingEntity(key = HS_MULTI_CLEAR_KEY, paintCoatType = PaintCoatType.CLEAR)
            PlasticPaintingClearCoatType.Selection.NO_TYPE -> Mono.empty()
        }.toFlux()
    }

    @DisableEntityCreationInModularizedEntity
    @EntityCreation(Entities.MATERIAL)
    fun createMaterialTop(paintingTopCoat: PlasticPaintingTopCoatType?): Flux<ManufacturingEntity> {
        if (paintingTopCoat == null) return Flux.empty()
        return when (paintingTopCoat.res) {
            PlasticPaintingTopCoatType.Selection.GLASURIT_LINE ->
                createPaintingEntity(key = GLASURIT_55_KEY, paintCoatType = PaintCoatType.TOP)
            PlasticPaintingTopCoatType.Selection.NO_TYPE -> Mono.empty()
        }.toFlux()
    }

    @EntityCreation(Entities.MACHINE)
    fun createMachinesSurfacePreparation(
        surfacePreparationMethod: PlasticCoatingSurfacePrepType,
        templateNameSurfacePreparation: Text?,
    ): Flux<ManufacturingEntity>? =
        if (surfacePreparationMethod != PlasticCoatingSurfacePrepType.NO_TYPE) {
            createEntitiesFromTemplate(
                name = templateNameSurfacePreparation?.res + "_Machine",
                location = "Global",
            )
        } else {
            null
        }

    @EntityCreation(Entities.MACHINE)
    fun createMachinesSurfaceActivation(
        surfaceActivationMethod: PlasticCoatingSurfaceActivationType,
        templateNameSurfaceActivation: Text?,
    ): Flux<ManufacturingEntity>? =
        if (surfaceActivationMethod != PlasticCoatingSurfaceActivationType.NO_TYPE) {
            createEntitiesFromTemplate(
                name = templateNameSurfaceActivation?.res + "_Machine",
                location = "Global",
            )
        } else {
            null
        }

    @EntityCreation(Entities.MACHINE)
    fun createMachinesPaintingPostProcess(
        paintingPostProcessionMethod: PlasticCoatingPostProcessingType,
        templateNamePaintingPostProcess: Text?,
    ): Flux<ManufacturingEntity>? =
        if (paintingPostProcessionMethod != PlasticCoatingPostProcessingType.NO_TYPE) {
            createEntitiesFromTemplate(
                name = templateNamePaintingPostProcess?.res + "_Machine",
                location = "Global",
            )
        } else {
            null
        }

    @EntityCreation(Entities.SYSTEM_PARAMETER)
    fun createSystemParameter(templateName: Text): Mono<SystemParameterPlasticPainting> =
        createSystemParameter(
            masterData = MasterDataType.SYSTEM_PARAMETER_PLASTIC_PAINTING,
            clazz = SystemParameterPlasticPainting::class,
            system = templateName.res,
            year = calculationContext?.year!!,
            location = "Global",
        )

    @EntityCreation(Entities.CYCLETIME_STEP_GROUP)
    fun createCycleTimeStepGroup() =
        createEntity(
            name = "Painting cycle time step group",
            clazz = PaintingCycleTimeStepGroup::class,
            entityType = Entities.CYCLETIME_STEP_GROUP,
        )

    @EntityCreation(Entities.MACHINE)
    fun createMachines(templateName: Text): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Machine",
            location = "Global",
        )

    @Nocalc
    private fun createPlasticPaintingLabor(
        displayDesignation: String,
        timePerPart: Time,
    ) = createEntity(
        name = "$displayDesignation labor",
        clazz = PlasticPaintingLabor::class,
        entityType = Entities.LABOR,
        fields =
            mapOf(
                "displayDesignation" to Text(displayDesignation),
                "timePerPart" to timePerPart,
            ),
    )

    @EntityCreation(Entities.LABOR)
    fun createLabor(needsMasking: SelectableBoolean): List<ManufacturingEntity> {
        val commonLabor =
            listOf(
                createEntity(
                    name = "Machine labor",
                    clazz = Labor::class,
                    entityType = Entities.LABOR,
                    fields =
                        mapOf(
                            "displayDesignation" to Text("Machine operator"),
                            "requiredLabor" to Num(2.0),
                        ),
                ),
                createPlasticPaintingLabor("Visual inspection", Time(BigDecimal.TEN, TimeUnits.SECOND)),
            )

        val loadingUnloadingLabor =
            listOf(
                createPlasticPaintingLabor("Loading", Time(BigDecimal.TEN, TimeUnits.SECOND)),
                createPlasticPaintingLabor("Unloading", Time(5.0, TimeUnits.SECOND)),
            )

        return if (!needsMasking.toBoolean()) {
            commonLabor + loadingUnloadingLabor
        } else {
            val maskingUnmaskingLabor =
                listOf(
                    createPlasticPaintingLabor("Masking", Time(20.0, TimeUnits.SECOND)),
                    createPlasticPaintingLabor("Unmasking", Time(BigDecimal.TEN, TimeUnits.SECOND)),
                )
            commonLabor + loadingUnloadingLabor + maskingUnmaskingLabor
        }
    }

    @EntityCreation(Entities.SETUP)
    fun createSetup(
        templateName: Text,
        locationName: Text,
    ): Flux<ManufacturingEntity> =
        createEntitiesFromTemplate(
            name = templateName.res + "_Setup",
            location = locationName.res,
        )
}
