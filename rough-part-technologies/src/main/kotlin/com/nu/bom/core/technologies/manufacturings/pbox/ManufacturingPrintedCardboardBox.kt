package com.nu.bom.core.technologies.manufacturings.pbox

import com.nu.bom.core.exception.readable.LimitType
import com.nu.bom.core.exception.readable.NumericInputExceedsLimitException
import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.Condition
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.DisableEntityCreationInModularizedEntity
import com.nu.bom.core.manufacturing.annotations.DontSortOptions
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityProvider
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Hidden
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.OrderedEntityCreation
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.Precompute
import com.nu.bom.core.manufacturing.annotations.StaticDenominatorUnit
import com.nu.bom.core.manufacturing.annotations.WizardField
import com.nu.bom.core.manufacturing.entities.BaseModelManufacturing
import com.nu.bom.core.manufacturing.entities.Manufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.enums.MasterDataType
import com.nu.bom.core.manufacturing.enums.StaticUnitOverride
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.AreaUnits
import com.nu.bom.core.manufacturing.fieldTypes.CardboardBoxFieldResult
import com.nu.bom.core.manufacturing.fieldTypes.CardboardBoxType
import com.nu.bom.core.manufacturing.fieldTypes.FluteType
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.ListOfSheetDimensions
import com.nu.bom.core.manufacturing.fieldTypes.NestingResponseFieldResult
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.PiecesUnits
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SheetDimensions
import com.nu.bom.core.manufacturing.fieldTypes.SurfaceDensity
import com.nu.bom.core.manufacturing.fieldTypes.SurfaceDensityUnits
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.service.masterdata.MdDetailCrudService
import com.nu.bom.core.service.masterdata.MdHeaderCrudService
import com.nu.bom.core.service.masterdata.MdHeaderTypes
import com.nu.bom.core.service.wizard.steps.pbox.WizardPboxNestingFieldStep
import com.nu.bom.core.smf.model.FriedelNestingCurve
import com.nu.bom.core.technologies.manufacturings.pbox.material.MaterialPboxCorrugatedFlute
import com.nu.bom.core.technologies.manufacturings.pbox.material.MaterialPboxInnerLiner
import com.nu.bom.core.technologies.service.ShapeLookupReaderService
import com.nu.bom.core.technologies.steps.pboxdrying.ManufacturingStepPboxDrying
import com.nu.bom.core.technologies.steps.pboxfoldergluer.ManufacturingStepPboxFolderGluer
import com.nu.bom.core.technologies.steps.pboxlaminating.ManufacturingStepPboxLaminating
import com.nu.bom.core.technologies.steps.pboxmanualsplitting.ManufacturingStepPboxManualSplitting
import com.nu.bom.core.technologies.steps.pboxstampingcutting.ManufacturingStepPboxStampingCutting
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import reactor.core.publisher.Mono
import java.math.BigDecimal
import kotlin.reflect.KClass

@EntityType(Entities.MANUFACTURING)
@Suppress("unused")
class ManufacturingPrintedCardboardBox(
    name: String,
) : BaseModelManufacturing(name) {
    override val extends = Manufacturing(name)

    override val model: Model
        get() = Model.PBOX

    override val modelSteps: List<KClass<out ManufacturingEntity>>
        get() =
            listOf()

    companion object {
        val DEFAULT_TARGET_PEAK_LOT_SIZE = QuantityUnit(50_000.0)
        val DEFAULT_PRINTING_AREA_OUTSIDE_RATE = Rate(1.0)
        val DEFAULT_PRINTING_WEIGHT = SurfaceDensity(1.2, SurfaceDensityUnits.GRAM_PER_QM)
        val DEFAULT_VARNISH_WEIGHT = SurfaceDensity(3.5, SurfaceDensityUnits.GRAM_PER_QM)
        val DEFAULT_PRINTING_SHARE = Rate(0.25)
        val DEFAULT_FLAP_SIZE = Length(10.0, LengthUnits.MILLIMETER)
    }

    @Input
    fun technology() = Text(model.name)

    @Input
    fun costUnit() = Text(PiecesUnits.THOUSAND_PIECES.name)

    fun internalCallsPerYear(
        // Todo: Might need to be changed to average volume. See COST-50914
        peakUsableProductionVolumePerYear: QuantityUnit,
    ) = Num(peakUsableProductionVolumePerYear.div(DEFAULT_TARGET_PEAK_LOT_SIZE).res).atMost(12.0)

    @Input
    @StaticDenominatorUnit(StaticUnitOverride.YEAR)
    fun callsPerYear(
        // Todo: Might need to be changed to average volume. See COST-50914
        peakUsableProductionVolumePerYear: QuantityUnit,
        internalCallsPerYear: Num,
    ) = Num(min(peakUsableProductionVolumePerYear.res, internalCallsPerYear.res)).atLeast(BigDecimal.ONE)

    // region nesting

    @Input
    @WizardField(WizardPboxNestingFieldStep::class)
    @Precompute
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun distanceBetweenParts(): Length = Length(BigDecimal.ONE, LengthUnits.CENTIMETER)

    @Input
    @WizardField(WizardPboxNestingFieldStep::class)
    @Precompute
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun distanceToSheetEdge(): Length = Length(BigDecimal.ONE, LengthUnits.CENTIMETER)

    fun polygonToNest(
        unfoldedBoxLength: Length,
        unfoldedBoxWidthW1: Length,
        unfoldedBoxWidthW2: Length,
        boxSubtype: CardboardBoxType,
    ): FriedelNestingCurve =
        PrintedCardboardBoxGeometryUtils.createPolygon(
            boxSubtype,
            w1 = unfoldedBoxWidthW1.inMillimeter.toDouble(),
            w2 = unfoldedBoxWidthW2.inMillimeter.toDouble(),
            length = unfoldedBoxLength.inMillimeter.toDouble(),
            flapSize = DEFAULT_FLAP_SIZE.inMillimeter.toDouble(),
        )

    @Hidden
    @Precompute
    @WizardField(WizardPboxNestingFieldStep::class)
    fun defaultSheetSizes(mdDetailCrudService: MdDetailCrudService): Mono<ListOfSheetDimensions> =
        mdDetailCrudService
            .postDetailEntriesSearchAsListOfHeaderDto(
                accessCheck = calculationContext!!.accessCheck,
                headerTypeKey = SimpleKeyDto(MdHeaderTypes.MATERIAL),
                detailQueryDto = PrintedCardboardBoxUtils.getDetailQueryForSheetSize(),
            ).map { listOfHeaders ->
                listOfHeaders.map { header ->
                    SheetDimensions.fromHeaderDetailQueryResponseDto(header)
                }
            }.map { ListOfSheetDimensions(it) }

    @Input
    @Hidden
    @Precompute
    @WizardField(WizardPboxNestingFieldStep::class)
    fun customSheetSizes(): ListOfSheetDimensions = ListOfSheetDimensions(emptyList())

    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun nestingResultSheetLength(nestingResult: NestingResponseFieldResult) =
        Length(nestingResult.res.sheetDimensions.lengthMm, LengthUnits.MILLIMETER)

    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun nestingResultSheetWidth(nestingResult: NestingResponseFieldResult) =
        Length(nestingResult.res.sheetDimensions.widthMm, LengthUnits.MILLIMETER)

    @DefaultUnit(DefaultUnit.QCM)
    fun nestingResultArea(nestingResult: NestingResponseFieldResult) = Area(nestingResult.res.sheetDimensions.areaMm2(), AreaUnits.QMM)

    fun nestingResultFileId(nestingResult: NestingResponseFieldResult) = Text(nestingResult.res.nestingSvgFileId)

    fun nestingResultParts(nestingResult: NestingResponseFieldResult): QuantityUnit =
        nestingResult.res.nestedParts.let {
            if (it < 1) {
                throw NumericInputExceedsLimitException(
                    "nestedParts",
                    LimitType.LESS,
                    BigDecimal.ONE,
                )
            } else {
                QuantityUnit(it.toBigDecimal())
            }
        }

    fun nestingResultMaterialUsage(nestingResult: NestingResponseFieldResult) = Rate(nestingResult.res.materialUsagePercent / 100.0)

    // endregion

    // region Wizard inputs

    @Input
    @WizardField
    @Precompute
    @Hidden
    fun boxSubtype(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
    ): Mono<CardboardBoxType> =
        lookupReaderService.getPboxShapeData(calculationContext!!.accessCheck, shapeId).map {
            it.boxSubtype
        }

    @Input
    @WizardField(index = 10)
    @Condition(field = "boxSubtype", value = "MA", operator = Condition.EQUALS)
    @Path("/api/masterdataSelectable/cardboardBox?boxSubtype=MA", getDisplayNameFromPathQuery = true)
    fun boxTypeMa(): Text? = null

    @Input
    @WizardField(index = 10)
    @Condition(field = "boxSubtype", value = "LP", operator = Condition.EQUALS)
    @Path("/api/masterdataSelectable/cardboardBox?boxSubtype=LP", getDisplayNameFromPathQuery = true)
    fun boxTypeLp(): Text? = null

    @Input
    @Precompute
    @Hidden
    @WizardField
    fun cardboardBox(
        boxTypeMa: Text?,
        boxTypeLp: Text?,
        boxSubtype: CardboardBoxType,
        mdHeaderCrudService: MdHeaderCrudService,
    ): Mono<CardboardBoxFieldResult>? {
        val boxType =
            when (boxSubtype.res) {
                CardboardBoxType.Selection.LP -> boxTypeLp?.res
                CardboardBoxType.Selection.MA -> boxTypeMa?.res
            }
        if (boxType == null) return null
        return mdHeaderCrudService
            .getHeaderByKey(
                accessCheck = calculationContext!!.accessCheck,
                headerTypeKey = SimpleKeyDto(MdHeaderTypes.MATERIAL),
                headerKey = SimpleKeyDto(boxType),
            ).map {
                CardboardBoxFieldResult.fromHeaderDto(it)
            }
    }

    @Input
    @Precompute
    @WizardField(index = 20)
    fun fluteType(cardboardBox: CardboardBoxFieldResult): FluteType = FluteType.valueOf(cardboardBox.res.fluteType)

    @Input
    @Precompute
    @WizardField(index = 30)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun unfoldedBoxLength(cardboardBox: CardboardBoxFieldResult): Length = Length(cardboardBox.res.lengthM, LengthUnits.METER)

    @Input
    @Precompute
    @WizardField(index = 40)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun unfoldedBoxWidthW1(cardboardBox: CardboardBoxFieldResult): Length = Length(cardboardBox.res.width1M, LengthUnits.METER)

    @Input
    @Precompute
    @WizardField(index = 50)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun unfoldedBoxWidthW2(cardboardBox: CardboardBoxFieldResult): Length = Length(cardboardBox.res.width2M, LengthUnits.METER)

    @Input
    @Hidden
    fun differenceBetweenW2andW1(
        unfoldedBoxWidthW1: Length,
        unfoldedBoxWidthW2: Length,
    ) = unfoldedBoxWidthW2 - unfoldedBoxWidthW1

    @Input
    @Precompute
    @WizardField(index = 60)
    @DefaultUnit(DefaultUnit.QCM)
    fun boxSurfaceArea(polygonToNest: FriedelNestingCurve): Area = Area(polygonToNest.areaMm2(), AreaUnits.QMM)

    @Input
    @Precompute
    @WizardField(index = 70)
    fun paintingAreaOutside(): Rate = DEFAULT_PRINTING_AREA_OUTSIDE_RATE

    @Input
    @Precompute
    @WizardField(index = 80)
    fun varnishWeight(): SurfaceDensity = DEFAULT_VARNISH_WEIGHT

    @Input
    @Precompute
    @WizardField(index = 90)
    fun cyanShare(): Rate = DEFAULT_PRINTING_SHARE

    @Input
    @Precompute
    @WizardField(index = 100)
    fun cyanPrintingWeight(): SurfaceDensity = DEFAULT_PRINTING_WEIGHT

    @Input
    @Precompute
    @WizardField(index = 110)
    fun magentaShare(): Rate = DEFAULT_PRINTING_SHARE

    @Input
    @Precompute
    @WizardField(index = 120)
    fun magentaPrintingWeight(): SurfaceDensity = DEFAULT_PRINTING_WEIGHT

    @Input
    @Precompute
    @WizardField(index = 130)
    fun yellowShare(): Rate = DEFAULT_PRINTING_SHARE

    @Input
    @Precompute
    @WizardField(index = 140)
    fun yellowPrintingWeight(): SurfaceDensity = DEFAULT_PRINTING_WEIGHT

    @Input
    @Precompute
    @WizardField(index = 150)
    fun blackShare(): Rate = DEFAULT_PRINTING_SHARE

    @Input
    @Precompute
    @WizardField(index = 160)
    fun blackPrintingWeight(): SurfaceDensity = DEFAULT_PRINTING_WEIGHT

    @Input
    @WizardField(index = 170)
    @DontSortOptions
    @Path(
        "/api/model/pbox/materialName" +
            "?masterDataType=RAW_MATERIAL_PAPER_COIL" +
            "&criteriaField=paperCategory" +
            "&criteriaValue=INNER_LINER" +
            "&fieldToSortBy=paperQuality",
    )
    fun materialKeyInnerLiner(): Text? = null

    @Input
    @WizardField(index = 180)
    @DontSortOptions
    @Path(
        "/api/model/pbox/materialName" +
            "?masterDataType=RAW_MATERIAL_PAPER_SHEET" +
            "&criteriaField=paperCategory" +
            "&criteriaValue=OUTER_LINER" +
            "&fieldToSortBy=paperQuality",
    )
    fun materialKeyOuterLiner(): Text? = null

    @Input
    @WizardField(index = 190)
    @DontSortOptions
    @Path(
        "/api/model/pbox/materialName" +
            "?masterDataType=RAW_MATERIAL_PAPER_COIL" +
            "&criteriaField=paperCategory" +
            "&criteriaValue=CORRUGATED_FLUTE" +
            "&fieldToSortBy=paperQuality",
    )
    fun materialKeyCorrugatedFlute(): Text? = null

    // endregion

    // region Manufacturing step creation

    @OrderedEntityCreation
    fun manufacturingSteps() =
        arrayOf(
            ::laminating.name,
            ::stampingCutting.name,
            ::drying.name,
            ::manualSplitting.name,
            ::folderGluer.name,
        )

    @EntityProvider
    fun laminating() = createStepForCalculationModule(ManufacturingStepPboxLaminating::class)

    @EntityProvider
    fun drying() = createStepForCalculationModule(ManufacturingStepPboxDrying::class)

    @EntityProvider
    fun stampingCutting() = createStepForCalculationModule(ManufacturingStepPboxStampingCutting::class)

    @EntityProvider
    fun manualSplitting() = createStepForCalculationModule(ManufacturingStepPboxManualSplitting::class)

    @EntityProvider
    fun folderGluer(boxSubtype: CardboardBoxType) =
        when (boxSubtype.res) {
            CardboardBoxType.Selection.MA -> createStepForCalculationModule(ManufacturingStepPboxFolderGluer::class)
            CardboardBoxType.Selection.LP -> null
        }

    // endregion

    @DisableEntityCreationInModularizedEntity
    @EntityCreation(Entities.MATERIAL)
    fun createInnerLiner(materialKeyInnerLiner: Text) =
        createMaterialFromMasterdata(
            clazz = MaterialPboxInnerLiner::class,
            masterDataType = MasterDataType.RAW_MATERIAL_PAPER_COIL,
            masterDataKey = materialKeyInnerLiner.res,
        )

    @DisableEntityCreationInModularizedEntity
    @EntityCreation(Entities.MATERIAL)
    fun createCorrugatedFlute(materialKeyCorrugatedFlute: Text) =
        createMaterialFromMasterdata(
            clazz = MaterialPboxCorrugatedFlute::class,
            masterDataType = MasterDataType.RAW_MATERIAL_PAPER_COIL,
            masterDataKey = materialKeyCorrugatedFlute.res,
        )

    @EntityCreation(Entities.BOM_ENTRY)
    fun createSub() = createBomEntryForCalculationModule(SubManufacturingPrintedCardboardBoxBomEntry::class)
}
