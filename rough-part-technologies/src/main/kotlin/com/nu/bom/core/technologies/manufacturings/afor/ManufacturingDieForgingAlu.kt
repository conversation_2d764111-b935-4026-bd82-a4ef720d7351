package com.nu.bom.core.technologies.manufacturings.afor

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.BehaviourCreation
import com.nu.bom.core.manufacturing.annotations.Children
import com.nu.bom.core.manufacturing.annotations.Condition
import com.nu.bom.core.manufacturing.annotations.Default
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityCreation
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.Hidden
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.Precompute
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.SpecialLink
import com.nu.bom.core.manufacturing.annotations.SummaryView
import com.nu.bom.core.manufacturing.annotations.WizardField
import com.nu.bom.core.manufacturing.defaults.NullProvider
import com.nu.bom.core.manufacturing.entities.BaseModelManufacturing
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ShapeBasedCostModuleManufacturing
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.AreaUnits
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.LengthUnits
import com.nu.bom.core.manufacturing.fieldTypes.MachiningForTechnology
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.ShapeBehaviourType
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeCleanness
import com.nu.bom.core.manufacturing.fieldTypes.StepSubTypeHeatTreatmentAlu
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.ToolComplexityDieForging
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.nu.bom.core.manufacturing.service.behaviour.DynamicBehaviour
import com.nu.bom.core.manufacturing.service.behaviour.EntityBasedDynamicBehaviour
import com.nu.bom.core.technologies.manufacturings.afor.material.DieForgedAluMaterial
import com.nu.bom.core.technologies.service.ShapeLookupReaderService
import com.nu.bom.core.technologies.steps.afor.ManufacturingStepDieForgingAlu
import com.nu.bom.core.technologies.steps.cracktesting.ManufacturingStepCrackTesting
import com.nu.bom.core.technologies.steps.cuttolength.ManufacturingStepCutToLength
import com.nu.bom.core.technologies.steps.heatmaterial.ManufacturingStepHeatMaterial
import com.nu.bom.core.technologies.steps.inspection.ManufacturingStepInspection
import com.nu.bom.core.technologies.steps.pickling.ManufacturingStepPickling
import com.nu.bom.core.technologies.steps.preforging.ManufacturingStepPreforging
import com.nu.bom.core.technologies.steps.shotblasting.ManufacturingStepShotBlasting
import com.nu.bom.core.technologies.steps.t4.ManufacturingStepT4
import com.nu.bom.core.technologies.steps.t5.ManufacturingStepT5
import com.nu.bom.core.technologies.steps.t6.ManufacturingStepT6
import com.nu.bom.core.utils.annotations.TsetSuppress
import reactor.core.publisher.Mono
import java.math.BigDecimal
import java.math.RoundingMode
import kotlin.reflect.KClass
import kotlin.reflect.full.primaryConstructor

@EntityType(Entities.MANUFACTURING)
class ManufacturingDieForgingAlu(
    name: String,
) : BaseModelManufacturing(name) {
    override val extends: ManufacturingEntity = ShapeBasedCostModuleManufacturing(name)

    override val model: Model
        get() = Model.AFOR

    override val modelSteps: List<KClass<out ManufacturingEntity>>
        get() =
            listOf(
                ManufacturingStepCutToLength::class,
                ManufacturingStepPreforging::class,
                ManufacturingStepHeatMaterial::class,
                ManufacturingStepDieForgingAlu::class,
                ManufacturingStepT4::class,
                ManufacturingStepT5::class,
                ManufacturingStepT6::class,
                ManufacturingStepShotBlasting::class,
                ManufacturingStepCrackTesting::class,
                ManufacturingStepPickling::class,
                ManufacturingStepInspection::class,
            )

    // This will be migrated to be managed via the cost module version.
    // Ticket: COST-63392
    // Due date: Release of semantic versioning (COST-62766)
    // Also, this is @WizardField (and therefore @Input and @Hidden)
    // so the actual value is correctly loaded in the edit flow.
    // See COST-51271.
    @Input
    @WizardField
    @Hidden
    fun shapeBehaviourType(): ShapeBehaviourType = ShapeBehaviourType.THREE_DB

    @BehaviourCreation
    fun createPartBehaviour(shapeBehaviourType: ShapeBehaviourType): DynamicBehaviour {
        val behaviourEntity =
            shapeBehaviourType.res.behaviour.primaryConstructor!!
                .call(name)
        return EntityBasedDynamicBehaviour(this, behaviourEntity)
    }

    fun inputGroupInternal(
        lookupReaderService: ShapeLookupReaderService,
        shapeId: Text,
    ) = lookupReaderService.getInputGroupFromShapeLookup(calculationContext!!.accessCheck, shapeId, Model.AFOR)

    @Input
    @TsetSuppress("tset:engine:field-override-configuration-value")
    fun technology() = Text("AFOR")

    @Input
    @Path("/api/model/afor/materialName")
    fun materialName(): Text? = null

    @Input
    @WizardField(index = 1)
    @SummaryView(SummaryView.PROCESS, 140)
    fun pickling(): SelectableBoolean? = null

    @Input
    @WizardField(index = 2)
    @SummaryView(SummaryView.PROCESS, 120)
    fun inspection(): SelectableBoolean? = null

    @Input
    @WizardField(index = 4)
    @Precompute
    @Condition(field = "machining", value = "NONE", operator = Condition.EQUALS)
    fun cleaningNeeded(cleaningNeededRawPart: SelectableBoolean) = cleaningNeededRawPart

    @Input
    @WizardField(index = 4)
    @Precompute
    @Condition(field = "machining", value = "NONE", operator = Condition.NOT_EQUALS)
    fun cleaningNeededRawPart() = SelectableBoolean.FALSE

    @Input
    @WizardField(index = 5)
    @Precompute
    @Condition(field = "cleaningNeeded", value = "true", operator = Condition.EQUALS)
    fun cleanness(): StepSubTypeCleanness = StepSubTypeCleanness.NORMAL

    @Input
    @WizardField(index = 3)
    @SummaryView(SummaryView.PROCESS, 100)
    fun crackTesting(): SelectableBoolean? = null

    @Input
    @WizardField(index = 6)
    val stepSubTypeHeatTreatmentAlu = StepSubTypeHeatTreatmentAlu.NO_HEAT_TREATMENT

    val <T> T.exhaustive get() = this

    @SummaryView(SummaryView.PROCESS, 100)
    fun heatTreatmentSummaryView(stepSubTypeHeatTreatmentAlu: StepSubTypeHeatTreatmentAlu): Text =
        when (stepSubTypeHeatTreatmentAlu.res) {
            StepSubTypeHeatTreatmentAlu.Selection.T4 -> Text("T4 for aluminium parts")
            StepSubTypeHeatTreatmentAlu.Selection.T5 -> Text("T5 for aluminium parts")
            StepSubTypeHeatTreatmentAlu.Selection.T6 -> Text("T6 for aluminium parts")
            StepSubTypeHeatTreatmentAlu.Selection.NO_HEAT_TREATMENT -> Text("No heat treatment")
        }

    @Input
    @WizardField(index = 7)
    @ReadOnly
    @SummaryView(SummaryView.PROCESS, 150)
    val coldCalibrating: SelectableBoolean? = null

    @Input
    @Condition(field = "coldCalibrating", value = "true", operator = Condition.EQUALS)
    @WizardField(index = 8)
    @DefaultUnit(DefaultUnit.QMM)
    val calibratingAreaPerPart: Area = Area(0.toBigDecimal(), AreaUnits.QMM)

    @Input
    @WizardField(index = 9)
    fun machining(): MachiningForTechnology? = null

    @Input
    @WizardField(index = 10)
    @Condition(field = "machining", value = "TURNING", operator = Condition.EQUALS)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    val margin: Length = Length(BigDecimal(3), LengthUnits.MILLIMETER)

    @Input
    @WizardField(index = 11)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun maxWallThickness(
        @Parent(Entities.PROCESSED_MATERIAL) @Default(NullProvider::class)
        maxWallThickness: Length?,
    ) = maxWallThickness ?: Length(2.0, LengthUnits.MILLIMETER)

    @Input
    val coefficientBulkMaterialLookupKey: Text = Text("AFOR")

    @Input
    val materialClass: Text = Text("MaterialForgingAfor")

    private class CallsPerYearLookupEntry(
        val maxWeight: Weight,
        val complexity: String,
        val serviceLifeInCycles: Num,
    )

    @Input
    @SpecialLink("MaterialForging", "weightBeforeScrapPerPart")
    val weightBeforeScrapPerPart: QuantityUnit? = null

    fun shapeComplexity(
        lookupReaderService: ShapeLookupReaderService,
        @Children(Entities.PROCESSED_MATERIAL)
        shapeId: Text,
    ): Mono<ToolComplexityDieForging> =
        lookupReaderService.getAforShapeData(calculationContext!!.accessCheck, shapeId).map {
            it.complexity
        }

    fun serviceLifeInCyclesForCallsPerYear(
        weightBeforeScrapPerPart: QuantityUnit,
        shapeComplexity: ToolComplexityDieForging,
    ): Mono<QuantityUnit> =
        services
            .getLookupTable("CallsPerYear_AFOR") {
                CallsPerYearLookupEntry(
                    maxWeight = Weight(it[0].toBigDecimal(), WeightUnits.KILOGRAM),
                    complexity = it[1],
                    serviceLifeInCycles = Num(it[2]),
                )
            }.filter {
                val maxWeight = (it.maxWeight.res * 0.85.toBigDecimal()) > weightBeforeScrapPerPart.res
                val specialCaseForMediumParts =
                    68.toBigDecimal() > weightBeforeScrapPerPart.res &&
                        (it.maxWeight.res * 0.85.toBigDecimal()) < weightBeforeScrapPerPart.res &&
                        it.maxWeight.res.compareTo(
                            50.toBigDecimal(),
                        ) == 0
                val specialCaseForBigParts =
                    100.toBigDecimal() > weightBeforeScrapPerPart.res &&
                        (it.maxWeight.res * 0.85.toBigDecimal()) < weightBeforeScrapPerPart.res &&
                        it.maxWeight.res.compareTo(
                            100.toBigDecimal(),
                        ) == 0
                val diffWeight = maxWeight || specialCaseForMediumParts || specialCaseForBigParts

                val simpleShape = shapeComplexity == ToolComplexityDieForging.SIMPLE
                val normalShape = shapeComplexity == ToolComplexityDieForging.NORMAL
                val specialShape = weightBeforeScrapPerPart.res >= 14.25.toBigDecimal()
                val diffComplexity = simpleShape || normalShape || specialShape

                diffWeight && diffComplexity
            }.elementAt(0)
            .map { QuantityUnit(it.serviceLifeInCycles) }

    fun internalCallsPerYear(
        // Todo: Might need to be changed to average volume. See COST-50914
        peakUsableProductionVolumePerYear: QuantityUnit,
        serviceLifeInCyclesForCallsPerYear: QuantityUnit,
    ): Num {
        val res =
            (
                peakUsableProductionVolumePerYear.res /
                    (0.5.toBigDecimal() * serviceLifeInCyclesForCallsPerYear.res).setScale(
                        0,
                        RoundingMode.UP,
                    )
            ).setScale(0, RoundingMode.UP)
        return when {
            peakUsableProductionVolumePerYear.res < 5000.toBigDecimal() -> Num(4.0)
            peakUsableProductionVolumePerYear.res < 20000.toBigDecimal() -> Num(6.0)
            else -> Num(max(12.toBigDecimal(), res))
        }
    }

    @EntityCreation(Entities.PROCESSED_MATERIAL, childCreations = [Entities.MANUFACTURING_STEP])
    fun createMaterial(): ManufacturingEntity =
        createEntity(
            name = "DieForgedAluMaterial",
            clazz = DieForgedAluMaterial::class,
            entityType = Entities.MATERIAL,
        )
}
