package com.nu.bom.core.technologies.manufacturings.inj.material

import com.nu.bom.core.manufacturing.Model
import com.nu.bom.core.manufacturing.annotations.Condition
import com.nu.bom.core.manufacturing.annotations.DefaultUnit
import com.nu.bom.core.manufacturing.annotations.EntityLinkField
import com.nu.bom.core.manufacturing.annotations.EntityLinkProvider
import com.nu.bom.core.manufacturing.annotations.EntityType
import com.nu.bom.core.manufacturing.annotations.ExpectedParents
import com.nu.bom.core.manufacturing.annotations.ExternalDependency
import com.nu.bom.core.manufacturing.annotations.FieldIndex
import com.nu.bom.core.manufacturing.annotations.IgnoreForOverwrittenState
import com.nu.bom.core.manufacturing.annotations.Input
import com.nu.bom.core.manufacturing.annotations.Modularized
import com.nu.bom.core.manufacturing.annotations.Parent
import com.nu.bom.core.manufacturing.annotations.Path
import com.nu.bom.core.manufacturing.annotations.ReadOnly
import com.nu.bom.core.manufacturing.annotations.RequiredFor
import com.nu.bom.core.manufacturing.entities.ManufacturingEntity
import com.nu.bom.core.manufacturing.entities.ManufacturingStep
import com.nu.bom.core.manufacturing.entities.RawMaterialPaint
import com.nu.bom.core.manufacturing.enums.Entities
import com.nu.bom.core.manufacturing.fieldTypes.Area
import com.nu.bom.core.manufacturing.fieldTypes.AreaUnits
import com.nu.bom.core.manufacturing.fieldTypes.Density
import com.nu.bom.core.manufacturing.fieldTypes.Dimension
import com.nu.bom.core.manufacturing.fieldTypes.EntityRef
import com.nu.bom.core.manufacturing.fieldTypes.Length
import com.nu.bom.core.manufacturing.fieldTypes.Num
import com.nu.bom.core.manufacturing.fieldTypes.PaintCoatType
import com.nu.bom.core.manufacturing.fieldTypes.PaintSpreadingRate
import com.nu.bom.core.manufacturing.fieldTypes.PlasticPaintingType
import com.nu.bom.core.manufacturing.fieldTypes.QuantityUnit
import com.nu.bom.core.manufacturing.fieldTypes.Rate
import com.nu.bom.core.manufacturing.fieldTypes.SelectableBoolean
import com.nu.bom.core.manufacturing.fieldTypes.Speed
import com.nu.bom.core.manufacturing.fieldTypes.Text
import com.nu.bom.core.manufacturing.fieldTypes.TotalOrOneSidePaintingType
import com.nu.bom.core.manufacturing.fieldTypes.Volume
import com.nu.bom.core.manufacturing.fieldTypes.VolumeUnits
import com.nu.bom.core.manufacturing.fieldTypes.Weight
import com.nu.bom.core.manufacturing.fieldTypes.WeightUnits
import com.nu.bom.core.technologies.manufacturings.inj.ManufacturingInjection2
import com.nu.bom.core.technologies.steps.plasticcoating.ManufacturingStepPlasticPainting
import com.nu.bom.core.technologies.steps.plasticcoating.ManufacturingStepPlasticPaintingUtils
import com.nu.bom.core.technologies.steps.plasticcoating.ManufacturingStepPlasticPaintingUtils.areaToPaintNetImpl
import com.nu.bom.core.technologies.steps.plasticcoating.ManufacturingStepPlasticPaintingUtils.areaToPaintNetMaskedImpl
import com.nu.bom.core.technologies.steps.plasticcoating.ManufacturingStepPlasticPaintingUtils.boundingRectanglesImpl
import com.nu.bom.core.technologies.steps.plasticcoating.ManufacturingStepPlasticPaintingUtils.shapeRelatedPaintLossRateImpl
import com.tset.bom.clients.tsetdel.model.TsetDelCoatingAreaResponse
import java.math.BigDecimal

@EntityType(Entities.MATERIAL)
@Modularized(
    technologies = [Model.INJ2],
    parents = [
        ExpectedParents(model = Model.INJ2, type = Entities.MANUFACTURING, klass = ManufacturingInjection2::class),
        ExpectedParents(model = Model.INJ2, type = Entities.PROCESSED_MATERIAL, klass = InjectedMaterial2::class),
    ],
    dimensions = [Dimension.Selection.NUMBER],
    userCreatable = true,
)
class MaterialPlasticPaint(
    name: String,
) : ManufacturingEntity(name) {
    override val extends = RawMaterialPaint(name)

    companion object {
        const val CLASS_NAME = "MaterialPlasticPaint"
        const val MATERIAL_PAINT_BASE = "MaterialPaintBase"
        const val MATERIAL_PAINT_CLEAR = "MaterialPaintClear"
        const val MATERIAL_PAINT_TOP = "MaterialPaintTop"
    }

    @EntityLinkProvider([ManufacturingStepPlasticPainting.CLASS_NAME], entityClasses = [ManufacturingStep::class])
    @ExternalDependency(section = ExternalDependency.SECTIONS.STEP)
    @Path("/api/link{bomPath}{branchPath}?entityClasses=${ManufacturingStepPlasticPainting.CLASS_NAME}")
    @FieldIndex(10)
    @IgnoreForOverwrittenState
    fun linkedStep(): EntityRef? = getLinkedStep(ManufacturingStepPlasticPainting::class)

    // region Fields from MANUFACTURING

    @Input
    @Parent(Entities.MANUFACTURING)
    fun technologyKey(): Text? = null

    // endregion

    // region Fields from Entities.PROCESSED_MATERIAL

    @Parent(Entities.PROCESSED_MATERIAL)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 20)
    @RequiredFor([RequiredFor.KpiName.CO2, RequiredFor.KpiName.COST])
    fun shapeId(): Text? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 30)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun partHeight(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 40)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun partLength(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 50)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun partWidth(): Length? = null

    @Input
    @Parent(Entities.PROCESSED_MATERIAL, skipDependencyWhenFieldDoesNotExist = false)
    @ExternalDependency(section = ExternalDependency.SECTIONS.PART)
    @FieldIndex(index = 60)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun partOuterDiameter(): Length? = null

    // endregion

    // region Fields from MANUFACTURING_STEP

    @EntityLinkField(providerField = "linkedStep", "topCoatThickness")
    @ExternalDependency(section = ExternalDependency.SECTIONS.STEP)
    @FieldIndex(index = 20)
    @DefaultUnit(DefaultUnit.MICROMETER)
    @Condition(field = "paintCoatType", value = "TOP", operator = Condition.EQUALS)
    fun topCoatThickness(): Length? = null

    @EntityLinkField(providerField = "linkedStep", "baseCoatThickness")
    @ExternalDependency(section = ExternalDependency.SECTIONS.STEP)
    @FieldIndex(index = 20)
    @DefaultUnit(DefaultUnit.MICROMETER)
    @Condition(field = "paintCoatType", value = "BASE", operator = Condition.EQUALS)
    fun baseCoatThickness(): Length? = null

    @EntityLinkField(providerField = "linkedStep", "clearCoatThickness")
    @ExternalDependency(section = ExternalDependency.SECTIONS.STEP)
    @FieldIndex(index = 20)
    @DefaultUnit(DefaultUnit.MICROMETER)
    @Condition(field = "paintCoatType", value = "CLEAR", operator = Condition.EQUALS)
    fun clearCoatThickness(): Length? = null

    @EntityLinkField(providerField = "linkedStep", "numberOfTopCoatLayers")
    @ExternalDependency(section = ExternalDependency.SECTIONS.STEP)
    @FieldIndex(index = 30)
    @Condition(field = "paintCoatType", value = "TOP", operator = Condition.EQUALS)
    fun numberOfTopCoatLayers(): Num? = null

    @EntityLinkField(providerField = "linkedStep", "numberOfBaseCoatLayers")
    @ExternalDependency(section = ExternalDependency.SECTIONS.STEP)
    @FieldIndex(index = 30)
    @Condition(field = "paintCoatType", value = "BASE", operator = Condition.EQUALS)
    fun numberOfBaseCoatLayers(): Num? = null

    @EntityLinkField(providerField = "linkedStep", "numberOfClearCoatLayers")
    @ExternalDependency(section = ExternalDependency.SECTIONS.STEP)
    @FieldIndex(index = 30)
    @Condition(field = "paintCoatType", value = "CLEAR", operator = Condition.EQUALS)
    fun numberOfClearCoatLayers(): Num? = null

    @ExternalDependency(section = ExternalDependency.SECTIONS.STEP)
    @EntityLinkField(providerField = "linkedStep", "needsMasking")
    @FieldIndex(index = 40)
    fun needsMasking(): SelectableBoolean? = null

    @ExternalDependency(section = ExternalDependency.SECTIONS.STEP)
    @EntityLinkField(providerField = "linkedStep", "relativeMaskedArea")
    @FieldIndex(index = 41)
    @Condition(field = "needsMasking", value = "TRUE", operator = Condition.EQUALS)
    fun relativeMaskedArea() = Rate(BigDecimal.ZERO)

    @Input
    @ExternalDependency(section = ExternalDependency.SECTIONS.STEP)
    @EntityLinkField(providerField = "linkedStep", "totalOrOneSidePainting")
    @FieldIndex(index = 50)
    fun totalOrOneSidePainting(): TotalOrOneSidePaintingType? = null

    @Input
    @ExternalDependency(section = ExternalDependency.SECTIONS.STEP)
    @EntityLinkField(providerField = "linkedStep", "paintingMethod")
    @FieldIndex(index = 60)
    fun paintingMethod(): PlasticPaintingType? = null

    @Input
    @ExternalDependency(section = ExternalDependency.SECTIONS.STEP)
    @EntityLinkField(providerField = "linkedStep", "flashOffSkidLength")
    @FieldIndex(index = 70)
    @DefaultUnit(DefaultUnit.MILLIMETER)
    fun flashOffSkidLength(): Length? = null

    @Input
    @ExternalDependency(section = ExternalDependency.SECTIONS.STEP)
    @EntityLinkField(providerField = "linkedStep", "conveyorSpeed")
    @FieldIndex(index = 80)
    fun conveyorSpeed(): Speed? = null

    // endregion

    fun coatingAreaFromTopPart(coatingAreas: TsetDelCoatingAreaResponse) = Area(coatingAreas.coating_areas.from_top.part, AreaUnits.QMM)

    fun coatingAreaFromTopTotal(coatingAreas: TsetDelCoatingAreaResponse) = Area(coatingAreas.coating_areas.from_top.total, AreaUnits.QMM)

    fun coatingAreaFromRotationalFirstPart(coatingAreas: TsetDelCoatingAreaResponse) =
        Area(coatingAreas.coating_areas.rotational_first.part, AreaUnits.QMM)

    fun coatingAreaFromRotationalFirstTotal(coatingAreas: TsetDelCoatingAreaResponse) =
        Area(coatingAreas.coating_areas.rotational_first.total, AreaUnits.QMM)

    fun areaToPaintNet(
        totalOrOneSidePainting: TotalOrOneSidePaintingType,
        coatingAreaFromTopPart: Area,
        coatingAreaFromRotationalFirstPart: Area,
    ): Area = areaToPaintNetImpl(totalOrOneSidePainting, coatingAreaFromTopPart, coatingAreaFromRotationalFirstPart)

    fun areaToPaintNetMasked(
        relativeMaskedArea: Rate,
        areaToPaintNet: Area,
    ) = areaToPaintNetMaskedImpl(relativeMaskedArea, areaToPaintNet)

    fun boundingRectangles(
        totalOrOneSidePainting: TotalOrOneSidePaintingType,
        coatingAreaFromTopTotal: Area,
        coatingAreaFromRotationalFirstTotal: Area,
    ): Area = boundingRectanglesImpl(totalOrOneSidePainting, coatingAreaFromTopTotal, coatingAreaFromRotationalFirstTotal)

    fun applicationRelatedLossRate(paintingMethod: PlasticPaintingType): Rate =
        ManufacturingStepPlasticPaintingUtils.applicationRelatedLossRateImpl(paintingMethod)

    fun shapeRelatedPaintLossRate(
        boundingRectangles: Area,
        areaToPaintNetMasked: Area,
    ): Rate = shapeRelatedPaintLossRateImpl(boundingRectangles, areaToPaintNetMasked)

    fun actualSprayRate(
        applicationRelatedLossRate: Rate,
        shapeRelatedPaintLossRate: Rate,
    ) = (Rate(BigDecimal.ONE) - applicationRelatedLossRate) * (Rate(BigDecimal.ONE) - shapeRelatedPaintLossRate)

    fun coatThicknessTotal(
        paintCoatType: PaintCoatType,
        topCoatThickness: Length?,
        baseCoatThickness: Length?,
        clearCoatThickness: Length?,
        numberOfTopCoatLayers: Num?,
        numberOfBaseCoatLayers: Num?,
        numberOfClearCoatLayers: Num?,
    ): Length? {
        val (coatThickness, numberOfLayers) =
            when (paintCoatType.res) {
                PaintCoatType.Selection.BASE -> baseCoatThickness to numberOfBaseCoatLayers
                PaintCoatType.Selection.TOP -> topCoatThickness to numberOfTopCoatLayers
                PaintCoatType.Selection.CLEAR -> clearCoatThickness to numberOfClearCoatLayers
            }
        return if (coatThickness != null && numberOfLayers != null) {
            coatThickness * numberOfLayers
        } else {
            null
        }
    }

    @ReadOnly
    fun spreadingRateTotalWet(
        spreadingRatePerMicrometerWet: PaintSpreadingRate,
        coatThicknessTotal: Length,
    ) = spreadingRatePerMicrometerWet.div(coatThicknessTotal.inMicrometer)

    @DefaultUnit(DefaultUnit.MILLILITER)
    fun netMixUsage(
        spreadingRateTotalWet: PaintSpreadingRate,
        areaToPaintNetMasked: Area,
    ) = Volume(areaToPaintNetMasked.inQm / spreadingRateTotalWet.inQmPerLiter, VolumeUnits.LITER)

    fun netMixWeight(
        netMixUsage: Volume,
        density: Density,
    ) = Weight(netMixUsage.inCm * density.inKgPerCm, WeightUnits.KILOGRAM)

    @ReadOnly
    fun netWeightPerPart(netMixWeight: Weight) = QuantityUnit(netMixWeight)

    @DefaultUnit(DefaultUnit.MILLILITER)
    fun scrapMixUsage(
        deployedMixUsage: Volume,
        netMixUsage: Volume,
    ): Volume = deployedMixUsage - netMixUsage

    fun scrapMixWeight(
        scrapMixUsage: Volume,
        density: Density,
    ) = Weight(scrapMixUsage.inCm * density.inKgPerCm, WeightUnits.KILOGRAM)

    @ReadOnly
    fun scrapWeightPerPart(scrapMixWeight: Weight) = QuantityUnit(scrapMixWeight)

    @DefaultUnit(DefaultUnit.MILLILITER)
    fun deployedMixUsage(
        netMixUsage: Volume,
        actualSprayRate: Rate,
    ) = netMixUsage / actualSprayRate

    fun deployedMixWeight(
        netMixWeight: Weight,
        actualSprayRate: Rate,
    ) = QuantityUnit(netMixWeight / actualSprayRate)

    // region RawMaterials override

    fun quantityForExport(deployedWeightPerPart: QuantityUnit) = deployedWeightPerPart

    // endregion
}
