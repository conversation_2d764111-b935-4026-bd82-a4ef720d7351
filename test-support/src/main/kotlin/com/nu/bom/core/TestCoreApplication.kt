package com.nu.bom.core

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication
import org.springframework.context.annotation.Import

@SpringBootApplication(
    scanBasePackages = ["com.nu.bom.core", "com.tset.core", "com.tset.bom.clients", "com.nu.bom.tests.docker"],
)
@Import(DefaultTestConfig::class)
class TestCoreApplication {
    companion object {
        @JvmStatic
        fun main(args: Array<String>) {
            runApplication<TestCoreApplication>(*args)
        }
    }
}
