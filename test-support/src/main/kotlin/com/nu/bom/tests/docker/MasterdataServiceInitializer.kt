package com.nu.bom.tests.docker

import com.nu.bom.core.service.masterdata.MasterdataClientProperties
import com.nu.bom.core.service.masterdata.MasterdataConnectionService
import com.nu.bom.core.version.gitlab.GitLabApiForIT
import com.nu.bom.tests.docker.MasterdataContainerManagement.Companion.SCHEMA_TENANT_NAME
import com.nu.security.BomradsService
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.context.annotation.Profile
import org.springframework.core.env.Environment
import org.springframework.test.context.ActiveProfiles
import software.amazon.awssdk.services.ecr.EcrClient
import java.time.Duration

const val MASTERDATA_REPOSITORY_NAME = "nu-masterdata"

fun interface MasterdataAdditionalHeaderProvider {
    fun provideAdditionalHeaders(): List<Pair<String, String>>?
}

/**
 * By default all tests use the same tenant on masterdata side.
 * This means that changing data in the masterdata service would affect all tests.
 * If you want to change data in masterdata service, make sure to add this annotation to your test. then a new tenant wil be provisioned for your test that does not affect other tests
 */
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
@ActiveProfiles("update-masterdata", "test")
annotation class MasterdataUpdateTest

/**
 * Configures the Spring context to either use Masterdata on a specified location - if 'MASTERDATA' environment
 * variable or system property defined.
 * Or start a Masterdata and PostgreSQL container and use that.
 */
@Configuration
@Profile("test & !no-md")
class MasterdataServiceInitializer {
    companion object {
        private val logger = LoggerFactory.getLogger(MasterdataServiceInitializer::class.java)
    }

    @Profile("test && !update-masterdata")
    @Bean
    fun readOnlyMdHeaders(): MasterdataAdditionalHeaderProvider =
        MasterdataAdditionalHeaderProvider {
            // the implication of this is, that by default all tests will use the same schema/account
            // this is fine for read-only tests, where nothing is updated in masterdata, but not if changes in masterdata happen
            listOf(BomradsService.NU_ACCOUNT_HEADER to SCHEMA_TENANT_NAME)
        }

    @Profile("test && update-masterdata")
    @Bean
    fun updateMdHeaders(): MasterdataAdditionalHeaderProvider = MasterdataAdditionalHeaderProvider { listOf() }

    @Bean
    @Primary
    fun masterdataService(
        environment: Environment,
        headerProvider: MasterdataAdditionalHeaderProvider,
        nbkGitLabApi: GitLabApiForIT,
        ecrClient: EcrClient,
    ): MasterdataConnectionService {
        val imageTagToUse =
            TestcontainerUtil.getImageTagToUseFromEcr(
                MASTERDATA_REPOSITORY_NAME,
                environment,
                ecrClient,
                nbkGitLabApi,
            )

        logger.info("🎆🎆🎆 $MASTERDATA_REPOSITORY_NAME: imageTag: $imageTagToUse")

        val createClientProperties =
            MasterdataClientProperties(
                url = getMasterdataUrl(environment, imageTagToUse),
                latestVersionLookupCacheDuration = Duration.ZERO,
                lookupCacheDuration = Duration.ZERO,
                crudCacheDuration = Duration.ZERO,
            )
        return MasterdataConnectionService(
            createClientProperties,
            headerProvider.provideAdditionalHeaders(),
        )
    }

    private fun getMasterdataUrl(
        environment: Environment,
        tagName: String,
    ): String {
        // to use locally running masterdata instance use "http://localhost:8104" as customUrl
        val customUrl = environment.getProperty("masterdata")
        return if (customUrl != null) {
            logger.info("🎆🎆🎆 Configuring spring to use Masterdata at $customUrl")
            customUrl
        } else {
            logger.info("🎆🎆🎆 Configuring spring to use Masterdata in docker")

            val container = MasterdataContainerManagement.getInstance(tagName)
            container.getMasterdataUrl()
        }
    }
}
