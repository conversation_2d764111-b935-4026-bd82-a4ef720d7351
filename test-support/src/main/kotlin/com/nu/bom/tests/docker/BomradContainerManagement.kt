package com.nu.bom.tests.docker

import org.slf4j.LoggerFactory
import org.testcontainers.containers.GenericContainer
import org.testcontainers.containers.Network
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.containers.wait.strategy.Wait
import org.testcontainers.utility.DockerImageName
import java.time.Duration

class BomradContainerManagement private constructor(
    private val network: Network,
    private val database: PostgreSQLContainer<*>,
    private val bomrad: GenericContainer<*>,
) {
    fun getJdbcUrl(): String = "jdbc:postgresql://${database.host}:${database.firstMappedPort}/${database.databaseName}"

    fun getBomradUrl(): String = "http://${bomrad.host}:${bomrad.getMappedPort(8087)}"

    fun close() {
        bomrad.stop()
        database.stop()
        network.close()
    }

    companion object {
        private val logger = LoggerFactory.getLogger(BomradContainerManagement::class.java)
        private var instance: BomradContainerManagement? = null

        private fun create(
            schema: String,
            tagName: String,
        ): BomradContainerManagement {
            val network = TestcontainerUtil.createReusableNetwork("bomrad")
            val db = buildPostgresContainer(network)
            logger.info("🚀 - Launching Postgres")
            try {
                db.start()
            } catch (e: Exception) {
                logger.info("Unable to start Postgres: ${e.message}", e)
                logger.info(db.logs)
                throw e
            }
            val bomrad = buildBomradContainer(network, schema, tagName)
            logger.info("🚀🚀 - Launching Bomrads")
            try {
                bomrad.start()
                val setup = BomradContainerManagement(network, db, bomrad)
                logger.info("🛰 - PostgreSQL runs at ${setup.getJdbcUrl()}")
                logger.info("🛰🛰 - Bomrads runs at ${setup.getBomradUrl()}")
                return setup
            } catch (e: Exception) {
                logger.info("Unable to start Bomrads: ${e.message}", e)
                logger.info(bomrad.logs)
                throw e
            }
        }

        private fun buildPostgresContainer(network: Network): PostgreSQLContainer<*> {
            return PostgreSQLContainer("postgres:14.6")
                .withUsername("BomRads")
                .withPassword("BomRads123")
                .withDatabaseName("BomRads")
                .withNetwork(network)
                .withNetworkAliases("bomrads-postgresql")
                .withReuse(true)
        }

        private fun buildBomradContainer(
            networkConfig: Network,
            schema: String,
            tagName: String,
        ): GenericContainer<*> {
            return GenericContainer(
                DockerImageName.parse("684712464887.dkr.ecr.eu-central-1.amazonaws.com/nu-bomrads:$tagName"),
            ).waitingFor(Wait.defaultWaitStrategy().withStartupTimeout(Duration.ofSeconds(120L)))
                .withNetwork(networkConfig)
                .withNetworkAliases("bomrads")
                .withExposedPorts(8087, 5005)
                .withEnv("SPRING_PROFILES_ACTIVE", "dev,dockerlocal,nbk-integration-test")
                .withEnv("SPRING_ZIPKIN_ENABLED", "false")
                .withEnv("APPLICATION_DATABASE_SCHEMA", schema)
                .withEnv("JAVA_MEMORY_OPTS", "-Xms256m -Xmx2048m")
                .withImagePullPolicy { true }
                .withReuse(true)
        }

        @Synchronized
        fun getInstance(tagName: String): BomradContainerManagement {
            if (instance == null) {
                instance = create("nbk_integration_tests", tagName)
            }
            return instance!!
        }

        @JvmStatic
        fun main(args: Array<String>) {
            val instance = getInstance("latest")
            println("press enter to exit!")
            readln()
            instance.close()
        }
    }
}
