package com.nu.bom.tests.docker

import com.mongodb.ConnectionString
import com.mongodb.MongoClientSettings
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.mongo.MongoClientFactory
import org.springframework.boot.autoconfigure.mongo.ReactiveMongoClientFactory
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile
import org.springframework.context.event.EventListener
import org.springframework.core.annotation.Order
import org.springframework.core.env.Environment
import org.springframework.data.mongodb.MongoDatabaseFactory
import org.springframework.data.mongodb.ReactiveMongoDatabaseFactory
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory
import org.springframework.data.mongodb.core.SimpleReactiveMongoDatabaseFactory

@Configuration
@Profile("test")
@Order(100)
class MongoInitializer(val env: Environment) {
    private val logger: Logger = LoggerFactory.getLogger(MongoInitializer::class.java)

    @Bean
    fun mongoDbFactory(mongoClient: com.mongodb.client.MongoClient): MongoDatabaseFactory {
        logger.debug("Creating MongoDatabaseFactory")
        return SimpleMongoClientDatabaseFactory(mongoClient, MongoContainerManagement.DB_NAME)
    }

    @Bean
    fun reactiveMongoDbFactory(mongoClient: com.mongodb.reactivestreams.client.MongoClient): ReactiveMongoDatabaseFactory {
        logger.debug("Creating ReactiveMongoDatabaseFactory")
        return SimpleReactiveMongoDatabaseFactory(mongoClient, MongoContainerManagement.DB_NAME)
    }

    @Bean
    fun mongoClient(): com.mongodb.client.MongoClient {
        logger.debug("Creating MongoClient")
        return mongoClient(select())
    }

    @Bean
    fun reactiveMongoClient(): com.mongodb.reactivestreams.client.MongoClient {
        logger.debug("Creating reactive MongoClient")
        return reactiveMongoClient(select())
    }

    @EventListener(ApplicationReadyEvent::class)
    @Order(200)
    fun onStartup() {
        if (env.activeProfiles.contains("masterdata-init")) {
            MongoContainerManagement.saveSnapshot()
        }
    }

    private fun select(): MongoContainerManagement {
        return when {
            env.activeProfiles.contains("masterdata-init") -> MongoContainerManagement.getMasterDataInstance()
            else -> MongoContainerManagement.getInstance()
        }
    }

    private fun mongoClient(container: MongoContainerManagement): com.mongodb.client.MongoClient {
        return MongoClientFactory(emptyList()).createMongoClient(
            MongoClientSettings.builder().applyConnectionString(ConnectionString(container.getMongoUrl())).build(),
        )
    }

    private fun reactiveMongoClient(container: MongoContainerManagement): com.mongodb.reactivestreams.client.MongoClient {
        return ReactiveMongoClientFactory(listOf()).createMongoClient(
            MongoClientSettings.builder().applyConnectionString(ConnectionString(container.getMongoUrl())).build(),
        )
    }
}
