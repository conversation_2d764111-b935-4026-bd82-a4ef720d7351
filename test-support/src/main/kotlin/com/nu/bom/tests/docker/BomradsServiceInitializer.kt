package com.nu.bom.tests.docker

import com.nu.bom.core.service.bomrads.BomradsClientProperties
import com.nu.bom.core.service.bomrads.BomradsService
import com.nu.bom.core.user.UserClient
import com.nu.bom.core.version.gitlab.GitLabApiForIT
import com.nu.http.EnvironmentNameSupplier
import com.nu.http.TsetService
import com.nu.security.config.NuHeadersConfiguration
import com.tset.bom.clients.FaultToleranceService
import jakarta.annotation.PreDestroy
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.context.annotation.Profile
import org.springframework.core.env.Environment
import software.amazon.awssdk.services.ecr.EcrClient

/**
 * Configures the Spring context to either use Bo<PERSON>rad<PERSON> on a specified location - if 'BOMRADS' environment variable or system property defined.
 * Or start a Bomrads and PostgreSQL container and use that.
 */

const val BOMRADS_REPOSITORY_NAME = "nu-bomrads"

@Configuration
@Profile("test & !no-bomrads")
class BomradsServiceInitializer {
    companion object {
        private val logger = LoggerFactory.getLogger(BomradsServiceInitializer::class.java)
    }

    @Bean
    @Primary
    fun bomradsService(
        environment: Environment,
        faultToleranceService: FaultToleranceService,
        userClient: UserClient,
        headerConfig: NuHeadersConfiguration,
        environmentNameSupplier: EnvironmentNameSupplier,
        nbkGitLabApi: GitLabApiForIT,
        ecrClient: EcrClient,
        tsetService: TsetService,
    ): BomradsService {
        val imageTagToUse =
            TestcontainerUtil.getImageTagToUseFromEcr(
                BOMRADS_REPOSITORY_NAME,
                environment,
                ecrClient,
                nbkGitLabApi,
            )

        logger.info("🎆🎆🎆 $BOMRADS_REPOSITORY_NAME: imageTag: $imageTagToUse")

        val createClientProperties = BomradsClientProperties(url = getBomradUrl(environment, imageTagToUse))
        return BomradsService(
            userClient,
            headerConfig,
            environmentNameSupplier,
            createClientProperties,
            tsetService,
        )
    }

    @PreDestroy
    fun shutdown() {
        BomradContainerManagement.closeInstance()
    }

    private fun getBomradUrl(
        environment: Environment,
        tagName: String,
    ): String {
        // to use locally running bomrads instance use "http://localhost:8087" as customUrl
        val customUrl = environment.getProperty("bomrads")
        return if (customUrl != null) {
            logger.info("🎆🎆🎆 Configuring spring to use Bomrads at $customUrl")
            customUrl
        } else {
            logger.info("🎆🎆🎆 Configuring spring to use Bomrads in docker")

            val container = BomradContainerManagement.getInstance(tagName)
            container.getBomradUrl()
        }
    }
}
