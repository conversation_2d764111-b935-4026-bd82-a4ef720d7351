replicaCount: 1
pod_request_memory: "12Gi"  # request memory for the pod, might be overridden in the Makefile for develop deployments!
pod_limit_memory: "12Gi"    # limit memory for the pod, might be overridden in the Makefile for develop deployments!
pod_request_cpu: "100m"


image:
  repository: 684712464887.dkr.ecr.eu-central-1.amazonaws.com/nu-bom-kotlin
  tag: latest
  pullPolicy: Always

java:
  memory_opts: "-XX:+UseContainerSupport -XX:MaxRAMPercentage=75"  #Heap Memory for the JVM

prediction:
  dca:
    url: http://nu-cost-models-dca
  inj:
    url: http://nu-cost-models-inj
  sand:
    url: http://nu-cost-models-sand

spring:
  profiles: cloud,shape-init,redis-pubsub,rabbitmq,redis,elasticsearch
  zipkin:
    enabled: true

server:
  error:
    include_message: always
    include_binding_errors: always
    include_stacktrace: always
    include_exception: true

elasticsearch:
  name: production
  indices:
    - useraccess
    - bomnode

mongodb:
  hosted: false
  memory_limit: 1G

usertset:
  issues:
    bug:
      id: 10004
      project: COST
    feature:
      id: 10028
      project: COST
    feedback:
      id: 10057
      project: UF

gitlab:
  access_token: ""
####

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

feature:
  name: ""
  branch: master
  base: ""

mongodb:
  db_name: ""

rabbitmq:
  name: ""
  drop: false
  exchanges:
    - events-dlx
    - general-topic-exchange
    - fti-dlx
  queues:
    - events-dlx-queue
    - events-queue
    - fti-queue
    - fti-dlx-queue

redis:
  name: ""

datadog:
  enabled: true
  environment: ""

s3:
  restore: false
  environment:
  buckets:
    - files
    - blobs
    - reports
    - exports
  sync_buckets:
    - files
    - blobs
    - reports

service:
  type: ClusterIP
  port: 80
  management: 81
  jmx: 1234
  annotations: {}

podAnnotations:
  admission.datadoghq.com/java-lib.version: v1.46.1
  ad.datadoghq.com/nu-bom-kotlin.logs: '[{"source": "java"}]'
  ad.datadoghq.com/nu-bom-kotlin.checks: |
    {
      "openmetrics": {
        "instances": [
          {
          "openmetrics_endpoint": "http://%%host%%:8081/actuator/prometheus",
          "namespace": "nu-bom-kotlin",
          "metrics": [
            "resilience4j_*",
            "mongodb_driver_pool_*"
            ]
          }
        ]
      }
    }
