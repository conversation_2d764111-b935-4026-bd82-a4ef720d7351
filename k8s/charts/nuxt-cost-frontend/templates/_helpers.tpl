{{/* vim: set filetype=mustache: */}}
{{/*
Expand the name of the chart.
*/}}
{{- define "nu-cost-frontend.name" -}}
{{- default "nu-cost-frontend" .Values.nameOverride | trunc 53 | trimSuffix "-" | replace "/" "-" -}}
{{- end -}}

{{/*
Create a default fully qualified app name.
We truncate at 53 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "nu-cost-frontend.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 53 | trimSuffix "-" }}
{{- else -}}
{{- .Release.Name | trunc 53 | trimSuffix "-" }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "nu-cost-frontend.chart" -}}
{{- printf "%s-%s" "nu-cost-frontend" .Chart.Version | replace "+" "_" | trunc 53 | trimSuffix "-" | replace "/" "-" -}}
{{- end -}}

{{/*
Feature name is used for service discovery
*/}}
{{- define "nu-cost-frontend.feature-name" -}}
{{- default "" .Values.feature.name | trimPrefix "feature-" -}}
{{- end -}}


{{/*
Annotations
*/}}
{{- define "nu-cost-frontend.annotations" -}}
app.tset/version: "{{ .Values.image.tag }}"
{{- if .Values.feature.name }}
tset.com/feature-name: {{ include "nu-cost-frontend.feature-name" . }}
{{- end -}}
{{- end -}}

{{/*
Common labels
*/}}
{{- define "nu-cost-frontend.labels" -}}
app.kubernetes.io/name: {{ include "nu-cost-frontend.name" . }}
helm.sh/chart: {{ include "nu-cost-frontend.chart" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- if .Values.feature.name }}
tset.com/feature-name: {{ include "nu-cost-frontend.feature-name" . }}
{{- end -}}
{{- end -}}

{{- define "nu-cost-frontend.selectorLabels" -}}
app.kubernetes.io/name: {{ include "nu-cost-frontend.fullname" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end -}}

{{- define "datadog.labels" -}}
tags.datadoghq.com/env: {{ .Release.Namespace | quote }}
tags.datadoghq.com/service: {{ include "nu-cost-frontend.name" . | quote }}
tags.datadoghq.com/version: {{ .Values.image.tag | quote }}
admission.datadoghq.com/enabled: "true"
{{- end -}}
