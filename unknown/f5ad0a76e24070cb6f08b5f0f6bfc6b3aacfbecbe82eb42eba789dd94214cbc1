package com.nu.bom.core.api

import com.nu.bom.core.api.dtos.BomNodeDto
import com.nu.bom.core.exception.readable.ErrorCode
import com.nu.bom.core.exception.userException.NodeIsProtectedException
import com.nu.bom.core.manufacturing.utils.ManufacturingWizardTestBuilder
import com.nu.bom.core.model.BomNodeId
import com.nu.bom.core.model.toMongoProjectId
import com.nu.bom.core.service.BomTreeService
import com.nu.bom.core.utils.AccountTestUtil.Companion.authHeader
import com.nu.bom.core.utils.NbkClient
import com.nu.bom.core.utils.assertErrorThat
import com.nu.bom.core.utils.toObjectId
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.reactive.server.WebTestClient
import org.springframework.test.web.reactive.server.expectBody
import org.springframework.test.web.reactive.server.returnResult

@SpringBootTest
@ActiveProfiles("test")
@AutoConfigureWebTestClient
class BomradsControllerTest {
    @Autowired
    private lateinit var bomTreeService: BomTreeService

    @Autowired
    private lateinit var wizardBuilder: ManufacturingWizardTestBuilder

    @Autowired
    private lateinit var webTestClient: WebTestClient

    @Autowired
    private lateinit var nbkClient: NbkClient

    @Test
    @DisplayName("When status api is called with no jwt, the default status is returned")
    fun testDefaultStatus() {
        val result =
            webTestClient
                .get()
                .uri("/api/bomrads/status")
                .accept(MediaType.APPLICATION_JSON)
                .exchange()
                .expectStatus()
                .isOk
                .expectBody(BomradsController.Status::class.java)
                .returnResult()
                .responseBody

        Assertions.assertEquals("account_default", result?.accountName)
        Assertions.assertEquals("system", result?.userName)
        Assertions.assertEquals(true, result!!.bomradEnabled, "bomradsEnabled")
        Assertions.assertEquals("EMPTY_TOKEN", result.token)
        Assertions.assertFalse(result.useServiceTokenAsFallback, "useServiceTokenAsFallback")
    }

    @Test
    fun `protects bom node`() {
        val (accessCheck, project) = nbkClient.setupWithProject(name = "Test Project", key = "BNUIT")

        val bomNode =
            wizardBuilder
                .createStandard()
                .bomNode
        val bomNodeId = bomNode.id

        webTestClient
            .put()
            .uri("/api/bomrads/$bomNodeId/protect")
            .accept(MediaType.APPLICATION_JSON)
            .authHeader(accessCheck.token)
            .exchange()
            .expectStatus()
            .isOk

        val savedBomNode =
            webTestClient
                .get()
                .uri("/api/man/$bomNodeId") // in ManufacturingQueryController
                .authHeader(accessCheck.token)
                .exchange()
                .expectStatus()
                .isOk
                .returnResult<BomNodeDto>()
                .responseBody
                .blockFirst()

        Assertions.assertNotNull(savedBomNode!!.protectedAt)
        Assertions.assertEquals("system", savedBomNode.protectedBy)
    }

    @Test
    fun `can't protect already protected bom node`() {
        val (accessCheck, _) = nbkClient.setupWithProject(name = "Test Project", key = "BNUIT")

        val bomNode =
            wizardBuilder
                .createStandard()
                .bomNode
        val bomNodeId = bomNode.id

        webTestClient
            .put()
            .uri("/api/bomrads/$bomNodeId/protect")
            .accept(MediaType.APPLICATION_JSON)
            .authHeader(accessCheck.token)
            .exchange()
            .expectStatus()
            .isOk

        webTestClient
            .put()
            .uri("/api/bomrads/$bomNodeId/protect")
            .accept(MediaType.APPLICATION_JSON)
            .authHeader(accessCheck.token)
            .exchange()
            .expectStatus()
            .isEqualTo(HttpStatus.CONFLICT)
            .expectBody<Map<String, Any>>()
            .consumeWith { response ->
                val error = response.responseBody
                assertThat(error).isNotEmpty
                val userErrorCode = error!!["userErrorCode"] as String
                assertThat(userErrorCode).contains(ErrorCode.NODE_IS_ALREADY_PROTECTED.name)
            }
    }

    @Test
    fun `protect throws error if node not found`() {
        val (accessCheck, _) = nbkClient.setupWithProject(name = "Test Project", key = "BNUIT")

        val bomNodeId = BomNodeId()

        webTestClient
            .put()
            .uri("/api/bomrads/$bomNodeId/protect")
            .accept(MediaType.APPLICATION_JSON)
            .authHeader(accessCheck.token)
            .exchange()
            .expectStatus()
            .isNotFound
    }

    @Test
    fun `cant create variant in protected node`() {
        val (accessCheck, project) = nbkClient.setupWithProject(name = "Test Project", key = "BNUIT")

        val bomNode = wizardBuilder.createStandard().bomNode
        val bomNodeId = bomNode.id.toObjectId()!!

        webTestClient
            .put()
            .uri("/api/bomrads/${bomNode.id}/protect")
            .accept(MediaType.APPLICATION_JSON)
            .authHeader(accessCheck.token)
            .exchange()
            .expectStatus()
            .isOk

        // This creates new varaint
        assertErrorThat(
            bomTreeService.copyMain(accessCheck, bomNodeId, "main-branch-copy"),
        ).satisfies({ e ->
            e is NodeIsProtectedException && e.userErrorCode == ErrorCode.NODE_IS_PROTECTED.name
        })
    }

    @Test
    fun `cant delete variant in protected node`() {
        val (accessCheck, project) = nbkClient.setupWithProject(name = "Test Project", key = "BNUIT")

        val projectId = project.project!!.id.toMongoProjectId()
        val bomNode = wizardBuilder.createStandard().bomNode
        val bomNodeId = bomNode.id.toObjectId()!!

        // This creates new varaint
        val branchDTO = bomTreeService.copyMain(accessCheck, bomNodeId, "main-branch-copy").block()!!

        webTestClient
            .put()
            .uri("/api/bomrads/${bomNode.id}/protect")
            .accept(MediaType.APPLICATION_JSON)
            .authHeader(accessCheck.token)
            .exchange()
            .expectStatus()
            .isOk

        // Delete branch
        val response =
            webTestClient
                .post()
                .uri("/api/projects/$projectId/branch/${branchDTO.id}/delete")
                .accept(MediaType.APPLICATION_JSON)
                .authHeader(accessCheck.token)
                .exchange()
                .expectStatus()
                .is4xxClientError
                .returnResult<Exception>()
                .responseBody
                .blockFirst()

        Assertions.assertEquals(response!!.message, "Node is protected")
    }
}
