import generators.PackageUtil
import org.gradle.api.logging.Logger
import java.io.File
import java.net.URI

class ProjectGenerator(
    private val source: String,
    private val auth: AuthProps,
    private val target: String,
    private val logger: Logger
) {
    private val t = File(target)

    init {
        t.mkdirs()
    }

    fun generateSource() {
        logger.info("Generating into $target")
        logger.info("Using the source $source")
        val schemaFetcher = SchemaFetcher(URI(source), auth, logger)
        val codeGenerator = CodeGenerator(t, PackageUtil("com.tset.clientsdk.schema"), schemaFetcher, logger)
        codeGenerator.generate()
    }

}