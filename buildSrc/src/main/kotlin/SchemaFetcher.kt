import com.nu.bom.core.publicapi.dtos.SchemaField
import com.nu.bom.core.publicapi.dtos.SchemaFieldsDefinition
import com.nu.bom.core.publicapi.dtos.TypeDefinition
import definition.StaticTranslationsDefinition
import org.gradle.api.logging.Logger
import java.math.BigDecimal
import java.net.URI

private val primitiveTypeNames = mapOf(
    "String" to "String",
    "Decimal" to "Double",
    "Integer" to "Int",
    "Boolean" to "Boolean"
)

private fun primitiveTypeOrElse(type: String?, default: () -> String) = primitiveTypeNames[type] ?: default()

private val requiredFields = mapOf(
    "Labor" to mapOf(
        "requiredLabor" to "0.0"
    ),
    "Tool" to mapOf(
        "investPerTool" to "0.0",
        "serviceLifeInCycles" to "1.0",
        "maintenanceRate" to "0.0"
    ),
    "Manufacturing" to mapOf(
        "location" to "\"Austria\"",
        "customProcurementType" to "\"tset.ref.lov-entry.purchased\""
    ),
    "BaseEntityFields" to mapOf(
        "baseCurrency" to "\"EUR\""
    ),
    "ManualMaterial" to mapOf(
        "materialBasePrice" to "0.0",
        "inputQuantity" to "0.0"
    )
)

class SchemaFetcher(source: URI, auth: AuthProps, logger: Logger) {

    private val httpService = HttpService(source, auth, logger)

    private val typeDefinitions: List<TypeDefinition> by lazy {
        httpService.getTypeDefinitionList()
    }

    val typeDefinitionBases = typeDefinitions.associate { td -> td.type to TypeDefinitionsBase.parse(td) }

    private val entityDefinitions: List<EntityDefinition> by lazy {
        val entityTypeMapping = httpService.getEntityList().associateBy { it.name }
        val entityFields = httpService.getEntityFieldsList()
        val entityFieldMap = entityFields.associateBy { it.entity }

        fun abstract(fieldType: String): Boolean = setOf("FieldResult", "NumericFieldResultWithUnit").contains(fieldType)

        fun inherited(entity: SchemaFieldsDefinition, fieldName: String, isInput: Boolean): Boolean =
            entityFieldMap[entity.extends]?.let { extending ->
                extending.fields.firstOrNull { it.name == fieldName }?.let {
                    if ((it.input == true) == isInput) {
                        true
                    } else null
                } ?: inherited(extending, fieldName, isInput)
            } ?: false

        entityFields.map { entity ->
            val fields = entity.fields.mapNotNull { field ->
                if (inherited(entity, field.name, field.input ?: false, )) {
                    null
                } else if (abstract(field.tp())) {
                    null
                } else {
                    val typeDefinition = typeDefinitionBases[field.tp()] ?: error("type not found: ${field.tp()} but defined for field ${entity.entity}.${field.name}")
                    val defaultValue = requiredFields[entity.entity]?.get(field.name)
                    val required = defaultValue != null
                    EntityFieldDefinition(field.name, typeDefinition, field.input ?: false, field.readOnly ?: false, required, defaultValue)
                }
            }
            val type = entityTypeMapping[entity.entity]?.type ?: throw Exception("Entity type not defined: ${entity.entity}!")
            EntityDefinition(entity.entity, type, fields, entity.extends)
        }
    }

    val selectables: List<Selectable> by lazy {
        typeDefinitions.mapNotNull {
            when (it.category) {
                "Selectable" -> Selectable(it.type, it.selectables ?: emptyList())
                else -> null
            }
        }
    }

    val units: List<QuantityType> by lazy {
        typeDefinitions.mapNotNull { typeDef ->
            when (typeDef.category) {
                "DecimalWithUnit" -> {
                    val defaultUnit = typeDef.defaultUnit ?: typeDef.units?.first()?.unit ?: error("no default unit defined")
                    QuantityType(typeDef.type, typeDef.units?.map { UnitDefinition(it.unit, it.factor) } ?: emptyList(), defaultUnit)
                }
                else -> null
            }
        }
    }

    val entities: Map<String, EntityDefinition> by lazy {
        entityDefinitions.associateBy { it.name }
    }

    val staticTranslations: StaticTranslationsDefinition by lazy {
        httpService.getStaticTranslations()
    }

    private fun SchemaField.tp() = type.type()
    private fun String.type() = split('.').lastOrNull() ?: error("Can not parse type name: $this")
}

data class Selectable(val name: String, val selectables: List<String>)

data class QuantityType(val name: String, val units: List<UnitDefinition>, val defaultUnit: String)

class EntityDefinition(
    val name: String,
    val type: String,
    val fields: List<EntityFieldDefinition>,
    val extends: String? = null
) {
    val inputFields: List<EntityFieldDefinition>
        get() = fields.filter { it.input }.toList()
    val overrideFields: List<EntityFieldDefinition>
        get() = fields.filter { !it.input }.toList()

}

data class EntityFieldDefinition(
    val name: String,
    val typeDefinition: TypeDefinitionsBase,
    val input: Boolean,
    val readOnly: Boolean,
    val required: Boolean,
    val defaultValue: String?
)

sealed class TypeDefinitionsBase(
    val typeName: String,
    val kotlinType: String,
) {
    companion object {
        private fun String.type() =
            split('.').lastOrNull() ?: throw Exception("Can not parse type name: $this")

        fun parse(type: TypeDefinition): TypeDefinitionsBase {
            val t = type.type.type()
            return when(type.category) {
                "Primitive" -> PrimitiveTypeDefinition(t, primitiveTypeOrElse(type.valueType) { error("Primitive without type!! $type") })
                "Complex" -> ComplexTypeDefinition(t)
                "Selectable" -> SelectableTypeDefinition(t, type.selectables?.toSet() ?: error("List of selectables empty"))
                "DecimalWithUnit" -> QuantityTypeDefinition(
                    t,
                    type.units?.map { UnitDefinition(it.unit, it.factor) }?.toSet() ?: error("List of units empty"),
                    type.defaultUnit
                        ?: type.units?.first()?.unit
                        ?: error("Default unit not defined")
                )
                // TODO: we are currently missing information about key / value types from the schema api.
                "List" -> ListTypeDefinition(t, primitiveTypeOrElse(type.valueType) { "Any" })
                "Map" -> MapTypeDefinition(t, primitiveTypeOrElse(type.keyType) { "Any" }, primitiveTypeOrElse(type.valueType) { "Any" })
                else -> error("Invalid type definition: ${type.category}")
            }
        }
    }
}

open class PrimitiveTypeDefinition(
    typeName: String,
    kotlinType: String = "Any"
) : TypeDefinitionsBase(typeName, kotlinType)

open class ComplexTypeDefinition(
    typeName: String
) : TypeDefinitionsBase(typeName, "Any")

open class SelectableTypeDefinition(
    typeName: String,
    val validSelections: Set<String>
) : TypeDefinitionsBase(typeName, typeName)

open class ListTypeDefinition(
    typeName: String,
    val elementType: String
) : TypeDefinitionsBase(typeName, "MutableList<$elementType>")

open class MapTypeDefinition(
    typeName: String,
    val keyType: String,
    val valueType: String
) : TypeDefinitionsBase(typeName, "MutableMap<$keyType, $valueType>")

class UnitDefinition(val name: String, val factor: BigDecimal)

open class QuantityTypeDefinition(
    typeName: String,
    val validUnits: Set<UnitDefinition>,
    @Suppress("unused") val defaultUnitName: String
) : TypeDefinitionsBase(typeName, typeName)
