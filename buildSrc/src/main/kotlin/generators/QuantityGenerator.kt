package generators

import QuantityType
import SchemaFetcher
import com.squareup.kotlinpoet.*
import enumConstant
import org.gradle.api.logging.Logger
import unitsClassName
import java.io.File
import java.math.BigDecimal
import java.math.MathContext

class QuantityGenerator(
    private val target: File,
    private val packageUtil: PackageUtil,
    private val schemaFetcher: SchemaFetcher,
    private val logger: Logger
) {

    fun quantityTypes() {
        logger.info("Handling ${schemaFetcher.units.size} QuantityTypes")
        schemaFetcher.units.forEach {
            val unitClass = ClassName(packageUtil.unitsPackage, unitsClassName(it))
            val unitName = "unit"
            val thisName = ClassName(packageUtil.quantitiesPackage, it.name)
            val typeSpec = TypeSpec.classBuilder(thisName)
                .superclass(ClassName(packageUtil.quantitiesPackage, "QuantityTypeBase"))
                .addSuperclassConstructorParameter("value.toBigDecimal(), unit.unitName")
                .primaryConstructor(
                    FunSpec.constructorBuilder()
                    .addParameter("value", Double::class)
                    .addParameter(unitName, unitClass)
                    .build()
                )
                .addProperty(PropertySpec.builder(unitName, unitClass).initializer(unitName).build())
                .addType(
                    TypeSpec.companionObjectBuilder()
                    .addProperty(
                        PropertySpec
                        .builder("defaultUnit", unitClass)
                        .initializer("%T.%N", unitClass, it.defaultUnit.enumConstant())
                        .build()
                    )
                    .build()
                )
                .addFunction(FunSpec.builder("inUnit")
                    .addModifiers(KModifier.INFIX)
                    .addParameter("newUnit", unitClass)
                    .returns(thisName)
                    .addStatement("return %T((value * factor(unit).divide(factor(newUnit), %T.DECIMAL64)).toDouble(), newUnit)", thisName, MathContext::class)
                    .build())
                .addFunction(FunSpec.builder("factor")
                    .addModifiers(KModifier.PRIVATE)
                    .addParameter("unit", unitClass)
                    .returns(BigDecimal::class)
                    .addCode(factorStatement(it, unitClass))
                    .build())
            val funSpec = FunSpec.builder("inUnit")
                .addModifiers(KModifier.INFIX)
                .receiver(Double::class)
                .addParameter("unit", unitClass)
                .addStatement("return %T(this, unit)", thisName)

            val fileSpec = FileSpec.builder(thisName)
                .addType(typeSpec.build())
                .addFunction(funSpec.build())
                .build()
            fileSpec.writeTo(target)
        }
    }

    private fun factorStatement(quantityType: QuantityType, unitClass: ClassName): CodeBlock =
        CodeBlock.builder()
            .beginControlFlow("return when (unit)").let {
                quantityType.units.fold(it) { builder, unit ->
                    //val name = SelectionTypesCodeGen.specialNameMappings[unit.name] ?: CaseUtils.toCamelCase(unit.name, true, '_')
                    //            builder.line("        ${type.typeName}Units.$name -> BigDecimal(\"${unit.factor}\")")
                    builder.addStatement("%T.${unit.name.enumConstant()} -> %T(%S)", unitClass, BigDecimal::class, unit.factor)
                }
            }
            .endControlFlow()
            .build()


}