package generators

import ComplexTypeDefinition
import EntityDefinition
import EntityFieldDefinition
import ListTypeDefinition
import MapTypeDefinition
import PrimitiveTypeDefinition
import QuantityTypeDefinition
import SchemaFetcher
import SelectableTypeDefinition
import com.squareup.kotlinpoet.*
import com.squareup.kotlinpoet.ParameterizedTypeName.Companion.parameterizedBy
import org.gradle.api.logging.Logger
import org.slf4j.LoggerFactory
import java.io.File

private const val ENTITY_SUFFIX = "Entity"
private val supressUnchecked = AnnotationSpec.builder(Suppress::class).addMember("\"UNCHECKED_CAST\"").build()

class EntityGenerator(
    private val target: File,
    private val packageUtil: PackageUtil,
    private val schemaFetcher: SchemaFetcher,
    private val logger: Logger
) {

    private val baseClass = ClassName(packageUtil.root, "BaseEntity")
    private val fieldDtoCn = ClassName(packageUtil.dtos, "FieldDto")

    fun entities() {
        logger.info("Handling ${schemaFetcher.entities.size} Entities")
        val manufacturingTranslations = schemaFetcher.staticTranslations.steps
        schemaFetcher.entities.forEach {
            handleEntity(it.value, manufacturingTranslations)
        }
        handleEntityFactory(schemaFetcher.entities)
    }

    private fun handleEntityFactory(entities: Map<String, EntityDefinition>) {
        val typeSpec = TypeSpec.objectBuilder("EntityFactory")
            .addFunction(FunSpec.builder("create")
                .returns(baseClass)
                .addParameter("manufacturingType", String::class.asClassName())
                .addCode(CodeBlock.builder()
                    .beginControlFlow("return when (manufacturingType)").let {
                        entities.values.fold(it) { builder, entity ->
                            builder.addStatement("%S -> %T()", entity.name, className(entity.name))
                        }
                    }
                    .addStatement("else -> error(%S + manufacturingType)", "Unexpected manufacturingType ")
                    .endControlFlow()
                    .build()
                )
                .build()
            )
        val fileSpec = FileSpec.builder(ClassName(packageUtil.root, "EntityFactory"))
            .addType(typeSpec.build())
            .build()

        fileSpec.writeTo(target)
    }

    private fun handleEntity(entity: EntityDefinition, manufacturingTranslations: Map<String, String>) {
        val cn = className(entity.name)
        val extends = entity.extends?.let {
            className(it)
        } ?: baseClass

        val typeSpec = TypeSpec.classBuilder(cn)
            .addModifiers(KModifier.OPEN)
            .superclass(extends)
            .addConstants(entity, manufacturingTranslations)
            .addInputs(entity)
            .addOverrides(entity, cn, extends)
            .addInputAccessors(entity)
            .addOverrideAccessors(entity)
            .build()

        val fileSpec = FileSpec.builder(cn)
            .addType(typeSpec)
            .addProperty(PropertySpec.builder("log", org.slf4j.Logger::class, KModifier.PRIVATE)
                .initializer("%T.getLogger(%T::class.java)", LoggerFactory::class.java, cn)
                .build())
            .build()

        fileSpec.writeTo(target)
    }

    private fun TypeSpec.Builder.addConstants(
        entity: EntityDefinition,
        manufacturingTranslations: Map<String, String>
    ): TypeSpec.Builder {
        val displayName = if (entity.type == "MANUFACTURING" || entity.type == "MANUFACTURING_STEP") {
            manufacturingTranslations[entity.name]
        } else {
            null
        }

        return addProperty(
                PropertySpec.builder("entityType", String::class, KModifier.OVERRIDE)
                    .initializer("\"${entity.type}\"")
                    .build()
            )
            .addProperty(
                PropertySpec.builder("entityName", String::class, KModifier.OVERRIDE)
                    .initializer("\"${entity.name}\"")
                    .build()
            )
            .addProperty(
                PropertySpec.builder(
                    "entityDisplayName",
                    String::class.asTypeName().copy(nullable = true),
                    KModifier.OVERRIDE
                )
                    .initializer(displayName?.let { "\"$it\"" } ?: "null")
                    .build()
            )
    }

    private fun TypeSpec.Builder.addInputs(entity: EntityDefinition): TypeSpec.Builder {
        return entity.inputFields.fold(this) { builder, field ->
            builder.addProperty(createProperty(field))
        }
    }

    private fun TypeSpec.Builder.addOverrides(entity: EntityDefinition, cn: ClassName, extends: ClassName): TypeSpec.Builder {
        val typespec = TypeSpec.classBuilder("Override")
            .addModifiers(KModifier.OPEN)
            .superclass(ClassName(packageUtil.entitiesPackage, "${extends}.Override"))
        return addType(
            entity.overrideFields.fold(typespec) { builder, field ->
                builder.addProperty(createProperty(field))
            }.build()
        ).let {
            if (entity.overrideFields.isNotEmpty()) {
                it.addProperty(
                    PropertySpec.builder("override", ClassName(packageUtil.entitiesPackage, "${cn}.Override"), KModifier.OVERRIDE)
                        .initializer("Override()")
                        .build()
                )
            } else {
                it
            }
        }
    }

    private fun createProperty(field: EntityFieldDefinition): PropertySpec {
        val typeName = when (val typeDefinition = field.typeDefinition) {
            is ListTypeDefinition, is MapTypeDefinition -> typeDefinition.typeName
            else -> typeDefinition.kotlinType
        }.let {
            ClassName(pkg(it), it).copy(nullable = !field.required)
        }
        return PropertySpec.builder(field.name, typeName).mutable(true)
            .let { ps ->
                field.defaultValue?.let { ps.initializer(it) }
                    ?: ps.initializer("null")
            }
            .build()
    }

    private fun TypeSpec.Builder.addInputAccessors(entity: EntityDefinition): TypeSpec.Builder =
        if (entity.inputFields.isNotEmpty()) {
            val getter = FunSpec.builder("inputs")
                .addModifiers(KModifier.OVERRIDE)
                .returns(List::class.asClassName().parameterizedBy(fieldDtoCn))
                .addCode("val fields = mutableListOf<FieldDto>()\n")
                .addCode(getterBlock(entity.inputFields, false))
                .addCode("return super.inputs() + fields")
                .build()
            addFunction(getter)
            val setter = FunSpec.builder("setInputs")
                .addAnnotation(supressUnchecked)
                .addModifiers(KModifier.OVERRIDE)
                .addParameter("fieldMap", Map::class.asClassName().parameterizedBy(String::class.asClassName(), fieldDtoCn))
                .addStatement("super.setInputs(fieldMap)")
                .addCode(setterBlock(entity.inputFields, false))
                .build()
            addFunction(setter)
        } else {
            this
        }

    private fun TypeSpec.Builder.addOverrideAccessors(entity: EntityDefinition): TypeSpec.Builder =
        if (entity.overrideFields.isNotEmpty()) {
            val getter = FunSpec.builder("overrides")
                .addModifiers(KModifier.OVERRIDE)
                .returns(List::class.asTypeName().parameterizedBy(fieldDtoCn))
                .addStatement("val fields = mutableListOf<FieldDto>()")
                .addCode(getterBlock(entity.overrideFields, true))
                .addStatement("return super.overrides() + fields")
                .build()
            addFunction(getter)
            val setter = FunSpec.builder("setOverrides")
                .addAnnotation(supressUnchecked)
                .addModifiers(KModifier.OVERRIDE)
                .addParameter("fieldMap", Map::class.asClassName().parameterizedBy(String::class.asClassName(), fieldDtoCn))
                .addStatement("super.setOverrides(fieldMap)")
                .addCode(setterBlock(entity.overrideFields, true))
                .build()
            addFunction(setter)
        } else {
            this
        }


    private fun getterBlock(fields: List<EntityFieldDefinition>, override: Boolean): String {
        val prefix = if (override) "override." else ""
        val sb = StringBuilder()
        fields.forEach { field ->
            val opt = if (field.required) "" else "!!"
            val value = when (field.typeDefinition) {
                is SelectableTypeDefinition -> "${field.name}$opt.selectableName"
                is QuantityTypeDefinition -> "${field.name}$opt.value"
                else -> field.name
            }
            val unit = if (field.typeDefinition is QuantityTypeDefinition) ", ${prefix}${field.name}$opt.unitName" else ""
            val condition = if (field.required) "" else "if (${prefix}${field.name} != null) "

            sb.appendLine("${condition}fields.add(FieldDto(\"${field.name}\", ${prefix}$value, \"${field.typeDefinition.typeName}\"$unit))")
        }
        return sb.toString()
    }

    private fun setterBlock(fields: List<EntityFieldDefinition>, override: Boolean): CodeBlock {
        val code = CodeBlock.builder()


        val prefix = if (override) "override." else ""
        val fieldDtoVar = "fieldDto"
        if (fields.isNotEmpty()) {
            code.addStatement("var $fieldDtoVar: %T?\n", fieldDtoCn)
        }
        for (field in fields) {
            val variable = "$prefix${field.name}"
            code.addStatement("$fieldDtoVar = fieldMap[\"${field.name}\"]")
            val fieldDtoValue = "$fieldDtoVar.value"
            code.beginControlFlow("if ($fieldDtoVar?.value != null && $fieldDtoVar.type != \"Null\")")

            when (val typeDefinition = field.typeDefinition) {
                is PrimitiveTypeDefinition -> code.addStatement("$variable = $fieldDtoValue!! as ${typeDefinition.kotlinType}")
                is ComplexTypeDefinition -> code.addStatement("$variable = $fieldDtoValue!!")
                is SelectableTypeDefinition -> {
                    val enumString = "($fieldDtoValue as? Boolean)?.toString() ?: $fieldDtoValue.toString()"
                    val suffix = if (typeDefinition.typeName == "Emission") {
                        // TODO!! why do we sometimes get Weight enum values for Emission!? investigate!
                        ".removeSuffix(\"Co2e\") + \"Co2e\""
                    } else {
                        ""
                    }
                    code.addStatement("val enumString = \"_\${$enumString.lowercase()}\".replace(\"_[a-z]\".toRegex()) { it.value.last().uppercase() }$suffix",
                        ClassName("org.apache.commons.text", "CaseUtils"))
                    code.addStatement("$variable = ${typeDefinition.typeName}.valueOf(enumString)")
                }
                is QuantityTypeDefinition -> {
                    val unitClassName = typeDefinition.typeName
                    val unitEnumClassName = "${unitClassName}Units"
                    val suffix = if (typeDefinition.typeName == "Emission") {
                        // TODO!! why do we sometimes get Weight enum values for Emission!? investigate!
                        """.removeSuffix("Co2e") + "Co2e""""
                    } else {
                        ""
                    }
                    code.addStatement("var unit = $unitClassName.defaultUnit")
                    code.beginControlFlow("if ($fieldDtoVar.unit != null)")
                    code.beginControlFlow("try")
                    code.addStatement("val unitString = %T.toCamelCase($fieldDtoVar.unit, true, '_')$suffix",
                        ClassName("org.apache.commons.text", "CaseUtils"))
                    code.addStatement("unit = %T.valueOf(unitString)", ClassName(packageUtil.unitsPackage, unitEnumClassName))
                    code.nextControlFlow("catch(e: Exception)")
                    code.addStatement("log.warn(%S + e.message)", "!!! [issue with units] Could not parse field '$variable' due to:")
                    code.endControlFlow()
                    code.endControlFlow()
                    code.addStatement("$variable = $unitClassName($fieldDtoValue!! as Double, unit)")
                }
                is ListTypeDefinition, is MapTypeDefinition ->
                    code.addStatement("$variable = $fieldDtoValue!! as ${typeDefinition.typeName}")
            }
            code.endControlFlow()
            code.addStatement("")
        }
        return code.build()
    }

    private fun className(s: String) = ClassName(packageUtil.entitiesPackage, "$s$ENTITY_SUFFIX")

    private fun pkg(tpe: String): String =
        schemaFetcher.typeDefinitionBases[tpe]?.let {
            when (it) {
                is SelectableTypeDefinition -> packageUtil.selectionTypesPackage
                is QuantityTypeDefinition -> packageUtil.quantitiesPackage
                is ListTypeDefinition, is MapTypeDefinition -> packageUtil.root
                else -> packageUtil.entitiesPackage
            }
        } ?: "kotlin"
}
