package generators

import SchemaFetcher
import com.squareup.kotlinpoet.*
import java.io.File
import enumConstant
import org.gradle.api.logging.Logger
import unitsClassName

class EnumGenerator(
    private val target: File,
    private val packageUtil: PackageUtil,
    private val schemaFetcher: SchemaFetcher,
    private val logger: Logger
) {

    fun selectionTypes() {
        logger.info("Handling ${schemaFetcher.selectables.size} Selectables")
        schemaFetcher.selectables.forEach { selection ->
            handleEnumGeneration(packageUtil.selectionTypesPackage, selection.name, selection.selectables, "selectableName")
        }
    }

    fun unitEnums() {
        logger.info("Handling ${schemaFetcher.units.size} Units")
        schemaFetcher.units.forEach { unit ->
            handleEnumGeneration(packageUtil.unitsPackage, unitsClassName(unit), unit.units.map { it.name }, "unitName")
        }
    }

    private fun handleEnumGeneration(pkg: String, className: String, values: List<String>, propertyName: String) {
        val cn = ClassName(pkg, className)
        val typeSpec = TypeSpec.enumBuilder(cn)
            .primaryConstructor(
                FunSpec.constructorBuilder().addParameter(propertyName, String::class).build()
            )
            .addProperty(PropertySpec.builder(propertyName, String::class).initializer(propertyName).build())
        values.forEach { enumValue ->
            typeSpec.addEnumConstant(enumValue.enumConstant(), TypeSpec.anonymousClassBuilder()
                .addSuperclassConstructorParameter("%S", enumValue).build())
        }

        val fileSpec = FileSpec.builder(cn)
            .addType(typeSpec.build())
            .build()
        fileSpec.writeTo(target)
    }

}