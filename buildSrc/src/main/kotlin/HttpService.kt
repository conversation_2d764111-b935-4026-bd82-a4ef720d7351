import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.core.JsonParseException
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.module.kotlin.*
import com.nu.bom.core.publicapi.dtos.SchemaEntitiesResponse
import com.nu.bom.core.publicapi.dtos.SchemaFieldsDefinition
import com.nu.bom.core.publicapi.dtos.TypeDefinition
import definition.StaticTranslationsDefinition
import org.gradle.api.logging.Logger
import java.io.BufferedReader
import java.io.InputStream
import java.net.URI
import java.net.URLEncoder
import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.net.http.HttpRequest.BodyPublishers
import java.net.http.HttpResponse
import java.net.http.HttpResponse.BodyHandlers
import java.nio.charset.StandardCharsets
import java.time.Duration
import java.util.concurrent.TimeUnit

class HttpService(baseUri: URI, auth: AuthProps, private val logger: Logger) {
    private val client =
        HttpClient.newBuilder()
            .followRedirects(HttpClient.Redirect.NORMAL)
            .connectTimeout(Duration.ofSeconds(600))
            .build()

    private val authRequestBuilder =
        HttpRequest.newBuilder(URI(auth.url))
            .timeout(Duration.ofSeconds(600))

    private val typesUri = baseUri.resolve("/v1/types")
    private val typesRequestBuilder =
        HttpRequest.newBuilder(typesUri)
            .timeout(Duration.ofSeconds(600))

    private val entityUri = baseUri.resolve("/v1/entities")
    private val entityRequestBuilder =
        HttpRequest.newBuilder(entityUri)
            .timeout(Duration.ofSeconds(600))

    private val entityFieldsUri = baseUri.resolve("/v1/fields")
    private val entityFieldsRequestBuilder =
        HttpRequest.newBuilder(entityFieldsUri)
            .timeout(Duration.ofSeconds(600))

    private val staticTranslationsUri = baseUri.resolve("static/locales/en.json")
    private val staticTranslationsRequestBuilder =
        HttpRequest.newBuilder(staticTranslationsUri)
            .timeout(Duration.ofSeconds(600))

    private val mapper =
        jacksonObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)

    private val token: String by lazy {
        logger.info("  Requesting token from ${auth.url}")
        val params =
            listOf(
                "client_id" to "qa-client",
                "grant_type" to "password",
                "client_secret" to auth.secret,
                "scope" to "openid",
                "username" to auth.user,
                "password" to auth.password,
            ).joinToString("&") { "${it.first}=${URLEncoder.encode(it.second, StandardCharsets.UTF_8)}" }
        val request =
            authRequestBuilder
                .header("Content-Type", "application/x-www-form-urlencoded")
                .POST(BodyPublishers.ofString(params))
                .build()
        val response = makeSpinUpScreenSaveClientCall(request)
        logger.info("    Got ${response.statusCode()} token response")

        @JsonInclude(JsonInclude.Include.NON_NULL)
        data class TokenResponse(val access_token: String)
        logBodyInCaseOfException<TokenResponse>(response.body(), 4).access_token
    }

    fun getTypeDefinitionList(): List<TypeDefinition> {
        logger.info("Requesting $typesUri to get the types")
        val request =
            typesRequestBuilder
                .header("Authorization", "Bearer $token")
                .build()
        val response = makeSpinUpScreenSaveClientCall(request)
        logger.info("  Got ${response.statusCode()} as response")
        return logBodyInCaseOfException<List<TypeDefinition>>(response.body())
    }

    fun getEntityList(): List<SchemaEntitiesResponse> {
        logger.info("Requesting $entityUri to get the entities")
        val request =
            entityRequestBuilder
                .header("Authorization", "Bearer $token")
                .build()
        val response = makeSpinUpScreenSaveClientCall(request)
        logger.info("  Got ${response.statusCode()} as response")
        return logBodyInCaseOfException<List<SchemaEntitiesResponse>>(response.body())
    }

    fun getEntityFieldsList(): List<SchemaFieldsDefinition> {
        logger.info("Requesting $entityFieldsUri to get the fields")
        val request =
            entityFieldsRequestBuilder
                .header("Authorization", "Bearer $token")
                .build()
        val response = makeSpinUpScreenSaveClientCall(request)
        logger.info("  Got ${response.statusCode()} as response")
        return logBodyInCaseOfException<List<SchemaFieldsDefinition>>(response.body())
    }

    fun getStaticTranslations(): StaticTranslationsDefinition {
        logger.info("Requesting $staticTranslationsUri to get the static asset translations")
        val request =
            staticTranslationsRequestBuilder
                .header("Authorization", "Bearer $token")
                .build()
        val response = makeSpinUpScreenSaveClientCall(request)
        logger.info("  Got ${response.statusCode()} as response")
        return logBodyInCaseOfException<StaticTranslationsDefinition>(response.body())
    }

    private fun makeSpinUpScreenSaveClientCall(
        request: HttpRequest,
        maximalNumberOfRetries: Int = 100, // 15 min equals 90 retries
    ): HttpResponse<InputStream> {
        repeat(maximalNumberOfRetries) {
            val response = client.send(request, BodyHandlers.ofInputStream())
            when (response.statusCode()) {
                in 200..299 -> return response
                308 -> TimeUnit.SECONDS.sleep(10)
                else -> error("Posting request $request lead to ${response.statusCode()}")
            }
        }
        error("Maximum number of retries exceeded. NBK is unresponsive!")
    }

    private inline fun <reified T> logBodyInCaseOfException(inputStream: InputStream, indent: Int = 2): T =
        BufferedReader(inputStream.reader(Charsets.UTF_8)).use { reader ->
            val body = reader.readText()
            try {
                mapper.readValue<T>(body).also {
                    logger.info("${" ".repeat(indent)}... and mapped in jackson")
                }
            } catch (e: JsonParseException) {
                logger.error("Error in deserializing response body to {}: '{}'", T::class.qualifiedName, body)
                throw e
            }
        }
}
