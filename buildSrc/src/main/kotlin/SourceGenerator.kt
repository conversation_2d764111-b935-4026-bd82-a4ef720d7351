import org.gradle.api.DefaultTask
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.Optional
import org.gradle.api.tasks.OutputDirectory
import org.gradle.api.tasks.TaskAction
import java.io.File

open class SourceGenerator : DefaultTask() {

    @Input
    lateinit var id: String

    @Input
    @Optional
    var source: String? = null

    @Input
    @Optional
    var authenticationUrl: String? = null

    @Input
    @Optional
    var target: String? = null

    @Input
    @Optional
    var clientSecret: String? = null

    @Input
    @Optional
    var clientUser: String? = null

    @Input
    @Optional
    var clientPassword: String? = null

    @OutputDirectory
    @Optional
    var targetDirectory: File? = null

    @TaskAction
    fun generate() {
        source?.let { s ->
            authenticationUrl?.let { a ->
                target?.let { t ->
                    clientUser?.let { cu ->
                        clientSecret?.let { cs ->
                            clientPassword?.let { cp ->
                                val pg = ProjectGenerator(s, AuthProps(a, cu, cs, cp), t, logger)
                                pg.generateSource()
                            }
                        }
                    }
                }
            }
        }
        targetDirectory = File(target)
    }

}

data class AuthProps(val url: String, val user: String, val secret: String, val password: String)
