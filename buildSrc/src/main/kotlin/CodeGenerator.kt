import generators.EntityGenerator
import generators.PackageUtil
import generators.QuantityGenerator
import generators.EnumGenerator
import org.gradle.api.logging.Logger
import java.io.File

private val pattern = "_[a-z]".toRegex()

class CodeGenerator(target: File, packageUtil: PackageUtil, schemaFetcher: <PERSON><PERSON><PERSON><PERSON><PERSON>cher, logger: Logger) {
    private val enumGenerator = EnumGenerator(target, packageUtil, schemaFetcher, logger)
    private val quantityGenerator = QuantityGenerator(target, packageUtil, schemaFetcher, logger)
    private val entityGenerator = EntityGenerator(target, packageUtil, schemaFetcher, logger)

    fun generate() {
        enumGenerator.selectionTypes()
        enumGenerator.unitEnums()
        quantityGenerator.quantityTypes()
        entityGenerator.entities()
    }

}

fun String.enumConstant(): String {
    return "_${this.lowercase()}".replace(pattern) { it.value.last().uppercase() }
}

fun unitsClassName(q: QuantityType) = "${q.name}Units"