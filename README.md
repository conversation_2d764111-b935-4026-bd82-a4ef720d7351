<a href="#"><img src="https://git.tset.cloud/qa/nu-api-test/badges/master/pipeline.svg"></img></a>

# nu-api-test

API functional test automation repository

## Description

API testing is a software testing type that validates Application Programming Interfaces (APIs).
The purpose of API Testing is to check the functionality, reliability, performance, and security
of the programming interfaces. In API Testing, instead of using standard user inputs(keyboard) and outputs,
you use software to send calls to the API, get output, and note down the system's response. It mainly concentrates
on the business logic layer of the software architecture

The API test automation framework is responsible for providing reusable test automation components which are used in
automatically executable test cases. The aim of this document is to give an overview of the API test automation
framework, the used technologies and approaches.

## Structure

The project follows the standard Gradle setup and uses its recommended folder structure.
`main` folder contains the framework elements, endpoint actions whilst all the tests are placed in
`test` package.

The framework consists several abstract, base test classes and a test listener.
The `BaseTest` class is responsible for the common test class functionality such as properties file reading,
restAssured setup and failure mechanisms.

The `endpointActions` package contains the request creation classes.

The `model` package contains the DTO classes for the data transmissions.

The `tests` folder contains the implemented tests as extensions of the base test.

Next to the java packages the `resources` folder is placed. It contains the environment and user properties,
logback and allure settings as well as the TestNG suite files.

## Environment setup

In order to have the freedom of choice between different environments, users and test suits the following environment
variables should be set before execution:

| Variable name  | Description                                                                                                                                     | Default value | Mandatory |
| -------------- | ----------------------------------------------------------------------------------------------------------------------------------------------- | ------------- | --------- |
| ENV            | The name or the URL of the environment.<br/>Eg.: testing, develop, <br/>https://my-branch.cost.feature.tset.cloud/, <br/>http://localhost:8082/ | —             | YES       |
| TESTTYPE       | The test suite which tests are going to be executed.                                                                                            | test          | NO        |
| THREAD_NUM     | The number of threads to use when running tests in parallel.                                                                                    | 4             | NO        |
| NEXUS_USER     | Get it from 1Password (vault: Dev stuff)                                                                                                        | —             | YES       |
| NEXUS_PASSWORD | Get it from 1Password (vault: Dev stuff)                                                                                                        | —             | YES       |
| USER           | The user which executes the tests e.g.: username :shrug:                                                                                        | —             | YES       |

In addition, the following environment variables which are used for authentication must also be set. They can be gathered
from Keycloak manually or automatically by use of a script: `scripts/bash/write.env.sh`, or `scripts/powershell/write.env.ps1`
for Windows users. See the [Slite documentation](https://tset.slite.com/app/docs/9cOZD6eLk6wS_u/Run-tests-in-local-with-secrets)
for more details.

| Variable name          | Description                                                                   |
|------------------------|-------------------------------------------------------------------------------|
| TSET_USERNAME          | The user which executes the tests, e.g. `antman`                              |
| TSET_PASSWORD          | The password for the selected user in                                         |
| KEYCLOAK_CLIENT_USER   | The Keycloak client user for fetching the API token, e.g. `client.qa.develop` |
| KEYCLOAK_CLIENT_SECRET | The secret for the selected user                                              |

## Build

### Requirements

Java SDK 17 or newer

### Build from command line

Navigate to root directory and execute the following command:

```bash
gradlew clean build -x test
```

## Test Execution

### From command line

For whole regression job use the following command:

```bash
gradlew test -i
```

If you want to execute only smoke test set, execute this command:

```bash
gradlew smokeTest -i
```

### From the IDE

The following instructions are generalized in order to keep it compatible with different IDE types
and versions. Some steps might slightly different from the actual executions in real IDEs.

1. Import the project as gradle or simple java project.
1. Install lombok plugin in your IDE and enable annotation processing
1. Build the project with gradle plugin.
1. Set the environment variables either in system level or in IDE at configuration defaults
1. Set IDE as test executor instead of external build tool
1. Navigate to the tests folder and choose a desired test class
1. Right-click on the class or on a test method and choose execute test

## Reporting

### Allure

After test execution use the following command in order to generate [Allure] report:

```bash
gradlew allureReport
```

Allure results will appear in build/allure-results folder.

Fire up a server and automatically open it in a web browser:

```bash
gradlew allureServe
```

### Coverage

[PitayaReport] is created in root folder as static HTML document. It contains the endpoint coverage report.

[Allure]: http://allure.qatools.ru/
[PitayaReport]: https://osvalda.github.io/Pitaya/
