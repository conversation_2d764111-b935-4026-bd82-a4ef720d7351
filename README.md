# TSET-VUE-FRONTEND

## Getting started

It is recommended to develop on a unix based system. If you are running windows, it is advised to install [wsl](https://learn.microsoft.com/en-us/windows/wsl/install).

### On unix based systems

Install [devbox](https://www.jetpack.io/devbox/docs/installing_devbox/) and start a development shell with

```bash
devbox shell
devbox run install
devbox run dev
```

### On windows

Install the following tools:

- [node](https://nodejs.org/en)
- [yarn](https://yarnpkg.com/)

## npm packages

In order to be able to install the dependencies (npm packages) you need to have access to our own npm registry.

To gain access download the file `develop .npmrc` from 1Password and then copy it into your home directory/project root as `.npmrc`.

## Debugging in vs code

1. run the dev server with `devbox run dev` in some console

2. click on the debugger icon in vs code

3. click “Launch Chrome against localhost”

enjoy breakpoints

## Running related stacks locally

### Frontend

change into the normal root directory. Simply run:

```shell
docker compose -f docker-compose-dev.yaml up
```

`docker compose` or `docker-compose` (older) depends on your docker version!

### Backend

It is possible to run the whole backend locally on docker containers. Simply run:

```bash
# You might need to start the docker service separately first.
docker-compose -f docker-compose/backend-compose.yml up -d # the -d will launch the containers in daemon mode.

# Depending on the docker version you have installed, compose could be shipped directly.
docker compose -f docker-compose/backend-compose.yml up -d
```

In order to use it, you need access to all the backend repositories used or docker will not be able to pull the images. For example you need access to `geom/gattierungsrechner`, `geom/tset-del` and `geom/tset-turning`. `data/nucost-models` is also required.

Most of the images (git repositories) are feature aware. The available images can be found [here for `nu-bom-kotlin`/`backend`](https://git.tset.cloud/backend/nu-bom-kotlin/container_registry/4), [here for `nu-bomrads`](https://git.tset.cloud/backend/nu-bomrads/container_registry/65) and [here for `static-assets`](https://git.tset.cloud/frontend/static-assets/container_registry/99), etc. You can access the repository you are interested in and look up in the `Package Registry/Container Registry` page to see available images

(!) It is important to keep in mind that in case the Git branch you want to mount changed since the first time you pulled it automatically when launching the containers, you will have to pull it yourself: `docker pull docker.tset.cloud/backend/nu-bomrads:feature-my-feature`.

You can specify feature branches to be used for each service by using environment variables which will be used as docker tag. In case the variables do not exist or are empty, docker will fall back to `latest`.

An easy way to do this would be to setup a dedicated `.env.docker.local` file and declare the variables with feature branches values that you want to use for each service like so:

```bash
# .env.docker.local
# the pattern is FEATURE_REPOSITORY_NAME for the variable name. So for `nu-bomrads` you need to use `FEATURE_NU_BOMRADS
# the pattern is feature-the-feature-branch-name for the value. So `feature/my-feature` needs to be declared as `feature-my-feature`. You will find this information in the repository container registry.
FEATURE_NU_BOM_KOTLIN=feature-my-feature
# You can reuse an already declared variable like so
FEATURE_STATIC_ASSETS=${FEATURE_NU_BOM_KOTLIN}
# But you can use a different image available
FEATURE_TSET_MILLING=feature-my-other-feature
```

Then you may use this file when you up your docker compose:

```bash
# at the root of `nuxt-cost`
docker compose -f docker-compose/backend-compose.yml --env-file .env.docker.local up -d
```

When you want to stop your containers you can just do:

```bash
# at the root of `nuxt-cost`
docker compose -f docker-compose/backend-compose.yml stop
```

### Populating backend static data

Some data for the backend need to be populated as it is static (not User controlled). This is taken care of by a dedicated `profile` of `nu-bom-kotlin`: `data-deployment`. In order to populate your container properly, you need to run this profile in addition.

You can do so by defining a `PROFILES_NU_BOM_KOTLIN` environment variable (see above) like so:

```
# .env.docker.local
PROFILES_NU_BOM_KOTLIN=local, bomrad, data-deployment
```

If this variable does not exist or is empty, the docker compose file will default to the base profiles needed to run `nu-bom-kotlin`: `local` and `bomrad`

(!) Please note that this `data-deployment` profile ends the process, so you need to stop your container and rerun it with the default profiles only, or at least without `data-deployment`.

(!) Please note that you might need to run the profile afterwards in case some static data are needed for new features.

(!) This is feature container dependant, so if you need data on a feature container, you also need to use the `FEATURE_NU_BOM_KOTLIN` in conjuction (see above section).

You can find more about available profiles [here](https://tsetplatform.atlassian.net/wiki/spaces/NU/pages/2920906753/Application+Profiles)

## ENV .env.local

Please make sure to use a `.env.local` file for your local settings.

In order to run the frontend locally you will need to add a variable `tokenTsetCloud`! See [how to issue a new dev-token](https://tset.slite.com/app/docs/stgUAZhcd8YK-V/How-to-issue-new-dev-token).

The access token must be prefixed with `Bearer `, e.g.:

```
tokenTsetCloud=Bearer <access token>
```

Mostly you want to set this up in order to run against a local tech stack or target a feature branch.

### Feature Deployments

Most of the time it should be good enough to just set `baseUrl`.

There are different ENV that can be set, e.g. `apiBaseUrl`, `nuLedgeUrl`, `masterdataUrl` and `staticBaseUrl`.

You can setup the urls in the `.env.local` the following way:

- if unset
  - `baseUrl` will be used instead
- if set to a url
  - that url is used
- if set to `USE_GIT_BRANCH_URL` and branch name starts with `feature/`
  - branch-specific url will be generated
    (e.g. https://cost-1234-whatever.cost.feature.tset.cloud)
    to make it working you need:
    - push the branch to the upstream
    - wait until it's deployed

Same `USE_GIT_BRANCH_URL` can be used for `baseUrl`

Example `.env.local`

```shell
baseUrl="https://example.cost.feature.tset.cloud"

apiBaseUrl="https://example.cost.feature.tset.cloud"
nuLedgeUrl="https://example.cost.feature.tset.cloud"
masterdataUrl="https://example.cost.feature.tset.cloud"
staticBaseUrl="https://example.cost.feature.tset.cloud"

tokenTsetCloud="Bearer ..."
```

### Local Deployments

Remember to override the ENV for `apiBaseUrl` with `http://localhost:8082` to actually run against the local setup.

To connect to local nu-ledge overwrite the ENV `nuLedgeUrl` with `http://localhost:8102`

To connect to local masterdata overwrite the ENV `masterdataUrl` with `http://localhost:8104`

## Deployment targets

All products/apps are deployed onto the development cluster and follow the same convention:

- for `master` branch:
  - <https://cost.develop.tset.cloud>
  - <https://design.develop.tset.cloud>
- for `feature` branches (feature/example):
  - <https://example.cost.feature.tset.cloud>
  - <https://example.design.feature.tset.cloud>

## Internal file imports

Around the application, imports of different files are required. For this purpose 2 notations are used:

- If an import starts with `"@"`, then the import is pointing towards `"./src/*"`.
- If an import starts with `"@@"`, then the import is pointing toward `"./*"`

## Caching mode

The purpose of this mode is to make local development faster and less dependant on
network quality or bomrads unstable responses. FOR LOCAL DEV ONLY

If you run the app locally with `yarn dev:cached` it will run in "caching" mode.
All requests to the backend will be saved to cache and served from cache first
with stale-while-revalidate strategy.

If you want to work completely offline, you have to click
all the api-requesting elements you need (calculations / tabs / etc) to cache the data.
After that you can run the app without the internet.
(While you don't need to POST/PUT/DELETE something to backend)

### Possible issues

The app can show stale data and have widget updates issues.
To get the latest version of backend data refresh the page 1-2 times.

If caching service worker is not killed after running app in
normal mode, go to devTools -> application -> serviceWorkers and
press `unregister` on `dev-sw.js`

## Enabling Vue devtools on prod

If you want to use vue devtools on production build (e.g. on your git feature deployment)
follow the steps:

1. open chrome devtools js console
2. type `enableVueDevtools()` , enter
3. reopen chrome devtools
4. "Vue" tab should now appear

## Masterdata OpenAPI Client

The masterdata package uses a generated client using OpenAPI.

Use the script in the corresponding [package.json](./packages/masterdata/api/package.json) to re-generate the client.

```shell
cd packages/masterdata/api
yarn generate:client
```

The frontend needs to be running locally on localhost:8083 for the script to work.

## Report architecture violations

We have integrated [dependency-cruiser](https://github.com/sverweij/dependency-cruiser) in order to analyze our repository. You can simply run `devbox run test:arch` for quick feedback, or `devbox run report:arch` for an interactive report (`dependency-report.html`).

The architecture violations are checked in the CI. We have whitelisted the known violations as a starting point within the file `.dependency-cruiser-known-violations.json`. You can update the known violation with the `package.json` script `upate:arch`.

Please refer to the original documentation for more advanced usages.

## Testing and Vitest VS Code Extension

For the usage of the **VS Code Vitest Extension**, is necessary to add a `PATH` to the execution for in `Vitest/Settings/Node Executable`

It needs to come from the `devbox` installation path:
`/{PROJECT_LOCATION}/.devbox/nix/profile/default/bin/node`
