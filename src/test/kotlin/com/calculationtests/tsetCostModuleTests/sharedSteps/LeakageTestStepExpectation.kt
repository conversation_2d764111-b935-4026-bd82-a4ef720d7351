package com.calculationtests.tsetCostModuleTests.sharedSteps

import com.calculationtests.testCostModules.expectations.CycleTimeStepGroupFromTemplateExpectation
import com.calculationtests.testCostModules.expectations.StepExpectation
import com.tset.clientsdk.schema.entities.LeakageTestingManualCycleTimeStepEntity
import com.tset.clientsdk.schema.entities.LoadingLeakageTestCycleTimeStepEntity
import com.tset.clientsdk.schema.entities.ManufacturingStepEntity
import com.tset.clientsdk.schema.entities.ManufacturingStepLeakageTestEntity
import com.tset.clientsdk.schema.entities.ManufacturingStepSecondLeakageTestEntity
import com.tset.clientsdk.schema.entities.UnloadingLekageTestCycleTimeStepEntity

sealed class LeakageTestStepExpectationBase(
    entity: ManufacturingStepEntity,
    name: String? = null,
    condition: (() -> Boolean)? = null,
) : StepExpectation(entity, name, condition = condition) {
    init {
        withCycleTimeGroup(
            CycleTimeStepGroupFromTemplateExpectation()
                .withCycleTimeStep(LoadingLeakageTestCycleTimeStepEntity(), name = "Loading")
                .withCycleTimeStep(LeakageTestingManualCycleTimeStepEntity(), name = "Sealing, compressing air, calming down, measuring")
                .withCycleTimeStep(UnloadingLekageTestCycleTimeStepEntity(), name = "Unloading"),
        )
    }
}

class LeakageTestStepExpectation(
    name: String? = null,
    condition: (() -> Boolean)? = null,
) : LeakageTestStepExpectationBase(ManufacturingStepLeakageTestEntity(), name, condition = condition)

class SecondLeakageTestStepExpectation(
    name: String? = null,
    condition: (() -> Boolean)? = null,
) : LeakageTestStepExpectationBase(ManufacturingStepSecondLeakageTestEntity(), name, condition = condition)
