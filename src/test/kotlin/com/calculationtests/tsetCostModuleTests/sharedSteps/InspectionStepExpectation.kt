package com.calculationtests.tsetCostModuleTests.sharedSteps

import com.calculationtests.testCostModules.expectations.CycleTimeStepGroupFromTemplateExpectation
import com.calculationtests.testCostModules.expectations.StepExpectation
import com.tset.clientsdk.schema.entities.InspectionCycleTimeStepEntity
import com.tset.clientsdk.schema.entities.ManufacturingStepInspectionEntity

class InspectionStepExpectation(
    name: String? = null,
    condition: (() -> Boolean)? = null,
) : StepExpectation(ManufacturingStepInspectionEntity(), name, condition = condition) {
    init {
        withCycleTimeGroup(
            CycleTimeStepGroupFromTemplateExpectation()
                .withCycleTimeStep(InspectionCycleTimeStepEntity(), name = "Grinding work center with suction"),
        )
    }
}
