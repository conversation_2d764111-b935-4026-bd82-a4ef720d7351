package com.calculationtests.tsetCostModuleTests.modularization.barTurning

import com.calculationtests.testCostModules.expectations.InputParameter
import com.calculationtests.testCostModules.expectations.OverrideParameter
import com.calculationtests.testCostModules.expectations.Parameter
import com.calculationtests.tsetCostModuleTests.*
import com.tset.clientsdk.schema.entities.ManufacturingBartEntity
import com.tset.clientsdk.schema.entities.ManufacturingSandCastingEntity
import com.tset.clientsdk.schema.quantities.Density
import com.tset.clientsdk.schema.quantities.Emission
import com.tset.clientsdk.schema.quantities.Length
import com.tset.clientsdk.schema.units.DensityUnits
import com.tset.clientsdk.schema.units.EmissionUnits
import com.tset.clientsdk.schema.units.LengthUnits

class MaterialBarTurningModularizationTests : BaseModularizationTests<MaterialBarTurning>(
    ::MaterialBarTurning,
) {
    override val technologyModelEntity = ManufacturingBartEntity::class.java
    override val shapeId = null

    override fun createEntityParameters(): List<Parameter<MaterialBarTurning, out Any?, out Any?>> {
        return listOf(
            // Inputs
            InputParameter(MaterialBarTurning::displayDesignation, genericExpectationEntityName),
            InputParameter(MaterialBarTurning::technologyModel, technologyModel),
            InputParameter(MaterialBarTurning::density, Density(0.6, DensityUnits.KilogramPerCm)),
            InputParameter(MaterialBarTurning::barLength, Length(6.0, LengthUnits.Meter)),
            OverrideParameter(MaterialBarTurningOverride::barDiameter, Length(0.8, LengthUnits.Meter)),
            OverrideParameter(MaterialBarTurningOverride::blankLength, Length(0.78, LengthUnits.Meter)),
            InputParameter(MaterialBarTurning::partingOffWidth, Length(0.005, LengthUnits.Meter)),
            InputParameter(MaterialBarTurning::cO2PerUnit, Emission(1.0, EmissionUnits.KilogramCo2e))
            )
    }
}
