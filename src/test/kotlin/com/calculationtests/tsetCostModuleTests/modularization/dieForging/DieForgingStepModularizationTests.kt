package com.calculationtests.tsetCostModuleTests.modularization.dieForging

import com.calculationtests.testCostModules.expectations.InputParameter
import com.calculationtests.testCostModules.expectations.Parameter
import com.calculationtests.tsetCostModuleTests.BaseModularizationTests
import com.calculationtests.tsetCostModuleTests.StepDieForging
import com.tset.clientsdk.schema.entities.ManufacturingDieForgingEntity
import com.tset.clientsdk.schema.selections.CastingAlloyMaterialGroup
import com.tset.clientsdk.schema.selections.StepSubTypeMaterial
import org.springframework.boot.test.context.SpringBootTest

@SpringBootTest
class DieForgingStepModularizationTests :
    BaseModularizationTests<StepDieForging>(
        ::StepDieForging,
    ) {
    override val technologyModelEntity = ManufacturingDieForgingEntity::class.java
    override val shapeId = "S_613"

    override fun createEntityParameters(): List<Parameter<StepDieForging, out Any?, out Any?>> =
        listOf(
            // Inputs
            InputParameter(StepDieForging::displayDesignation, genericExpectationEntityName),
            InputParameter(StepDieForging::technologyModel, technologyModel),
            // Self
            InputParameter(StepDieForging::weightBeforeScrapPerPart, 0.1),
            InputParameter(StepDieForging::materialGroup, CastingAlloyMaterialGroup.P1),
        )
}
