package com.calculationtests.tsetCostModuleTests.generated.sintering

import com.calculationtests.publicApi.dtos.DumpDetail
import com.calculationtests.testCostModules.TestCase
import com.calculationtests.testCostModules.expectations.CostModuleExpectation
import com.calculationtests.testCostModules.expectations.InputParameter
import com.calculationtests.testCostModules.expectations.ProcessedMaterialExpectation
import com.calculationtests.testCostModules.expectations.StepExpectation
import com.calculationtests.testCostModules.expectations.withSteps
import com.calculationtests.tsetCostModuleTests.BaseCostModuleTests
import com.calculationtests.tsetCostModuleTests.DefaultParameters
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.DynamicTest
import org.junit.jupiter.api.TestFactory
import org.springframework.boot.test.context.SpringBootTest
import com.tset.clientsdk.schema.entities.ManufacturingSinteringEntity
import com.tset.clientsdk.schema.entities.ManufacturingStepCalibratingEntity
import com.tset.clientsdk.schema.entities.ManufacturingStepCleaningEntity
import com.tset.clientsdk.schema.entities.ManufacturingStepPressGreenEntity
import com.tset.clientsdk.schema.entities.ManufacturingStepSinteringEntity
import com.tset.clientsdk.schema.entities.SinteredMaterialEntity
import com.tset.clientsdk.schema.quantities.inUnit
import com.tset.clientsdk.schema.selections.Bool
import com.tset.clientsdk.schema.selections.SelectableBoolean
import com.tset.clientsdk.schema.selections.StepSubTypeCleanness
import com.tset.clientsdk.schema.units.WeightUnits

@SpringBootTest
@Disabled
class sinteringTests : BaseCostModuleTests<ManufacturingSinteringEntity>(
    ::ManufacturingSinteringEntity,
) {
    @TestFactory
    fun ManufacturingSinteringEntityTests(): List<DynamicTest> {
        val testCases = createTestCases()
        val defaultCostModuleParameters = createDefaultParameters()
        return createDynamicTests(
            testCases = testCases,
            costModuleDefaultParameters = defaultCostModuleParameters,
            manufacturingDefaultParameters = DefaultParameters.getManufacturingDefaultParameters(),
            dumpDetail = DumpDetail.AllChildren,
        ) { costModule -> createTestExpectation(costModule) }
    }

    override fun createTestCases() = listOf(
        TestCase(),
        TestCase(
            InputParameter(ManufacturingSinteringEntity::calibrating, TODO()),
        ),
        TestCase(
            InputParameter(ManufacturingSinteringEntity::rapidCooling, TODO()),
        ),
        TestCase(
            InputParameter(ManufacturingSinteringEntity::cleaningNeeded, SelectableBoolean.False),
        ),
        TestCase(
            InputParameter(ManufacturingSinteringEntity::cleanness, StepSubTypeCleanness.Normal),
        ),
        TestCase(
            InputParameter(ManufacturingSinteringEntity::materialName, ""),
        ),
        TestCase(
            InputParameter(ManufacturingSinteringEntity::netWeightPerPartForWizard, 0.0 inUnit WeightUnits.Kilogram),
        ),
        TestCase(
            InputParameter(ManufacturingSinteringEntity::shapeId, ""),
        ),
    )

    override fun createDefaultParameters() = listOf(
        // Mandatory parameters:
        InputParameter(ManufacturingSinteringEntity::calibrating, TODO()),
        InputParameter(ManufacturingSinteringEntity::rapidCooling, TODO()),
        InputParameter(ManufacturingSinteringEntity::cleaningNeeded, SelectableBoolean.False),
        InputParameter(ManufacturingSinteringEntity::cleanness, StepSubTypeCleanness.Normal),
        InputParameter(ManufacturingSinteringEntity::materialName, ""),
        InputParameter(ManufacturingSinteringEntity::netWeightPerPartForWizard, 0.0 inUnit WeightUnits.Kilogram),
        InputParameter(ManufacturingSinteringEntity::shapeId, ""),

        // Optional parameters:
        /*
        */
    )

    override fun createTestExpectation(costModule: ManufacturingSinteringEntity): CostModuleExpectation {
        // TODO("Use the step clases in the expectations and remove this list")
        val modelStepClasses = listOf(
            ManufacturingStepPressGreenEntity(),
            ManufacturingStepSinteringEntity(),
            ManufacturingStepCalibratingEntity(),
        )

        return CostModuleExpectation(
            ManufacturingSinteringEntity(),
            processedMaterial = ProcessedMaterialExpectation(
                SinteredMaterialEntity(),
                name = "SinteredMaterial",
            )
                .withSteps(
                    StepExpectation(ManufacturingStepPressGreenEntity(), "ManufacturingStepPressGreen"),
                )
                .withSteps(
                    StepExpectation(ManufacturingStepSinteringEntity(), "ManufacturingStepSintering"),
                )
                .withSteps(
                    StepExpectation(ManufacturingStepCalibratingEntity(), "ManufacturingStepCalibrating") { costModule.calibrating == Bool.True },
                )
                .withSteps(
                    StepExpectation(ManufacturingStepCleaningEntity(), "ManufacturingStepCleaning") { costModule.cleaningNeeded == SelectableBoolean.True },
                ),
        )
    }
}
