package com.calculationtests.tsetCostModuleTests.costModules.sandCasting

import com.calculationtests.testCostModules.expectations.CycleTimeStepGroupExpectation
import com.calculationtests.testCostModules.expectations.StepExpectation
import com.tset.clientsdk.schema.entities.*
import com.tset.clientsdk.schema.selections.Bool
import com.tset.clientsdk.schema.selections.SelectableBoolean
import com.tset.clientsdk.schema.selections.StepSubTypeManualSandFettling
import com.tset.clientsdk.schema.selections.StepSubTypeSandFettling

class SandCastingFettlingStepExpectation(
    entity: ManufacturingStepEntity,
    burrFree: SelectableBoolean,
    sandFettlingType: StepSubTypeSandFettling,
    manualSandFettlingType: StepSubTypeManualSandFettling,
    name: String? = null,
    condition: (() -> Boolean)? = null
) : StepExpectation(entity, name, condition = condition) {
    init {
        when (sandFettlingType) {
            StepSubTypeSandFettling.Automated -> when (burrFree) {
                SelectableBoolean.False -> {
                    withCycleTimeGroup(
                        CycleTimeStepGroupExpectation(
                            CycleTimeStepGroupFromTemplateEntity(),
                            "Fettling automated"
                        )
                            .withCycleTimeStep(FettlingGrabbingCycleTimeStepEntity(), name = "Grabbing")
                            .withCycleTimeStep(FettlingRoughCycleTimeStepEntity(), name = "Fettling rough")
                            .withCycleTimeStep(FettlingReleasingCycleTimeStepEntity(), name = "Releasing")
                    )
                }
                SelectableBoolean.True ->
                    withCycleTimeGroup(
                        CycleTimeStepGroupExpectation(
                            CycleTimeStepGroupFromTemplateEntity(),
                            "Fettling automated"
                        )
                            .withCycleTimeStep(FettlingGrabbingCycleTimeStepEntity(), name = "Grabbing")
                            .withCycleTimeStep(FettlingCycleTimeStepEntity(), name = "Fettling fine")
                            .withCycleTimeStep(FettlingReleasingCycleTimeStepEntity(), name = "Releasing")
                    )
            }
            StepSubTypeSandFettling.Manual -> when (manualSandFettlingType) {
                StepSubTypeManualSandFettling.SprueFeederOnly -> {
                    withCycleTimeGroup(
                        CycleTimeStepGroupExpectation(
                            ManualSandFettlingCycleTimeStepGroupEntity(),
                            "Fettling manual"
                        )
                            .withCycleTimeStep(FettlingManualGrabbingCycleTimeStepEntity(), name = "Part loading")
                            .withCycleTimeStep(SprueAndFeederFettlingCycleTimeStepEntity(), name = "Sprue and feeder fettling time")
                            .withCycleTimeStep(FettlingManualGrabbingCycleTimeStepEntity(), name = "Part releasing")
                    )
                }
                StepSubTypeManualSandFettling.BurrRough -> {
                    withCycleTimeGroup(
                        CycleTimeStepGroupExpectation(
                            ManualSandFettlingCycleTimeStepGroupEntity(),
                            "Fettling manual"
                        )
                            .withCycleTimeStep(FettlingManualGrabbingCycleTimeStepEntity(), name = "Part loading")
                            .withCycleTimeStep(SprueAndFeederFettlingCycleTimeStepEntity(), name = "Sprue and feeder fettling time")
                            .withCycleTimeStep(PartingLineBurrGrindingCycleTimeStepEntity(), name = "Fettling rough")
                            .withCycleTimeStep(FettlingManualGrabbingCycleTimeStepEntity(), name = "Part releasing")
                    )
                }
                StepSubTypeManualSandFettling.BurrHammeringRough -> {
                    withCycleTimeGroup(
                        CycleTimeStepGroupExpectation(
                            ManualSandFettlingCycleTimeStepGroupEntity(),
                            "Fettling manual"
                        )
                            .withCycleTimeStep(FettlingManualGrabbingCycleTimeStepEntity(), name = "Part loading")
                            .withCycleTimeStep(SprueAndFeederFettlingCycleTimeStepEntity(), name = "Sprue and feeder fettling time")
                            .withCycleTimeStep(PartingLineBurrGrindingCycleTimeStepEntity(), name = "Fettling rough")
                            .withCycleTimeStep(PartingLineBurrChisellingCycleTimeStepEntity(), name = "Fettling rough with chiseling")
                            .withCycleTimeStep(FettlingManualGrabbingCycleTimeStepEntity(), name = "Part releasing")
                    )
                }
                StepSubTypeManualSandFettling.BurrHammeringRoughFine -> {
                    withCycleTimeGroup(
                        CycleTimeStepGroupExpectation(
                            ManualSandFettlingCycleTimeStepGroupEntity(),
                            "Fettling manual"
                        )
                            .withCycleTimeStep(FettlingManualGrabbingCycleTimeStepEntity(), name = "Part loading")
                            .withCycleTimeStep(SprueAndFeederFettlingCycleTimeStepEntity(), name = "Sprue and feeder fettling time")
                            .withCycleTimeStep(PartingLineBurrGrindingCycleTimeStepEntity(), name = "Fettling rough")
                            .withCycleTimeStep(PartingLineBurrChisellingCycleTimeStepEntity(), name = "Fettling rough with chiseling")
                            .withCycleTimeStep(FineFettlingManualCycleTimeStepEntity(), name = "Fettling fine")
                            .withCycleTimeStep(FettlingManualGrabbingCycleTimeStepEntity(), name = "Part releasing")
                    )
                }
            }
            StepSubTypeSandFettling.NoFettling -> {  }
        }
    }
}
