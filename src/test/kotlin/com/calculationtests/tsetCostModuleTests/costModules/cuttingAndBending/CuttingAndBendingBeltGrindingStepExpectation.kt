package com.calculationtests.tsetCostModuleTests.costModules.cuttingAndBending

import com.calculationtests.testCostModules.expectations.CycleTimeStepGroupExpectation
import com.calculationtests.testCostModules.expectations.StepExpectation
import com.tset.clientsdk.schema.entities.BeltGrindingCycleTimeStepEntity
import com.tset.clientsdk.schema.entities.BeltGrindingCycleTimeStepGroupEntity
import com.tset.clientsdk.schema.entities.BeltGrindingLoadingUnloadingCycleTimeStepEntity
import com.tset.clientsdk.schema.entities.ManufacturingStepEntity

class CuttingAndBendingBeltGrindingStepExpectation(
    entity: ManufacturingStepEntity,
    name: String? = null,
    condition: (() -> <PERSON>olean)? = null
) : StepExpectation(entity, name, condition = condition) {
    init {
        withCycleTimeGroup(
            CycleTimeStepGroupExpectation(
                BeltGrindingCycleTimeStepGroupEntity(), "Belt grinding"
            )
                .withCycleTimeStep(BeltGrindingLoadingUnloadingCycleTimeStepEntity(), name = "Loading")
                .withCycleTimeStep(BeltGrindingCycleTimeStepEntity(), name = "Belt grinding")
                .withCycleTimeStep(BeltGrindingLoadingUnloadingCycleTimeStepEntity(), name = "Unloading")
        )
    }
}
