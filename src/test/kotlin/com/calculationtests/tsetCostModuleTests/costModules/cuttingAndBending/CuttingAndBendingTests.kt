package com.calculationtests.tsetCostModuleTests.costModules.cuttingAndBending

import com.calculationtests.mockEngine.calculationObjects.configuration.CalculationMethodologyConfiguration
import com.calculationtests.testCostModules.TestCase
import com.calculationtests.testCostModules.expectations.CostModuleExpectation
import com.calculationtests.testCostModules.expectations.InputParameter
import com.calculationtests.testCostModules.expectations.ProcessedMaterialExpectation
import com.calculationtests.testCostModules.expectations.withSteps
import com.calculationtests.tsetCostModuleTests.BaseCostModuleTests
import com.calculationtests.tsetCostModuleTests.sharedSteps.CleaningStepExpectation
import com.tset.clientsdk.schema.entities.BentMaterialEntity
import com.tset.clientsdk.schema.entities.ManufacturingCuttingAndBendingEntity
import com.tset.clientsdk.schema.entities.ManufacturingStepBeltGrindingEntity
import com.tset.clientsdk.schema.entities.ManufacturingStepBendingCubeEntity
import com.tset.clientsdk.schema.entities.ManufacturingStepCuttingCubeEntity
import com.tset.clientsdk.schema.entities.ManufacturingStepFettlingCubeEntity
import com.tset.clientsdk.schema.entities.ManufacturingStepShotBlastingDeburringEntity
import com.tset.clientsdk.schema.entities.ManufacturingStepStraighteningEntity
import com.tset.clientsdk.schema.quantities.inUnit
import com.tset.clientsdk.schema.selections.FettlingCubeType
import com.tset.clientsdk.schema.selections.SelectableBoolean
import com.tset.clientsdk.schema.selections.SheetSize
import com.tset.clientsdk.schema.selections.StepSubTypeStraightening
import com.tset.clientsdk.schema.units.LengthUnits
import com.tset.clientsdk.schema.units.WeightUnits
import org.junit.jupiter.api.DynamicTest
import org.junit.jupiter.api.TestFactory
import org.springframework.boot.test.context.SpringBootTest

@SpringBootTest
class CuttingAndBendingTests : BaseCostModuleTests<ManufacturingCuttingAndBendingEntity>(
    ::ManufacturingCuttingAndBendingEntity,
) {
    @TestFactory
    fun test(): List<DynamicTest> {
        return super.basicTest(verifyCalculation = true, CalculationMethodologyConfiguration())
    }

    override fun createTestCases() = listOf(
        TestCase(),
        TestCase(
            InputParameter(
                ManufacturingCuttingAndBendingEntity::sheetLengthUserInput,
                0.0 inUnit LengthUnits.Meter
            )
        ),
        TestCase(
            InputParameter(
                ManufacturingCuttingAndBendingEntity::sheetWidthUserInput,
                0.0 inUnit LengthUnits.Meter
            )
        ),
        TestCase(InputParameter(ManufacturingCuttingAndBendingEntity::cleaningNeeded, SelectableBoolean.True)),
        TestCase(InputParameter(ManufacturingCuttingAndBendingEntity::fettlingCubeType, FettlingCubeType.NoFettling)),
        TestCase(InputParameter(ManufacturingCuttingAndBendingEntity::fettlingCubeType, FettlingCubeType.OneSide)),
        TestCase(InputParameter(ManufacturingCuttingAndBendingEntity::fettlingCubeType, FettlingCubeType.BothSide)),
        TestCase(InputParameter(ManufacturingCuttingAndBendingEntity::beltGrinding, SelectableBoolean.True)),
        TestCase(InputParameter(ManufacturingCuttingAndBendingEntity::shotBlastingNeeded, SelectableBoolean.True)),
        TestCase(
            InputParameter(
                ManufacturingCuttingAndBendingEntity::straighteningType,
                StepSubTypeStraightening.PassThrough
            )
        ),
        TestCase(
            InputParameter(
                ManufacturingCuttingAndBendingEntity::straighteningType,
                StepSubTypeStraightening.Pressing
            )
        ),
        TestCase(InputParameter(ManufacturingCuttingAndBendingEntity::numberOfBends, 10)),
        TestCase(
            InputParameter(ManufacturingCuttingAndBendingEntity::fettlingCubeType, FettlingCubeType.OneSide),
            InputParameter(ManufacturingCuttingAndBendingEntity::beltGrinding, SelectableBoolean.True),
            InputParameter(ManufacturingCuttingAndBendingEntity::shotBlastingNeeded, SelectableBoolean.True),
            InputParameter(ManufacturingCuttingAndBendingEntity::straighteningType, StepSubTypeStraightening.Pressing),
        ),
    )

    override fun createDefaultParameters() = listOf(
        // Mandatory parameters:
        InputParameter(ManufacturingCuttingAndBendingEntity::sheetSize, SheetSize.SmallFormat),
        InputParameter(ManufacturingCuttingAndBendingEntity::sheetThickness, 0.005 inUnit LengthUnits.Meter),
        InputParameter(ManufacturingCuttingAndBendingEntity::materialName, "C10-RAW_MATERIAL_SHEET"),
        InputParameter(ManufacturingCuttingAndBendingEntity::shapeId, "S_978"),
        InputParameter(
            ManufacturingCuttingAndBendingEntity::netWeightPerPartForWizard,
            1.2 inUnit WeightUnits.Kilogram
        ),
        InputParameter(ManufacturingCuttingAndBendingEntity::numberOfBends, 0),
        InputParameter(ManufacturingCuttingAndBendingEntity::fettlingCubeType, FettlingCubeType.NoFettling),
        InputParameter(
            ManufacturingCuttingAndBendingEntity::straighteningType,
            StepSubTypeStraightening.NoStraightening
        ),
        InputParameter(ManufacturingCuttingAndBendingEntity::beltGrinding, SelectableBoolean.False),
        InputParameter(ManufacturingCuttingAndBendingEntity::cleaningNeeded, SelectableBoolean.False),
        InputParameter(ManufacturingCuttingAndBendingEntity::shotBlastingNeeded, SelectableBoolean.False),
        InputParameter(ManufacturingCuttingAndBendingEntity::setupScrapPartsInBending, 0.0),

        // Optional parameters:
        /*
        InputParameter(ManufacturingCuttingAndBendingEntity::calculatedNetWeightPerPart, 0.0 inUnit WeightUnits.Kilogram),
        InputParameter(ManufacturingCuttingAndBendingEntity::gapNetWeightPerPart, 0.0 inUnit WeightUnits.Kilogram),
        InputParameter(ManufacturingCuttingAndBendingEntity::longestBendLength, 0.0 inUnit LengthUnits.Meter),
        InputParameter(ManufacturingCuttingAndBendingEntity::netWeightPerPartDuringUnfoldStep, 0.0 inUnit WeightUnits.Kilogram),
        InputParameter(ManufacturingCuttingAndBendingEntity::numberOfBends, 0),
        InputParameter(ManufacturingCuttingAndBendingEntity::unfoldedPartLength, 0.0 inUnit LengthUnits.Meter),
        InputParameter(ManufacturingCuttingAndBendingEntity::unfoldedPartWidth, 0.0 inUnit LengthUnits.Meter),
        */
    )

    override fun createTestExpectation(costModule: ManufacturingCuttingAndBendingEntity): CostModuleExpectation {
        return CostModuleExpectation(
            ManufacturingCuttingAndBendingEntity(),
            processedMaterial = ProcessedMaterialExpectation(
                BentMaterialEntity(),
                name = "BentMaterial",
            )
                .withSteps(
                    CuttingAndBendingCuttingCubeStepExpectation(
                        ManufacturingStepCuttingCubeEntity(),
                        "ManufacturingStepCuttingCube",
                    )
                )
                .withSteps(
                    CuttingAndBendingBendingCubeStepExpectation(
                        entity = ManufacturingStepBendingCubeEntity(),
                        name = "ManufacturingStepBendingCube",
                        numberOfBends = costModule.numberOfBends!!,
                    ) { costModule.numberOfBends!! > 0 },
                )
                .withSteps(
                    CuttingAndBendingFettlingCubeStepExpectation(
                        entity = ManufacturingStepFettlingCubeEntity(),
                        name = "ManufacturingStepFettling",
                        fettlingType = costModule.fettlingCubeType!!,
                    ) { costModule.fettlingCubeType != FettlingCubeType.NoFettling }
                )
                .withSteps(
                    CuttingAndBendingBeltGrindingStepExpectation(
                        ManufacturingStepBeltGrindingEntity(),
                        "ManufacturingStepBeltGrinding"
                    ) { costModule.beltGrinding == SelectableBoolean.True }
                )
                .withSteps(
                    CuttingAndBendingShotBlastingDeburringStepExpectation(
                        ManufacturingStepShotBlastingDeburringEntity(),
                        "ManufacturingStepShotBlastingDeburring"
                    ) { costModule.shotBlastingNeeded == SelectableBoolean.True }
                )
                .withSteps(
                    CuttingAndBendingStraighteningStepExpectation(
                        entity = ManufacturingStepStraighteningEntity(),
                        name = "ManufacturingStepStraightening",
                        straighteningType = costModule.straighteningType!!,
                    ) { costModule.straighteningType != StepSubTypeStraightening.NoStraightening }
                )
                .withSteps(
                    CleaningStepExpectation { costModule.cleaningNeeded == SelectableBoolean.True },
                )
        )
    }
}