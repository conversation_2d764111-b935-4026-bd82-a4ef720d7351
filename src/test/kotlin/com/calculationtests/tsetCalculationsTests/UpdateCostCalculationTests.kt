package com.calculationtests.tsetCalculationsTests

import com.calculationtests.helperFunctions.defaultedZeroRateMap
import com.calculationtests.mockEngine.engine.CalculationEngine
import com.calculationtests.testObjects.*
import com.calculationtests.testObjects.dynamicOverrides.DynamicOverrideField
import com.calculationtests.testObjects.dynamicOverrides.DynamicRate
import com.calculationtests.tsetEntityFactories.*
import com.calculationtests.verification.CalculationAndVerificationService
import com.tset.clientsdk.schema.BaseEntity
import com.tset.clientsdk.schema.dtos.findChildEntity
import com.tset.clientsdk.schema.entities.ConsumableEntity
import com.tset.clientsdk.schema.entities.ManualManufacturingEntity
import com.tset.clientsdk.schema.quantities.inUnit
import com.tset.clientsdk.schema.units.TimeInYearsUnits
import com.tset.clientsdk.schema.with
import org.apache.commons.collections4.map.DefaultedMap
import org.apache.commons.collections4.map.DefaultedMap.defaultedMap
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest

@SpringBootTest
class UpdateCostCalculationTests {
    @Autowired
    lateinit var calculationAndVerificationService: CalculationAndVerificationService

    @Autowired
    lateinit var calculationEngine: CalculationEngine

    @Autowired lateinit var manufacturingFactory: TsetManufacturingFactory

    @Autowired lateinit var stepFactory: TsetManufacturingStepFactory

    @Autowired lateinit var bomEntryFactory: TsetBomEntryFactory

    @Autowired lateinit var materialFactory: TsetMaterialEntityFactory

    private val testMaterialFactory by lazy {
        TestMaterialFactory(manufacturingFactory, bomEntryFactory, materialFactory)
    }
    private val testStepFactory by lazy {
        TestManufacturingStepFactory(stepFactory, manufacturingFactory, bomEntryFactory, testMaterialFactory)
    }
    private val complexTreeFactory by lazy {
        TsetComplexTreeFactory(stepFactory, manufacturingFactory)
    }

    val projectKey = "UPD2"

    private fun importBomAndVerify(root: ManualManufacturingEntity) =
        calculationAndVerificationService.calculateAndVerifyRecursivelyFromTestEntity(
            root,
            calculationEngine,
            projectKey,
            "Tests Field Change of persisted BOM 2",
        )

    private fun updateBomAndVerify(
        expectedRootBom: ManualManufacturingEntity,
        actualRootBomNodeId: String,
        updateBomNodeId: String,
        updateEntity: BaseEntity,
        updateObjectId: String,
        updateFieldName: String,
        verify: Boolean = true,
    ) {
        calculationAndVerificationService.updateCalculateAndVerifyRecursivelyFromTestEntity(
            projectKey = projectKey,
            expectedRootBom = expectedRootBom,
            actualRootBomNodeId = actualRootBomNodeId,
            updateBomNodeId = updateBomNodeId,
            updateEntity = updateEntity,
            updateObjectId = updateObjectId,
            updateFieldName = updateFieldName,
            calculationService = calculationEngine,
            verify = verify,
        )
    }

    // region simple Tests
    @Test
    fun `Test field update of consumable in BOM`() {
        // create initial persisted BOM
        val params = OperationParameters()

        val consumable =
            materialFactory.createConsumableEntity(
                identifier = "Consumable",
                pricePerUnit = 5.0,
                quantity = 2.0,
                operationParameters = params,
            )

        val manufacturing =
            manufacturingFactory.createManualManufacturingEntity(
                "Root with consumable",
                peakProductionVolume = TestObjectsProductionVolume.highWithPeak.peak,
                averageProductionVolume = TestObjectsProductionVolume.highWithPeak.average,
                lifetime = TestObjectsProductionVolume.highWithPeak.lifetime,
                callsPerYear = 10.0,
                operationParameters = params,
            ).with(
                consumable,
            )

        // import initial persisted BOM
        val (bomNodeDto, creationDto) = importBomAndVerify(manufacturing)

        // create update
        val consumableBomNodeDto = bomNodeDto.findChildEntity(consumable.toString())
        val bomNodeId = creationDto.id
        val objectId = consumableBomNodeDto!!.id
        val rootBomNodeId = bomNodeId

        consumable.quantity = 4.0

        updateBomAndVerify(
            expectedRootBom = manufacturing,
            actualRootBomNodeId = rootBomNodeId,
            updateBomNodeId = bomNodeId,
            updateEntity = consumable,
            updateObjectId = objectId,
            updateFieldName = ConsumableEntity::quantity.name,
        )
    }

    @Test
    fun `Test field update of external step in BOM`() {
        // create initial persisted BOM
        val params = OperationParameters()
        val externalStep = testStepFactory.createExternalStepWithRoughManufacturing(params, scrapRate = 0.0)

        val manufacturing =
            manufacturingFactory.createManualManufacturingEntity(
                "Root with external step",
                peakProductionVolume = TestObjectsProductionVolume.highWithPeak.peak,
                averageProductionVolume = TestObjectsProductionVolume.highWithPeak.average,
                lifetime = TestObjectsProductionVolume.highWithPeak.lifetime,
                callsPerYear = 10.0,
                operationParameters = params,
            ).with(
                listOf(externalStep),
            )

        // import initial persisted BOM
        val (bomNodeDto, creationDto) = importBomAndVerify(manufacturing)

        // create update
        manufacturing.lifeTime = 14.0 inUnit TimeInYearsUnits.Years

        val bomNodeId = creationDto.id
        val objectId = bomNodeDto.id
        val rootBomNodeId = bomNodeId

        updateBomAndVerify(
            expectedRootBom = manufacturing,
            actualRootBomNodeId = rootBomNodeId,
            updateBomNodeId = bomNodeId,
            updateEntity = manufacturing,
            updateObjectId = objectId,
            updateFieldName = ManualManufacturingEntity::lifeTime.name,
        )
    }

    @Test
    fun `Test field update of steps and consumables in BOM`() {
        // create initial persisted BOM
        val params = OperationParameters()

        val materialTriple =
            testMaterialFactory.createAllMaterialsThreeTimes(params)
                .first { it.first is ConsumableEntity }

        val manufacturing =
            complexTreeFactory.createManufacturingWithStepsAndSteplessMaterials(
                steplessMaterials = listOf(materialTriple.first),
                step2Materials = listOf(materialTriple.second),
                step1Materials = listOf(materialTriple.third),
                params,
                identifier = "Root with steps and consumables",
                scrapRate = 0.0,
            )

        // import initial persisted BOM
        val (bomNodeDto, creationDto) = importBomAndVerify(manufacturing)

        // create update
        val consumable = materialTriple.first as ConsumableEntity
        consumable.quantity = 5.0
        val consumableBomNodeDto = bomNodeDto.findChildEntity(consumable.toString())

        val bomNodeId = creationDto.id
        val objectId = consumableBomNodeDto!!.id
        val rootBomNodeId = bomNodeId

        updateBomAndVerify(
            expectedRootBom = manufacturing,
            actualRootBomNodeId = rootBomNodeId,
            updateBomNodeId = bomNodeId,
            updateEntity = consumable,
            updateObjectId = objectId,
            updateFieldName = ConsumableEntity::quantity.name,
        )
    }

    private fun createParameters(salesAndAdministrationRate: Double): OperationParameters {
        return OperationParameters(
            dynamicOverheads =
                defaultedMap(
                    mapOf(
                        ParameterSettingLevel.MANUFACTURING to createOverheads(salesAndAdministrationRate),
                    ),
                    defaultedZeroRateMap(),
                ),
        )
    }

    private fun createOverheads(salesAndAdministrationRate: Double): DefaultedMap<String, DynamicOverrideField> =
        defaultedMap(
            mapOf(
                "SalesAndGeneralAdministrationCosts-Rm" to DynamicRate(salesAndAdministrationRate),
            ),
            DynamicRate(),
        )
}
