package com.calculationtests.tsetCalculationsTests

import com.calculationtests.defaulttsetvalues.TsetProcurementType
import com.calculationtests.helperFunctions.defaultedZeroPeriodMap
import com.calculationtests.helperFunctions.defaultedZeroRateMap
import com.calculationtests.helperFunctions.randomObjectId
import com.calculationtests.mockEngine.engine.CalculationEngine
import com.calculationtests.testObjects.OperationParameters
import com.calculationtests.testObjects.ParameterSettingLevel
import com.calculationtests.testObjects.TestManufacturingStepFactory
import com.calculationtests.testObjects.TestMaterialFactory
import com.calculationtests.testObjects.TestObjectsProductionVolume
import com.calculationtests.testObjects.TestProcess
import com.calculationtests.testObjects.TestSpecialDirectCostsFactory
import com.calculationtests.testObjects.dynamicOverrides.DynamicPeriod
import com.calculationtests.testObjects.dynamicOverrides.DynamicRate
import com.calculationtests.tsetEntityFactories.TsetBomEntryFactory
import com.calculationtests.tsetEntityFactories.TsetComplexTreeFactory
import com.calculationtests.tsetEntityFactories.TsetManufacturingFactory
import com.calculationtests.tsetEntityFactories.TsetManufacturingStepFactory
import com.calculationtests.tsetEntityFactories.TsetMaterialEntityFactory
import com.calculationtests.tsetEntityFactories.TsetWorkCenterMemberFactory.createRoughMachineEntity
import com.calculationtests.verification.CalculationAndVerificationService
import org.junit.jupiter.api.DynamicTest
import org.junit.jupiter.api.TestFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import com.tset.clientsdk.schema.entities.ManualManufacturingEntity
import com.tset.clientsdk.schema.with
import org.apache.commons.collections4.map.DefaultedMap.defaultedMap

@SpringBootTest
class ConnectedLinesTests @Autowired constructor(
    val calculationAndVerificationService: CalculationAndVerificationService,
    val calculationEngine: CalculationEngine,
    val manufacturingFactory : TsetManufacturingFactory,
    val stepFactory : TsetManufacturingStepFactory,
    val bomEntryFactory: TsetBomEntryFactory,
    val materialFactory: TsetMaterialEntityFactory,
) {


    private val testMaterialFactory by lazy {
        TestMaterialFactory(manufacturingFactory, bomEntryFactory, materialFactory)
    }

    private val testStepFactory by lazy {
        TestManufacturingStepFactory(stepFactory, manufacturingFactory, bomEntryFactory, testMaterialFactory)
    }

    private val complexTreeFactory by lazy {
        TsetComplexTreeFactory(stepFactory, manufacturingFactory)
    }

    private fun executeTest(root: ManualManufacturingEntity, dynamicTestName: String) =
        DynamicTest.dynamicTest(dynamicTestName) {
            calculationAndVerificationService.calculateAndVerifyRecursivelyFromTestEntity(
                root,
                calculationEngine,
                "COLTS",
                "Tests for Connected Lines"
            )
        }

    private val operationParametersWithOneOption by lazy {
        OperationParameters(
            dynamicOverheads = defaultedMap(
                mapOf(
                    ParameterSettingLevel.MATERIAL_USAGE to defaultedMap(mapOf(), DynamicRate()),
                    ParameterSettingLevel.MANUFACTURING to defaultedMap(mapOf(), DynamicRate()),
                    ParameterSettingLevel.MANUFACTURING_STEP to defaultedMap(mapOf(), DynamicRate()),
                ),
                defaultedZeroRateMap(),
            ),
            dynamicInterests = defaultedMap(
                mapOf(
                    ParameterSettingLevel.MATERIAL_USAGE to defaultedMap(mapOf(), DynamicPeriod()),
                    ParameterSettingLevel.MANUFACTURING to defaultedMap(mapOf(), DynamicPeriod()),
                    ParameterSettingLevel.MANUFACTURING_STEP to defaultedMap(mapOf(), DynamicPeriod()),
                ),
                defaultedZeroPeriodMap(),
            ),
            setupScrapParts = 0.002,
            lotFraction = 0.8,
            utilizationRate = 0.7,
            cycleTime = 360.0,
            partsPerCycle = 2.0,
        )
    }

    @TestFactory
    fun `Simple Tests for Connected Lines`(): List<DynamicTest> =
        listOf(TsetProcurementType.INHOUSE, TsetProcurementType.PURCHASE).map { procurementType ->
            val groupId = randomObjectId()
            val productionVolume = TestObjectsProductionVolume.lowWithPeak
            val operationParameters = OperationParameters()
            val testManufacturing = manufacturingFactory.createManualManufacturingEntity(
                "Root",
                operationParameters,
                peakProductionVolume = productionVolume.peak,
                averageProductionVolume = productionVolume.average,
                lifetime = productionVolume.lifetime,
                customProcurementType = procurementType.customProcurementType,
            ).with(
                stepFactory.createLineOfSteps(
                    stepFactory.createManualManufacturingStepEntity(
                        "step1", operationParameters = operationParameters, groupId = groupId, cycleTime = 60.0,
                    ).with(
                        createRoughMachineEntity("Machine")
                    ),
                    stepFactory.createManualManufacturingStepEntity(
                        "step2", operationParameters = operationParameters, groupId = groupId,
                    ).with(
                        createRoughMachineEntity("Machine")
                    )
                )
            )
            executeTest(testManufacturing, "step sanity test")
        }


    @TestFactory
    fun `Test Processes with Lines`(): List<DynamicTest> {
        val operationParameters = OperationParameters(
            dynamicOverheads = defaultedMap(
                mapOf(
                    ParameterSettingLevel.MANUFACTURING_STEP to defaultedMap(mapOf(), DynamicRate()),
                    ParameterSettingLevel.MATERIAL_USAGE to defaultedMap(mapOf(), DynamicRate())
                ),
                defaultedZeroRateMap()
            ),
            dynamicInterests = defaultedMap(
                mapOf(ParameterSettingLevel.MANUFACTURING_STEP to defaultedMap(mapOf(),DynamicPeriod())),
                defaultedZeroPeriodMap()
            ),
            setupScrapParts = 0.002,
            lotFraction = 0.1,
            utilizationRate = 0.85,
        )
        val scrapRate = 0.4
        return listOf(
            TestProcess(
                testStepFactory.createManualStepWithAll1(operationParameters, scrapRate = scrapRate),
                testStepFactory.createManualStepWithAllWithSetup(operationParameters, scrapRate = scrapRate),
                testStepFactory.createLineOfManualSteps(operationParameters, scrapRate),
            ),
            TestProcess(
                testStepFactory.createManualStepWithAll1(operationParameters, scrapRate = scrapRate),
                testStepFactory.createLineOfManualSteps(operationParameters, scrapRate),
                testStepFactory.createManualStepWithAllWithSetup(operationParameters, scrapRate = scrapRate),
            ),
            TestProcess(
                testStepFactory.createLineOfManualSteps(operationParameters, scrapRate),
                testStepFactory.createManualStepWithAll1(operationParameters, scrapRate = scrapRate),
                testStepFactory.createManualStepWithAllWithSetup(operationParameters, scrapRate = scrapRate),
            ),
            TestProcess(
                testStepFactory.createLineOfManualSteps(operationParameters, scrapRate),
                testStepFactory.createManualStepWithAllWithSetup(operationParameters, scrapRate = scrapRate),
                testStepFactory.createLineOfManualSteps(operationParameters, scrapRate),
            ),
            TestProcess(
                testStepFactory.createLineOfManualSteps(operationParameters, scrapRate),
                testStepFactory.createManualStepWithAllWithSetup(operationParameters, scrapRate = scrapRate),
                testStepFactory.createLineWithRoughStep(operationParameters, scrapRate),
            ),
        ).map { process ->
            val testName = "Process: $process"
            val root = complexTreeFactory.createManufacturingWithNestedManufacturingSteps(
                process,
                operationParameters = operationParameters,
                identifier = testName
            )
            executeTest(root, testName)
        }
    }

    @TestFactory
    fun `Test Manufacturing containing lines with special direct Costs`(): List<DynamicTest> =
        listOf(0.0, 0.3).flatMap { scrapRate ->
            listOf(
                "Manual Line" to testStepFactory.createLineOfManualSteps(operationParametersWithOneOption, scrapRate),
                "Line with Rough Step" to testStepFactory.createLineWithRoughStep(operationParametersWithOneOption, scrapRate),
            ).map { (name, step) ->
                val testName = "indirect costs and $name and scrap = $scrapRate"
                val productionVolume = TestObjectsProductionVolume.lowWithPeak
                val operationParameters = operationParametersWithOneOption
                val root = manufacturingFactory.createManualManufacturingEntity(
                    testName,
                    peakProductionVolume = productionVolume.peak,
                    averageProductionVolume = productionVolume.average,
                    lifetime = productionVolume.lifetime,
                    callsPerYear = 100.0,
                    operationParameters = operationParameters,
                ).with(
                    step.with(testMaterialFactory.createAllMaterials(operationParameters)),
                ).with(
                    testMaterialFactory.createAllMaterials(operationParameters),
                    TestSpecialDirectCostsFactory.createSpecialDirectCosts(),
                )
                executeTest(root, testName)
            }
        }

}
