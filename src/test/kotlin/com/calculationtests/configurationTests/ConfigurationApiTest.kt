package com.calculationtests.configurationTests

import com.calculationtests.publicApi.communicationWithAPI.keyCloak.Authentication
import com.calculationtests.publicApi.communicationWithAPI.nuBomKotlin.ApiException
import com.calculationtests.publicApi.communicationWithAPI.nuBomKotlin.ConfigurationApi
import com.calculationtests.publicApi.communicationWithAPI.nuBomKotlin.NuBomKotlinApiProperties
import com.nu.bom.core.publicapi.dtos.configurations.*
import org.assertj.core.api.recursive.comparison.RecursiveComparisonConfiguration
import org.assertj.core.util.BigDecimalComparator
import org.assertj.core.api.Assertions as AssertJ
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import java.math.BigDecimal

@SpringBootTest
class ConfigurationApiTest {

    @Autowired
    lateinit var nuBomProperties: NuBomKotlinApiProperties

    @Autowired
    lateinit var authentication: Authentication

    private val configurationApi by lazy {
        ConfigurationApi(nuBomProperties, authentication)
    }

    companion object {
        private const val GROUP = "CalculationAssumptions"
        private const val TYPE = "DefaultValues"
        private const val KEY = "test-key"
    }

    @AfterEach
    fun cleanup() {
        configurationApi
            .findAllOfType(GROUP, TYPE, includeInactive = true)!!
            .map { it.id.key }
            .filterNot { it.startsWith("tset.") }
            .distinct()
            .forEach { configurationApi.delete(GROUP, TYPE, it) }
    }

    private fun createShiftModelConfigurationDto(
        shiftsPerDay: Double = 2.0,
        extraShifts: Double = 1.0,
        daysPerWeek: Double = 5.0,
        weeksPerYear: Double = 48.0,
        timePerShift: Double = 6.0,
    ) = DefaultValuesConfigurationDto(
        shiftsPerDay = shiftsPerDay.toBigDecimal(),
        extraShifts = extraShifts.toBigDecimal(),
        daysPerWeek = daysPerWeek.toBigDecimal(),
        weeksPerYear = weeksPerYear.toBigDecimal(),
        timePerShift = timePerShift.toBigDecimal(),
    )

    private fun createNew(
        group: String,
        type: String,
        key: String,
        value: ConfigurationValueDto,
        displayName: String = key,
    ): ConfigurationResponseDto {
        val res = configurationApi.createNew(group, type, key, displayName, value)

        Assertions.assertNotNull(res)
        Assertions.assertEquals(type, res!!.id.type)
        AssertJ.assertThat(res.id.key).endsWith(key)
        Assertions.assertEquals(SemanticVersionDto(1, 0), res.id.version)
        Assertions.assertTrue(res.isActive)
        Assertions.assertFalse(res.isDefault)

        return res
    }

    private fun get(id: ConfigurationIdentifierDto): ConfigurationResponseDto {
        val result = configurationApi.get(id)
        Assertions.assertNotNull(result)

        return result!!
    }

    private fun update(
        old: ConfigurationResponseDto,
        newValue: ConfigurationValueDto,
    ): ConfigurationResponseDto {
        val new = configurationApi.createMajorVersion(old.id.group, old.id.type, old.id.key, null, newValue)

        Assertions.assertNotNull(new)
        Assertions.assertEquals(old.id.type, new!!.id.type)
        Assertions.assertEquals(SemanticVersionDto(old.id.version.major + 1, 0), new.id.version)
        Assertions.assertEquals(old.isActive, new.isActive)
        Assertions.assertEquals(old.isDefault, new.isDefault)
        return new
    }

    @Test
    fun createNew() {
        val configurationValueDto = createShiftModelConfigurationDto()

        createNew(GROUP, TYPE, KEY, configurationValueDto)
    }

    @Test
    fun saveFail() {
        val nonExistingGroupKey = "38dc78d9a5e043ef132795ee99eb21174e38e4c5"
        val nonExistingContext = "3208ae9bdc001c273c6b54759ef7ae24eaaa0e1b"
        val configurationValueDto = createShiftModelConfigurationDto()

        Assertions.assertThrows(ApiException::class.java) {
            configurationApi.createNew(
                group = nonExistingGroupKey,
                type = nonExistingContext,
                KEY,
                configuration = configurationValueDto,
            )
        }
    }

    @Test
    fun saveFailInvariants() {
        val configuration = createShiftModelConfigurationDto(daysPerWeek = 8.0)

        Assertions.assertThrows(ApiException::class.java) {
            createNew(GROUP, TYPE, KEY, configuration)
        }
    }

    @Test
    fun activeDefault() {
        val configuration = createShiftModelConfigurationDto()

        val res = createNew(GROUP, TYPE, KEY, configuration)

        val deactivated = configurationApi.deactivate(res.id)
        Assertions.assertNotNull(deactivated)
        Assertions.assertFalse(deactivated!!.isActive)
    }

    @Test
    fun get() {
        val configurationValueDto = createShiftModelConfigurationDto()

        val saved = createNew(GROUP, TYPE, KEY, configurationValueDto)
        val fetched = get(saved.id)

        assertWithBigDecimal(configurationValueDto).isEqualTo(saved.value)
        assertWithBigDecimal(saved).isEqualTo(fetched)
    }

    @Test
    fun update() {
        val configurationValueDto = createShiftModelConfigurationDto(daysPerWeek = 4.0)
        val og = createNew(GROUP, TYPE, KEY, configurationValueDto)

        val newConfigurationValueDto = createShiftModelConfigurationDto(daysPerWeek = 5.0)
        update(og, newConfigurationValueDto)
    }

    @Test
    fun updateDefault() {
        val configurationValueDto = createShiftModelConfigurationDto(weeksPerYear = 1.0)
        val new = createNew(GROUP, TYPE, KEY, configurationValueDto)

        val previousDefault = configurationApi.getDefault(GROUP, TYPE)!!

        val newDefault = configurationApi.setDefault(new.id)!!

        Assertions.assertTrue(newDefault.isDefault)

        val updatedConfigurationValueDto = configurationValueDto.copy(weeksPerYear = configurationValueDto.weeksPerYear + BigDecimal.ONE)
        val updated = update(newDefault, updatedConfigurationValueDto)

        Assertions.assertTrue(updated.isDefault)

        val revertDefault = configurationApi.setDefault(previousDefault.id)!!

        Assertions.assertTrue(revertDefault.isDefault)
    }

    @Test
    fun delete() {
        val configurationValueDto = createShiftModelConfigurationDto()
        val new = createNew(GROUP, TYPE, KEY, configurationValueDto)

        val gotDeleted = configurationApi.delete(new.id.group, new.id.type, new.id.key)

        Assertions.assertNotNull(gotDeleted)
        Assertions.assertTrue(gotDeleted!!)
    }

    @Test
    fun find() {
        val configurationValueDto = createShiftModelConfigurationDto()

        val existing = configurationApi.findAllOfType(GROUP, TYPE)
        Assertions.assertNotNull(existing)
        Assertions.assertFalse(existing!!.isEmpty())

        val new = createNew(GROUP, TYPE, KEY, configurationValueDto)

        val all = configurationApi.findAllOfType(GROUP, TYPE)

        Assertions.assertNotNull(all)
        Assertions.assertEquals(existing.size + 1, all!!.size)

        val found = all.find { it == new }

        Assertions.assertNotNull(found)
    }

    @Test
    fun findGetLatest() {
        val configuration = createShiftModelConfigurationDto(timePerShift = 5.0)
        val og = createNew(GROUP, TYPE, KEY, configuration)

        val newConfiguration = createShiftModelConfigurationDto(timePerShift = 6.0)
        val new = update(og, newConfiguration)

        val latest = configurationApi.findLatestMajor(og.id.group, og.id.type, og.id.key)
        Assertions.assertNotNull(latest)
        Assertions.assertEquals(new, latest)

        val ogActive = configurationApi.activate(og.id)!!

        Assertions.assertTrue(ogActive.isActive)

        val newInactive = configurationApi.deactivate(new.id)!!

        Assertions.assertFalse(newInactive.isActive)

        val latestActive = configurationApi
            .findLatestMajor(og.id.group, og.id.type, og.id.key, includeInactive = false)!!

        Assertions.assertEquals(latestActive, og)
        Assertions.assertTrue(latestActive.isActive)
    }

    @Test
    fun getAll() {
        val configurationValueDtos = listOf(
            createShiftModelConfigurationDto(timePerShift = 6.0),
            createShiftModelConfigurationDto(timePerShift = 5.0),
            createShiftModelConfigurationDto(timePerShift = 4.0),
        )

        val foundBaseline = configurationApi.findAllOfType(GROUP, TYPE)

        configurationValueDtos
            .forEachIndexed { i, value -> createNew(GROUP, TYPE, "$KEY-$i", value) }

        val foundConfigurations = configurationApi.findAllOfType(GROUP, TYPE)

        Assertions.assertNotNull(foundConfigurations)
        Assertions.assertEquals(foundBaseline!!.size + configurationValueDtos.size, foundConfigurations!!.size)

        AssertJ
            .assertThat(foundConfigurations.map { it.value })
            .usingRecursiveFieldByFieldElementComparator(
                RecursiveComparisonConfiguration
                    .builder()
                    .withComparatorForType(BigDecimalComparator.BIG_DECIMAL_COMPARATOR, BigDecimal::class.java)
                    .build()
            ).containsAll(configurationValueDtos)
    }

    private fun <T> assertWithBigDecimal(actual: T) =
        AssertJ
            .assertThat(actual)
            .usingRecursiveComparison(
                RecursiveComparisonConfiguration
                    .builder()
                    .withComparatorForType(BigDecimalComparator.BIG_DECIMAL_COMPARATOR, BigDecimal::class.java)
                    .build()
            )
}
