package com.tset.masterdata.fixtures

import com.tset.masterdata.model.SimpleKey
import com.tset.masterdata.model.headertype.LookupStrategy
import com.tset.masterdata.repository.LookupStrategyRepository
import org.springframework.stereotype.Component
import java.util.*

@Component
class LookupStrategyFixtures(
    private val lookupStrategyRepository: LookupStrategyRepository,
    private val headerFixtures: HeaderFixtures,
) {
    suspend fun createLookupStrategy(
        name: String,
        headerTypeId: UUID,
    ) = lookupStrategyRepository.save(
        LookupStrategy(null, SimpleKey(name), name, headerTypeId),
    )

    suspend fun createLookupStrategy(
        key: <PERSON><PERSON><PERSON>,
        name: String,
        headerTypeId: UUID,
    ) = lookupStrategyRepository.save(
        LookupStrategy(null, key, name, headerTypeId),
    )

    suspend fun createOne(): LookupStrategyFixtureInfo {
        val headerInfo = headerFixtures.createOneSimple()
        val strat = createLookupStrategy("LOOKUP_STRATEGY", headerInfo.type.id!!)
        return LookupStrategyFixtureInfo(strat, headerInfo)
    }
}

data class LookupStrategyFixtureInfo(
    val lookupStrategy: LookupStrategy,
    val headerInfo: HeaderFixtureInfo,
)
