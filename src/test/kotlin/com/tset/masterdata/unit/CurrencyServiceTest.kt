package com.tset.masterdata.unit

import com.nu.masterdata.dto.v1.basicdata.CurrencyDto
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import com.tset.masterdata.model.SimpleKey
import com.tset.masterdata.model.toKey
import com.tset.masterdata.service.api.basicData.CurrencyApiService
import com.tset.masterdata.service.basicData.CurrencyService
import com.tset.masterdata.service.exception.InvalidKeyFormatException
import com.tset.masterdata.service.mapper.basicData.CurrencyMapper
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.Mockito

class CurrencyServiceTest {
    private val currencyService = Mockito.mock(CurrencyService::class.java)

    private val currencyApiService = CurrencyApiService(
        currencyService,
        Mockito.mock(CurrencyMapper::class.java),
    )

    @ParameterizedTest
    @ValueSource(strings = ["KEA", "CAX", "AAA", "ADS", "EUR"])
    fun `create with valid currency keys should work`(strKey: String) {
        val dto = CurrencyDto(
            key = SimpleKeyDto(strKey),
            name = strKey,
            symbol = strKey,
        )
        Assertions.assertDoesNotThrow {
            runBlocking {
                currencyApiService.create(dto)
            }
        }
    }

    @ParameterizedTest
    @ValueSource(strings = ["kEa", "C1X", "AA", "ADSE", "ÄÖU", "ewd"])
    fun `create with invalid currency keys should throw an error`(strKey: String) {
        val dto = CurrencyDto(
            key = SimpleKeyDto(strKey),
            name = strKey,
            symbol = strKey,
        )
        assertThrows<InvalidKeyFormatException> {
            runBlocking {
                currencyApiService.create(dto)
            }
        }
    }

    @ParameterizedTest
    @ValueSource(strings = ["KEA", "CAX", "AAA", "ADS", "EUR"])
    fun `update valid currency with valid currency keys should work`(strKey: String) {
        val dto = CurrencyDto(
            key = SimpleKeyDto(strKey),
            name = strKey,
            symbol = strKey,
        )
        Assertions.assertDoesNotThrow {
            runBlocking {
                currencyApiService.update(dto.key.toKey(), dto)
            }
        }
    }

    @ParameterizedTest
    @ValueSource(strings = ["KEA", "CAX", "AAA", "ADS", "EUR"])
    fun `update invalid currency with valid currency keys should work`(strKey: String) {
        val dto = CurrencyDto(
            key = SimpleKeyDto(strKey),
            name = strKey,
            symbol = strKey,
        )
        Assertions.assertDoesNotThrow {
            runBlocking {
                currencyApiService.update(SimpleKey("invalidkeyhere1"), dto)
            }
        }
    }

    @Test
    fun `update invalid currency with same invalid currency keys should work`() {
        val dto = CurrencyDto(
            key = SimpleKeyDto("invalid"),
            name = "adsf",
            symbol = "ads",
        )
        assertThrows<InvalidKeyFormatException> {
            runBlocking {
                currencyApiService.update(SimpleKey(dto.key.key), dto)
            }
        }
    }
}
