package com.tset.masterdata.unit

import com.nu.security.BomradsService
import com.nu.security.config.NuHeadersConfiguration
import com.tset.masterdata.configuration.ApplicationProperties
import com.tset.masterdata.configuration.security.TenantProvider
import com.tset.masterdata.configuration.security.TenantProviderImpl
import com.tset.masterdata.fixtures.security.TokenCreator
import com.tset.masterdata.service.ProvisioningService
import com.tset.masterdata.service.Tenant
import io.micrometer.tracing.Baggage
import io.micrometer.tracing.Tracer
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.Mockito
import org.springframework.http.HttpHeaders
import org.springframework.web.server.ResponseStatusException

class TokenProviderTest {

    private val expectedTenant = Tenant("tenant", null)

    private lateinit var tenantProvider: TenantProvider

    @BeforeEach
    fun prepareMocks() {
        val headerConfig = NuHeadersConfiguration()
        val tracer = Mockito.mock(Tracer::class.java)
        Mockito.`when`(tracer.getBaggage(headerConfig.envId)).thenReturn(Baggage.NOOP)
        val provisioningService = mockk<ProvisioningService>()
        coEvery { provisioningService.tenantExists(expectedTenant) } returns true

        tenantProvider = TenantProviderImpl(
            ApplicationProperties(publicAccess = false, autoProvision = false),
            headerConfig = headerConfig,
            tracer = tracer,
            provisioningService = provisioningService,
        )
    }

    @Test
    fun `user token without override works`() {
        val token = TokenCreator.createToken("user", expectedTenant.tenant, listOf("role1"))

        val tenant = tenantProvider.getTenant(token, HttpHeaders()).block()
        Assertions.assertEquals(expectedTenant.tenant, tenant?.tenant)
    }

    @Test
    fun `user token with same override works`() {
        val token = TokenCreator.createToken("user", expectedTenant.tenant, listOf("role1"))

        val headers = HttpHeaders()
        headers.add(BomradsService.NU_ACCOUNT_HEADER, expectedTenant.tenant)
        val tenant = tenantProvider.getTenant(token, headers).block()
        Assertions.assertEquals(expectedTenant.tenant, tenant?.tenant)
    }

    @Test
    fun `user token with different override ignores override`() {
        val token = TokenCreator.createToken("user", expectedTenant.tenant, listOf("role1"))

        val headers = HttpHeaders()
        headers.add(BomradsService.NU_ACCOUNT_HEADER, "other")

        val tenant = tenantProvider.getTenant(token, headers).block()
        Assertions.assertEquals(expectedTenant.tenant, tenant?.tenant)
    }

    @Test
    fun `service token with jwt account and without override works`() {
        val token = TokenCreator.createToken("user", expectedTenant.tenant, listOf("role1"), serviceToken = true)

        val tenant = tenantProvider.getTenant(token, HttpHeaders()).block()
        Assertions.assertEquals(expectedTenant.tenant, tenant?.tenant)
    }

    @Test
    fun `service token without jwt account and with override works`() {
        val token = TokenCreator.createToken("user", null, listOf("role1"), serviceToken = true)

        val headers = HttpHeaders()
        headers.add(BomradsService.NU_ACCOUNT_HEADER, expectedTenant.tenant)
        val tenant = tenantProvider.getTenant(token, headers).block()
        Assertions.assertEquals(expectedTenant.tenant, tenant?.tenant)
    }

    @Test
    fun `service token with jwt account and with different override works`() {
        val token = TokenCreator.createToken("user", "otherJwtAccount", listOf("role1"), serviceToken = true)

        val headers = HttpHeaders()
        headers.add(BomradsService.NU_ACCOUNT_HEADER, expectedTenant.tenant)
        val tenant = tenantProvider.getTenant(token, headers).block()
        Assertions.assertEquals(expectedTenant.tenant, tenant?.tenant)
    }

    @Test
    fun `service token without jwt account and without override throws`() {
        val token = TokenCreator.createToken("user", null, listOf("role1"), serviceToken = true)

        val headers = HttpHeaders()
        assertThrows<ResponseStatusException> {
            tenantProvider.getTenant(token, headers).block()
        }
    }
}
