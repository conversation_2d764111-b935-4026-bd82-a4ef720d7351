package com.tset.masterdata.apiservice

import com.ninjasquad.springmockk.MockkBean
import com.nu.masterdata.dto.v1.detail.*
import com.nu.masterdata.dto.v1.header.HeaderKeyType
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import com.tset.masterdata.assertInstanceOf
import com.tset.masterdata.configuration.security.PermissionProvider
import com.tset.masterdata.configuration.security.PermissionProviderTestImpl
import com.tset.masterdata.model.MaterialKey
import com.tset.masterdata.model.SimpleKey
import com.tset.masterdata.model.basicData.*
import com.tset.masterdata.model.fields.ClassificationFieldDefinition
import com.tset.masterdata.model.fields.FieldDefinition
import com.tset.masterdata.model.fields.LovFieldDefinition
import com.tset.masterdata.model.fields.NumericFieldDefinition
import com.tset.masterdata.model.headerdetails.ClassificationFieldValues
import com.tset.masterdata.model.headerdetails.Detail
import com.tset.masterdata.model.headerdetails.Header
import com.tset.masterdata.model.headertype.Effectivity
import com.tset.masterdata.model.headertype.EffectivityWithField
import com.tset.masterdata.model.headertype.HeaderType
import com.tset.masterdata.model.schema.*
import com.tset.masterdata.repository.HeaderTypeRepository
import com.tset.masterdata.service.DetailService
import com.tset.masterdata.service.HeaderService
import com.tset.masterdata.service.HeaderTypeService
import com.tset.masterdata.service.api.DetailsApiService
import com.tset.masterdata.service.exception.meta.EntityCode
import com.tset.masterdata.service.exception.meta.ErrorCode
import com.tset.masterdata.service.util.DynamicKeyMap
import com.tset.masterdata.service.util.KeyMap
import com.tset.masterdata.service.util.toList
import io.mockk.coEvery
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Import
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.time.LocalDate
import java.util.*

@ActiveProfiles("test", "local")
@ExtendWith(MockKExtension::class, SpringExtension::class)
@Import(PermissionProviderTestImpl::class)
class DetailApiServiceTest {
    @MockkBean
    private lateinit var detailService: DetailService

    @MockkBean
    private lateinit var headerTypeService: HeaderTypeService

    private lateinit var detailApiService: DetailsApiService

    @Autowired
    private lateinit var permissionProvider: PermissionProvider

    companion object {
        private val unit = NumericUnit(UUID.randomUUID(), SimpleKey("unit"), "unit", UUID.randomUUID(), 1.0)
        private val numerator = UnitMeasurementDto(unit.key.toDto(), unit.name)
        private val simpleHeaderType = HeaderType(
            UUID.randomUUID(),
            SimpleKey("header type"),
            HeaderKeyType.SIMPLE,
            "header type",
            true,
            1,
            detailValueSchema = NumericDetailValueSchema(
                NumericFieldSchema(UnitOfMeasurementType.scalarUnit(UnitType(unit.id!!))),
            ),
        )
        private fun genHeaders(n: Int) = (1..n).map { i ->
            Header(
                id = UUID.randomUUID(),
                key = if (i % 2 == 1) SimpleKey("HEADER $i") else MaterialKey("HEADER", "$i"),
                name = "header $i",
                headerTypeId = UUID.randomUUID(),
                active = true,
                null,
                ClassificationFieldValues(),
            )
        }

        private fun genLov(n: Int, nEntries: Int) = (1..n).map { i ->
            val type = LovType(id = UUID.randomUUID(), SimpleKey("LOV $i"), "", false)
            val entries = (1..nEntries).map { j ->
                LovEntry(
                    id = UUID.randomUUID(),
                    SimpleKey(type.key.key + " - $j"),
                    name = "",
                    shortName = "",
                    lovType = type.id!!,
                    false,
                )
            }
            type to entries
        }

        // Actual structure is irrelevant for mapping
        private fun genClassifications(n: Int, depth: Int) = (1..n).map { i ->
            val type = ClassificationType(id = UUID.randomUUID(), SimpleKey("CLASS $i"), "", false)
            var parentId: UUID? = null
            val nodes = (1..depth).map { j ->
                Classification(id = UUID.randomUUID(), SimpleKey(type.key.key + " - $j"), name = "", classificationType = type.id!!, parent = parentId, false)
                    .also { parentId = it.id!! }
            }
            type to nodes
        }

        private fun genDims(n: Int, nUnits: Int) = (1..n).map { i ->
            assert(nUnits > 0)
            val dimId = UUID.randomUUID()
            val dimKey = SimpleKey("DIM $i")
            val units = (1..nUnits).map { j ->
                NumericUnit(id = UUID.randomUUID(), SimpleKey(dimKey.key + " - $j"), name = "", dimensionId = dimId, factor = j.toDouble())
            }
            val dim = Dimension(id = dimId, dimKey, "", units.first().id!!)
            dim to units
        }

        private fun genCurrencies(n: Int) = (1..n).map { i ->
            val cur = NumericCurrency(id = UUID.randomUUID(), SimpleKey("DIM $i"), "", symbol = "")
            cur
        }

        @JvmStatic
        private fun arguments(): List<Arguments> = genInput().map { Arguments.of(it.first, it.second.build()) }

        private fun genInput(): List<Pair<List<FieldDefinition>, DynamicKeyMap.Builder>> {
            val n = 2
            val headers = genHeaders(n)
            val lov = genLov(n, n)
            val classifications = genClassifications(n, n)
            val dimensionId = UUID.randomUUID()
            val unitId = UUID.randomUUID()
            val factorId = UUID.randomUUID()
            val dims = genDims(n, n) +
                listOf(
                    Pair(
                        Dimension(dimensionId, SimpleKey("rate"), "rate", factorId),
                        listOf(
                            NumericUnit(factorId, SimpleKey("factor"), "factor", dimensionId, 1.0),
                            NumericUnit(UUID.randomUUID(), SimpleKey("percentage"), "percent", dimensionId, 100.0),
                        ),
                    ),
                )
            val currencies = genCurrencies(n)

            return headers.mapIndexed { i, header ->
                val effectivities = listOf(
                    LovFieldDefinition(UUID.randomUUID(), lov[i].first.key, false, LovFieldSchema(lovTypeId = lov[i].first.id!!, displayName = lov[i].first.key.key)),
                    ClassificationFieldDefinition(UUID.randomUUID(), classifications[i].first.key, false, ClassificationFieldSchema(classificationTypeId = classifications[i].first.id!!, displayName = classifications[i].first.key.key)),
                    NumericFieldDefinition(UUID.randomUUID(), dims[i].first.key, false, NumericFieldSchema(UnitOfMeasurementType.scalarUnit(DimensionType(dimensionId, unitId)), displayName = dims[i].first.key.key)),
                ).map { field ->
                    EffectivityWithField(
                        Effectivity(
                            UUID.randomUUID(),
                            field.id!!,
                            header.headerTypeId,
                            true,
                            true,
                            10,
                            dims[i].second.first().id!!.takeIf { field is NumericFieldDefinition },
                        ),
                        field,
                    )
                }
                Pair(
                    effectivities.map { it.field },
                    DynamicKeyMap.Builder()
                        .put(KeyMap.fromEntities(listOf(header)))
                        .put(KeyMap(effectivities, EntityCode.EFFECTIVITY))
                        .put(dims.map { it.first })
                        // include fixed value unit
                        .put(dims.flatMap { it.second } + unit)
                        .put(currencies)
                        .put(lov.map { it.first })
                        .put(lov.flatMap { it.second })
                        .put(classifications.map { it.first })
                        .put(classifications.flatMap { it.second }),
                )
            }
        }

        @JvmStatic
        private fun fieldMismatches() = listOf(
            Arguments.of("DIM 1", LovValueDto(SimpleKeyDto("a"))),
            Arguments.of("DIM 1", ClassificationValueDto(SimpleKeyDto("a"))),
            Arguments.of("DIM 1", TextValueDto("a")),
            Arguments.of("DIM 1", DateValueDto(LocalDate.of(2000, 1, 1))),
            Arguments.of("LOV 1", NumericValueDto(1.0, UnitMeasurementDto(SimpleKeyDto("a")))),
        )

        @JvmStatic
        private fun fieldValueInvalidRef() = listOf(
            Arguments.of("LOV 1", LovValueDto(SimpleKeyDto("LOV 2 - 2"))),
            Arguments.of("CLASS 1", ClassificationValueDto(SimpleKeyDto("CLASS 2 - 1"))),
        )

        @JvmStatic
        private fun fieldValueKeyNotExist() = listOf(
            Arguments.of("LOV 1", LovValueDto(SimpleKeyDto("LOV 2 - 3"))),
            Arguments.of("CLASS 1", ClassificationValueDto(SimpleKeyDto("CLASS 2 - 3"))),
            Arguments.of("DIM 1", NumericValueDto(1.0, SimpleKeyDto("DIM 4 - 2"))),
        )
    }

    @BeforeEach
    fun beforeEach() {
        // Springmockk could not handle creating this automatically
        // Root source seems to be related to [HeaderTypeService] subclassing a generic type with constructor or something
        val repo = mockk<HeaderTypeRepository>()
        headerTypeService = HeaderTypeService(repo)
        coEvery { headerTypeService.getByKeyForUpdate(any()) } returns simpleHeaderType

        coEvery { detailService.getPostMappingData(any(), any()) } returns genInput().first().second.build()
        coEvery { detailService.saveAll(any(), any()) } answers {
            firstArg<List<Detail>>()
        }
        coEvery { detailService.getAllSystemManagedFields() } answers { emptyFlow() }

        detailApiService = DetailsApiService(detailService, headerTypeService, mockk<HeaderService>(), permissionProvider)
    }

    @ParameterizedTest
    @MethodSource("arguments")
    fun `missing effectivities are filled`(expected: List<FieldDefinition>, keyMap: DynamicKeyMap) = runBlocking {
        coEvery { detailService.getPostMappingData(any(), any()) } returns keyMap
        val dto = DetailDto(
            emptyMap(),
            keyMap.headers.values.first().key.toDto(),
            NumericDetailValueDto(100.0, numerator, valueInBaseSiUnit = 100.0),
            true,
        )

        val res = detailApiService.postDetails(SimpleKey("A"), flowOf(dto)).okAndGet()
        val expectedDetails =
            listOf(dto.copy(effectivities = expected.associate { it.key.toDto() to null }))
                .map(::DetailBulkResponseSuccessDto)

        assertEquals(expectedDetails, res)
    }

    @Test
    fun `header not found`() = runBlocking {
        val dto = DetailDto(
            emptyMap(),
            SimpleKeyDto("A"),
            NumericDetailValueDto(100.0, numerator),
            true,
        )

        val res = detailApiService.postDetails(SimpleKey("A"), flowOf(dto)).errAndGet()

        assertErr(
            res[0],
            ErrAssert(ErrorCode.MD_REFERENCED_KEY_NOT_FOUND, listOf(0 to EntityCode.HEADER)),
        )
    }

    @Test
    fun `effectivity not part of header`() = runBlocking {
        val dto = DetailDto(
            mapOf(
                SimpleKeyDto("DIM 2") to null,
            ),
            SimpleKeyDto("HEADER 1"),
            NumericDetailValueDto(100.0, numerator),
            true,
        )

        val res = detailApiService.postDetails(SimpleKey("A"), flowOf(dto)).errAndGet()

        assertErr(
            res[0],
            ErrAssert(ErrorCode.MD_REFERENCED_KEY_NOT_FOUND, listOf(1 to SimpleKey("DIM 2"))),
        )
    }

    @ParameterizedTest
    @MethodSource("fieldMismatches")
    fun `field value type mismatch`(eff: SimpleKeyDto, value: FieldValueDto) = runBlocking {
        val dto = DetailDto(
            mapOf(eff to value),
            SimpleKeyDto("HEADER 1"),
            NumericDetailValueDto(100.0, numerator),
            true,
        )

        val res = detailApiService.postDetails(SimpleKey("A"), flowOf(dto)).errAndGet()

        assertErr(
            res[0],
            ErrAssert(ErrorCode.MD_MISMATCHED_FIELD_VALUE_TYPE),
        )
    }

    @ParameterizedTest
    @MethodSource("fieldValueInvalidRef")
    fun `eff value key does not reference right type`(eff: SimpleKeyDto, value: FieldValueDto) = runBlocking {
        val dto = DetailDto(
            mapOf(eff to value),
            SimpleKeyDto("HEADER 1"),
            NumericDetailValueDto(100.0, numerator),
            true,
        )

        val res = detailApiService.postDetails(SimpleKey("A"), flowOf(dto)).errAndGet()

        assertErr(
            res[0],
            ErrAssert(ErrorCode.MD_MISMATCHED_TYPE),
        )
    }

    @Test
    fun `eff value has incompatible unit`() = runBlocking {
        val value = NumericValueDto(1.0, UnitMeasurementDto(SimpleKeyDto("DIM 2 - 2")))
        val dto = DetailDto(
            mapOf(SimpleKeyDto("DIM 1") to value),
            SimpleKeyDto("HEADER 1"),
            NumericDetailValueDto(100.0, numerator),
            true,
        )

        val res = detailApiService.postDetails(SimpleKey("A"), flowOf(dto)).errAndGet()
        assertErr(res[0], ErrAssert(ErrorCode.MD_GENERIC_UNIT_MISMATCH))
    }

    @ParameterizedTest
    @MethodSource("fieldValueKeyNotExist")
    fun `eff value key does not exist`(eff: SimpleKeyDto, value: FieldValueDto) = runBlocking {
        val dto = DetailDto(
            mapOf(eff to value),
            SimpleKeyDto("HEADER 1"),
            NumericDetailValueDto(100.0, numerator),
            true,
        )

        val res = detailApiService.postDetails(SimpleKey("A"), flowOf(dto)).errAndGet()

        assertErr(
            res[0],
            ErrAssert(ErrorCode.MD_REFERENCED_KEY_NOT_FOUND),
        )
    }

    @Test
    fun `duplicate key same header`() = runBlocking {
        val dto = DetailDto(
            emptyMap(),
            SimpleKeyDto("HEADER 1"),
            NumericDetailValueDto(100.0, numerator),
            true,
        )

        val res = detailApiService.postDetails(SimpleKey("A"), flowOf(dto, dto)).errAndGet()

        assertErr(res[0], ErrAssert(ErrorCode.MD_DUPLICATE_EFFECTIVITIES))
        assertErr(res[1], ErrAssert(ErrorCode.MD_DUPLICATE_EFFECTIVITIES))
    }

    @Test
    fun `duplicate key diff header`() = runBlocking {
        val keyMap = (genInput().first().second).let { builder ->
            val header = genHeaders(1).single()
            val headers = listOf(header, header.copy(id = UUID.randomUUID(), key = SimpleKey("HEADER 2")))
            builder
                .put(KeyMap.fromEntities(headers))
                .build()
        }
        coEvery { detailService.getPostMappingData(any(), any()) } returns keyMap
        val dto = DetailDto(
            emptyMap(),
            SimpleKeyDto("HEADER 1"),
            NumericDetailValueDto(100.0, numerator),
            true,
        )
        val dtos = flowOf(
            dto,
            dto.copy(headerKey = SimpleKeyDto("HEADER 2")),
        )

        assertEquals(HttpStatus.OK, detailApiService.postDetails(SimpleKey("A"), dtos).statusCode)
    }

    @Test
    fun `partial success`() = runBlocking {
        val mappingData = genInput().first().second.build()
        coEvery { detailService.getPostMappingData(any(), any()) } returns mappingData
        val header = mappingData.headers.values.first()
        val dtos = listOf(
            DetailDto(
                mapOf(SimpleKeyDto("LOV 1") to null),
                header.key.toDto(),
                NumericDetailValueDto(100.0, numerator),
                true,
            ),
            DetailDto(
                mapOf(SimpleKeyDto("LOV 2") to null),
                header.key.toDto(),
                NumericDetailValueDto(100.0, numerator),
                true,
            ),
        )

        val res = detailApiService.postDetails(SimpleKey("A"), dtos.asFlow()).errAndGet()

        assertInstanceOf<DetailBulkResponseSuccessDto>(res[0])
        val err = assertInstanceOf<DetailBulkResponseErrorDto>(res[1])
        assertEquals(1, err.errors.size)
        assertEquals(ErrorCode.MD_REFERENCED_KEY_NOT_FOUND.name, err.errors[0].userErrorCode)
    }
}

private suspend fun <T> ResponseEntity<Flow<T>>.okAndGet(): List<T> {
    val res = body!!.toList()
    assertEquals(HttpStatus.OK, statusCode) {
        res.filterIsInstance<DetailBulkResponseErrorDto>()
            .map { it.errors }
            .joinToString("\n")
    }
    return res
}

private data class ErrAssert(
    val code: ErrorCode,
    val parameters: List<Pair<Int, Any>> = emptyList(),
)

private suspend fun <T> ResponseEntity<Flow<T>>.errAndGet(): List<T> {
    assertEquals(HttpStatus.UNPROCESSABLE_ENTITY, statusCode)
    return body!!.toList()
}

private fun assertErr(dto: DetailBulkResponseDto, vararg errors: ErrAssert) {
    val err = assertInstanceOf<DetailBulkResponseErrorDto>(dto)
    assertEquals(errors.map { it.code.name }, err.errors.map { it.userErrorCode })
    errors
        .map { it.parameters }
        .zip(err.errors)
        .forEach { (params, err) ->
            params.forEach { (idx, param) ->
                assertEquals(param, err.userParameters[idx])
            }
        }
}
