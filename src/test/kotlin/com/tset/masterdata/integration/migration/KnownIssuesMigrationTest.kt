package com.tset.masterdata.integration.migration

import com.tset.masterdata.repository.HeaderTypeRepository
import com.tset.masterdata.service.util.toList
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class KnownIssuesMigrationTest @Autowired constructor(
    private val headerTypeRepository: HeaderTypeRepository,
) : BaseMigrationTest() {

    companion object {
        const val SCHEMA: String = "knownissuesschema"
    }

    /**
     * covers issues when migrating headertypes without effectivities
     */
    @Test
    fun headertypeWithoutEffectivities() {
        val allHeaderTypes = runBlocking {
            withSchema(SCHEMA).use {
                createSchemaWithData(SCHEMA, "no-effectivities-header.sql")
                headerTypeRepository.findAll().toList()
            }
        }
        // unexpected number of header types? - Did you add a new tset HeaderType - then add it here
        Assertions.assertThat(allHeaderTypes.map { it.key.key })
            .containsExactlyInAnyOrder(
                "no-effectivities",
                "period",
                "exchange-rate",
                "overhead",
                "tset.ref.header-type.location-factor",
                "tset.ref.header-type.material",
                "tset.ref.header-type.machine",
            )
    }
}
