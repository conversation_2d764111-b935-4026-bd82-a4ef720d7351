package com.tset.masterdata.integration.lookup

import com.fasterxml.jackson.databind.ObjectMapper
import com.nu.masterdata.dto.v1.detail.*
import com.nu.masterdata.dto.v1.header.HeaderKeyType
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import com.nu.masterdata.dto.v1.lookup.request.Effectivity
import com.nu.masterdata.dto.v1.lookup.request.MasterdataLookupRequest
import com.tset.masterdata.assertIsCloseTo
import com.tset.masterdata.fixtures.EffectivityFixtures
import com.tset.masterdata.fixtures.HeaderFixtures
import com.tset.masterdata.fixtures.HeaderTypeFixtures
import com.tset.masterdata.fixtures.LookupStrategyFixtures
import com.tset.masterdata.fixtures.basicData.UnitFixtures
import com.tset.masterdata.fixtures.fields.FieldDefinitionFixtures
import com.tset.masterdata.integration.base.BaseIntegrationTest
import com.tset.masterdata.model.SimpleKey
import com.tset.masterdata.model.fields.fieldvalues.ClassificationValue
import com.tset.masterdata.model.fields.fieldvalues.LovValue
import com.tset.masterdata.model.fields.fieldvalues.NumericValue
import com.tset.masterdata.model.fields.ids.MultiFieldEffectivityKey
import com.tset.masterdata.model.headerdetails.Detail
import com.tset.masterdata.model.headerdetails.DetailWithBasicAuditInfo
import com.tset.masterdata.model.headerdetails.MeasurementFraction
import com.tset.masterdata.model.headerdetails.UnitMeasurement
import com.tset.masterdata.model.headerdetails.detailvalues.NumericDetailValue
import com.tset.masterdata.repository.DetailRepository
import com.tset.masterdata.service.*
import com.tset.masterdata.service.api.DetailsLookupApiService
import com.tset.masterdata.service.api.HeaderApiService
import com.tset.masterdata.service.basicData.*
import com.tset.masterdata.service.exception.InvalidUnitException
import com.tset.masterdata.service.lookup.LookupRequest
import com.tset.masterdata.service.lookup.LookupService
import com.tset.masterdata.service.mapper.ExtendedKeyMapForDetail
import com.tset.masterdata.service.util.DynamicKeyMap
import com.tset.masterdata.service.util.mapToSet
import com.tset.masterdata.service.util.toBaseUnit
import com.tset.masterdata.util.toDetailValueSchema
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service
import java.time.Instant

// TODO We should mock the underling DetailsService instead of doing this
@Service
@Profile("test")
class DetailsLookupApiServiceForTest(
    lookupService: LookupService,
    lovEntryService: LovEntryService,
    classificationService: ClassificationService,
    headerTypeService: HeaderTypeService,
    lookupStrategyService: LookupStrategyService,
    headerService: HeaderService,
    detailRepo: DetailRepository,
    effectivityService: EffectivityService,
    dimensionService: DimensionService,
    unitService: UnitService,
    currencyService: CurrencyService,
    detailService: DetailService,
    objectMapper: ObjectMapper,
    lovTypeService: LovTypeService,
    headerApiService: HeaderApiService,
    basicDataFetcherService: BasicDataFetcherService,
) : DetailsLookupApiService(
    lookupService,
    lovEntryService,
    classificationService,
    headerTypeService,
    lookupStrategyService,
    headerService,
    detailRepo,
    effectivityService,
    dimensionService,
    unitService,
    currencyService,
    detailService,
    objectMapper,
    lovTypeService,
    headerApiService,
    basicDataFetcherService,
) {
    suspend fun loadMappingDataForTest(masterdataLookupRequest: MasterdataLookupRequest): MappingData {
        return super.loadMappingData(masterdataLookupRequest)
    }

    suspend fun createRequestForTest(
        mappingData: MappingData,
        effectivities: List<Effectivity>,
    ): List<LookupRequest> {
        return super.createRequests(mappingData, effectivities)
    }

    suspend fun createResponseItemsForTest(
        mappingData: MappingData,
        details: List<DetailWithBasicAuditInfo>,
        measures: DynamicKeyMap,
    ): List<DetailDto> {
        return super.createResponseItems(mappingData, details, ExtendedKeyMapForDetail(measures, emptyList()))
    }
}
class DetailLookupApiServiceIntegrationTest @Autowired constructor(
    private val detailsLookupApiService: DetailsLookupApiServiceForTest,
    private val fieldDefinitionFixtures: FieldDefinitionFixtures,
    private val effectivityFixtures: EffectivityFixtures,
    private val headerTypeFixtures: HeaderTypeFixtures,
    private val headerFixtures: HeaderFixtures,
    private val unitFixtures: UnitFixtures,
    private val lookupStrategyFixtures: LookupStrategyFixtures,
) : BaseIntegrationTest() {

    private lateinit var htMeasure: MeasurementFraction

    @ParameterizedTest
    @ValueSource(booleans = [ true, false ])
    fun `check lookup request and response dto conversions`(testInvalidUnit: Boolean) = test {
        val sectorLovFieldKey = "sector"
        val classificationFieldKey = "myClassification"
        val headerTypeKey = "my-overhead"
        val strategyKey = "my-strategy"
        val header1Key = "header1Key"
        val header2Key = "header2Key"
        val (sectorLovField, sectorLovInfo) = fieldDefinitionFixtures.createOneLovWithEntries(
            SimpleKey(
                sectorLovFieldKey,
            ),
            5,
        )
        val sectorLovEntry = sectorLovInfo.entries.first()

        val (classificationFieldDefinition, classificationFixtureInfo) = fieldDefinitionFixtures.createOneClassificationWithTree(
            SimpleKey(classificationFieldKey),
        )
        val selectedClassification = classificationFixtureInfo.nodes.first()

        val numericFieldKey = "numeric"
        val unitInfo = unitFixtures.createBase()
        val otherUnitInfo = unitFixtures.createBase("other", "other")
        val factor = 100.0
        val selectedUnit = unitInfo.units.first { it.factor == factor }
        val baseUnit = unitInfo.units.first { it.id == unitInfo.dimension.baseUnitId }
        val otherSelectedUnit = otherUnitInfo.units.first { it.factor == factor }
        val numericField = fieldDefinitionFixtures.createNumericFieldDefinition(
            SimpleKey(numericFieldKey),
            "$numericFieldKey-name",
            unitInfo.dimension.id!!,
        )
        val numericFieldValue = 0.345

        htMeasure = MeasurementFraction(UnitMeasurement(selectedUnit.id!!))
        val headerType = headerTypeFixtures.createHeaderType(
            HeaderKeyType.SIMPLE,
            headerTypeKey,
            true,
            23,
            detailValueSchema = htMeasure.toDetailValueSchema(),
        )
        val headerTypeId = headerType.id!!
        val sectorEffectivity = effectivityFixtures.createEffectivity(sectorLovField.id!!, headerTypeId, null)
        val classificationEffectivity = effectivityFixtures.createEffectivity(classificationFieldDefinition.id!!, headerTypeId, null)
        val numericEffectivity = effectivityFixtures.createEffectivity(numericField.id!!, headerTypeId, selectedUnit.id)

        val strategy =
            lookupStrategyFixtures.createLookupStrategy(SimpleKey(strategyKey), "my-strategy-name", headerTypeId)

        val header1 = headerFixtures.createHeader(SimpleKey(header1Key), "$header1Key-name", headerTypeId, true)
        val header2 = headerFixtures.createHeader(SimpleKey(header2Key), "$header2Key-name", headerTypeId, true)

        val unitForReq = if (testInvalidUnit) otherSelectedUnit else selectedUnit
        val lookupRequest = MasterdataLookupRequest(
            strategyKey = SimpleKeyDto(strategyKey),
            headerTypeKey = SimpleKeyDto(headerTypeKey),
            headerKeys = listOf(SimpleKeyDto(header1Key), SimpleKeyDto(header2Key)),
            effectivities = listOf(
                Effectivity(SimpleKeyDto(sectorLovFieldKey), LovValueDto(sectorLovEntry.key.toDto())),
                Effectivity(SimpleKeyDto(classificationFieldKey), ClassificationValueDto(selectedClassification.key.toDto())),
                Effectivity(SimpleKeyDto(numericFieldKey), NumericValueDto(numericFieldValue, unitForReq.key.toDto())),
            ),
            timestampEpochMillis = 100000,
        )

        val mappingData = detailsLookupApiService.loadMappingDataForTest(lookupRequest)

        Assertions.assertEquals(strategy.id, mappingData.strategyId)
        Assertions.assertEquals(headerType.id, mappingData.headerType.id!!)
        Assertions.assertEquals(sectorLovEntry.id, mappingData.usedLovEntries.single().id)
        Assertions.assertEquals(selectedClassification.id, mappingData.usedClassifications.single().id)
        Assertions.assertEquals(setOf(header1.id, header2.id), mappingData.headers.mapToSet { it.id })
        val sector = mappingData.effectivitiesWithField.single { it.effectivity.id == sectorEffectivity.id }
        Assertions.assertEquals(sectorLovField.id, sector.field.id)
        val classification = mappingData.effectivitiesWithField.single { it.effectivity.id == classificationEffectivity.id }
        Assertions.assertEquals(classificationFieldDefinition.id, classification.field.id)
        val numeric = mappingData.effectivitiesWithField.single { it.effectivity.id == numericEffectivity.id }
        Assertions.assertEquals(numericField.id, numeric.field.id)

        if (testInvalidUnit) {
            assertThrows<InvalidUnitException> { detailsLookupApiService.createRequestForTest(mappingData, lookupRequest.effectivities) }
        } else {
            val lookupRequests = detailsLookupApiService.createRequestForTest(mappingData, lookupRequest.effectivities)

            Assertions.assertEquals(2, lookupRequests.size)

            lookupRequests.forEachIndexed { _, request ->
                Assertions.assertTrue(listOf(header1.id!!, header2.id!!).contains(request.headerId))
                Assertions.assertEquals(3, request.effectivities.size)
                val sectorRequest = request.effectivities.single { sectorLovField.id == it.fieldId }
                Assertions.assertEquals(sectorLovEntry.id, (sectorRequest.value as LovValue).value)
                val classificationRequest = request.effectivities.single { classificationFieldDefinition.id == it.fieldId }
                Assertions.assertEquals(selectedClassification.id, (classificationRequest.value as ClassificationValue).value)
                val numericRequest = request.effectivities.single { numericField.id == it.fieldId }
                Assertions.assertEquals(numericFieldValue * factor, (numericRequest.value as NumericValue).value)
            }

            val detail1TimeStamp = Instant.parse("2023-01-01T10:15:30.123Z")
            val detail1Rate = 3.0
            val detail2TimeStamp = Instant.parse("2023-02-02T10:15:30.123Z")
            val detail2Rate = 4.0
            val numericEffectivityId = numericEffectivity.id!!
            val key = MultiFieldEffectivityKey(
                effectivities = mapOf(
                    sectorEffectivity.id!! to LovValue(sectorLovEntry.id!!),
                    classificationEffectivity.id!! to ClassificationValue(selectedClassification.id!!),
                    numericEffectivityId to NumericValue(numericFieldValue.toBaseUnit(selectedUnit.factor), null),
                ),
            )
            val details = listOf(
                DetailWithBasicAuditInfo(
                    Detail(
                        versionTimestamp = detail1TimeStamp,
                        key = key,
                        headerId = header1.id!!,
                        value = NumericDetailValue(detail1Rate.toBaseUnit(selectedUnit), htMeasure),
                        active = true,
                    ),
                    "modifier1",
                    Instant.parse("2023-02-01T10:15:30.123Z"),
                ),

                DetailWithBasicAuditInfo(
                    Detail(
                        versionTimestamp = detail2TimeStamp,
                        key = key,
                        headerId = header2.id!!,
                        value = NumericDetailValue(detail2Rate.toBaseUnit(selectedUnit), htMeasure),
                        active = true,
                    ),
                    "modifier2",
                    Instant.parse("2023-03-01T10:15:30.123Z"),
                ),
            )

            val dynamicKeyMap =
                DynamicKeyMap.Builder()
                    .put(listOf(selectedUnit, baseUnit))
                    .put(listOf(unitInfo.dimension))
                    .put(listOf(sectorLovEntry))
                    .put(listOf(selectedClassification))
                    .build()
            val responseItems = detailsLookupApiService.createResponseItemsForTest(mappingData, details, dynamicKeyMap)
            val responseItemsList = responseItems.toList()

            Assertions.assertEquals(2, responseItemsList.size)
            responseItemsList.forEach { responseItem ->
                Assertions.assertEquals(
                    mapOf(
                        Pair(sectorLovField.key.toDto(), LovValueDto(sectorLovEntry.key.toDto(), sectorLovEntry.name)),
                        Pair(classificationFieldDefinition.key.toDto(), ClassificationValueDto(selectedClassification.key.toDto(), selectedClassification.name)),
                        Pair(numericField.key.toDto(), NumericValueDto(numericFieldValue, selectedUnit.toMeasurementDto())),
                    ),
                    responseItem.effectivities,
                )
            }

            val responseItemForHeader1 = responseItemsList.single { it.headerKey == header1.key.toDto() }
            val responseItemForHeader2 = responseItemsList.single { it.headerKey == header2.key.toDto() }
            val header1Value = (responseItemForHeader1.value as NumericDetailValueDto)
            val header2Value = (responseItemForHeader2.value as NumericDetailValueDto)

            assertIsCloseTo(detail1Rate.toBaseUnit(selectedUnit), header1Value.value)
            Assertions.assertEquals(baseUnit.toMeasurementDto(), header1Value.numerator)
            Assertions.assertNull(header1Value.denominator)
            Assertions.assertEquals("modifier1", responseItemForHeader1.modifier)

            assertIsCloseTo(detail2Rate.toBaseUnit(selectedUnit), header2Value.value)
            Assertions.assertEquals(baseUnit.toMeasurementDto(), header2Value.numerator)
            Assertions.assertNull(header2Value.denominator)
            Assertions.assertEquals("modifier2", responseItemForHeader2.modifier)
        }
    }
}
