package com.tset

import com.tset.validator.execution.parseCommaSeparatedStrings
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

class ParseCommaSeparatedStringsTest {

    @Test
    fun `empty string results in no ids`() {
        Assertions.assertEquals(emptySet<String>(), parseCommaSeparatedStrings(""))
        Assertions.assertEquals(emptySet<String>(), parseCommaSeparatedStrings(" "))
        Assertions.assertEquals(emptySet<String>(), parseCommaSeparatedStrings(","))
    }

    @Test
    fun `white spaces are ignored`() {
        Assertions.assertEquals(emptySet<String>(), parseCommaSeparatedStrings(" "))
        Assertions.assertEquals(setOf("a", "b", "c   d"), parseCommaSeparatedStrings(" a, b  ,   c   d "))
        Assertions.assertEquals(setOf("a"), parseCommaSeparatedStrings(" a "))
    }

    @Test
    fun `additional commas are ignored`() {
        Assertions.assertEquals(emptySet<String>(), parseCommaSeparatedStrings(" ,,,, "))
        Assertions.assertEquals(setOf("a", "b", "cd"), parseCommaSeparatedStrings("a, b,, cd,,"))
    }

    @Test
    fun `single id is parsed correctly`() {
        Assertions.assertEquals(setOf("a"), parseCommaSeparatedStrings("a"))
    }

    @Test
    fun `several ids are parsed correctly`() {
        Assertions.assertEquals(setOf("a", "f"), parseCommaSeparatedStrings("a, f,"))
    }

    @Test
    fun `several same ids are parsed correctly`() {
        Assertions.assertEquals(setOf("a"), parseCommaSeparatedStrings("a, a"))
        Assertions.assertEquals(setOf("a", "b"), parseCommaSeparatedStrings("a, b, a,"))
    }
}
