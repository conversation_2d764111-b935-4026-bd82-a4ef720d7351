package com.tset.qa.api.masterdataservice.basicdata.classification;

import api.endpointactions.masterdataservice.basicdata.ClassificationTypeControllerActions;
import api.entities.generators.masterdataservice.MasterDataServiceDataGenerator;
import com.nu.masterdata.dto.v1.basicdata.ClassificationDto;
import com.nu.masterdata.dto.v1.basicdata.ClassificationFieldResponseDto;
import com.nu.masterdata.dto.v1.basicdata.ClassificationTypeDto;
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto;
import com.tset.qa.api.masterdataservice.MasterDataServiceBaseTest;
import io.restassured.common.mapper.TypeRef;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.http.HttpStatus;
import org.assertj.core.api.SoftAssertions;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;

public abstract class ClassificationBaseTest extends MasterDataServiceBaseTest {

    protected static final TypeRef<List<ClassificationDto>> LIST_OF_CLASSIFICATION = new TypeRef<>() {
    };

    protected static final TypeRef<Map<SimpleKeyDto, ClassificationFieldResponseDto>> CLASSIFICATION_FIELDS_RESPONSE = new TypeRef<>() {
    };

    protected ClassificationTypeDto classificationType1;
    protected ClassificationTypeDto classificationType2;

    @BeforeMethod(alwaysRun = true)
    public void createClassificationTypes() {
        softAssert = new SoftAssertions();
        classificationType1 = MasterDataServiceDataGenerator.classificationType();
        classificationType2 = MasterDataServiceDataGenerator.classificationType();
        createClassificationType(classificationType1);
        createClassificationType(classificationType2);
    }

    @AfterMethod(alwaysRun = true)
    public void cleanupClassificationType() {
        List<ClassificationDto> entries = ClassificationTypeControllerActions.getEntriesOfClassificationType(getAccessToken(),
                        classificationType1.getKey().getKey(), null, true)
                .as(LIST_OF_CLASSIFICATION);
        getOrderedCleanupList(entries).forEach(u -> deleteClassification(u.getKey()));

        entries = ClassificationTypeControllerActions.getEntriesOfClassificationType(getAccessToken(),
                        classificationType2.getKey().getKey(), null, true)
                .as(LIST_OF_CLASSIFICATION);
        getOrderedCleanupList(entries).forEach(u -> deleteClassification(u.getKey()));

        if (ClassificationTypeControllerActions.getClassificationType(getAccessToken(), classificationType1.getKey().getKey()).getStatusCode() != HttpStatus.SC_NOT_FOUND) {
            deleteClassificationType(classificationType1.getKey().getKey());
        }
        if (ClassificationTypeControllerActions.getClassificationType(getAccessToken(), classificationType2.getKey().getKey()).statusCode() != HttpStatus.SC_NOT_FOUND) {
            deleteClassificationType(classificationType2.getKey().getKey());
        }
    }

    /**
     * simple conversion to return the child nodes first (order in which classification can be deleted correctly)
     */
    protected List<SimpleKeyDto> getOrderedCleanupList(List<ClassificationDto> unorderedNodes) {
        var childToParent = new HashMap<SimpleKeyDto, SimpleKeyDto>();
        var parentToChildren = new HashMap<SimpleKeyDto, ArrayList<SimpleKeyDto>>();
        unorderedNodes.forEach(n -> parentToChildren.put(n.getKey(), new ArrayList<>()));
        unorderedNodes.forEach(node -> {
                    childToParent.put(node.getKey(), node.getParentClassificationKey());
                    if (node.getParentClassificationKey() != null) {
                        parentToChildren.get(node.getParentClassificationKey()).add(node.getKey());
                    }
                }
        );
        ArrayList<SimpleKeyDto> target = new ArrayList<>(unorderedNodes.size());
        var nextBatch = new ArrayList<SimpleKeyDto>();
        while (target.size() < unorderedNodes.size()) {
            for (var en : parentToChildren.entrySet()) {
                if (en.getValue().isEmpty()) {
                    nextBatch.add(en.getKey());
                }
            }
            parentToChildren.entrySet().removeIf(entry -> entry.getValue().isEmpty());
            for (var toRemoveNext : nextBatch) {
                target.add(toRemoveNext);
                var parentOfEn = childToParent.get(toRemoveNext);
                if (parentOfEn != null) {
                    parentToChildren.get(parentOfEn).remove(toRemoveNext);
                }
            }
            nextBatch.clear();

        }
        return target;
    }

    /**
     * @formatter:off
     *
     * Creates a Classification tree in the Form of
     *
     * root
     * |
     * -----------------
     * |               |
     * rC1             rC2
     * |               |
     * ---------       |
     * |       |       |
     * rC1C1   rC1C2   rC2C1
     *         |
     *         rC1C2C1
     *
     * @return root node
     *
     * @formatter:on
     */
    protected MultiLevelTree initMultiLevelTree(SimpleKeyDto classificationTypeKey) {
        var root = MasterDataServiceDataGenerator.classification(classificationTypeKey, null);
        var rC1 = MasterDataServiceDataGenerator.classification(classificationTypeKey, root.getKey());
        var rC2 = MasterDataServiceDataGenerator.classification(classificationTypeKey, root.getKey());
        var rC1C1 = MasterDataServiceDataGenerator.classification(classificationTypeKey, rC1.getKey());
        var rC1C2 = MasterDataServiceDataGenerator.classification(classificationTypeKey, rC1.getKey());
        var rC2C1 = MasterDataServiceDataGenerator.classification(classificationTypeKey, rC2.getKey());
        var rC1C2C1 = MasterDataServiceDataGenerator.classification(classificationTypeKey, rC1C2.getKey());
        createClassification(root);
        createClassification(rC1);
        createClassification(rC2);
        createClassification(rC1C1);
        createClassification(rC1C2);
        createClassification(rC2C1);
        createClassification(rC1C2C1);

        return new MultiLevelTree(root, rC1, rC2, rC1C1, rC1C2, rC2C1, rC1C2C1);
    }

    protected record MultiLevelTree(ClassificationDto root, ClassificationDto rC1, ClassificationDto rC2, ClassificationDto rC1C1,
                                    ClassificationDto rC1C2, ClassificationDto rC2C1, ClassificationDto rC1C2C1) {
    }
}
