package com.tset.qa.api.masterdataservice.overhead;

import api.endpointactions.masterdataservice.basicdata.DimensionControllerActions;
import api.endpointactions.masterdataservice.overhead.HeaderControllerActions;
import api.entities.generators.masterdataservice.MasterDataServiceDataGenerator;
import com.nu.masterdata.dto.v1.basicdata.DimensionDto;
import com.nu.masterdata.dto.v1.basicdata.DimensionUnitDto;
import com.nu.masterdata.dto.v1.header.HeaderDto;
import com.nu.masterdata.dto.v1.header.HeaderTypeDto;
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto;
import com.nu.masterdata.dto.v1.schema.*;
import com.tset.qa.api.masterdataservice.MasterDataServiceBaseTest;
import io.restassured.common.mapper.TypeRef;
import org.assertj.core.api.SoftAssertions;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;

import java.util.Collections;
import java.util.List;

public class HeaderBaseTest extends MasterDataServiceBaseTest {

    protected static final TypeRef<List<HeaderDto>> LIST_OF_HEADERS = new TypeRef<>() {
    };

    protected HeaderTypeDto headerType1;
    protected HeaderTypeDto headerType2;
    protected HeaderTypeDto headerTypeWOMeasurement;

    protected DimensionDto dimension;

    protected DimensionUnitDto unit;

    @BeforeClass
    public void createHeaderTypes() {
        dimension = MasterDataServiceDataGenerator.dimension();
        createDimension(dimension);
        unit = dimension.getUnits().get(0);

        NumericDetailValueSchemaDto valueSchema = new NumericDetailValueSchemaDto(
                new NumericFieldSchemaDto(new UnitOfMeasurementTypeDto(
                        new DimensionTypeDto(dimension.getKey(), null, unit.getKey(), null),
                        new WithoutUnitOfMeasurementTypeDto()
                ), null)
        );
        headerType1 = MasterDataServiceDataGenerator.headerType(Collections.emptyMap(), valueSchema);
        headerType2 = MasterDataServiceDataGenerator.headerType(Collections.emptyMap(), valueSchema);
        headerTypeWOMeasurement = MasterDataServiceDataGenerator.headerType(Collections.emptyMap(), null);
        createHeaderType(headerType1);
        createHeaderType(headerType2);
        createHeaderType(headerTypeWOMeasurement);
    }

    @BeforeMethod(alwaysRun = true)
    public void testSetup() {
        softAssert = new SoftAssertions();
    }

    @AfterClass(alwaysRun = true)
    public void cleanupHeaderTypes() {
        List<HeaderDto> headers;
        if (headerType1 != null) {
            headers = HeaderControllerActions.getHeaders(getAccessToken(), headerType1.getKey(), headerType1.getKey().getKey())
                    .as(LIST_OF_HEADERS);
            headers.forEach(header -> deleteHeader(headerType1.getKey(), ((SimpleKeyDto) header.getKey()).getKey()));
            deleteHeaderType(headerType1.getKey().getKey());
        }
        if (headerType2 != null) {
            headers = HeaderControllerActions.getHeaders(getAccessToken(), headerType2.getKey(), headerType2.getKey().getKey())
                    .as(LIST_OF_HEADERS);
            headers.forEach(header -> deleteHeader(headerType2.getKey(), ((SimpleKeyDto) header.getKey()).getKey()));
            deleteHeaderType(headerType2.getKey().getKey());
        }
        if (headerTypeWOMeasurement != null) {
            headers = HeaderControllerActions.getHeaders(getAccessToken(), headerTypeWOMeasurement.getKey(), headerTypeWOMeasurement.getKey().getKey())
                    .as(LIST_OF_HEADERS);
            headers.forEach(header -> deleteHeader(headerTypeWOMeasurement.getKey(), ((SimpleKeyDto) header.getKey()).getKey()));
            deleteHeaderType(headerTypeWOMeasurement.getKey().getKey());
        }
        if (dimension != null) {
            DimensionControllerActions.deleteDimension(getAccessToken(), dimension.getKey().getKey());
        }
    }
}
