package com.tset.qa.api.publicapi.metainfo;

import api.BaseTest;
import api.endpointactions.publicapi.MetaInfoControllerActions;
import com.nu.bom.core.publicapi.dtos.*;
import io.github.osvalda.pitaya.annotation.TestCaseSupplementary;
import io.qameta.allure.Description;
import io.qameta.allure.Severity;
import io.qameta.allure.SeverityLevel;
import io.restassured.common.mapper.TypeRef;
import io.restassured.response.Response;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.SoftAssertions;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.util.List;

@Slf4j
public class GetMetaInfoTests extends BaseTest {

    private static final Integer NUMBER_OF_MODULARIZED_TECHNOLOGIES = 8;
    private static final String SAMPLE_MASTER_DATA_FIELD = "masterDataTypeInternal";
    private static final String MATERIAL_KEY_FIELD_NAME = "designation";

    @BeforeMethod(alwaysRun = true)
    public void testSetup() {
        softAssert = new SoftAssertions();
    }

    private void checkTechnologies(Response result, Integer expectedNumberOfTechnologies) {
        softAssert.assertThat(result.statusCode())
                .as("The get technologies request failed.\n" + result.asPrettyString())
                .isEqualTo(200);

        MetaInfoTechnologies dto = result.getBody().as(MetaInfoTechnologies.class);
        List<String> technologyIds = dto.getRes().stream().map(MetaInfoTechnology::getIdentifier).toList();
        List<String> technologyNames = dto.getRes().stream().map(MetaInfoTechnology::getHumanReadableName).toList();

        softAssert.assertThat(dto.getRes().size())
                .as("Not the expected number of technologies")
                .isEqualTo(expectedNumberOfTechnologies);
        softAssert.assertThat(technologyIds)
                .as("Contains duplicates")
                .containsExactlyElementsOf(technologyIds.stream().distinct().toList());
        softAssert.assertThat(technologyNames)
                .as("Is not sorted")
                .containsExactlyElementsOf(technologyNames.stream().sorted().toList());
        softAssert.assertAll();
    }

    @DataProvider(name = "technologiesWithNoMaterials")
    protected Object[][] technologiesWithNoMaterials() {
        return new Object[][]{
                {"pcba"}, {"pcb"},
        };
    }

    @Test(dataProvider = "technologiesWithNoMaterials")
    @TestCaseSupplementary(api = {GET + MetaInfoControllerActions.MATERIALS})
    @Severity(SeverityLevel.NORMAL)
    @Description("Get materials for technology without materials")
    public void getNoMaterials(String technology) {
        // WHEN
        Response result = MetaInfoControllerActions.getMaterials(getAccessToken(), technology);

        // THEN
        softAssert.assertThat(result.statusCode())
                .as("The get materials request failed.\n" + result.asPrettyString())
                .isEqualTo(200);

        MetaInfoMaterials dto = result.getBody().as(MetaInfoMaterials.class);

        softAssert.assertThat(dto.getTechnology().getIdentifier())
                .as("Wrong technology returned")
                .isEqualTo(technology);

        List<MetaInfoMaterialsGroup> groups = dto.getGroups();

        softAssert.assertThat(groups.size())
                .as("Expected no material groups, found \n" + groups)
                .isEqualTo(0);
        softAssert.assertAll();
    }

    @Test
    @TestCaseSupplementary(api = {GET + MetaInfoControllerActions.MODULARIZATION_TECHNOLOGIES})
    @Severity(SeverityLevel.NORMAL)
    @Description("Get all modularized calculation modules")
    public void getModularizedCalculationModulesTest() {
        // WHEN
        Response result = MetaInfoControllerActions.getModularizedCalculationModules(getAccessToken());

        // THEN
        checkTechnologies(result, NUMBER_OF_MODULARIZED_TECHNOLOGIES);
    }

    @DataProvider(name = "modularizedEntitiesForTechnology")
    protected Object[][] modularizedEntitiesForTechnology() {
        return new Object[][] {
                {"inj2", 9},
                {"dca", 6},
                {"sint", 5}
        };
    }

    @Test(dataProvider = "modularizedEntitiesForTechnology")
    @TestCaseSupplementary(api = {GET + MetaInfoControllerActions.MODULARIZATION_TECHNOLOGY_ENTITIES})
    @Severity(SeverityLevel.NORMAL)
    @Description("Get all modularized entities for a modularized technology")
    public void getModularizedEntitiesForTechnologyTest(String technology, int expectedMinNumEntities) {
        // WHEN
        Response result = MetaInfoControllerActions.getModularizedEntitiesForTechnology(getAccessToken(), technology);

        // THEN
        softAssert.assertThat(result.statusCode())
                .as("The get entities from modularized technology request failed.\n" + result.asPrettyString())
                .isEqualTo(200);

        List<MetaInfoModularizedEntity> entities = result.getBody().as(new TypeRef<>() {});

        softAssert.assertThat(entities.size())
                .as("Not the expected number of entities")
                .isEqualTo(expectedMinNumEntities);

        for (MetaInfoModularizedEntity entity : entities) {
            softAssert.assertThat(entity.getEntityType())
                    .as("Wrong entity type")
                    .isIn("MANUFACTURING_STEP", "MATERIAL");

            List<MetaInfoTechnology> entityTechnologies = entity.getTechnologies();
            softAssert.assertThat(entityTechnologies.stream().map(MetaInfoTechnology::getIdentifier).toList())
                    .as("Could not find technology " + technology + " in " + entityTechnologies)
                    .contains(technology);
        }

        softAssert.assertAll();
    }
}
