package com.tset.qa.api.publicapi.bomnodes;

import api.BomNodesEndpointBaseTest;
import api.validations.publicapi.BomNodeValidations;
import com.nu.bom.core.publicapi.dtos.PubApiManufacturing;
import com.nu.qa.utilities.api.actions.publicapi.BomNodesControllerActions;
import io.github.osvalda.pitaya.annotation.TestCaseSupplementary;
import io.qameta.allure.Description;
import io.qameta.allure.Severity;
import io.qameta.allure.SeverityLevel;
import io.restassured.response.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.assertj.core.api.SoftAssertions;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.assertThat;

@Slf4j
public class RecalculateBomNodeTests extends BomNodesEndpointBaseTest {

    @BeforeClass(alwaysRun = true)
    public void initPreRequisites() {
        log.info("Init pre requisites for RecalculateBomNodeTests");
        createFolderAndProjectForBomnodeTests();
    }

    @BeforeMethod(alwaysRun = true)
    public void testSetup() {
        softAssert = new SoftAssertions();
    }

    @AfterClass(alwaysRun = true)
    public void cleanUp() {
        log.info("Clean up for RecalculateBomNodeTests");
        deleteProjectAndFolderUsedByBomnodeTests();
    }

    @Test(dataProvider = "calculationTypes")
    @TestCaseSupplementary(api = {POST + BomNodesControllerActions.BOMNODES_ID_RECALCULATE})
    @Description("Recalculate BomNode without saving")
    @Severity(SeverityLevel.NORMAL)
    public void recalculateBomNodeTest(PubApiManufacturing bomNode) {
        // GIVEN
        Response result = BomNodesControllerActions.addBomNode(getAccessToken(), projectKey, bomNode);

        assertThat(result.statusCode())
                .as(bomNode.getManufacturingType() + " bomNode creation request failed.")
                .isEqualTo(HttpStatus.SC_OK);

        String bomNodeId = result.jsonPath().getString("id");

        assertThat(bomNodeId)
                .as("Node id must be present in successful creation response!")
                .isNotNull();

        // WHEN
        result = BomNodesControllerActions.recalculateBomNode(
                getAccessToken(),
                projectKey,
                bomNodeId,
                false,
                false,
                false
        );

        // THEN
        String nodeId = result.jsonPath().getString("id");
        String branchId = result.jsonPath().getString("branchId");

        softAssert.assertThat(result.statusCode())
                .as("Recalculate " + bomNode.getManufacturingType() + " bomNode request failed.")
                .isEqualTo(HttpStatus.SC_OK);
        softAssert.assertThat(nodeId)
                .as("Node id must be present in successful creation response!")
                .isNotNull();
        softAssert.assertThat(branchId)
                .as("Branch id must be present in successful creation response!")
                .isNotNull();
        softAssert.assertAll();

        // WHEN
        result = BomNodesControllerActions.getBomNode(getAccessToken(), projectKey, nodeId, branchId);

        // THEN AGAIN
        // recalculation of newly created calculation should never change anything
        BomNodeValidations.validateBomNode(result.as(PubApiManufacturing.class), bomNode);
    }
}
