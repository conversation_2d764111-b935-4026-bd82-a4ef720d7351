package com.nu.qa.ui.data.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum Technology {
    ALU_EXTRUSION("Aluminum extrusion", "ManufacturingAluExtrusion", "AEXT"),
    ALU_FORGING("Die forging aluminium", "ManufacturingDieForgingAlu", "AFOR"),
    BAR_TURNING("Bar turning", "ManufacturingBart", "BART"),
    CHILL_CASTING("Chill casting", "ManufacturingChillCasting", "CHILL"),
    COLD_EXTRUSION("Cold extrusion", "ManufacturingColdExtrusion", "CEXT"),
    CORE_SHOOTING("Core shooting", "ManufacturingCoreShootingStandAlone", "CORES"),
    COTTER_KEY_ROLLING("Cotter key rolling", "ManufacturingCotterKeyRolling", "CROL"),
    CUTTING_AND_BENDING("Cutting and bending", "ManufacturingCuttingAndBending", "SMF"),
    DIE_CASTING("Die casting", "ManufacturingDieCasting", "DCA"),
    DIE_FORGING("Die forging steel", "ManufacturingDieForging", "DFOR"),
    HATEBUR_HOT("Hatebur hot forging", "ManufacturingHateburHot", "WHAT"),
    HATEBUR_COLD("Hatebur cold forging", "ManufacturingHateburCold", "CHAT"),
    HIGH_PRESSURE_DIE_CASTING("High pressure die casting", "ManufacturingDieCasting", "DCA"),
    LAMINATION_STACK("Lamination stack", "ManufacturingLaminationStack", "LAST"),
    NDFEB_MAGNETS("NdFeB magnets", "ManufacturingMagnet", "MAG"),
    PLASTIC_INJECTION("Plastic injection", "ManufacturingInjection2", "INJ"),
    PLASTIC_INJECTION_V2("Plastic injection (v2)", "ManufacturingInjection2", "INJ"),
    PROGRESSIVE_DIE_STAMPING("Progressive Die Stamping", "ManufacturingProgressiveDieStamping", "PDS"),
    TRANSFER_DIE_STAMPING("Transfer Die Stamping", "ManufacturingTransferDieStamping", "TDS"),
    MICRO_INJECTION("Micro injection", "ManufacturingMicroInjection", "MINJ"),
    RUBBER_INJECTION("Rubber injection", "ManufacturingRubberInjection", "RUBB"),
    PRECISION_CASTING("Precision casting", "ManufacturingPrecisionCasting", "PREC"),
    PCB("Printed circuit board", "ManufacturingPrintedCircuitBoard", "PCB"),
    PCBA("Printed circuit board assembly", "ManufacturingPrintedCircuitBoardAssembly", "PCBA"),
    RING_ROLLING("Ring rolling", "ManufacturingRingRolling", "RROL"),
    ROTARY_SWAGING("Rotary swaging", "ManufacturingRotarySwaging", "RSWAG"),
    SAND_CASTING("Sand casting", "ManufacturingSandCasting", "SAND"),
    SINTERING("Sintering", "ManufacturingSintering", "SINT"),
    VACUUM_PRECISION_CASTING("Vacuum precision casting", "ManufacturingVacuumPrecisionCasting", "VPREC"),
    PRINTED_CARDBOARD_BOX("Printed cardboard box", "ManufacturingPrintedCardboardBox", "PBOX");

    private final String name;
    private final String id;
    private final String abbreviation;
}
