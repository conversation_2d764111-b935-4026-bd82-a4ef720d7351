package com.nu.qa.ui.data.testData;

import lombok.Getter;

import java.io.File;

@Getter
public enum ProjectImportConstants {

    WO_ROOT_AND_WO_SUB_WITH_TWO_CHILDREN("wo_root_and_wo_sub_with_two_children.zip", "parent"),
    WO_ROOT_WITH_MANUAL_ENTITIES("wo_root_with_manual_entities.zip", "Calculation without cost D"),
    WO_AND_COST_ROOTS_WITH_ENTITIES("wo_and_cost_roots_with_entities.zip", "Calculation without cost D"),
    WO_ROOT_WITH_TWO_RES_SUB_CALCULATIONS("wo_root_with_two_res_subs.zip", "WO_Root"),
    SAND_ROOT_WITH_MATERIALS_AND_SUBS("sand_root_with_materials_and_subs.zip", "SAND"),
    BULK_ACTION_CALCULATION("bulk_action_calculation.zip", "bulkCalc"),
    COPY_PASTE_BASE_PROJECT("copy_paste_base_project.zip", "Calculation without cost A"),
    MULTI_SUBCALCS_COPY_PASTE_PROJECT("multiple_subcalculations_copy_paste.zip", "Calculation without cost module F"),
    PDS_PROJECT("PDS_project.zip", "PDS_aZymNBo"),
    NESTED_CALCULATION("nestedMixCalculation.zip", "PREC_OuElWYl"),
    NESTED_MIXED_PROCUREMENT_CALCULATION("nestedCalcMixedProcurement.zip", "ROOT_Purchased");

    private final String projectArchive;
    private final String mainCalculationPartName;
    private static final String FOLDER = "src/test/resources/testData/projectsForImport/";

    ProjectImportConstants(String projectArchive, String mainCalculationPartName) {
        this.projectArchive = FOLDER + projectArchive;
        this.mainCalculationPartName = mainCalculationPartName;
    }
    public File getArchiveFile() {
        return new File(getProjectArchive());
    }
}
