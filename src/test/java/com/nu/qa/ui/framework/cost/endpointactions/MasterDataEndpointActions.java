package com.nu.qa.ui.framework.cost.endpointactions;

import com.nu.bom.core.publicapi.dtos.MasterDataDetails;
import com.nu.qa.utilities.api.actions.AbstractEndpointAction;
import io.qameta.allure.Step;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import lombok.extern.slf4j.Slf4j;

import static io.restassured.RestAssured.given;

@Slf4j
public class MasterDataEndpointActions extends AbstractEndpointAction {

    public static final String MASTERDATA = "v1/masterdata";
    public static final String MASTERDATA_KEY = "v1/masterdata/{mdKey}";

    public static final String MD_KEY = "mdKey";
    public static final String TYPE = "type";

    @Step("Delete master data via API")
    public static Response deleteMasterData(String accessToken, String url, String mdKey, String type) {
        log.info("Delete master data '{}' ", mdKey);
        return given().header(AUTHORIZATION, accessToken)
            .pathParam(MD_KEY, mdKey)
            .queryParam(TYPE, type)
            .delete(url + MASTERDATA_KEY)
            .then()
            .extract()
            .response();
    }

    @Step("Create master data via API")
    public static Response postMasterData(String accessToken, String url, MasterDataDetails requestBody) {
        log.info("Create master data: '{}' ", requestBody.getType());
        return given()
            .header(AUTHORIZATION, accessToken)
            .contentType(ContentType.JSON)
            .body(requestBody)
            .post(url + MASTERDATA)
            .then()
            .extract()
            .response();
    }

    @Step("Get master data details from API")
    public static Response getMasterDataDetails(String accessToken, String url, String mdKey, String type) {
        log.info("Get master data details: '{}' ", mdKey);
        return given()
            .header(AUTHORIZATION, accessToken)
            .pathParam(MD_KEY, mdKey)
            .queryParam(TYPE, type)
            .get(url + MASTERDATA_KEY)
            .then()
            .extract().response();
    }
}
