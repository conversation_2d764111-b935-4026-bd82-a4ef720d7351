package com.nu.qa.ui.framework;

import com.nu.qa.ui.framework.pageFactory.TsetFieldDecorator;
import com.nu.qa.ui.framework.utils.TsetConditionEvaluationListener;
import com.nu.qa.utilities.data.enums.units.Units;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import io.qameta.allure.model.Status;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.awaitility.Awaitility;
import org.awaitility.core.ConditionFactory;
import org.awaitility.core.ConditionTimeoutException;
import org.jetbrains.annotations.NotNull;
import org.openqa.selenium.*;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedCondition;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.openqa.selenium.support.ui.ExpectedConditions.attributeContains;

@Slf4j
public abstract class PageObject {

    private static final String os = System.getProperty("os.name").toLowerCase();
    private static final Duration TIME_OUT_DURATION = Duration.ofSeconds(BaseTest.getTimeout() / 1000);
    private static final Duration POLLING_TIMEOUT = Duration.ofMillis(500);
    private static final int PDS_TIMEOUT_IN_MILLIS = 1200000;
    private static final String LOADING_LOGO_LOCATOR = "[data-test='loading-screen']";
    private static final String LOADING_INDICATOR_WAVES = "[data-test='loading-wave']";
    private static final String LOADING_INDICATOR_TABLE = ".loading-row";
    private static final String TABULATOR_ROW_IS_LOADING = ".row-is-loading";
    private static final String BUTTON_IS_LOADING = ".is-loading";
    private static final String LOADING_INDICATOR_WAVES_MODAL = ".vm--modal [data-test='loading-wave']";
    private static final String LOADING_SPINNER_MODAL = ".vm--modal [data-test='loading-spinner']";
    private static final String LOADING_SPINNER = "[data-test='loading-spinner']";
    private static final String BOM_IMPORTER_LOADING_SPINNER = ".spinner";
    private static final String LOADING_TEXT = "Loading...";

    protected static final String BASE_CURRENCY_DROPDOWN = "//*[@data-test='nuinputselect-baseCurrency']";
    protected static final String DIMENSION_DROPDOWN = "//*[@id='dimension']";
    protected static final String CALCULATION_QUALITY_DROPDOWN = "//*[@id='calculationQualityConfigurationKey']";
    protected static final String WEIGHT_MODE = "//*[@data-test='nuinputselect-weightMode']";
    protected static final String MASTER_DATA_BASE_CURRENCY_DROPDOWN = "//*[@data-test='nuinputselect-masterdataBaseCurrency']";
    protected static final String TOOL_TYPE_DROPDOWN = "//*[@data-test='nu-class-selector']";
    protected static final By DROPDOWN_CHECKMARK_SELECTOR = By.cssSelector("div[data-test='tsetselect-list-scroll'] " +
        "div[data-test='tsetselect-listitem-right-icon'] svg");
    protected static final String INPUT_ELEMENT = "[data-test='input-element']";
    protected static final String PLAIN_TEXT_CELL = "[data-test='tset-plain-text-cell']";
    protected static final String SIDE_UNIT_LABEL = "[class='side-unit-label']";
    protected static final String READONLY_INPUT_ELEMENT = "[data-test='nu-input-readonly']";
    protected static final String SELECT_INPUT_ELEMENT = "[data-test='tsetselect-input']";
    protected static final String SELECT_HEADER = "[data-test='tsetselect-header']";
    protected static final String CLOSE_SELECT_INPUT_ELEMENT = "[data-test='caret-up']";
    protected static final String OPEN_SELECT_INPUT_ELEMENT = "[data-test='caret-down']";
    protected static final String TOOLTIP = ".tooltip-inner";
    protected static final String VALUE_ATTRIBUTE = "value";
    protected static final String CLASS_ATTRIBUTE = "class";
    protected static final String DISABLED_ATTRIBUTE = "disabled";
    protected static final String DISABLED_ACTION_CLASS_ATTRIBUTE = "disabled-action";
    protected static final String TOOLTIP_SHOWN_ATTRIBUTE = "data-popper-shown";
    protected static final String TRUE = "true";
    protected static final String SYSTEM_VALUE_WRAPPER_XPATH = "div[contains(@data-test,'nu-field-system-value-wrapper')]";
    protected static final String SYSTEM_VALUE_SELECTOR = "[data-test='system-value-field']";
    protected static final String SYSTEM_VALUE_DROPDOWN = "[data-test='tset-select-list-footer']";
    protected static final String UNLINK_ICON = "[data-test='icon-unlink']";
    protected static final By UNLINK_ICON_TABLE = By.cssSelector("[data-test='tset-text-cell-icon-un-link'] svg");
    protected static final By DOLLAR_ICON_TABLE_ROW = By.cssSelector("[data-test='tset-text-cell-icon-currency']");
    protected static final String TSET_KPI_VALUE = "[data-test='tset-kpi-value']";
    protected static final String TSET_KPI_UNIT = "[data-test='tset-kpi-unit']";
    protected static final String FIELD_DETAILS_BUTTON = "[data-test='nu-field-details']";
    protected static final String EDITABLE_LABEL = "[data-test='editable-layout-label']";
    protected static final String TSET_FIELD_VALIDATION_ERROR_TEXT = "[data-test$=error-area]>[data-test=field-error-area-error-text]";
    protected static final String READONLY_LABEL = "[data-test='readonly-layout-label']";
    protected static final String READONLY_LAYOUT_INPUT = "[data-test='readonly-layout-input']";
    protected static final String POPUP_CONTAINER_SELECTOR = "[data-test='modal-content-wrapper']";
    protected static final String LOADING_LAZY_COMPONENT = "[data-test='lazy-component']";
    protected static final String READONLY_PLAIN_VALUE = "[data-test='nu-input-readonly-plain-value']";

    protected static final String MULTI_LEVEL_DROPDOWN_ITEM_SELECTOR = "div[data-test*='hierarchical-select-entry']";
    protected static final String MULTI_LEVEL_DROPDOWN_NAVIGATE_BUTTON_SELECTOR = "button[data-test$='navigate-button']";
    protected static final String MULTI_LEVEL_DROPDOWN_SELECT_BUTTON_SELECTOR = "button[data-test$='select-button']";
    protected static final String MULTI_LEVEL_DROPDOWN_BREADCRUMB_SELECTOR = "[data-test*='breadcrumb']";

    protected WebDriver driver;

    protected PageObject() {
    }

    protected PageObject(WebDriver driver) {
        this.driver = driver;
        PageFactory.initElements(new TsetFieldDecorator(driver), this);
    }

    protected void scrollToElement(WebElement element) {
        ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView(true);", element);
    }

    protected void scrollToTopOfElement(WebElement element) {
        ((JavascriptExecutor) driver).executeScript("arguments[0].scrollTo(0,0);", element);
    }

    protected void scrollToBottomOfElement(WebElement element) {
        ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView(false);", element);
    }

    protected static final ConditionFactory CONDITION_FACTORY = Awaitility.await().ignoreExceptions()
        .atMost(BaseTest.getTimeout(), TimeUnit.MILLISECONDS);

    protected static final ConditionFactory CONDITION_FACTORY_WITH_1_SEC_GRACE_PERIOD = Awaitility.await()
        .ignoreExceptions()
        .pollDelay(1, TimeUnit.SECONDS)
        .atMost(BaseTest.getTimeout(), TimeUnit.MILLISECONDS);

    protected static final ConditionFactory LOADING_TIMEOUT_CONDITION_FACTORY = Awaitility.await().ignoreExceptions()
        .atMost(BaseTest.getLoadingTimeout(), TimeUnit.MILLISECONDS);

    protected static final ConditionFactory LOADING_CONDITION_FACTORY_WITH_1_SEC_GRACE_PERIOD = Awaitility.await()
        .ignoreExceptions()
        .pollDelay(1, TimeUnit.SECONDS)
        .atMost(BaseTest.getLoadingTimeout(), TimeUnit.MILLISECONDS);

    protected static final ConditionFactory LOADING_CONDITION_FACTORY_1_MIN =
        Awaitility.await().ignoreExceptions()
            .pollDelay(2, TimeUnit.SECONDS)
            .atMost(PDS_TIMEOUT_IN_MILLIS, TimeUnit.MILLISECONDS);

    protected void clearWebElement(WebElement toBeCleared) {
        scrollToElement(toBeCleared);
        if (!os.contains("mac")) {
            toBeCleared.sendKeys(Keys.CONTROL + "a");
        } else {
            toBeCleared.sendKeys(Keys.COMMAND + "a");
        }
        toBeCleared.sendKeys(Keys.DELETE);
    }

    protected boolean isWebElementCurrentlyDisplayed(WebElement element) {
        try {
            return element.isDisplayed();
        } catch (WebDriverException e) {
            return false;
        }
    }

    protected boolean isWebElementCurrentlyDisplayed(SearchContext context, By by) {
        try {
            return context.findElement(by).isDisplayed();
        } catch (WebDriverException e) {
            return false;
        }
    }

    protected boolean isWebElementFromParentCurrentlyDisplayed(WebElement parent, By locator) {
        try {
            return isWebElementCurrentlyDisplayed(parent.findElement(locator));
        } catch (WebDriverException e) {
            return false;
        }
    }

    protected void waitUntilWebElementDisplayed(WebElement element) {
        CONDITION_FACTORY.conditionEvaluationListener(new TsetConditionEvaluationListener(element::isDisplayed))
            .until(element::isDisplayed);
    }

    protected boolean isWebElementVisible(WebElement element) {
        try {
            CONDITION_FACTORY.until(element::isDisplayed);
        } catch (ConditionTimeoutException e) {
            return false;
        }
        return true;
    }

    protected boolean isWebElementVisible(By by) {
        try {
            waitUntilElementVisible(by);
        } catch (TimeoutException e) {
            return false;
        }
        return true;
    }

    private static boolean checkIsDisplayed(WebElement element) {
        try {
            return element.isDisplayed();
        } catch (Exception ignored) {
        }
        return false;
    }

    protected WebElement waitForFirstElementVisible(WebElement... elements) {
        final String message = "Failed to find any of the expected elements";
        try {
            CONDITION_FACTORY.until(
                () -> Arrays
                    .stream(elements)
                    .anyMatch(PageObject::checkIsDisplayed));
        } catch (ConditionTimeoutException e) {
            throw new ConditionTimeoutException(
                message, e);
        }
        return Arrays.stream(elements)
            .filter(PageObject::checkIsDisplayed)
            .findFirst()
            .orElseThrow(() -> new ConditionTimeoutException(message));
    }

    protected List<String> getInvisibleElements(WebElement... elements) {
        List<String> result = new ArrayList<>();
        for (WebElement element : elements) {
            try {
                CONDITION_FACTORY.until(element::isDisplayed);
            } catch (ConditionTimeoutException e) {
                Allure.step("Failed to find element " + element, Status.FAILED);
                Allure.addAttachment("Page source at failure", driver.getPageSource());
                result.add(element.toString());
            }
        }
        return result;
    }

    protected boolean isWebElementEnabled(WebElement element) {
        scrollToElement(element);
        try {
            CONDITION_FACTORY.until(element::isEnabled);
        } catch (ConditionTimeoutException e) {
            return false;
        }
        return true;
    }

    protected boolean isWebElementDisabled(WebElement element) {
        scrollToElement(element);
        try {
            CONDITION_FACTORY.until(() -> !element.isEnabled());
        } catch (ConditionTimeoutException e) {
            return false;
        }
        return true;
    }

    protected boolean isWebElementInvisible(By by) {
        try {
            waitUntilElementInvisible(by);
        } catch (TimeoutException e) {
            return false;
        }
        return true;
    }

    protected String getElementText(By locator) {
        waitUntilElementVisible(locator);
        try {
            return getElementText(findElement(locator));
        } catch (StaleElementReferenceException sere) {
            log.warn("We should not get a StaleElementReferenceException in this case. Please check if there is a reasonable wait in upper levels of the stack before the current statement. Trying again.", sere);
            return getElementText(findElement(locator));
        }
    }

    protected String getElementText(By locator, boolean scroll) {
        waitUntilElementVisible(locator);
        return getElementText(findElement(locator), scroll);
    }

    protected String getElementText(WebElement element) {
        return getElementText(element, true);
    }

    protected String getElementText(WebElement element, boolean scroll) {
        if (isWebElementVisible(element)) {
            if (scroll) {
                scrollToElement(element);
            }
            return element.getText();
        }
        return null;
    }

    protected String getElementValueAttribute(WebElement element) {
        return getElementAttribute(element, VALUE_ATTRIBUTE);
    }

    protected String getElementDisabledAttribute(WebElement element) {
        return getElementAttribute(element, DISABLED_ATTRIBUTE);
    }

    protected String getElementClassAttribute(WebElement element) {
        return getElementAttribute(element, CLASS_ATTRIBUTE);
    }

    protected String getElementAttribute(WebElement element, String attribute) {
        if (isWebElementVisible(element)) {
            scrollToElement(element);
            try {
                CONDITION_FACTORY.until(() -> element.getDomProperty(attribute) != null || element.getDomAttribute(attribute) != null);
            } catch (ConditionTimeoutException e) {
                log.warn("Element '{}' does not have attribute, value or dom property: '{}'", element, attribute);
                return null;
            }
            String value = element.getDomProperty(attribute);
            if (value == null) {
                value = element.getDomAttribute(attribute);
            }
            return value;
        }
        log.warn("Element '{}' is not visible!", element);
        return null;
    }

    protected String getDropDownElementText(By locator) {
        waitUntilElementVisible(locator);
        CONDITION_FACTORY.until(() -> !StringUtils.isBlank(getElementText(findElement(locator))));
        return getElementText(findElement(locator));
    }

    protected List<String> getAllDropDownItems(WebElement element) {
        List<String> allElements;
        moveAndClickOnWebElement(element);
        allElements = driver
            .findElements(By.cssSelector("[data-test='tsetselect-list-scroll'] [data-test='tsetselect-listitem']"))
            .stream().map(this::getElementText)
            .collect(Collectors.toList());
        clickOnWebElement(findElement(By.cssSelector("span[data-test='caret-up']")));
        return allElements;
    }

    protected void waitUntilRefreshedAndAllVisible(By locator) {
        WebDriverWait wait = new WebDriverWait(driver, TIME_OUT_DURATION);
        wait.until(ExpectedConditions.refreshed(ExpectedConditions.visibilityOfAllElementsLocatedBy(locator)));
    }

    protected void waitUntilRefreshedAndAllPresent(By locator) {
        WebDriverWait wait = new WebDriverWait(driver, TIME_OUT_DURATION);
        wait.until(ExpectedConditions.refreshed(ExpectedConditions.presenceOfAllElementsLocatedBy(locator)));
    }

    protected void waitUntilAttributeContains(WebElement element, String attribute, String value) {
        WebDriverWait wait = new WebDriverWait(driver, TIME_OUT_DURATION);
        wait.until(attributeContains(element, attribute, value));
    }

    protected List<WebElement> waitUntilAllPresented(By locator) {
        WebDriverWait wait = new WebDriverWait(driver, TIME_OUT_DURATION);
        return wait.until(ExpectedConditions.presenceOfAllElementsLocatedBy(locator));
    }

    protected void waitUntilTextChangesTo(By locator, String value) {
        WebDriverWait wait = new WebDriverWait(driver, TIME_OUT_DURATION);
        wait.until(ExpectedConditions.textToBe(locator, value));
    }

    protected void waitUntilTextPresentedInElement(WebElement element, String value) {
        WebDriverWait wait = new WebDriverWait(driver, TIME_OUT_DURATION);
        wait.until(ExpectedConditions.textToBePresentInElement(element, value));
    }

    protected void waitUntilTextHasChangedInElement(WebElement element, String newValue) {
        WebDriverWait wait = new WebDriverWait(driver, TIME_OUT_DURATION);
        wait.until(ExpectedConditions.not(ExpectedConditions.attributeToBe(element, "data-value", newValue)));
    }

    protected void waitUntilTextNotPresentedInElement(By locator, String value) {
        WebDriverWait wait = new WebDriverWait(driver, TIME_OUT_DURATION);
        wait.until(ExpectedConditions.not(ExpectedConditions.textToBePresentInElementLocated(locator, value)));
    }

    protected void waitUntilTextContains(By locator, String value) {
        Pattern pattern = Pattern.compile("(?i).*" + value + ".*");
        waitUntilTextMatches(locator, pattern);
    }

    protected void waitUntilTextMatches(By locator, Pattern pattern) {
        WebDriverWait wait = new WebDriverWait(driver, TIME_OUT_DURATION);
        wait.until(ExpectedConditions.textMatches(locator, pattern));
    }

    private void waitUntilElementClickable(WebElement element) {
        WebDriverWait wait = new WebDriverWait(driver, TIME_OUT_DURATION);
        try {
            wait.until(ExpectedConditions.elementToBeClickable(element));
        } catch (TimeoutException e) {
            throw new AssertionError("The element '" + element +
                "' is either invisible or disabled! Click operation cannot be performed on it! See screenshot for more information!");
        }
    }

    protected static void waitUntilElementPresent(WebDriver driver, By locator) {
        WebDriverWait wait = new WebDriverWait(driver, TIME_OUT_DURATION);
        wait.until(ExpectedConditions.presenceOfElementLocated(locator));
    }

    protected void waitUntilElementPresent(By locator) {
        waitUntilElementPresent(driver, locator);
    }

    protected void waitUntilElementVisible(By locator) {
        WebDriverWait wait = new WebDriverWait(driver, TIME_OUT_DURATION);
        wait.until(ExpectedConditions.visibilityOfElementLocated(locator));
    }

    protected void waitUntilElementInvisible(By locator) {
        waitUntilElementInvisibleWithGivenTimeout(locator, TIME_OUT_DURATION);
    }

    protected void waitUntilElementInvisibleWithGivenTimeout(By locator, Duration timeout) {
        WebDriverWait wait = new WebDriverWait(driver, timeout);
        wait.until(ExpectedConditions.invisibilityOfElementLocated(locator));
    }

    private void waitUntilAllElementsAreInvisibleWithGivenTimeout(By locator, Duration timeout) {
        WebDriverWait wait = new WebDriverWait(driver, timeout);
        wait.until(ExpectedConditions.invisibilityOfAllElements(driver.findElements(locator)));
    }

    protected void waitUntilEitherConditionPass(ExpectedCondition<?>... conditions) {
        WebDriverWait wait = new WebDriverWait(driver, TIME_OUT_DURATION);
        wait.until(ExpectedConditions.or(conditions));
    }

    protected void waitUntilElementIsVisible(WebElement element) {
        WebDriverWait wait = new WebDriverWait(driver, TIME_OUT_DURATION);
        wait.until(ExpectedConditions.visibilityOf(element));
    }

    protected void waitUntilElementIsClickable(WebElement element) {
        WebDriverWait wait = new WebDriverWait(driver, TIME_OUT_DURATION);
        wait.until(ExpectedConditions.elementToBeClickable(element));
    }

    protected void waitUntilElementIsNotClickable(WebElement element) {
        WebDriverWait wait = new WebDriverWait(driver, TIME_OUT_DURATION);
        wait.until(ExpectedConditions.not(ExpectedConditions.elementToBeClickable(element)));
    }

    protected boolean isElementTextContains(By by, String expected) {
        try {
            waitUntilTextContains(by, expected);
        } catch (TimeoutException e) {
            return false;
        }
        return true;
    }

    protected boolean isTextPresentedInElement(WebElement element, String expected) {
        scrollToElement(element);
        try {
            waitUntilTextPresentedInElement(element, expected);
        } catch (TimeoutException e) {
            return false;
        }
        return true;
    }

    protected void waitUntilTsetLogoLoadingIsVisible() {
        log.info("Wait until Tset logo loading indicator is visible.");
        LOADING_CONDITION_FACTORY_WITH_1_SEC_GRACE_PERIOD.until(
            () -> isLoadingIndicatorInvisibleWithTimeout(By.cssSelector(LOADING_LOGO_LOCATOR), POLLING_TIMEOUT));
    }

    protected void waitUntilLoadingIndicatorIsVisibleForMax1Minute() {
        log.info("Wait until Tset logo loading indicator is visible for maximum of 1 minute");
        LOADING_CONDITION_FACTORY_1_MIN.until(
            () -> isLoadingIndicatorInvisibleWithTimeout(By.cssSelector(LOADING_LOGO_LOCATOR), POLLING_TIMEOUT));
    }

    protected void waitUntilLoadingIndicatorIsVisible() {
        log.info("Wait until loading indicator waves are visible.");
        LOADING_CONDITION_FACTORY_WITH_1_SEC_GRACE_PERIOD.until(
            () -> isLoadingIndicatorInvisibleWithTimeout(By.cssSelector(LOADING_INDICATOR_WAVES), POLLING_TIMEOUT));
    }

    protected void waitUntilLazyLoadAnimationIsVisible() {
        log.info("Wait until lazyload animations are done");
        LOADING_CONDITION_FACTORY_WITH_1_SEC_GRACE_PERIOD.until(
            () -> isLazyLoadAnimationInvisibleWithTimeout(findElement(By.cssSelector(LOADING_LAZY_COMPONENT))));
    }

    protected void waitUntilLoadingIndicatorIsVisibleInModal() {
        log.info("Wait until loading indicator waves are visible within a modal.");
        LOADING_CONDITION_FACTORY_WITH_1_SEC_GRACE_PERIOD
            .until(() -> isLoadingIndicatorInvisibleWithTimeout(By.cssSelector(LOADING_INDICATOR_WAVES_MODAL),
                POLLING_TIMEOUT));
    }

    protected void waitUntilPDSServiceLoads() {
        log.info("Wait until loading indicator waves are visible in case of PDS calculation...");
        LOADING_CONDITION_FACTORY_1_MIN.until(() ->
            isLoadingIndicatorInvisibleWithTimeout(By.cssSelector(LOADING_INDICATOR_WAVES), POLLING_TIMEOUT));
    }

    protected void waitUntilLoadingSpinnerIsVisibleInModal() {
        log.info("Wait until loading spinner is visible within a modal.");
        LOADING_CONDITION_FACTORY_WITH_1_SEC_GRACE_PERIOD.until(
            () -> isLoadingIndicatorInvisibleWithTimeout(By.cssSelector(LOADING_SPINNER_MODAL), POLLING_TIMEOUT));
    }

    protected void waitUntilSpinnersAreVisible() {
        log.info("Wait until loading spinner is visible.");
        LOADING_CONDITION_FACTORY_WITH_1_SEC_GRACE_PERIOD
            .until(() -> isLoadingIndicatorInvisibleWithTimeout(By.cssSelector(LOADING_SPINNER), POLLING_TIMEOUT));
    }

    protected void waitUntilBomImporterSpinnersAreVisible() {
        log.info("Wait until loading spinner is visible in BOM Importer app.");
        LOADING_CONDITION_FACTORY_WITH_1_SEC_GRACE_PERIOD
            .until(() -> isLoadingIndicatorInvisibleWithTimeout(By.cssSelector(BOM_IMPORTER_LOADING_SPINNER),
                POLLING_TIMEOUT));
    }

    protected void waitUntilTableLoadingIndicatorIsVisible() {
        log.info("Wait until table loading indicator is visible.");
        LOADING_CONDITION_FACTORY_WITH_1_SEC_GRACE_PERIOD.until(
            () -> isLoadingIndicatorInvisibleWithTimeout(By.cssSelector(LOADING_INDICATOR_TABLE), POLLING_TIMEOUT)
                && isLoadingIndicatorInvisibleWithTimeout(By.cssSelector(TABULATOR_ROW_IS_LOADING), POLLING_TIMEOUT));
    }

    protected void waitUntilButtonLoadingIndicatorIsVisible() {
        log.info("Wait until loading indicator is visible in any buttons.");
        LOADING_CONDITION_FACTORY_WITH_1_SEC_GRACE_PERIOD.until(
            () -> isLoadingIndicatorInvisibleWithTimeout(By.cssSelector(BUTTON_IS_LOADING), POLLING_TIMEOUT));
    }

    protected void waitUntilMouseCursorFinishesProgress() {
        log.info("Wait until mouse course finishes progress (spinner).");
        WebDriverWait wait = new WebDriverWait(driver, TIME_OUT_DURATION);
        wait.until(ExpectedConditions.not(attributeContains(By.cssSelector("body"), "cursor", "progress")));
    }

    protected void waitUntilElementVisibleWithGracePeriod(By locator) {
        log.info("Wait until element is visible {}", locator);
        LOADING_CONDITION_FACTORY_WITH_1_SEC_GRACE_PERIOD.until(() -> isWebElementVisible(locator));
    }

    protected void waitUntilElementInvisibleWithGracePeriod(By locator) {
        log.info("Wait until element is visible {}", locator);
        LOADING_CONDITION_FACTORY_WITH_1_SEC_GRACE_PERIOD.until(() -> isWebElementInvisible(locator));
    }

    private boolean isLoadingIndicatorInvisibleWithTimeout(By by, Duration timeout) {
        try {
            waitUntilAllElementsAreInvisibleWithGivenTimeout(by, timeout);
        } catch (TimeoutException e) {
            return false;
        }
        return true;
    }

    protected void waitUntilTooltipIsVisible() {
        log.info("Wait until tooltip is visible");
        LOADING_TIMEOUT_CONDITION_FACTORY.until(() -> isWebElementVisible(By.cssSelector(TOOLTIP)));
    }

    protected void waitWhileLoadingTextIsPresentInElement(WebElement element) {
        WebDriverWait wait = new WebDriverWait(driver, TIME_OUT_DURATION);
        wait.until(ExpectedConditions.not(ExpectedConditions.textToBePresentInElement(element, LOADING_TEXT)));
    }

    protected void waitUntilElementContainsAnyText(WebElement element) {
        WebDriverWait wait = new WebDriverWait(driver, TIME_OUT_DURATION);
        wait.until((ExpectedCondition<Boolean>) driver -> {
            String text = element.getText();
            return !text.isEmpty();
        });
    }

    protected void sendKeyToBrowser(Keys key) {
        Actions action = new Actions(driver);
        action.sendKeys(key).perform();
    }

    protected void sendKeyAndWaitUntilItPresentsInValue(WebElement input, String value) {
        String currentValue = getElementAttribute(input, "data-value");
        sendKey(input, value);
        String newDataValue = getElementAttribute(input, "data-value");
        if (currentValue != null && !currentValue.equals(newDataValue)) {
            waitUntilTextHasChangedInElement(input, currentValue);
        }
    }

    protected void sendKeyWaitUntilValueIsPresentAndPressKey(WebElement input, String value, Keys key) {
        sendKeyAndWaitUntilItPresentsInValue(input, value);
        input.sendKeys(key);
    }

    protected void sendKeyWithoutClearing(WebElement input, String value) {
        CONDITION_FACTORY.until(input::isDisplayed);
        input.sendKeys(value);
    }

    protected void sendKey(WebElement input, Keys key) {
        CONDITION_FACTORY.until(input::isDisplayed);
        input.sendKeys(key);
    }

    protected void sendKey(WebElement input, String value) {
        CONDITION_FACTORY.until(input::isDisplayed);
        clearWebElement(input);
        input.sendKeys(value);
    }

    protected void sendKeyAndPressEnter(WebElement input, String value) {
        sendKeyAndWaitUntilItPresentsInValue(input, value);
        input.sendKeys(Keys.ENTER);
    }

    protected void sendKeyAndClickOutside(WebElement input, String value, WebElement outsideItem) {
        sendKeyAndWaitUntilItPresentsInValue(input, value);
        clickOnWebElement(outsideItem);
    }

    protected void sendKeyAndPressTab(WebElement input, String value) {
        sendKeyAndWaitUntilItPresentsInValue(input, value);
        sendKey(input, Keys.TAB);
    }

    protected void sendKeyAndPressEsc(WebElement input, String value) {
        sendKeyAndWaitUntilItPresentsInValue(input, value);
        sendKey(input, Keys.ESCAPE);
    }

    /**
     * Send a file to the hidden input element to simulate a file upload flow.
     * This method does not trigger a native file explorer modal!
     *
     * @param hiddenFileInput the WebElement of the hidden input
     * @param fileUri         the uri of the file to upload
     */
    protected void sendKeyForFileUpload(WebElement hiddenFileInput, String fileUri) {
        try {
            CONDITION_FACTORY.until(hiddenFileInput::isEnabled);
        } catch (ConditionTimeoutException e) {
            throw new AssertionError("The hidden file input html element is not present in DOM! " +
                "See page source for more info!");
        }
        hiddenFileInput.sendKeys(fileUri);
    }

    protected void clickOnWebElement(WebElement element) {
        clickOnWebElement(element, true, true);
    }

    protected void clickOnWebElement(WebElement element, boolean scrollRequired) {
        clickOnWebElement(element, scrollRequired, true);
    }

    protected void clickOnWebElement(WebElement element, boolean scrollRequired, boolean top) {
        waitUntilElementClickable(element);
        if (scrollRequired) {
            if (top) {
                scrollToElement(element);
            } else {
                scrollToBottomOfElement(element);
            }
        }
        element.click();
    }

    protected void forceClickOnWebElement(WebElement element) {
        ((JavascriptExecutor) driver).executeScript("arguments[0].click();", element);
    }

    protected void moveAndClickOnWebElement(WebElement element) {
        waitUntilElementClickable(element);
        Actions action = new Actions(driver);
        action.moveToElement(element).click().perform();
    }

    protected void doubleClickOnWebElement(WebElement element) {
        waitUntilElementClickable(element);
        scrollToElement(element);
        Actions action = new Actions(driver);
        action.moveToElement(element).doubleClick(element).perform();
    }

    protected void rightClickOnWebElement(WebElement element) {
        waitUntilElementClickable(element);
        scrollToBottomOfElement(element);
        Actions action = new Actions(driver);
        action.moveToElement(element).contextClick(element).perform();
    }

    protected void chooseFromDropDownByOptionTextWithXpathAndClose(String dropDownXpath, String value) {
        By closeDropDownLocator = By.xpath("//span[@data-test='caret-up']");
        if (StringUtils.isBlank(value)
            || StringUtils.equalsIgnoreCase(getElementText(By.xpath(dropDownXpath)), value)) {
            return;
        }
        chooseFromDropDownByOptionTextWithXpath(dropDownXpath, value);
        waitUntilElementVisible(DROPDOWN_CHECKMARK_SELECTOR);
        WebElement closeDropDown = findElement(closeDropDownLocator);
        moveAndClickOnWebElement(closeDropDown);
        CONDITION_FACTORY.until(() -> getDropDownElementText(By.xpath(dropDownXpath)).contains(value));
    }

    protected void chooseFromDropDownByOptionTextWithXpath(String dropDownXpath, String value, String currency) {
        By dropDown = By.xpath(dropDownXpath);
        if (StringUtils.isBlank(value) || StringUtils.equalsIgnoreCase(getElementText(dropDown), currency)) {
            return;
        }
        chooseFromDropDownByOptionTextWithXpath(dropDownXpath, value);
    }

    protected void chooseFromDropDownByOptionTextWithXpath(String dropDownXpath, String value) {
        By dropDown = By.xpath(dropDownXpath);
        By valueList = By.xpath("//div[@data-test='tsetselect-list-scroll']//div[@data-test='tsetselect-listitem']/span[1][text()='" + value + "']");

        if (StringUtils.isBlank(value) || StringUtils.equalsIgnoreCase(getElementText(dropDown), value)) {
            return;
        }
        waitUntilAllPresented(dropDown);
        waitUntilElementInvisible(By.xpath("//div[@class='toasted-container top-center']"));
        try {
            CONDITION_FACTORY.until(() -> {
                clickOnWebElement(findElement(dropDown));
                return findElement(valueList).isDisplayed();
            });
        } catch (Exception e) {
            CONDITION_FACTORY.until(() -> {
                moveAndClickOnWebElement(findElement(dropDown));
                return findElement(valueList).isDisplayed();
            });
        }

        moveAndHoverAndClickOnWebElement(findElement(valueList), findElement(valueList));
    }

    protected void chooseFromDropDownByTextWithXpath(String dropDownXpath, String value) {
        By dropDown = By.xpath(dropDownXpath);
        By valueList = By.xpath("//div[@data-test='nu-class-selector-step-item']//*[contains(text(),'" + value + "')]");
        if (StringUtils.isBlank(value) || StringUtils.equalsIgnoreCase(getElementText(dropDown), value)) {
            return;
        }

        waitUntilAllPresented(dropDown);
        waitUntilElementInvisible(By.xpath("//div[@class='toasted-container top-center']"));
        clickOnWebElement(findElement(dropDown));
        waitUntilAllPresented(valueList);
        Actions action = new Actions(driver);
        action.moveToElement(findElement(valueList)).click().perform();
    }

    protected void chooseFromUnitDropDownByTextWithXpath(String dropDownXpath, String value) {
        By dropDown = By.xpath(dropDownXpath);
        By valueList = By.xpath("//*[@data-test='tsetselect-list-scroll']//*[contains(text(),'" + value + "')]");
        if (StringUtils.isBlank(value) || StringUtils.equalsIgnoreCase(getElementText(dropDown), value)) {
            return;
        }

        waitUntilAllPresented(dropDown);
        waitUntilElementInvisible(By.xpath("//div[@class='toasted-container top-center']"));
        clickOnWebElement(findElement(dropDown));
        waitUntilAllPresented(valueList);
        Actions action = new Actions(driver);
        action.moveToElement(findElement(valueList)).click().build().perform();
        waitUntilLoadingIndicatorIsVisible();
    }

    protected void chooseFromDropDownByText(WebElement dropDown, String value) {
        chooseFromDropDownByText(dropDown, value, true);
    }

    protected void chooseFromDropDownByText(WebElement dropDown, String value, boolean top) {
        if (StringUtils.isBlank(value) || StringUtils.equalsIgnoreCase(getElementText(dropDown), value)) {
            return;
        }
        waitUntilElementInvisible(By.xpath("//div[@class='toasted-container top-center']"));
        clickOnWebElement(dropDown, top);
        selectOptionFromDropdownList(value);
        waitUntilLoadingIndicatorIsVisible();
    }

    protected void selectOptionFromDropdownList(String value) {
        By valueList = By.xpath("//div[@class='tset-select-dropdown']//*[contains(text(),'" + value + "')]");
        waitUntilAllPresented(valueList);
        clickOnWebElement(findElement(valueList));
    }

    @Step("Select option {multiLevelDropdownOption} from dropdown {dropdownName}")
    public void selectOptionFromMultiLevelDropdown(String dropdownName, WebElement dropdown, List<String> multiLevelDropdownOption) {
        log.info("Select option {} from dropdown {}", multiLevelDropdownOption, dropdownName);
        goToTopOfMultiLevelDropdown(dropdownName, dropdown);
        for (int i = 0; i < multiLevelDropdownOption.size() - 1; ++i) {
            openMultiLevelParent(dropdown, multiLevelDropdownOption.get(i));
        }
        selectMultiLevelOption(dropdown, multiLevelDropdownOption.get(multiLevelDropdownOption.size() - 1));

        waitUntilLoadingIndicatorIsVisible();
        waitUntilTableLoadingIndicatorIsVisible();
    }

    @Step("Select options {multiLevelLovFilters} from dropdown {dropdownName}")
    public void selectOptionsFromMultiLevelDropdown(String dropdownName, WebElement dropdown, List<List<String>> multiLevelLovFilters) {
        log.info("Select options {} from dropdown {}", multiLevelLovFilters, dropdownName);
        for (List<String> multiLevelLovFilter : multiLevelLovFilters) {
            goToTopOfMultiLevelDropdown(dropdownName, dropdown);
            for (int j = 0; j < multiLevelLovFilter.size() - 1; ++j) {
                openMultiLevelParent(dropdown, multiLevelLovFilter.get(j));
            }
            selectMultiLevelOption(dropdown, multiLevelLovFilter.get(multiLevelLovFilter.size() - 1));
        }
        waitUntilLoadingIndicatorIsVisible();
        waitUntilTableLoadingIndicatorIsVisible();
    }

    private void openMultiLevelParent(WebElement dropdown, String filterParentName) {
        List<WebElement> lovElements = dropdown.findElements(By.cssSelector(MULTI_LEVEL_DROPDOWN_ITEM_SELECTOR));
        clickOnWebElement(lovElements.stream().filter(webElement ->
                getElementText(webElement).startsWith(filterParentName))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("Dropdown element not found by name: " + filterParentName))
            .findElement(By.cssSelector(MULTI_LEVEL_DROPDOWN_NAVIGATE_BUTTON_SELECTOR)));
    }

    protected void selectMultiLevelOption(WebElement dropdown, String filterName) {
        List<WebElement> lovElements = dropdown.findElements(By.cssSelector(MULTI_LEVEL_DROPDOWN_ITEM_SELECTOR));
        clickOnWebElement(lovElements.stream().filter(webElement ->
                getElementText(webElement).startsWith(filterName))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("Dropdown element not found by name: " + filterName))
            .findElement(By.cssSelector(MULTI_LEVEL_DROPDOWN_SELECT_BUTTON_SELECTOR)));
    }

    private void goToTopOfMultiLevelDropdown(String headerName, WebElement dropdown) {
        clickOnWebElement(dropdown.findElements(By.cssSelector(MULTI_LEVEL_DROPDOWN_BREADCRUMB_SELECTOR))
            .stream()
            .filter(webElement -> webElement.getText().equals(headerName))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("Breadcrumb not found by name: " + headerName)));
    }

    protected void moveAndHoverOnWebElement(WebElement element) {
        waitUntilWebElementDisplayed(element);
        Actions action = new Actions(driver);
        action.moveToElement(element).perform();
    }

    protected void moveAndHoverAndClickOnWebElement(WebElement hoverElement, WebElement clickElement) {
        waitUntilElementClickable(hoverElement);
        Actions action = new Actions(driver);
        action.moveToElement(hoverElement).perform();
        waitUntilElementClickable(clickElement);
        action.click(clickElement).perform();
    }

    protected void dragAndDropByWebElement(WebElement hoverElement, int xOffset, int yOffset) {
        waitUntilElementClickable(hoverElement);
        Actions action = new Actions(driver);
        action.dragAndDropBy(hoverElement, xOffset, yOffset).perform();
    }

    protected boolean isWebElementAvailable(By by) {
        try {
            driver.findElement(by);
            return true;
        } catch (NoSuchElementException e) {
            return false;
        }
    }

    protected boolean isChildWebElementAvailable(WebElement parent, By by) {
        try {
            parent.findElement(by);
            return true;
        } catch (NoSuchElementException e) {
            return false;
        }
    }

    @Step("Check if {pageObjectName} is loaded successfully")
    protected void isPageLoaded(String pageObjectName, WebElement... validationElements) {
        log.info("Check if {} is loaded successfully", pageObjectName);
        List<String> invisibleElements = getInvisibleElements(validationElements);
        if (!invisibleElements.isEmpty()) {
            throw new AssertionError(pageObjectName + " page not loaded properly within " + BaseTest.getTimeout() +
                " milliseconds!\nThe following elements are invisible:\n"
                + StringUtils.join(invisibleElements, "\n"));
        }
    }

    protected String getTooltipOfElement(WebElement element) {
        try {
            waitUntilElementIsVisible(element);
        } catch (TimeoutException e) {
            throw new AssertionError("The WebElement for tooltip validation is not present!");
        }

        try {
            By tooltipIndicator = By.cssSelector("[" + TOOLTIP_SHOWN_ATTRIBUTE + "]");

            CONDITION_FACTORY.until(() ->
                isWebElementFromParentCurrentlyDisplayed(element, tooltipIndicator)
                    || element.getDomAttribute(TOOLTIP_SHOWN_ATTRIBUTE) != null);
            String tooltipId = extractTooltipId(element, tooltipIndicator);
            assertThat(tooltipId)
                .as("Tooltip id is null. It probably disappeared")
                .isNotNull();
            return getElementText(By.id(tooltipId), false);
        } catch (ConditionTimeoutException e) {
            throw new AssertionError("The examined WebElement does not have an open/visible tooltip!");
        }
    }

    protected String extractTooltipId(WebElement element, By tooltipIndicator) {
        final String attributeContainingTooltipId = "aria-describedby";
        if (null != getElementAttribute(element, TOOLTIP_SHOWN_ATTRIBUTE)) {
            return getElementAttribute(element, attributeContainingTooltipId);
        }
        List<WebElement> elementsDescribedByTooltip = element.findElements(tooltipIndicator);
        assertThat(elementsDescribedByTooltip)
            .as("Expecting to read exactly one tooltip element to be linked under parent element when the parent is self is not linked with a tooltip.")
            .hasSize(1);
        return getElementAttribute(elementsDescribedByTooltip.get(0), attributeContainingTooltipId);
    }

    protected static List<WebElement> findElements(WebDriver driver, By locator) {
        try {
            waitUntilElementPresent(driver, locator);
            return driver.findElements(locator);
        } catch (NoSuchElementException | TimeoutException e) {
            return Collections.emptyList();
        }
    }

    protected static List<WebElement> findElementsNoWait(WebDriver driver, By locator) {
        try {
            return driver.findElements(locator);
        } catch (WebDriverException e) {
            return Collections.emptyList();
        }
    }

    protected WebElement findElement(By locator) {
        try {
            waitUntilElementPresent(locator);
            return driver.findElement(locator);
        } catch (NoSuchElementException | TimeoutException e) {
            log.error("WebElement could not be located by {}!", locator);
            throw new AssertionError(e.getLocalizedMessage());
        }
    }

    public void removeVisibleTooltips() {
        JavascriptExecutor js;
        if (driver instanceof JavascriptExecutor) {
            js = (JavascriptExecutor) driver;
            if (isWebElementAvailable(By.cssSelector(".tooltip.vue-tooltip-theme"))) {
                boolean tooltipAvailable = true;
                while (tooltipAvailable) {
                    try {
                        js.executeScript("document.getElementsByClassName('tooltip vue-tooltip-theme')[0].remove();");
                        log.info("Removed hanging tooltip");
                    } catch (JavascriptException e) {
                        tooltipAvailable = false;
                    }
                }
            }
            if (isWebElementAvailable(By.cssSelector(".tooltip-inner"))) {
                boolean tooltipAvailable = true;
                while (tooltipAvailable) {
                    try {
                        js.executeScript("document.getElementsByClassName('tooltip-inner')[0].remove();");
                        log.info("Removed hanging tooltip");
                    } catch (JavascriptException e) {
                        tooltipAvailable = false;
                    }
                }
            }
        }
    }

    @Step("Choose system value of input element")
    protected void chooseSystemValueOfInputElement(WebElement element) {
        if (doesAnyParentHasSystemValue(element)) {
            clickOnWebElement(getSystemValueSelectorForInputElement(element));
        } else if (isChildWebElementAvailable(element, By.cssSelector(SYSTEM_VALUE_SELECTOR))) {
            clickOnWebElement(element.findElement(By.cssSelector(SYSTEM_VALUE_SELECTOR)));
        } else {
            throw new AssertionError("The system value cannot be selected because the element does not have one.");
        }
    }

    protected WebElement getSystemValueSelectorForInputElement(WebElement element) {
        int parentLevel = 0;
        String parentSelector = "";
        while (parentLevel < 5) {
            if (isChildWebElementAvailable(element, By.xpath(parentSelector + SYSTEM_VALUE_WRAPPER_XPATH))) {
                return element.findElement(By.xpath(parentSelector + SYSTEM_VALUE_WRAPPER_XPATH))
                    .findElement(By.cssSelector(SYSTEM_VALUE_SELECTOR));
            } else {
                parentLevel++;
                parentSelector = parentSelector.concat("../");
            }
        }
        throw new AssertionError("System value selector not found for element!");
    }

    private boolean doesAnyParentHasSystemValue(WebElement element) {
        int parentLevel = 0;
        String parentSelector = "";
        while (parentLevel < 5) {
            if (isChildWebElementAvailable(element, By.xpath(parentSelector + SYSTEM_VALUE_WRAPPER_XPATH))) {
                return true;
            } else {
                parentLevel++;
                parentSelector = parentSelector.concat("../");
            }
        }
        return false;
    }

    @Step("Choose system value of dropdown element")
    protected void chooseSystemValueOfDropdown(WebElement element) {
        clickOnWebElement(element, false);
        if (isWebElementAvailable(By.cssSelector(SYSTEM_VALUE_DROPDOWN))) {
            clickOnWebElement(findElement(By.cssSelector(SYSTEM_VALUE_DROPDOWN)), false);
        } else {
            throw new AssertionError("The system value cannot be selected because the element does not have one.");
        }
    }

    @Step("Get tooltip text")
    public String getTooltipText() {
        waitUntilElementVisible(By.cssSelector(TOOLTIP));
        return getElementText(By.cssSelector(TOOLTIP));
    }

    @Step("Press ESC to close action menu")
    public void pressEscToCloseActionMenu() {
        log.info("Press ESC to close action menu");
        Actions action = new Actions(driver);
        action.sendKeys(Keys.ESCAPE).perform();
    }

    @Step("Get field validation error")
    public String getFieldValidationError(WebElement element) {
        return element.findElement(By.cssSelector(TSET_FIELD_VALIDATION_ERROR_TEXT)).getText();
    }

    public String getFieldValidationError(String selector) {
        return getFieldValidationError(findElement(By.cssSelector(selector)));
    }

    @Step("Get fields with error")
    public List<String> getFieldsWithError() {
        return this.driver.findElements(
                By.cssSelector("[data-test^=field-]:has(%s)".formatted(TSET_FIELD_VALIDATION_ERROR_TEXT))
            ).stream()
            .map(s -> s.findElement(By.cssSelector(EDITABLE_LABEL)).getText())
            .toList();
    }

    protected byte[] takeScreenshotOfElement(WebElement element) {
        try {
            return element.getScreenshotAs(OutputType.BYTES);
        } catch (Exception e) {
            log.error("Failed to take screenshot of element: {}{}", element.toString(), System.lineSeparator(), e);
            return new byte[0];
        }
    }

    protected boolean isElementClassContainsDisableActionAttribute(WebElement element) {
        try {
            return getElementClassAttribute(element).contains(DISABLED_ACTION_CLASS_ATTRIBUTE);
        } catch (TimeoutException e) {
            return false;
        }
    }

    private boolean isLazyLoadAnimationInvisibleWithTimeout(WebElement element) {
        try {
            return getElementClassAttribute(element).contains("loaded");
        } catch (TimeoutException e) {
            return false;
        }
    }

    /**
     * Set input field value where you also need to set the unit from a dropdown.
     *
     * @param input      The actual input field web element
     * @param unitHeader The head element containing the currently selected unit.
     * @param fieldName  The name of the field used in the unit dropdown DOM
     *                   structure
     *                   This field is used to construct the xpath locator for the
     *                   unit to be selected.
     * @param value      The value to be set.
     *                   In case it's set to null:
     *                   1. The function will log that it's not doing anything with
     *                   a null value.
     *                   2. Return
     * @param unit       The unit to be selected.
     *                   First the function will check the text of the unitHeader
     *                   element
     *                   If the unitHeader element text equals the unit. Then it
     *                   will not change the unit.
     *                   Otherwise, it will open the unit dropdown by:
     *                   1. Click unitHeader to open the dropdown
     *                   2. Construct the xpath locator using fieldName and unit
     *                   3. Click the unit locator to select it
     */
    @NotNull
    protected PageObject setInputFieldWithUnitDropdown(
        WebElement input, WebElement unitHeader, String value, String fieldName, Units unit) {
        if (null == value) {
            log.info("Skip trying to set value to null.");
            return this;
        }
        String currentUnit = getElementText(unitHeader);

        if (!StringUtils.equalsIgnoreCase(currentUnit, unit.getUnitSymbol())) {
            clickOnWebElement(unitHeader);
            final String unitValueXpath = "//*[@data-test='nu-side-units-" + fieldName + "']" +
                "//*[@data-test='list-inline-wrapper']" +
                "//*[contains(text(), '" + unit.getUnitSymbol() + "')]";
            final By unitDropdown = By.xpath(unitValueXpath);
            clickOnWebElement(findElement(unitDropdown));
        }
        sendKeyAndPressEnter(input, value);
        waitUntilLoadingIndicatorIsVisible();
        return this;
    }

    /**
     * The method performs an action and verifies if a desired event happened after or not.
     *
     * @param actionToPerform the Runnable action what needs to be performed in order to get the desired result
     * @param eventToHappen   The expected event to happen after the action had performed
     * @return true if the desired event occurred and false otherwise
     */
    protected boolean doActionAndVerifyEvent(Runnable actionToPerform, Callable<Boolean> eventToHappen) {
        actionToPerform.run();
        try {
            return eventToHappen.call();
        } catch (Exception e) {
            log.warn("The expected event does not occurred!");
            return false;
        }
    }

    /**
     * The method continuously performs the action until the anticipated event occurs,
     * or the specified timeout period is exceeded.
     *
     * @param actionToPerform the Runnable action what needs to be performed in order to get the desired result
     * @param eventToHappen   The expected event to happen after the action had performed
     */
    protected void performActionAndWaitUntilAnEventHappens(Runnable actionToPerform, Callable<Boolean> eventToHappen,
                                                           String... message) {
        CONDITION_FACTORY.alias(StringUtils.join(message))
            .until(() -> doActionAndVerifyEvent(actionToPerform, eventToHappen));
    }

    protected boolean isElementFocused(WebElement element) {
        return element.equals(driver.switchTo().activeElement());
    }

    protected boolean isWebElementHidden(WebElement element) {
        try {
            CONDITION_FACTORY.until(() -> isHidden(element));
        } catch (ConditionTimeoutException e) {
            return false;
        }
        return true;
    }

    private static boolean isHidden(WebElement element) {
        try {
            return !element.isDisplayed();
        } catch (NoSuchElementException nse) {
            return true;
        }
    }
}
