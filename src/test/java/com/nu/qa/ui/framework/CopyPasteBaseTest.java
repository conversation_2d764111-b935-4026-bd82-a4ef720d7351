package com.nu.qa.ui.framework;

import com.nu.qa.ui.pageobjects.CalculationPage;
import com.nu.qa.ui.pageobjects.ProjectDashboardPage;
import com.nu.qa.ui.pageobjects.tabs.ManufacturingTab;
import com.nu.qa.ui.pageobjects.tabs.ParametersTab;
import com.nu.qa.ui.pageobjects.widgets.BomExplorerPanel;
import com.nu.qa.utilities.data.enums.units.Dimensions;
import com.nu.qa.utilities.utilities.RandomUtil;
import com.nu.qa.utilities.utilities.ValueUtil;
import io.qameta.allure.Step;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.data.Percentage;
import org.testng.ITestResult;
import org.testng.annotations.AfterMethod;

import static com.nu.qa.ui.data.testData.ProjectImportConstants.COPY_PASTE_BASE_PROJECT;
import static com.nu.qa.ui.framework.utils.apiUtils.ProjectUtil.deleteProjectKeyViaApiAndCheckIfDeleted;
import static com.nu.qa.utilities.utilities.RandomUtil.getPartNumber;
import static com.nu.qa.utilities.utilities.RandomUtil.getWithoutCostModulePartName;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

@Slf4j
public abstract class CopyPasteBaseTest extends CalculationBaseTest {

    public static final String MACHINE_OPERATOR = "Machine operator";
    protected String projectKey;
    protected String targetCalcPartName;
    protected static final String ASSEMBLY_10_KG_STEP_DESIGNATION = "Assembly (10kg)";

    @AfterMethod
    public void logout(ITestResult result) {
        try {
            if (result.isSuccess()) {
                logout(getProjectDashboardPage());
            }
        } catch (Throwable t) {
            log.error("Logout failed with exception", t);
        }

    }

    @Step("Delete the test data at the end of the test")
    protected void deleteTestData() {
        deleteProjectKeyViaApiAndCheckIfDeleted(getTestUser(), getBaseUrl(), projectKey);
    }

    @Step("Import precondition calculations into a new project")
    protected ProjectDashboardPage importCopyPastePreconditionCalculations() {
        projectKey = RandomUtil.getNLongRandomString(8);
        return importProjectViaAPIAndOpenIt(projectKey, getFolderId(), COPY_PASTE_BASE_PROJECT.getArchiveFile());
    }

    @Step("Create a target calculation and add a manufacturing step to it")
    protected CalculationPage createTargetCalculationAndAddManufacturingStep() {
        targetCalcPartName = getWithoutCostModulePartName();
        return createTargetCalculationAndAddManufacturingStep(this.targetCalcPartName, ASSEMBLY_10_KG_STEP_DESIGNATION);
    }

    @Step("Create a target calculation {targetCalcPartName} and add a manufacturing step {stepDesignation} to it")
    protected CalculationPage createTargetCalculationAndAddManufacturingStep(String targetCalcPartName, String stepDesignation) {
        CalculationPage calculationPage = createCalculationWithoutCostModule(getProjectDashboardPage(),
            LOCATION, targetCalcPartName, getPartNumber(), Dimensions.PIECE);

        ManufacturingTab manufacturingTab = calculationPage.clickManufacturingTabButton();
        manufacturingTab.clickAddButton()
            .clickAddStepButton()
            .addMasterDataStep(stepDesignation, "Basic Station");

        saveCalculation(calculationPage);

        return calculationPage;
    }

    private static String errorMessageWhenChildNodesMissingForNode(String node) {
        return node + " bom node is missing some of it's expected child nodes";
    }

    @Step("Verify calculation with cost A")
    protected void checkCalculationWithCostA(BomExplorerPanel bomExplorerPanel) {
        final String BENTONITE = "Bentonite";
        softAssert.assertThat(bomExplorerPanel.getNodeAndEntityNamesForNode(PROJECT_CALCULATION_A, BENTONITE))
            .as(errorMessageWhenChildNodesMissingForNode(PROJECT_CALCULATION_A))
            .contains(SUB_CALCULATION_A, "11SMn30", BENTONITE);
        softAssert.assertAll();
        bomExplorerPanel.expandBomNodeByPartName(SUB_CALCULATION_A);
        checkSubcalcWithCostA(bomExplorerPanel);
        bomExplorerPanel.collapseBomNodeByPartName(SUB_CALCULATION_A);
    }

    @Step("Verify subcalculation with cost A")
    protected void checkSubcalcWithCostA(BomExplorerPanel bomExplorerPanel) {
        final String FETTLING_NODE = "Fettling";
        softAssert.assertThat(bomExplorerPanel.getNodeAndEntityNamesForNode(SUB_CALCULATION_A, FETTLING_NODE))
            .as(errorMessageWhenChildNodesMissingForNode(SUB_CALCULATION_A))
            .contains(
                "GJL-250", "New molding sand", "Depositing molding sand", "Bentonite", "Casting filter",
                "Water", "Feeder", "Coke", "Melting", "Sand casting", "Shot blasting", FETTLING_NODE);

        softAssert.assertAll();

        bomExplorerPanel.expandBomEntryByName(FETTLING_NODE);
        softAssert.assertThat(bomExplorerPanel.isBomEntryHasChildBomEntryByName("Fettling", "Bartsch Flexcell"))
            .as("Manufacturing step Fettling should contain Bartsch Flexcell")
            .isTrue();
        softAssert.assertThat(bomExplorerPanel.getNodeAndEntityNamesForEntry(FETTLING_NODE, MACHINE_OPERATOR))
            .as(errorMessageWhenChildNodesMissingForNode(FETTLING_NODE))
            .contains("Bartsch Flexcell", "Robot gripper fettling automated small", MACHINE_OPERATOR);
        softAssert.assertAll();
        bomExplorerPanel.collapseBomEntryByName(FETTLING_NODE); // contains
    }

    @Step("Verify calculation with cost B")
    protected void checkCalculationWithCostB(BomExplorerPanel bomExplorerPanel) {
        final String CHILL_CASTING_NODE = "Chill casting";
        softAssert.assertThat(bomExplorerPanel.getNodeAndEntityNamesForNode(PROJECT_CALCULATION_B, CALCULATION_B_BONDING_BOM_ENTRY_NAME))
            .as(errorMessageWhenChildNodesMissingForNode(PROJECT_CALCULATION_B))
            .contains(
                SUB_CALCULATION_B, "100Cr6", "AlMg3", "Casting filter", "Casting filter chill casting",
                CHILL_CASTING_NODE, CALCULATION_B_BONDING_BOM_ENTRY_NAME);
        softAssert.assertAll();

        bomExplorerPanel.expandBomNodeByPartName(SUB_CALCULATION_B);
        checkSubcalcWithCostB(bomExplorerPanel);
        bomExplorerPanel.collapseBomNodeByPartName(SUB_CALCULATION_B);

        bomExplorerPanel.expandBomEntryByName(CHILL_CASTING_NODE);
        softAssert.assertThat(bomExplorerPanel.getNodeAndEntityNamesForEntry(CHILL_CASTING_NODE, MACHINE_OPERATOR))
            .as(errorMessageWhenChildNodesMissingForNode(CHILL_CASTING_NODE))
            .contains("Reis Cast frame (gravity)", "Gravity Casting Gravity Die", MACHINE_OPERATOR);
        softAssert.assertAll();
        bomExplorerPanel.collapseBomEntryByName(CHILL_CASTING_NODE);

        bomExplorerPanel.expandBomEntryByName(CALCULATION_B_BONDING_BOM_ENTRY_NAME);
        softAssert.assertThat(bomExplorerPanel.getNodeAndEntityNamesForEntry(CALCULATION_B_BONDING_BOM_ENTRY_NAME, MACHINE_OPERATOR))
            .as(errorMessageWhenChildNodesMissingForNode(CALCULATION_B_BONDING_BOM_ENTRY_NAME))
            .contains("Asys AES 03D loading station", "Test tool", MACHINE_OPERATOR);
        softAssert.assertAll();
        bomExplorerPanel.collapseBomEntryByName(CALCULATION_B_BONDING_BOM_ENTRY_NAME);
    }

    @Step("Verifying Subcalculation With cost B")
    protected void checkSubcalcWithCostB(BomExplorerPanel bomExplorerPanel) {
        softAssert.assertThat(bomExplorerPanel.getNodeAndEntityNamesForNode(SUB_CALCULATION_B, CALCULATION_B_BALL_BEARING_ASSEMBLY_BOM_ENTRY_NAME))
            .as(errorMessageWhenChildNodesMissingForNode(SUB_CALCULATION_B))
            .contains("11SMn30", "Coke", CALCULATION_B_BALL_BEARING_ASSEMBLY_BOM_ENTRY_NAME);
        softAssert.assertAll(); // contains all in this order

        bomExplorerPanel.expandBomEntryByName(CALCULATION_B_BALL_BEARING_ASSEMBLY_BOM_ENTRY_NAME);
        final String OPERATOR_LINE_LEADER = "Operator line leader";
        softAssert.assertThat(bomExplorerPanel.getNodeAndEntityNamesForEntry(CALCULATION_B_BALL_BEARING_ASSEMBLY_BOM_ENTRY_NAME, OPERATOR_LINE_LEADER))
            .as(errorMessageWhenChildNodesMissingForNode(CALCULATION_B_BALL_BEARING_ASSEMBLY_BOM_ENTRY_NAME))
            .contains("Assembly line", "Test tool", OPERATOR_LINE_LEADER);
        softAssert.assertAll();
        bomExplorerPanel.collapseBomEntryByName(CALCULATION_B_BALL_BEARING_ASSEMBLY_BOM_ENTRY_NAME);
    }

    @Step("Verify calculation with cost C")
    protected void checkCalculationWithCostC(BomExplorerPanel bomExplorerPanel) {
        final String CUTTING_NODE = "Cutting";
        softAssert.assertThat(bomExplorerPanel.getNodeAndEntityNamesForNode(PROJECT_CALCULATION_C, CUTTING_NODE))
            .as(errorMessageWhenChildNodesMissingForNode(PROJECT_CALCULATION_C))
            .contains(GENERATED_SUB_CALCULATION_C, "GMR 235", "Depositing molding sand", CUTTING_NODE);
        softAssert.assertAll();

        bomExplorerPanel.expandBomEntryByName(CUTTING_NODE);
        softAssert.assertThat(bomExplorerPanel.getNodeAndEntityNamesForEntry(CUTTING_NODE, MACHINE_OPERATOR))
            .as(errorMessageWhenChildNodesMissingForNode(CUTTING_NODE))
            .contains("Abrasive cutting system with peripherals", "Cutting device", MACHINE_OPERATOR);
        softAssert.assertAll();
        bomExplorerPanel.collapseBomEntryByName(CUTTING_NODE);
    }

    @Step("Validate child calculation '{childCalcName}' after paste")
    protected void validateChildCalculationAfterPaste(BomExplorerPanel bomExplorerPanel, String calcName, String childCalcName) {
        CalculationPage calculationPage = bomExplorerPanel.clickOnBomNodeChildBomNodeByPartName(calcName, childCalcName);
        bomExplorerPanel.hideCalculationPreview();
        ParametersTab parametersTab = calculationPage.clickParametersTabButton();

        softAssert.assertThat(ValueUtil.parseDoubleWithWhitespaces(parametersTab.getProductionVolumePerYearValue()))
            .as("Peak volume was not inherited properly!")
            .isGreaterThan(ValueUtil.parseDoubleWithWhitespaces(PEAK_VALUE))
            .isCloseTo(ValueUtil.parseDoubleWithWhitespaces(PEAK_VALUE), Percentage.withPercentage(5));
        softAssert.assertThat(ValueUtil.parseDoubleWithWhitespaces(parametersTab.getAverageProductionVolumePerYearInputValue()))
            .as("Average volume was not inherited properly!")
            .isGreaterThan(ValueUtil.parseDoubleWithWhitespaces(AVERAGE_VALUE))
            .isCloseTo(ValueUtil.parseDoubleWithWhitespaces(AVERAGE_VALUE), Percentage.withPercentage(5));
        softAssert.assertAll();
    }

    @Step("Validate updated data after master data update for calculation: {calculation} and machine: {machineName}")
    protected void checkUpdatedMachineNameAfterMasterDataUpdate(
        BomExplorerPanel bomExplorerPanel, String calculation, String partName, String machineName) {
        bomExplorerPanel.expandBomNodeByPartName(calculation);
        bomExplorerPanel.expandBomEntryByName(CALCULATION_B_BONDING_BOM_ENTRY_NAME);
        CalculationPage calculationPage = bomExplorerPanel.clickOnBomNodeChildBomNodeByPartName(partName, calculation);
        calculationPage.clickOnOpenCalculationUpdateSidePanelButton()
            .clickOnUpdateCurrentCalculationButton(new ParametersTab(getDriver()))
            .closeCalculationSidePanel();
        assertThat(bomExplorerPanel.isBomEntryHasChildBomEntryByName(CALCULATION_B_BONDING_BOM_ENTRY_NAME, machineName))
            .as("Manufacturing step Bonding should contain " + machineName)
            .isTrue();
        bomExplorerPanel.collapseBomEntryByName(CALCULATION_B_BONDING_BOM_ENTRY_NAME);
    }
}
