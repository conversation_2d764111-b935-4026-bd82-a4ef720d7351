package com.nu.qa.ui.pageobjects.costmodule;

import com.nu.qa.ui.data.enums.Technology;
import com.nu.qa.ui.pageobjects.modals.CalculationCreationModal;
import com.nu.qa.ui.pageobjects.widgets.GlobalHeader;
import io.qameta.allure.Step;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.PageFactory;

@Getter
@Slf4j
public class TechnologyPage extends CostModuleWizardPage {

    GlobalHeader globalHeader;

    public TechnologyPage(WebDriver driver) {
        this.driver = driver;
        globalHeader = new GlobalHeader(driver);
        waitUntilLoadingIndicatorIsVisible();
        waitUntilAllPresented(By.cssSelector("img[data-test='base-part-image']"));
        waitUntilAllPresented(By.cssSelector("[data-test^='wizard-grouped-card-selection']"));
        waitUntilAllPresented(By.cssSelector(CONTINUE_BUTTON_SELECTOR));
        PageFactory.initElements(driver, this);
    }

    @Step("Proceed to Weight and Material page")
    public WeightAndMaterialPage proceedToWeightAndMaterialPage() {
        clickContinueButton();
        return new WeightAndMaterialPage(driver);
    }

    @Step("Step back to Calculation creation page")
    public CalculationCreationModal stepBackToCalculationCreationPage() {
        clickBackButton();
        return new CalculationCreationModal(driver);
    }

    @Step("Choose {technology} from technologies")
    public WeightAndMaterialPage chooseTechnology(Technology technology) {
        log.info("Choose {} from technologies", technology.getName());
        moveAndClickOnWebElement(getWebElementFromTechnology(technology));
        return proceedToWeightAndMaterialPage();
    }

    @Step("Choose PDS from technologies")
    public ShapePage choosePDS() {
        log.info("Choose PDS from technologies");
        clickOnWebElement(getWebElementFromTechnology(Technology.PROGRESSIVE_DIE_STAMPING));
        clickContinueButton();
        return new ShapePage(driver);
    }

    @Step("Choose TDS from technologies")
    public ShapePage chooseTDS() {
        log.info("Choose TDS from technologies");
        clickOnWebElement(getWebElementFromTechnology(Technology.TRANSFER_DIE_STAMPING));
        clickContinueButton();
        return new ShapePage(driver);
    }

    @Step("Choose PCB from technologies")
    public TechnologySpecificPage choosePCB() {
        log.info("Choose PCB from technologies");
        moveAndClickOnWebElement(getWebElementFromTechnology(Technology.PCB));
        clickContinueButton();
        return new TechnologySpecificPage(driver);
    }

    @Step("Choose Packaging from technologies")
    public ShapePage choosePbox() {
        log.info("Choose Packaging from technologies");
        clickOnWebElement(getWebElementFromTechnology(Technology.PRINTED_CARDBOARD_BOX));
        clickContinueButton();
        return new ShapePage(driver);
    }

    private WebElement getWebElementFromTechnology(Technology technology) {
        By locator = By.id(technology.getId());
        waitUntilElementVisible(locator);
        return findElement(locator);
    }

}
