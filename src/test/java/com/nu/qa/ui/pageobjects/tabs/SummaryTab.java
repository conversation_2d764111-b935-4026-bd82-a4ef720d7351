package com.nu.qa.ui.pageobjects.tabs;

import com.nu.qa.ui.framework.PageObject;
import com.nu.qa.ui.framework.pageFactory.FindByDataTest;
import com.nu.qa.ui.pageobjects.modals.AddMaterialModal;
import com.nu.qa.ui.pageobjects.modals.ChangeCalculationTypeModal;
import com.nu.qa.ui.pageobjects.modals.Shape3DViewModal;
import com.nu.qa.utilities.utilities.ValueUtil;
import io.qameta.allure.Step;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

import java.util.List;

@Slf4j
public class SummaryTab extends PageObject {

    @FindBy(xpath = "//*[contains(@class, 'total-row')]//*[@data-test='cbd-total-value']")
    private WebElement totalRowValue;

    @FindBy(css = "[id='statics.productionEnvironment'] section")
    private WebElement economicInformationContainer;

    @FindBy(xpath = "//*[contains(@class, 'parent-row') and contains(.,'Material')]//*[@data-test='cbd-total-value']")
    private WebElement materialTotalValue;

    @FindBy(xpath = "//*[contains(@class, 'parent-row') and contains(.,'Manufacturing')]//*[@data-test='cbd-total-value']")
    private WebElement manufacturingTotalValue;

    @FindBy(xpath = "//*[contains(@class, 'parent-row') and contains(.,'Special direct costs')]//*[@data-test='cbd-total-value']")
    private WebElement SDCTotalValue;

    @FindBy(xpath = "//*[contains(@class, 'parent-row') and contains(.,'Overheads')]//*[@data-test='cbd-total-value']")
    private WebElement overheadsTotalValue;

    @FindBy(css = ".grand-child-row")
    private WebElement subcalculationsRows;

    @FindBy(css = "[data-test='the-production-environment-location']")
    private WebElement overviewLocationValue;

    @FindBy(css = "[data-test='the-production-environment'] [data-test='tset-badge']")
    private WebElement overviewProcurementValue;

    @FindBy(css = "[data-test='nu-overview-field-life-time'] " + READONLY_LAYOUT_INPUT)
    private WebElement overviewLifetimeValue;

    @FindBy(css = "[data-test='nu-overview-field-average-usable-production-volume-per-year'] " + READONLY_LAYOUT_INPUT)
    private WebElement overviewAverageVolume;

    @FindBy(css = "[data-test='nu-overview-field-average-volume-over-life-time'] " + READONLY_LAYOUT_INPUT)
    private WebElement overviewProdVolumeOverLifetime;

    @FindBy(css = "[data-test='nu-overview-field-calls-per-year'] " + READONLY_LAYOUT_INPUT)
    private WebElement overviewLotsPerYearValue;

    @FindBy(css = "#statics\\.roughCalculation .label")
    private WebElement roughEstimateValue;

    private static final String SCROLL_CONTAINER_ID = "statics.costBreakdown";
    @FindBy(id = SCROLL_CONTAINER_ID)
    private WebElement scrollContainer;

    @FindBy(id = "statics.emissionBreakdown")
    private WebElement scrollContainerCo2Mode;

    @FindBy(css = "[data-test='the-cost-breakdown-table-breakdown-legend']")
    private WebElement legendText;

    @FindBy(css = "[data-test='the-cost-breakdown-table-chart']")
    private WebElement chart;

    @FindBy(css = ".child-row-right")
    private List<WebElement> costBreakdownTableRows;

    @FindByDataTest("shape-modal-button-open-shape-modal")
    private WebElement open3DViewModalButton;

    @FindBy(css = "[data-test='tset-lazy-load-parent']")
    private WebElement summaryTable;

    @FindByDataTest("empty-breakdown-card-add-material")
    private WebElement addMaterialTile;

    @FindByDataTest("MATERIAL")
    private WebElement addMaterialButton;

    @FindByDataTest("empty-breakdown-card-change-calculation-type")
    private WebElement changeCalculationTypeButton;

    public SummaryTab(WebDriver driver) {
        this.driver = driver;
        PageFactory.initElements(driver, this);
        waitUntilLoadingIndicatorIsVisible();
        waitUntilLazyLoadAnimationIsVisible();
        if (costBreakdownTableRows.isEmpty()) {
            isPageLoaded(this.getClass().getSimpleName(), economicInformationContainer,
                    overviewLocationValue);
        } else {
            isPageLoaded(this.getClass().getSimpleName(), economicInformationContainer,
                    overviewLocationValue, costBreakdownTableRows.get(0));
        }
    }

    private void scrollToBottom() {
        log.info("Scroll to bottom of Summary Tab");
        if (isWebElementAvailable(By.id(SCROLL_CONTAINER_ID))) {
            scrollToBottomOfElement(scrollContainer);
        } else {
            scrollToBottomOfElement(scrollContainerCo2Mode);
        }
    }

    public String getTotalRowValue() {
        scrollToBottom();
        return getElementText(totalRowValue);
    }

    public String getMaterialTotalValue() {
        scrollToElement(summaryTable);
        return getElementText(materialTotalValue);
    }

    public String getSubcalculationRows() {
        return getElementText(subcalculationsRows);
    }

    public String getManufacturingTotalValue() {
        scrollToElement(summaryTable);
        return getElementText(manufacturingTotalValue);
    }

    public String getSDCTotalValue() {
        scrollToBottom();
        return getElementText(SDCTotalValue);
    }

    public String getOverheadsTotalValue() {
        scrollToBottom();
        return getElementText(overheadsTotalValue);
    }

    public String getRoughEstimateValue() {
        return getElementText(roughEstimateValue);
    }

    public Double getTotalDoubleValue() {
        String actValue = getTotalRowValue();
        if (actValue != null && !actValue.isEmpty()) {
            actValue = actValue.replace(" ", "");
            return ValueUtil.parseDoubleWithUnit(actValue);
        }
        return null;
    }

    public String getOverviewLocationValue() {
        return getElementText(overviewLocationValue);
    }

    public String getOverviewProcurementValue() {
        return getElementText(overviewProcurementValue);
    }

    public String getOverviewLifetimeValue() {
        return getElementText(overviewLifetimeValue);
    }

    public String getOverviewAverageVolumeValue() {
        waitUntilWebElementDisplayed(overviewAverageVolume);
        return getElementText(overviewAverageVolume);
    }

    public String getOverviewAverageVolumeUnit() {
        waitUntilWebElementDisplayed(overviewAverageVolume);
        return getElementText(overviewAverageVolume.findElement(By.cssSelector("span:last-child span")));
    }

    public String getOverviewProdVolumeOverLifetimeValue() {
        return getElementText(overviewProdVolumeOverLifetime);
    }

    public String getOverviewProdVolumeOverLifetimeUnit() {
        waitUntilWebElementDisplayed(overviewProdVolumeOverLifetime);
        return getElementText(overviewProdVolumeOverLifetime.findElement(By.cssSelector("span:last-child span")));
    }

    public String getOverviewLotsPerYearValueValue() {
        waitUntilWebElementDisplayed(overviewLotsPerYearValue);
        return getElementText(overviewLotsPerYearValue);
    }

    public int numberOfExpandedRows() {
        return driver.findElements(By.cssSelector(".child-wrapper:not([style='display: none;'])")).size();
    }

    public boolean isChartVisible() {
        return isWebElementCurrentlyDisplayed(chart);
    }

    @Step("Click on 'Open 3D View modal' button")
    public Shape3DViewModal clickOnOpen3DViewModal() {
        log.info("Click on 'Open 3D View modal' button");
        scrollToBottom();
        clickOnWebElement(open3DViewModalButton);
        return new Shape3DViewModal(driver);
    }

    @Step("Open add material modal")
    public AddMaterialModal openAddMaterialModal() {
        log.info("Open add material modal");
        clickOnWebElement(addMaterialTile);
        clickOnWebElement(addMaterialButton);
        return new AddMaterialModal(driver);
    }

    @Step("Open change calculation type modal")
    public ChangeCalculationTypeModal openChangeCalculationTypeModal() {
        log.info("Open change calculation type modal");
        clickOnWebElement(changeCalculationTypeButton);
        return new ChangeCalculationTypeModal(driver);
    }
}
