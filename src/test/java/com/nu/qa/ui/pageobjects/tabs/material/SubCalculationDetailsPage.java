package com.nu.qa.ui.pageobjects.tabs.material;

import com.nu.qa.ui.pageobjects.CalculationPage;
import com.nu.qa.ui.pageobjects.PageWithGlobalHeader;
import com.nu.qa.ui.pageobjects.tabs.DetailsPage;
import com.nu.qa.ui.pageobjects.widgets.GlobalHeader;
import io.qameta.allure.Step;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

@Slf4j
public class SubCalculationDetailsPage extends DetailsPage implements PageWithGlobalHeader {

    @FindBy(css = "#tsetinput-quantity input")
    private WebElement quantityInput;

    @FindBy(css = "[data-test='button-go-to-calculation']")
    private WebElement goToCalculationButton;

    @Getter
    private final GlobalHeader globalHeader;

    public SubCalculationDetailsPage(WebDriver driver) {
        this.driver = driver;
        this.globalHeader = new GlobalHeader(driver);
        PageFactory.initElements(driver, this);
        isPageLoaded(this.getClass().getSimpleName(), quantityInput, goToCalculationButton);
    }

    @Step("Enter subcalculation quantity: {value}")
    public void enterSubCalculationQuantity(String value) {
        log.info("Enter subcalculation quantity: {}", value);
        sendKeyAndPressEnter(quantityInput, value);
        waitUntilLoadingIndicatorIsVisible();
    }

    @Step("Click Go to calculation button")
    public CalculationPage clickGoToCalculationButton() {
        log.info("Click Go to calculation button");
        clickOnWebElement(goToCalculationButton);
        return new CalculationPage(driver);
    }
}
