package com.nu.qa.ui.pageobjects.modals;

import io.qameta.allure.Step;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

@Slf4j
public class Shape3DViewModal extends BasicModal {
    private static final String SHAPE_3D_MODAL_SELECTOR =
            "[data-modal='shapeModal']";

    @FindBy(css = SHAPE_3D_MODAL_SELECTOR)
    private WebElement shapeModal;

    @FindBy(css = SHAPE_3D_MODAL_SELECTOR +" canvas")
    private WebElement shapeModalView;

    @FindBy(css = SHAPE_3D_MODAL_SELECTOR + " [data-test='button-close-modal']")
    protected WebElement closeButton;

    public Shape3DViewModal(WebDriver driver) {
        this.driver = driver;
        PageFactory.initElements(driver, this);
        isPageLoaded(this.getClass().getSimpleName(), closeButton, shapeModal);
    }

    public boolean is3dModalCanvasVisible() {
        return isWebElementVisible(shapeModalView);
    }

    @Step("Click modal's Close button")
    public void clickModalsCloseButton() {
        log.info("Click modal's Close button");
        clickOnWebElement(closeButton);
        waitUntilModalCloses(By.cssSelector(SHAPE_3D_MODAL_SELECTOR));
    }
}
