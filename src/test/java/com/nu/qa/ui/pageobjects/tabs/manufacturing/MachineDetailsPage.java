package com.nu.qa.ui.pageobjects.tabs.manufacturing;

import com.nu.qa.ui.pageobjects.PageWithGlobalHeader;
import com.nu.qa.ui.pageobjects.tabs.DetailsPage;
import com.nu.qa.ui.pageobjects.widgets.GlobalHeader;
import com.nu.qa.ui.pageobjects.widgets.TabulatorTable;
import com.nu.qa.utilities.data.enums.Currencies;
import com.nu.qa.utilities.utilities.ValueUtil;
import io.qameta.allure.Step;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

@Slf4j
public class MachineDetailsPage extends DetailsPage implements PageWithGlobalHeader {

    @FindBy(css = "#investBase input")
    private WebElement investBaseInput;

    @FindBy(css = "#investFundament input")
    private WebElement investFundamentInput;

    @FindBy(css = "#investMisc input")
    private WebElement investOtherInput;

    @FindBy(css = "#investSetup input")
    private WebElement investSetupInput;

    @FindBy(css = "#totalInvest " + READONLY_PLAIN_VALUE)
    private WebElement totalInvest;

    @FindBy(css = "[data-test='field-investBase-MACHINE'] " + UNLINK_ICON)
    public WebElement investBaseUnlinkIcon;

    @FindBy(css = "#setupCostPerPart")
    private WebElement setupCostPerPart;

    @FindBy(css = "#setupCostPerLot")
    private WebElement setupCostPerLot;

    @FindBy(css = "#roughMachineSetupCostPerLot")
    private WebElement roughMachineSetupCostPerLot;

    @FindBy(css = "#roughMachineSetupCostPerPart")
    private WebElement roughMachineSetupCost;

    @FindBy(css = "#systemDownTime input")
    private WebElement systemDownTime;

    @FindBy(css = "#maintenanceRate input")
    private WebElement maintenanceRate;

    @FindBy(css = "#depreciationTime input")
    private WebElement depreciationTime;

    @FindBy(css = "#technicalDescription textarea")
    private WebElement descriptionInput;

    @FindBy(css = "#manufacturer input")
    private WebElement manufacturerInput;

    @FindBy(css = ("#variableCostsFraction input"))
    private WebElement variableCostsFractionNonOccupancyInput;

    @FindBy(css = "[data-test='field-variableCostsFraction-MACHINE'] " + EDITABLE_LABEL)
    private WebElement variableCostsFractionNonOccupancyLabel;

    @FindBy(css = "#variableCostsFraction")
    private WebElement variableCostsFractionNonOccupancyValue;

    private final String APPORTIONMENT_TYPE_DROPDOWN = "//*[@data-test='nuinputselect-apportionmentType']";

    @FindBy(css ="#interestRate input")
    private WebElement interestRate;

    @FindBy(css ="#leasingFeeProduction input")
    private WebElement spaceCost;

    @FindBy(css ="#energyCost input")
    private WebElement electricityCost;

    @FindBy(css = "#electricityCarbon input")
    private WebElement emissionElectricity;

    @FindBy(css = "#naturalGasEmissionDirect input")
    private WebElement naturalGasEmissionActive;

    @FindBy(css = "#naturalGasEmissionInDirect input")
    private WebElement naturalGasEmissionPassive;

    @FindBy(css = "[data-test='object-viewer-json-footprint']")
    public WebElement footprintSection;

    @FindBy(css = "#machineCO2Footprint input")
    private WebElement machineCO2Footprint;

    @Getter
    private final GlobalHeader globalHeader;

    private final TabulatorTable machineCostTable;

    public MachineDetailsPage(WebDriver driver) {
        this.driver = driver;
        this.globalHeader = new GlobalHeader(driver);
        this.machineCostTable = new TabulatorTable(driver, "[id='machineCost']");
        waitUntilTableLoadingIndicatorIsVisible();
        PageFactory.initElements(driver, this);
        isPageLoaded(this.getClass().getSimpleName(), designation, objectHeaderValueWithUnit);
    }

    public String getHeaderTotalValue() {
        waitUntilWebElementDisplayed(objectHeaderValueWithUnit);
        return objectHeaderValueWithUnit.getText();
    }

    @Step("Get Machine's base currency")
    public String getBaseCurrency() {
        log.info("Get Machine's base currency");
        return getDropDownElementText(By.id("baseCurrency"));
    }

    @Step("Change Machine's base currency to: {currencies}")
    public MachineDetailsPage editBaseCurrency(Currencies currencies) {
        log.info("Change base currency to: {}", currencies);
        chooseFromDropDownByOptionTextWithXpath(BASE_CURRENCY_DROPDOWN, currencies.getCurrencyName(), currencies.getIsoCode());
        return this;
    }

    @Step("Enter Base investment {investBase}")
    public MachineDetailsPage enterInvestBase(String investBase) {
        log.info("Enter Base investment {}", investBase);
        sendKey(investBaseInput, investBase);
        sendKeyToBrowser(Keys.ENTER);
        waitUntilLoadingIndicatorIsVisible();
        return this;
    }

    public String getInvestBaseValue() {
        return getElementValueAttribute(investBaseInput);
    }

    public String getInvestFundamentValue() {
        return getElementValueAttribute(investFundamentInput);
    }

    public String getInvestOtherValue() {
        return getElementValueAttribute(investOtherInput);
    }

    public String getInvestSetupValue() {
        return getElementValueAttribute(investSetupInput);
    }

    public String getTotalInvestValue() {
        return getElementText(totalInvest);
    }

    public boolean isInvestBaseUnlinkIconVisible() {
        return isWebElementVisible(investBaseUnlinkIcon);
    }

    public String getSetupCostPerPartValue() {
        waitUntilWebElementDisplayed(setupCostPerPart);
        return getElementText(setupCostPerPart);
    }

    public String getSetupCostPerLotValue() {
        scrollToElement(setupCostPerLot);
        return getElementText(setupCostPerLot);
    }

    public String getRoughMachineSetupCostPerLotValue() {
        scrollToElement(roughMachineSetupCostPerLot);
        return getElementText(roughMachineSetupCostPerLot);
    }

    public String getRoughMachineSetupCost() {
        return getElementText(roughMachineSetupCost);
    }

    public String getSystemDownTime() {
        waitUntilWebElementDisplayed(systemDownTime);
        return getElementValueAttribute(systemDownTime);
    }

    @Step("Get Machine's Designation")
    public String getMachineDesignation() {
        log.info("Get Machine's Designation");
        return super.getTitleDesignation();
    }

    @Step("Get Maintenance rate")
    public String getMaintenanceRate() {
        log.info("Get Maintenance rate");
        return getElementValueAttribute(maintenanceRate);
    }

    @Step("Get Depreciation time")
    public String getDepreciationTime() {
        log.info("Get Depreciation time");
        return getElementValueAttribute(depreciationTime);
    }

    @Step("Get Description value")
    public String getDescriptionValue() {
        log.info("Get Description value");
        return getElementValueAttribute(descriptionInput).split("\\n")[0];
    }

    public boolean isDescriptionVisible() {
        return isWebElementVisible(descriptionInput);
    }

    @Step("Get Manufacturer value")
    public String getManufacturerValue() {
        log.info("Get Manufacturer value");
        return getElementValueAttribute(manufacturerInput);
    }

    @Step("Select {apportionmentType} class from the list")
    public MachineDetailsPage selectApportionmentType(String apportionmentType) {
        log.info("Select {} from the list", apportionmentType);
        chooseFromDropDownByOptionTextWithXpath(APPORTIONMENT_TYPE_DROPDOWN, apportionmentType);
        waitUntilLoadingIndicatorIsVisible();
        return this;
    }

    public boolean isVariableCostsFractionNonOccupancyLabelVisible() {
        return isWebElementVisible(variableCostsFractionNonOccupancyLabel);
    }

    public boolean isVariableCostsFractionNonOccupancyValueVisible() {
        return isWebElementVisible(variableCostsFractionNonOccupancyValue);
    }

    public MachineDetailsPage enterVariableCostsFractionNonOccupancy(String variableCostsFraction) {
        log.info("Enter Variable Costs Fraction Non Occupancy: {}", variableCostsFraction);
        sendKey(variableCostsFractionNonOccupancyInput, variableCostsFraction);
        clickOnWebElement(variableCostsFractionNonOccupancyLabel);
        waitUntilLoadingIndicatorIsVisible();
        return this;
    }

    @Step("Enter Machine CO₂e Footprint: {footprint}")
    public MachineDetailsPage enterMachineCO2Footprint(String footprint) {
        log.info("Enter Machine CO₂e Footprint: {}", footprint);
        sendKeyAndPressEnter(machineCO2Footprint, footprint);
        waitUntilLoadingIndicatorIsVisible();
        return this;
    }

    public boolean ifFootprintSectionVisible() {
        return isWebElementVisible(footprintSection);
    }

    public String getVariableCostFractionNonOccupancyValue() {
        return getElementValueAttribute(variableCostsFractionNonOccupancyInput);
    }

    public double getCostNonOccupancyValue() {
        return ValueUtil.parseDoubleWithWhitespaces(machineCostTable.getTextFromColumnForEntity("Cost", "Cost during non occupancy"));
    }

    public double getVariableCostNonOccupancyValue() {
        machineCostTable.expandEntityInTableByDesignation("Cost during non occupancy");
        int parentPosition = machineCostTable.getPositionInATable("Cost during non occupancy");
        String rowText = machineCostTable.getRowTextByPosition(parentPosition+2);
        return ValueUtil.parseDoubleWithWhitespaces(StringUtils.substringBetween(rowText, "\n", "\n"));
    }

    @Step("Get interest rate value")
    public String getInterestRate() {
        log.info("Get interest rate value");
        return getElementValueAttribute(interestRate);
    }

    @Step("Get Space cost")
    public String getSpaceCost() {
        log.info("Get Space cost");
        return getElementValueAttribute(spaceCost);
    }

    @Step("Get Electricity cost")
    public String getElectricityCost() {
        log.info("Get Electricity cost");
        return getElementValueAttribute(electricityCost);
    }

    @Step("Get Natural gas emissions active")
    public String getNaturalGasEmissionActive() {
        log.info("Get Natural gas emissions active");
        return getElementValueAttribute(naturalGasEmissionActive);
    }

    @Step("Get Natural gas emissions passive")
    public String getNaturalGasEmissionPassive() {
        log.info("Get Natural gas emissions passive");
        return getElementValueAttribute(naturalGasEmissionPassive);
    }

    @Step("Get Emission electricity")
    public String getEmissionElectricity() {
        log.info("Get Emission electricity");
        return getElementValueAttribute(emissionElectricity);
    }
}
