package com.nu.qa.ui.pageobjects.masterData;

import com.nu.qa.ui.pageobjects.modals.masterData.manufacturingSteps.AddManufacturingStepModal;
import com.nu.qa.ui.pageobjects.widgets.TabulatorTable;
import com.nu.qa.utilities.data.enums.ManufacturingStepType;
import io.qameta.allure.Step;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class ManufacturingStepsTab extends MasterDataTab {

    @FindBy(css = "button[data-test='button-add-manufacturing-step']")
    private WebElement addManufacturingStepButton;

    private final TabulatorTable manufacturingStepsTable;

    public ManufacturingStepsTab(WebDriver driver) {
        super(driver);
        PageFactory.initElements(driver, this);
        this.manufacturingStepsTable = new TabulatorTable(driver, "[data-test='steptemplates-table']");
        isPageLoaded(this.getClass().getSimpleName(), title, addManufacturingStepButton);
        waitUntilLoadingIndicatorIsVisible();
    }

    @Step("Click on 'Add manufacturing step' button")
    public AddManufacturingStepModal clickOnAddManufacturingStepButton() {
        log.info("Click on 'Add manufacturing step' button");
        waitUntilLoadingIndicatorIsVisible();
        clickOnWebElement(addManufacturingStepButton);
        return new AddManufacturingStepModal(driver);
    }

    @Step("Click on first available predefined Manufacturing step")
    public ManufacturingStepsDetailsPage clickOnFirstAvailablePredefinedManufacturingStep() {
        log.info("Click on first available predefined Manufacturing step from list");
        String firstStep = StringUtils.substringBefore(manufacturingStepsTable.getRowTextByPosition(1), "\n");
        manufacturingStepsTable.clickOnEntityInTableByDesignation(firstStep);
        return new ManufacturingStepsDetailsPage(driver);
    }

    @Step("Click on Manufacturing steps: {designation}")
    public ManufacturingStepsDetailsPage clickOnManufacturingStepByName(String designation) {
        log.info("Click on Manufacturing steps: {}", designation);
        manufacturingStepsTable.clickOnEntityInTableByDesignation(designation);
        return new ManufacturingStepsDetailsPage(driver);
    }

    @Step("Filter Manufacturing step tab by id/designation: {inputText}")
    public ManufacturingStepsTab findManufacturingStepByIdOrDesignation(String inputText) {
        log.info("Filter Manufacturing step tab by id/designation: {}", inputText);
        waitUntilLoadingIndicatorIsVisible();
        waitUntilElementIsVisible(searchInput);
        String searchFieldValue = getElementValueAttribute(searchInput);
        if (!searchFieldValue.isEmpty()) {
            waitUntilElementIsClickable(clearSearchInputButton);
            clearSearchInputButton.click();
        }
        sendKey(searchInput, inputText);
        waitUntilTableLoadingIndicatorIsVisible();
        return new ManufacturingStepsTab(driver);
    }

    @Step("Is Manufacturing Step '{designation}' present in search results")
    public boolean isManufacturingStepPresentInResults(String designation) {
        log.info("Is Manufacturing Step '{}' present in search results", designation);
        return manufacturingStepsTable.isEntityPresentInTableByName(designation);
    }

    public boolean isManufacturingStepTypePresent(ManufacturingStepType manufacturingStepType) {
        List<WebElement> manufacturingStepsTypeColumn = manufacturingStepsTable.getAllCellsFromColumn("Type");
        return manufacturingStepsTypeColumn.stream().anyMatch(e -> e.getText().trim().equals(manufacturingStepType.getDesignation()));
    }

    public boolean onlyFilteredManufacturingStepsArePresent(ManufacturingStepType manufacturingStepTypeFirst,
                                                              ManufacturingStepType manufacturingStepTypeSecond) {
        List<WebElement> manufacturingStepsTypeColumn = manufacturingStepsTable.getAllCellsFromColumn("Type");
        return manufacturingStepsTypeColumn.stream().allMatch(e -> e.getText().trim().equals(manufacturingStepTypeFirst.getDesignation())
                || e.getText().trim().equals(manufacturingStepTypeSecond.getDesignation()));
    }

    public ArrayList<String> getFilteredManufacturingSteps() {
        List<WebElement> manufacturingStepsDesignationColumn = manufacturingStepsTable.getAllCellsFromColumn("Designation");
        return manufacturingStepsDesignationColumn.stream()
                .map(manStep -> getElementText(manStep).trim())
                .collect(Collectors.toCollection(ArrayList::new));
    }
}
