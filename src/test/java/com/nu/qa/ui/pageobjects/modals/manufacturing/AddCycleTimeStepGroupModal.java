package com.nu.qa.ui.pageobjects.modals.manufacturing;

import com.nu.qa.ui.data.enums.CycleTimeStepGroupGrouping;
import com.nu.qa.ui.pageobjects.modals.BasicModal;
import io.qameta.allure.Step;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

@Slf4j
public class AddCycleTimeStepGroupModal extends BasicModal {

    @FindBy(css = "[id='modal-layout-wrapper'] button[data-test='button-add-group']")
    private WebElement addCycleTimeStepGroupButton;

    @FindBy(css = "[id='modal-layout-wrapper'] button[data-test='button-add-create-cycle-time-step']")
    private WebElement addCycleTimeStepToGroupButton;

    @FindBy(css = "#modal-layout-wrapper #displayDesignation input")
    private WebElement designationInput;

    private final String GROUPING_DROPDOWN = "//*[@data-test='knowledge-manual-entry']//*[@data-test='nuinputselect-grouping']";
    private final String CYCLE_TIME_GROUP_TYPE_DROPDOWN = "//*[@id='modal-layout-wrapper']//*[@data-test='nuinputselect-type']";
    private final String MATERIAL_DROPDOWN = "//*[@data-test='nuinputselect-materialName']";
    private final String MATERIAL_GROUP_DROPDOWN = "//*[@data-test='nuinputselect-materialGroup']";

    public AddCycleTimeStepGroupModal(WebDriver driver) {
            this.driver = driver;
            waitUntilLoadingIndicatorIsVisibleInModal();
            PageFactory.initElements(driver, this);
        }

    @Step("Enter cycle time step group designation: {designation}")
    public AddCycleTimeStepGroupModal enterGroupDesignation(String designation) {
        log.info("Enter cycle time step group designation: {}", designation);
        sendKey(designationInput, designation);
        return this;
    }

    @Step("Click Add Group button")
    public void clickAddCycleTimeStepGroupButton() {
        log.info("Click Add Group button");
        clickOnWebElement(addCycleTimeStepGroupButton);
        waitUntilModalCloses();
    }

    @Step("Click Add cycle time step for Calculated to Group button")
    public AddCycleTimeStepModal clickAddCycleTimeStepForCalculatedToGroupButton() {
        log.info("Click Add cycle time step for Calculated to Group button");
        clickOnWebElement(addCycleTimeStepToGroupButton);
        return new AddCycleTimeStepModal(driver);
    }

    @Step("Chose cycle time step group order: {groupingOrder}")
    public AddCycleTimeStepGroupModal chooseGrouping(CycleTimeStepGroupGrouping grouping) {
        log.info("Chose cycle time step group order: {}", grouping);
        chooseFromDropDownByOptionTextWithXpath(GROUPING_DROPDOWN, grouping.getType());
        return this;
    }

    @Step("Enter designation {designation}")
    public AddCycleTimeStepGroupModal enterDesignation(String designation) {
        log.info("Enter designation {}", designation);
        sendKeyAndPressEnter(designationInput, designation);
        return this;
    }

    @Step("Choose Group Type {groupType}")
    public AddCycleTimeStepGroupModal chooseGroupType(String groupType) {
        log.info("Choose Group Type {}", groupType);
        chooseFromDropDownByOptionTextWithXpath(CYCLE_TIME_GROUP_TYPE_DROPDOWN, groupType);
        waitUntilElementVisible(By.xpath(MATERIAL_DROPDOWN));
        return this;
    }

    @Step("Choose material {material}")
    public AddCycleTimeStepGroupModal chooseMaterial(String material) {
        log.info("Choose material {}", material);
        chooseFromDropDownByOptionTextWithXpath(MATERIAL_DROPDOWN, material);
        waitUntilElementVisible(By.xpath(MATERIAL_GROUP_DROPDOWN));
        return this;
    }

    @Step("Choose material group {materialGroup}")
    public AddCycleTimeStepGroupModal chooseMaterialGroup(String materialGroup) {
        log.info("Choose material group {}", materialGroup);
        chooseFromDropDownByOptionTextWithXpath(MATERIAL_GROUP_DROPDOWN, materialGroup);
        return this;
    }
}
