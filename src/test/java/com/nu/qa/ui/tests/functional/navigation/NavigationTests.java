package com.nu.qa.ui.tests.functional.navigation;

import com.nu.qa.ui.framework.CalculationBaseTest;
import com.nu.qa.ui.pageobjects.CalculationPage;
import com.nu.qa.ui.pageobjects.modals.CalculationCreationModal;
import com.nu.qa.ui.pageobjects.modals.DirtyChildModal;
import com.nu.qa.ui.pageobjects.modals.SaveOrDiscardModal;
import com.nu.qa.ui.pageobjects.tabs.MaterialTab;
import com.nu.qa.utilities.utilities.RandomUtil;
import io.qameta.allure.Severity;
import io.qameta.allure.SeverityLevel;
import io.qameta.allure.TmsLink;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import static com.nu.qa.ui.pageobjects.modals.CalculationCreationModal.CALCULATION_WITHOUT_COST_MODULE;
import static com.nu.qa.utilities.utilities.RandomUtil.getPartNumber;
import static org.assertj.core.api.Assertions.assertThat;

@Slf4j
public class NavigationTests extends CalculationBaseTest {

    final String partName = RandomUtil.getWithoutCostModulePartName();
    final String partNumber = RandomUtil.getPartNumber();

    @Test
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-4851")
    public void checkNavigationFromUnsavedDirtyChild() {
        CalculationPage calculationPage = createCalculationWithoutCostModule(getProjectDashboardPage(), partName, partNumber);

        String subCalculationName = "SubCalculation";
        String subCalculationPartName = RandomUtil.getWithoutCostModulePartName();

        MaterialTab materialTab = calculationPage.clickMaterialTabButton();
        CalculationCreationModal calcCreationModal = materialTab.clickDirectMaterialCostAddButton()
            .clickSubcalculationButton();
        calcCreationModal.selectCalculationType(CALCULATION_WITHOUT_COST_MODULE)
            .enterCalculationTitle(subCalculationName)
            .enterStepId(NO_STEP)
            .enterPartName(subCalculationPartName)
            .enterPartNumber(getPartNumber())
            .clickAddCalculationButton();

        getProjectDashboardPage().getBomExplorerPanel().clickOnBomNodeByPartName(partName);

        addStepToCalculation(calculationPage);
        getProjectDashboardPage().getBomExplorerPanel().expandBomNodeByPartName(partName);

        assertThat(calculationPage.checkIfDirtyChildIconIsPresent())
            .as("Dirty Child icon must be visible on screen")
            .isTrue();

        SaveOrDiscardModal saveOrDiscardModal = getProjectDashboardPage().getBomExplorerPanel()
            .tryToOpenCalculationWhileUnSave(subCalculationPartName);
        saveOrDiscardModal.clickSaveButton();

        DirtyChildModal dirtyChildModal = new DirtyChildModal(getDriver());
        dirtyChildModal.clickOnUpdateAndSaveDirtyChildModal();

        deleteCalculationFromDefaultTestProjectUsingApi(partName);
        logout(calculationPage);
    }
}
