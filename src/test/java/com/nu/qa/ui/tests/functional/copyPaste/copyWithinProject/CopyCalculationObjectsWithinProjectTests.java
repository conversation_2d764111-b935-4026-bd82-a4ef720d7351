package com.nu.qa.ui.tests.functional.copyPaste.copyWithinProject;

import com.nu.qa.ui.framework.CopyPasteBaseTest;
import com.nu.qa.ui.framework.validationsteps.MachineDetailsPageValidator;
import com.nu.qa.ui.framework.validationsteps.StepDetailsPageValidator;
import com.nu.qa.ui.pageobjects.widgets.BomExplorerPanel;
import io.qameta.allure.Description;
import io.qameta.allure.Severity;
import io.qameta.allure.SeverityLevel;
import io.qameta.allure.TmsLink;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class CopyCalculationObjectsWithinProjectTests extends CopyPasteBaseTest implements StepDetailsPageValidator, MachineDetailsPageValidator {

    @Test
    @Description("Copy subcalculation as root into project via Paste to Project")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-1928")
    public void copySubcalculationAsRootIntoProjectViaPasteToProjectTest() {
        String calculationASubcalcPartName = "Subcalc with cost A";
        String calculationBSubcalcPartName = "Subcalc without cost B";

        importCopyPastePreconditionCalculations();

        BomExplorerPanel bomExplorerPanel = getProjectDashboardPage().getBomExplorerPanel();
        bomExplorerPanel.expandBomNodeByPartName(TSET_CALCULATION_A)
            .openContextMenuOnBomNodeByPartName(calculationASubcalcPartName)
            .clickCopyButton();

        bomExplorerPanel.openContextMenuOnBottomOfBom()
            .clickPasteToProjectButton();

        bomExplorerPanel.collapseBomNodeByPartName(TSET_CALCULATION_A);

        assertThat(bomExplorerPanel.isBomNodeVisibleByPartName(calculationASubcalcPartName))
            .as(calculationASubcalcPartName + " should be present in Bom explorer as root calculation!")
            .isTrue();

        bomExplorerPanel.expandBomNodeByPartName(TSET_CALCULATION_B)
            .openContextMenuOnBomNodeByPartName(calculationBSubcalcPartName)
            .clickCopyButton();

        bomExplorerPanel.openContextMenuOnBottomOfBom()
            .clickPasteToProjectButton();

        bomExplorerPanel.collapseBomNodeByPartName(TSET_CALCULATION_B);

        assertThat(bomExplorerPanel.isBomNodeVisibleByPartName(calculationBSubcalcPartName))
            .as(calculationBSubcalcPartName + " should be present in Bom explorer as root calculation!")
            .isTrue();

        deleteTestData();
    }
}
