package com.nu.qa.ui.tests.functional.calculation.issues;

import com.nu.qa.ui.framework.CalculationBaseTest;
import com.nu.qa.ui.pageobjects.CalculationPage;
import com.nu.qa.ui.pageobjects.modals.manufacturing.AddStepModal;
import com.nu.qa.ui.pageobjects.sidePanel.IssuesSidePanel;
import com.nu.qa.ui.pageobjects.tabs.PartTab;
import com.nu.qa.utilities.data.enums.units.Dimensions;
import com.nu.qa.utilities.data.enums.units.Units;
import com.nu.qa.utilities.utilities.RandomUtil;
import io.qameta.allure.Description;
import io.qameta.allure.Severity;
import io.qameta.allure.SeverityLevel;
import io.qameta.allure.TmsLink;
import org.testng.annotations.Test;

import static com.nu.qa.utilities.data.enums.Currencies.EURO;
import static com.nu.qa.utilities.utilities.RandomUtil.getPartNumber;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

public class IssuesModificationsTests extends CalculationBaseTest {

    private static final String ABS = "ABS";

    @Test
    @Description("Missing inputs due to modularized (broken) calculation")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-18225")
    public void issuesModificationsTest() {
        String partName = RandomUtil.getWithoutCostModulePartName();
        String partNumber = getPartNumber();

        CalculationPage calculationPage = createCalculationWithoutCostModule(getProjectDashboardPage(),
            LOCATION, EURO, partName, partNumber, Dimensions.PIECE);

        getProjectDashboardPage().getGlobalHeader().selectDisplayCurrency(EURO);

        IssuesSidePanel issuePanel = new IssuesSidePanel(getDriver());

        assertThat(issuePanel.isPanelOpen())
            .as("The sidepanel for issues should be closed when the calculation is not broken!")
            .isFalse();

        AddStepModal addStepModal = calculationPage.clickManufacturingTabButton()
            .clickAddButton()
            .clickAddStepButton();
        addStepModal.clickFromCalculationModule();
        addStepModal.selectPlasticInjection()
            .addFromCalculationModuleStep("Injection molding");

        calculationPage
            .clickMaterialTabButton()
            .clickDirectMaterialCostAddButton()
            .clickOnAddMaterialButton()
            .addMaterialFromCalculationModule(ABS);

        softAssert.assertThat(issuePanel.isPanelOpen())
            .as("Issue side panel should be open, bc we have a broken calculation!")
            .isTrue();
        softAssert.assertThat(issuePanel.isIssueNotificationPresent())
            .as("There should be an indication which shows that issues already exist after adding material/step!")
            .isTrue();
        softAssert.assertThat(issuePanel.numberOfMissingFields())
            .as("There should be 28 missing fields")
            .isEqualTo(28);
        softAssert.assertThat(issuePanel.getTitleOfEntityCards())
            .as("Expect the given titels")
            .containsExactly("Part", "Injection molding", ABS);
        softAssert.assertThat(issuePanel.getFieldNamesOfEntityCards(0))
            .as("Expect the given fields")
            .containsIgnoringNewLines("Net quantity", "Shape id", "Part height");
        softAssert.assertThat(issuePanel.moreMissingFields(0))
            .as("Expect how many more fields are missing")
            .contains("7 more missing fields");
        softAssert.assertAll();

        issuePanel.setLinkedMaterial(ABS);
        issuePanel.setLinkedStep("Injection molding");

        assertThat(issuePanel.numberOfMissingFields())
            .as("There should be 10 missing fields")
            .isEqualTo(10);

        issuePanel.clickOnEntityCard();

        PartTab partTab = new PartTab(this.getDriver());
        partTab.setNetQuantity("400", Units.GRAM)
            .selectShapeId("S_019")
            .selectPartQuality("Standard")
            .setMainWallThickness("5", Units.MILLIMETER)
            .setMaxWallThickness("8", Units.MILLIMETER)
            .setProjectedAreaPerPart("300", Units.MILLIMETER)
            .setPartHeight("33", Units.MILLIMETER)
            .setPartLength("33", Units.MILLIMETER)
            .setPartOuterDiameter("33", Units.MILLIMETER)
            .setPartWidth("33", Units.MILLIMETER);

        softAssert.assertThat(issuePanel.isIssueNotificationPresent())
                .as("There should be an indication which shows that issues already exist after adding material/step!")
                .isFalse();
        softAssert.assertThat(issuePanel.isNoMissingInputs())
                .as("There should be the check and a text indicating that there are not missing inputs anymore!")
                .isTrue();
        softAssert.assertAll();

        checkThatCostAndCo2TotalsAreNotBroken(calculationPage);
        saveAndDeleteCalculationThenLogout(calculationPage);
    }
}
