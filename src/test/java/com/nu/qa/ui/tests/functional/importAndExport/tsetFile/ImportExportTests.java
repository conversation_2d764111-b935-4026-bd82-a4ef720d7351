package com.nu.qa.ui.tests.functional.importAndExport.tsetFile;

import com.nu.qa.ui.data.ToastMessageDao;
import com.nu.qa.ui.data.builders.calculation.CalculationWithCost;
import com.nu.qa.ui.framework.CalculationBaseTest;
import com.nu.qa.ui.framework.validationsteps.HistoryValidator;
import com.nu.qa.ui.framework.verificationObjects.BomEntry;
import com.nu.qa.ui.pageobjects.CalculationPage;
import com.nu.qa.ui.pageobjects.NotesSideBar;
import com.nu.qa.ui.pageobjects.ProjectDashboardPage;
import com.nu.qa.ui.pageobjects.ProjectsPage;
import com.nu.qa.ui.pageobjects.costmodule.ShapePage;
import com.nu.qa.ui.pageobjects.costmodule.TechnologyPage;
import com.nu.qa.ui.pageobjects.costmodule.TechnologySpecificPage;
import com.nu.qa.ui.pageobjects.costmodule.WeightAndMaterialPage;
import com.nu.qa.ui.pageobjects.modals.CalculationCreationModal;
import com.nu.qa.ui.pageobjects.modals.manufacturing.AddStepModal;
import com.nu.qa.ui.pageobjects.sidePanel.ExportsSidePanel;
import com.nu.qa.ui.pageobjects.tabs.*;
import com.nu.qa.ui.pageobjects.tabs.manufacturing.StepDetailsPage;
import com.nu.qa.ui.pageobjects.widgets.BomExplorerPanel;
import com.nu.qa.ui.pageobjects.widgets.ToastMessage;
import com.nu.qa.utilities.data.enums.units.Dimensions;
import com.nu.qa.utilities.environment.EnvironmentProcessor;
import com.nu.qa.utilities.utilities.RandomUtil;
import io.qameta.allure.*;
import org.testng.annotations.Test;

import java.io.File;

import static com.nu.qa.ui.data.builders.calculation.CalculationWithCostDirector.*;
import static com.nu.qa.ui.data.enums.CalculationStatus.*;
import static com.nu.qa.ui.data.enums.SkillType.SKILLED;
import static com.nu.qa.ui.data.enums.Technology.BAR_TURNING;
import static com.nu.qa.ui.data.enums.Technology.CHILL_CASTING;
import static com.nu.qa.utilities.api.creators.FolderCreatorUtil.initTestFolder;
import static com.nu.qa.utilities.api.creators.ProjectCreatorUtil.checkOrCreateAProjectViaApi;
import static com.nu.qa.utilities.api.creators.WorkspaceCreatorUtil.initTestWorkspace;
import static com.nu.qa.utilities.data.enums.Currencies.EURO;
import static com.nu.qa.utilities.data.enums.units.Units.YEARS;
import static com.nu.qa.utilities.utilities.FileUtils.deleteFileIfExists;
import static com.nu.qa.utilities.utilities.RandomUtil.getPartNumber;
import static com.nu.qa.utilities.utilities.RandomUtil.getWithoutCostModulePartName;
import static org.assertj.core.api.Assertions.assertThat;

public class ImportExportTests extends CalculationBaseTest implements HistoryValidator {

    @Test
    @Description("Import calculation from a different project")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-1070")
    public void importCalculationFromDifferentProjectTest() {
        String title = "Import title";
        String projectKey = RandomUtil.getNLongRandomString(5);
        String projectName = RandomUtil.getRandomStringWithPrefix(EnvironmentProcessor.TEST_DATA_PREFIX, 7);
        CalculationWithCost calculation = constructSAND();
        String partName = calculation.getPartName();

        CalculationPage calculationPage = createCalculationWithCostModuleViaAPI(calculation);
        String downloadedFileAbsolutePath = SYSTEM_DEFAULT_USER_DIR_PATH + File.separator + partName + TSET_FILE_EXTENSION;
        ExportsSidePanel exportsSidePanel = calculationPage.openExportCalculationSidePanel();
        exportsSidePanel.downloadAsTsetFile();
        deleteCalculationFromDefaultTestProjectUsingApi(partName);

        ProjectDashboardPage projectDashboardPage = createProjectAndOpenIt(projectKey, projectName);

        CalculationCreationModal calcCreationModal = projectDashboardPage.getBomExplorerPanel().clickAddCalculationButton();
        calcCreationModal.selectCalculationTypeTsetImport();
        calcCreationModal.enterCalculationTitle(title);
        calcCreationModal.uploadTsetFile(downloadedFileAbsolutePath);
        calcCreationModal.waitUntilModalCloses();

        calculationPage = new CalculationPage(getDriver(), new ParametersTab(getDriver()));

        softAssert.assertThat(calculationPage.getPartNameHeaderValue())
            .as("Imported calculation part name is wrong")
            .isEqualTo(partName);
        softAssert.assertThat(calculationPage.getVariantSelectorText())
            .as("Imported calculation part name is wrong")
            .isEqualTo(title);
        softAssert.assertAll();

        deleteCalculationFromProjectUsingApi(partName, projectKey);
        ProjectsPage projectsPage = deleteProject(projectName);
        logout(projectsPage);
        deleteFileIfExists(downloadedFileAbsolutePath);
    }

    @Test
    @Description("Export a calc with changed values then Import it and check system value")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-3526")
    public void exportACalculationWithChangedValuesThenImportItAndCheckSystemValueTest() {
        String title = "Import changed values";
        String productionTimePerYear = "136792";
        String newLocation = "Liberec Region";

        CalculationWithCost calculation = constructBART();
        CalculationPage calculationPage = createCalculationWithCostModuleViaAPI(calculation);
        String partName = calculation.getPartName();

        ParametersTab parametersTab = calculationPage.clickParametersTabButton()
            .enterProductionVolumePerYear(productionTimePerYear)
            .selectLocation(newLocation);
        assertThat(parametersTab.isLocationUnlinkIconVisible())
            .as("The location on parameters tab must have a broken chane icon after value change!")
            .isTrue();
        saveCalculation(calculationPage);
        validateBasicValuesOfHistoryPanel(calculationPage.openHistorySidePanel(), 2, 0);
        calculationPage.closeHistorySidePanel();

        double originalPeakSystemValue = parametersTab.getPeakVolumePerYearSystemValue();
        String originalLocationSystemValue = parametersTab.getLocationSystemValue();

        String downloadedFileAbsolutePath = SYSTEM_DEFAULT_USER_DIR_PATH + File.separator + partName + TSET_FILE_EXTENSION;
        ExportsSidePanel exportsSidePanel = calculationPage.openExportCalculationSidePanel();
        exportsSidePanel.downloadAsTsetFile();
        deleteCalculation(calculationPage);

        calculationPage.getBomExplorerPanel()
            .clickAddCalculationButton()
            .selectCalculationTypeTsetImport()
            .enterCalculationTitle(title)
            .uploadTsetFile(downloadedFileAbsolutePath);

        softAssert.assertThat(calculationPage.getPartNameHeaderValue())
            .as("Imported calculation part name is wrong")
            .isEqualTo(partName);
        softAssert.assertThat(calculationPage.getVariantSelectorText())
            .as("Imported calculation part name is wrong")
            .isEqualTo(title);
        softAssert.assertAll();

        parametersTab = calculationPage.clickParametersTabButton();

        softAssert.assertThat(parametersTab.getPeakVolumePerYearSystemValue())
            .as("System value of Peak volume per year after import is wrong!")
            .isEqualTo(originalPeakSystemValue);
        softAssert.assertThat(parametersTab.getLocation())
            .as("The current location does not correspond with the formerly changed value!")
            .isEqualTo(newLocation);
        softAssert.assertThat(parametersTab.getLocationSystemValue())
            .as("The location must have a system value of the original location!")
            .isEqualTo(originalLocationSystemValue);
        softAssert.assertAll();
        validateBasicValuesOfHistoryPanel(calculationPage.openHistorySidePanel(), 1, 0);

        deleteCalculationFromDefaultTestProjectUsingApi(partName);
        logout(calculationPage);
        deleteFileIfExists(downloadedFileAbsolutePath);
    }

    @Test(groups = "smoke")
    @Description("Export calculation without COST module")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-1065")
    public void exportCalculationWithoutCostModuleTest() {
        String title = "Import title";
        String partName = RandomUtil.getWithoutCostModulePartName();
        String partNumber = getPartNumber();
        String downloadedFileAbsolutePath = SYSTEM_DEFAULT_USER_DIR_PATH + File.separator + partName + TSET_FILE_EXTENSION;
        String designation = "100Cr6";
        String netWeightPerPart = "5";
        String scrapWeightPerPart = "1";
        String notesText = "Add a note and check the same after importing the file";
        String partClassification = RandomUtil.getNLongRandomString(20);
        String partDescription = RandomUtil.getNLongRandomString(100);

        CalculationPage calculationPage = createCalculationWithoutCostModule(getProjectDashboardPage(), partName, partNumber);

        NotesSideBar notesSideBar = calculationPage.clickOnNotesButton()
            .enterTextAndClickEnterButtonInNotesTextArea(notesText);
        notesSideBar.clickNotesBoxCloseButton();
        calculationPage.clickSaveButton();

        addAndCheckMaterialFromMasterdata(calculationPage, designation, NO_STEP, netWeightPerPart, scrapWeightPerPart);

        PartTab partTab = calculationPage.clickPartTabButton()
            .enterPartName(partName)
            .enterPartNumber(partNumber)
            .enterPartClassification(partClassification)
            .enterPartDescription(partDescription)
            .attachFile(XLSB_ATTACHMENT)
            .attachFile(PDF_ATTACHMENT)
            .attachFile(PHOTO_ATTACHMENT_PNG);

        assertThat(partTab.getNumberOfAttachments())
            .as("There must be three attachments!")
            .isEqualTo(3);

        saveCalculation(calculationPage);
        ExportsSidePanel exportsSidePanel = calculationPage.openExportCalculationSidePanel();
        exportsSidePanel.downloadAsTsetFile();
        deleteCalculationFromDefaultTestProjectUsingApi(partName);

        BomExplorerPanel bomExplorerPanel = calculationPage.getBomExplorerPanel();
        uploadTsetFileAndVerifyImportedCalculation(bomExplorerPanel, title, downloadedFileAbsolutePath, partName, partNumber, calculationPage);

        calculationPage.clickPartTabButton();

        softAssert.assertThat(partTab.getNumberOfAttachments())
            .as("There must be three attachments!")
            .isEqualTo(3);
        softAssert.assertThat(partTab.getPartClassificationValue())
            .as("Part classification value should be same as in original calculation!")
            .isEqualTo(partClassification);
        softAssert.assertThat(partTab.getPartDescriptionValue())
            .as("Part description value should be same as in original calculation!")
            .isEqualTo(partDescription);
        softAssert.assertAll();

        calculationPage.clickOnNotesButton();

        assertThat(notesSideBar.getNotesText())
            .as("Notes is not matching or empty after uploading the attachment!")
            .isEqualToIgnoringNewLines(notesText);

        notesSideBar.clickNotesBoxCloseButton();

        deleteCalculationFromDefaultTestProjectUsingApi(partName);
        logout(calculationPage);
        deleteFileIfExists(downloadedFileAbsolutePath);
    }

    @Test
    @Description("Export calculation with COST module")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-1066")
    public void exportCalculationWithCostModuleTest() {
        CalculationWithCost calculation = constructPREC();
        CalculationPage calculationPage = createCalculationWithCostModuleViaAPI(calculation);
        String partName = calculation.getPartName();
        String partNumber = calculation.getPartNumber();
        String title = "Import title";
        String subcalculation = "Ceramic Mold";
        String consumable = "Depositing molding sand";
        String downloadedFileAbsolutePath = SYSTEM_DEFAULT_USER_DIR_PATH + File.separator + partName + TSET_FILE_EXTENSION;

        ExportsSidePanel exportsSidePanel = calculationPage.openExportCalculationSidePanel();
        exportsSidePanel.downloadAsTsetFile();
        deleteCalculation(calculationPage);

        BomExplorerPanel bomExplorerPanel = calculationPage.getBomExplorerPanel();
        uploadTsetFileAndVerifyImportedCalculation(bomExplorerPanel, title, downloadedFileAbsolutePath, partName, partNumber, calculationPage);

        bomExplorerPanel.expandBomNodeByPartName(partName);

        softAssert.assertThat(bomExplorerPanel.isBomNodeHasChildBomNodeByPartName(partName, subcalculation))
            .as("Bom Explorer Panel must have " + subcalculation + " subcalculation!")
            .isTrue();
        softAssert.assertThat(bomExplorerPanel.isBomNodeHasChildBomEntryByName(partName, consumable))
            .as("Bom Explorer Panel must have " + consumable + " consumable!")
            .isTrue();
        softAssert.assertAll();

        ManufacturingTab manufacturingTab = calculationPage.clickManufacturingTabButton();

        assertThat(manufacturingTab.getManufacturingStepListSize())
            .as("There should be ten manufacturing steps!")
            .isEqualTo(10);

        deleteCalculationFromDefaultTestProjectUsingApi(partName);
        logout(calculationPage);
        deleteFileIfExists(downloadedFileAbsolutePath);
    }

    @Test
    @Description("Export Rough Estimate")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-1069")
    public void exportRoughEstimateTest() {
        String partName = RandomUtil.getRoughEstimationPartName();
        String partNumber = RandomUtil.getPartNumber();
        String modifiedPartName = "RE_calculation";
        String title = "Import title";
        String downloadedFileAbsolutePath = SYSTEM_DEFAULT_USER_DIR_PATH + File.separator + modifiedPartName + TSET_FILE_EXTENSION;

        CalculationPage calculationPage = createRoughEstimate(getProjectDashboardPage(), "23", "100",
            EURO, partName, partNumber, Dimensions.PIECE);

        calculationPage.clickPartTabButton().enterPartName(modifiedPartName);

        saveCalculation(calculationPage);
        ExportsSidePanel exportsSidePanel = calculationPage.openExportCalculationSidePanel();
        exportsSidePanel.downloadAsTsetFile();
        deleteCalculation(calculationPage);

        BomExplorerPanel bomExplorerPanel = calculationPage.getBomExplorerPanel();
        uploadTsetFileAndVerifyImportedCalculation(bomExplorerPanel, title, downloadedFileAbsolutePath, modifiedPartName, partNumber, calculationPage);

        deleteCalculationFromDefaultTestProjectUsingApi(modifiedPartName);
        logout(calculationPage);
        deleteFileIfExists(downloadedFileAbsolutePath);
    }

    @Test(enabled = false)
    @Description("Import calculation from different account")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-1071")
    public void importCalculationFromDifferentAccountTest() {
        CalculationWithCost calculation = constructBART();
        CalculationPage calculationPage = createCalculationWithCostModuleViaAPI(calculation);
        String partName = calculation.getPartName();
        String partNumber = calculation.getPartNumber();
        String projectKey = RandomUtil.getNLongRandomString(5);
        String projectTitle = getTestDataPrefix() + RandomUtil.getNLongRandomString(7);
        String folderDesignation = "AAAA" + getTestDataPrefix() + RandomUtil.getNLongRandomString(4);
        String workspaceDesignation = getTestDataPrefix() + RandomUtil.getNLongRandomString(4);

        String title = "Import title";
        String downloadedFileAbsolutePath = SYSTEM_DEFAULT_USER_DIR_PATH + File.separator + partName + TSET_FILE_EXTENSION;

        addStepToCalculation(calculationPage);
        saveCalculation(calculationPage);
        ExportsSidePanel exportsSidePanel = calculationPage.openExportCalculationSidePanel();
        exportsSidePanel.downloadAsTsetFile();
        deleteCalculationThenLogout(calculationPage);

        loginWithBctUser(true);
        String workspaceId = initTestWorkspace(getBctUser(), getBaseUrl(), workspaceDesignation);
        String rootFolderId = initTestFolder(getBctUser(), getBaseUrl(), workspaceId, folderDesignation);
        checkOrCreateAProjectViaApi(projectKey, projectTitle, rootFolderId, getBaseUrl(), getBctUserAccessToken());
        navigateToAndGetPageObject((getBaseUrl() + "project/" + projectKey), ProjectDashboardPage.class);

        BomExplorerPanel bomExplorerPanel = calculationPage.getBomExplorerPanel();
        uploadTsetFileAndVerifyImportedCalculation(bomExplorerPanel, title, downloadedFileAbsolutePath, partName, partNumber, calculationPage);

        bomExplorerPanel.expandBomNodeByPartName(partName);

        assertThat(bomExplorerPanel.isBomNodeHasChildBomEntryByName(partName, STEP_DESIGNATION))
            .as("Bom Explorer Panel must have " + STEP_DESIGNATION + " !")
            .isTrue();

        deleteWorkspace(workspaceDesignation);
        deleteFileIfExists(downloadedFileAbsolutePath);
    }

    @Test
    @Description("Export calculation with variant")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-1077")
    public void exportCalculationWithVariantTest() {
        CalculationWithCost calculation = constructBART();
        CalculationPage calculationPage = createCalculationWithCostModuleViaAPI(calculation);
        String partName = calculation.getPartName();
        String partNumber = calculation.getPartNumber();
        String location = "Serbia";
        String main = calculationPage.getVariantSelectorText();
        String variantName = "Variant";
        String title = "Imported variant calculation";
        String downloadedFileAbsolutePath = SYSTEM_DEFAULT_USER_DIR_PATH + File.separator + partName + TSET_FILE_EXTENSION;

        calculationPage.clickParametersTabButton()
            .selectLocation(location);

        calculationPage.clickSaveAsVariantButton(variantName);

        ExportsSidePanel exportsSidePanel = calculationPage.openExportCalculationSidePanel();
        exportsSidePanel.downloadAsTsetFile();

        calculationPage.chooseVariant(main);
        deleteCalculation(calculationPage);

        BomExplorerPanel bomExplorerPanel = calculationPage.getBomExplorerPanel();
        uploadTsetFileAndVerifyImportedCalculation(bomExplorerPanel, title, downloadedFileAbsolutePath, partName, partNumber, calculationPage);

        SummaryTab summaryTab = calculationPage.clickSummaryTabButton();

        assertThat(summaryTab.getOverviewLocationValue())
            .as("Location must be the same as in the input!")
            .isEqualTo(location);

        deleteCalculationFromDefaultTestProjectUsingApi(partName);
        logout(calculationPage);
        deleteFileIfExists(downloadedFileAbsolutePath);
    }

    @Test
    @Description("Export calculation with manually added 2nd level subcalculation")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-1990")
    public void exportCalculationWithManuallyAdded2ndLevelSubcalculationTest() {
        String partName = RandomUtil.getWithoutCostModulePartName();
        String partNumber = getPartNumber();
        String subCalcPartTitle = RandomUtil.getRandomStringWithPrefix(EnvironmentProcessor.TEST_DATA_PREFIX, 5);
        String subCalcPartName = RandomUtil.getWithoutCostModulePartName();
        CalculationWithCost secondLevelSubcalc = constructPREC();
        String title = "Imported variant calculation";
        String downloadedFileAbsolutePath = SYSTEM_DEFAULT_USER_DIR_PATH + File.separator + partName + TSET_FILE_EXTENSION;

        CalculationPage calculationPage = createCalculationWithoutCostModule(getProjectDashboardPage(), partName, partNumber);

        BomExplorerPanel bomExplorerPanel = calculationPage.getBomExplorerPanel();

        createSubCalculationWithoutCostModule(calculationPage, subCalcPartTitle, subCalcPartName);

        createSubcalculationWithPrecisionCastingCostModule(calculationPage, secondLevelSubcalc, NO_STEP,
            "300", "10");

        saveCalculation(calculationPage);

        bomExplorerPanel.clickOnBomNodeByPartName(partName);
        ExportsSidePanel exportsSidePanel = calculationPage.openExportCalculationSidePanel();
        exportsSidePanel.downloadAsTsetFile();
        deleteCalculation(calculationPage);
        uploadTsetFileAndVerifyImportedCalculation(bomExplorerPanel, title, downloadedFileAbsolutePath, partName, partNumber, calculationPage);

        bomExplorerPanel.expandBomNodeByPartName(partName)
            .expandBomNodeByPartName(subCalcPartName);

        softAssert.assertThat(bomExplorerPanel.isBomNodeHasChildBomNodeByPartName(partName, subCalcPartName))
            .as("Bom Explorer Panel must have " + subCalcPartName + " subcalculation!")
            .isTrue();
        softAssert.assertThat(bomExplorerPanel.isBomNodeHasChildBomNodeByPartName(partName, secondLevelSubcalc.getPartName()))
            .as("Bom Explorer Panel must have " + secondLevelSubcalc.getPartName() + " 2nd level subcalculation!")
            .isTrue();
        softAssert.assertAll();

        deleteCalculationFromDefaultTestProjectUsingApi(partName);
        logout(calculationPage);
        deleteFileIfExists(downloadedFileAbsolutePath);
    }

    @Test
    @Description("Export modified calculation")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-1067")
    public void exportModifiedCalculationTest() {
        CalculationWithCost calculation = constructBART();
        CalculationPage calculationPage = createCalculationWithCostModuleViaAPI(calculation);
        String location = "Czech Republic";
        String lifetime = "10.00";
        String peakVolume = "30000";
        String materialName = "AlSi11";
        String shapeId = "S_050";
        double netWeightPerPart = 200;
        String maxWallThickness = "2.00";
        String minWallThickness = "1.00";
        String title = "Import title";

        PartTab partTab = calculationPage.clickPartTabButton();

        TechnologySpecificPage specificPage = partTab.clickCostModuleEditIcon()
            .chooseTechnology(CHILL_CASTING)
            .enterNetWeightPerPartForWizard(String.valueOf(netWeightPerPart))
            .enterMaterialName(materialName)
            .proceedToShapePage()
            .chooseShapeByIdAndDoubleClick(shapeId);

        specificPage.selectNeedsFettling(NO)
            .enterNeedsShotBlasting(NO)
            .enterMaxWallThickness(maxWallThickness)
            .enterMinWallThickness(minWallThickness);

        calculationPage = specificPage.saveCalculation();

        ParametersTab parametersTab = calculationPage.clickParametersTabButton();
        parametersTab.selectLocation(location)
            .enterProductionVolumePerYear(peakVolume)
            .enterLifeTimeInput(lifetime)
            .selectStatus(IN_PROGRESS);

        calculationPage.clickPartTabButton();
        String changedPartName = "CHILL_" + RandomUtil.getNLongRandomString(7);
        String changedPartNumber = RandomUtil.getPartNumber();
        partTab.enterPartName(changedPartName)
            .enterPartNumber(changedPartNumber)
            .attachFile(PHOTO_ATTACHMENT_PNG);

        softAssert.assertThat(calculationPage.getPartNameHeaderValue())
            .as("Calculation part name must be the same as in the input!")
            .isEqualTo(changedPartName);
        softAssert.assertThat(calculationPage.getPartNumberHeaderValue())
            .as("Calculation part number must be the same as in the input!")
            .isEqualTo(changedPartNumber);
        softAssert.assertThat(partTab.getNumberOfAttachments())
            .as("One image should be present!")
            .isEqualTo(1);
        softAssert.assertAll();

        BomExplorerPanel bomExplorerPanel = calculationPage.getBomExplorerPanel();

        assertThat(bomExplorerPanel.isCalculationPresent(changedPartName, changedPartNumber))
            .as("Bom Explorer Panel must have renamed calculation!")
            .isTrue();

        saveCalculation(calculationPage);

        String downloadedFileAbsolutePath = SYSTEM_DEFAULT_USER_DIR_PATH + File.separator + changedPartName + TSET_FILE_EXTENSION;
        ExportsSidePanel exportsSidePanel = calculationPage.openExportCalculationSidePanel();
        exportsSidePanel.downloadAsTsetFile();

        deleteCalculation(calculationPage);

        uploadTsetFileAndVerifyImportedCalculation(bomExplorerPanel, title, downloadedFileAbsolutePath, changedPartName, changedPartNumber, calculationPage);

        SummaryTab summaryTab = calculationPage.clickSummaryTabButton();

        softAssert.assertThat(summaryTab.getOverviewLocationValue())
            .as("Location must be the same as in the input!")
            .isEqualTo(location);
        //TODO use the assertion below once the COST-9426 is fixed
        //  softAssert.assertThat(summaryTab.getOverviewStatusValue())
        //          .as("Status must be the same as in the input!")
        //          .isEqualTo(status);
        softAssert.assertThat(summaryTab.getOverviewLifetimeValue())
            .as("Lifetime value must be the same as in the input! ")
            .isEqualTo(lifetime + "\n" + YEARS.getUnitSymbol());
        softAssert.assertAll();

        calculationPage.clickPartTabButton();

        softAssert.assertThat(partTab.getCostModuleTechnologyText())
            .as("Technology must be the same as in the input!")
            .isEqualTo("Chill casting");
        softAssert.assertThat(partTab.getNetWeightPerPart())
            .as("Net amount must be the same as in the input!")
            .isEqualTo(netWeightPerPart);
        softAssert.assertThat(partTab.getMaterialName())
            .as("Material name must be the same as in the input!")
            .isEqualTo(materialName);
        softAssert.assertThat(partTab.getMaxWallThicknessValue())
            .as("Maximum wall thickness must be the same as in the input!")
            .isEqualTo(maxWallThickness);
        softAssert.assertThat(partTab.getMinWallThicknessValue())
            .as("Minimum wall thickness must be the same as in the input!")
            .isEqualTo(minWallThickness);
        softAssert.assertThat(partTab.getShapeId())
            .as("Shape id must be the same as in the input!")
            .isEqualTo(shapeId);
        softAssert.assertThat(partTab.getNumberOfAttachments())
            .as("One image should be present!")
            .isEqualTo(1);
        softAssert.assertAll();

        deleteCalculationFromDefaultTestProjectUsingApi(changedPartName);
        logout(calculationPage);
        deleteFileIfExists(downloadedFileAbsolutePath);
    }

    @Test
    @Description("Export calculation with manually added subcalculation")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-1068")
    public void exportCalculationWithManuallyAddedSubcalculationTest() {
        String partName = RandomUtil.getWithoutCostModulePartName();
        String partNumber = getPartNumber();
        String subCalcTitle = "SubCalcTitle";
        String shapeId = "S_085";
        String title = "Imported calculation";
        String subCalcPartName = "BART_" + getPartNumber();
        CalculationPage calculationPage = addStepToCalculation(createCalculationWithoutCostModule(getProjectDashboardPage(),
            partName, partNumber));

        saveCalculation(calculationPage);

        TechnologyPage technologyPage = initiateSubCalculationWithCostModule(calculationPage,
            subCalcTitle, subCalcPartName, getPartNumber());

        WeightAndMaterialPage weightAndMaterial = technologyPage.chooseTechnology(BAR_TURNING);

        ShapePage shapePage = weightAndMaterial.enterMaterialName("28Mn6 - annealed")
            .enterNetWeightPerPartForWizard("500")
            .proceedToShapePage();

        TechnologySpecificPage specific = shapePage.chooseShapeByIdAndDoubleClick(shapeId);
        specific.enterCleaningNeeded(NO);
        specific.clickContinueButtonUntilPropertiesTableAppears();
        calculationPage = specific.saveSubCalculation();

        calculationPage.clickSaveButton();

        String downloadedFileAbsolutePath = SYSTEM_DEFAULT_USER_DIR_PATH + File.separator + partName + TSET_FILE_EXTENSION;
        ExportsSidePanel exportsSidePanel = calculationPage.openExportCalculationSidePanel();
        exportsSidePanel.downloadAsTsetFile();

        deleteCalculation(calculationPage);

        BomExplorerPanel bomExplorerPanel = calculationPage.getBomExplorerPanel();
        uploadTsetFileAndVerifyImportedCalculation(bomExplorerPanel, title, downloadedFileAbsolutePath, partName, partNumber, calculationPage);

        bomExplorerPanel.expandBomNodeByPartName(partName);

        softAssert.assertThat(bomExplorerPanel.isBomNodeHasChildBomNodeByPartName(partName, subCalcPartName))
            .as("Bom Explorer Panel must have " + subCalcPartName + " subcalculation!")
            .isTrue();
        softAssert.assertThat(bomExplorerPanel.isBomNodeHasChildBomEntryByName(partName, STEP_DESIGNATION))
            .as("Bom Explorer Panel must have " + STEP_DESIGNATION + " !")
            .isTrue();
        softAssert.assertAll();

        deleteCalculationFromDefaultTestProjectUsingApi(partName);
        logout(calculationPage);
        deleteFileIfExists(downloadedFileAbsolutePath);
    }

    @Test
    @Description("Export calculation without cost module and manual objects")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-1072")
    public void exportCalculationWithoutCostModuleAndManualObjectsTest() {
        String title = "Import title";
        String partName = RandomUtil.getWithoutCostModulePartName();
        String partNumber = getPartNumber();
        String stepDesignation = "Assembly Press";
        String stepConfiguration = "Assembly workstation with pneumatic press (simple)";
        String laborName = "Operator";
        String materialName = "Manual Material";
        Dimensions dimension = Dimensions.MASS;
        String netWeightPerPart = "5.00";
        String pricePerUnit = "22";
        String consumableName = "Manual Consumable";
        String quantity = "5.00";
        String machineName = "Manual Machine";
        String investBase = "10.00";
        String requiredSpace = "11.00";
        String connectedLoad = "12.00";
        String maintenanceRate = "13.00";
        String consumableRate = "14.00";
        String downloadedFileAbsolutePath = SYSTEM_DEFAULT_USER_DIR_PATH + File.separator + partName + TSET_FILE_EXTENSION;

        CalculationPage calculationPage = createCalculationWithoutCostModule(getProjectDashboardPage(), partName, partNumber);

        ManufacturingTab manufacturingTab = calculationPage.clickManufacturingTabButton()
            .clickAddStepButton()
            .addMasterDataStep(stepDesignation, stepConfiguration);

        ToastMessageDao toastMessageDao = ToastMessage.getLastToastMessage(getDriver());
        assertThat(toastMessageDao.getTitle())
            .as("Add manufacturing step successful toast message title didn't show up correctly!")
            .isEqualTo("Manufacturing step added successfully");

        ToastMessage.closeToastsWithEscKey(getDriver());

        StepDetailsPage stepDetailsPage = manufacturingTab.clickOnStepByName(stepDesignation)
            .clickAddLaborButton()
            .addLabor(laborName, SKILLED.getReadableName(), "1");

        toastMessageDao = ToastMessage.getLastToastMessage(getDriver());
        assertThat(toastMessageDao.getTitle())
            .as("Add labor successful toast message title didn't show up correctly!")
            .isEqualTo("Labor added successfully");

        ToastMessage.closeToastsWithEscKey(getDriver());

        stepDetailsPage.clickAddMachineButton()
            .addMachineManually(machineName, investBase, requiredSpace, connectedLoad, maintenanceRate, consumableRate);

        toastMessageDao = ToastMessage.getLastToastMessage(getDriver());
        assertThat(toastMessageDao.getTitle())
            .as("Add machine successful toast message title didn't show up correctly!")
            .isEqualTo("Machine added successfully");

        ToastMessage.closeToastsWithEscKey(getDriver());

        MaterialTab materialTab = calculationPage.clickMaterialTabButton()
            .clickDirectMaterialCostAddButton()
            .clickOnAddMaterialButton()
            .addMaterialManually(NO_STEP, materialName, dimension, pricePerUnit, netWeightPerPart);

        toastMessageDao = ToastMessage.getLastToastMessage(getDriver());
        assertThat(toastMessageDao.getTitle())
            .as("Add material successful toast message title didn't show up correctly!")
            .isEqualTo("Material added successfully");

        ToastMessage.closeToastsWithEscKey(getDriver());

        materialTab.clickDirectMaterialCostAddButton()
            .clickOnAddConsumableButton()
            .addConsumableManually(stepDesignation, consumableName, dimension, quantity, pricePerUnit, "0");

        toastMessageDao = ToastMessage.getLastToastMessage(getDriver());
        assertThat(toastMessageDao.getTitle())
            .as("Add consumable successful toast message title didn't show up correctly!")
            .isEqualTo("Consumable added successfully");

        ToastMessage.closeToastsWithEscKey(getDriver());

        saveCalculation(calculationPage);
        ExportsSidePanel exportsSidePanel = calculationPage.openExportCalculationSidePanel();
        exportsSidePanel.downloadAsTsetFile();
        deleteCalculation(calculationPage);

        BomExplorerPanel bomExplorerPanel = calculationPage.getBomExplorerPanel();

        uploadTsetFileAndVerifyImportedCalculation(bomExplorerPanel, title, downloadedFileAbsolutePath, partName, partNumber, calculationPage);

        bomExplorerPanel.expandBomNodeByPartName(partName);

        softAssert.assertThat(bomExplorerPanel.isBomNodeHasChildBomEntryByName(partName, consumableName))
            .as("Bom Explorer Panel must have " + consumableName + " consumable!")
            .isTrue();
        softAssert.assertThat(bomExplorerPanel.isBomNodeHasChildBomEntryByName(partName, materialName))
            .as("Bom Explorer Panel must have " + materialName + " material!")
            .isTrue();
        softAssert.assertThat(bomExplorerPanel.isBomNodeHasChildBomEntryByName(partName, stepDesignation))
            .as("Bom Explorer Panel must have " + stepDesignation + " step!")
            .isTrue();
        softAssert.assertAll();

        bomExplorerPanel.expandBomEntryByName(stepDesignation);

        softAssert.assertThat(bomExplorerPanel.isBomEntryHasChildBomEntryByName(stepDesignation, machineName))
            .as("Bom Explorer Panel must have " + machineName + " machine!")
            .isTrue();
        softAssert.assertThat(bomExplorerPanel.isBomEntryHasChildBomEntryByName(stepDesignation, laborName))
            .as("Bom Explorer Panel must have " + laborName + " labor!")
            .isTrue();
        softAssert.assertAll();

        deleteCalculationFromDefaultTestProjectUsingApi(partName);
        logout(calculationPage);
        deleteFileIfExists(downloadedFileAbsolutePath);
    }

    @Test
    @Description("Export and reimport a calculation containing 2 attachments and changed name")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-1694")
    public void exportAndReimportCalculationContainingTwoAttachmentsAndChangedNameTest() {
        String partName = RandomUtil.getWithoutCostModulePartName();
        String partNumber = getPartNumber();
        String subCalcTitle = "SubCalcTitle";
        String shapeId = "S_085";
        String title = "Imported calculation";
        String subCalcPartName = "BART_" + getPartNumber();
        String changedPartName = RandomUtil.getWithoutCostModulePartName();
        String changedSubCalPartName = "BART_" + getPartNumber();
        String downloadedFileMainCalculation = SYSTEM_DEFAULT_USER_DIR_PATH + File.separator + changedPartName + TSET_FILE_EXTENSION;
        String downloadedFileSubCalculation = SYSTEM_DEFAULT_USER_DIR_PATH + File.separator + changedSubCalPartName + TSET_FILE_EXTENSION;

        CalculationPage calculationPage = createCalculationWithoutCostModule(getProjectDashboardPage(), partName, partNumber);

        TechnologyPage technologyPage = initiateSubCalculationWithCostModule(calculationPage,
            subCalcTitle, subCalcPartName, getPartNumber());

        WeightAndMaterialPage weightAndMaterial = technologyPage.chooseTechnology(BAR_TURNING);

        ShapePage shapePage = weightAndMaterial.enterMaterialName("28Mn6 - annealed")
            .enterNetWeightPerPartForWizard("500")
            .proceedToShapePage();

        TechnologySpecificPage specific = shapePage.chooseShapeByIdAndDoubleClick(shapeId);
        specific.enterCleaningNeeded(NO);
        specific.clickContinueButtonUntilPropertiesTableAppears();
        calculationPage = specific.saveSubCalculation();

        PartTab partTab = calculationPage.clickPartTabButton()
            .enterPartName(changedPartName)
            .attachFile(PDF_ATTACHMENT)
            .attachFile(PHOTO_ATTACHMENT_PNG);

        assertThat(partTab.getNumberOfAttachments())
            .as("There must be two attachments!")
            .isEqualTo(2);

        saveCalculation(calculationPage);

        BomExplorerPanel bomExplorerPanel = calculationPage.getBomExplorerPanel();
        bomExplorerPanel.expandBomNodeByPartName(changedPartName)
            .clickOnBomNodeByPartName(subCalcPartName);

        String partNumberOfChangedSubCalc = calculationPage.getPartNumberHeaderValue();
        calculationPage.clickPartTabButton()
            .enterPartName(changedSubCalPartName)
            .attachFile(XLS_ATTACHMENT)
            .attachFile(PHOTO_ATTACHMENT_PNG);

        assertThat(partTab.getNumberOfAttachments())
            .as("There must be two attachments!")
            .isEqualTo(2);

        saveCalculation(calculationPage);
        ExportsSidePanel exportsSidePanel = calculationPage.openExportCalculationSidePanel();
        exportsSidePanel.downloadAsTsetFile();

        bomExplorerPanel = calculationPage.getBomExplorerPanel();
        bomExplorerPanel.clickOnBomNodeByPartName(changedPartName);

        exportsSidePanel = calculationPage.openExportCalculationSidePanel();
        exportsSidePanel.downloadAsTsetFile();
        deleteCalculation(calculationPage);

        uploadTsetFileAndVerifyImportedCalculation(bomExplorerPanel, title, downloadedFileMainCalculation, changedPartName,
            partNumber, calculationPage);
        bomExplorerPanel = calculationPage.getBomExplorerPanel()
            .expandBomNodeByPartName(changedPartName);

        assertThat(bomExplorerPanel.isBomNodeHasChildBomNodeByPartName(changedPartName, changedSubCalPartName))
            .as("Bom Explorer Panel must have " + changedSubCalPartName + " subcalculation!")
            .isTrue();

        calculationPage.clickPartTabButton();

        assertThat(partTab.getNumberOfAttachments())
            .as("There must be two attachments!")
            .isEqualTo(2);

        uploadTsetFileAndVerifyImportedCalculation(bomExplorerPanel, title, downloadedFileSubCalculation, changedSubCalPartName,
            partNumberOfChangedSubCalc, calculationPage);
        calculationPage.clickPartTabButton();

        assertThat(partTab.getNumberOfAttachments())
            .as("There must be two attachments!")
            .isEqualTo(2);

        deleteCalculationFromDefaultTestProjectUsingApi(changedPartName);
        logout(calculationPage);
        deleteFileIfExists(downloadedFileMainCalculation);
        deleteFileIfExists(downloadedFileSubCalculation);
    }

    @Test
    @Description("Reimport an auto generated calculation")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-3618")
    public void reimportAnAutoGeneratedCalculationTest() {
        CalculationWithCost calculation = constructPREC();
        CalculationPage calculationPage = createCalculationWithCostModuleViaAPI(calculation);
        String partName = calculation.getPartName();

        String downloadedFileAbsolutePath = SYSTEM_DEFAULT_USER_DIR_PATH + File.separator + partName + TSET_FILE_EXTENSION;
        ExportsSidePanel exportsSidePanel = calculationPage.openExportCalculationSidePanel();
        exportsSidePanel.downloadAsTsetFile();
        deleteCalculation(calculationPage);

        String ceramicMold = "Ceramic Mold";

        calculationPage = importTsetArchive(downloadedFileAbsolutePath);
        BomExplorerPanel bomExplorerPanel = getProjectDashboardPage().getBomExplorerPanel();

        assertThat(calculationPage.getPartNameHeaderValue())
            .as("The calculation part name of reimported Prec in header is not correct")
            .isEqualTo(partName);

        BomEntry bomEntry = bomExplorerPanel.extractBomEntryFromSelectedBomNode();

        softAssert.assertThat(bomEntry.getPartName())
            .as("The calculation part name of reimported Prec in bom explorer is not correct")
            .isEqualTo(partName);
        softAssert.assertThat(bomEntry.getPartNumber())
            .as("The calculation part number of reimported Prec in bom explorer is not correct")
            .isEqualTo(calculation.getPartNumber());
        softAssert.assertThat(bomEntry.getProcurement())
            .as("BOM explorer entry's reimported Prec calculation procurement is different from value during creation!")
            .isEqualTo("PU");
        softAssert.assertAll();

        MaterialTab materialTab = calculationPage.clickMaterialTabButton();

        assertThat(materialTab.isObjectPresentInMaterialDetailsTableCostMode(ceramicMold))
            .as("Calculation has incorrect name in Material tab")
            .isTrue();

        bomExplorerPanel.expandBomNodeByPartName(partName);
        bomExplorerPanel.clickOnBomNodeByPartName(ceramicMold);

        bomEntry = bomExplorerPanel.extractBomEntryFromSelectedBomNode();

        softAssert.assertThat(bomEntry.getPartName())
            .as("The calculation is not imported correctly")
            .isEqualTo(ceramicMold);
        softAssert.assertThat(bomEntry.getProcurement())
            .as("BOM explorer selected entry's procurement is different from value during creation!")
            .isEqualTo("IH");
        softAssert.assertAll();

        deleteCalculationFromDefaultTestProjectUsingApi(partName);
        logout(calculationPage);
    }

    @Test
    @Description("Export a calculation by modifying the status then import it and verify")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-4775")
    public void exportACalculationWithCalculationStatusThenImportItAndCheckStatusTest() {
        String partName = getWithoutCostModulePartName();

        CalculationPage calculationPage = createCalculationWithoutCostModule(getProjectDashboardPage(),
            partName, RandomUtil.getPartNumber());

        calculationPage.clickParametersTabButton().selectStatus(IN_PROGRESS);

        String downloadedFileAbsolutePathInProgress = SYSTEM_DEFAULT_USER_DIR_PATH + File.separator + partName + TSET_FILE_EXTENSION;

        ExportsSidePanel exportsSidePanel = calculationPage.openExportCalculationSidePanel();
        exportsSidePanel.downloadAsTsetFile();

        deleteCalculation(calculationPage);

        calculationPage = importTsetArchive(downloadedFileAbsolutePathInProgress);

        //ToDo: below all assertions needs to be updated after the fix for import export status is fixed
        assertThat(calculationPage.clickParametersTabButton().getStatus())
            .as("Calculation status is not as expected")
            .isEqualTo(TO_DO.getStatus());

        calculationPage.clickParametersTabButton().selectStatus(DONE);

        String downloadedFileAbsolutePathDone = SYSTEM_DEFAULT_USER_DIR_PATH + File.separator + partName + TSET_FILE_EXTENSION;

        exportsSidePanel = calculationPage.openExportCalculationSidePanel();
        exportsSidePanel.downloadAsTsetFile();

        deleteCalculation(calculationPage);

        calculationPage = importTsetArchive(downloadedFileAbsolutePathDone);

        assertThat(calculationPage.clickParametersTabButton().getStatus())
            .as("Calculation status is not as expected")
            .isEqualTo(TO_DO.getStatus());

        calculationPage.clickParametersTabButton().selectStatus(BLOCKED);

        String downloadedFileAbsolutePathBlocked = SYSTEM_DEFAULT_USER_DIR_PATH + File.separator + partName + TSET_FILE_EXTENSION;

        exportsSidePanel = calculationPage.openExportCalculationSidePanel();
        exportsSidePanel.downloadAsTsetFile();

        deleteCalculation(calculationPage);

        calculationPage = importTsetArchive(downloadedFileAbsolutePathBlocked);

        assertThat(calculationPage.clickParametersTabButton().getStatus())
            .as("calc status is not as expected")
            .isEqualTo(TO_DO.getStatus());

        calculationPage.clickParametersTabButton().selectStatus(TO_DO);

        String downloadedFileAbsolutePathToDo = SYSTEM_DEFAULT_USER_DIR_PATH + File.separator + partName + TSET_FILE_EXTENSION;

        exportsSidePanel = calculationPage.openExportCalculationSidePanel();
        exportsSidePanel.downloadAsTsetFile();

        deleteCalculation(calculationPage);

        calculationPage = importTsetArchive(downloadedFileAbsolutePathToDo);

        assertThat(calculationPage.clickParametersTabButton().getStatus())
            .as("calc status is not To Do")
            .isEqualTo(TO_DO.getStatus());

        deleteCalculationFromDefaultTestProjectUsingApi(partName);
        logout(calculationPage);
    }

    @Test
    @Description("Automated Line Import/Export")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-5110")
    public void exportCalculationWithManufacturingStepsInAutomatedLineTest() {
        final String manuStepDesignation1 = "Assembly (10kg)";
        final String manuStepConfig1 = "Laser marking";
        final String manuStepDesignation2 = "Back injection molding";
        final String manuStepConfig2 = "Back injection equipment to be added to injection machine";
        CalculationWithCost chillCalculation = constructCHILL();
        String partName = chillCalculation.getPartName();

        CalculationPage calculationPage = createCalculationWithCostModuleViaAPI(chillCalculation);

        ManufacturingTab manufacturingTab = calculationPage.clickManufacturingTabButton();
        AddStepModal addStepModal = manufacturingTab.clickAddStepButton();
        addStepModal.addMasterDataStep(manuStepDesignation1, manuStepConfig1);
        addStepModal = manufacturingTab.clickAddStepButton();
        addStepModal.addMasterDataStep(manuStepDesignation2, manuStepConfig2);

        softAssert.assertThat(manufacturingTab.isStepsContain(manuStepDesignation1))
            .as("Newly added manufacturing step '%s' must be among steps!", manuStepDesignation1)
            .isTrue();
        softAssert.assertThat(manufacturingTab.isStepsContain(manuStepDesignation2))
            .as("Newly added manufacturing step '%s' must be among steps!", manuStepDesignation2)
            .isTrue();
        softAssert.assertAll();

        manufacturingTab
            .selectStepByName(manuStepDesignation1)
            .selectStepByName(manuStepDesignation2)
            .openActionsMenuOnStepByName(manuStepDesignation1)
            .clickCreateLineButton();

        softAssert.assertThat(manufacturingTab.isAutomatedLineIndicatorVisibleForStep(manuStepDesignation1))
            .as("Automated line indicator should be shown for manufacturing step: " + manuStepDesignation1)
            .isTrue();
        softAssert.assertThat(manufacturingTab.isAutomatedLineIndicatorVisibleForStep(manuStepDesignation2))
            .as("Automated line indicator should be shown for manufacturing step: " + manuStepDesignation2)
            .isTrue();
        softAssert.assertAll();

        calculationPage.clickSaveButton();
        String downloadedFileAbsolutePath = SYSTEM_DEFAULT_USER_DIR_PATH + File.separator + partName + TSET_FILE_EXTENSION;
        ExportsSidePanel exportsSidePanel = calculationPage.openExportCalculationSidePanel();
        exportsSidePanel.downloadAsTsetFile();

        deleteCalculationFromDefaultTestProjectUsingApi(partName);

        navigateToAndGetPageObject(getBaseUrl() + "project/" + getProjectKey(), ProjectDashboardPage.class);
        calculationPage.getBomExplorerPanel()
            .clickAddCalculationButton()
            .selectCalculationTypeTsetImport()
            .enterCalculationTitle(TITLE)
            .uploadTsetFile(downloadedFileAbsolutePath);

        assertThat(calculationPage.getPartNameHeaderValue())
            .as("Imported calculation part name is wrong")
            .isEqualTo(partName);

        manufacturingTab = calculationPage.clickManufacturingTabButton();

        softAssert.assertThat(manufacturingTab.isStepsContain(manuStepDesignation1))
            .as("Newly added manufacturing step '%s' must be among steps!", manuStepDesignation1)
            .isTrue();
        softAssert.assertThat(manufacturingTab.isStepsContain(manuStepDesignation2))
            .as("Newly added manufacturing step '%s' must be among steps!", manuStepDesignation2)
            .isTrue();
        softAssert.assertThat(manufacturingTab.isAutomatedLineIndicatorVisibleForStep(manuStepDesignation1))
            .as("Automated line indicator should be shown for manufacturing step: " + manuStepDesignation1)
            .isTrue();
        softAssert.assertThat(manufacturingTab.isAutomatedLineIndicatorVisibleForStep(manuStepDesignation2))
            .as("Automated line indicator should be shown for manufacturing step: " + manuStepDesignation2)
            .isTrue();
        softAssert.assertAll();

        deleteCalculationFromDefaultTestProjectUsingApi(partName);
        logout(calculationPage);
        deleteFileIfExists(downloadedFileAbsolutePath);
    }

    @Step("Upload tset file and verify that data of imported match with the original calculation, it is visible in Bom explorer panel and cost per part is positive")
    private void uploadTsetFileAndVerifyImportedCalculation(BomExplorerPanel bomExplorerPanel, String title, String downloadedFileAbsolutePath,
                                                            String partName, String partNumber, CalculationPage calculationPage) {
        bomExplorerPanel.clickAddCalculationButton()
            .selectCalculationTypeTsetImport()
            .enterCalculationTitle(title)
            .uploadTsetFile(downloadedFileAbsolutePath);

        // TODO this fails because costHeaderTotalValue element does not exist.
        softAssert.assertThat(calculationPage.isTotalCostValueBroken())
            .as("The calculation is broken after import!")
            .isFalse();
        softAssert.assertThat(calculationPage.getPartNameHeaderValue())
            .as("Imported calculation part name is wrong")
            .isEqualTo(partName);
        softAssert.assertThat(calculationPage.getVariantSelectorText())
            .as("Imported variant selector text is wrong")
            .isEqualTo(title);
        softAssert.assertThat(calculationPage.getTotalCostDoubleValue())
            .as("Cost per part value must be positive!")
            .isPositive();
        softAssert.assertThat(bomExplorerPanel.isCalculationPresent(partName, partNumber))
            .as("Bom Explorer Panel must have the imported calculation!")
            .isTrue();
        softAssert.assertAll();
    }
}
