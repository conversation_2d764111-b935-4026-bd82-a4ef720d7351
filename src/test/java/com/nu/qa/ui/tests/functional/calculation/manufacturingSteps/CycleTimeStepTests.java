package com.nu.qa.ui.tests.functional.calculation.manufacturingSteps;

import com.nu.qa.ui.data.ToastMessageDao;
import com.nu.qa.ui.data.builders.calculation.CalculationWithCost;
import com.nu.qa.ui.data.enums.CycleTimeStepGroupType;
import com.nu.qa.ui.data.enums.CycleTimeStepTypes;
import com.nu.qa.ui.framework.CalculationBaseTest;
import com.nu.qa.ui.framework.validationsteps.StepDetailsPageValidator;
import com.nu.qa.ui.pageobjects.CalculationPage;
import com.nu.qa.ui.pageobjects.modals.manufacturing.AddCycleTimeStepGroupModal;
import com.nu.qa.ui.pageobjects.modals.manufacturing.AddCycleTimeStepModal;
import com.nu.qa.ui.pageobjects.modals.manufacturing.AddStepModal;
import com.nu.qa.ui.pageobjects.tabs.ManufacturingTab;
import com.nu.qa.ui.pageobjects.tabs.manufacturing.CycleTimeStepsPage;
import com.nu.qa.ui.pageobjects.tabs.manufacturing.StepDetailsPage;
import com.nu.qa.ui.pageobjects.widgets.BomExplorerPanel;
import com.nu.qa.ui.pageobjects.widgets.ToastMessage;
import com.nu.qa.utilities.data.enums.Currencies;
import com.nu.qa.utilities.data.enums.units.Dimensions;
import com.nu.qa.utilities.utilities.RandomUtil;
import io.qameta.allure.*;
import org.assertj.core.api.SoftAssertions;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.util.List;

import static com.nu.qa.ui.data.builders.calculation.CalculationWithCostDirector.*;
import static com.nu.qa.ui.data.enums.CycleTimeStepGroupGrouping.PARALLEL;
import static com.nu.qa.ui.data.enums.CycleTimeStepGroupGrouping.SEQUENTIAL;
import static com.nu.qa.ui.data.enums.CycleTimeStepGroupType.MACHINING;
import static com.nu.qa.ui.data.enums.CycleTimeStepTypes.*;
import static com.nu.qa.ui.data.enums.materials.DCA.MG_AL_9_ZN_1;
import static com.nu.qa.utilities.data.enums.Currencies.EURO;
import static com.nu.qa.utilities.data.enums.units.Units.*;
import static com.nu.qa.utilities.utilities.RandomUtil.getPartNumber;
import static com.nu.qa.utilities.utilities.RandomUtil.getWithoutCostModulePartName;
import static org.assertj.core.api.Assertions.assertThat;

public class CycleTimeStepTests extends CalculationBaseTest implements StepDetailsPageValidator {

    public final static String EXPECTED_TOOL_FOR_SHOULDER_MILLING_CIRCULAR_OUTER = "F4041.B22.050.Z04.13-LNGX1307";
    private final String ten = "10.00";
    private final String sixty = "60.00";
    private final String fifteen = "15.00";
    private final String fifty = "50.00";
    private final String twenty = "20.00";
    private final String twentyTwo = "22.00";
    private final String twentyFive = "25.00";
    private final String eighty = "80.00";
    private final String forty = "40.00";
    private final String twelve = "12.00";
    private final String oneTwenty = "120.00";
    private final String hundred = "100.00";
    private final String thirty = "30.00";
    private final String thirtyEight = "38.00";
    final String groupName = "Milling group";
    final String cycleTimeName = "Slot/Pocket Milling";
    final String customDesignationInner = "Shoulder milling circular inner";
    final String customDesignationOuter = "Shoulder milling circular outer";
    final String slotType = "Unidirectional open slot";
    final String slotTypeBi = "Bilaterally closed slot";
    final String stepName = "Melting";
    final String materialGroup = "Non-alloyed steel, C <= 0.25%, Annealed";
    final String defaultStepGroup = "Cycle time step group";
    final String seamWelding = "Seam welding";
    final String assemblyForWelding = "Assembly for welding";
    final String roughCycleDesignation = "Rough cycle time step";
    final String cycleTimeStep1 = cycleTimeName + " 1";
    final String cycleTimeStep2 = cycleTimeName + " 2";
    final String cycleTimeStep3 = cycleTimeName + " 3";
    final String cycleTimeStep4 = cycleTimeName + " 4";
    final String cycleTimeStep5 = cycleTimeName + " 5";
    final String cycleTimeStepAddedSuccessfully = "Cycle time step added successfully";
    final String cycleTimeStepGroupAddedSuccessfully = "Cycle time step group added successfully";
    final String cycleTimeStepText = "Cycle time step ";
    final String cycleTimeStepGroupText = "Cycle time step group ";
    final String roughCycleDesignation1 = roughCycleDesignation + " 1";
    final String roughCycleDesignation2 = roughCycleDesignation + " 2";


    @Test
    @Description("Edit cycle time step details")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-2692")
    public void editCycleTimeStepDetailsTest() {
        String stepName = "Die casting";
        String cycleTimeStepName = "Fill material";
        String modifiedTimeValue = "10.00";
        String adjustmentRateValue = "80.00";

        CalculationWithCost dcaCalculation = constructDCA();
        createCalculationWithCostModuleViaAPI(dcaCalculation);
        String partName = dcaCalculation.getPartName();

        CalculationPage calculationPage = getProjectDashboardPage().getBomExplorerPanel()
                .clickOnBomNodeByPartName(partName);

        ManufacturingTab manufacturingTab = calculationPage.clickManufacturingTabButton();

        double totalCalcValueBeforeUpdate = calculationPage.getTotalCostDoubleValue();
        StepDetailsPage stepDetailsPage = manufacturingTab.clickOnStepByName(stepName);

        String originalTotalCycleTime = stepDetailsPage.getDetailedCycleTimeValue();
        CycleTimeStepsPage cycleTimeStepsPage = stepDetailsPage.clickOnCycleTimeStepByName(cycleTimeStepName)
                .enterTime(modifiedTimeValue)
                .enterAdjustmentRate(adjustmentRateValue);

        softAssert.assertThat(cycleTimeStepsPage.getTimeValue())
                .as("The time value must be the same as in the input!")
                .isEqualTo(modifiedTimeValue);
        softAssert.assertThat(cycleTimeStepsPage.getAdjustmentRateValue())
                .as("The adjustment rate must be the same as in the input!")
                .isEqualTo(adjustmentRateValue);
        softAssert.assertThat(cycleTimeStepsPage.isTimeUnlinkIconVisible())
                .as("The broken chain icon must be visible in the time input field!")
                .isTrue();
        softAssert.assertThat(cycleTimeStepsPage.isAdjustmentRateUnlinkIconVisible())
                .as("The broken chain icon must be visible in the adjustment rate input field!")
                .isTrue();
        softAssert.assertThat(calculationPage.getTotalCostDoubleValue())
                .as("Total calculation value must be greater than the input!")
                .isGreaterThan(totalCalcValueBeforeUpdate);
        softAssert.assertThat(cycleTimeStepsPage.getCycleTimeStepTextByCycleNameFromSideNavigation(cycleTimeStepName))
                .as("The adjustment value must be the same as kpi value on the side navigation")
                .endsWith(cycleTimeStepsPage.getAdjustedTimeValue());
        softAssert.assertAll();

        cycleTimeStepsPage.clickBackToPreviousButton(StepDetailsPage.class);
        assertThat(stepDetailsPage.getDetailedCycleTimeValue())
                .as("Total cycle time must be updated!")
                .isGreaterThan(originalTotalCycleTime);

        calculationPage.clickSaveButton();
        softAssert.assertThat(calculationPage.isSaveButtonContainingNotificationIconBeforeSave())
                .as("Save button must not be enabled after saving changes!")
                .isFalse();
        softAssert.assertThat(calculationPage.getTotalCostDoubleValue())
                .as("Total calculation value must be greater than the input!")
                .isGreaterThan(totalCalcValueBeforeUpdate);
        softAssert.assertAll();

        deleteCalculationThenLogout(calculationPage);
    }

    @Test
    @Description("Add new cycle time step to manufacturing step")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-2693")
    public void addNewCycleTimeStepToManufacturingStepTest() {
        String stepName = "Die casting";
        String cycleTimeStepDesignation = "Rough cycle time step";
        String stepTimeValue = "12.5";

        CalculationWithCost dcaCalculation = constructDCA();
        createCalculationWithCostModuleViaAPI(dcaCalculation);
        String partName = dcaCalculation.getPartName();

        CalculationPage calculationPage = getProjectDashboardPage().getBomExplorerPanel()
                .clickOnBomNodeByPartName(partName);

        ManufacturingTab manufacturingTab = calculationPage.clickManufacturingTabButton();

        double totalCalcValueBeforeUpdate = calculationPage.getTotalCostDoubleValue();
        StepDetailsPage stepDetailsPage = manufacturingTab.clickOnStepByName(stepName);

        String originalTotalCycleTime = stepDetailsPage.getDetailedCycleTimeValue();
        AddCycleTimeStepModal addCycleTimeStepModal = stepDetailsPage.clickAddCycleTimeStepButtonOnAGroup(defaultStepGroup, DIRECT_INPUT);

        addCycleTimeStepModal.enterCycleTimeStepDesignation(cycleTimeStepDesignation)
                .enterTimeValue(stepTimeValue)
                .clickAddCycleTimeStepButton();
        assertAndCloseTooltipAfterAddingAStepOrGroup(cycleTimeStepAddedSuccessfully, cycleTimeStepText, cycleTimeStepDesignation, defaultStepGroup);

        softAssert.assertThat(stepDetailsPage.hasStepObject(cycleTimeStepDesignation))
                .as("A new step must be added to the group!")
                .isTrue();
        softAssert.assertThat(stepDetailsPage.getDetailedCycleTimeValue())
                .as("Total Cycle time must be updated and greater than the input!")
                .isGreaterThan(originalTotalCycleTime);
        softAssert.assertThat(calculationPage.getTotalCostDoubleValue())
                .as("Total calculation value must be updated and greater than the input!")
                .isGreaterThan(totalCalcValueBeforeUpdate);
        softAssert.assertAll();

        calculationPage.clickSaveButton();
        softAssert.assertThat(calculationPage.isSaveButtonContainingNotificationIconBeforeSave())
                .as("Save button must not be enabled after saving changes!")
                .isFalse();
        softAssert.assertThat(calculationPage.getTotalCostDoubleValue())
                .as("Total calculation value must be the same as in the input!")
                .isGreaterThan(totalCalcValueBeforeUpdate);
        softAssert.assertAll();

        deleteCalculationThenLogout(calculationPage);
    }

    @Test
    @Description("Duplicate cycle time step")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-2694")
    public void duplicateCycleTimeStepTest() {
        String stepName = "Die casting";
        String cycleTimeStepName = "Solidification time";

        CalculationWithCost dcaCalculation = constructDCA();
        dcaCalculation.setMaterialName(MG_AL_9_ZN_1.getMaterialName());
        createCalculationWithCostModuleViaAPI(dcaCalculation);
        String partName = dcaCalculation.getPartName();

        CalculationPage calculationPage = getProjectDashboardPage().getBomExplorerPanel()
                .clickOnBomNodeByPartName(partName);

        StepDetailsPage stepDetailsPage = calculationPage.clickManufacturingTabButton()
                .clickOnStepByName(stepName);
        assertThat(stepDetailsPage.isCycleTimeSettingsTableVisible())
                .as("Cycle time settings table must be visible!")
                .isTrue();

        double totalCalcValueBeforeUpdate = calculationPage.getTotalCostDoubleValue();
        String originalTotalCycleTime = stepDetailsPage.getDetailedCycleTimeValue();
        int updatedTableSize = stepDetailsPage.getCtsTableSize() + 1;

        stepDetailsPage.duplicateCycleTimeStepOrGroup(cycleTimeStepName, false);
        softAssert.assertThat(stepDetailsPage.getCtsTableSize())
                .as("Cycle time settings table should have one row more!")
                .isEqualTo(updatedTableSize);
        softAssert.assertThat(stepDetailsPage.getDetailedCycleTimeValue())
                .as("Total Cycle time must be updated and greater than the input!")
                .isGreaterThan(originalTotalCycleTime);
        softAssert.assertThat(calculationPage.getTotalCostDoubleValue())
                .as("Total calculation value must be updated and greater than the input!")
                .isGreaterThan(totalCalcValueBeforeUpdate);
        softAssert.assertAll();

        calculationPage.clickSaveButton();
        softAssert.assertThat(calculationPage.isSaveButtonContainingNotificationIconBeforeSave())
                .as("Save button must not be enabled after saving changes!")
                .isFalse();
        softAssert.assertThat(calculationPage.getTotalCostDoubleValue())
                .as("Total calculation value must be greater than the original calc value since it " +
                        "has been increased by the step duplication!")
                .isGreaterThan(totalCalcValueBeforeUpdate);
        softAssert.assertAll();

        deleteCalculationThenLogout(calculationPage);
    }

    @Test
    @Description("Delete cycle time step")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-2695")
    public void deleteCycleTimeStepTest() {
        String stepName = "Die casting";
        String cycleTimeStepName = "Close slider";

        CalculationWithCost dcaCalculation = constructDCA();
        createCalculationWithCostModuleViaAPI(dcaCalculation);
        String partName = dcaCalculation.getPartName();

        CalculationPage calculationPage = getProjectDashboardPage().getBomExplorerPanel()
                .clickOnBomNodeByPartName(partName);
        StepDetailsPage stepDetailsPage = calculationPage.clickManufacturingTabButton()
                .clickOnStepByName(stepName);

        double totalCalcValueBeforeUpdate = calculationPage.getTotalCostDoubleValue();
        String originalTotalCycleTime = stepDetailsPage.getDetailedCycleTimeValue();

        stepDetailsPage.deleteCycleTimeTableObjectByName(cycleTimeStepName);
        softAssert.assertThat(stepDetailsPage.hasStepObject(cycleTimeStepName))
                .as("Deleted step must not be in the group!")
                .isFalse();
        softAssert.assertThat(stepDetailsPage.getDetailedCycleTimeValue())
                .as("Total Cycle time must not be the same as in the input!")
                .isNotEqualTo(originalTotalCycleTime);
        softAssert.assertThat(calculationPage.getTotalCostDoubleValue())
                .as("Total calculation value must be updated and not the same as in the input!")
                .isNotEqualTo(totalCalcValueBeforeUpdate);
        softAssert.assertAll();

        calculationPage.clickSaveButton();
        softAssert.assertThat(calculationPage.isSaveButtonContainingNotificationIconBeforeSave())
                .as("Save button must not be enabled after saving changes!")
                .isFalse();
        softAssert.assertThat(calculationPage.getTotalCostDoubleValue())
                .as("Total calculation value must not be the same as in the input!")
                .isNotEqualTo(totalCalcValueBeforeUpdate);
        softAssert.assertAll();

        deleteCalculationThenLogout(calculationPage);
    }

    @Test
    @Description("Add and modify Assembly for Welding Cycle time step")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-3705")
    public void addAndModifyCycleTimeStepAssemblyForWelding() {
        CalculationWithCost precCalculation = constructPREC();
        createCalculationWithCostModuleViaAPI(precCalculation);
        String partName = precCalculation.getPartName();
        String stepName = "Precision casting";

        CalculationPage calculationPage = getProjectDashboardPage().getBomExplorerPanel()
                .clickOnBomNodeByPartName(partName);

        StepDetailsPage stepDetailsPage = calculationPage.clickManufacturingTabButton()
                .clickOnStepByName(stepName);

        AddCycleTimeStepModal addCycleTimeStepModal = stepDetailsPage.clickAddCycleTimeStepButtonOnAGroup(defaultStepGroup, CALCULATED)
                .clickOnCycleTimeStep(ASSEMBLY_FOR_WELDING);
        assertThat(addCycleTimeStepModal.getAmountOfStairs())
                .as("Number of stairs should not contain null!")
                .isNotEqualTo("null");
        addCycleTimeStepModal.enterWorkSpaceLength("98.675")
                .enterWorkSpaceWidth("98.675")
                .enterAverageWeight(ten)
                .selectLiftingGear(YES)
                .selectClampingNeeded(NO)
                .selectLoadingUnloading("Loading")
                .enterWalkingDistance("100")
                .selectKneelNeeded(YES)
                .selectNeedBodyTwist(NO)
                .enterStairsAmount("23");
        assertThat(addCycleTimeStepModal.isAddButtonEnabled())
                .as("Add button should be enabled because all required fields are populated!")
                .isTrue();
        addCycleTimeStepModal.clickAddCycleTimeStepButton();
        assertAndCloseTooltipAfterAddingAStepOrGroup(cycleTimeStepAddedSuccessfully, cycleTimeStepText, assemblyForWelding, defaultStepGroup);
        String originalTotalCycleTime = stepDetailsPage.getDetailedCycleTimeValue();
        saveCalculation(calculationPage);
        stepDetailsPage.clickOnCycleTimeStepByName(assemblyForWelding);
        addCycleTimeStepModal.enterWorkSpaceLength("188")
                .enterWorkSpaceWidth("188")
                .enterAverageWeight("2")
                .selectLiftingGear(NO)
                .enterStairsAmount("7")
                .selectLoadingUnloading("Unloading");
        stepDetailsPage.clickBackToPreviousButton(StepDetailsPage.class);
        String cycleTimeAfterEdit = stepDetailsPage.getDetailedCycleTimeValue();
        assertThat(cycleTimeAfterEdit)
                .as("Total Cycle time must be updated and greater than the original value!")
                .isGreaterThan(originalTotalCycleTime);

        saveAndDeleteCalculationThenLogout(calculationPage);
    }

    @Test
    @Description("Add and modify Seam Welding Cycle time step")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-3706")
    public void addAndModifyCycleTimeStepSeamWelding() {
        CalculationPage calculationPage = addStepToCalculation(createCalculationWithoutCostModule(getProjectDashboardPage(),
                getWithoutCostModulePartName(), getPartNumber()));

        ManufacturingTab manufacturingTab = new ManufacturingTab(getDriver());
        StepDetailsPage stepDetailsPage = manufacturingTab.clickOnStepByName(STEP_DESIGNATION);

        AddCycleTimeStepModal addCycleTimeStepModal = stepDetailsPage.clickAddCycleTimeStepButtonOnAGroup(defaultStepGroup, CALCULATED)
                .clickOnCycleTimeStep(SEAM_WELDING);
        softAssert.assertThat(addCycleTimeStepModal.getNumberOfSeams())
                .as("Number of seams should not contain null!")
                .isNotEqualTo("null");
        addCycleTimeStepModal.selectProcedure("Robot")
                .selectWeldingType("CMT")
                .selectMaterial("Steel")
                .selectSeamGeometry("Normal")
                .enterSeamLength("100")
                .enterSeamsNum("45");
        assertThat(addCycleTimeStepModal.isAddButtonEnabled())
                .as("Add button should be enabled because all required fields are populated!")
                .isTrue();
        addCycleTimeStepModal.clickAddCycleTimeStepButton();
        assertAndCloseTooltipAfterAddingAStepOrGroup(cycleTimeStepAddedSuccessfully, cycleTimeStepText, seamWelding, defaultStepGroup);
        String originalTotalCycleTime = stepDetailsPage.getDetailedCycleTimeValue();
        saveCalculation(calculationPage);
        stepDetailsPage.clickOnCycleTimeStepByName(seamWelding);
        addCycleTimeStepModal.selectProcedure("Manual")
                .selectWeldingType("Laser")
                .selectMaterial("Aluminium")
                .selectSeamGeometry("Easy")
                .enterSeamsNum(ten)
                .enterSeamLength("78.98");
        saveCalculation(calculationPage);
        stepDetailsPage.clickBackToPreviousButton(StepDetailsPage.class);
        String cycleTimeAfterEdit = stepDetailsPage.getDetailedCycleTimeValue();
        softAssert.assertThat(cycleTimeAfterEdit)
                .as("Total Cycle time must be updated and greater than the original value!")
                .isGreaterThan(originalTotalCycleTime);
        softAssert.assertAll();
        deleteCalculationThenLogout(calculationPage);
    }

    @Test
    @Description("Add and modify Spot Welding Cycle time step")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-3707")
    public void addAndModifyCycleTimeStepSpotWelding() {
        String stepAdded = "Spot welding";
        String filterOption = "Welding";

        CalculationWithCost precCalculation = constructDCA();
        createCalculationWithCostModuleViaAPI(precCalculation);
        String partName = precCalculation.getPartName();
        CalculationPage calculationPage = getProjectDashboardPage().getBomExplorerPanel()
                .clickOnBomNodeByPartName(partName);
        StepDetailsPage stepDetailsPage = calculationPage.clickManufacturingTabButton()
                .clickOnStepByName(stepName);

        AddCycleTimeStepModal addCycleTimeStepModal = stepDetailsPage.clickAddCycleTimeStepButtonOnAGroup(defaultStepGroup, CALCULATED);

        softAssert.assertThat(addCycleTimeStepModal.isSearchInputVisible())
                .as("Search Input should be visible")
                .isTrue();
        softAssert.assertThat(addCycleTimeStepModal.isFilterDropdownVisible())
                .as("Filter dropdown should be visible")
                .isTrue();
        softAssert.assertAll();

        addCycleTimeStepModal.filterCycleTimeStep(filterOption)
                .clickOnCycleTimeStep(SPOT_WELDING);
        softAssert.assertThat(addCycleTimeStepModal.getNumberOfSpots())
                .as("Number of spots should not contain null!")
                .isNotEqualTo("null");
        softAssert.assertAll();
        addCycleTimeStepModal.enterSpotsNum("64");
        assertThat(addCycleTimeStepModal.isAddButtonEnabled())
                .as("Add button should be enabled because all required fields are populated!")
                .isTrue();
        addCycleTimeStepModal.clickAddCycleTimeStepButton();
        assertAndCloseTooltipAfterAddingAStepOrGroup(cycleTimeStepAddedSuccessfully, cycleTimeStepText, stepAdded, defaultStepGroup);
        String originalTotalCycleTime = stepDetailsPage.getDetailedCycleTimeValue();
        saveCalculation(calculationPage);
        stepDetailsPage.clickOnCycleTimeStepByName(stepAdded);
        addCycleTimeStepModal.enterSpotsNum(oneTwenty);
        saveCalculation(calculationPage);
        stepDetailsPage.clickBackToPreviousButton(StepDetailsPage.class);
        String cycleTimeAfterEdit = stepDetailsPage.getDetailedCycleTimeValue();
        assertThat(cycleTimeAfterEdit)
                .as("Total Cycle time must be updated and greater than the original value!")
                .isGreaterThan(originalTotalCycleTime);

        deleteCalculationThenLogout(calculationPage);
    }

    @Test
    @Description("Unsuccessful adding of Welding Cycle time")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-3710")
    public void unsuccessfulAddingOfWeldingCycleTime() {

        CalculationPage calculationPage = addStepToCalculation(createCalculationWithoutCostModule(getProjectDashboardPage(),
                getWithoutCostModulePartName(), getPartNumber()));
        String stepName = "Test step designation";
        ManufacturingTab manufacturingTab = new ManufacturingTab(getDriver());
        saveCalculation(calculationPage);
        StepDetailsPage stepDetailsPage = manufacturingTab.clickOnStepByName(stepName);
        assertThat(stepDetailsPage.isCycleTimeSettingsTableVisible())
                .as("Cycle time settings table must be visible!")
                .isTrue();
        AddCycleTimeStepModal addCycleTimeStepModal = stepDetailsPage.clickAddCycleTimeStepButtonOnAGroup(defaultStepGroup, CALCULATED)
                .clickOnCycleTimeStep(SEAM_WELDING);
        addCycleTimeStepModal.clickAddCycleTimeForErrorMessage();

        assertValidationErrorMessage(addCycleTimeStepModal, List.of(
            "Procedure", "Welding type", "Material", "Seam geometry", "Total seam length", "Number of seams", "Welding speed"
        ));

        addCycleTimeStepModal.clickBackButtonOnCycleStepModal();

        addCycleTimeStepModal.clickOnCycleTimeStep(SPOT_WELDING);
        addCycleTimeStepModal.enterSpotsNum("0");
        addCycleTimeStepModal.clickAddCycleTimeForErrorMessage();
        assertAndCloseTooltipAfterAddingAStepOrGroup("An error occurred",
                "Number of spots must be greater than 0.", null, null);
        addCycleTimeStepModal.clickBackButtonOnCycleStepModal();

        addCycleTimeStepModal.clickOnCycleTimeStep(ASSEMBLY_FOR_WELDING);
        addCycleTimeStepModal.clickAddCycleTimeForErrorMessage();
        assertValidationErrorMessage(addCycleTimeStepModal, List.of("Workspace length", "Workspace width", "Average weight", "Use lifting gear", "Clamping needed", "Loading / unloading", "Total walking distance"));
        addCycleTimeStepModal.clickBackButtonOnCycleStepModal();
        ToastMessage.closeToastsWithEscKey(getDriver());
        addCycleTimeStepModal.clickModalsCloseButton();
        deleteCalculationThenLogout(calculationPage);
    }

    @Test
    @Description("Create a Circular pocket milling cycle time step for machining cycle time step group")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-3858")
    public void createCircularPocketMillingCycleTimeStepForMachiningCycleTimeStepGroupTest() {
        CalculationWithCost calculation = constructSAND();
        String customDesignation = "Circular Pocket Milling";
        String expectedTool = "********-20";
        String modifiedTool = "F4041.B22.050.Z04.13-LNGX130720R-L55";
        String expectedTime = "7.35";
        String updatedTime = "25.66";

        CalculationPage calculationPage = createCalculationWithCostModuleViaAPI(calculation);

        ManufacturingTab manufacturingTab = calculationPage.clickManufacturingTabButton();
        StepDetailsPage stepDetailsPage = manufacturingTab.clickOnStepByName(stepName);

        stepDetailsPage = stepDetailsPage.scrollToSystemCard();
        AddCycleTimeStepModal addCycleTimeStepModal = stepDetailsPage.clickAddCycleTimeGroupButton()
                .enterDesignation(groupName)
                .chooseGroupType(MACHINING)
                .chooseMaterial("Other")
                .chooseMaterialGroup(materialGroup)
                .clickAddCycleTimeStepForCalculatedToGroupButton()
                .clickOnCycleTimeStep(CIRCULAR_POCKET_MILLING)
                .enterDiameter(thirty)
                .enterPlungeAngle(ten)
                .enterDepth(ten);
        assertThat(stepDetailsPage.hasStepObject(groupName))
                .as("A new group must have been added to a step!")
                .isTrue();
        assertSelectedToolDataAndFinishAddingStep(addCycleTimeStepModal, expectedTool);
        CycleTimeStepsPage cycleTimeStepsPage = stepDetailsPage.clickOnCycleTimeStepByName(customDesignation);
        assertExpectedTimeInCycleTimeStepDetails(cycleTimeStepsPage, expectedTime);
        addCycleTimeStepModal.enterDiameter(sixty)
                .enterPlungeAngle(fifteen)
                .enterDepth(twenty);
        assertExpectedToolAndUpdatedTime(cycleTimeStepsPage, EXPECTED_TOOL_FOR_SHOULDER_MILLING_CIRCULAR_OUTER, updatedTime);
        cycleTimeStepsPage.chooseDifferentTool(modifiedTool);
        assertThat(cycleTimeStepsPage.getToolDiameterValue())
                .as("Tool diameter value should change when tool is modified manually!")
                .isEqualTo("50.00");

        saveAndDeleteCalculationThenLogout(calculationPage);
    }

    @Test
    @Description("Create a Groove milling circular inner cycle time step for machining cycle time step group")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-3904")
    public void createGrooveMillingCircularInnerCycleTimeStepForMachiningCycleTimeStepGroupTest() {
        CalculationWithCost calculation = constructSAND();
        String customDesignation = "Internal Circular Groove Milling";
        String expectedTool = "Horn Groove Milling 306.0150";
        String expectedTime = "410.68";
        String updatedTime = "295.80";

        CalculationPage calculationPage = createCalculationWithCostModuleViaAPI(calculation);

        ManufacturingTab manufacturingTab = calculationPage.clickManufacturingTabButton();
        StepDetailsPage stepDetailsPage = manufacturingTab.clickOnStepByName(stepName);

        stepDetailsPage.scrollToSystemCard();
        AddCycleTimeStepModal addCycleTimeStepModal = stepDetailsPage.clickAddCycleTimeGroupButton()
                .enterDesignation(groupName)
                .chooseGroupType(MACHINING)
                .chooseMaterial("Other")
                .chooseMaterialGroup(materialGroup)
                .clickAddCycleTimeStepForCalculatedToGroupButton()
                .clickOnCycleTimeStep(GROOVE_MILLING_INNER)
                .enterOuterDiameter(sixty)
                .enterInnerDiameter(thirty)
                .enterWidth(ten);

        softAssert.assertThat(addCycleTimeStepModal.getDepthValue())
                .as("Calculated depth is not correct")
                .isNotBlank();
        softAssert.assertThat(stepDetailsPage.hasStepObject(groupName))
                .as("A new group must have been added to a step!")
                .isTrue();
        softAssert.assertAll();
        assertSelectedToolDataAndFinishAddingStep(addCycleTimeStepModal, expectedTool);

        CycleTimeStepsPage cycleTimeStepsPage = stepDetailsPage.clickOnCycleTimeStepByName(customDesignation);
        assertExpectedTimeInCycleTimeStepDetails(cycleTimeStepsPage, expectedTime);
        addCycleTimeStepModal.enterInnerDiameter(ten)
                .enterOuterDiameter(forty);
        assertExpectedToolAndUpdatedTime(cycleTimeStepsPage, expectedTool, updatedTime);
        addCycleTimeStepModal.enterToolDiameter(twelve);
        softAssert.assertThat(cycleTimeStepsPage.getSelectedTool())
                .as("Selected tool should not be modified when the tool diameter is manually modified!")
                .startsWith(expectedTool);
        softAssert.assertAll();
        saveAndDeleteCalculationThenLogout(calculationPage);
    }

    @Test
    @Description("Create a Groove milling circular outer cycle time step for machining cycle time step group")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-3914")
    public void createGrooveMillingCircularOuterCycleTimeStepForMachiningCycleTimeStepGroupTest() {
        CalculationWithCost calculation = constructDCA();
        String customDesignation = "External Circular Groove Milling";
        String expectedTool = "Horn Circlip Groove 306";
        String expectedTime = "150.36";
        String updatedTime = "270.03";

        CalculationPage calculationPage = createCalculationWithCostModuleViaAPI(calculation);

        ManufacturingTab manufacturingTab = calculationPage.clickManufacturingTabButton();
        StepDetailsPage stepDetailsPage = manufacturingTab.clickOnStepByName(stepName);

        stepDetailsPage.scrollToSystemCard();
        AddCycleTimeStepModal addCycleTimeStepModal = stepDetailsPage.clickAddCycleTimeGroupButton()
                .enterDesignation(groupName)
                .chooseGrouping(PARALLEL)
                .chooseGroupType(MACHINING)
                .chooseMaterial("Other")
                .chooseMaterialGroup(materialGroup)
                .clickAddCycleTimeStepForCalculatedToGroupButton()
                .clickOnCycleTimeStep(GROOVE_MILLING_OUTER)
                .enterOuterDiameter(twentyTwo)
                .enterInnerDiameter(twenty)
                .enterWidth(ten);

        softAssert.assertThat(addCycleTimeStepModal.getDepthValue())
                .as("Calculated depth is not correct")
                .isEqualTo("1.00");
        softAssert.assertThat(stepDetailsPage.hasStepObject(groupName))
                .as("A new group must have been added to a step!")
                .isTrue();
        softAssert.assertAll();
        assertSelectedToolDataAndFinishAddingStep(addCycleTimeStepModal, expectedTool);

        CycleTimeStepsPage cycleTimeStepsPage = stepDetailsPage.clickOnCycleTimeStepByName(customDesignation);
        assertExpectedTimeInCycleTimeStepDetails(cycleTimeStepsPage, expectedTime);
        addCycleTimeStepModal.enterOuterDiameter(forty)
                .enterInnerDiameter(thirtyEight);
        assertExpectedToolAndUpdatedTime(cycleTimeStepsPage, expectedTool, updatedTime);

        saveAndDeleteCalculationThenLogout(calculationPage);
    }


    @DataProvider(name = "shoulderMillingCircularCycleTime-data")
    public Object[][] testDataShoulderMilling() {
        return new Object[][]{
                {SHOULDER_MILLING_INNER, "********-20", sixty, thirty, ten, "0.1", "2 832.96", "4", "12.43", forty, twentyFive, "8.64", "********-20", customDesignationInner, "QA-3952"},
                {SHOULDER_MILLING_OUTER, "********-20", forty, twenty, ten, "0.1", "2 832.96", "4", "5.47", sixty, twentyFive, "4.72", EXPECTED_TOOL_FOR_SHOULDER_MILLING_CIRCULAR_OUTER, customDesignationOuter, "QA-3951"}
        };
    }

    @Test(dataProvider = "shoulderMillingCircularCycleTime-data")
    @Description("Create cycle time step for Shoulder milling circular inner and outer cycle time")
    @Severity(SeverityLevel.CRITICAL)
    @TmsLinks({@TmsLink("QA-3952"), @TmsLink("QA-3951")})
    public void createShoulderMillingCircularCycleTimeStepForMachiningCycleTimeStepGroupTest(CycleTimeStepTypes cycleTimeStepTypes,
                                                                                             String expectedTool,
                                                                                             String outerDiameter, String innerDiameter,
                                                                                             String depth, String feedRate,
                                                                                             String rotationSpeed, String teethNum,
                                                                                             String expectedTime, String outerDiameterUpdate,
                                                                                             String innerDiameterUpdate,
                                                                                             String updatedTime, String toolAfterUpdate, String customDesignation, String tmsLink) {
        String partName = RandomUtil.getWithoutCostModulePartName();
        CalculationPage calculationPage = createCalculationWithoutCostModule(getProjectDashboardPage(),
                LOCATION, EURO, partName, RandomUtil.getPartNumber(), Dimensions.AREA);
        addStepToCalculation(calculationPage);
        ManufacturingTab manufacturingTab = calculationPage.clickManufacturingTabButton();
        StepDetailsPage stepDetailsPage = manufacturingTab.clickOnStepByName(STEP_DESIGNATION)
                .scrollToCycleTimeCard();

        AddCycleTimeStepModal addCycleTimeStepModal = stepDetailsPage.clickAddCycleTimeGroupButton()
                .enterDesignation(groupName)
                .chooseGroupType(MACHINING)
                .chooseMaterial("Other")
                .chooseMaterialGroup(materialGroup)
                .clickAddCycleTimeStepForCalculatedToGroupButton();
        ToastMessageDao toastMessageDao = ToastMessage.getLastToastMessage(getDriver());
        softAssert.assertThat(toastMessageDao.getTitle())
                .as("Cycle time step group successful toast message title didn't show up correctly!")
                .isEqualTo("Cycle time step group added successfully");
        softAssert.assertThat(toastMessageDao.getMessage())
                .as("Cycle time step group successful toast message must appear and contain text!")
                .isEqualTo("Cycle time step group " + groupName + " added to " + STEP_DESIGNATION);
        softAssert.assertAll();
        ToastMessage.closeToastsWithEscKey(getDriver());

        addShoulderMillingCycleTimeStepToAGroup(addCycleTimeStepModal, cycleTimeStepTypes, outerDiameter,
                innerDiameter, depth, expectedTool);

        CycleTimeStepsPage cycleTimeStepsPage = stepDetailsPage.clickOnCycleTimeStepByName(customDesignation);
        assertExpectedTimeInCycleTimeStepDetails(cycleTimeStepsPage, expectedTime);
        addCycleTimeStepModal.enterOuterDiameter(outerDiameterUpdate)
                .enterInnerDiameter(innerDiameterUpdate);
        assertExpectedToolAndUpdatedTime(cycleTimeStepsPage, toolAfterUpdate, updatedTime);

        saveAndDeleteCalculationThenLogout(calculationPage);
    }

    @Test
    @Description("Create cycle time step for Slot/Pocket milling")
    @Severity(SeverityLevel.CRITICAL)
    @TmsLink("QA-3900")
    public void createSlotPocketMillingUnidirectionalEvenSlotForMachiningCycleTimeStepGroupTest() {
        String partName = RandomUtil.getWithoutCostModulePartName();

        CalculationPage calculationPage = createCalculationWithoutCostModule(getProjectDashboardPage(),
                LOCATION, EURO, partName, RandomUtil.getPartNumber(), Dimensions.AREA);

        addStepToCalculation(calculationPage);

        StepDetailsPage stepDetailsPage = calculationPage.clickManufacturingTabButton()
                .clickOnStepByName(STEP_DESIGNATION)
                .scrollToCycleTimeCard();

        AddCycleTimeStepModal addCycleTimeStepModal = stepDetailsPage.clickAddCycleTimeGroupButton()
                .enterDesignation(groupName)
                .chooseGroupType(MACHINING)
                .chooseMaterial("Other")
                .chooseMaterialGroup(materialGroup)
                .clickAddCycleTimeStepForCalculatedToGroupButton();
        ToastMessageDao toastMessageDao = ToastMessage.getLastToastMessage(getDriver());
        softAssert.assertThat(toastMessageDao.getTitle())
                .as("Cycle time step group successful toast message title didn't show up correctly!")
                .isEqualTo("Cycle time step group added successfully");
        softAssert.assertThat(toastMessageDao.getMessage())
                .as("Cycle time step group successful toast message must appear and contain text!")
                .isEqualTo("Cycle time step group " + groupName + " added to " + STEP_DESIGNATION);
        softAssert.assertAll();
        ToastMessage.closeToastsWithEscKey(getDriver());

        addSlotPocketCycleTimeStepToAGroup(addCycleTimeStepModal, SLOT_POCKET_MILLING, cycleTimeStep1, slotType,
                "F4041.B27.080.Z07.13", ten, oneTwenty, ten);
        stepDetailsPage.clickAddCycleTimeStepButtonOnAGroup(groupName, CALCULATED);
        addSlotPocketCycleTimeStepToAGroup(addCycleTimeStepModal, SLOT_POCKET_MILLING, cycleTimeStep2, slotType,
                "F4041.T36.040.Z03.13", twenty, forty, thirty);
        stepDetailsPage.clickAddCycleTimeStepButtonOnAGroup(groupName, CALCULATED);
        addSlotPocketCycleTimeStepToAGroup(addCycleTimeStepModal, SLOT_POCKET_MILLING, cycleTimeStep3, slotType,
                "********-20", sixty, thirty, ten);
        stepDetailsPage.clickAddCycleTimeStepButtonOnAGroup(groupName, CALCULATED);
        addSlotPocketCycleTimeStepToAGroup(addCycleTimeStepModal, SLOT_POCKET_MILLING, cycleTimeStep4, slotType,
                "********-20", ten, twenty, ten);

        CycleTimeStepsPage cycleTimeStepsPage = stepDetailsPage.clickOnCycleTimeStepByName(cycleTimeStep1);
        assertExpectedTimeInCycleTimeStepDetails(cycleTimeStepsPage, "4.83");
        cycleTimeStepsPage.clickBackToPreviousButton(StepDetailsPage.class);
        stepDetailsPage.clickOnCycleTimeStepByName(cycleTimeStep2);
        assertExpectedTimeInCycleTimeStepDetails(cycleTimeStepsPage, "15.80");
        cycleTimeStepsPage.clickBackToPreviousButton(StepDetailsPage.class);
        stepDetailsPage.clickOnCycleTimeStepByName(cycleTimeStep3);
        assertExpectedTimeInCycleTimeStepDetails(cycleTimeStepsPage, "5.67");
        cycleTimeStepsPage.clickBackToPreviousButton(StepDetailsPage.class);
        stepDetailsPage.clickOnCycleTimeStepByName(cycleTimeStep4);
        assertExpectedTimeInCycleTimeStepDetails(cycleTimeStepsPage, "1.87");

        saveAndDeleteCalculationThenLogout(calculationPage);
    }

    @Test
    @Description("Add Slot/Pocket milling bilaterally closed cycle time step")
    @Severity(SeverityLevel.CRITICAL)
    @TmsLink("QA-3967")
    public void addSlotPocketMillingBilaterallyClosedCycleTimeStep() {
        String partName = RandomUtil.getWithoutCostModulePartName();

        CalculationPage calculationPage = createCalculationWithoutCostModule(getProjectDashboardPage(),
                LOCATION, EURO, partName, RandomUtil.getPartNumber(), Dimensions.AREA);

        addStepToCalculation(calculationPage);

        StepDetailsPage stepDetailsPage = calculationPage.clickManufacturingTabButton()
                .clickOnStepByName(STEP_DESIGNATION)
                .scrollToCycleTimeCard();

        AddCycleTimeStepModal addCycleTimeStepModal = stepDetailsPage.clickAddCycleTimeGroupButton()
                .enterDesignation(groupName)
                .chooseGroupType(MACHINING)
                .chooseMaterial("Other")
                .chooseMaterialGroup(materialGroup)
                .clickAddCycleTimeStepForCalculatedToGroupButton();
        ToastMessageDao toastMessageDao = ToastMessage.getLastToastMessage(getDriver());
        softAssert.assertThat(toastMessageDao.getTitle())
                .as("Cycle time step group successful toast message title didn't show up correctly!")
                .isEqualTo("Cycle time step group added successfully");
        softAssert.assertThat(toastMessageDao.getMessage())
                .as("Cycle time step group successful toast message must appear and contain text!")
                .isEqualTo("Cycle time step group " + groupName + " added to " + STEP_DESIGNATION);
        softAssert.assertAll();
        ToastMessage.closeToastsWithEscKey(getDriver());

        addSlotPocketCycleTimeStepToAGroup(addCycleTimeStepModal, SLOT_POCKET_MILLING, cycleTimeStep1, slotTypeBi,
                "F4041.T36.040.Z03.13-LNGX130", ten, forty, twenty);
        stepDetailsPage.clickAddCycleTimeStepButtonOnAGroup(groupName, CALCULATED);
        addSlotPocketCycleTimeStepToAGroup(addCycleTimeStepModal, SLOT_POCKET_MILLING, cycleTimeStep2, slotTypeBi,
                "F4041.B27.080.Z07.13-LNGX130", twenty, eighty, twenty);
        stepDetailsPage.clickAddCycleTimeStepButtonOnAGroup(groupName, CALCULATED);
        addSlotPocketCycleTimeStepToAGroup(addCycleTimeStepModal, SLOT_POCKET_MILLING, cycleTimeStep3, slotTypeBi,
                EXPECTED_TOOL_FOR_SHOULDER_MILLING_CIRCULAR_OUTER, fifteen, fifty, ten);
        stepDetailsPage.clickAddCycleTimeStepButtonOnAGroup(groupName, CALCULATED);
        addSlotPocketCycleTimeStepToAGroup(addCycleTimeStepModal, SLOT_POCKET_MILLING, cycleTimeStep4, slotTypeBi,
                EXPECTED_TOOL_FOR_SHOULDER_MILLING_CIRCULAR_OUTER, ten, sixty, ten);
        stepDetailsPage.clickAddCycleTimeStepButtonOnAGroup(groupName, CALCULATED);
        addSlotPocketCycleTimeStepToAGroup(addCycleTimeStepModal, SLOT_POCKET_MILLING, cycleTimeStep5, slotTypeBi,
                "********-20", fifteen, twenty, twenty);

        CycleTimeStepsPage cycleTimeStepsPage = stepDetailsPage.clickOnCycleTimeStepByName(cycleTimeStep1);
        assertExpectedTimeInCycleTimeStepDetails(cycleTimeStepsPage, "5.86");
        stepDetailsPage = cycleTimeStepsPage.clickBackToPreviousButton(StepDetailsPage.class);
        stepDetailsPage.clickOnCycleTimeStepByName(cycleTimeStep2);
        assertExpectedTimeInCycleTimeStepDetails(cycleTimeStepsPage, "7.22");
        stepDetailsPage = cycleTimeStepsPage.clickBackToPreviousButton(StepDetailsPage.class);
        stepDetailsPage.clickOnCycleTimeStepByName(cycleTimeStep3);
        assertExpectedTimeInCycleTimeStepDetails(cycleTimeStepsPage, "3.27");
        stepDetailsPage = cycleTimeStepsPage.clickBackToPreviousButton(StepDetailsPage.class);
        stepDetailsPage.clickOnCycleTimeStepByName(cycleTimeStep4);
        assertExpectedTimeInCycleTimeStepDetails(cycleTimeStepsPage, "9.01");
        stepDetailsPage = cycleTimeStepsPage.clickBackToPreviousButton(StepDetailsPage.class);
        stepDetailsPage.clickOnCycleTimeStepByName(cycleTimeStep5);
        assertExpectedTimeInCycleTimeStepDetails(cycleTimeStepsPage, "3.60");

        saveAndDeleteCalculationThenLogout(calculationPage);
    }

    @Test
    @Description("Add Cutting cycle time step")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-4359")
    public void addCuttingCycleTimeStep() {
        String partName = RandomUtil.getWithoutCostModulePartName();
        String designation = "Cutting";
        String expectedTime = "68.33";

        CalculationPage calculationPage = createCalculationWithoutCostModule(getProjectDashboardPage(),
                LOCATION, EURO, partName, RandomUtil.getPartNumber(), Dimensions.AREA);

        getProjectDashboardPage().getGlobalHeader().selectDisplayCurrency(Currencies.EURO);
        addStepToCalculation(calculationPage);

        ManufacturingTab manufacturingTab = calculationPage.clickManufacturingTabButton();

        StepDetailsPage stepDetailsPage = manufacturingTab.clickOnStepByName(STEP_DESIGNATION)
                .scrollToCycleTimeCard();

        AddCycleTimeStepModal addCycleTimeStepModal = stepDetailsPage.clickAddCycleTimeStepButtonOnAGroup(defaultStepGroup, CALCULATED);
        assertThat(addCycleTimeStepModal.isSearchInputVisible())
                .as("Search Input should be visible")
                .isTrue();
        addCycleTimeStepModal.findCycleTimeStepByDesignation(designation)
                .clickOnCycleTimeStep(CUTTING)
                .enterLengthToCut("1000")
                .enterPiercingTime(sixty)
                .enterCuttingSpeed(oneTwenty);
        addCycleTimeStepModal.clickAddCycleTimeStepButton();
        assertAndCloseTooltipAfterAddingAStepOrGroup(cycleTimeStepAddedSuccessfully, cycleTimeStepText, designation, defaultStepGroup);
        CycleTimeStepsPage cycleTimeStepsPage = stepDetailsPage.clickOnCycleTimeStepByName(designation);
        assertExpectedTimeInCycleTimeStepDetails(cycleTimeStepsPage, expectedTime);

        saveAndDeleteCalculationThenLogout(calculationPage);
    }

    @Test
    @Description("Add more cycle times using the Add cycle step button in the modal")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-4598")
    public void addAdditionalCycleTimeStepsUsingButtonInModal() {
        String partName = RandomUtil.getWithoutCostModulePartName();
        String drillingCycleDesignation = "Drilling";
        String filterOption = "Grinding";
        String turningCycleDesignation = "External plunge turning";
        String grindingCycleDesignation = "Feedthrough grinding";

        CalculationPage calculationPage = createCalculationWithoutCostModule(getProjectDashboardPage(),
                LOCATION, EURO, partName, RandomUtil.getPartNumber(), Dimensions.AREA);

        getProjectDashboardPage().getGlobalHeader().selectDisplayCurrency(Currencies.EURO);
        addStepToCalculation(calculationPage);

        ManufacturingTab manufacturingTab = calculationPage.clickManufacturingTabButton();

        StepDetailsPage stepDetailsPage = manufacturingTab.clickOnStepByName(STEP_DESIGNATION);
        AddCycleTimeStepModal addCycleTimeStepModal = stepDetailsPage.clickAddCycleTimeGroupButton()
                .enterDesignation(groupName)
                .chooseGroupType(MACHINING)
                .chooseMaterial("Other")
                .chooseMaterialGroup(materialGroup)
                .clickAddCycleTimeStepForCalculatedToGroupButton();

        ToastMessageDao toastMessageDao = ToastMessage.getLastToastMessage(getDriver());
        softAssert.assertThat(toastMessageDao.getTitle())
                .as("Cycle time step group successful toast message title didn't show up correctly!")
                .isEqualTo("Cycle time step group added successfully");
        softAssert.assertThat(toastMessageDao.getMessage())
                .as("Cycle time step group successful toast message must appear and contain text!")
                .isEqualTo("Cycle time step group " + groupName + " added to " + STEP_DESIGNATION);
        softAssert.assertAll();

        ToastMessage.closeToastsWithEscKey(getDriver());

        addCycleTimeStepModal.clickOnCycleTimeStep(DRILLING)
                .enterCycleTimeStepDesignation(drillingCycleDesignation)
                .enterLength(oneTwenty)
                .enterDiameter(thirty)
                .clickAddAdditionalCycleTimeStepButton();
        assertAndCloseTooltipAfterAddingAStepOrGroup(cycleTimeStepAddedSuccessfully, cycleTimeStepText, drillingCycleDesignation, groupName);
        addCycleTimeStepModal.clickOnCycleTimeStep(SLOT_POCKET_MILLING)
                .enterLength(sixty)
                .enterWidth(twenty)
                .enterDepth(ten);
        addCycleTimeStepModal.clickAddAdditionalCycleTimeStepButton();
        assertAndCloseTooltipAfterAddingAStepOrGroup(cycleTimeStepAddedSuccessfully, cycleTimeStepText, cycleTimeName, groupName);
        addCycleTimeStepModal.clickOnCycleTimeStep(EXTERNAL_PLUNGE_TURNING)
                .enterCycleTimeStepDesignation(turningCycleDesignation)
                .enterMinimumDiameter(twenty)
                .enterMaximumDiameter(forty)
                .enterHorizontalLength(hundred);
        addCycleTimeStepModal.clickAddAdditionalCycleTimeStepButton();
        assertAndCloseTooltipAfterAddingAStepOrGroup(cycleTimeStepAddedSuccessfully, cycleTimeStepText, turningCycleDesignation, groupName);
        addCycleTimeStepModal.filterCycleTimeStep(filterOption)
                .clickOnCycleTimeStep(FEED_THROUGH_GRINDING)
                .enterCycleTimeStepDesignation(grindingCycleDesignation)
                .enterGrindingWheelDiameter(thirty)
                .enterGrindingWheelWidth(sixty)
                .enterWorkPieceDiameter(ten)
                .enterWorkPieceLength(hundred)
                .enterWorkPieceRotationalSpeed(ten)
                .enterRegulatingWheelAngle(ten);
        addCycleTimeStepModal.clickAddCycleTimeStepButton();
        assertAndCloseTooltipAfterAddingAStepOrGroup(cycleTimeStepAddedSuccessfully, cycleTimeStepText, grindingCycleDesignation, groupName);

        saveAndDeleteCalculationThenLogout(calculationPage);
    }

    @Test
    @Description("Move cycle time step to different group")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-426")
    public void moveCycleTimeStepToDifferentGroupTest() {
        CalculationWithCost dcaCalculation = constructDCA();
        createCalculationWithCostModuleViaAPI(dcaCalculation);
        String partName = dcaCalculation.getPartName();

        CalculationPage calculationPage = getProjectDashboardPage().getBomExplorerPanel()
                .clickOnBomNodeByPartName(partName);

        ManufacturingTab manufacturingTab = calculationPage.clickManufacturingTabButton();

        StepDetailsPage stepDetailsPage = manufacturingTab.clickOnStepByName(stepName);

        assertThat(stepDetailsPage.isCycleTimeSettingsTableVisible())
                .as("Cycle time settings table must be visible!")
                .isTrue();

        stepDetailsPage.clickAddCycleTimeGroupButton()
                .enterDesignation(groupName)
                .chooseGroupType(MACHINING)
                .chooseMaterial("Other")
                .chooseMaterialGroup(materialGroup)
                .clickAddCycleTimeStepGroupButton();
        assertAndCloseTooltipAfterAddingAStepOrGroup(cycleTimeStepGroupAddedSuccessfully, cycleTimeStepGroupText, groupName, stepName);
        AddCycleTimeStepModal addCycleTimeStepModal = stepDetailsPage.clickAddCycleTimeStepButtonOnAGroup(groupName, DIRECT_INPUT);
        addCycleTimeStepModal.enterCycleTimeStepDesignation(roughCycleDesignation)
                .enterTimeValue(oneTwenty)
                .clickAddCycleTimeStepButton();

        int positionBeforeMove = stepDetailsPage.getPositionInCTSTable(roughCycleDesignation);
        stepDetailsPage.moveCycleTimeStepToAGroup(roughCycleDesignation, defaultStepGroup);

        assertThat(stepDetailsPage.getPositionInCTSTable(roughCycleDesignation))
                .as("Cycle tyne step should have been moved to a different group!")
                .isLessThan(positionBeforeMove);

        saveAndDeleteCalculationThenLogout(calculationPage);
    }

    @Test
    @Description("Add additional cycle time steps using Add&create button in modal for direct input")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-5228")
    public void addAdditionalCycleTimeStepsUsingAddAndCreateButtonForDirectInputTest() {
        CalculationWithCost dcaCalculation = constructDCA();
        createCalculationWithCostModuleViaAPI(dcaCalculation);
        String partName = dcaCalculation.getPartName();

        CalculationPage calculationPage = getProjectDashboardPage().getBomExplorerPanel()
                .clickOnBomNodeByPartName(partName);

        ManufacturingTab manufacturingTab = calculationPage.clickManufacturingTabButton();

        StepDetailsPage stepDetailsPage = manufacturingTab.clickOnStepByName(stepName);
        assertThat(stepDetailsPage.isCycleTimeSettingsTableVisible())
                .as("Cycle time settings table must be visible!")
                .isTrue();
        AddCycleTimeStepModal addCycleTimeStepModal = stepDetailsPage.clickAddCycleTimeStepButtonOnAGroup(defaultStepGroup, DIRECT_INPUT);
        addCycleTimeStepModal.enterCycleTimeStepDesignation(roughCycleDesignation)
                .enterTimeValue(oneTwenty)
                .clickAddAdditionalCycleTimeStepButton();
        assertAndCloseTooltipAfterAddingAStepOrGroup(cycleTimeStepAddedSuccessfully, cycleTimeStepText, roughCycleDesignation, defaultStepGroup);
        addCycleTimeStepModal.enterCycleTimeStepDesignation(roughCycleDesignation1)
                .enterTimeValue(hundred)
                .clickAddAdditionalCycleTimeStepButton();
        assertAndCloseTooltipAfterAddingAStepOrGroup(cycleTimeStepAddedSuccessfully, cycleTimeStepText, roughCycleDesignation1, defaultStepGroup);
        addCycleTimeStepModal.enterCycleTimeStepDesignation(roughCycleDesignation2)
                .enterTimeValue(thirty)
                .clickAddCycleTimeStepButton();
        assertAndCloseTooltipAfterAddingAStepOrGroup(cycleTimeStepAddedSuccessfully, cycleTimeStepText, roughCycleDesignation2, defaultStepGroup);

        saveAndDeleteCalculationThenLogout(calculationPage);
    }

    @Test
    @Description("Enter data to calculate diameter and time for Cycle time step")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-2745")
    public void enterDataToCalculateDiameterAndTimeForCycleTimeStepTest() {
        final String stepDesignation = "Chiron milling machines";
        final String stepConfiguration = "Chiron FZ08S - 1-Spindle - 4 Axis - Max RPM 15000 - Max Torque 18Nm - Fixed Table";
        final String groupDesignation = "Test designation";
        final String cycleTimeStepDesignation = "Test Cycle Time Step Designation";
        final String cycleTimeStepModifiedLength = "8.00";

        CalculationWithCost calculationDCA = constructDCA();
        CalculationPage calculationPage = createCalculationWithCostModuleViaAPI(calculationDCA);

        BomExplorerPanel bomExplorerPanel = getProjectDashboardPage().getBomExplorerPanel();

        bomExplorerPanel.openContextMenuOnBomNodeByPartName(calculationDCA.getPartName())
                .clickOnAddManufacturingStepBomButton();

        AddStepModal addStepModal = new AddStepModal(getDriver());
        addStepModal.addMasterDataStep(stepDesignation, stepConfiguration);

        bomExplorerPanel.expandBomNodeByPartName(calculationDCA.getPartName())
                .clickOnBomEntryByName(stepDesignation);

        StepDetailsPage stepDetailsPage = new StepDetailsPage(getDriver());
        stepDetailsPage.scrollToCycleTimeCard();

        AddCycleTimeStepGroupModal addCycleTimeStepGroupModal = stepDetailsPage.clickAddCycleTimeGroupButton();

        addCycleTimeStepGroupModal.enterGroupDesignation(groupDesignation)
                .chooseGrouping(SEQUENTIAL)
                .chooseGroupType(CycleTimeStepGroupType.MACHINING)
                .chooseMaterial("Other")
                .chooseMaterialGroup("Non-alloyed steel, C <= 0.25%, Annealed")
                .clickAddCycleTimeStepGroupButton();

        AddCycleTimeStepModal addCycleTimeStepModal = stepDetailsPage.clickAddCycleTimeStepButtonOnAGroup(groupDesignation, CALCULATED);

        addCycleTimeStepModal.filterCycleTimeStep("Milling")
                .findCycleTimeStepByDesignation("Face Milling")
                .clickOnCycleTimeStep(FACE_MILLING)
                .enterCycleTimeStepDesignation(cycleTimeStepDesignation)
                .enterLength("6")
                .enterWidth("4")
                .enterDepth("1");

        validateAddCycleTimeStepModalFields(addCycleTimeStepModal);

        addCycleTimeStepModal.clickAddCycleTimeStepButton();

        CycleTimeStepsPage cycleTimeStepsPage = stepDetailsPage.clickOnCycleTimeStepByName(cycleTimeStepDesignation);
        cycleTimeStepsPage.enterLength(cycleTimeStepModifiedLength);
        cycleTimeStepsPage.clickBackToButton();

        assertThat(stepDetailsPage.isCycleTimeStepUnlinkIconVisible(cycleTimeStepDesignation))
                .as("Unlink icon must be visible next to the modified Cycle time step")
                .isTrue();

        stepDetailsPage.clickOnCycleTimeStepByName(cycleTimeStepDesignation);

        softAssert.assertThat(cycleTimeStepsPage.getLengthValue())
                .as("Length must have been updated wirth value: " + cycleTimeStepModifiedLength)
                .isEqualTo(cycleTimeStepModifiedLength);
        softAssert.assertThat(cycleTimeStepsPage.isLengthUnlinkIconVisible())
                .as("Unlink icon must be visible next to the modified Length field")
                .isTrue();
        softAssert.assertAll();

        stepDetailsPage = cycleTimeStepsPage.clickBackToButton();

        stepDetailsPage.selectCycleTimeUnit(MINUTE.getUnitSymbol());

        assertThat(stepDetailsPage.getDetailedCycleTimeUnit())
                .as("Cycle time unit has to be recalculated after change!")
                .contains(MINUTE.getUnitSymbol());

        stepDetailsPage.selectCycleTimeUnit(HOUR.getUnitSymbol());

        assertThat(stepDetailsPage.getDetailedCycleTimeUnit())
                .as("Cycle time unit has to be recalculated after change!")
                .contains(HOUR.getUnitSymbol());

        stepDetailsPage.selectCycleTimeUnit(STROKES_PER_SECOND.getUnitSymbol());

        assertThat(stepDetailsPage.getDetailedCycleTimeUnit())
                .as("Cycle time unit has to be recalculated after change!")
                .contains(STROKES_PER_SECOND.getUnitSymbol());

        stepDetailsPage.selectCycleTimeUnit(STROKES_PER_MINUTE.getUnitSymbol());

        assertThat(stepDetailsPage.getDetailedCycleTimeUnit())
                .as("Cycle time unit has to be recalculated after change!")
                .contains(STROKES_PER_MINUTE.getUnitSymbol());

        stepDetailsPage.selectCycleTimeUnit(STROKES_PER_HOUR.getUnitSymbol());

        assertThat(stepDetailsPage.getDetailedCycleTimeUnit())
                .as("Cycle time unit has to be recalculated after change!")
                .contains(STROKES_PER_HOUR.getUnitSymbol());

        saveAndDeleteCalculationThenLogout(calculationPage);
    }

    @Test
    @Description("Create new machining group inside cycle time step from Manufacturing tab")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-2706")
    public void createNewMachiningGroupInsideCycleTimeStepFromManufacturingTabTest() {
        String groupDesignation = "Test Machining Group";
        String cycleTimeStepDesignation = "Drilling 123";
        String threadingPitchType = "M1.8 x 0.35";

        CalculationWithCost dcaCalculation = constructDCA();
        CalculationPage calculationPage = createCalculationWithCostModuleViaAPI(dcaCalculation);

        StepDetailsPage stepDetailsPage = calculationPage.clickManufacturingTabButton()
                .clickOnStepByName(stepName);

        stepDetailsPage.clickAddCycleTimeGroupButton()
                .enterDesignation(groupDesignation)
                .chooseGrouping(SEQUENTIAL)
                .chooseGroupType(MACHINING)
                .chooseMaterial("Other")
                .chooseMaterialGroup("Non-alloyed steel, C <= 0.25%, Annealed")
                .clickAddCycleTimeStepGroupButton();

        verifyCycleTimeStepGroupCreationToastMessage(groupDesignation, stepName, getDriver());

        softAssert.assertThat(stepDetailsPage.hasStepObject(groupDesignation))
                .as("Cycle time group should be visible on manufacturing step details page!")
                .isTrue();
        softAssert.assertThat(calculationPage.isSaveButtonContainingNotificationIconBeforeSave())
                .as("Save button must be enabled after adding new Cycle time step group!")
                .isTrue();
        softAssert.assertAll();

        stepDetailsPage.clickAddCycleTimeStepButtonOnAGroup(groupDesignation, CALCULATED)
                .clickOnCycleTimeStep(THREAD_TAPPING)
                .enterCycleTimeStepDesignation(cycleTimeStepDesignation)
                .enterOperations("2")
                .enterToolMoveTimeOperation("1")
                .enterThreadDepth("1")
                .clickAddCycleTimeStepButton();

        assertAndCloseTooltipAfterAddingAStepOrGroup(
            cycleTimeStepAddedSuccessfully, cycleTimeStepText, cycleTimeStepDesignation, groupDesignation);

        assertThat(stepDetailsPage.hasStepObject(cycleTimeStepDesignation))
                .as("A new step must be added to the group!")
                .isTrue();

        checkThatCostAndCo2TotalsAreNotBroken(calculationPage);

        CycleTimeStepsPage cycleTimeStepsPage = stepDetailsPage.clickOnCycleTimeStepByName(cycleTimeStepDesignation);

        cycleTimeStepsPage.chooseThreadingPitchType(threadingPitchType);

        softAssert.assertThat(cycleTimeStepsPage.isThreadingPitchTypeUnlinkIconVisible())
                .as("Threading pitch type unlink icon must be visible after modifying it's value!")
                .isTrue();
        softAssert.assertThat(cycleTimeStepsPage.getThreadingPitchType())
                .as("Threading pitch type must be equal to the new modified value: " + threadingPitchType)
                .isEqualTo(threadingPitchType);
        softAssert.assertThat(calculationPage.isSaveButtonContainingNotificationIconBeforeSave())
                .as("Save button must be enabled after modifying Threading pitch type!")
                .isTrue();
        softAssert.assertAll();

        checkThatCostAndCo2TotalsAreNotBroken(calculationPage);

        saveAndDeleteCalculationThenLogout(calculationPage);
    }

    @Step("Validate Cycle Time Step modal fields are correctly filled")
    private void validateAddCycleTimeStepModalFields(AddCycleTimeStepModal addCycleTimeStepModal) {
        softAssert.assertThat(addCycleTimeStepModal.getTimeFieldText().replace(SECOND.getUnitSymbol(), ""))
                .as("Actual time value does not match the expected Time value")
                .isNotBlank();
        softAssert.assertThat(addCycleTimeStepModal.getRotationalSpeedValue().replace("1" + PER_MINUTE, ""))
                .as("Actual rotational speed value does not match the expected Rotational speed value")
                .isNotBlank();
        softAssert.assertThat(addCycleTimeStepModal.getFeedRateValue().replace(PER_MINUTE.getUnitSymbol(), ""))
                .as("Actual feed rate value does not match the expected Feed rate value")
                .isNotBlank();
        softAssert.assertThat(addCycleTimeStepModal.getSelectedTool())
                .as("Actual selected tool does not match the expected selected tool")
                .isEqualTo("********-5");
        softAssert.assertThat(addCycleTimeStepModal.getToolDiameterValue())
                .as("Actual tool diameter value does not match the expected Tool diameter value")
                .isNotBlank();
        softAssert.assertThat(addCycleTimeStepModal.getFeedRatePerToothValue())
                .as("Actual feed rate per tooth value does not match the expected Feed rate per tooth value")
                .isNotBlank();
        softAssert.assertThat(addCycleTimeStepModal.getFeedRatePerToothUnit())
                .as("Actual feed rate per tooth unit does not match the expected Feed rate per tooth unit")
                .isEqualTo(MILLIMETER.getUnitSymbol());
        softAssert.assertThat(addCycleTimeStepModal.getNumberOfTeethValue())
                .as("Actual number of teeth value does not match the expected Number of teeth value")
                .isNotBlank();
        softAssert.assertThat(addCycleTimeStepModal.getMaxCuttingDepthValue())
                .as("Actual maximum cutting depth value does not match the expected Maximum cutting depth value")
                .isNotBlank();
        softAssert.assertThat(addCycleTimeStepModal.getMaxCuttingDepthUnit())
                .as("Actual maximum cutting depth unit does not match the expected Maximum cutting depth unit")
                .isEqualTo(MILLIMETER.getUnitSymbol());
        softAssert.assertThat(addCycleTimeStepModal.getCuttingWidthFactorValue())
                .as("Actual cutting width factor value does not match the expected Cutting width factor value")
                .isNotBlank();
        softAssert.assertThat(addCycleTimeStepModal.getCuttingWidthFactorUnit())
                .as("Actual cutting width factor unit does not match the expected Cutting width factor unit")
                .isEqualTo(PERCENT.getUnitSymbol());
        softAssert.assertThat(addCycleTimeStepModal.getCuttingSpeedValue())
                .as("Actual cutting speed value does not match the expected Cutting speed value")
                .isNotBlank();
        softAssert.assertThat(addCycleTimeStepModal.getCuttingSpeedUnit())
                .as("Actual cutting speed unit does not match the expected Cutting speed unit")
                .isEqualTo(METER_PER_MINUTE.getUnitSymbol());
        softAssert.assertThat(addCycleTimeStepModal.getToolMoveTimeInitialPositionValue())
                .as("Actual tool move time initial position value does not match the expected Tool move time initial position value")
                .isNotBlank();
        softAssert.assertThat(addCycleTimeStepModal.getToolMoveTimeInitialPositionUnit())
                .as("Actual tool move time initial position unit does not match the expected Tool move time initial position unit")
                .isEqualTo(SECOND.getUnitSymbol());
        softAssert.assertThat(addCycleTimeStepModal.getOperationsValue())
                .as("Actual operations value does not match the expected Operations value")
                .isNotBlank();
        softAssert.assertThat(addCycleTimeStepModal.getTorqueValue().replace("Nm", ""))
                .as("Actual Torque value does not match the expected Torque value")
                .isNotBlank();
        softAssert.assertAll();
    }

    @Step("Assert and close tooltip after adding a cycle time step or group")
    private void assertAndCloseTooltipAfterAddingAStepOrGroup(String tooltipTitle, String tooltipMessage, String object, String targetName) {
        SoftAssertions softAssert = new SoftAssertions();
        ToastMessageDao toastMessageDao = ToastMessage.getLastToastMessage(getDriver());
        softAssert.assertThat(toastMessageDao.getTitle())
                .as("Toast message title didn't show up correctly!")
                .contains(tooltipTitle);
        if (object == null) {
            softAssert.assertThat(toastMessageDao.getMessage())
                    .as("Toast message must appear and contain text!")
                    .isEqualTo(tooltipMessage);
        } else {
            softAssert.assertThat(toastMessageDao.getMessage())
                    .as("Cycle time step successful toast message must appear and contain text!")
                    .isEqualTo(tooltipMessage + object + " added to " + targetName);
        }
        softAssert.assertAll();
        ToastMessage.closeToastsWithEscKey(getDriver());
    }

    @Step("Assert and verify validation error message")
    private void assertValidationErrorMessage(AddCycleTimeStepModal addCycleTimeStepModal, List<String> missingFields) {
        SoftAssertions softAssert = new SoftAssertions();
        String formError = addCycleTimeStepModal.getFormErrorText();
        softAssert.assertThat(formError)
                .as("Form error must appear in the footer of the modal!")
                .isEqualTo("Please fill out " + missingFields.size() + " required fields that are currently empty");
        softAssert.assertThat(addCycleTimeStepModal.getFieldsWithError())
                .as("Required fields should have field errors!")
                .isEqualTo(missingFields);
        softAssert.assertAll();
    }

    @Step("Add Slot pocket cycle time step")
    private void addSlotPocketCycleTimeStepToAGroup(AddCycleTimeStepModal addCycleTimeStepModal, CycleTimeStepTypes cycleTimeStepType,
                                                    String designation, String slotType, String expectedTool, String length,
                                                    String width, String depth) {
        addCycleTimeStepModal.clickOnCycleTimeStep(cycleTimeStepType)
                .enterCycleTimeStepDesignation(designation)
                .chooseSlotType(slotType)
                .enterLength(length)
                .enterWidth(width)
                .enterDepth(depth);

        assertSelectedToolDataAndFinishAddingStep(addCycleTimeStepModal, expectedTool);
    }

    @Step("Add Shoulder milling cycle time step")
    private void addShoulderMillingCycleTimeStepToAGroup(AddCycleTimeStepModal addCycleTimeStepModal, CycleTimeStepTypes cycleTimeStepTypes,
                                                         String outerDiameter, String innerDiameter, String depth,
                                                         String expectedTool) {
        addCycleTimeStepModal.clickOnCycleTimeStep(cycleTimeStepTypes)
                .enterOuterDiameter(outerDiameter)
                .enterInnerDiameter(innerDiameter)
                .enterDepth(depth);

        assertSelectedToolDataAndFinishAddingStep(addCycleTimeStepModal, expectedTool);
    }

    @Step("Assert selected tool data and finish adding the step")
    private void assertSelectedToolDataAndFinishAddingStep(AddCycleTimeStepModal addCycleTimeStepModal, String expectedTool) {

        addCycleTimeStepModal.clickAddCycleTimeStepButton();
    }

    @Step("Assert Expected time in cycle time step details")
    private void assertExpectedTimeInCycleTimeStepDetails(CycleTimeStepsPage cycleTimeStepsPage, String expectedTime) {
        assertThat(cycleTimeStepsPage.getCycleTimeValue())
                .as("Expected cycle time value is incorrect!")
                .isEqualTo(expectedTime);
    }

    @Step("Assert Expected Tool And Updated Time")
    private void assertExpectedToolAndUpdatedTime(CycleTimeStepsPage cycleTimeStepsPage, String expectedTool, String updatedTime) {
        softAssert.assertThat(cycleTimeStepsPage.getSelectedTool())
                .as("Selected tool is not what expected!")
                .startsWith(expectedTool);
        softAssert.assertThat(cycleTimeStepsPage.getCycleTimeValue())
                .as("Time value is incorrect!")
                .isEqualTo(updatedTime);
        softAssert.assertAll();
    }

    @Step("Verify unlinked icons for modified values in add cycle sep modal")
    private void verifyUnlinkedIconsForModifiedValuesInAddCycleSepModal(AddCycleTimeStepModal addCycleTimeStepModal) {
        softAssert.assertThat(addCycleTimeStepModal.isToolUnlinkIconVisible())
                .as("Broken chain icon should be visible after modifications of the selected tool")
                .isTrue();
        softAssert.assertThat(addCycleTimeStepModal.isToolDiameterUnlinkIconVisible())
                .as("Broken chain icon should be visible after modifications of tool diameter value")
                .isTrue();
        softAssert.assertThat(addCycleTimeStepModal.isTeethNumUnlinkIconVisible())
                .as("Broken chain icon should be visible after modifications of teeth number value")
                .isTrue();
        softAssert.assertThat(addCycleTimeStepModal.isCuttingDepthUnlinkIconVisible())
                .as("Broken chain icon should be visible after modifications of max cutting depth value")
                .isTrue();
        softAssert.assertThat(addCycleTimeStepModal.isCuttingSpeedUnlinkIconVisible())
                .as("Broken chain icon should be visible after modifications of cutting speed value")
                .isTrue();
        softAssert.assertThat(addCycleTimeStepModal.isFeedRateUnlinkIconVisible())
                .as("Broken chain icon should be visible after modifications of feed rate value")
                .isTrue();
        softAssert.assertThat(addCycleTimeStepModal.isCuttingWidthFactorUnlinkIconVisible())
                .as("Broken chain icon should not be visible after modification for cutting width factor")
                .isFalse();
        softAssert.assertThat(addCycleTimeStepModal.isToolInitialUnlinkIconVisible())
                .as("Broken chain icon should be visible after modification for tool initial value")
                .isFalse();
        softAssert.assertThat(addCycleTimeStepModal.isToolOperationUnlinkIconVisible())
                .as("Broken chain icon should be visible after modification for tool between operation value")
                .isFalse();
        softAssert.assertThat(addCycleTimeStepModal.isOperationUnlinkIconVisible())
                .as("Broken chain icon should be visible after modification for operation value")
                .isFalse();
        softAssert.assertAll();
    }
}
