package com.nu.qa.ui.tests.functional.calculation.history;

import com.nu.qa.ui.data.builders.calculation.CalculationWithCost;
import com.nu.qa.ui.data.enums.UsersList;
import com.nu.qa.ui.framework.CalculationBaseTest;
import com.nu.qa.ui.pageobjects.CalculationPage;
import com.nu.qa.ui.pageobjects.sidePanel.HistorySidePanel;
import com.nu.qa.ui.pageobjects.tabs.ParametersTab;
import com.nu.qa.ui.pageobjects.tabs.PartTab;
import com.nu.qa.ui.pageobjects.tabs.manufacturing.MachineDetailsPage;
import com.nu.qa.ui.pageobjects.tabs.manufacturing.StepDetailsPage;
import com.nu.qa.utilities.data.enums.units.Dimensions;
import com.nu.qa.utilities.utilities.RandomUtil;
import com.nu.qa.utilities.utilities.ValueUtil;
import io.qameta.allure.Description;
import io.qameta.allure.Severity;
import io.qameta.allure.SeverityLevel;
import io.qameta.allure.TmsLink;
import org.apache.commons.lang3.StringUtils;
import org.testng.annotations.Test;

import static com.nu.qa.ui.data.builders.calculation.CalculationWithCostDirector.constructCHILL;
import static com.nu.qa.utilities.data.enums.Currencies.*;
import static com.nu.qa.utilities.data.enums.units.Units.*;
import static com.nu.qa.utilities.utilities.RandomUtil.getWithoutCostModulePartName;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

public class HistoryTests extends CalculationBaseTest {

    @Test
    @Description("See changes in history")
    @Severity(SeverityLevel.CRITICAL)
    @TmsLink("QA-97")
    public void seeChangesInHistoryTest() {
        String newLocation = "Germany";
        String partName = getWithoutCostModulePartName();
        String subCalcTitle = "SubCalcTitle";
        String subCalcPartName = getWithoutCostModulePartName();

        CalculationPage calculationPage =
            createCalculationWithoutCostModule(getProjectDashboardPage(), LOCATION, EURO, partName, RandomUtil.getPartNumber(),
                Dimensions.PIECE);

        CalculationPage subCalculationPage = createSubCalculationWithoutCostModule(calculationPage, subCalcTitle, subCalcPartName);

        HistorySidePanel historySidePanel = calculationPage.getBomExplorerPanel()
            .clickOnBomNodeByPartName(partName)
            .openHistorySidePanel();
        assertThat(historySidePanel.getSavedHistoryRecordsNumber())
            .as("History modal must contain three saved record!")
            .isEqualTo(2);
        softAssert.assertThat(historySidePanel.getNthHistoryRecordText(1))
            .as("Expected third entry: Calculation created!")
            .contains("Calculation created");
        softAssert.assertThat(historySidePanel.getNthChildOfNthDetailedHistoryRecordText(0, 1))
            .as("Expected second entry: Step created!")
            .contains(STEP_DESIGNATION);
        softAssert.assertThat(historySidePanel.getNthChildOfNthDetailedHistoryRecordText(0, 0))
            .as("Expected first entry: subCalculation created!")
            .contains(subCalcPartName);
        softAssert.assertAll();

        ParametersTab parametersTab = subCalculationPage.getBomExplorerPanel()
            .expandBomNodeByPartName(partName)
            .clickOnBomNodeByPartName(subCalcPartName)
            .clickParametersTabButton()
            .selectLocation(newLocation);
        assertThat(parametersTab.getLocation())
            .as("The Location must contain the modified value!")
            .isEqualTo(newLocation);

        saveCalculation(subCalculationPage);

        historySidePanel.waitUntilPanelIsVisible();

        softAssert.assertThat(historySidePanel.getSavedHistoryRecordsNumber())
            .as("History modal must contain two saved record!")
            .isEqualTo(2);
        softAssert.assertThat(historySidePanel.getNthDetailedHistoryRecordText(0))
            .as("Expected first entry: Location changed on subCalculation")
            .contains("Location");
        softAssert.assertThat(historySidePanel.getNthDetailedHistoryRecordText(1))
            .as("Expected second entry: Parent calculation created!")
            .contains(partName);
        softAssert.assertAll();

        calculationPage = subCalculationPage.getBomExplorerPanel()
            .clickOnBomNodeByPartName(partName);

        historySidePanel.waitUntilPanelIsVisible();

        softAssert.assertThat(historySidePanel.getSavedHistoryRecordsNumber())
            .as("History modal must contain three saved record!")
            .isEqualTo(3);
        softAssert.assertThat(historySidePanel.getNthHistoryRecordText(2))
            .as("Expected third entry: Calculation created!")
            .contains("Calculation created");
        softAssert.assertThat(historySidePanel.getNthChildOfNthDetailedHistoryRecordText(1, 1))
            .as("Expected second child entry: Step created!")
            .contains(STEP_DESIGNATION);
        softAssert.assertThat(historySidePanel.getNthChildOfNthDetailedHistoryRecordText(1, 0))
            .as("Expected second child entry: subCalculation created!")
            .contains(subCalcPartName);
        softAssert.assertThat(historySidePanel.getNthDetailedHistoryRecordText(0))
            .as("Expected first entry: Changes in subpart!")
            .contains(subCalcPartName);
        softAssert.assertAll();

        deleteCalculationThenLogout(calculationPage);
    }

    @Test()
    @Description("See history field path of changes")
    @Severity(SeverityLevel.CRITICAL)
    @TmsLink("QA-98")
    public void seeFieldPathOfChangesTest() {
        final String newLocation = "Germany";
        final String machineName = "Reis Cast frame (gravity)";
        final String newUtilizationRate = "99.99";
        final String newInvestBase = "555.55";

        CalculationWithCost calculationWithCostChill = constructCHILL();
        CalculationPage calculationPage = createCalculationWithCostModuleViaAPI(calculationWithCostChill);

        getProjectDashboardPage().getGlobalHeader()
            .selectDisplayCurrency(EURO);

        ParametersTab parametersTab = calculationPage.clickParametersTabButton();
        parametersTab.selectLocation(newLocation);

        assertThat(parametersTab.isLocationUnlinkIconVisible())
            .as("The unlink icon must be visible next to Location!")
            .isTrue();
        assertThat(parametersTab.getLocation())
            .as("The Location must contains the modified value!")
            .isEqualTo(newLocation);

        StepDetailsPage stepDetailsPage = calculationPage.clickManufacturingTabButton()
            .clickOnStepByName("Chill casting");

        String originalUtilizationRate = ValueUtil.formatDoubleValuesToUI(ValueUtil.parseDouble(stepDetailsPage.getUtilizationRateValue()), "###,##0.00");

        stepDetailsPage.enterUtilizationRate(newUtilizationRate);

        assertThat(stepDetailsPage.isUtilizationRateUnlinkIconVisible())
            .as("The unlink icon must be visible next to Utilization rate!")
            .isTrue();
        assertThat(stepDetailsPage.getUtilizationRateValue())
            .as("The Utilization rate must contain the modified value!")
            .isEqualTo(newUtilizationRate);

        MachineDetailsPage machineDetailsPage = stepDetailsPage.clickOnMachineByName(machineName);
        String originalInvestBase = machineDetailsPage.getInvestBaseValue();

        machineDetailsPage.enterInvestBase(newInvestBase);

        assertThat(machineDetailsPage.isInvestBaseUnlinkIconVisible())
            .as("The unlink icon must be visible next to Base investment!")
            .isTrue();
        assertThat(machineDetailsPage.getInvestBaseValue())
            .as("The Base investment must contain the modified value!")
            .isEqualTo(newInvestBase);

        HistorySidePanel historySidePanel = calculationPage.openHistorySidePanel();
        softAssert.assertThat(historySidePanel.getSavedHistoryRecordsNumber())
            .as("History modal must contain one already saved record before calculation save!")
            .isEqualTo(1);
        softAssert.assertThat(historySidePanel.getUnsavedHistoryRecordsNumber())
            .as("History modal must contain three unsaved records!")
            .isEqualTo(3);
        softAssert.assertThat(historySidePanel.getNthUnsavedChangeText(0))
            .as("Investment base must be the top unsaved history record!")
            .endsWith(
                StringUtils.joinWith("\n", machineName, "Chill casting / " + machineName, "Base investment", "previous", originalInvestBase,
                    "EUR", "new", newInvestBase, "EUR"));
        softAssert.assertThat(historySidePanel.getNthUnsavedChangeText(1))
            .as("Utilization rate must be the second unsaved history record!")
            .endsWith(StringUtils.joinWith("\n", "Chill casting", "Utilization rate", "previous", originalUtilizationRate, "%", "new",
                newUtilizationRate, "%"));
        softAssert.assertThat(historySidePanel.getNthUnsavedChangeText(2))
            .as("Location change must be the third unsaved history record!")
            .endsWith(StringUtils.joinWith("\n", "Calculation", "Location", "previous", calculationWithCostChill.getLocationForUi(), "new",
                newLocation));
        attachScreenShot("History modal");
        softAssert.assertAll();

        saveCalculation(calculationPage);

        historySidePanel.waitUntilPanelIsVisible();

        softAssert.assertThat(historySidePanel.getSavedHistoryRecordsNumber())
            .as("History modal must contain two saved sections after calculation save!")
            .isEqualTo(2);
        softAssert.assertThat(historySidePanel.getNthChildOfNthDetailedHistoryRecordText(0, 0))
            .as("Investment base must be the top saved history record!")
            .endsWith(
                StringUtils.joinWith("\n", machineName, "Chill casting / " + machineName, "Base investment", "previous", originalInvestBase,
                    "EUR", "new", newInvestBase, "EUR"));
        softAssert.assertThat(historySidePanel.getNthChildOfNthDetailedHistoryRecordText(0, 1))
            .as("Utilization rate must be the second saved history record!")
            .endsWith(StringUtils.joinWith("\n", "Chill casting", "Utilization rate", "previous", originalUtilizationRate, "%", "new",
                newUtilizationRate, "%"));
        softAssert.assertThat(historySidePanel.getNthChildOfNthDetailedHistoryRecordText(0, 2))
            .as("Location change must be the third saved history record!")
            .endsWith(StringUtils.joinWith("\n", "Calculation", "Location", "previous", calculationWithCostChill.getLocationForUi(), "new",
                newLocation));
        attachScreenShot("History modal after save");
        softAssert.assertAll();
        deleteCalculationThenLogout(calculationPage);
    }

    @Test
    @Description("History - Verify increment and decrement with modifications")
    @Severity(SeverityLevel.CRITICAL)
    @TmsLink("QA-1931")
    public void historyIncrementAndDecrementTest() {
        CalculationWithCost calculationWithCostChill = constructCHILL();
        calculationWithCostChill.setPeakValue(1);
        CalculationPage calculationPage = createCalculationWithCostModuleViaAPI(calculationWithCostChill);

        getProjectDashboardPage().getGlobalHeader()
            .selectDisplayCurrency(EURO);

        ParametersTab parametersTab = calculationPage.clickParametersTabButton();
        parametersTab.selectResponsible("role admin");

        calculationPage.clickRenameUnsavedMainVariantButton()
            .enterVariantTitle("Modified title")
            .clickRenameVariantButton();

        HistorySidePanel historySidePanel = calculationPage.openHistorySidePanel();

        softAssert.assertThat(historySidePanel.getSavedHistoryRecordsNumber())
            .as("History modal must contain one already saved record before calculation save!")
            .isEqualTo(1);
        softAssert.assertThat(historySidePanel.getUnsavedHistoryRecordsNumber())
            .as("History modal must contain two unsaved records!")
            .isEqualTo(2);
        softAssert.assertThat(historySidePanel.getNthUnsavedChangeText(0))
            .as("Calculation title change must be the top unsaved history record!")
            .isEqualTo(
                StringUtils.joinWith("\n", "Modified title", "is the new name of", "Main", "Variant renamed"));
        softAssert.assertThat(historySidePanel.isUnsavedChangeCostChangeAvailable(0))
            .as("Calculation title change history entry should not display cost change badge")
            .isFalse();
        softAssert.assertThat(historySidePanel.getNthUnsavedChangeText(1))
            .as("Base currency change must be the second unsaved history record!")
            .endsWith(StringUtils.joinWith("\n", "Calculation", "Responsible", "previous", UsersList.ANTMAN_USER.getUserName(), "new", "role admin"));
        softAssert.assertThat(historySidePanel.isCostUnsavedChangeNeutral(1))
            .as("Base currency change change must not change cost per part!")
            .isTrue();
        softAssert.assertThat(historySidePanel.isCo2UnsavedChangeNeutral(1))
            .as("Base currency change change must not change co2 per part!")
            .isTrue();
        softAssert.assertAll();

        calculationPage.clickDiscardModificationsButton();

        historySidePanel.waitUntilPanelIsVisible();

        softAssert.assertThat(historySidePanel.getSavedHistoryRecordsNumber())
            .as("History modal must contain one already saved record before calculation save!")
            .isEqualTo(1);
        softAssert.assertThat(historySidePanel.getUnsavedHistoryRecordsNumber())
            .as("History modal must contain three unsaved records!")
            .isZero();
        softAssert.assertThat(calculationPage.isSaveButtonContainingCheckIconAfterSave())
            .as("Calculation save button must be disabled after modification reset!")
            .isTrue();
        softAssert.assertAll();

        getProjectDashboardPage().getGlobalHeader()
            .selectDisplayCurrency(EURO);

        parametersTab = calculationPage.clickParametersTabButton();
        parametersTab.enterProductionVolumePerYear("1000000");

        historySidePanel.waitUntilPanelIsVisible();

        softAssert.assertThat(historySidePanel.getSavedHistoryRecordsNumber())
            .as("History modal must contain one already saved record before calculation save!")
            .isEqualTo(1);
        softAssert.assertThat(historySidePanel.getUnsavedHistoryRecordsNumber())
            .as("History modal must contain one unsaved record!")
            .isEqualTo(1);
        softAssert.assertThat(historySidePanel.getNthUnsavedChangeText(0))
            .as("Peak volume per year must be the top unsaved history record!")
            .endsWith(StringUtils.joinWith("\n", "Calculation", "Peak volume per year",
                "previous", "1.00", PIECE_PER_YEAR.getUnitSymbol(),
                "new", "1,000,000.00", PIECES_PER_YEAR.getUnitSymbol()
            ));
        softAssert.assertThat(historySidePanel.isCostUnsavedChangeRecordADecrement(0))
            .as("Peak volume per year must be a cost decrement after its value increased!")
            .isTrue();
        softAssert.assertThat(historySidePanel.isCo2UnsavedChangeAnIncrement(0))
            .as("Peak volume per year must be a co2 increment after its value increased!")
            .isTrue();
        softAssert.assertAll();
        historySidePanel.clickSidePanelCloseButton();
        saveCalculation(calculationPage);

        parametersTab.enterProductionVolumePerYear("1");
        historySidePanel.openPanel();

        assertThat(historySidePanel.getSavedHistoryRecordsNumber())
            .as("History modal must contain two already saved records before calculation save!")
            .isEqualTo(2);
        assertThat(historySidePanel.getUnsavedHistoryRecordsNumber())
            .as("History modal must contain one unsaved record1!")
            .isEqualTo(1);
        softAssert.assertThat(historySidePanel.getNthUnsavedChangeText(0))
            .as("Peak volume per year must be the top unsaved history record!")
            .endsWith(StringUtils.joinWith("\n", "Calculation", "Peak volume per year",
                "previous", "1,000,000.00", PIECES_PER_YEAR.getUnitSymbol(),
                "new", "1.00", PIECE_PER_YEAR.getUnitSymbol()));
        softAssert.assertThat(historySidePanel.isCostUnsavedChangeAnIncrement(0))
            .as("Peak volume per year must be a cost increment after its value reduced!")
            .isTrue();
        softAssert.assertThat(historySidePanel.isCo2UnsavedChangeRecordADecrement(0))
            .as("Peak volume per year must be a co2 decrement after its value reduced!")
            .isTrue();
        softAssert.assertAll();

        saveAndDeleteCalculationThenLogout(calculationPage);
    }

    @Test
    @Description("See money field update in history")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-1023")
    public void seeMoneyFieldUpdateInHistoryTest() {
        CalculationWithCost calculationWithCostChill = constructCHILL();
        CalculationPage calculationPage = createCalculationWithCostModuleViaAPI(calculationWithCostChill);

        getProjectDashboardPage().getGlobalHeader()
            .selectDisplayCurrency(BRITISH_POUND);

        ParametersTab parametersTab = calculationPage.clickParametersTabButton();

        String actTarget = parametersTab.getTargetCost();

        parametersTab.enterTargetCost("1");

        HistorySidePanel historySidePanel = calculationPage.openHistorySidePanel();

        softAssert.assertThat(historySidePanel.getUnsavedHistoryRecordsNumber())
            .as("History modal must contain one unsaved record!")
            .isEqualTo(1);
        softAssert.assertThat(historySidePanel.getNthUnsavedChangeText(0))
            .as("Target value change should be shown!")
            .endsWith(StringUtils.joinWith("\n", "Calculation", "Target cost", "previous", actTarget,
                BRITISH_POUND.getIsoCode() + PER_PIECE.getUnitSymbol(), "new", "1.00",
                BRITISH_POUND.getIsoCode() + PER_PIECE.getUnitSymbol()));
        softAssert.assertAll();

        saveCalculation(calculationPage);

        historySidePanel.waitUntilPanelIsVisible();

        softAssert.assertThat(historySidePanel.getSavedHistoryRecordsNumber())
            .as("History modal must contain two already saved record before calculation save!")
            .isEqualTo(2);
        softAssert.assertThat(historySidePanel.getNthDetailedHistoryRecordText(0))
            .as("Target value change should be shown!")
            .endsWith(
                StringUtils.joinWith("\n", "Target cost", "previous", actTarget, BRITISH_POUND.getIsoCode() + PER_PIECE.getUnitSymbol(),
                    "new", "1.00", BRITISH_POUND.getIsoCode() + PER_PIECE.getUnitSymbol()));
        softAssert.assertAll();

        calculationPage.closeHistorySidePanel();

        deleteCalculationThenLogout(calculationPage);
    }

    @Test()
    @Description("See change of calculation base currency in history")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-1024")
    public void seeChangeOfCalculationBaseCurrencyInHistoryTest() {
        CalculationWithCost calculationWithCostChill = constructCHILL();
        CalculationPage calculationPage = createCalculationWithCostModuleViaAPI(calculationWithCostChill);

        ParametersTab parametersTab = calculationPage.clickParametersTabButton();
        parametersTab.selectBaseCurrency(BRITISH_POUND);

        HistorySidePanel historySidePanel = calculationPage.openHistorySidePanel();
        assertThat(historySidePanel.getUnsavedHistoryRecordsNumber())
            .as("History modal must contain one unsaved record!")
            .isEqualTo(1);
        assertThat(historySidePanel.getNthUnsavedChangeText(0))
            .as("Target value change should be shown!")
            .endsWith(StringUtils.joinWith("\n", "Calculation", "Base currency", "previous", "EUR", "new", "GBP"));

        saveCalculation(calculationPage);
        historySidePanel.waitUntilPanelIsVisible();
        assertThat(historySidePanel.getSavedHistoryRecordsNumber())
            .as("History modal must contain two already saved record before calculation save!")
            .isEqualTo(2);
        assertThat(historySidePanel.getNthDetailedHistoryRecordText(0))
            .as("Target value change should be shown!")
            .endsWith(StringUtils.joinWith("\n", "Calculation", "Base currency", "previous", "EUR", "new", "GBP"));

        deleteCalculationThenLogout(calculationPage);
    }

    @Test
    @Description("See change of base currency of calculation object in history")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-1025")
    public void seeChangeOfBaseCurrencyOfCalculationObjectInHistoryTest() {
        CalculationWithCost calculationWithCostChill = constructCHILL();
        CalculationPage calculationPage = createCalculationWithCostModuleViaAPI(calculationWithCostChill);

        calculationPage.clickManufacturingTabButton()
            .clickAddStepButton()
            .addMasterDataStep("Cable extrusion", "Wire extrusion")
            .clickOnStepByName("Cable extrusion")
            .clickOnMachineByName("SIEBE Extrusion line (for automotive wires)")
            .editBaseCurrency(SWISS_FRANC);
        calculationPage.waitUntilSaveButtonIsEnabled();
        HistorySidePanel historySidePanel = calculationPage.openHistorySidePanel();
        assertThat(historySidePanel.getUnsavedHistoryRecordsNumber())
            .as("History modal must contain two unsaved record!")
            .isEqualTo(2);
        assertThat(historySidePanel.getNthUnsavedChangeText(0))
            .as("Target value change should be shown!")
            .endsWith("""
                SIEBE Extrusion line (for automotive wires)
                Cable extrusion / SIEBE Extrusion line (for automotive wires)
                Base currency
                previous
                EUR
                new
                CHF""");

        saveCalculation(calculationPage);

        historySidePanel.waitUntilPanelIsVisible();
        assertThat(historySidePanel.getSavedHistoryRecordsNumber())
            .as("History modal must contain two saved record!")
            .isEqualTo(2);
        assertThat(historySidePanel.getNthDetailedHistoryRecordText(0))
            .as("Target value change should be shown!")
            .contains("""
                SIEBE Extrusion line (for automotive wires)
                Cable extrusion / SIEBE Extrusion line (for automotive wires)
                Base currency
                previous
                EUR
                new
                CHF""");

        deleteCalculationThenLogout(calculationPage);
    }

    @Test
    @Description("History - Load more entries")
    @Severity(SeverityLevel.NORMAL)
    @TmsLink("QA-1567")
    public void historyLoadMoreEntriesTest() {
        CalculationWithCost calculationWithCostChill = constructCHILL();
        CalculationPage calculationPage = createCalculationWithCostModuleViaAPI(calculationWithCostChill);

        getProjectDashboardPage().getGlobalHeader()
            .selectDisplayCurrency(EURO);

        ParametersTab parametersTab = calculationPage.clickParametersTabButton();
        parametersTab.enterLifeTimeInput("1");

        saveCalculation(calculationPage);

        parametersTab.selectLocation("Estonia");

        saveCalculation(calculationPage);

        calculationPage.clickRenameMainVariantButton()
            .enterVariantTitle(TITLE + " - update")
            .clickRenameVariantButton();

        saveCalculation(calculationPage);

        PartTab partTab = calculationPage.clickPartTabButton();
        partTab.enterPartName(calculationWithCostChill.getPartName() + " - update");

        saveCalculation(calculationPage);

        partTab.attachFile(PHOTO_ATTACHMENT_PNG);

        saveCalculation(calculationPage);

        calculationPage.clickManufacturingTabButton()
            .clickAddStepButton()
            .addMasterDataStep("Surface grinding", "Fine-surface grinding");

        saveCalculation(calculationPage);

        parametersTab = calculationPage.clickParametersTabButton();
        parametersTab.enterLifeTimeInput("2");
        partTab = calculationPage.clickPartTabButton();
        partTab.enterPartNumber(calculationWithCostChill.getPartNumber() + " - update");

        HistorySidePanel historySidePanel = calculationPage.openHistorySidePanel();
        softAssert.assertThat(historySidePanel.getUnsavedHistoryRecordsNumber())
            .as("History modal should contain two unsaved changes!")
            .isEqualTo(2);
        softAssert.assertThat(historySidePanel.getSavedHistoryRecordsNumber())
            .as("History modal should contain only the last two history entries!")
            .isGreaterThanOrEqualTo(5);
        if (historySidePanel.getSavedHistoryRecordsNumber() == 5) {
            softAssert.assertThat(historySidePanel.isLoadMoreButtonAvailable())
                .as("History modal must contain load more button if there are further history elements!")
                .isTrue();
        }
        softAssert.assertAll();

        if (historySidePanel.getSavedHistoryRecordsNumber() == 5 && historySidePanel.isLoadMoreButtonAvailable()) {
            historySidePanel.clickLoadMoreButton();
        }

        historySidePanel.scrollToBottomOfHistory();

        softAssert.assertThat(historySidePanel.getSavedHistoryRecordsNumber())
            .as("History modal should load more history entries after Load more button is clicked!")
            .isEqualTo(7);
        softAssert.assertThat(historySidePanel.isLoadMoreButtonAvailable())
            .as("History modal must contain load more button if there are further history elements!")
            .isFalse();
        softAssert.assertThat(historySidePanel.getNthHistoryRecordText(6))
            .as("The last history record should be the creation of the calculation!")
            .contains("Calculation created");
        softAssert.assertAll();

        saveAndDeleteCalculationThenLogout(calculationPage);
    }
}
