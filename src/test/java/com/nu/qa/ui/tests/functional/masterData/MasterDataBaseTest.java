package com.nu.qa.ui.tests.functional.masterData;

import com.nu.qa.ui.framework.CalculationBaseTest;
import com.nu.qa.ui.pageobjects.widgets.FilterTabulatorTable;
import com.nu.qa.utilities.utilities.ValueUtil;
import io.qameta.allure.Step;

import java.util.List;
import java.util.Locale;

import static org.assertj.core.api.Assertions.assertThat;

public abstract class MasterDataBaseTest extends CalculationBaseTest {

    public static final String VALID_FROM_DATE = "Valid from date";
    public static final String VALUE = "Value";
    public static final String DATA_SOURCE = "Data source";
    public static final String MODIFIER = "Modifier";
    public static final String MODIFICATION_DATE = "Modification date";

    public static final String CUSTOMER = "Customer";

    @Step("Validate master data table initial state for filters and columns")
    protected void validateMasterDataTableInitialState(String masterDataName, FilterTabulatorTable masterDataTable,
                                                       List<String> headerFilterNames, List<String> tableHeaders) {
        masterDataTable.showEffectivitiesFilters();
        softAssert.assertThat(masterDataTable.getHeaderFilterNames().size())
            .as(masterDataName + " default headers should be visible!")
            .isEqualTo(headerFilterNames.size());
        softAssert.assertThat(masterDataTable.getHeaderFilterNames())
            .as(masterDataName + " filters should show default set of elements!")
            .containsExactlyElementsOf(headerFilterNames);
        softAssert.assertThat(masterDataTable.getHeaders().size())
            .as("Default " + masterDataName + " table headers should be visible!")
            .isEqualTo(tableHeaders.size());
        softAssert.assertThat(masterDataTable.getHeaderNames())
            .as("Default " + masterDataName + " table headers should be visible!")
            .containsExactlyElementsOf(tableHeaders);
        softAssert.assertThat(masterDataTable.isTableColumnAndFilterSelectorButtonVisible())
            .as("Column/Filter selector button should be visible above " + masterDataName + " table!")
            .isTrue();
        softAssert.assertThat(masterDataTable.getNumberOfTableRows())
            .as(masterDataName + " table should contain elements!")
            .isPositive();
        softAssert.assertThat(ValueUtil.dateStringMatchesFormat(masterDataTable.getTextFromColumnForEntity(
                        MODIFICATION_DATE, masterDataTable.getFirstRowDesignation()), DATE_FORMAT_STRING, Locale.US))
                .as(masterDataName + " table " + MODIFICATION_DATE + " fields should be in proper format!")
                .isTrue();
        softAssert.assertAll();
    }

    protected void validateValidFromDate(String masterDataName, FilterTabulatorTable masterDataTable) {
        if (!"-".equals(masterDataTable.getTextFromColumnForEntity(VALID_FROM_DATE,
            masterDataTable.getFirstRowDesignation()))) {
            assertThat(ValueUtil.dateStringMatchesFormat(masterDataTable.getTextFromColumnForEntity(VALID_FROM_DATE,
                masterDataTable.getFirstRowDesignation()), DATE_FORMAT_STRING))
                .as(masterDataName + "table " + VALID_FROM_DATE + " fields should be in proper format!")
                .isTrue();
        }
    }
}
