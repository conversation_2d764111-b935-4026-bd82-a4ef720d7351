package com.nu.bomrads.service.user;

import com.nu.bomrads.id.AccountId;
import com.nu.bomrads.service.UnitTest;
import com.nu.bomrads.service.history.AccessCheck;
import com.nu.security.config.MultitenancyConfig;
import com.nu.user.KeycloakUserClient;
import org.junit.jupiter.api.Test;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.GroupResource;
import org.keycloak.admin.client.resource.GroupsResource;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.representations.idm.GroupRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.List;
import java.util.NoSuchElementException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class KeycloakUserServiceTest extends UnitTest {

    @Mock
    private Keycloak keycloak;
    @Mock
    private MultitenancyConfig multitenancyConfig;
    @InjectMocks
    private KeycloakUserClient userService;


    @Test
    void userServiceGetUsersFindByUser() {
        String groupPath = "groupgroupgroup";
        setupKeycloakMock(groupPath);

        var accessCheck = getAccessCheck(groupPath);
        assertThat(userService.getUsers(accessCheck, "first")).hasSize(1);
    }

    @Test
    void userServiceGetUsersFindByUserWithNotMatchingGroup() {
        String groupPath = "groupgroupgroup";
        setupKeycloakMock(groupPath);

        var accessCheck = getAccessCheck("differen groupd");
        assertThatThrownBy(() -> userService.getUsers(accessCheck, "first")).isInstanceOf(NoSuchElementException.class);
    }

    @Test
    void userServiceGetUsersFindByUserNotExists() {
        String groupPath = "groupgroupgroup";
        setupKeycloakMock(groupPath);

        var accessCheck = getAccessCheck(groupPath);
        assertThat(userService.getUsers(accessCheck, "who are you")).isEmpty();
    }

    @Test
    void userServiceGetUsersWithNoGroupy() {
        setupKeycloakMock("groupgroupgroup");

        var accessCheck = getNoGroupAccessCheck();
        assertThat(userService.getUsers(accessCheck, "who are you")).isEmpty();
    }

    @Test
    void userServiceGetByIdWithDifferentGroup() {
        String groupPath = "groupgroupgroup";
        setupKeycloakMock(groupPath);

        var accessCheck = getAccessCheck("other group");
        assertThat(userService.getUsersByIds(accessCheck, List.of("u1"))).isEmpty();
    }

    @Test
    void userServiceGetByIdExists() {
        String groupPath = "groupgroupgroup";
        setupKeycloakMock(groupPath);

        var accessCheck = getAccessCheck(groupPath);
        assertThat(userService.getUsersByIds(accessCheck, List.of("u1"))).hasSize(1);
    }

    @Test
    void userServiceGetByIdWithNoGroup() {
        setupKeycloakMock("groupgroupgroup");

        var accessCheck = getNoGroupAccessCheck();
        assertThat(userService.getUsersByIds(accessCheck, List.of("1"))).isEmpty();
    }

    private static com.nu.user.AccessCheck getAccessCheck(String groupPath) {
        return new AccessCheck("userId", "userName", "accountName", new AccountId(), "someRealm", List.of(), List.of(groupPath)).toNuLib();
    }

    private static com.nu.user.AccessCheck getNoGroupAccessCheck() {
        return new AccessCheck("userId", "userName", "accountName", new AccountId(), "someRealm", List.of(), List.of()).toNuLib();
    }

    private void setupKeycloakMock(String groupPath) {
        RealmResource realm = mock();
        GroupsResource groupsResource = mock();
        GroupResource groupResource = mock();


        when(keycloak.realm(anyString())).thenReturn(realm);
        when(realm.groups()).thenReturn(groupsResource);
        GroupRepresentation group = new GroupRepresentation();
        String groupId = "1";
        group.setId(groupId);
        group.setPath(groupPath);
        when(groupsResource.groups()).thenReturn(List.of(group));

        UserRepresentation user = new UserRepresentation();

        user.setId("u1");
        user.setFirstName("first");
        user.setLastName("last");
        user.setEmail("email");

        when(groupResource.members(anyInt(), anyInt())).thenReturn(List.of(user));

        when(groupsResource.group(groupId)).thenReturn(groupResource);
    }
}
