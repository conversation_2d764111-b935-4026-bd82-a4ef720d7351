package com.nu.bomrads.service.history;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.function.Executable;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.test.context.support.WithMockUser;

import com.nu.bomrads.IntegrationTest;
import com.nu.bomrads.dto.BomExplorerResponseDTO;
import com.nu.bomrads.dto.BomNodeCreationResultDTO;
import com.nu.bomrads.dto.BranchViewDTO;
import com.nu.bomrads.dto.InternalChild;
import com.nu.bomrads.dto.NewBranchCreationDTO;
import com.nu.bomrads.dto.NodeLabels;
import com.nu.bomrads.dto.NodeSnapshotDTO;
import com.nu.bomrads.dto.NodeSnapshotQueryDTO;
import com.nu.bomrads.dto.NodeSnapshotsRequestDTO;
import com.nu.bomrads.dto.NodeTreeDTO;
import com.nu.bomrads.dto.admin.SnapshotDTO;
import com.nu.bomrads.enumeration.BranchTarget;
import com.nu.bomrads.enumeration.ChangeType;
import com.nu.bomrads.fields.Trigger;
import com.nu.bomrads.id.BomEntryId;
import com.nu.bomrads.id.BomNodeId;
import com.nu.bomrads.id.BranchId;
import com.nu.bomrads.id.ChangesetId;
import com.nu.bomrads.id.ManufacturingTreeId;
import com.nu.bomrads.service.BranchService;
import com.nu.bomrads.service.errors.BranchStateException;
import com.nu.bomrads.service.query.MainBranch;
import com.nu.bomrads.service.query.SpecificBranch;

import static com.nu.bomrads.utils.StreamUtils.find;
import static com.nu.bomrads.utils.StreamUtils.of;

@IntegrationTest
@WithMockUser
@DisplayName("Parent-Child nodes in the same calculation context")
class ParentChildBomNodeIT extends CommonTestSetup {

    BomNodeCreationResultDTO nodeCreation;
    BomNodeId childBomNodeId;
    ManufacturingTreeId childManufacturingTreeId;
    BomEntryId parentChildEntryId = new BomEntryId();

    @Autowired
    private BranchService branchService;

    @BeforeEach
    void createParentChildNode() {
        childBomNodeId = bomNodeNames.createNamed("child-bom-node-id");
        childManufacturingTreeId = manufacturingTreeNames.createNamed("child-manufacturing-tree-id");

        nodeCreation = boms.create(testUser, projectId, "main-variant",
            new NodeTreeDTO(bomNodeId, manufacturingTreeId,
                new NodeLabels("bomNode", "title", 2021, false))
                .withChildren(
                    new NodeTreeDTO(childBomNodeId, childManufacturingTreeId,
                        new NodeLabels("childBomNode", "child title", 2021, true))
                        .withBomEntryId(parentChildEntryId)
                        .withAsRoot(false)
                        ),
            null, CREATION);
        assertNotNull(nodeCreation, "bomNodeCreationResult");
    }

    @Test
    @DisplayName(" are created in a Project")
    void checkCurrentState() {
        countBomNodes(2);

        verifySnapshots(bomNodeId, st -> verifyParentSnapshot(bomNodeId, manufacturingTreeId, st));
        verifySnapshots(childBomNodeId, snapshot -> {
            assertEquals("childBomNode", snapshot.getName(), "snapshot.name");
            assertEquals(2021, snapshot.getYear(), "snapshot.year");
            assertEquals("child title", snapshot.getTitle(), "snapshot.title");
            assertEquals(childBomNodeId, snapshot.getBomNode().getId(), "snapshot.bomNode.id");
            assertTrue(snapshot.getGenerated(), "snapshot.generated");
            assertEquals("test-user", snapshot.getResponsibleUser(), "snapshot.responsibleUser");

            assertNull(snapshot.getPrevious(), "snapshot.previous");
            assertEquals(childManufacturingTreeId, snapshot.getManufacturingTreeId(), "snapshot.manufacturingTreeId");
        });

        {
            var bomNodeTreeForBranch = getBranchStatus(nodeCreation.getBranchId());
            verifyBomNodeTreeStatus(nodeCreation.getBranchId(), "parent-child", bomNodeTreeForBranch, "main-variant",
                    CREATION, ChangeType.CREATION);
            var root = bomNodeTreeForBranch.findByBomNodeId(bomNodeId);
            verifyNodeStatus(root, "rootId",  bomNodeId, manufacturingTreeId, 1, bomNodeId, "title", "bomNode");
            //assertNull(root.bomEntryId(), "root.bomEntryId");
            assertEquals("test-user", root.getResponsibleUser(), "root.responsibleUser");
            assertEquals(1, root.getChildren().size(), "root.children.size");
            var child = root.getChildren().get(0);
            var childNode = bomNodeTreeForBranch.findByBomNodeId(child.bomNodeId());
            verifyNodeStatus(childNode, "child", childBomNodeId, childManufacturingTreeId, 0, childBomNodeId,
                    "child title", "childBomNode");
            assertEquals(parentChildEntryId, child.bomEntryId(), "child.bomEntryId");
            assertEquals("test-user", childNode.getResponsibleUser(), "child.responsibleUser");
        }

        {
            var rootNodes = boms.rootNodes(new MainBranch(testUser, projectId));
            assertNotNull(rootNodes, "rootNodes");
            assertEquals(1, rootNodes.getTotalElements(), "rootNodes.size");
            checkRootNodes(rootNodes.getContent(), bomNodeId, manufacturingTreeId, "title", false, true);
        }
    }

    @Test
    @DisplayName(" are returned in bom explorer API")
    void checkBomExplorerApi() {
        // test bom explorer api
        var rootResp = boms.getSnapshotsForBomExplorer(projectId, null, testUser);
        verifyBomExplorerRootResponse(rootResp);
        var rootNodeSnapshots = rootResp.getSnapshots();

        var req = new NodeSnapshotsRequestDTO(List.of(new NodeSnapshotQueryDTO(rootNodeSnapshots.get(0).getBomNodeId(), false),
            new NodeSnapshotQueryDTO(rootNodeSnapshots.get(0).getBomNodeId(), false)),
            nodeCreation.branchId());
        var branchViewSpecificNodesNoChildrenResp = boms.getSnapshotsForBomExplorer(projectId, req, testUser);
        verifyBomExplorerRootResponse(branchViewSpecificNodesNoChildrenResp);
        var branchViewSpecificNodesNoChildren = branchViewSpecificNodesNoChildrenResp.getSnapshots();
        var root = getNodeByTitle(branchViewSpecificNodesNoChildren, "title");
        assertEquals(1, root.children().size());

        req = new NodeSnapshotsRequestDTO(List.of(new NodeSnapshotQueryDTO(rootNodeSnapshots.get(0).getBomNodeId(), true)),
            null);
        var branchViewSpecificNodesResp = boms.getSnapshotsForBomExplorer(projectId, req, testUser);
        verifyBomExplorerBothNodesResponse(branchViewSpecificNodesResp);
        var branchViewSpecificNodes = branchViewSpecificNodesResp.getSnapshots();

        req = new NodeSnapshotsRequestDTO(List.of(new NodeSnapshotQueryDTO(branchViewSpecificNodes.get(0).getBomNodeId(), true),
            new NodeSnapshotQueryDTO(branchViewSpecificNodes.get(1).getBomNodeId(), true)),
            nodeCreation.branchId());

        branchViewSpecificNodesResp = boms.getSnapshotsForBomExplorer(projectId, req, testUser);
        verifyBomExplorerBothNodesResponse(branchViewSpecificNodesResp);
    }

    private NodeSnapshotDTO getNodeByTitle(List<NodeSnapshotDTO> snapshots, String title) {
        return snapshots.stream().filter(s -> s.getTitle().equals(title)).findAny().orElseThrow();
    }

    private void verifyBomExplorerBothNodesResponse(BomExplorerResponseDTO bomExplorerResponse) {
        var snapshots = bomExplorerResponse.getSnapshots();
        assertEquals(1, bomExplorerResponse.getBranchInfos().size());
        assertEquals(2, snapshots.size());
        var root = getNodeByTitle(snapshots, "title");
        assertEquals(1, root.children().size());
        var child = getNodeByTitle(snapshots, "child title");
        assertEquals(0, child.children().size());
        assertEquals(List.of(child.getBomNodeId()), root.getChildren().stream().map(InternalChild::bomNodeId).toList());
    }

    private void verifyBomExplorerRootResponse(BomExplorerResponseDTO bomExplorerResponse) {
        var snapshots = bomExplorerResponse.getSnapshots();
        assertEquals(1, bomExplorerResponse.getBranchInfos().size());
        assertEquals(1, snapshots.size());
        assertEquals("title", snapshots.get(0).getTitle());
        assertEquals(0, snapshots.get(0).getNoBranches(), "invalid number of branches");
    }

    @ParameterizedTest
    @DisplayName(" is modifiable, after GetBomNodeTreeForUpdate is called")
    @ValueSource(booleans = {false, true})
    void pushChangeAfterGetBomNodeTreeForUpdate(boolean useCompatibilityApi) {
        var newManufacturingTreeId = manufacturingTreeNames.createNamed("newManufacturingTreeId");

        var forUpdate = callBomNodeTreeForUpdate(useCompatibilityApi);

        var result = apiPushChanges(testUser, projectId, forUpdate.branch().id(), forUpdate.changeset().id(),
            pushChanges("changed-variant-name", "random trigger", ChangeType.FIELD_CHANGE,
                new NodeTreeDTO(bomNodeId, newManufacturingTreeId,
                    new NodeLabels("bomNode-x", "title-modified", 2021, false))
                .withChildren(
                    new NodeTreeDTO(childBomNodeId, childManufacturingTreeId,
                        new NodeLabels("childBomNode", "child title", 2021, true))
                    .withBomEntryId(parentChildEntryId))));
        assertNotNull(result, "result not null");
        assertNotNull(result.branch(), "result.branch");
        assertNotEquals(nodeCreation.branchId(), result.branch().id(), "different.branch.than.nodeCreation");
        assertEquals(nodeCreation.branchId(), result.branch().sourceId(), "new.branch.source=old.branch");
        assertEquals(forUpdate.branch().id(), result.branch().id(), "forUpdate.branch.id = result.branch.id");

        var changeset = result.changeset();
        assertEquals(ChangeType.FIELD_CHANGE, changeset.type(), "checkoutChangeset.type");
        assertEquals(forUpdate.changeset().id(), changeset.previousChangesetId(), "checkoutChangeset.previousChangeset");
        assertEquals(testUser.userName(), changeset.creatorUser(), "checkoutChangeset.creatorUser");
        assertEquals("changed-variant-name", changeset.variantName(), "checkoutChangeset.variantName");
        assertEquals(RANDOM_TRIGGER, changeset.trigger(), "checkoutChangeset.trigger");

        verifyBranch(result.branch(), false, false, false, testUser.userName(), nodeCreation.getBranchId());
    }

    private BranchViewDTO callBomNodeTreeForUpdate(boolean useCompatibilityApi) {
        var dto = new NewBranchCreationDTO(nodeCreation.branchId(), nodeCreation.rootBomNodeId(), FOR_UPDATE, BranchTarget.user, null, null, false);
        var forUpdate = useCompatibilityApi ?
            boms.getBomNodeTreeForUpdateCompatibility(testUser, dto) :
            boms.getBomNodeTreeForUpdate(testUser, projectId, nodeCreation.branchId(), nodeCreation.rootBomNodeId(), FOR_UPDATE);

        // var forUpdate = boms.getBomNodeTreeForUpdate(testUser, projectId, nodeCreation.branchId(), nodeCreation.rootBomNodeId());
        assertNotNull(forUpdate, "forUpdate not null");
        assertNotNull(forUpdate.branch(), "forUpdate.branch");
        assertNotEquals(nodeCreation.branchId(), forUpdate.branch().id(), "different.branch");
        assertEquals(nodeCreation.changesetId(), forUpdate.changeset().previousChangesetId(), "checkoutChangeset.previousChangeset");

        verifyBranch(forUpdate.branch(), false, false, false, testUser.userName(), nodeCreation.branchId());
        return forUpdate;
    }

    @Test
    @DisplayName(" is not modifiable on the default, main branch")
    void pushChangeOnMain() {
        var newManufacturingTreeId = new ManufacturingTreeId();
        assertExceptionMessage("error.bomService.branch.mainBranch",
            assertThrows(BranchStateException.class,
                () -> apiPushChanges(testUser, projectId, nodeCreation.branchId(), nodeCreation.changesetId(),
                        pushChanges("changed-variant-name", "random trigger", ChangeType.FIELD_CHANGE,
                            new NodeTreeDTO(bomNodeId, newManufacturingTreeId,
                                new NodeLabels("bomNode-x", "title-modified", 2021, false))))));
    }

    @Test
    @DisplayName(" pushing changes on a branch fails, if a wrong changeset id is sent")
    void pushChangeFails() {
        var newManufacturingTreeId = new ManufacturingTreeId();

        var forUpdate = callBomNodeTreeForUpdate(false);

        assertExceptionMessage("error.bomService.different.changeset.expected",
            assertThrows(IllegalArgumentException.class, () -> apiPushChanges(testUser, projectId, forUpdate.branch().id(), new ChangesetId(),
                pushChanges("changed-variant-name", "random trigger",
                    ChangeType.FIELD_CHANGE, new NodeTreeDTO(bomNodeId, newManufacturingTreeId,
                        new NodeLabels("bomNode-x", "title-modified", 2021, false))))));
    }


    @Nested
    @DisplayName("can be checked-out to a user branch")
    class Checkout {
        private static final String CHECKOUT_USER = "checkout-user";
        BranchViewDTO checkoutResultDto;
        BranchId checkoutBranchId;

        void checkout(boolean global, String branchName) {
            checkoutResultDto = boms.checkoutMain(checkoutUser, projectId, null, bomNodeId, global, branchName, CHECKOUT_MAIN);
            checkoutBranchId = checkoutResultDto.getBranch().getId();
        }

        @ParameterizedTest
        @MethodSource("checkoutConfigurations")
        @DisplayName("which creates new snapshot and relation records attached, with a checkout Changeset")
        void verifyExpectation(boolean global, String branchName) {
            checkout(global, branchName);

            var checkoutBranch = checkoutResultDto.getBranch();
            var checkoutChangeset = checkoutResultDto.getChangeset();

            assertEquals(ChangeType.CHECKOUT, checkoutChangeset.getType(), "checkoutChangeset.type");
            assertEquals(nodeCreation.getChangesetId(), checkoutChangeset.getPreviousChangesetId(), "checkoutChangeset.previousChangeset");
            assertEquals(CHECKOUT_USER, checkoutChangeset.getCreatorUser(), "checkoutChangeset.creatorUser");
            assertEquals(branchName != null ? branchName : "main-variant", checkoutChangeset.getVariantName(), "checkoutChangeset.variantName");
            assertEquals(CHECKOUT_MAIN, checkoutChangeset.getTrigger(), "checkoutChangeset.trigger");

            verifyBranch(checkoutBranch, false, global, false, CHECKOUT_USER, nodeCreation.getBranchId());

            var root = checkoutResultDto.findByBomNodeId(bomNodeId);
            // verifyNodeStatus(root, "checkedOut.rootId",  bomNodeId, manufacturingTreeId, 1, bomNodeId, nvl(branchName, "title"), "bomNode");
            verifyNodeStatus(root, "checkedOut.rootId",  bomNodeId, manufacturingTreeId, 1, bomNodeId, "title", "bomNode");
            assertEquals(manufacturingTreeId, root.getTreeId(), "checkedOut.treeId");
        }

        @Test
        @DisplayName("which is not visible to a different user")
        void otherUserCantCheckout() {
            checkout(false, null);
            assertExceptionMessage("error.bomService.branch.notVisible",
                    assertThrows(BranchStateException.class, () -> boms.checkoutMain(otherUser, projectId, checkoutBranchId, bomNodeId, false, null, CHECKOUT_MAIN)));
        }

        @Test
        @DisplayName("other users can't push to this branch")
        void otherUserCantModify() {
            checkout(false, null);
            assertThrows(BranchStateException.class,
                () -> apiPushChanges(testUser2, projectId, checkoutBranchId, checkoutResultDto.changeset().id(),
                    pushChanges("changed-variant-name", "random trigger",
                        ChangeType.FIELD_CHANGE,
                        new NodeTreeDTO(bomNodeId, new ManufacturingTreeId(),
                            new NodeLabels("bomNode-x", "title-modified", 2021, false)))),
                "error.bomService.branch.notCreatedByUser");
        }

        @Test
        @DisplayName("other users can't make this branch writable")
        void otherUserCantMakeItWritable() {
            checkout(false, null);
            assertThrows(BranchStateException.class,
                () -> boms.getBomNodeTreeForUpdate(testUser2, projectId, checkoutBranchId, bomNodeId, FOR_UPDATE),
                "error.bomService.branch.notVisible");
        }

        @Test
        @DisplayName("other users can modify the global branch, after a checkout")
        void otherUserCanModifyGlobal() {
            checkout(true, "other-variant");
            var checkout = boms.getBomNodeTreeForUpdate(testUser2, projectId, checkoutBranchId, bomNodeId, FOR_UPDATE);
            assertEquals(ChangeType.CHECKOUT, checkout.changeset().type(), "checkout.type");
            assertEquals(checkoutResultDto.changeset().id(), checkout.changeset().previousChangesetId(), "checkout.previousChangeset");

            var result = apiPushChanges(testUser2, projectId, checkout.branch().id(), checkout.changeset().id(),
                pushChanges("changed-variant-name", "random trigger",
                    ChangeType.FIELD_CHANGE,
                    new NodeTreeDTO(bomNodeId, new ManufacturingTreeId(),
                        new NodeLabels("bomNode-x", "title-modified", 2021, false))));
            assertNotNull(result, "result");
            var pushChangeset = result.changeset();
            assertEquals(ChangeType.FIELD_CHANGE, pushChangeset.getType(), "pushChangeset.type");
            assertEquals(checkout.changeset().id(), pushChangeset.previousChangesetId(), "pushChangeset.previousChangeset");
            assertEquals(testUser2.userName(), pushChangeset.getCreatorUser(), "pushChangeset.creatorUser");
            assertEquals("changed-variant-name", pushChangeset.getVariantName(), "pushChangeset.variantName");
            assertEquals(RANDOM_TRIGGER, pushChangeset.getTrigger(), "pushChangeset.trigger");
        }

        @ParameterizedTest
        @MethodSource("checkoutConfigurations")
        @DisplayName("user can't execute undo on the checkout step")
        void userCantUndoIt(boolean global, String branchName) {
            checkout(global, branchName);
            Executable task = () -> boms.undo(new SpecificBranch(checkoutUser, projectId, checkoutBranchId));
            if (global) {
                assertThrows(BranchStateException.class, task, "error.bomService.branch.globalBranch");
            } else {
                assertThrows(IllegalArgumentException.class, task, "error.bomService.branch.goBeforeBigBang");
            }
        }

        static List<Object[]> checkoutConfigurations() {
            return List.of(
                    new Object[] {false, null},
                    new Object[] {false, "private-named-branch"},
                    new Object[] {true, "public-variant"}
                );
        }

        @Nested
        @DisplayName("child bomNode can be deleted")
        class DeleteChild {
            BranchViewDTO pushResult;
            ManufacturingTreeId newManufacturingTreeId;

            @BeforeEach
            void change() {
                newManufacturingTreeId = manufacturingTreeNames.createNamed("newManufacturingTreeId");
                checkout(false, null);
                pushResult = apiPushChanges(checkoutUser, projectId, checkoutBranchId, checkoutResultDto.changeset().id(),
                    pushChanges("changed-variant-name", RANDOM_TRIGGER.text(), ChangeType.FIELD_CHANGE,
                        new NodeTreeDTO(bomNodeId, newManufacturingTreeId,
                            new NodeLabels("bomNode-x-modified-1", "title-modified-1", 2021, false))));
            }

            @Test
            @DisplayName("and it is not visible to other users")
            void isNotVisibleToOthers() {
                assertExceptionMessage("error.bomService.branch.notVisible",
                    assertThrows(BranchStateException.class, () -> boms.getBomNodeTreeForBranch(testUser, projectId, checkoutBranchId)));
            }

            @Test
            @DisplayName("and it will have one new snapshots, with the expected treeId")
            void verifyResult() {
                countBomNodes(2);

                verifyAllSnapshotsFor(bomNodeId, newManufacturingTreeId, st -> verifyUpdatedSnapshot("modified-1", bomNodeId, newManufacturingTreeId, st));

                var bomNodeTreeForBranch = boms.getBomNodeTreeForBranch(checkoutUser, projectId, checkoutBranchId);
                verifyBomNodeTreeStatus(checkoutBranchId, "modified", bomNodeTreeForBranch,
                        "changed-variant-name", RANDOM_TRIGGER, ChangeType.FIELD_CHANGE);

                var root = bomNodeTreeForBranch.findByBomNodeId(bomNodeId);
                verifyNodeStatus(root, "rootId", bomNodeId, newManufacturingTreeId, 0, bomNodeId, "title-modified-1", "bomNode-x-modified-1");
            }
        }


        @Nested
        @DisplayName("and parent can be modified")
        class ChangeParentFields {
            BranchViewDTO pushResult;
            ManufacturingTreeId newManufacturingTreeId;
            ManufacturingTreeId newParentTreeId;
            ManufacturingTreeId newChildTreeId;

            @BeforeEach
            void change() {
                newManufacturingTreeId = manufacturingTreeNames.createNamed("newManufacturingTreeId");
                checkout(false, null);
                pushResult = apiPushChanges(checkoutUser, projectId, checkoutBranchId, checkoutResultDto.getChangeset().getId(),
                    pushChanges("changed-variant-name", "random trigger",
                        ChangeType.FIELD_CHANGE,
                        new NodeTreeDTO(bomNodeId, newManufacturingTreeId,
                            new NodeLabels("bomNode-x-modified-1", "title-modified-1", 2021, false))
                            .withChildren(
                                new NodeTreeDTO(childBomNodeId, childManufacturingTreeId,
                                    new NodeLabels("childBomNode", "child title", 2021, true))
                                    .withBomEntryId(parentChildEntryId))));
            }

            @Test
            @DisplayName("and it will have two new snapshots, with the expected treeIds and child is added as external relation (lazy child update)")
            void verifyResult() {
                countBomNodes(2);

                verifyAllSnapshotsFor(bomNodeId, newManufacturingTreeId, st -> verifyUpdatedSnapshot("modified-1", bomNodeId, newManufacturingTreeId, st));

                var bomNodeTreeForBranch = getBranchStatus(checkoutUser, checkoutBranchId);
                verifyBomNodeTreeStatus(checkoutBranchId, "modified", bomNodeTreeForBranch,
                    "changed-variant-name", RANDOM_TRIGGER, ChangeType.FIELD_CHANGE);

                var root = bomNodeTreeForBranch.findByBomNodeId(bomNodeId);
                verifyNodeStatus(root, "rootId", bomNodeId, newManufacturingTreeId, 1, bomNodeId,
                    "title-modified-1", "bomNode-x-modified-1");

                var child = root.allChildren().get(0);
                var childNode = bomNodeTreeForBranch.findByBomNodeId(child.bomNodeId());
                verifyNodeStatus(childNode, "child", childBomNodeId, childManufacturingTreeId, 0,
                    childBomNodeId, "child title", "childBomNode");

                {
                    // COST-15213 lazy child update
                    assertEquals(2, bomNodeTreeForBranch.snapshots().size());
                    assertEquals(1, root.externalChildren().size());
                    assertEquals(1, root.allChildren().size());
                    assertEquals(0, root.internalChildren().size());
                    verifyChildren(bomNodeTreeForBranch.collectChildren(bomNodeId));
                    verifyChildren(bomNodeTreeForBranch.directChildren(bomNodeId));
                    verifyParents(bomNodeTreeForBranch.directParents(childBomNodeId));
                    verifyParents(bomNodeTreeForBranch.collectParents(childBomNodeId));
                }
            }
            private void verifyChildren(Collection<NodeSnapshotDTO> children) {
                assertEquals(2, children.size());
                assertTrue(children.stream().anyMatch(n -> Objects.equals(n.getBomNodeId(), childBomNodeId)));
            }
            private void verifyParents(Collection<NodeSnapshotDTO> parents) {
                assertEquals(2, parents.size());
                assertTrue(parents.stream().anyMatch(n -> Objects.equals(n.getBomNodeId(), bomNodeId)));
            }

            @Test
            @DisplayName("and a new parent-child change handled properly")
            void changeParentAndChild() {
                var newChange = changeBothNode();
                tester(newChange, "bomNodeId")
                    .verifyTreeId("new-parent-tree-id")
                    .verifyExternalParents()
                    .verifyChildren(List.of("child-bom-node-id"));
                tester(newChange, "child-bom-node-id")
                    .verifyTreeId("new-child-tree-id")
                    .verifyExternalParents(List.of("bomNodeId"))
                    .verifyLeaf();
                {
                    var fullBranchView = getBranchStatus(checkoutUser, checkoutBranchId);
                    tester(fullBranchView, "bomNodeId")
                        .verifyTreeId("new-parent-tree-id")
                        .verifyExternalParents()
                        .verifyChildren(List.of("child-bom-node-id"));
                    tester(fullBranchView, "child-bom-node-id")
                        .verifyTreeId("new-child-tree-id")
                        .verifyExternalParents(List.of("bomNodeId"))
                        .verifyLeaf();
                }
            }

            @Test
            @DisplayName("and a new parent-child change handled properly, and after we can change parent similarly")
            void changeParentAndChildAndAgainTheParent() {
                var prevChange = changeBothNode();

                var finalParentTreeId = manufacturingTreeNames.createNamed("final-parent-tree-id");
                var finalChange = apiPushChanges(checkoutUser, projectId, checkoutBranchId, prevChange.getChangeset().getId(),
                    pushChanges("changed-variant-name-2", "random trigger-2",
                        ChangeType.FIELD_CHANGE,
                        new NodeTreeDTO(bomNodeId, finalParentTreeId,
                            new NodeLabels("bomNode-x-modified-2", "title-modified-2", 2021, false))
                            .withChildren(
                                new NodeTreeDTO(childBomNodeId, newChildTreeId,
                                    new NodeLabels("childBomNode-modified-2", "child title-modified-2", 2021, true))
                                    .withBomEntryId(parentChildEntryId))));
                tester(finalChange, "bomNodeId")
                    .verifyTreeId("final-parent-tree-id")
                    .verifyExternalParents()
                    .verifyChildren(Map.of("child-bom-node-id", "new-child-tree-id"));
                {
                    var fullBranchView = getBranchStatus(checkoutUser, checkoutBranchId);
                    tester(fullBranchView, "bomNodeId")
                        .verifyTreeId("final-parent-tree-id")
                        .verifyExternalParents()
                        .verifyChildren(Map.of("child-bom-node-id", "new-child-tree-id"));
                    tester(fullBranchView, "child-bom-node-id")
                        .verifyTreeId("new-child-tree-id")
                        .verifyExternalParents(List.of("bomNodeId"))
                        .verifyLeaf();
                }

            }

            /**
             * send a request which modifies the parent and the child.
             */
            private BranchViewDTO changeBothNode() {
                newParentTreeId = manufacturingTreeNames.createNamed("new-parent-tree-id");
                newChildTreeId = manufacturingTreeNames.createNamed("new-child-tree-id");
                return apiPushChanges(checkoutUser, projectId, checkoutBranchId, pushResult.getChangeset().getId(),
                    pushChanges("changed-variant-name", "random trigger",
                        ChangeType.FIELD_CHANGE,
                        new NodeTreeDTO(bomNodeId, newParentTreeId,
                            new NodeLabels("bomNode-x-modified-2", "title-modified-2", 2021, false))
                            .withChildren(
                                new NodeTreeDTO(childBomNodeId, newChildTreeId,
                                    new NodeLabels("childBomNode-modified-2", "child title-modified-2", 2021, true))
                                    .withBomEntryId(parentChildEntryId))));
            }
        }

        @Nested
        @DisplayName("and this can be modified")
        class ChangeFields {

            BranchViewDTO pushResult;
            ManufacturingTreeId newManufacturingTreeId;
            ManufacturingTreeId newChildManufacturingTreeId;

            @BeforeEach
            void change() {
                newManufacturingTreeId = manufacturingTreeNames.createNamed("newManufacturingTreeId");
                newChildManufacturingTreeId = manufacturingTreeNames.createNamed("newChildManufacturingTreeId");
                checkout(false, null);
                pushResult = apiPushChanges(checkoutUser, projectId, checkoutBranchId, checkoutResultDto.getChangeset().getId(),
                        pushChanges("changed-variant-name", "random trigger",
                            ChangeType.FIELD_CHANGE,
                            new NodeTreeDTO(bomNodeId, newManufacturingTreeId,
                                new NodeLabels("bomNode-x-modified-1", "title-modified-1", 2021, false))
                            .withChildren(
                                new NodeTreeDTO(childBomNodeId, newChildManufacturingTreeId,
                                    new NodeLabels("childBomNode-modified-1", "child title-modified-1", 2021, true)).withBomEntryId(parentChildEntryId))
                                ));
            }

            @Test
            @DisplayName("and it will have two new snapshots, with the expected treeIds")
            void verifyResult() {
                countBomNodes(2);

                verifyAllSnapshotsFor(bomNodeId, newManufacturingTreeId, st -> verifyUpdatedSnapshot("modified-1", bomNodeId, newManufacturingTreeId, st));

                var bomNodeTreeForBranch = getBranchStatus(checkoutUser, checkoutBranchId);
                verifyBomNodeTreeStatus(checkoutBranchId, "modified", bomNodeTreeForBranch,
                        "changed-variant-name", RANDOM_TRIGGER, ChangeType.FIELD_CHANGE);

                var root = bomNodeTreeForBranch.findByBomNodeId(bomNodeId);
                verifyNodeStatus(root, "rootId", bomNodeId, newManufacturingTreeId, 1, bomNodeId,
                        "title-modified-1", "bomNode-x-modified-1");

                var child = root.allChildren().get(0);
                var childNode = bomNodeTreeForBranch.findByBomNodeId(child.bomNodeId());
                verifyNodeStatus(childNode, "child", childBomNodeId, newChildManufacturingTreeId, 0,
                    childBomNodeId, "child title-modified-1", "childBomNode-modified-1");

            }
        }

        @Nested
        @DisplayName("and a new child can be added")
        class AddNewChild {

            BranchViewDTO pushResult;
            ManufacturingTreeId newManufacturingTreeId;
            ManufacturingTreeId child1ManufacturingTreeId;
            ManufacturingTreeId child2ManufacturingTreeId;
            BomNodeId child2BomNodeId;
            BomEntryId parent2ChildEntryId = new BomEntryId();

            @BeforeEach
            void setup() {
                child2BomNodeId = bomNodeNames.createNamed("child2BomNodeId");
                newManufacturingTreeId = manufacturingTreeNames.createNamed("newManufacturingTreeId");
                child1ManufacturingTreeId = manufacturingTreeNames.createNamed("child1ManufacturingTreeId");
                child2ManufacturingTreeId = manufacturingTreeNames.createNamed("child2ManufacturingTreeId");
            }

            void pushChangesWithChildren(BranchId branchId, ChangesetId previousChangesetId, String name, ManufacturingTreeId newManufacturingTreeId,
                    ManufacturingTreeId child1ManufacturingTreeId, ManufacturingTreeId child2ManufacturingTreeId) {
                pushResult = apiPushChanges(checkoutUser, projectId, branchId, previousChangesetId, pushChanges(name, "random trigger", ChangeType.FIELD_CHANGE,
                        new NodeTreeDTO(bomNodeId, newManufacturingTreeId, new NodeLabels("bomNode-x-" + name, "title-" + name, 2021, false))
                                .withChildren(List.of(
                                        new NodeTreeDTO(childBomNodeId, child1ManufacturingTreeId,
                                                new NodeLabels("childBomNode-" + name, "child title-" + name, 2021, true))
                                                        .withBomEntryId(parentChildEntryId),
                                        new NodeTreeDTO(child2BomNodeId, child2ManufacturingTreeId,
                                                new NodeLabels("child2BomNode-" + name, "child2 title-" + name, 2021, true))
                                                        .withBomEntryId(parent2ChildEntryId)))));
            }

            void addTwoChildren() {
                checkout(false, null);
                pushChangesWithChildren(checkoutBranchId, checkoutResultDto.getChangeset().getId(), "modified-1", newManufacturingTreeId,
                        child1ManufacturingTreeId, child2ManufacturingTreeId);
            }

            @Test
            @DisplayName("next to the current child, t will have two new snapshots, with the expected treeIds")
            void change() {
                addTwoChildren();

                // for the rootId, we already have 3 snapshots
                countBomNodes(3);

                verifyAllSnapshotsFor(bomNodeId, newManufacturingTreeId, st -> verifyUpdatedSnapshot("modified-1", bomNodeId, newManufacturingTreeId, st));
                verifyAllSnapshotsFor(childBomNodeId, child1ManufacturingTreeId, this::verifyChild1);

                verifyAllSnapshotsFor(child2BomNodeId, child2ManufacturingTreeId, this::verifyChild2);

                var bomNodeTreeForBranch = getBranchStatus(checkoutUser, checkoutBranchId);
                verifyBomNodeTreeStatus(checkoutBranchId, "modified", bomNodeTreeForBranch,
                        "modified-1", RANDOM_TRIGGER, ChangeType.FIELD_CHANGE);

                var root = verifyNodeStatus(bomNodeTreeForBranch, "rootId", bomNodeId, newManufacturingTreeId, 2, bomNodeId, "title-modified-1",
                        "bomNode-x-modified-1");

                assertEquals(2, root.allChildren().size(), "root.children.size");
                var child1 = find(bomNodeTreeForBranch.snapshots(), node -> "childBomNode-modified-1".equals(node.getName()));
                var child2 = find(bomNodeTreeForBranch.snapshots(), node -> "child2BomNode-modified-1".equals(node.getName()));
                verifyNodeStatus(child1,
                        "child",  childBomNodeId, child1ManufacturingTreeId, 0, childBomNodeId, "child title-modified-1", "childBomNode-modified-1");
                verifyNodeStatus(child2,
                        "child2",  child2BomNodeId, child2ManufacturingTreeId, 0, child2BomNodeId, "child2 title-modified-1", "child2BomNode-modified-1");

            }

            @Nested
            @DisplayName("we can archive snapshots")
            class ArchiveTest {

                @Test
                @DisplayName("also in a private user branch")
                void archiveTest() {
                    addTwoChildren();
                    var now = Instant.now();
                    var possibleIds = snapshotHistoryService.collectArchivableSnapshots(checkoutUser, now.minus(1, ChronoUnit.DAYS), now, 1000);
                    assertNotNull(possibleIds, "possibleIds");
                    var names = manufacturingTreeNames.lookupNames(possibleIds);
                    assertEquals(Set.of("newManufacturingTreeId", "child1ManufacturingTreeId", "child2ManufacturingTreeId"), names, "expected archivable ids");

                    archiveNodes(possibleIds);
                }

                private void archiveNodes(Set<ManufacturingTreeId> possibleIds) {
                    var removals = snapshotHistoryService.snapshotsRemoved(checkoutUser, possibleIds);
                    assertEquals(3, removals, "archived");
                    verifyAllSnapshotsFor(bomNodeId, newManufacturingTreeId, 1, this::archived);
                    verifyAllSnapshotsFor(childBomNodeId, child1ManufacturingTreeId, 1, this::archived);
                    verifyAllSnapshotsFor(child2BomNodeId, child2ManufacturingTreeId, 1, this::archived);
                }

                @Test
                @DisplayName("if the snapshots are on a global branch")
                void archiveTestWithGlobalBranch() {
                    addTwoChildren();
                    createNewPublicVariant(checkoutUser,
                        saveAsNewPublicVariant(pushResult.branch().id(), pushResult.changeset().id(), "public variant",
                            Trigger.from("Trigger")));
                    var now = Instant.now();
                    var possibleIds = snapshotHistoryService.collectArchivableSnapshots(checkoutUser, now.minus(1, ChronoUnit.DAYS), now, 1000);
                    assertNotNull(possibleIds, "possibleIds");
                    var names = manufacturingTreeNames.lookupNames(possibleIds);
                    assertEquals(Set.of(), names, "expected archivable ids");
                }

                @Test
                @DisplayName("if the snapshots are in a history of a global branch")
                void archiveTestWithGlobalBranchAndHistory() {
                    addTwoChildren();
                    pushChangesWithChildren(checkoutBranchId, pushResult.changeset().id(), "modified-2",
                        manufacturingTreeNames.createNamed("parent-modified-2"),
                        manufacturingTreeNames.createNamed("child-1-modified-2"),
                        manufacturingTreeNames.createNamed("child-2-modified-2"));

                    createNewPublicVariant(checkoutUser,
                        saveAsNewPublicVariant(pushResult.branch().id(), pushResult.changeset().id(), "public variant",
                            Trigger.from("Trigger")));
                    var now = Instant.now();
                    {
                        var possibleIds = snapshotHistoryService.collectArchivableSnapshots(checkoutUser, now.minus(1, ChronoUnit.DAYS), now, 1000);
                        assertNotNull(possibleIds, "possibleIds");
                        var names = manufacturingTreeNames.lookupNames(possibleIds);
                        assertEquals(Set.of("newManufacturingTreeId", "child1ManufacturingTreeId", "child2ManufacturingTreeId"), names, "expected archivable ids");

                        archiveNodes(possibleIds);
                    }
                    {
                        var nothing = snapshotHistoryService.collectArchivableSnapshots(checkoutUser, now.minus(1, ChronoUnit.DAYS), now, 1000);
                        assertNotNull(nothing, "nothing left to archieve");
                        assertEquals(Set.of(), nothing, "nothing left to archieve");
                    }
                }

                @DisplayName("if the snapshots in the source-changeset of a branch")
                @ParameterizedTest
                @ValueSource(booleans = {false, true})
                void globalBranchSourceChangesetShouldBeProtected(boolean global) {
                    addTwoChildren();
                    {
                        final var otherBranch = boms.checkoutMain(checkoutUser, projectId, checkoutBranchId, child2BomNodeId, false, "other-branch", RANDOM_TRIGGER);
                        assertNotNull(otherBranch, "otherBranch");
                        assertEquals(checkoutBranchId, otherBranch.branch().sourceId(), "otherBranch.sourceId");
                        assertEquals(ChangeType.CHECKOUT,otherBranch.changeset().type(), "otherBranch.changeset.type");
                        var nodeNames= bomNodeNames.lookupNames(of(otherBranch.snapshots()).map(NodeSnapshotDTO::bomNodeId).toList());
                        assertEquals(Set.of("child2BomNodeId", "bomNodeId"), nodeNames, "otherbranch.snapshots");

                        apiPushChanges(checkoutUser, projectId, otherBranch.branch().id(), otherBranch.branch().changesetId(),
                            pushChanges("child-change", "random trigger", ChangeType.FIELD_CHANGE,
                                    new NodeTreeDTO(child2BomNodeId, manufacturingTreeNames.createNamed("child-sub-branch-1"),
                                new NodeLabels("child-sub-branch-1", "title--sub-branch-1", 2021, false))));
                        if (global) {
                            var branchUpdate = branchService.setBranchGlobal(otherBranch.branch().id(), true, checkoutUser);
                            assertNotNull(branchUpdate, "branch");
                            assertTrue(branchUpdate.isPresent(), "branch.exists");
                            assertTrue(branchUpdate.orElseThrow().global(), "branch.global");
                        }
                    }

                    pushChangesWithChildren(checkoutBranchId, pushResult.changeset().id(), "modified-2",
                        manufacturingTreeNames.createNamed("parent-modified-2"),
                        manufacturingTreeNames.createNamed("child-1-modified-2"),
                        manufacturingTreeNames.createNamed("child-2-modified-2"));

                    createNewPublicVariant(checkoutUser,
                        saveAsNewPublicVariant(pushResult.branch().id(), pushResult.changeset().id(), "public variant",
                            Trigger.from("Trigger")));
                    var now = Instant.now();
                    {
                        var nothing = snapshotHistoryService.collectArchivableSnapshots(checkoutUser, now.minus(1, ChronoUnit.DAYS), now, 1000);
                        assertNotNull(nothing, "nothing left to archieve");
                        var names = manufacturingTreeNames.lookupNames(nothing);
                        if (global) {
                            assertEquals(Set.of(), names, "nothing left to archieve");
                        } else {
                            assertEquals(Set.of("newManufacturingTreeId", "child1ManufacturingTreeId", "child2ManufacturingTreeId", "child-sub-branch-1"),
                                    names, "The snapshots from the source changeset, and from the private branch can be deleted");
                        }
                    }
                }

                private void archived(SnapshotDTO node) {
                    assertNotNull(node, "node");
                    assertTrue(node.getArchived(), "node.archived");
                }
            }

            private void verifyChild1(SnapshotDTO child) {
                assertEquals("childBomNode-modified-1", child.getName(), "child.name");
                assertEquals(2021, child.getYear(), "snapshot.year");
                assertEquals("child title-modified-1", child.getTitle(), "snapshot.title");
                assertEquals(childBomNodeId, child.getBomNode().getId(), "snapshot.bomNode.id");
                assertTrue(child.getGenerated(), "snapshot.generated");

                assertNotNull(child.getPrevious(), "snapshot.previous");
                assertNotNull(child.getPrevious().getId(), "snapshot.previous.id");
            }

            private void verifyChild2(SnapshotDTO child) {
                assertEquals("child2BomNode-modified-1", child.getName(), "child.name");
                assertEquals(2021, child.getYear(), "snapshot.year");
                assertEquals("child2 title-modified-1", child.getTitle(), "snapshot.title");
                assertEquals(child2BomNodeId, child.getBomNode().getId(), "snapshot.bomNode.id");
                assertTrue(child.getGenerated(), "snapshot.generated");

                // The snapshot/bomnode is just created
                assertNull(child.getPrevious(), "snapshot.previous");
            }

        }

        void verifyUpdatedSnapshot(String prefix, BomNodeId bomNodeId, ManufacturingTreeId treeId, SnapshotDTO snapshot) {
            assertEquals("bomNode-x-" + prefix, snapshot.getName(), "snapshot.name");
            assertEquals(2021, snapshot.getYear(), "snapshot.year");
            assertEquals("title-" + prefix, snapshot.getTitle(), "snapshot.title");
            assertEquals(bomNodeId, snapshot.getBomNode().getId(), "snapshot.bomNode.id");
            assertEquals(false, snapshot.getGenerated(), "snapshot.generated");

            assertNotNull(snapshot.getPrevious(), "snapshot.previous");
            assertNotNull(snapshot.getPrevious().getId(), "snapshot.previous.id");
            assertEquals(treeId, snapshot.getManufacturingTreeId(), "snapshot.manufacturingTreeId");
        }
    }

}
