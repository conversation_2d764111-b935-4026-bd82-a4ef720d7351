package com.nu.bomrads.service.history;


import com.nu.bomrads.domain.BomNode;
import com.nu.bomrads.domain.Changeset;
import com.nu.bomrads.domain.Relation;
import com.nu.bomrads.domain.Snapshot;
import com.nu.bomrads.enumeration.RelationType;
import com.nu.bomrads.id.SnapshotId;
import com.nu.bomrads.service.history.MergeService.NewEntities;
import com.nu.bomrads.service.history.MergeService.SourceChanged;
import com.nu.bomrads.service.history.MergeService.UseBranch;
import com.nu.bomrads.service.utils.BomNodeNaming;
import com.nu.bomrads.service.utils.ChangesetNaming;
import com.nu.bomrads.service.utils.ManufacturingNaming;
import io.soabase.recordbuilder.core.RecordBuilder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;

import static com.nu.bomrads.utils.StreamUtils.of;
import static java.util.List.of;
import static java.util.Objects.requireNonNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

class MergeServiceTest extends BomUnitTest {
    final static Logger LOG = LoggerFactory.getLogger(MergeServiceTest.class);

    enum BranchUsage {
        CURRENT, SOURCE
    }

    enum RecalcExpected {
        YES, NO;
        boolean isTrue() {
            return this == YES;
        }
    }

    enum Root {
        YES, NO;
        boolean isTrue() {
            return this == YES;
        }
    }

    MergeService service;
    BomNodeNaming nodeNames = new BomNodeNaming();
    ManufacturingNaming manufacturingNames = new ManufacturingNaming();
    ChangesetNaming changesetNaming = new ChangesetNaming();
    Changeset source;
    Changeset branch;
    Changeset newChangeset;

    @BeforeEach
    void setupTest() {
        service = new MergeService(branchStateService, entitySaveService, branchModification(entitySaveService()));
        nodeNames.clear();
        manufacturingNames.clear();
        changesetNaming.clear();
        source = new Changeset().id(changesetNaming.createNamed("source"));
        branch = new Changeset().id(changesetNaming.createNamed("branch"));
        newChangeset = new Changeset().id(changesetNaming.createNamed("newChangeset"));
    }

    private record TestCase(String name, List<NodeSetup> nodes) {
        TestCase(String name, NodeSetup... nodeSetups) {
            this(name, of(nodeSetups));
        }
    }

    @RecordBuilder
    record NodeSetup(String name, BranchUsage useBranch, List<String> childrens, RecalcExpected expectedToRecalc, Root root, boolean deletion, boolean expectedToBeDeleted) implements MergeServiceTestNodeSetupBuilder.With {

        NodeSetup(String name, BranchUsage useBranch, List<String> childrens) {
            this(name, useBranch, childrens, RecalcExpected.NO, Root.NO, false, false);
        }

        String getExpectedPreviousManufacturingName() {
            return useBranch == BranchUsage.CURRENT ? "manuf-" + name : "manuf-source-" + name;
        }

        String getExpectedManufacturingName() {
            if (expectedToRecalc == RecalcExpected.YES) {
                return null;
            } else {
                return useBranch == BranchUsage.CURRENT  ? "manuf-" + name : "manuf-source-" + name;
            }
        }

        NodeSetup asRoot() {
            return withRoot(Root.YES);
        }

        NodeSetup asDeleted() {
            return withDeletion(true).withExpectedToBeDeleted(true);
        }

        NodeSetup andRecalcExpected() {
            return withExpectedToRecalc(RecalcExpected.YES);
        }
    }

    @ParameterizedTest
    @MethodSource("arguments")
    void simpleParentChild(TestCase testCase) {
        runTestCase(testCase.name(), testCase.nodes());
    }

    static List<TestCase> arguments() {
        return of(
            new TestCase("everything from the branch",
                new NodeSetup("A", BranchUsage.CURRENT, List.of("B", "C")).asRoot(),
                new NodeSetup("B", BranchUsage.CURRENT, List.of()),
                new NodeSetup("C", BranchUsage.CURRENT, List.of())),
            new TestCase("B comes from the source",
                new NodeSetup("A", BranchUsage.CURRENT, List.of("B", "C")).asRoot().andRecalcExpected(),
                new NodeSetup("B", BranchUsage.SOURCE, List.of()).andRecalcExpected(),
                new NodeSetup("C", BranchUsage.CURRENT, List.of())),
            new TestCase("Just A comes from the branch",
                new NodeSetup("A", BranchUsage.CURRENT, List.of("B", "C")).asRoot().andRecalcExpected(),
                new NodeSetup("B", BranchUsage.SOURCE, List.of()).andRecalcExpected(),
                new NodeSetup("C", BranchUsage.SOURCE, List.of()).andRecalcExpected()),
            new TestCase("Everything from the source",
                new NodeSetup("A", BranchUsage.SOURCE, List.of("B", "C")).asRoot(),
                new NodeSetup("B", BranchUsage.SOURCE, List.of()),
                new NodeSetup("C", BranchUsage.SOURCE, List.of())),
            new TestCase("Common child scenario",
                new NodeSetup("A", BranchUsage.CURRENT, List.of("B", "C")).asRoot(),
                new NodeSetup("B", BranchUsage.CURRENT, List.of()),
                new NodeSetup("C", BranchUsage.CURRENT, List.of()),
                new NodeSetup("D", BranchUsage.CURRENT, List.of("B")).asRoot()),
            new TestCase("Common child comes from source",
                new NodeSetup("A", BranchUsage.CURRENT, List.of("B", "C")).asRoot().andRecalcExpected(),
                new NodeSetup("B", BranchUsage.SOURCE, List.of()).andRecalcExpected(),
                new NodeSetup("C", BranchUsage.CURRENT, List.of()),
                new NodeSetup("D", BranchUsage.CURRENT, List.of("B")).asRoot().andRecalcExpected()),
            new TestCase("Other child comes from source",
                new NodeSetup("A", BranchUsage.CURRENT, List.of("B", "C")).asRoot().andRecalcExpected(),
                new NodeSetup("B", BranchUsage.CURRENT, List.of()),
                new NodeSetup("C", BranchUsage.SOURCE, List.of()).andRecalcExpected(),
                new NodeSetup("D", BranchUsage.CURRENT, List.of("B")).asRoot()),
            new TestCase("2 Parents and child scenario - child comes from source",
                new NodeSetup("A", BranchUsage.CURRENT, List.of("C")).asRoot().andRecalcExpected(),
                new NodeSetup("B", BranchUsage.CURRENT, List.of("C")).asRoot().andRecalcExpected(),
                new NodeSetup("C", BranchUsage.SOURCE, List.of()).andRecalcExpected()),
            new TestCase("2 Parents and child scenario - one parent comes from source",
                new NodeSetup("A", BranchUsage.CURRENT, List.of("C")).asRoot().andRecalcExpected(),
                new NodeSetup("B", BranchUsage.SOURCE, List.of("C")).asRoot().andRecalcExpected(),
                new NodeSetup("C", BranchUsage.CURRENT, List.of()).andRecalcExpected()),

            new TestCase("Parent-child-grand children scenario - plus an independent node",
                new NodeSetup("A", BranchUsage.CURRENT, List.of("B")).asRoot(),
                new NodeSetup("B", BranchUsage.CURRENT, List.of("C")),
                new NodeSetup("C", BranchUsage.CURRENT, List.of()),
                new NodeSetup("D", BranchUsage.SOURCE, List.of()).asRoot()),
            new TestCase("Parent-child-grand children scenario - grand child changed",
                new NodeSetup("A", BranchUsage.CURRENT, List.of("B")).asRoot().andRecalcExpected(),
                new NodeSetup("B", BranchUsage.CURRENT, List.of("C")).andRecalcExpected(),
                new NodeSetup("C", BranchUsage.SOURCE, List.of()).andRecalcExpected()),
            new TestCase("Parent-child-grand children scenario - child changed",
                new NodeSetup("A", BranchUsage.CURRENT, List.of("B")).asRoot().andRecalcExpected(),
                new NodeSetup("B", BranchUsage.SOURCE, List.of("C")).andRecalcExpected(),
                new NodeSetup("C", BranchUsage.CURRENT, List.of()).andRecalcExpected()),
            new TestCase("Parent-child-grand children scenario - parent changed",
                new NodeSetup("A", BranchUsage.SOURCE, List.of("B")).asRoot().andRecalcExpected(),
                new NodeSetup("B", BranchUsage.CURRENT, List.of("C")).andRecalcExpected(),
                new NodeSetup("C", BranchUsage.CURRENT, List.of()).andRecalcExpected()),
            new TestCase("Parent-child-grand children scenario - grand child deleted",
                new NodeSetup("A", BranchUsage.CURRENT, List.of("B")).asRoot(),
                new NodeSetup("B", BranchUsage.CURRENT, List.of("C")),
                new NodeSetup("C", BranchUsage.CURRENT, List.of()).asDeleted()),
            new TestCase("Parent-child-grand children scenario - child deleted",
                new NodeSetup("A", BranchUsage.CURRENT, List.of("B")).asRoot(),
                new NodeSetup("B", BranchUsage.CURRENT, List.of("C")).asDeleted(),
                new NodeSetup("C", BranchUsage.CURRENT, List.of()).withExpectedToBeDeleted(true)),
            new TestCase("Parent-child-grand children scenario - parent deleted",
                new NodeSetup("A", BranchUsage.CURRENT, List.of("B")).asRoot().asDeleted(),
                new NodeSetup("B", BranchUsage.CURRENT, List.of("C")).withExpectedToBeDeleted(true),
                new NodeSetup("C", BranchUsage.CURRENT, List.of()).withExpectedToBeDeleted(true)),
            new TestCase("Parent-child-grand children scenario - child deleted in the source",
                new NodeSetup("A", BranchUsage.CURRENT, List.of("B")).asRoot().andRecalcExpected(),
                new NodeSetup("B", BranchUsage.SOURCE, List.of("C")).asDeleted().andRecalcExpected(),
                new NodeSetup("C", BranchUsage.CURRENT, List.of()).withExpectedToBeDeleted(true).andRecalcExpected()),
            new TestCase("2 Parents and child scenario - child deleted",
                new NodeSetup("A", BranchUsage.CURRENT, List.of("C")).asRoot().andRecalcExpected(),
                new NodeSetup("B", BranchUsage.CURRENT, List.of("C")).asRoot().andRecalcExpected(),
                new NodeSetup("C", BranchUsage.SOURCE, List.of()).asDeleted().andRecalcExpected()),
            new TestCase("2 Parents and child scenario - one parent deleted",
                new NodeSetup("A", BranchUsage.CURRENT, List.of("C")).asRoot().andRecalcExpected(),
                new NodeSetup("B", BranchUsage.SOURCE, List.of("C")).asRoot().asDeleted().andRecalcExpected(),
                new NodeSetup("C", BranchUsage.CURRENT, List.of()).andRecalcExpected())
        );
    }

    private void runTestCase(String description, List<NodeSetup> nodeSetupList) {
        final var nodeSetupMap = of(nodeSetupList).toMap(NodeSetup::name);
        final var newEntities = of(nodeSetupList).map(this::toNewEntities).toList();
        final var result = service.buildTree(newEntities);
        for (var node : result.treeNodes()) {
            final var name = nodeNames.getName(node.bomNodeId());
            LOG.info("result for {} -> {}", name, node);
            assertNotNull(name, "expected to have name: " + node);
            final var nodeSetup = requireNonNull(nodeSetupMap.get(name), "node-setup-expected: "+ name);
            final var snapshot = node.entity.newSnapshot();
            final var manufTreeId = snapshot.getManufacturingTreeId();
            LOG.info("manufacturing {} -> {}", name, manufTreeId);
            assertEquals(nodeSetup.expectedToRecalc.isTrue(), node.isNeedRecalculation(),
                "node " + name + " recalculation needed in " + description);
            if (nodeSetup.expectedToRecalc.isTrue()) {
                assertNull(manufTreeId, "getManufacturingTreeId is null for " + name + " in " + description);
            } else {
                manufacturingNames.assertName(nodeSetup.getExpectedManufacturingName(), manufTreeId, "getManufacturingTreeId exists for " + nodeSetup + " in " + description);
            }
            manufacturingNames.assertName(nodeSetup.getExpectedPreviousManufacturingName(), snapshot.getPreviousManufacturingTreeId(),
                "previous manuf tree for " + nodeSetup + " in " + description);

            changesetNaming.assertName("newChangeset", snapshot.getChangeset().getId());
            assertEquals(nodeSetup.expectedToBeDeleted, snapshot.isDeleted(), "snapshot is deleted for " + name + " in " + description);
        }
        assertEquals(Collections.emptySet(), result.externalChildrens(), "external children set is empty" );
    }

    NewEntities toNewEntities(NodeSetup nodeSetup) {
        final var bomNodeId = nodeNames.getOrCreateFromName(nodeSetup.name());
        final var node = new BomNode().id(bomNodeId);
        var branchSnapshot = newSnapshot(node, nodeSetup.name(), branch).root(nodeSetup.root.isTrue());
        var mergeCheckResult = switch (nodeSetup.useBranch) {
            case CURRENT -> new UseBranch(false, branchSnapshot.deleted(nodeSetup.deletion), null);
            case SOURCE -> new SourceChanged(true, null, branchSnapshot,
                newSnapshot(node, "source-" + nodeSetup.name(), source)
                    .deleted(nodeSetup.deletion)
                    .root(nodeSetup.root.isTrue())
                    );
        };
        var relations = of(nodeSetup.childrens).map(childName -> {
            var childId = nodeNames.getOrCreateFromName(childName);
            final var child = new BomNode().id(childId);
            return new Relation().parent(node).child(child).relationType(RelationType.INTERNAL);
        }).toList();
        return new NewEntities(mergeCheckResult,
            mergeCheckResult.markMerged(newChangeset, source), relations);
    }

    Snapshot newSnapshot(BomNode node, String name, Changeset changeset) {
        final var manufName = "manuf-" + name;
        final var prevManufName = "prev-manuf-" + name;
        final var manufId = manufacturingNames.getOrCreateFromName(manufName);
        final var prevManufId = manufacturingNames.getOrCreateFromName(prevManufName);
        LOG.info("snapshot for {} -> manu id= {}:{} prev ={}:{}", node.getId(), manufName, manufId, prevManufName, prevManufId);

        return new Snapshot()
            .id(new SnapshotId())
            .bomNode(node).calculationRoot(node).title(name).name(name)
            .changeset(changeset)
            .manufacturingTreeId(manufId)
            .previousManufacturingTreeId(prevManufId);
    }
}
