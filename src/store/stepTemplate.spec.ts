import stepTemplate from '@/store/stepTemplate'
import { useDisplayCurrency } from '@tset/shared-utils/api/useDisplayCurrency'
import { withAxiosMock } from '@tset/shared-utils/tests/mocks/withAxiosMock'
import { withModalsMock } from '@tset/shared-utils/tests/mocks/withModalsMock'
import { getVuexModuleInstance } from '@tset/shared-utils/tests/wrapVuexModule'

const { setDisplayCurrency } = useDisplayCurrency()
setDisplayCurrency('CAD')

const { getLastCall, setMockResponse } = withAxiosMock()
withModalsMock()

vi.mock('@tset/shared-utils/helpers/table', () => ({
  ICON_TYPES: {},
  hasUnitColumn: () => true,
}))

vi.mock('@/store', () => ({
  navigationStore: { navigateTo: () => {} },
  userStore: { displayCurrency: 'CAD' },
}))

// TODO: remove axios tests
describe('stepTemplate store: $axios calls check', () => {
  it('.getConfigurations()', async () => {
    const { stepTemplateStore } = setupAxiosTest()
    await stepTemplateStore.getConfigurations('template_key')
    expect(getLastCall('get')).toEqual([
      '/api/knowledge/templates/template_key/configurations',
    ])
  })

  it('.fetchFields()', async () => {
    const { stepTemplateStore } = setupAxiosTest()
    await stepTemplateStore.fetchFields()
    expect(getLastCall('get')).toEqual([
      '/api/helpers/knowledge/templates/configurations/entities',
      {
        params: {
          configurationKey: 'conf_id',
          entityClass: 'entity_class',
          entityType: 'TEMPLATE',
          masterdataKey: 'comp_key',
          masterdataType: 'comp_type',
          parentKey: 'parent_key',
          parentType: 'parent_type',
          templateKey: 'template_key',
        },
      },
    ])
  })

  describe('.addObject()', async () => {
    it('entityType: TEMPLATE', async () => {
      const { stepTemplateStore } = setupAxiosTest({ entityType: 'TEMPLATE' })
      await stepTemplateStore.addObject()
      expect(getLastCall('post')).toEqual([
        '/api/knowledge/templates/template_key/configurations/conf_id/entities',
        {
          currency: 'CAD',
          entityClass: 'entity_class',
          entityType: 'TEMPLATE',
          fields: [],
          masterDataKey: { key: 'comp_key', type: 'comp_type' },
          parentKey: 'parent_key',
        },
      ])
    })

    it('entityType: MANUFACTURING_STEP_CONFIGURATION', async () => {
      const { stepTemplateStore } = setupAxiosTest({
        entityType: 'MANUFACTURING_STEP_CONFIGURATION',
      })
      await stepTemplateStore.addObject()
      expect(getLastCall('post')).toEqual([
        '/api/knowledge/templates/template_key/configurations',
        { fields: [] },
      ])
    })

    it('entityType: MANUFACTURING_STEP_TEMPLATE', async () => {
      const { stepTemplateStore } = setupAxiosTest({
        entityType: 'MANUFACTURING_STEP_TEMPLATE',
      })
      await stepTemplateStore.addObject()
      expect(getLastCall('post')).toEqual([
        '/api/knowledge/templates',
        { fields: [] },
      ])
    })
  })

  it('.getTemplateWithKey()', async () => {
    const { stepTemplateStore } = setupAxiosTest()
    await stepTemplateStore.getTemplateWithKey('template_key')
    expect(getLastCall('get')).toEqual([
      '/api/knowledge/templates/template_key',
    ])
  })

  it('.getConfigurationWithKey()', async () => {
    const { stepTemplateStore } = setupAxiosTest()
    await stepTemplateStore.getConfigurationWithKey({
      templateKey: 'template_key',
      configurationKey: 'conf_key',
    })
    expect(getLastCall('get')).toEqual([
      '/api/knowledge/templates/template_key/configurations/conf_key',
    ])
  })

  it('.deleteConfiguration()', async () => {
    const { stepTemplateStore } = setupAxiosTest()
    await stepTemplateStore.deleteConfiguration('conf_key')
    expect(getLastCall('delete')).toEqual([
      '/api/knowledge/templates/template_key/configurations/conf_key',
    ])
  })

  it('.restoreTsetConfiguration()', async () => {
    const { stepTemplateStore } = setupAxiosTest()
    await stepTemplateStore.restoreTsetConfiguration()
    expect(getLastCall('post')).toEqual([
      '/api/knowledge/templates/template_key/configurations/conf_id/restore',
    ])
  })

  it('.patchConfiguration()', async () => {
    const { stepTemplateStore } = setupAxiosTest()
    await stepTemplateStore.patchConfiguration({
      name: 'field_name',
      value: 'field_value',
    } as ResultField)
    expect(getLastCall('patch')).toEqual([
      '/api/knowledge/templates/template_key/configurations/conf_id',
      {
        field: { name: 'field_name', value: 'field_value' },
      },
    ])
  })

  it('.saveConfiguration()', async () => {
    const { stepTemplateStore } = setupAxiosTest()
    await stepTemplateStore.saveConfiguration()
    expect(getLastCall('post')).toEqual([
      '/api/knowledge/templates/template_key/configurations/conf_id/save',
    ])
  })

  it('.saveAsConfiguration()', async () => {
    const { stepTemplateStore } = setupAxiosTest()
    await stepTemplateStore.saveAsConfiguration({
      name: 'field_name',
      value: 'field_value',
    } as ResultField)
    expect(getLastCall('post')).toEqual([
      '/api/knowledge/templates/template_key/configurations/conf_id/save-as',
      {
        field: { name: 'field_name', value: 'field_value' },
      },
    ])
  })

  it('.deleteTemplate()', async () => {
    const { stepTemplateStore } = setupAxiosTest()
    await stepTemplateStore.deleteTemplate('template_key')
    expect(getLastCall('delete')).toEqual([
      '/api/knowledge/templates/template_key',
    ])
  })

  it('.restoreTsetTemplate()', async () => {
    const { stepTemplateStore } = setupAxiosTest()
    await stepTemplateStore.restoreTsetTemplate()
    expect(getLastCall('post')).toEqual([
      '/api/knowledge/templates/template_key/restore',
    ])
  })

  it('.patchTemplate()', async () => {
    const { stepTemplateStore } = setupAxiosTest()
    await stepTemplateStore.patchTemplate({
      name: 'field_name',
      value: 'field_value',
    } as ResultField)
    expect(getLastCall('patch')).toEqual([
      '/api/knowledge/templates/template_key',
      {
        field: { name: 'field_name', value: 'field_value' },
      },
    ])
  })

  it('.saveTemplate()', async () => {
    const { stepTemplateStore } = setupAxiosTest()
    await stepTemplateStore.saveTemplate()
    expect(getLastCall('post')).toEqual([
      '/api/knowledge/templates/template_key/save',
    ])
  })

  it('.saveAsTemplate()', async () => {
    const { stepTemplateStore } = setupAxiosTest()
    await stepTemplateStore.saveAsTemplate({
      name: 'field_name',
      value: 'field_value',
    } as ResultField)
    expect(getLastCall('post')).toEqual([
      '/api/knowledge/templates/template_key/save-as',
      {
        field: { name: 'field_name', value: 'field_value' },
      },
    ])
  })

  it('.deleteEntity()', async () => {
    const { stepTemplateStore } = setupAxiosTest()
    await stepTemplateStore.deleteEntity('entity_id')
    expect(getLastCall('delete')).toEqual([
      '/api/knowledge/templates/template_key/configurations/conf_id/entities/entity_id',
    ])
  })

  it('.uploadFiles()', async () => {
    const { stepTemplateStore } = setupAxiosTest()
    await stepTemplateStore.uploadFiles([
      { file: 'file_1' },
      { file: 'file_2' },
    ])
    expect(getLastCall('post')).toEqual([
      '/api/knowledge/templates/template_key/attachments',
      expect.any(FormData),
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    ])
  })

  it('.fetchAttachments()', async () => {
    const { stepTemplateStore } = setupAxiosTest()
    await stepTemplateStore.fetchAttachments()
    expect(getLastCall('get')).toEqual([
      '/api/knowledge/templates/template_key/attachments',
    ])
  })

  it('.deleteAttachment()', async () => {
    const { stepTemplateStore } = setupAxiosTest()
    await stepTemplateStore.deleteAttachment('attachment_id')
    expect(getLastCall('delete')).toEqual([
      '/api/knowledge/templates/template_key/attachments/attachment_id',
    ])
  })

  it('.discardTemplate()', async () => {
    const { stepTemplateStore } = setupAxiosTest()
    await stepTemplateStore.discardTemplate(false)
    expect(getLastCall('post')).toEqual([
      '/api/knowledge/templates/template_key/discard',
    ])
  })

  it('.discardConfiguration()', () => {
    const { stepTemplateStore } = setupAxiosTest()
    stepTemplateStore.discardConfiguration(false)
    expect(getLastCall('post')).toEqual([
      '/api/knowledge/templates/template_key/configurations/conf_id/discard',
    ])
  })
})

function setupAxiosTest({ response = {}, entityType = 'TEMPLATE' } = {}) {
  const stepTemplateStore = getVuexModuleInstance<stepTemplate>(
    stepTemplate,
    'stepTemplate'
  )
  stepTemplateStore.setConfiguration({ key: 'conf_id' } as Configuration)
  stepTemplateStore.setEntityClass('entity_class')
  stepTemplateStore.setSelectedSearchItem({
    composite: { key: 'comp_key', type: 'comp_type' } as Composite,
  })
  stepTemplateStore.setParentType('parent_type')
  stepTemplateStore.setParentKey('parent_key')
  stepTemplateStore.setTemplate({ key: 'template_key' } as Template)
  stepTemplateStore.setEntityType(entityType as ManufacturingEntityType)

  setMockResponse(response)

  return { stepTemplateStore }
}
