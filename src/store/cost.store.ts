import { getPiniaInstance } from '@/piniaInstance'
import { projectStore } from '@domain/project/data/project.store'
import { isProjectReadonly } from '@domain/project/utils/project'
import { defineStore } from 'pinia'
import { computed } from 'vue'

// TODO this store needs to die
const loadedProject = computed<Nullable<ProjectFolderNode>>(
  () => projectStore.loadedProject ?? null
)
const isReadonlyMode = computed<boolean>(() =>
  loadedProject.value ? isProjectReadonly(loadedProject.value) : true
)

/* GLOBAL STORE FOR THE COST APPLICATION */
export const useCostStore = defineStore('cost', () => {
  return {
    /** project */
    isReadonlyMode,
    loadedProject,
    clearProject: projectStore.clearProject,
    fetchDefaultProject: projectStore.fetchDefaultProject,
    initProject: projectStore.initProject,
    setProject: projectStore.setProject,
    updateProjectName: projectStore.updateProjectName,
  }
})

export const costStore = useCostStore(getPiniaInstance())
