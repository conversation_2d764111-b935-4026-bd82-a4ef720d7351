CREATE VIEW IF NOT EXISTS snapshot_cleanup_mat_vw
AS
 SELECT
    s1.id,
    s1.manufacturing_tree_id,
    s1.account_id,
    b.project_id
FROM
    snapshot s1
JOIN bom_node b ON
    s1.bom_node_id = b.id
JOIN changeset c ON
    s1.changeset_id = c.id
WHERE
    s1.account_id = b.account_id
    AND c.created <= (now() - INTERVAL '2' DAY)
    AND c.account_id = b.account_id
    AND s1.manufacturing_tree_id IS NOT NULL
    AND s1.archived = FALSE
    AND b.last_modified > (now() - INTERVAL '100' DAY)
    AND NOT (EXISTS (
    SELECT
        s2.manufacturing_tree_id
    FROM
        snapshot s2
    CROSS JOIN branch br
    CROSS JOIN project p
    WHERE
        p.deleted_at IS NULL
        AND s2.account_id = b.account_id
        AND s2.deleted = FALSE
        AND (br.current_changeset_id = s2.changeset_id
            OR br.source_changeset_id = s2.changeset_id)
        AND br.account_id = b.account_id
        AND (br.global = TRUE
            OR br.main = TRUE)
        AND br.deleted = FALSE
        AND br.published = FALSE
        AND s2.manufacturing_tree_id = s1.manufacturing_tree_id
        AND p.id = br.project_id
        AND p.account_id = b.account_id))
