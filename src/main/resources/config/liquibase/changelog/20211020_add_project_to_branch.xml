<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.6.xsd">

    <changeSet id="20211020_01_add_project_id_to_branch" author="Zsombor Gegesy">
        <addColumn tableName="branch">
            <column name="project_id" type="${uuidType}">
                <constraints nullable="true" />
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="20211020_02_add_project_fk_to_branch" author="Zsombor Gegesy">
        <addForeignKeyConstraint baseColumnNames="project_id"
                                 baseTableName="branch"
                                 constraintName="fk_branch_project_id"
                                 referencedColumnNames="id"
                                 referencedTableName="project"/>
    </changeSet>
    <changeSet id="20211020_03_backfill_project_id_in_branch_table" author="Zsombor Gegesy">
        <update tableName="branch">
            <column name="project_id" valueComputed="(select b.project_id from bom_node b where b.id = root_id)"/>
            <where>project_id is null</where>
        </update>
    </changeSet>
    <changeSet id="20211020_04_project_id_is_required" author="Zsombor Gegesy">
        <addNotNullConstraint 
            columnDataType="${uuidType}"
            columnName="project_id" 
            tableName="branch"/> 
    </changeSet>
</databaseChangeLog>