spring:
  application:
    name: nu-masterdata
  boot:
    admin:
      client:
        url: https://admin.tset.cloud
        username: admin
        password: xeev1ar7eiF<PERSON>0sheelojeuquoo9Gai1
        instance:
          metadata:
            tags:
              environment: cloud
  sql:
    init:
      mode: never
      platform: postgres
  r2dbc:
    pool:
      max-size: 35
      max-acquire-time: PT1M
  flyway:
    enabled: false
  reactor:
    context-propagation: auto

postgres:
  schema: "public"
  sslmode: ${POSTGRES_SSLMODE}
  username: ${POSTGRES_USER}
  password: ${POSTGRES_PASSWORD}
  host: ${POSTGRES_HOST}
  port: 5432
  database: data
  statement-timeout: PT5M

keycloak:
  base-url: https://id.develop.tset.cloud

nu-masterdata:
  defaultTenant: account_default
  runMigrationOnStartup: false
  autoProvision: true
  validIssuerHosts: id.testing.tset.cloud,keycloak-identity.develop.svc.cluster.local,id.develop.tset.cloud,auth-keycloak.identity.svc.cluster.local,keycloak-identity.identity.svc.cluster.local,id.develop.tset.cloud,id.tset.com,id.tc.tset.com,id.zf.tset.com,id.bmw.tset.com,id.tset-staging.tset.cloud,id.stellantis.tset.com

springdoc:
  api-docs:
    enabled: true
    path: /api/md/v3/api-docs
  swagger-ui:
    enabled: true
    path: /api/md/swagger-ui.html
    urlsPrimaryName: v1
  model-converters:
    polymorphic-converter:
      # If this is true, oneOf schemas are substitutes with inline schemas.
      # This is kind of good because it allows polymorphism and inheritance without manual overhead.
      # But for code generation it leads to badly-named schemas.
      # We instead opt for a similar solution, where we substitute the schema with a dedicated non-inline schema.
      enabled: false

nu:
  headers:
    session-id: X-TSET-SESSION-ID
    request-id: X-TSET-REQUEST-ID
    env-id: X-TSET-SUB-ENV
    fallback-env-id: X-TSET-FALLBACK-SUB-ENV
    forwarded-for: forwarded_for_ip
  multitenant:
    account-claim: nu_account_claim
    username-claim: preferred_username

management:
  server:
    port: 8088
  security:
    enabled: false
  endpoints:
    web:
      base-path: /management
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
      group:
        liveness.include:
          - livenessState
          - r2dbc
        readiness.include:
          - readinessState
          - r2dbc
    jhimetrics:
      enabled: true
    loggers:
      enabled: true
  info:
    enabled: true
    git:
      mode: full
    mail:
      enabled: false # When using the MailService, configure an SMTP server and set this to true
    redis:
      enabled: false
  metrics:
    enable:
      http: true
      jvm: true
      logback: true
      process: true
      system: true
    distribution:
      percentiles-histogram:
        all: true
      percentiles:
        all: 0, 0.5, 0.75, 0.95, 0.99, 1.0
    tags:
      application: ${spring.application.name}
    web:
      server:
        request:
          autotime:
            enabled: true
  prometheus:
    metrics:
      export:
        enabled: true
        step: 60
  tracing:
    enabled: true
    propagation:
      type: w3c,b3
    baggage:
      enabled: true
      remote-fields:
        - ${nu.headers.session-id}
        - ${nu.headers.request-id}
        - ${nu.multitenant.account-claim}
        - ${nu.multitenant.username-claim}
        - ${nu.headers.forwarded-for}
        - ${nu.headers.env-id}
        - ${nu.headers.fallback-env-id}
      correlation:
        fields:
          - ${nu.headers.session-id}
          - ${nu.headers.request-id}
          - ${nu.multitenant.account-claim}
          - ${nu.multitenant.username-claim}
          - ${nu.headers.forwarded-for}
          - ${nu.headers.env-id}
          - ${nu.headers.fallback-env-id}
