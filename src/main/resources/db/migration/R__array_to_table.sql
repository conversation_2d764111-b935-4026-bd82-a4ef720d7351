drop function if exists ${flyway:defaultSchema}.array_to_table(
    effectivityidforfilter uuid,
    requestlist ${flyway:defaultSchema}.request[]
    );

create or replace function ${flyway:defaultSchema}.array_to_table(
    effectivityidforfilter uuid,
    requestlist ${flyway:defaultSchema}.request[]
) returns TABLE(requestid integer,
                headerid uuid,
                detailvaluetype text,
                effectivityid uuid,
                effectivitytype character varying,
                datevalue date,
                referenceid uuid,
                classificationid uuid,
                numericvalue double precision,
                distance double precision,
                datefallback date,
                numericfallback double precision)
    language plpgsql
as
$$
DECLARE

BEGIN
    return query select r.requestId, r.headerId, r.detailValueType, r.effectivityId, r.effectivityType, r.dateValue,
                        r.referenceId, r.classificationId, r.numericValue, r.distance,
                        r.dateFallback, r.numericFallback
                 from (select x.requestId, x.headerId, x.detailValueType, x.effectivityId, x.effectivityType, x.dateValue,
                              x.referenceId, x.classificationId, x.numericValue, x.distance,
                              x.dateFallback, x.numericFallback
                       from unnest(requestList)
                                as x(requestId, headerId, detailValueType, effectivityId, effectivityType, dateValue,
                                     referenceId, classificationId, numericValue, distance, dateFallback, numericFallback))
                          as r(requestId, headerId, detailValueType, effectivityId, effectivityType, dateValue,
                               referenceId, classificationId, numericValue, distance, dateFallback, numericFallback)

                 where r.effectivityId = effectivityIdForFilter
    ;
END;
$$;


