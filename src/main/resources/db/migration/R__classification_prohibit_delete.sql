CREATE OR REPLACE FUNCTION ${flyway:defaultSchema}.classification_prohibit_delete() RETURNS TRIGGER LANGUAGE 'plpgsql' AS
$$
BEGIN
    IF EXISTS (
        select 1
        from ${flyway:defaultSchema}.detail as d
                 inner join ${flyway:defaultSchema}.header as h on d.header_id = h.id
                 inner join ${flyway:defaultSchema}.header_type as ht on h.header_type_id = ht.id
                 inner join ${flyway:defaultSchema}.effectivity as e on ht.id = e.header_type_id
                 inner join ${flyway:defaultSchema}.field_definition as fd on e.field_definition_id = fd.id
            and fd.type = 'classification'
        where jsonb_extract_path_text(d.key,'effectivities', e.id::text, 'value') = old.id::text
    ) THEN
        RAISE EXCEPTION USING ERRCODE = 'NU002',
            MESSAGE = 'The deletion of a classification that is used as an effectivity is not allowed!' ;
    ELSE
        return OLD;
    END IF;
END;
$$
;

CREATE or replace TRIGGER classification_effectivity_usage_check
    BEFORE DELETE ON ${flyway:defaultSchema}.classification
    FOR EACH ROW
EXECUTE PROCEDURE ${flyway:defaultSchema}.classification_prohibit_delete();
