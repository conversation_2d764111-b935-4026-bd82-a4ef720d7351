insert into dimension(id, key, name)
    values('8fe6048c-6763-4f2a-8d26-aed402b3a96f', '{ "key": "dimension-turnover", "type": "header.key.simple"}', 'turnover dimension');

update field_definition set numerator_dimension='8fe6048c-6763-4f2a-8d26-aed402b3a96f'
    where id = '51bd3134-9101-11ed-a1eb-0242ac120002';


alter table field_definition
    add constraint field_definition_dimension_id_fk
        foreign key (numerator_dimension) references dimension;

alter table field_definition
    add constraint field_definition_lov_type_id_fk
        foreign key (lov_type) references lov_type;

alter table field_definition
    add constraint field_definition_tree_type_id_fk
        foreign key (tree_type) references tree_type;

alter table field_definition
    rename constraint fk1ulsqnx42veo0n3emsjahmm90 to field_definition_type_definition_id_fk;

alter table field_definition
    add constraint dimension_set_when_numeric check ( type <> 'numeric' or numerator_dimension is not null );

alter table field_definition
    add constraint lovtype_set_when_lov check ( type <> 'lov' or lov_type is not null );

alter table field_definition
    add constraint treetype_set_when_tree check ( type <> 'tree' or tree_type is not null );
