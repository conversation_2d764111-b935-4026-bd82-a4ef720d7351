DO $$
    DECLARE
        regionId uuid := '503524f4-19c6-4053-a6e2-65add5ee7ae1';
    BEGIN

        if exists (select id from ${flyway:defaultSchema}.classification where id = regionId) then

            -- electricity emission
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('6ee9c930-c698-43d3-bbe3-43e893e24ead', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "38e2ed59-9afc-4619-a6d7-f8913a777920"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "503524f4-19c6-4053-a6e2-65add5ee7ae1"}}}', '{"type": "detail.value.numeric", "value": 0.000000075, "originalMeasure": {"numerator": {"type": "unit", "unitId": "1e2f725a-9875-469c-b37b-e31609520a8e"}, "denominator": {"type": "unit", "unitId": "233831d9-32b2-4822-baf7-8189752c5697"}}}', '038ab550-0b78-4aff-8f28-c38be6d9c29a', 'TSET reference data', '2025-02-21 10:54:37.287217', 'unknown', 'TSET reference data', '2025-02-21 10:54:37.287217', 'unknown', true, '2025-02-21 09:54:37.287240 +00:00') ON CONFLICT DO NOTHING;
            -- interest rate
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('c9c6e8a0-b7a5-4f62-89e0-e4d14fb0b2aa', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "38e2ed59-9afc-4619-a6d7-f8913a777920"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "503524f4-19c6-4053-a6e2-65add5ee7ae1"}}}', '{"type": "detail.value.numeric", "value": 0.000000005232115677321157, "originalMeasure": {"numerator": {"type": "unit", "unitId": "3b1eba6a-a088-4629-9610-bd38b4a8d842"}, "denominator": {"type": "unit", "unitId": "feb3c760-356f-414d-bc9a-9cb741873dbb"}}}', '8c65f89a-4275-464a-ae68-2a2367e291f7', 'TSET reference data', '2025-02-21 10:54:37.287217', 'unknown', 'TSET reference data', '2025-02-21 10:54:37.287217', 'unknown', true, '2025-02-21 09:54:37.287240 +00:00') ON CONFLICT DO NOTHING;
            -- natural gas price
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('9ac20a1b-d3f9-4e3f-a500-29c8d31c17bb', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "efdf3658-5ac6-490d-8d99-edaf269c952c"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "503524f4-19c6-4053-a6e2-65add5ee7ae1"}}}', '{"type": "detail.value.numeric", "value": 0.4, "originalMeasure": {"numerator": {"type": "currency", "currencyId": "090eb463-b5df-4bf7-9b9e-3f6f45246a86"}, "denominator": {"type": "unit", "unitId": "0f89fa13-fc7d-426b-971b-c70b7d653bc5"}}}', 'c1f2eebc-27e8-44ed-9b3b-c702436b7cb9', 'TSET reference data', '2025-02-21 10:54:37.287217', 'unknown', 'TSET reference data', '2025-02-21 10:54:37.287217', 'unknown', true, '2025-02-21 09:54:37.287240 +00:00') ON CONFLICT DO NOTHING;
            -- primary aluminium emission
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('94f082b6-55bb-42d1-94c2-673b68abbb25', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "38e2ed59-9afc-4619-a6d7-f8913a777920"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "503524f4-19c6-4053-a6e2-65add5ee7ae1"}}}', '{"type": "detail.value.numeric", "value": 8.1, "originalMeasure": {"numerator": {"type": "unit", "unitId": "1e2f725a-9875-469c-b37b-e31609520a8e"}, "denominator": {"type": "unit", "unitId": "7250ffb1-904a-43b9-ae1d-f317c88812c5"}}}', 'e433d9d7-2944-4c86-8bf4-b7962c1f4bbd', 'TSET reference data', '2025-02-21 10:54:37.287217', 'unknown', 'TSET reference data', '2025-02-21 10:54:37.287217', 'unknown', true, '2025-02-21 09:54:37.287240 +00:00') ON CONFLICT DO NOTHING;
            -- secondary aluminium emission
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('c2411b57-e1a1-4a83-a937-521ac200c2ba', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "38e2ed59-9afc-4619-a6d7-f8913a777920"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "503524f4-19c6-4053-a6e2-65add5ee7ae1"}}}', '{"type": "detail.value.numeric", "value": 0.38, "originalMeasure": {"numerator": {"type": "unit", "unitId": "1e2f725a-9875-469c-b37b-e31609520a8e"}, "denominator": {"type": "unit", "unitId": "7250ffb1-904a-43b9-ae1d-f317c88812c5"}}}', '99e2f0d9-712e-47fd-a48b-f8406d0657e6', 'TSET reference data', '2025-02-21 10:54:37.287217', 'unknown', 'TSET reference data', '2025-02-21 10:54:37.287217', 'unknown', true, '2025-02-21 09:54:37.287240 +00:00') ON CONFLICT DO NOTHING;
            -- aluminium share primary
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('d5f44ba7-df01-4248-a060-6c6d38ae2c64', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "38e2ed59-9afc-4619-a6d7-f8913a777920"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "503524f4-19c6-4053-a6e2-65add5ee7ae1"}}}', '{"type": "detail.value.numeric", "value": 1.0, "originalMeasure": {"numerator": {"type": "unit", "unitId": "3b1eba6a-a088-4629-9610-bd38b4a8d842"}, "denominator": null}}', '96ecc22f-b650-4db4-acb7-e8cd5a09ce87', 'TSET reference data', '2025-02-21 10:54:37.287217', 'unknown', 'TSET reference data', '2025-02-21 10:54:37.287217', 'unknown', true, '2025-02-21 09:54:37.287240 +00:00') ON CONFLICT DO NOTHING;
            -- aluminium share secondary
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('a53c093e-6478-4346-b67e-535117180367', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "38e2ed59-9afc-4619-a6d7-f8913a777920"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "503524f4-19c6-4053-a6e2-65add5ee7ae1"}}}', '{"type": "detail.value.numeric", "value": 0.0, "originalMeasure": {"numerator": {"type": "unit", "unitId": "3b1eba6a-a088-4629-9610-bd38b4a8d842"}, "denominator": null}}', 'f57d0a6f-2475-408c-8cef-ec3e423ffe35', 'TSET reference data', '2025-02-21 10:54:37.287217', 'unknown', 'TSET reference data', '2025-02-21 10:54:37.287217', 'unknown', true, '2025-02-21 09:54:37.287240 +00:00') ON CONFLICT DO NOTHING;
        end if;

    END;
$$;