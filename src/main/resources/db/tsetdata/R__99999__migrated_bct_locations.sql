
DO language plpgsql
$$
    declare
        legacyId uuid;
    BEGIN
        --create the legacy bct locations only for test accounts
        if '${flyway:defaultSchema}' like 'tset_qa%' or '${flyway:defaultSchema}' like 'yellowjacket%' then
        IF NOT EXISTS (SELECT * FROM ${flyway:defaultSchema}.flyway_schema_history WHERE script = 'R__99999__migrated_bct_locations.sql' AND success = true) THEN


            legacyId := (select id from ${flyway:defaultSchema}.classification where key ->> 'key' = 'tset.ref.classification.region.legacy');

            if legacyId is null then
                legacyId := gen_random_uuid();
                INSERT INTO ${flyway:defaultSchema}.classification (id, key, parent, name, classification_type, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, system_managed) VALUES (legacyId, '{"key": "tset.ref.classification.region.legacy", "type": "header.key.simple"}', 'fb5949bc-479b-464a-be1d-3f11de1cd21b', 'zz_Former regions', 'ec96a4af-2c2f-49c5-9bbc-37c6e78525f6', 'tsetLegacyMigrator', '2024-07-12 10:55:24.837961', '6690ef7ce8594fa64c3f576080fcf908', 'tsetLegacyMigrator', '2024-07-12 10:55:24.837961', '6690ef7ce8594fa64c3f576080fcf908', false);
            end if;

            --ZF China GL 2026
            INSERT INTO ${flyway:defaultSchema}.classification (id, key, parent, name, classification_type, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, system_managed) VALUES ('825bdd53-3fea-4c6d-8ad1-e03da5213e3b', '{"key": "d38a6b14-486f-48f5-a463-fd76a59c5e3b", "type": "header.key.simple"}', legacyId, 'BCT_location2', 'ec96a4af-2c2f-49c5-9bbc-37c6e78525f6', 'tsetLegacyMigrator', '2024-07-29 13:38:13.506796', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:13.506796', 'unknown', false) on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.classification (id, key, parent, name, classification_type, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, system_managed) VALUES ('0dafa23a-e377-40dc-99a8-c6142e28d4e0', '{"key": "876b137d-1cdb-486b-a44d-aec148eab8b7", "type": "header.key.simple"}', legacyId, 'BCT_location', 'ec96a4af-2c2f-49c5-9bbc-37c6e78525f6', 'tsetLegacyMigrator', '2024-07-29 13:38:13.393544', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:13.393544', 'unknown', false) on conflict do nothing;


            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('a540e0d2-7a3a-488b-9a6f-f73354e4c0b6', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "0dafa23a-e377-40dc-99a8-c6142e28d4e0"}}}', '{"type": "detail.value.numeric", "value": 2.3, "originalMeasure": {"numerator": {"type": "unit", "unitId": "1e2f725a-9875-469c-b37b-e31609520a8e"}, "denominator": {"type": "unit", "unitId": "0f89fa13-fc7d-426b-971b-c70b7d653bc5"}}}', '0ba0e493-4d59-4efd-b5c3-6e3b60f50e88', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('ffc606d2-e441-40ff-8915-dbb5f9bc8623', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "825bdd53-3fea-4c6d-8ad1-e03da5213e3b"}}}', '{"type": "detail.value.numeric", "value": 0.00003277777777777778, "originalMeasure": {"numerator": {"type": "currency", "currencyId": "b725ae50-1b9c-4858-b814-f0c4382985a0"}, "denominator": {"type": "unit", "unitId": "233831d9-32b2-4822-baf7-8189752c5697"}}}', '3fb08cd6-c147-461c-a566-a945af0c695c', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('f82b0cc4-fa6b-4f6a-8791-2c3b7e08229f', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": {"type": "lov", "value": "41ab564f-af91-4be7-82aa-780cc4e27793"}, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "0dafa23a-e377-40dc-99a8-c6142e28d4e0"}}}', '{"type": "detail.value.numeric", "value": 0.63, "originalMeasure": {"numerator": {"type": "unit", "unitId": "3b1eba6a-a088-4629-9610-bd38b4a8d842"}, "denominator": null}}', 'a8b7b879-8f83-4865-a24c-5768649c2292', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('bb91b0b1-8f09-4b0e-86c2-5460987c1bc6', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "825bdd53-3fea-4c6d-8ad1-e03da5213e3b"}}}', '{"type": "detail.value.numeric", "value": 17.0, "originalMeasure": {"numerator": {"type": "unit", "unitId": "1e2f725a-9875-469c-b37b-e31609520a8e"}, "denominator": {"type": "unit", "unitId": "7250ffb1-904a-43b9-ae1d-f317c88812c5"}}}', 'e433d9d7-2944-4c86-8bf4-b7962c1f4bbd', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('f28daf61-dd56-482c-a48f-08072924f9fb', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": {"type": "lov", "value": "25522524-f22f-4af6-8183-2621943d5cb2"}, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "825bdd53-3fea-4c6d-8ad1-e03da5213e3b"}}}', '{"type": "detail.value.numeric", "value": 0.0011027777777777778, "originalMeasure": {"numerator": {"type": "currency", "currencyId": "b725ae50-1b9c-4858-b814-f0c4382985a0"}, "denominator": {"type": "unit", "unitId": "902f31c0-e98e-47a9-a825-eaa418d18c93"}}}', 'bf777119-85c1-4648-a9b9-eeaae0bf3842', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('e217ffed-bd4b-43e1-ab62-2f245b7e592d', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": {"type": "lov", "value": "803319ef-e33a-4ee8-b37b-426dc0ddbc40"}, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "825bdd53-3fea-4c6d-8ad1-e03da5213e3b"}}}', '{"type": "detail.value.numeric", "value": 0.892, "originalMeasure": {"numerator": {"type": "unit", "unitId": "3b1eba6a-a088-4629-9610-bd38b4a8d842"}, "denominator": null}}', 'a8b7b879-8f83-4865-a24c-5768649c2292', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('67409499-26b0-4775-aaa8-0c371efa4cc3', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "0dafa23a-e377-40dc-99a8-c6142e28d4e0"}}}', '{"type": "detail.value.numeric", "value": 0.4500000000015543, "originalMeasure": {"numerator": {"type": "currency", "currencyId": "b725ae50-1b9c-4858-b814-f0c4382985a0"}, "denominator": {"type": "unit", "unitId": "0f89fa13-fc7d-426b-971b-c70b7d653bc5"}}}', 'c1f2eebc-27e8-44ed-9b3b-c702436b7cb9', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('ea930e0f-8993-49b3-a709-cc0aa667d322', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "0dafa23a-e377-40dc-99a8-c6142e28d4e0"}}}', '{"type": "detail.value.numeric", "value": 0.33, "originalMeasure": {"numerator": {"type": "unit", "unitId": "1e2f725a-9875-469c-b37b-e31609520a8e"}, "denominator": {"type": "unit", "unitId": "0f89fa13-fc7d-426b-971b-c70b7d653bc5"}}}', '9ac4a10c-e894-4d30-aaf7-ef2e733018f5', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('24b4d2f1-46f6-40fa-8d63-64fc57571752', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": {"type": "lov", "value": "b04045fc-e78f-4fcb-9100-cc3c1d22277f"}, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "825bdd53-3fea-4c6d-8ad1-e03da5213e3b"}}}', '{"type": "detail.value.numeric", "value": 0.892, "originalMeasure": {"numerator": {"type": "unit", "unitId": "3b1eba6a-a088-4629-9610-bd38b4a8d842"}, "denominator": null}}', 'a8b7b879-8f83-4865-a24c-5768649c2292', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('394a200c-9809-4522-8e23-c3dfe248bf36', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "825bdd53-3fea-4c6d-8ad1-e03da5213e3b"}}}', '{"type": "detail.value.numeric", "value": 0.0001976023888888889, "originalMeasure": {"numerator": {"type": "unit", "unitId": "1e2f725a-9875-469c-b37b-e31609520a8e"}, "denominator": {"type": "unit", "unitId": "233831d9-32b2-4822-baf7-8189752c5697"}}}', '038ab550-0b78-4aff-8f28-c38be6d9c29a', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('3d72b4d0-4521-4f79-863b-d9bc2c26f112', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": {"type": "lov", "value": "b04045fc-e78f-4fcb-9100-cc3c1d22277f"}, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "0dafa23a-e377-40dc-99a8-c6142e28d4e0"}}}', '{"type": "detail.value.numeric", "value": 0.6, "originalMeasure": {"numerator": {"type": "unit", "unitId": "3b1eba6a-a088-4629-9610-bd38b4a8d842"}, "denominator": null}}', 'a8b7b879-8f83-4865-a24c-5768649c2292', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('ecda8531-b18c-47a2-9da1-c9ea2c7a024b', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "0dafa23a-e377-40dc-99a8-c6142e28d4e0"}}}', '{"type": "detail.value.numeric", "value": 0.13, "originalMeasure": {"numerator": {"type": "unit", "unitId": "3b1eba6a-a088-4629-9610-bd38b4a8d842"}, "denominator": {"type": "unit", "unitId": "feb3c760-356f-414d-bc9a-9cb741873dbb"}}}', '8c65f89a-4275-464a-ae68-2a2367e291f7', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('f5867dfa-0a22-487f-94a1-54c5a18020ad', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "0dafa23a-e377-40dc-99a8-c6142e28d4e0"}}}', '{"type": "detail.value.numeric", "value": 0.002777777777777778, "originalMeasure": {"numerator": {"type": "currency", "currencyId": "b725ae50-1b9c-4858-b814-f0c4382985a0"}, "denominator": {"type": "unit", "unitId": "233831d9-32b2-4822-baf7-8189752c5697"}}}', '3fb08cd6-c147-461c-a566-a945af0c695c', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('ae5d9023-055f-41e8-9535-a8d95b219329', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "0dafa23a-e377-40dc-99a8-c6142e28d4e0"}}}', '{"type": "detail.value.numeric", "value": 0.1, "originalMeasure": {"numerator": {"type": "unit", "unitId": "1e2f725a-9875-469c-b37b-e31609520a8e"}, "denominator": {"type": "unit", "unitId": "7250ffb1-904a-43b9-ae1d-f317c88812c5"}}}', 'e433d9d7-2944-4c86-8bf4-b7962c1f4bbd', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('1d81c449-8c3a-4d43-b874-528b6bcdb2c6', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "0dafa23a-e377-40dc-99a8-c6142e28d4e0"}}}', '{"type": "detail.value.numeric", "value": 3.0, "originalMeasure": {"numerator": {"type": "unit", "unitId": "90fcda36-d066-4f19-b23d-7fa229c9924e"}, "denominator": null}}', 'ba4a5c40-1d6a-4b20-8eb8-6052e0d2ad8b', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('f5b98905-fba9-4161-a341-cc8639ae90bb', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "825bdd53-3fea-4c6d-8ad1-e03da5213e3b"}}}', '{"type": "detail.value.numeric", "value": 10.0, "originalMeasure": {"numerator": {"type": "unit", "unitId": "90fcda36-d066-4f19-b23d-7fa229c9924e"}, "denominator": null}}', 'ba4a5c40-1d6a-4b20-8eb8-6052e0d2ad8b', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('a2fd0739-10cf-42e3-b9a8-b271aab198af', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "825bdd53-3fea-4c6d-8ad1-e03da5213e3b"}}}', '{"type": "detail.value.numeric", "value": 0.200683177, "originalMeasure": {"numerator": {"type": "unit", "unitId": "3b1eba6a-a088-4629-9610-bd38b4a8d842"}, "denominator": null}}', 'f57d0a6f-2475-408c-8cef-ec3e423ffe35', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('bd5cd6cb-d9bb-4df8-acca-96fd6d4f49bd', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "825bdd53-3fea-4c6d-8ad1-e03da5213e3b"}}}', '{"type": "detail.value.numeric", "value": 0.65695802, "originalMeasure": {"numerator": {"type": "unit", "unitId": "1e2f725a-9875-469c-b37b-e31609520a8e"}, "denominator": {"type": "unit", "unitId": "7250ffb1-904a-43b9-ae1d-f317c88812c5"}}}', '99e2f0d9-712e-47fd-a48b-f8406d0657e6', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('f6d3695d-62d1-4396-a86b-2e06fd385647', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": {"type": "lov", "value": "3cad6a33-957e-4b1a-af5e-bbb60f8d2cfd"}, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "0dafa23a-e377-40dc-99a8-c6142e28d4e0"}}}', '{"type": "detail.value.numeric", "value": 0.57, "originalMeasure": {"numerator": {"type": "unit", "unitId": "3b1eba6a-a088-4629-9610-bd38b4a8d842"}, "denominator": null}}', 'a8b7b879-8f83-4865-a24c-5768649c2292', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('3744262b-6d38-4e89-a0ab-fbd0cf5533ac', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "825bdd53-3fea-4c6d-8ad1-e03da5213e3b"}}}', '{"type": "detail.value.numeric", "value": 0.799316823, "originalMeasure": {"numerator": {"type": "unit", "unitId": "3b1eba6a-a088-4629-9610-bd38b4a8d842"}, "denominator": null}}', '96ecc22f-b650-4db4-acb7-e8cd5a09ce87', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('bf3c788e-0848-46b5-9a11-e1b1a53521a3', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "825bdd53-3fea-4c6d-8ad1-e03da5213e3b"}}}', '{"type": "detail.value.numeric", "value": 0.0722, "originalMeasure": {"numerator": {"type": "unit", "unitId": "3b1eba6a-a088-4629-9610-bd38b4a8d842"}, "denominator": {"type": "unit", "unitId": "feb3c760-356f-414d-bc9a-9cb741873dbb"}}}', '8c65f89a-4275-464a-ae68-2a2367e291f7', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('e8ef2304-63d0-4926-b474-b8ef130d24e3', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "825bdd53-3fea-4c6d-8ad1-e03da5213e3b"}}}', '{"type": "detail.value.numeric", "value": 0.1, "originalMeasure": {"numerator": {"type": "currency", "currencyId": "b725ae50-1b9c-4858-b814-f0c4382985a0"}, "denominator": {"type": "unit", "unitId": "7250ffb1-904a-43b9-ae1d-f317c88812c5"}}}', '896e5c13-c5a2-438f-af14-487e3af8adaa', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('47359014-33a2-463a-b4ff-199c0ce4cb1a', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": {"type": "lov", "value": "eee39bd0-6b93-4827-bde0-577cea9a435f"}, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "825bdd53-3fea-4c6d-8ad1-e03da5213e3b"}}}', '{"type": "detail.value.numeric", "value": 0.0020833333333333333, "originalMeasure": {"numerator": {"type": "currency", "currencyId": "b725ae50-1b9c-4858-b814-f0c4382985a0"}, "denominator": {"type": "unit", "unitId": "902f31c0-e98e-47a9-a825-eaa418d18c93"}}}', 'bf777119-85c1-4648-a9b9-eeaae0bf3842', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('d547e320-8a82-4f6c-98de-3a090aa88d52', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "825bdd53-3fea-4c6d-8ad1-e03da5213e3b"}}}', '{"type": "detail.value.numeric", "value": 0.002, "originalMeasure": {"numerator": {"type": "currency", "currencyId": "b725ae50-1b9c-4858-b814-f0c4382985a0"}, "denominator": {"type": "unit", "unitId": "7250ffb1-904a-43b9-ae1d-f317c88812c5"}}}', '7327148c-9774-4403-b82c-1ba3b05d13cd', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('1f9c13bd-ba42-4a7d-9b3d-07cd05fbe4fd', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "825bdd53-3fea-4c6d-8ad1-e03da5213e3b"}}}', '{"type": "detail.value.numeric", "value": 4.65, "originalMeasure": {"numerator": {"type": "currency", "currencyId": "b725ae50-1b9c-4858-b814-f0c4382985a0"}, "denominator": {"type": "unit", "unitId": "5d20ba3a-580d-40f5-8b0d-667b8a5c1404"}}}', '39dc46c5-4765-4b43-ab15-ddfaaf922a76', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('7faec5b7-88b6-4412-92ca-34cffe0cf956', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "825bdd53-3fea-4c6d-8ad1-e03da5213e3b"}}}', '{"type": "detail.value.numeric", "value": 0.33, "originalMeasure": {"numerator": {"type": "unit", "unitId": "1e2f725a-9875-469c-b37b-e31609520a8e"}, "denominator": {"type": "unit", "unitId": "0f89fa13-fc7d-426b-971b-c70b7d653bc5"}}}', '9ac4a10c-e894-4d30-aaf7-ef2e733018f5', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('9d11c1e9-0705-400c-af6b-a46c8be26fb6', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "0dafa23a-e377-40dc-99a8-c6142e28d4e0"}}}', '{"type": "detail.value.numeric", "value": 0.002000000000006908, "originalMeasure": {"numerator": {"type": "currency", "currencyId": "b725ae50-1b9c-4858-b814-f0c4382985a0"}, "denominator": {"type": "unit", "unitId": "7250ffb1-904a-43b9-ae1d-f317c88812c5"}}}', '7327148c-9774-4403-b82c-1ba3b05d13cd', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('2839e844-ec7c-4d34-b97b-34c887c037e1', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "0dafa23a-e377-40dc-99a8-c6142e28d4e0"}}}', '{"type": "detail.value.numeric", "value": 0.1, "originalMeasure": {"numerator": {"type": "unit", "unitId": "1e2f725a-9875-469c-b37b-e31609520a8e"}, "denominator": {"type": "unit", "unitId": "7250ffb1-904a-43b9-ae1d-f317c88812c5"}}}', '99e2f0d9-712e-47fd-a48b-f8406d0657e6', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('83460c0d-189b-463f-9aea-e89247d7983f', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": {"type": "lov", "value": "20ee40d0-d5b5-4a16-a9aa-812adf70a611"}, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "825bdd53-3fea-4c6d-8ad1-e03da5213e3b"}}}', '{"type": "detail.value.numeric", "value": 0.001725, "originalMeasure": {"numerator": {"type": "currency", "currencyId": "b725ae50-1b9c-4858-b814-f0c4382985a0"}, "denominator": {"type": "unit", "unitId": "902f31c0-e98e-47a9-a825-eaa418d18c93"}}}', 'bf777119-85c1-4648-a9b9-eeaae0bf3842', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('b2e2455b-0754-4016-970d-13ed33147fa6', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": {"type": "lov", "value": "803319ef-e33a-4ee8-b37b-426dc0ddbc40"}, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "0dafa23a-e377-40dc-99a8-c6142e28d4e0"}}}', '{"type": "detail.value.numeric", "value": 0.65, "originalMeasure": {"numerator": {"type": "unit", "unitId": "3b1eba6a-a088-4629-9610-bd38b4a8d842"}, "denominator": null}}', 'a8b7b879-8f83-4865-a24c-5768649c2292', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('fd594868-a7a0-46c6-8219-714d31924440', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "0dafa23a-e377-40dc-99a8-c6142e28d4e0"}}}', '{"type": "detail.value.numeric", "value": 0.10000000000034541, "originalMeasure": {"numerator": {"type": "currency", "currencyId": "b725ae50-1b9c-4858-b814-f0c4382985a0"}, "denominator": {"type": "unit", "unitId": "7250ffb1-904a-43b9-ae1d-f317c88812c5"}}}', '896e5c13-c5a2-438f-af14-487e3af8adaa', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('f4e56cd8-5f2c-4e4a-b51b-5a7250d6ff0d', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": {"type": "lov", "value": "41ab564f-af91-4be7-82aa-780cc4e27793"}, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "825bdd53-3fea-4c6d-8ad1-e03da5213e3b"}}}', '{"type": "detail.value.numeric", "value": 0.892, "originalMeasure": {"numerator": {"type": "unit", "unitId": "3b1eba6a-a088-4629-9610-bd38b4a8d842"}, "denominator": null}}', 'a8b7b879-8f83-4865-a24c-5768649c2292', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('88231060-2620-401b-83fc-65e4b4ec3dc9', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "825bdd53-3fea-4c6d-8ad1-e03da5213e3b"}}}', '{"type": "detail.value.numeric", "value": 2.3, "originalMeasure": {"numerator": {"type": "unit", "unitId": "1e2f725a-9875-469c-b37b-e31609520a8e"}, "denominator": {"type": "unit", "unitId": "0f89fa13-fc7d-426b-971b-c70b7d653bc5"}}}', '0ba0e493-4d59-4efd-b5c3-6e3b60f50e88', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('7621d7ed-a5f8-4d69-8a98-8d06ba96a75f', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": {"type": "lov", "value": "3cad6a33-957e-4b1a-af5e-bbb60f8d2cfd"}, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "825bdd53-3fea-4c6d-8ad1-e03da5213e3b"}}}', '{"type": "detail.value.numeric", "value": 0.892, "originalMeasure": {"numerator": {"type": "unit", "unitId": "3b1eba6a-a088-4629-9610-bd38b4a8d842"}, "denominator": null}}', 'a8b7b879-8f83-4865-a24c-5768649c2292', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
            INSERT INTO ${flyway:defaultSchema}.detail (id, key, value, header_id, created_by, created_at, created_request_id, last_modified_by, last_modified_at, last_modified_request_id, active, version_timestamp) VALUES ('b667b254-4061-4f4c-8d84-695d561a80b2', '{"effectivities": {"0c72a0c6-d307-4fc1-abb8-bed22034a524": null, "197b08b6-3cbf-49d1-a8f6-fb2725f8ca80": null, "2e8a89a3-e1f6-4f32-88af-d0ec4dfd7010": {"type": "lov", "value": "0112824b-21a7-4336-974e-6b0a44a22f98"}, "63e0ecb2-ff21-406f-8c9f-54c0ae6bf733": null, "844e7078-056d-4891-8dd2-206557bec4f7": {"type": "classification", "value": "825bdd53-3fea-4c6d-8ad1-e03da5213e3b"}}}', '{"type": "detail.value.numeric", "value": 0.45, "originalMeasure": {"numerator": {"type": "currency", "currencyId": "b725ae50-1b9c-4858-b814-f0c4382985a0"}, "denominator": {"type": "unit", "unitId": "0f89fa13-fc7d-426b-971b-c70b7d653bc5"}}}', 'c1f2eebc-27e8-44ed-9b3b-c702436b7cb9', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', 'tsetLegacyMigrator', '2024-07-29 13:38:31.280555', 'unknown', true, '2024-07-29 11:38:31.280555 +00:00') on conflict do nothing;
        END IF;
        end if;
end $$;



