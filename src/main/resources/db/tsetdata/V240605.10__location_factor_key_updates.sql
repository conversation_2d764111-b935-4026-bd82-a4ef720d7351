-- takes care of adapting the key (and certain names) of entries introduced for/with location-factors
-- mainly adds the tset.ref. prefix to keys
CREATE OR REPLACE FUNCTION ${flyway:defaultSchema}.try_delete_lov_entry(entryId uuid) RETURNS VOID
    LANGUAGE plpgsql
AS $$
BEGIN
    BEGIN
        delete from ${flyway:defaultSchema}.lov_entry where id = entryId;
    EXCEPTION
        WHEN SQLSTATE 'NU001' THEN
        -- do nothing
    END;
END
$$;



CREATE OR REPLACE FUNCTION ${flyway:defaultSchema}.lov_entry_prohibit_delete() RETURNS TRIGGER LANGUAGE 'plpgsql' AS
$$
BEGIN
    IF EXISTS (
        select 1
        from ${flyway:defaultSchema}.detail as d
                 inner join ${flyway:defaultSchema}.header as h on d.header_id = h.id
                 inner join ${flyway:defaultSchema}.header_type as ht on h.header_type_id = ht.id
                 inner join ${flyway:defaultSchema}.effectivity as e on ht.id = e.header_type_id
                 inner join ${flyway:defaultSchema}.field_definition as fd on e.field_definition_id = fd.id
            and fd.type = 'lov'
        where jsonb_extract_path_text(d.key,'effectivities', e.id::text, 'value') = old.id::text
    ) THEN
        RAISE EXCEPTION USING ERRCODE = 'NU001',
            MESSAGE = 'The deletion of a lov_entry that is used as an effectivity is not allowed!' ;
    ELSE
        return OLD;
    END IF;
END;
$$
;

CREATE or replace TRIGGER lov_entry_effectivity_usage_check
    BEFORE DELETE ON ${flyway:defaultSchema}.lov_entry
    FOR EACH ROW
EXECUTE PROCEDURE ${flyway:defaultSchema}.lov_entry_prohibit_delete();






update ${flyway:defaultSchema}.classification_type
 set key = '{"key": "tset.ref.classification-type.region", "type": "header.key.simple"}',
     name = 'Region'
    where id = 'ec96a4af-2c2f-49c5-9bbc-37c6e78525f6';

update ${flyway:defaultSchema}.field_definition
    set key = '{"key": "tset.ref.field.region", "type": "header.key.simple"}',
    name = 'Region'
    where id = '168b6b46-a594-469a-8747-29ff0d09cd44';


update ${flyway:defaultSchema}.lov_type
set key =  '{"key": "tset.ref.lov-type.shift", "type": "header.key.simple"}',
    system_managed = true
    where id = '36066157-132f-4382-964a-c14ea3adbc5c';
update ${flyway:defaultSchema}.field_definition
    set key = '{"key": "tset.ref.field.shift", "type": "header.key.simple"}'
        where id = 'd8a5104d-4fad-4aa5-868e-5166cb633b80';
update ${flyway:defaultSchema}.lov_entry
set key = '{"key": "tset.ref.lov-entry.shift-1", "type": "header.key.simple"}'
where id = '3cad6a33-957e-4b1a-af5e-bbb60f8d2cfd';
update ${flyway:defaultSchema}.lov_entry
set key = '{"key": "tset.ref.lov-entry.shift-2", "type": "header.key.simple"}'
where id = 'b04045fc-e78f-4fcb-9100-cc3c1d22277f';
update ${flyway:defaultSchema}.lov_entry
set key = '{"key": "tset.ref.lov-entry.shift-3", "type": "header.key.simple"}'
where id = '41ab564f-af91-4be7-82aa-780cc4e27793';
update ${flyway:defaultSchema}.lov_entry
set key = '{"key": "tset.ref.lov-entry.shift-4", "type": "header.key.simple"}'
where id = '803319ef-e33a-4ee8-b37b-426dc0ddbc40';




update ${flyway:defaultSchema}.lov_type
    set key = '{"key": "tset.ref.lov-type.skill-type", "type": "header.key.simple"}',
        system_managed = true
    where id = 'd6c39f10-c3bf-4767-b5f5-8836a937bb82';

update ${flyway:defaultSchema}.field_definition
set key = '{"key": "tset.ref.field.skill-type", "type": "header.key.simple"}'
    where id = '723cdf21-3bc4-4919-ae3d-e4bb510869c7';
update ${flyway:defaultSchema}.lov_entry
set key = '{"key": "tset.ref.lov-entry.unskilled-worker", "type": "header.key.simple"}'
where id = '25522524-f22f-4af6-8183-2621943d5cb2';
update ${flyway:defaultSchema}.lov_entry
set key = '{"key": "tset.ref.lov-entry.skilled-worker", "type": "header.key.simple"}'
where id = '20ee40d0-d5b5-4a16-a9aa-812adf70a611';
update ${flyway:defaultSchema}.lov_entry
set key = '{"key": "tset.ref.lov-entry.setup-technician", "type": "header.key.simple"}'
where id = 'eee39bd0-6b93-4827-bde0-577cea9a435f';
update ${flyway:defaultSchema}.lov_entry
set key = '{"key": "tset.ref.lov-entry.production-supervisor", "type": "header.key.simple"}'
where id = '47fae403-40e1-448a-8735-8aa8748eabcb';
update ${flyway:defaultSchema}.lov_entry
set key = '{"key": "tset.ref.lov-entry.inspector", "type": "header.key.simple"}'
where id = '94a11ed8-b27a-4265-9673-f1a3721f5359';
update ${flyway:defaultSchema}.lov_entry
set key = '{"key": "tset.ref.lov-entry.average", "type": "header.key.simple"}',
name = 'Average'
where id = 'd7a662f4-6be2-4e2e-87c2-96e1bc3768e3';
update ${flyway:defaultSchema}.lov_entry
set key = '{"key": "tset.ref.lov-entry.minimum", "type": "header.key.simple"}',
    name = 'Minimum'
where id = 'a4833160-686a-488c-93ac-d1027a382612';

DO $$
BEGIN
    perform ${flyway:defaultSchema}.try_delete_lov_entry('66752fbb-6df8-4e3f-b06a-26ad60cfbb00'); -- administration-professional
    perform ${flyway:defaultSchema}.try_delete_lov_entry('47aee4b1-01e9-4f53-92e7-c461f8601d08'); -- middle-management
    perform ${flyway:defaultSchema}.try_delete_lov_entry('2c5d8d5b-ff9b-4f79-9131-55e98dc281cd'); -- technical-manager
    perform ${flyway:defaultSchema}.try_delete_lov_entry('ba737cbb-7a8c-44ce-aeac-cc5ebed97680'); -- production-engineer
END;
$$;

drop FUNCTION ${flyway:defaultSchema}.try_delete_lov_entry(id uuid);