DO $$
BEGIN
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        '731c7c35-efd0-4c7a-9936-0e6eff421562', '{"key": "CHF", "type": "header.key.simple"}', 'Swiss Franc', 'CHF', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        '1fbd0723-eb2c-4c5d-aece-3f1953ea291e', '{"key": "CNY", "type": "header.key.simple"}', 'Chinese Yuan', '元', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        'b725ae50-1b9c-4858-b814-f0c4382985a0', '{"key": "EUR", "type": "header.key.simple"}', 'Euro', '€', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        '7817dd28-d90d-4a2a-8eff-89eb2d454375', '{"key": "GBP", "type": "header.key.simple"}', 'British Pound Sterling', '£', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        '10bf6a3c-48d3-4407-bf24-a36dd4566541', '{"key": "JPY", "type": "header.key.simple"}', 'Japanese Yen', '¥', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        '090eb463-b5df-4bf7-9b9e-3f6f45246a86', '{"key": "USD", "type": "header.key.simple"}', 'United States Dollar', '$', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        'fd67a1cd-7ec3-4579-9c54-59b6abba3566', '{"key": "AUD", "type": "header.key.simple"}', 'Australian Dollar', 'A$', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        'f7a19dec-6980-49ec-bc83-60d405965c90', '{"key": "CAD", "type": "header.key.simple"}', 'Canadian Dollar', 'CA$', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        '502a8b30-2d8a-4141-bbdd-089891db4239', '{"key": "SEK", "type": "header.key.simple"}', 'Swedish Krona', 'kr', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        '07a0490f-1d28-4c4d-a64b-6bee8d058a87', '{"key": "KRW", "type": "header.key.simple"}', 'South Korean Won', '₩', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        'b01b9ae3-b6ba-4395-a8b1-8d957362ba03', '{"key": "MXN", "type": "header.key.simple"}', 'Mexican Peso', '$', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        '884b8eac-a369-4056-bb0a-d4da2ab45fbf', '{"key": "INR", "type": "header.key.simple"}', 'Indian Rupee', '₹', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        '73c50c0a-2ffa-4ffd-b52a-1569dd2b218b', '{"key": "RUB", "type": "header.key.simple"}', 'Russian Ruble', '₽', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        '411226e4-40d1-4c1c-b467-561428a392b4', '{"key": "TRY", "type": "header.key.simple"}', 'Turkish Lira', '₺', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        'fe5d8599-e399-4635-aa40-e2c0aa9f9234', '{"key": "BRL", "type": "header.key.simple"}', 'Brazilian Real', 'R$', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        '2fd7f688-de38-4aae-b111-85116ae50ecb', '{"key": "TWD", "type": "header.key.simple"}', 'New Taiwan Dollar', 'NT$', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        '97248133-b476-4fd8-a4bf-c5905e1a4bbc', '{"key": "DKK", "type": "header.key.simple"}', 'Danish Krone', 'kr', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        'd44a9e24-f6eb-44a3-bd72-c1a2710d07f7', '{"key": "PLN", "type": "header.key.simple"}', 'Polish Zloty', 'zł', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        '696e3e32-f7f0-4005-b297-2528bbb1a112', '{"key": "IDR", "type": "header.key.simple"}', 'Indonesian Rupiah', 'Rp', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        '688d1be0-219f-416f-9e53-a99fc6daabef', '{"key": "HUF", "type": "header.key.simple"}', 'Hungarian Forint', 'ft', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        '88edf2a9-e21d-45aa-a3ea-f3090445e19b', '{"key": "CZK", "type": "header.key.simple"}', 'Czech Koruna', 'Kč', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown', 'TSET reference data', '2023-08-09 09:15:49.442969', 'unknown'
    ));

    -- new currencies 2024-07
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        'd10ebd57-6c5d-4310-aeef-0dc0bd598d38', '{"key": "BGN", "type": "header.key.simple"}', 'Bulgarian Lev', '__', 'TSET reference data', '2024-07-03 16:12:37.166379', 'unknown', 'TSET reference data', '2024-07-03 16:12:37.166379', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        '018511bf-9bdf-47fb-b716-e9649cc2ed14', '{"key": "ILS", "type": "header.key.simple"}', 'Israeli New Shekel', '_', 'TSET reference data', '2024-07-03 16:12:37.166379', 'unknown', 'TSET reference data', '2024-07-03 16:12:37.166379', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        'ee6688e0-b80d-43ea-9d59-3d3fe855af74', '{"key": "MAD", "type": "header.key.simple"}', 'Moroccan Dirham', '_._.', 'TSET reference data', '2024-07-03 16:12:37.166379', 'unknown', 'TSET reference data', '2024-07-03 16:12:37.166379', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        '2a31096d-f06a-48a0-84cf-f0c1d1552715', '{"key": "NOK", "type": "header.key.simple"}', 'Norwegian Krone', 'kr', 'TSET reference data', '2024-07-03 16:12:37.166379', 'unknown', 'TSET reference data', '2024-07-03 16:12:37.166379', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        'e9c50382-4625-49e5-a408-502e45923ea3', '{"key": "RON", "type": "header.key.simple"}', 'Romanian Leu', 'lei', 'TSET reference data', '2024-07-03 16:12:37.166379', 'unknown', 'TSET reference data', '2024-07-03 16:12:37.166379', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        '6df4d1ed-71b3-4935-9f8d-aa970ec75592', '{"key": "RSD", "type": "header.key.simple"}', 'Serbian Dinar', '___.', 'TSET reference data', '2024-07-03 16:12:37.166379', 'unknown', 'TSET reference data', '2024-07-03 16:12:37.166379', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        '3b90b26f-1c4c-4c95-9e82-2cf0e594ce57', '{"key": "ZAR", "type": "header.key.simple"}', 'South African Rand', 'R', 'TSET reference data', '2024-07-03 16:12:37.166379', 'unknown', 'TSET reference data', '2024-07-03 16:12:37.166379', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        'ca34656c-2974-45d4-b7f2-903b772fa278', '{"key": "THB", "type": "header.key.simple"}', 'Thai Baht', '_', 'TSET reference data', '2024-07-03 16:12:37.166379', 'unknown', 'TSET reference data', '2024-07-03 16:12:37.166379', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        '40187e27-f197-480e-9928-5867d2d419b1', '{"key": "TND", "type": "header.key.simple"}', 'Tunisian Dinar', '_._', 'TSET reference data', '2024-07-03 16:12:37.166379', 'unknown', 'TSET reference data', '2024-07-03 16:12:37.166379', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        '5ea75d0d-2f41-4c6c-a8b1-ee6225d78b24', '{"key": "UAH", "type": "header.key.simple"}', 'Ukrainian Hryvnia', '_', 'TSET reference data', '2024-07-03 16:12:37.166379', 'unknown', 'TSET reference data', '2024-07-03 16:12:37.166379', 'unknown'
    ));
    PERFORM ${flyway:defaultSchema}.insert_currency_with_check(ROW(
        'f5a95b4d-38b2-430a-8872-9f39cc737d8f', '{"key": "VND", "type": "header.key.simple"}', 'Vietnamese Dong', '_', 'TSET reference data', '2024-07-03 16:12:37.166379', 'unknown', 'TSET reference data', '2024-07-03 16:12:37.166379', 'unknown'
    ));
    
END;
$$;
