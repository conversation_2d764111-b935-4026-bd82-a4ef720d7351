package com.calculationtests.helperFunctions

class TwoStringsToDoubleMap {
    val values = mutableMapOf<String, StringToDoubleMap>()

    fun clone(): TwoStringsToDoubleMap {
        val result = TwoStringsToDoubleMap()
        values.forEach { result[it.key] = it.value.clone() }
        return result
    }

    private fun keys(map: TwoStringsToDoubleMap): List<String> = map.values.map { it.key }

    private fun uniqueKeys(map1: TwoStringsToDoubleMap, map2: TwoStringsToDoubleMap): Set<String> {
        val keys1 = map1.values.map { it.key }
        val keys2 = map2.values.map { it.key }
        return keys1 union keys2
    }

    fun getOrZero(key1: String, key2: String): Double {
        return values[key1]?.getOrZero(key2) ?: 0.0
    }
    operator fun get(key1: String, key2: String): Double? {
        return values[key1]?.get(key2)
    }

    operator fun set(key1: String, key2: String, value: Double) {
        var map = values[key1]
        if (map == null) {
            map = StringToDoubleMap()
            values[key1] = map
        }
        map[key2] = value
    }

    operator fun get(key: String): StringToDoubleMap? {
        return values[key]
    }

    operator fun set(key: String, value: StringToDoubleMap) {
        values[key] = value
    }

    operator fun plusAssign(other: TwoStringsToDoubleMap) {
        keys(other).forEach { key ->
            internalPlus(values[key], other[key])?.let { values[key] = it }
        }
    }

    operator fun plus(other: TwoStringsToDoubleMap): TwoStringsToDoubleMap {
        val result = TwoStringsToDoubleMap()
        uniqueKeys(this, other).forEach { key ->
            internalPlus(values[key], other.values[key])?.let { result[key] = it }
        }
        return result
    }

    private fun internalPlus(a: StringToDoubleMap?, b: StringToDoubleMap?): StringToDoubleMap? {
        if (a != null && b != null) return a + b
        if (a != null) return a
        if (b != null) return b
        return null
    }

    operator fun timesAssign(factor: Double) {
        values.forEach { it.value *= factor }
    }

    operator fun times(factor: Double): TwoStringsToDoubleMap {
        val multipliedResults = TwoStringsToDoubleMap()
        values.forEach { multipliedResults[it.key] = it.value * factor }
        return multipliedResults
    }

    override fun toString(): String {
        return values.map { "${it.key}=[${it.value}]" }.joinToString()
    }
}
