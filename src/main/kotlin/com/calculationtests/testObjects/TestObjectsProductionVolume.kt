package com.calculationtests.testObjects

import com.calculationtests.mockEngine.calculationObjects.ProductionVolume

object TestObjectsProductionVolume {

    val low = ProductionVolume(
        average = 1_000.0,
        peak = 1_000.0,
        lifetime = 5.0,
    )
    val lowWithPeak = ProductionVolume(
        average = 1_000.0,
        peak = 1_500.0,
        lifetime = 5.0,
    )
    val high = ProductionVolume(
        average = 100_000.0,
        peak = 100_000.0,
        lifetime = 6.0,
    )
    val highWithPeak = ProductionVolume(
        average = 100_000.0,
        peak = 100_500.0,
        lifetime = 6.0,
    )
}
