package com.calculationtests.testCostModules.expectations

import com.calculationtests.testObjects.ManufacturingEntityType
import com.tset.clientsdk.schema.entities.BomEntryEntity
import com.tset.clientsdk.schema.entities.ManufacturingEntity

open class CostModuleExpectation(
    entity: ManufacturingEntity,
    name: String? = null,
    val useLegacyStructure: Boolean = true,
    val processedMaterial: ProcessedMaterialExpectation? = null,
) : BaseExpectation<ManufacturingEntity, CostModuleExpectation>(entity, name),
    ExpectationWithSteps,
    ExpectationWithRawMaterials {
    val bomEntryExpectations = ChildExpectationList<BomEntryEntityExpectation>(ManufacturingEntityType.BOM_ENTRY.name)
    override val stepsExpectations = ChildExpectationList<StepExpectation>(ManufacturingEntityType.MANUFACTURING_STEP.name)
    override val rawMaterialExpectations = ChildExpectationList<RawMaterialExpectation>(ManufacturingEntityType.MATERIAL.name)

    fun withSubManufacturing(
        bomEntryEntity: BomEntryEntity,
        manufacturingEntityExpectation: ManufacturingEntityExpectation,
    ): CostModuleExpectation {
        bomEntryExpectations.add(
            BomEntryEntityExpectation(
                bomEntryEntity,
            ).withManufacturing(
                manufacturingEntityExpectation,
            ),
        )
        return this
    }
}
