package com.calculationtests.defaulttsetvalues

enum class LegacyTsetCO2Types(val short: String, val long: String) {
    DirectRawMaterialCO2e("DiRm-CO2e", "Direct Raw Material Co2 equivalent"),
    DirectPurchasePartCO2e("DiPp-CO2e", "Direct Purchase Part Co2 equivalent"),
    DirectMaterialCO2e("DiM-CO2e", "Direct Material Co2 equivalent"),
    RawMaterialScrapCO2e("RmScrap-CO2e", "Raw Material Scrap Co2 equivalent"),
    PurchasePartsScrapCO2e("PpScrap-CO2e", "Purchase Part Scrap Co2 equivalent"),
    MaterialScrapCO2e("MScrap-CO2e", "Material Scrap Co2 equivalent"),
    RawMaterialOverheadCO2e("RmOv-CO2e", "Raw Material Overhead Co2 equivalent"),
    PurchasePartsOverheadCO2e("PpMOv-CO2e", "Purchase Part Material-Overhead Co2 equivalent"),
    MaterialOverheadCO2e("MOv-CO2e", "Material Overhead Co2 equivalent"),
    MaterialCO2e("M-CO2e", "Material Co2 equivalent"),
    MachineDepreciationCO2e("MacDep-CO2e", "Machine Depreciation Co2 equivalent"),
    MachineFixCO2e("MacFi-CO2e", "Machine Fix Co2 equivalent"),
    MachineElectricEnergyCO2e("MacElEn-CO2e", "Machine Electric Energy Co2 equivalent"),
    MachineGasEnergyActiveCO2e("MacGasEnAct-CO2e", "Machine Gas Energy Active Co2 equivalent"),
    MachineGasEnergyPassiveCO2e("MacGasEnPas-CO2e", "Machine Gas Energy Passive Co2 equivalent"),
    MachineGasEnergyCO2e("MacGasEn-CO2e", "Machine Gas Energy Co2 equivalent"),
    MachineVariableCO2e("MacVa-CO2e", "Machine Variable Co2 equivalent"),
    MachineCO2e("Mac-CO2e", "Machine Co2 equivalent"),
    ToolCO2e("To-CO2e", "Tool Co2 equivalent"),
    RoughProcessCO2e("RoPr-CO2e", "Rough Process Co2 equivalent"),
    DirectManufacturingCO2e("DiMan-CO2e", "Direct Manufacturing Co2 equivalent"),
    ManufacturingScrapCO2e("ManScrap-CO2e", "Manufacturing Scrap Co2 equivalent"),
    ManufacturingOverheadCO2e("ManOv-CO2e", "Manufacturing Overhead Co2 equivalent"),
    ManufacturingCO2e("Man-CO2e", "Manufacturing Co2 equivalent"),
    ProductionCO2e("Pro-CO2e", "Production Co2 equivalent"),
    OverheadCO2eBaseRm("OvBaseRm-CO2e", "Overhead Base Raw Material Co2 equivalent"),
    OverheadCO2eBasePp("OvBasePp-CO2e", "Overhead Base Purchase Parts Co2 equivalent"),
    OverheadCO2eBaseMfg("OvBaseMfg-CO2e", "Overhead Base Manufacturing Co2 equivalent"),
    DevelopmentCO2e("Dev-CO2e", "Development Co2 equivalent"),
    RampUpCO2e("RaUp-CO2e", "Ramp Up Co2 equivalent"),
    PackagingAndCarrierCO2e("Pa&Ca-CO2e", "Packaging and Carrier Co2 equivalent"),
    SpecialDirectCO2e("SpeDi-CO2e", "Special Direct Co2 equivalent"),
    SalesAndGeneralAdministrationCO2e("Sa&GeAd-CO2e", "Sales and General Administration Co2 equivalent"),
    ResearchAndDevelopmentCO2e("R&D-CO2e", "Research and Development Co2 equivalent"),
    BusinessRiskCO2e("BuRi-CO2e", "Business Risk Co2 equivalent"),
    OtherExpendituresCO2eAfterP("OthExAP-CO2e", "Other Expenditures after Production Co2 equivalent"),
    DirectOverheadsCO2eAfterP("DiOvAP-CO2e", "Direct Overheads after Production Co2 equivalent"),
    OverheadsCO2eAfterP("OvAP-CO2e", "Overheads after Production Co2 equivalent"),
    BeforeProfitCO2e("BeProf-CO2e", "Before Profit Co2 equivalent"),
    ProfitCO2e("Prof-CO2e", "Profit Co2 equivalent"),
    DiscountCO2e("Dis-CO2e", "Discount Co2 equivalent"),
    TermsOfPaymentCO2e("ToP-CO2e", "Terms of Payment Co2 equivalent"),
    TransportCO2e("Trans-CO2e", "Transport Co2 equivalent"),
    CustomsDutyCO2e("CuDu-CO2e", "Custom Duty Co2 equivalent"),
    IncoTermsCO2e("Inc-CO2e", "Inco Terms Co2 equivalent"),
    IndirectCO2eAfterProduction("AP-CO2e", "Indirect Co2 equivalent After Production"),
    DevelopmentCO2eForInvest("Dev-Inv-CO2e", "Development Co2 equivalent For Invest"),
    RampUpCO2eForInvest("RampUp-Inv-CO2e", "Ramp Up Co2 equivalent For Invest"),
    PackagingAndCarrierCO2eForInvest("Pack&Carrier-Inv-CO2e", "Packaging and Carrier Co2 equivalent For Invest"),
    TotalCO2eForInvest("Tot-Inv-CO2e", "Total Invest Co2 equivalent"),
    Scope1CO2e("Sco1-CO2e", "Scope 1 Co2 equivalent"),
    Scope2CO2e("Sco2-CO2e", "Scope 2 Co2 equivalent"),
    Scope3CO2e("Sco3-CO2e", "Scope 3 Co2 equivalent"),
    TotalScopeCO2e("Scope-CO2e", "Total Scope Co2 equivalent"),
    TotalCO2e("Total-CO2e", "Total Co2 equivalent"),
}
