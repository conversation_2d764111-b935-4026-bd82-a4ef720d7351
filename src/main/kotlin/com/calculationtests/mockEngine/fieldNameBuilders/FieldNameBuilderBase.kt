package com.calculationtests.mockEngine.fieldNameBuilders

import com.nu.bom.core.publicapi.dtos.configurations.fieldnamebuilders.FieldNameSectionDto
import com.nu.bom.core.publicapi.dtos.configurations.fieldnamebuilders.ValueTypeDto


sealed class FieldNameBuilderBase(
    val valueType: ValueTypeDto,
) {
    companion object {

        const val PREFIX: String = "#"
        const val SEPARATOR: String = "_"

        inline fun <reified T : Enum<T>> String.asEnumOrNull(): T? =
            enumValues<T>().firstOrNull { (it as? FieldNameSectionDto)?.fieldNameSection.equals(this, ignoreCase = true) }

        data class DecomposedResult(
            val valueType: ValueTypeDto,
            val nonProcessedNames: List<String>,
        )

        fun decomposeOrNull(composedFieldName: String, minAdditionalNames: Int, maxAdditionalNames: Int): DecomposedResult? {
            // we expect a string of following format: #ValueType_AggregationLevel_AggregationRole_ElementTypeKey[_ExtensionName]
            val split = composedFieldName.split(SEPARATOR)
            if ((split.size < 1 + minAdditionalNames) || (split.size > 1 + maxAdditionalNames)) return null
            if (!split[0].startsWith(PREFIX)) return null

            val valueType = split[0].removePrefix(PREFIX).asEnumOrNull<ValueTypeDto>() ?: return null
            return DecomposedResult(valueType, split.drop(1))
        }
    }

    open fun getNameParts(): List<String?> = listOf(valueType.fieldNameSection)

    val fieldName: String get() {
        // Example: #Cost_SoldMaterial_[InHouse]_SalesPrice_[Base|Rate]
        return "$PREFIX${getNameParts().filterNotNull().joinToString(SEPARATOR)}"
    }

    override fun toString() = fieldName
}
