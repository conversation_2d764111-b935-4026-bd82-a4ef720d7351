package com.calculationtests.mockEngine.calculationObjects.overheadSubRoutines.transport

import com.calculationtests.mockEngine.calculationObjects.overheadSubRoutines.OverheadSubRoutine
import com.nu.bom.core.publicapi.dtos.configurations.fieldnamebuilders.ValueTypeDto

class TransportCalculator(
    private val measurements: TransportMeasurements,
    private val routes: List<TransportRoute>,
    override var shouldBeUsed: Boolean
) : OverheadSubRoutine {

    override fun calculate(valueType: ValueTypeDto) = when (valueType){
        ValueTypeDto.CO2E_CALCULATION -> routes.sumOf { it.co2(measurements) }
        ValueTypeDto.COST_CALCULATION  -> routes.sumOf { it.cost(measurements) }
    }
}