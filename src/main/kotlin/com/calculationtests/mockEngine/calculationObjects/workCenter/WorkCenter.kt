package com.calculationtests.mockEngine.calculationObjects.workCenter

import com.calculationtests.mockEngine.calculationObjects.CalculationObject
import com.nu.bom.core.publicapi.dtos.configurations.CustomProcurementTypeWrapperDto
import com.tset.clientsdk.schema.dtos.FieldDto

class WorkCenter(
    private val name: String,
    outputs: List<FieldDto> = listOf(),
    procurementType: CustomProcurementTypeWrapperDto,
    isInhouse: Boolean,
) : CalculationObject(outputs, null, procurementType, isInhouse) {
    private val _machines = mutableListOf<Machine>()
    val machines: Iterable<Machine>
        get() = _machines

    fun addMachine(machine: Machine) {
        _machines.add(machine)
        addExcelResultsOf(machine)
    }

    private val _roughMachines = mutableListOf<RoughMachine>()
    val roughMachines: Iterable<RoughMachine>
        get() = _roughMachines

    fun addRoughMachine(roughMachine: RoughMachine) {
        _roughMachines.add(roughMachine)
        addExcelResultsOf(roughMachine)
    }

    private val _workers = mutableListOf<Labor>()
    val workers: Iterable<Labor>
        get() = _workers

    fun addLabor(labor: Labor) {
        _workers.add(labor)
        addExcelResultsOf(labor)
    }

    private val _setupWorkers = mutableListOf<Labor>()
    val setupWorkers: Iterable<Labor>
        get() = _setupWorkers

    fun addSetupLabor(labor: Labor) {
        _setupWorkers.add(labor)
        addExcelResultsOf(labor)
    }

    private val _tools = mutableListOf<Tool>()
    val tools: Iterable<Tool>
        get() = _tools

    fun addTool(tool: Tool) {
        _tools.add(tool)
        addExcelResultsOf(tool)
    }

    private val _roughTools = mutableListOf<RoughTool>()
    val roughTools: Iterable<RoughTool>
        get() = _roughTools

    fun addRoughTool(roughTool: RoughTool) {
        _roughTools.add(roughTool)
        addExcelResultsOf(roughTool)
    }

    val membersWithActivities: Iterable<WorkCenterElement>
        get() = _workers + _setupWorkers + _machines + _tools + _roughTools + _roughMachines

    override fun toString(): String {
        return "WorkCenter $name"
    }
}
