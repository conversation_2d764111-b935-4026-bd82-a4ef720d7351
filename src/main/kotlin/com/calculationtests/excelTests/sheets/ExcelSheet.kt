package com.calculationtests.excelTests.sheets

import com.calculationtests.excelTests.expectations.ExcelColumnHeader
import com.calculationtests.excelTests.sheets.ExcelExtractorHelper.getCellValueAsString
import com.calculationtests.helperFunctions.approximatelyEqual
import org.apache.poi.ss.usermodel.Row
import org.apache.poi.ss.usermodel.Sheet
import org.apache.poi.ss.usermodel.WorkbookFactory
import java.io.ByteArrayInputStream

data class ExcelHeadersWithValues(
    val headerToValue: Map<ExcelColumnHeader, String>,
)

data class ExcelHeaderAsFieldNameWithValues(
    val headerToValue: Map<String, String>,
) {
    fun diffs(other: ExcelHeaderAsFieldNameWithValues?): Map<String, Pair<String, String?>> {
        if (other == null) {
            return headerToValue.mapValues { it.value to null }
        }

        val entriesInOnlyOneMap =
            getDiffsOfMaps(headerToValue, other.headerToValue) +
                getDiffsOfMaps(other.headerToValue, headerToValue)

        val sameKeyButDifferentValues =
            headerToValue.filter { (key, value) ->
                key in other.headerToValue.keys && !areSameCellValue(value, other.headerToValue[key]!!)
            }.mapValues { it.value to other.headerToValue[it.key]!! }

        return entriesInOnlyOneMap + sameKeyButDifferentValues
    }

    companion object {
        private fun getDiffsOfMaps(
            lhs: Map<String, String>,
            rhs: Map<String, String>,
        ): Map<String, Pair<String, String?>> = lhs.filter { it.key !in rhs && it.value != "" }.mapValues { it.value to null }

        private fun areSameCellValue(
            lhs: String,
            rhs: String,
        ): Boolean {
            return try {
                rhs.toDouble().approximatelyEqual(lhs.toDouble(), 1e-4)
            } catch (e: NumberFormatException) {
                rhs == lhs
            }
        }
    }
}

/**
 * A PseudoUniqueIdentifier describes a row in which we have a bunch of columns all defined by their header
 */
data class ExcelSheet(
    val rows: Map<PseudoUniqueIdentifier, ExcelHeadersWithValues>,
    val headers: List<ExcelColumnHeader>,
) {
    init {
        val distinctHeadersPerRow = rows.values.map { it.headerToValue.keys }.distinct()
        val headersInRandomOrder =
            checkNotNull(distinctHeadersPerRow.singleOrNull()) {
                "Something went wrong with the headers when creating the tset internal ExcelSheet." +
                    " We have more than one set of headers per row"
            }
        require(headersInRandomOrder == headers.toSet()) {
            "Some headers are wrong in the internal ExcelSheet"
        }
    }

    companion object {
        fun fromByteArray(
            byteArray: ByteArray,
            index: Int = 0,
        ): ExcelSheet {
            val sheet = sheetFromByteArray(byteArray, index)
            return getExcelSheet(sheet)
        }

        private fun sheetFromByteArray(
            export: ByteArray,
            index: Int,
        ): Sheet {
            val workbook = WorkbookFactory.create(ByteArrayInputStream(export))
            val sheet = workbook.getSheetAt(index)
            workbook.close()
            return sheet
        }

        private fun getExcelSheet(sheet: Sheet): ExcelSheet {
            val headers = getHeaders(sheet)
            val indexes = getIndexes(headers)
            val identifiersToValues =
                sheet.drop(1).associateBy(
                    { PseudoUniqueIdentifier.fromRowAndIndexes(it, indexes) },
                    { getValuesPerRow(it, headers) },
                )
            return ExcelSheet(identifiersToValues, headers)
        }

        private fun getHeaders(sheet: Sheet): List<ExcelColumnHeader> {
            val row = sheet.getRow(0) ?: return emptyList()
            return specialHackForQuantityUnit(row.map { ExcelColumnHeader.fromHeaderName(it.stringCellValue) })
        }

        // https://tsetplatform.atlassian.net/browse/COST-60270 quantity unit appears twice
        private fun specialHackForQuantityUnit(headers: List<ExcelColumnHeader>): List<ExcelColumnHeader> {
            val mut = headers.toMutableList()
            require(mut[ExcelColumnHeader.QuantityUnit2.ordinal] == ExcelColumnHeader.QuantityUnit) {
                "Excel Sheet has wrong header for QuantityUnit"
            }
            mut[ExcelColumnHeader.QuantityUnit2.ordinal] = ExcelColumnHeader.QuantityUnit2
            return mut
        }

        private fun getIndexes(headers: List<ExcelColumnHeader>) =
            PseudoUniqueIdentifierIndexes(
                0,
                headers.indexOf(ExcelColumnHeader.Hierarchy),
                headers.indexOf(ExcelColumnHeader.Designation),
                headers.indexOf(ExcelColumnHeader.PartNumber),
                headers.indexOf(ExcelColumnHeader.StepNumber),
            )

        private fun getValuesPerRow(
            row: Row,
            headers: List<ExcelColumnHeader>,
        ): ExcelHeadersWithValues {
            return ExcelHeadersWithValues(
                headers.withIndex().associate { (index, header) ->
                    val cell = row.getCell(index)
                    header to getCellValueAsString(cell, cell?.cellType)
                },
            )
        }
    }

    val rowsWithHeadersAsFieldNames: Map<PseudoUniqueIdentifier, ExcelHeaderAsFieldNameWithValues> =
        rows.mapValues { (id, row) ->
            ExcelHeaderAsFieldNameWithValues(
                row.headerToValue.mapKeys { (excelHeader, _) ->
                    excelHeader.fieldNamePerEntityType[id.type]
                }.filterKeys { it != null }.mapKeys { it.key!! },
            )
        }
}
