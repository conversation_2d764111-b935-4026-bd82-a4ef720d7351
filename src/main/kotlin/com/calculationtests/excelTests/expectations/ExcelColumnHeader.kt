package com.calculationtests.excelTests.expectations

import com.calculationtests.excelTests.sheets.ExcelEntityType
import com.calculationtests.mockEngine.calculationObjects.workCenter.Machine
import com.calculationtests.mockEngine.calculationObjects.workCenter.Tool
import com.tset.clientsdk.schema.entities.LaborEntity
import com.tset.clientsdk.schema.entities.ManufacturingEntity

enum class ExcelColumnHeader(val headerName: String, val fieldNamePerEntityType: Map<ExcelEntityType, String> = emptyMap()) {
    ObjectType("Object type"),
    Hierarchy("Hierarchy"),
    CostCO2Unit("Cost/CO2 unit"),
    QuantityUnit("Quantity unit"),
    StepNumber("Step number"),
    Designation("Designation"),
    PartNumber("Part number"),
    Quantity("Quantity"),
    QuantityUnit2("Quantity unit"),
    Type("Type"),
    BaseCurrency("Base currency"),
    DisplayCurrency("Display currency"),
    Procurement("Procurement"),
    PeakVolumePerYear("Peak volume per year", mapOf(ExcelEntityType.PART to ManufacturingEntity::peakUsableProductionVolumePerYear.name)),
    PeakVolumePerYearUnit("Peak volume per year unit"),
    AverageVolumePerYear(
        "Average volume per year",
        mapOf(ExcelEntityType.PART to ManufacturingEntity::averageUsableProductionVolumePerYear.name),
    ),
    AverageVolumePerYearUnit("Average volume per year unit"),
    Lifetime("Lifetime", mapOf(ExcelEntityType.PART to ManufacturingEntity::lifeTime.name)),
    LifetimeUnit("Lifetime unit"),
    LotsPerYear("Lots per year", mapOf(ExcelEntityType.PART to ManufacturingEntity::callsPerYear.name)),
    DMCTotalPrice("DMC (Total price)", mapOf(ExcelEntityType.PART to ManufacturingEntity.Override::costPerPart.name)),
    DMCTotalPriceUnit("DMC (Total price) unit"),
    MaterialPurchasePricePerUnit("Material purchase price per unit"),
    MaterialPurchasePricePerUnitUnit("Material purchase price per unit unit"),
    Region("Region", mapOf(ExcelEntityType.PART to ManufacturingEntity.Override::locationName.name)),
    OverheadMethod("Overhead method"),
    Responsible("Responsible"),
    InterestRate(
        "Interest rate",
        mapOf(
            ExcelEntityType.PART to ManufacturingEntity.Override::interestRate.name,
            ExcelEntityType.TOOL to Tool::interestRate.name,
            ExcelEntityType.MACHINE to Machine::interestRate.name,
        ),
    ),
    DevelopmentCosts("Development costs"),
    DevelopmentCostsUnit("Development costs unit"),
    DevelopmentManufacturingOverheadBase("Development manufacturing overhead base"),
    DevelopmentManufacturingOverheadBaseUnit("Development manufacturing overhead base unit"),
    DevelopmentManufacturingOverheadRate("Development manufacturing overhead rate"),
    DevelopmentRawMaterialsOverheadBase("Development raw materials overhead base"),
    DevelopmentRawMaterialsOverheadBaseUnit("Development raw materials overhead base unit"),
    DevelopmentRawMaterialsOverheadRate("Development raw materials overhead rate"),
    DevelopmentPurchasedSubpartsOverheadBase("Development purchased subparts overhead base"),
    DevelopmentPurchasedSubpartsOverheadBaseUnit("Development purchased subparts overhead base unit"),
    DevelopmentPurchasedSubpartsOverheadRate("Development purchased subparts overhead rate"),
    RampUpCosts("Ramp up costs"),
    RampUpCostsUnit("Ramp up costs unit"),
    RampUpManufacturingOverheadBase("Ramp up manufacturing overhead base"),
    RampUpManufacturingOverheadBaseUnit("Ramp up manufacturing overhead base unit"),
    RampUpManufacturingOverheadRate("Ramp up manufacturing overhead rate"),
    RampUpRawMaterialsOverheadBase("Ramp up raw materials overhead base"),
    RampUpRawMaterialsOverheadBaseUnit("Ramp up raw materials overhead base unit"),
    RampUpRawMaterialsOverheadRate("Ramp up raw materials overhead rate"),
    RampUpPurchasedSubpartsOverheadBase("Ramp up purchased subparts overhead base"),
    RampUpPurchasedSubpartsOverheadBaseUnit("Ramp up purchased subparts overhead base unit"),
    RampUpPurchasedSubpartsOverheadRate("Ramp up purchased subparts overhead rate"),
    PackagingAndCarrierCosts("Packaging and carrier costs"),
    PackagingAndCarrierCostsUnit("Packaging and carrier costs unit"),
    PackagingAndCarrierManufacturingOverheadBase("Packaging and carrier manufacturing overhead base"),
    PackagingAndCarrierManufacturingOverheadBaseUnit("Packaging and carrier manufacturing overhead base unit"),
    PackagingAndCarrierManufacturingOverheadRate("Packaging and carrier manufacturing overhead rate"),
    PackagingAndCarrierRawMaterialsOverheadBase("Packaging and carrier raw materials overhead base"),
    PackagingAndCarrierRawMaterialsOverheadBaseUnit("Packaging and carrier raw materials overhead base unit"),
    PackagingAndCarrierRawMaterialsOverheadRate("Packaging and carrier raw materials overhead rate"),
    PackagingAndCarrierPurchasedSubpartsOverheadBase("Packaging and carrier purchased subparts overhead base"),
    PackagingAndCarrierPurchasedSubpartsOverheadBaseUnit("Packaging and carrier purchased subparts overhead base unit"),
    PackagingAndCarrierPurchasedSubpartsOverheadRate("Packaging and carrier purchased subparts overhead rate"),
    BusinessRiskCosts("Business risk costs"),
    BusinessRiskCostsUnit("Business risk costs unit"),
    BusinessRiskManufacturingOverheadBase("Business risk manufacturing overhead base"),
    BusinessRiskManufacturingOverheadBaseUnit("Business risk manufacturing overhead base unit"),
    BusinessRiskManufacturingOverheadRate("Business risk manufacturing overhead rate"),
    BusinessRiskRawMaterialsOverheadBase("Business risk raw materials overhead base"),
    BusinessRiskRawMaterialsOverheadBaseUnit("Business risk raw materials overhead base unit"),
    BusinessRiskRawMaterialsOverheadRate("Business risk raw materials overhead rate"),
    BusinessRiskPurchasedSubpartsOverheadBase("Business risk purchased subparts overhead base"),
    BusinessRiskPurchasedSubpartsOverheadBaseUnit("Business risk purchased subparts overhead base unit"),
    BusinessRiskPurchasedSubpartsOverheadRate("Business risk purchased subparts overhead rate"),
    InterestOnFinishedProduct("Interest on finished product"),
    InterestOnFinishedProductUnit("Interest on finished product unit"),
    InterestOnFinishedProductStockInterestBase("Interest on finished product stock interest base"),
    InterestOnFinishedProductStockInterestBaseUnit("Interest on finished product stock interest base unit"),
    InterestOnFinishedProductStockInterestRate("Interest on finished product stock interest rate"),
    OtherExpenditures("Other expenditures"),
    OtherExpendituresUnit("Other expenditures unit"),
    OtherExpendituresAfterProductionManufacturingOverheadBase("Other expenditures after production manufacturing overhead base"),
    OtherExpendituresAfterProductionManufacturingOverheadBaseUnit("Other expenditures after production manufacturing overhead base unit"),
    OtherExpendituresAfterProductionManufacturingOverheadRate("Other expenditures after production manufacturing overhead rate"),
    OtherExpendituresAfterProductionRawMaterialsOverheadBase("Other expenditures after production raw materials overhead base"),
    OtherExpendituresAfterProductionRawMaterialsOverheadBaseUnit("Other expenditures after production raw materials overhead base unit"),
    OtherExpendituresAfterProductionRawMaterialsOverheadRate("Other expenditures after production raw materials overhead rate"),
    OtherExpendituresAfterProductionPurchasedSubpartsOverheadBase("Other expenditures after production purchased subparts overhead base"),
    OtherExpendituresAfterProductionPurchasedSubpartsOverheadBaseUnit(
        "Other expenditures after production purchased subparts overhead base unit",
    ),
    OtherExpendituresAfterProductionPurchasedSubpartsOverheadRate("Other expenditures after production purchased subparts overhead rate"),
    ResearchAndDevelopmentCosts("Research and development costs"),
    ResearchAndDevelopmentCostsUnit("Research and development costs unit"),
    ResearchAndDevelopmentManufacturingOverheadBase("Research and development manufacturing overhead base"),
    ResearchAndDevelopmentManufacturingOverheadBaseUnit("Research and development manufacturing overhead base unit"),
    ResearchAndDevelopmentManufacturingOverheadRate("Research and development manufacturing overhead rate"),
    ResearchAndDevelopmentRawMaterialsOverheadBase("Research and development raw materials overhead base"),
    ResearchAndDevelopmentRawMaterialsOverheadBaseUnit("Research and development raw materials overhead base unit"),
    ResearchAndDevelopmentRawMaterialsOverheadRate("Research and development raw materials overhead rate"),
    ResearchAndDevelopmentPurchasedSubpartsOverheadBase("Research and development purchased subparts overhead base"),
    ResearchAndDevelopmentPurchasedSubpartsOverheadBaseUnit("Research and development purchased subparts overhead base unit"),
    ResearchAndDevelopmentPurchasedSubpartsOverheadRate("Research and development purchased subparts overhead rate"),
    SalesAndAdminCosts("Sales and admin costs"),
    SalesAndAdminCostsUnit("Sales and admin costs unit"),
    SalesAndAdminManufacturingOverheadBase("Sales and admin manufacturing overhead base"),
    SalesAndAdminManufacturingOverheadBaseUnit("Sales and admin manufacturing overhead base unit"),
    SalesAndAdminManufacturingOverheadRate("Sales and admin manufacturing overhead rate"),
    SalesAndAdminRawMaterialsOverheadBase("Sales and admin raw materials overhead base"),
    SalesAndAdminRawMaterialsOverheadBaseUnit("Sales and admin raw materials overhead base unit"),
    SalesAndAdminRawMaterialsOverheadRate("Sales and admin raw materials overhead rate"),
    SalesAndAdminPurchasedSubpartsOverheadBase("Sales and admin purchased subparts overhead base"),
    SalesAndAdminPurchasedSubpartsOverheadBaseUnit("Sales and admin purchased subparts overhead base unit"),
    SalesAndAdminPurchasedSubpartsOverheadRate("Sales and admin purchased subparts overhead rate"),
    DirectOverheadsAfterProduction("Direct overheads after production"),
    DirectOverheadsAfterProductionUnit("Direct overheads after production unit"),
    DirectOverheadsAfterProductionManufacturingOverheadBase("Direct overheads after production manufacturing overhead base"),
    DirectOverheadsAfterProductionManufacturingOverheadBaseUnit("Direct overheads after production manufacturing overhead base unit"),
    DirectOverheadsAfterProductionManufacturingOverheadRate("Direct overheads after production manufacturing overhead rate"),
    DirectOverheadsAfterProductionRawMaterialsOverheadBase("Direct overheads after production raw materials overhead base"),
    DirectOverheadsAfterProductionRawMaterialsOverheadBaseUnit("Direct overheads after production raw materials overhead base unit"),
    DirectOverheadsAfterProductionRawMaterialsOverheadRate("Direct overheads after production raw materials overhead rate"),
    DirectOverheadsAfterProductionPurchasedSubpartsOverheadBase("Direct overheads after production purchased subparts overhead base"),
    DirectOverheadsAfterProductionPurchasedSubpartsOverheadBaseUnit(
        "Direct overheads after production purchased subparts overhead base unit",
    ),
    DirectOverheadsAfterProductionPurchasedSubpartsOverheadRate("Direct overheads after production purchased subparts overhead rate"),
    Profit("Profit"),
    ProfitUnit("Profit unit"),
    ProfitManufacturingOverheadBase("Profit manufacturing overhead base"),
    ProfitManufacturingOverheadBaseUnit("Profit manufacturing overhead base unit"),
    ProfitManufacturingOverheadRate("Profit manufacturing overhead rate"),
    ProfitRawMaterialsOverheadBase("Profit raw materials overhead base"),
    ProfitRawMaterialsOverheadBaseUnit("Profit raw materials overhead base unit"),
    ProfitRawMaterialsOverheadRate("Profit raw materials overhead rate"),
    ProfitPurchasedSubpartsOverheadBase("Profit purchased subparts overhead base"),
    ProfitPurchasedSubpartsOverheadBaseUnit("Profit purchased subparts overhead base unit"),
    ProfitPurchasedSubpartsOverheadRate("Profit purchased subparts overhead rate"),
    Discount("Discount"),
    DiscountUnit("Discount unit"),
    DiscountOverheadBase("Discount overhead base"),
    DiscountOverheadBaseUnit("Discount overhead base unit"),
    DiscountOverheadRate("Discount overhead rate"),
    InterestForTermsOfPayment("Interest for terms of payment"),
    InterestForTermsOfPaymentUnit("Interest for terms of payment unit"),
    InterestForTermsOfPaymentOverheadBase("Interest for terms of payment overhead base"),
    InterestForTermsOfPaymentOverheadBaseUnit("Interest for terms of payment overhead base unit"),
    InterestForTermsOfPaymentInterestRate("Interest for terms of payment interest rate"),
    CustomsDuty("Customs duty"),
    CustomsDutyUnit("Customs duty unit"),
    CustomsDutyOverheadBase("Customs duty overhead base"),
    CustomsDutyOverheadBaseUnit("Customs duty overhead base unit"),
    CustomsDutyOverheadRate("Customs duty overhead rate"),
    TransportCosts("Transport costs"),
    TransportCostsUnit("Transport costs unit"),
    TransportOverheadBase("Transport overhead base"),
    TransportOverheadBaseUnit("Transport overhead base unit"),
    TransportOverheadRate("Transport overhead rate"),
    RawMaterialInterestDays("Raw material interest days"),
    RawMaterialInterestDaysUnit("Raw material interest days unit"),
    PurchasedPartInterestDays("Purchased part interest days"),
    PurchasedPartInterestDaysUnit("Purchased part interest days unit"),
    LaborCost("Labor cost"),
    LaborCostUnit("Labor cost unit"),
    ReuseOfScrap("Reuse of scrap"),
    MaterialScrapCosts("Material scrap costs"),
    MaterialScrapCostsUnit("Material scrap costs unit"),
    MaterialOverheadCosts("Material overhead costs"),
    MaterialOverheadCostsUnit("Material overhead costs unit"),
    InterestOnMaterialStock("Interest on material stock"),
    InterestOnMaterialStockUnit("Interest on material stock unit"),
    DaysPerWeek("Days per week"),
    WeeksPerYear("Weeks per year"),
    ExtraShifts("Extra shifts"),
    TimePerShift("Time per shift"),
    TimePerShiftUnit("Time per shift unit"),
    ShiftsPerDay("Shifts per day"),
    CostPerPart("Cost per part", mapOf(ExcelEntityType.PART to ManufacturingEntity.Override::costPerPart.name)),
    CostPerPartUnit("Cost per part unit"),
    MasterDataLookupDate("Master data lookup date"),
    InvestBase("Base investment"),
    InvestBaseUnit("Base investment unit"),
    InvestSetup("Setup investment"),
    InvestSetupUnit("Setup investment unit"),
    InvestOther("Other investment"),
    InvestOtherUnit("Other investment unit"),
    ElectricityCost("Electricity cost"),
    ElectricityCostUnit("Electricity cost unit"),
    GasPricePerM("Gas price per m³"),
    GasPricePerMUnit("Gas price per m³ unit"),
    OxygenPricePerKg("Oxygen price per kg"),
    OxygenPricePerKgUnit("Oxygen price per kg unit"),
    CastExcipientsPerKg("Cast excipients per kg"),
    CastExcipientsPerKgUnit("Cast excipients per kg unit"),
    InvestFoundation("Foundation investment"),
    InvestFoundationUnit("Foundation investment unit"),
    TotalInvest("Total investment"),
    TotalInvestUnit("Total investment unit"),
    SpaceCostPerMProductionArea("Space cost per m² production area"),
    SpaceCostPerMProductionAreaUnit("Space cost per m² production area unit"),
    DepreciationTime("Depreciation time"),
    DepreciationTimeUnit("Depreciation time unit"),
    MaintenanceCostPerYear("Maintenance cost per year"),
    MaintenanceCostPerYearUnit("Maintenance cost per year unit"),
    OperationalSupplyRate("Operational supply rate"),
    PowerUtilization("Power utilization"),
    ConnectedLoad("Connected load"),
    ConnectedLoadUnit("Connected load unit"),
    GrossRequiredSpace("Gross required space"),
    GrossRequiredSpaceUnit("Gross required space unit"),
    AgeInYears("Age in years"),
    MaintenanceRate("Maintenance rate"),
    ApportionmentType("Apportionment type"),
    VariableCostFractionNonOccupancy("Variable cost fraction non occupancy"),
    SystemDownTime("System down time"),
    SystemDownTimeUnit("System down time unit"),
    SystemOccupancyPerMachine("System occupancy per machine"),
    FixedCostPerHour("Fixed cost per hour"),
    FixedCostPerHourUnit("Fixed cost per hour unit"),
    VariableCostPerHour("Variable cost per hour"),
    VariableCostPerHourUnit("Variable cost per hour unit"),
    MachineHourlyRate("Machine hourly rate"),
    MachineHourlyRateUnit("Machine hourly rate unit"),
    CastExcipientsConsumptionCostPerHour("Cast excipients consumption cost per hour"),
    CastExcipientsConsumptionCostPerHourUnit("Cast excipients consumption cost per hour unit"),
    GasConsumptionMelting("Gas consumption melting"),
    GasConsumptionMeltingUnit("Gas consumption melting unit"),
    GasConsumptionMeltingCostPerHour("Gas consumption melting cost per hour"),
    GasConsumptionMeltingCostPerHourUnit("Gas consumption melting cost per hour unit"),
    GasConsumptionKeepWarmPerHour("Gas consumption keep warm per hour"),
    GasConsumptionKeepWarmPerHourUnit("Gas consumption keep warm per hour unit"),
    GasConsumptionKeepWarmCostPerHour("Gas consumption keep warm cost per hour"),
    GasConsumptionKeepWarmCostPerHourUnit("Gas consumption keep warm cost per hour unit"),
    SumCostPerHour("Sum cost per hour"),
    SumCostPerHourUnit("Sum cost per hour unit"),
    PricePerUnitForRoughStep("Price per unit for rough step"),
    PricePerUnitForRoughStepUnit("Price per unit for rough step unit"),
    StepUnit("Step unit"),
    ProductionTimePerYear("Production time per year"),
    ProductionTimePerYearUnit("Production time per year unit"),
    ManufacturingScrapRate("Manufacturing scrap rate"),
    PartsPerCycle("Parts per cycle"),
    PartsPerCycleUnit("Parts per cycle unit"),
    UtilizationRate("Utilization rate"),
    LaborUtilizationRate("Labor utilization rate"),
    ScrapRateWithoutSetup("Scrap rate without setup"),
    ScrapCosts("Scrap costs"),

    // See COST-59582
    // ScrapCostsUnit("Scrap costs unit"),
    CycleTime("Cycle time"),
    CycleTimeUnit("Cycle time unit"),
    RMOCRate("RMOC rate"),
    RMOC("RMOC"),
    RMOCUnit("RMOC unit"),
    AllocatedToolCosts("Allocated tool costs"),
    AllocatedToolCostsUnit("Allocated tool costs unit"),
    ConsiderationOfDirectLabor("Consideration of direct labor"),
    SetupSystemCosts("Setup system costs"),
    SetupSystemCostsUnit("Setup system costs unit"),
    SetupDirectLaborCosts("Setup direct labor costs"),
    SetupDirectLaborCostsUnit("Setup direct labor costs unit"),
    LotFraction("Lot fraction"),
    LotSize("Lot size"),
    LotSizeUnit("Lot size unit"),
    PeakUsablePartsPerLot("Peak usable parts per lot"),
    PeakUsablePartsPerLotUnit("Peak usable parts per lot unit"),
    ToolCostPerHour("Tool cost per hour"),
    ToolCostPerHourUnit("Tool cost per hour unit"),
    AllocatedToolCost("Allocated tool cost"),
    AllocatedToolCostUnit("Allocated tool cost unit"),
    ToolTotalInvestment("Tool total investment"),
    ToolTotalInvestmentUnit("Tool total investment unit"),
    ToolMaintenanceCostPerPart("Tool maintenance cost per part"),
    ToolMaintenanceCostPerPartUnit("Tool maintenance cost per part unit"),
    ToolServiceLife("Tool service life"),
    InvestmentPerTool("Investment per tool"),
    InvestmentPerToolUnit("Investment per tool unit"),
    ToolProportionalInvestment("Tool proportional investment"),
    ToolDirectlyPaidQuantityPerPart("Tool directly paid quantity per part"),
    ToolAllocationMode("Tool allocation mode"),
    AllocatedToolInvestCostPerPart("Allocated tool invest cost per part"),
    AllocatedToolInvestCostPerPartUnit("Allocated tool invest cost per part unit"),
    AllocatedToolInterestCostPerPart("Allocated tool interest cost per part"),
    AllocatedToolInterestCostPerPartUnit("Allocated tool interest cost per part unit"),
    InvestmentType("Investment type"),
    InvestmentPerUnit("Investment per unit"),
    AllocationRate("Allocation rate"),
    AllocatedInvestment("Allocated investment"),
    DirectlyPaidQuantity("Directly paid quantity"),
    DirectlyPaidInvestment("Directly paid investment"),
    CalculationQuality("Calculation quality"),
    NetWeight("Net weight"),
    NetWeightUnit("Net weight unit"),
    LossWeight("Loss weight"),
    LossWeightUnit("Loss weight unit"),
    ScrapWeight("Scrap weight"),
    ScrapWeightUnit("Scrap weight unit"),
    MaterialType("Material type"),
    MaterialRecyclingPricePerKg("Material recycling price per kg"),
    MaterialRecyclingPricePerKgUnit("Material recycling price per kg unit"),
    MaterialWastePricePerKg("Material waste price per kg"),
    MaterialWastePricePerKgUnit("Material waste price per kg unit"),
    MaterialCostMode("Material cost mode"),
    PurchasedWeightPerPart("Purchased weight per part"),
    PurchasedWeightPerPartUnit("Purchased weight per part unit"),
    IrretrievableScrapPerPart("Irretrievable scrap per part"),
    IrretrievableScrapPerPartUnit("Irretrievable scrap per part unit"),
    RetrievableScrapPerPart("Retrievable scrap per part"),
    RetrievableScrapPerPartUnit("Retrievable scrap per part unit"),
    RetrievableScrapCostsPerPart("Retrievable scrap costs per part"),
    RetrievableScrapCostsPerPartUnit("Retrievable scrap costs per part unit"),
    RecycledRetrievableScrapPerPart("Recycled retrievable scrap per part"),
    RecycledRetrievableScrapPerPartUnit("Recycled retrievable scrap per part unit"),
    RecycledRetrievableScrapCostsPerPart("Recycled retrievable scrap costs per part"),
    RecycledRetrievableScrapCostsPerPartUnit("Recycled retrievable scrap costs per part unit"),
    SoldIrretrievableScrapPerPart("Sold irretrievable scrap per part"),
    SoldIrretrievableScrapPerPartUnit("Sold irretrievable scrap per part unit"),
    SoldIrretrievableScrapCostsPerPart("Sold irretrievable scrap costs per part"),
    SoldIrretrievableScrapCostsPerPartUnit("Sold irretrievable scrap costs per part unit"),
    MaterialScrapRate("Material scrap rate"),
    AssignedManufacturingStep("Assigned manufacturing step"),
    MPN("MPN"),
    ElectronicComponentDiscountingFactor("Electronic component discounting factor"),
    ElectronicComponentDefaultDiscountApplied("Electronic component default discount applied"),
    MountingType("Mounting type"),
    Manufacturer("Manufacturer"),
    WagePerHour("Wage per hour"),
    WagePerHourUnit("Wage per hour unit"),
    LaborBurden(
        "Labor burden",
        mapOf(
            // https://tsetplatform.atlassian.net/browse/COST-60379
            // ExcelEntityType.PART to LaborEntity.Override::laborBurden.name,
            ExcelEntityType.LABOR to LaborEntity.Override::laborBurden.name,
            ExcelEntityType.SETUP to LaborEntity.Override::laborBurden.name,
        ),
    ),
    SkillType("Skill type"),
    SetupTime("Setup time"),
    SetupTimeUnit("Setup time unit"),
    SetupType("Setup type"),
    ;

    companion object {
        fun fromHeaderName(headerName: String): ExcelColumnHeader {
            // https://tsetplatform.atlassian.net/browse/COST-60270 quantity unit appears twice
            if (headerName == "Quantity unit") {
                return QuantityUnit
            }

            return checkNotNull(ExcelColumnHeader.values().singleOrNull { it.headerName == headerName }) {
                "Can not find correct Excel Header for $headerName"
            }
        }

        fun headerExistsForExcelEntityTypeAndFieldName(
            excelEntityType: ExcelEntityType,
            fieldName: String,
        ): Boolean {
            return fromForExcelEntityTypeAndFieldName(excelEntityType, fieldName) != null
        }

        private fun fromForExcelEntityTypeAndFieldName(
            excelEntityType: ExcelEntityType,
            fieldName: String,
        ): ExcelColumnHeader? = excelEntityTypeAndFieldNameToColumnHeader[Pair(excelEntityType, fieldName)]

        private val excelEntityTypeAndFieldNameToColumnHeader = getExcelEntityTypeAndFieldNameToColumnHeader()

        private fun getExcelEntityTypeAndFieldNameToColumnHeader(): Map<Pair<ExcelEntityType, String>, ExcelColumnHeader> {
            return values().map { excelColumnHeader ->
                excelColumnHeader.fieldNamePerEntityType.map { (excelEntityType, fieldName) ->
                    Pair(excelEntityType, fieldName) to excelColumnHeader
                }.toMap()
            }.reduce { acc, map -> acc + map }
        }
    }
}
