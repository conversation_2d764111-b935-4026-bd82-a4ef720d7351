package com.tset.clientsdk.schema

import com.nu.bom.core.publicapi.dtos.FieldParameter
import com.nu.bom.core.publicapi.dtos.MasterDataDetails
import com.nu.bom.core.publicapi.dtos.MasterdataKey
import com.tset.clientsdk.schema.dtos.BomNodeDto
import com.tset.clientsdk.schema.dtos.CreateBomNodeDto
import com.tset.clientsdk.schema.dtos.FieldDto
import com.tset.clientsdk.schema.dtos.PartDto
import com.tset.clientsdk.schema.quantities.Emission
import com.tset.clientsdk.schema.quantities.QuantityTypeBase
import org.slf4j.LoggerFactory

private val log = LoggerFactory.getLogger(BaseEntity::class.java)

abstract class BaseEntity {
    open val entityType = "BASE"
    open val entityName = "BaseEntityFields"
    open val entityDisplayName: String? = null

    open class Override

    open val override = Override()

    open fun inputs(): List<FieldDto> = emptyList()

    open fun setInputs(fieldMap: Map<String, FieldDto>) {}

    open fun overrides(): List<FieldDto> = emptyList()

    open fun setOverrides(fieldMap: Map<String, FieldDto>) {}

    private val childrenInternal = mutableListOf<BaseEntity>()
    val children: List<BaseEntity> get() = childrenInternal.toList()

    var uniqueExternalIdentifier: String? = null
    var isolated: Boolean = false
    var masterdataKey: MasterdataKey? = null

    internal fun addChildren(children: Collection<BaseEntity>) {
        childrenInternal.addAll(children)
    }

    var partName: String? = null
    var partNumber: String = ""

    var outputs: List<FieldDto> = listOf()
    val behaviours = mutableListOf<BaseEntity>()

    private val dynamicOverrideFields = mutableListOf<FieldDto>()

    fun addDynamicOverrideField(fieldDto: FieldDto) {
        dynamicOverrideFields.add(fieldDto)
    }

    fun addDynamicOverridePeriodField(
        fieldName: String,
        period: QuantityTypeBase,
    ) {
        dynamicOverrideFields.add(FieldDto(fieldName, period.value, "TimeInYears", period.unitName))
    }

    fun addDynamicOverrideRateField(
        fieldName: String,
        rate: Double,
    ) {
        dynamicOverrideFields.add(FieldDto(fieldName, rate, "Rate"))
    }

    fun addDynamicOverrideMoneyField(
        fieldName: String,
        cost: Double,
    ) {
        dynamicOverrideFields.add(FieldDto(fieldName, cost, "Money"))
    }

    fun addDynamicOverrideEmissionField(
        fieldName: String,
        emission: Emission,
    ) {
        dynamicOverrideFields.add(FieldDto(fieldName, emission.value, "Emission", emission.unitName))
    }

    fun dynamicOverrides() = dynamicOverrideFields

    // because bom-rads has problems with longer names
    fun bomNodeName() = (uniqueExternalIdentifier ?: this.toString()).takeLast(200)

    fun toDto(): CreateBomNodeDto {
        return CreateBomNodeDto(
            entityType,
            bomNodeName(),
            entityName,
            PartDto(partName ?: "Unknown", partNumber),
            inputs = inputs() + behaviours.flatMap { it.inputs() },
            overrides =
                overrides() + dynamicOverrides() +
                    behaviours.flatMap { it.overrides() + it.dynamicOverrides() },
            children =
                children.map { it.toDto() } +
                    behaviours.flatMap { it.children.map { child -> child.toDto() } },
            isolated = isolated,
            masterdataKey = masterdataKey,
        )
    }

    fun initializeFromBomNodeDto(bomNodeDto: BomNodeDto) {
        log.debug("deserializing node of type '${bomNodeDto.manufacturingType}'")

        outputs = bomNodeDto.outputs ?: listOf()

        val inputFields = bomNodeDto.inputs?.associateBy { it.name } ?: mapOf()
        val overrideFields = bomNodeDto.overrides?.associateBy { it.name } ?: mapOf()
        val outputFields = outputs.associateBy { it.name }

        setInputs(inputFields)
        setOverrides(overrideFields)

        setInputs(outputFields)
        setOverrides(outputFields)

        uniqueExternalIdentifier = bomNodeDto.name

        val childNodes = bomNodeDto.children ?: listOf()

        childNodes.forEach { childNode ->
            val childEntity = EntityFactory.create(childNode.manufacturingType)
            childEntity.initializeFromBomNodeDto(childNode)
            addChildren(listOf(childEntity))
        }
    }

    fun toMasterDataDto(key: String): MasterDataDetails {
        val inputs = inputs()
        return MasterDataDetails(
            entityType,
            key,
            fields = inputs.map { FieldParameter(it.name, it.value, it.type, it.unit, "I") },
            children = listOf(),
        )
    }

    companion object {
        fun fromBomNodeDto(bomNodeDto: BomNodeDto): BaseEntity {
            val entity = EntityFactory.create(bomNodeDto.manufacturingType)
            entity.initializeFromBomNodeDto(bomNodeDto)
            return entity
        }
    }
}

fun <T : BaseEntity> T.with(vararg children: BaseEntity): T {
    this.addChildren(children.asList())
    return this
}

fun <T : BaseEntity> T.with(vararg childrenLists: List<BaseEntity>): T {
    childrenLists.forEach {
        this.addChildren(it)
    }
    return this
}

fun <T : BaseEntity> T.withIsolated(): T {
    return this.apply { isolated = true }
}
