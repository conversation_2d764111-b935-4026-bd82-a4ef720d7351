package com.tset.clientsdk.schema.dtos

import com.fasterxml.jackson.annotation.JsonInclude

@JsonInclude(JsonInclude.Include.NON_NULL)
class FieldDto(
    // example: location
    // The name of the field
    val name: String,

    // example: Austria
    // The actual value of the field
    val value: Any?,

    // example: Text
    // The type of the field
    val type: String,

    // example: YEARS
    // If the type is a type that requires a unit, it is stated in this field
    val unit: String? = null,

    // example: "I"
    // The source of the field, can be either "I" if input by the user, or "M" if the data comes from master data
    val source: String? = null
)
