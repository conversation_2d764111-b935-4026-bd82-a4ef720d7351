package com.tset.clientsdk.schema.dtos

import com.tset.clientsdk.schema.FieldType
import java.util.*
import kotlin.math.abs


val BomNodeDto.entityName: String get() = "${this.manufacturingType}Entity"

// region entities
fun BomNodeDto.findChildEntity(childEntityName: String): BomNodeDto? = this.findChildEntityAtLevel(childEntityName, 2)
fun BomNodeDto.findGrandChildEntity(grandChildEntityName: String): BomNodeDto? = this.findChildEntityAtLevel(grandChildEntityName, 3)

fun BomNodeDto.findChildEntityAtLevel(childEntityName: String, level: Int): BomNodeDto? {
    val stack = this.findEntityPathRecursive(childEntityName, level = level) ?: return null
    if (stack.size == level) return stack.pop()
    return null
}

fun BomNodeDto.findEntityRecursive(entityName: String): BomNodeDto? {
    val stack = this.findEntityPathRecursive(entityName) ?: return null
    return stack.pop()
}

fun BomNodeDto.findEntityPathRecursive(entityName: String, typeName: String? = null, level: Int? = null): Stack<BomNodeDto>? {
    val stack = Stack<BomNodeDto>()
    if (this.findEntityPathRecursive(entityName, stack, typeName, level)) return stack
    return null
}
fun BomNodeDto.findEntityPathRecursive(entityName: String, stack: Stack<BomNodeDto>, typeName: String?, levelIfBomNodeIsSetToLevelOne: Int? = null): Boolean {
    val typeMatch = (typeName == null) || (typeName == "${this.manufacturingType}Entity")

    val levelShouldBeChecked = levelIfBomNodeIsSetToLevelOne == null || levelIfBomNodeIsSetToLevelOne == 1

    if (typeMatch && this.name == entityName && levelShouldBeChecked) {
        stack.push(this)
        return true
    }

    val newLevelToBeChecked = levelIfBomNodeIsSetToLevelOne?.let { it - 1 }

    stack.push(this)
    val children = this.children ?: return false

    for (childNode in children) {
        if (childNode.findEntityPathRecursive(entityName, stack, typeName, newLevelToBeChecked)) return true
    }
    stack.pop()

    return false
}

fun BomNodeDto.assertEntityExists(entityName: String, message: String? = null): BomNodeDto {
    return findEntityRecursive(entityName) ?: throw Exception(message ?: "Entity $entityName does not exist")
}

fun BomNodeDto.assertEntityDoesNotExist(entityName: String, message: String? = null) {
    if (findEntityRecursive(entityName) != null) {
        throw Exception(message ?: "Entity $entityName exists, but should not")
    }
}

fun BomNodeDto.assertEntityExistOnCondition(entityName: String, shouldExist: Boolean, message: String? = null) {
    val entity = findEntityRecursive(entityName)
    if (entity == null && shouldExist) {
        throw Exception(message ?: "Entity $entityName does not exist")
    }
    if (entity != null && !shouldExist) {
        throw Exception(message ?: "Entity $entityName exists, but should not")
    }
}

fun BomNodeDto.assertEntityIsOfType(entityTypeName: String?, message: String? = null) {
    assert(entityTypeName == "${this.manufacturingType}Entity") { message ?: "" }
}

fun BomNodeDto.assertEntityIsOfType(entityName: String, entityTypeName: String?, message: String? = null): BomNodeDto {
    val entity = findEntityRecursive(entityName) ?: throw Exception(message ?: "Entity $entityName does not exist")
    assert(entityTypeName == "${entity.manufacturingType}Entity") { message ?: "" }
    return entity
}

fun BomNodeDto.assertEntityHasChild(childEntityName: String): BomNodeDto {
/*
    val parent = findEntityRecursive(this.entityName)
    assert(parent != null) { "Parent entity:$entityName not found!" }
*/
    val child = this.findChildEntity(childEntityName)
    assert(child != null) { "Child entity:$childEntityName under parent:$entityName not found!" }
    return child!!
}

fun BomNodeDto.assertEntityHasGrandChild(grandChildEntityName: String): BomNodeDto {
    /*
    val parent = findEntityRecursive(this.entityName)
    assert(parent != null) { "Parent entity:$entityName not found!" }
    */
    val grandChild = this.findGrandChildEntity(grandChildEntityName)
    assert(grandChild != null) { "Grand child entity:$grandChildEntityName under parent:$entityName not found!" }
    return grandChild!!
}

fun BomNodeDto.assertEntityExistsAndIsUsed(entityName: String, typeName: String? = null): Stack<BomNodeDto> {
    val stack = findEntityPathRecursive(entityName, typeName)
    assert(stack != null) { "Entity \"$entityName\" not found" }

    assert(stack!!.size > 1) { "Entity \"$entityName\" has no parent." }

    return stack
}

// endregion

// region fields

fun BomNodeDto.fieldValue(fieldName: String) = this.findField(fieldName)?.value

fun BomNodeDto.fieldUnit(fieldName: String) = this.findField(fieldName)?.unit

fun BomNodeDto.findField(fieldName: String): FieldDto? = findField(FieldType.Inputs, fieldName)
    ?: findField(FieldType.Overrides, fieldName)
    ?: findField(FieldType.Outputs, fieldName)

fun BomNodeDto.fieldValue(fieldType: FieldType, fieldName: String): Any? {
    val field = findField(fieldType, fieldName) ?: return null
    return field.value
}

fun BomNodeDto.findField(fieldType: FieldType, fieldName: String): FieldDto? {
    val fields = when (fieldType) {
        FieldType.Inputs -> this.inputs
        FieldType.Overrides -> this.overrides
        FieldType.Outputs -> this.outputs
    }
    return fields?.firstOrNull { it.name == fieldName }
}

fun BomNodeDto.assertFieldDoesNotExists(fieldType: FieldType, fieldName: String) {
    assert(findField(fieldType, fieldName) == null) { "Field exists: $fieldName, but should not exist" }
}

fun BomNodeDto.assertNumericFieldEqualsRecursive(fieldType: FieldType, entityName: String, fieldName: String, expected: Double, epsilon: Double = 1e-5, message: String? = null) {
    val node = findEntityRecursive(entityName) ?: throw Exception("Entity not found: $entityName")
    node.assertNumericFieldEquals(fieldType, fieldName, expected, epsilon, message)
}

fun BomNodeDto.assertNumericFieldEquals(fieldType: FieldType, fieldName: String, expected: Double, epsilon: Double = 1e-5, message: String? = null) {
    val field = findAndAssertField(fieldType, fieldName)

    if (field.value is Double) {
        val diff = abs(expected - field.value)
        if (diff > epsilon) throw Exception(createMessage(this.name, fieldName, message, expected, field.value))
    } else {
        throw Exception("Field has wrong type: $fieldName in $fieldType of entity:${this.name}: Double expected")
    }
}

fun BomNodeDto.assertFieldEqualsRecursive(fieldType: FieldType, entityName: String, fieldName: String, expected: Any?, messageOverride: String? = null) {
    val node = findEntityRecursive(entityName) ?: throw Exception("Entity not found: $entityName")
    node.assertFieldEquals(fieldType, fieldName, expected, messageOverride)
}

fun BomNodeDto.assertFieldEquals(fieldType: FieldType, fieldName: String, expected: Any?, messageOverride: String? = null) {
    val field = findAndAssertField(fieldType, fieldName)
    val actual = field.value

    // check null cases
    if (actual == null && expected == null) return
    if (actual == null || expected == null) throw Exception(createMessage(this.name, fieldName, messageOverride, expected, field.value))

    if (actual::class != expected::class) throw Exception(createMessage(this.name, fieldName, "Field \"$fieldName\" in entity:${this.name} has wrong type", expected::class, actual::class))

    if (field.value != expected) throw Exception(createMessage(this.name, fieldName, messageOverride, expected, field.value))
}

private fun BomNodeDto.findAndAssertField(fieldType: FieldType, fieldName: String): FieldDto {
    return findField(fieldType, fieldName) ?: throw Exception("Field not found: $fieldName in $fieldType of entity:${this.name}")
}

// endregion

private fun createMessage(entityName: String, fieldName: String, message: String?, expected: Any?, actual: Any?): String {
    return "${message ?: "Entity name: $entityName, field: $fieldName"}: $expected was expected, but $actual was given!"
}
