package com.tset.clientsdk.schema.dtos

import com.fasterxml.jackson.annotation.JsonInclude
import com.nu.bom.core.publicapi.dtos.MasterdataKey

@JsonInclude(JsonInclude.Include.NON_NULL)
data class BomNodeDto(
    // example: 6245605907cda84d624728fd
    // The id of the entity, must be used as parentId for adding new objects.
    val id: String,
    // example: MANUFACTURING
    // The type of the created object, can also be queried via the /entities endpoint.
    val type: String,
    // example: ManualManufacturing
    // The name of the created object, can be an arbitrary chosen.
    val name: String,
    // example: ManualManufacturing
    // The specific type of the created object.
    val manufacturingType: String,
    // example: My Part
    // The name of the part that will be created on creation. Only needed for manufacturing creation.
    // val part: String?,
    val part: PartDto?,
    // inputs	[...]
    val inputs: List<FieldDto>?,
    // overrides	[...]
    val overrides: List<FieldDto>?,
    // outputs	[...]
    val outputs: List<FieldDto>?,
    // children	[...]
    val children: List<BomNodeDto>?,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
open class CreateBomNodeDto(
    // example: MANUFACTURING
    // The type of the created object, can also be queried via the /entities endpoint.
    val type: String,
    // example: ManualManufacturing
    // The name of the created object, can be an arbitrary chosen.
    val name: String,
    // example: ManualManufacturing
    // The specific type of the created object.
    val manufacturingType: String,
    // example: My Part
    // The name of the part that will be created on creation. Only needed for manufacturing creation.
//    val part: String?,
    val part: PartDto,
    // inputs	[...]
    val inputs: List<FieldDto>?,
    // overrides	[...]
    val overrides: List<FieldDto>?,
    // children	[...]
    val children: List<CreateBomNodeDto>?,
    val isolated: Boolean = false,
    // example:
    // MasterdataKey(
    //    type = "MACHINE",
    //    key = "KD-1192",
    // )
    // Master data key can be set if we want to use master data for entity creation (for example, material or machine)
    val masterdataKey: MasterdataKey?,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
open class PartDto(val designation: String, val number: String) {
    override fun toString(): String {
        return "PartDto(designation='$designation', number='$number')"
    }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
data class CreateBomNodeResponseDto(
    val id: String,
    val branchId: String,
)
