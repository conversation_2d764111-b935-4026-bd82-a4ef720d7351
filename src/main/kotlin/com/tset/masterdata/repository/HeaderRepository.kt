package com.tset.masterdata.repository

import com.tset.masterdata.model.HeaderNaturalKey
import com.tset.masterdata.model.headerdetails.Header
import com.tset.masterdata.model.headerdetails.HeaderWithoutSchema
import com.tset.masterdata.repository.base.BaseRepository
import kotlinx.coroutines.flow.Flow
import org.springframework.data.r2dbc.repository.Query
import org.springframework.stereotype.Repository
import java.util.*

private const val COLS_HEADER = "h.id,h.key,h.name,h.header_type_id,h.active,h.detail_value_schema,h.classification_field_values,h.classification_field_values"

@Repository
interface HeaderRepository : BaseRepository<Header> {

    suspend fun getByHeaderTypeIdAndKey(headerTypeId: UUID, key: HeaderNatural<PERSON>ey): Header?

    suspend fun deleteByHeaderTypeIdAndKey(headerTypeId: UUID, key: HeaderNaturalKey): Int

    fun findByHeaderTypeIdAndKeyIn(headerTypeId: UUID, keys: Collection<HeaderNaturalKey>): Flow<Header>

    fun findWithoutSchemaByHeaderTypeIdAndKeyIn(headerTypeId: UUID, keys: Collection<HeaderNaturalKey>): Flow<HeaderWithoutSchema>

    @Query(
        """
        select $COLS_HEADER
        from header h
        join header_type ht on h.header_type_id = ht.id
        where ht.key = :headerTypeKey
        order by upper(h.name), h.id
    """,
    )
    fun findByHeaderTypeKey(headerTypeKey: HeaderNaturalKey): Flow<Header>

    @Query(
        """
        select $COLS_HEADER
        from header h
        join header_type ht on h.header_type_id = ht.id
        where ht.key = :headerTypeKey
        order by h.key, h.id
    """,
    )
    fun findByHeaderTypeKeySortByKey(headerTypeKey: HeaderNaturalKey): Flow<Header>

    @Query(
        """
        select $COLS_HEADER
        from header h
        join header_type ht on h.header_type_id = ht.id
        where ht.key = :headerTypeKey AND h.name ilike :searchStr
        order by upper(h.name), h.id
    """,
    )
    fun findByHeaderTypeKeyAndNameLike(headerTypeKey: HeaderNaturalKey, searchStr: String): Flow<Header>

    @Query(
        """
        select $COLS_HEADER
        from header h
        join header_type ht on h.header_type_id = ht.id
        where ht.key = :headerTypeKey AND cast (h.key as text) ilike :searchStr
        order by upper(h.name), h.id
    """,
    )
    fun findByHeaderTypeKeyAndKeyLike(headerTypeKey: HeaderNaturalKey, searchStr: String): Flow<Header>

    @Query(
        """
        select $COLS_HEADER
        from header h
        where h.header_type_id = :headerTypeId
        order by upper(h.name), h.id
    """,
    )
    fun findByHeaderTypeId(headerTypeId: UUID): Flow<Header>

    @Query(
        """
        select true
        from header h
        where h.header_type_id = :headerTypeId
        limit 1
    """,
    )
    suspend fun existsHeadersOfType(headerTypeId: UUID): Boolean?

    @Query(
        """
        select $COLS_HEADER
        from header h
        where h.header_type_id = :headerTypeId
            and h.name ilike :searchStr
        order by upper(h.name), h.id
    """,
    )
    fun findByHeaderTypeIdAndNameLike(headerTypeId: UUID, searchStr: String): Flow<Header>
}
