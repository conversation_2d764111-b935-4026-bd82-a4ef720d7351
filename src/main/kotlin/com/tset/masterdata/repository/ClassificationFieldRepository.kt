package com.tset.masterdata.repository

import com.tset.masterdata.model.basicData.ClassificationField
import com.tset.masterdata.model.basicData.ClassificationFieldWithType
import com.tset.masterdata.model.headerdetails.HeaderClassificationAndField
import com.tset.masterdata.repository.base.BaseRepository
import kotlinx.coroutines.flow.Flow
import org.springframework.data.r2dbc.repository.Query
import org.springframework.stereotype.Repository
import java.util.*

private const val COLS_CLASSIFICATION_FIELD = "c.id,c.classification_id, c.field_definition_id,c.index,c.column_visible_by_default,c.filter_visible_by_default"

@Repository
interface ClassificationFieldRepository : BaseRepository<ClassificationField> {

    @Query(
        """
            select h.id as header_id, hcl.classification_id, field_definition_id
            from header h
                     join header_type ht on h.header_type_id = ht.id
                     join header_classification hcl on hcl.header_id = h.id,
            lateral jsonb_object_keys(h.classification_field_values) AS field_definition_id
            where exists (
                    select 1 
                    from header_type_classification_schema htsi
                    join header_classification hcli on htsi.id = hcli.classification_schema_id
                    where hcli.header_id = h.id and htsi.classification_type_id = :classificationTypeId)
                and cast(field_definition_id as uuid) in (:fieldIds)
            limit :limit
    """,
    )
    fun findForClassificationFieldDelete(classificationTypeId: UUID, fieldIds: Collection<UUID>, limit: Int): Flow<HeaderClassificationAndField>

    @Query(
        """
            $QUERY_CLASSIFICATION_FIELDS_WITH_TYPE
            where c.classification_id in (:ids)
            order by c.index, fd.key
        """,
    )
    fun findClassificationFieldsWithType(classificationIds: Collection<UUID>): Flow<ClassificationFieldWithType>

    @Query(
        """
            $QUERY_CLASSIFICATION_FIELDS_WITH_TYPE
            where c.classification_id in (:ids) and c.field_definition_id in (:fieldDefinitionIds)
            order by c.index, fd.key
        """,
    )
    fun findClassificationFieldsWithType(classificationIds: Collection<UUID>, fieldDefinitionIds: Collection<UUID>): Flow<ClassificationFieldWithType>

    @Query(
        """
            select $COLS_CLASSIFICATION_FIELD
            from classification_field c
            where c.classification_id in (:ids)
            order by c.index asc
        """,
    )
    fun findClassificationFields(classificationIds: Collection<UUID>): Flow<ClassificationField>

    suspend fun existsByFieldDefinitionId(fieldDefinitionId: UUID): Boolean
}

private const val QUERY_CLASSIFICATION_FIELDS_WITH_TYPE =
    """
            select $COLS_CLASSIFICATION_FIELD,
                fd.id as _field_id, fd.key as _field_key, fd.type as _field_type,
                    fd.lov_type as _field_lov_type, fd.classification_type as _field_classification_type,
                    fd.numerator_dimension as _field_numerator_dimension,
                    fd.system_managed as _field_system_managed, fd.lov_default_value as _field_lov_default_value,
                    fd.date_default_value as _field_date_default_value, fd.classification_default_value as _field_classification_default_value, fd.numeric_default_value as _field_numeric_default_value,
                    fd.field_schema as _field_field_schema,
                    class.key as _classification_type_key, class.name as _classification_type_name, class.system_managed as _classification_type_system_managed,
                    lov.key as lov_key, lov.name as lov_name, lov.system_managed as lov_system_managed,
                    dim.key as dim_key, dim.name as dim_name, dim.base_unit_id as dim_base_unit_id,
                    lov_entry_default.key as lov_default_value_key,
                    class_default.key as classification_default_value_key
            from classification_field c
            join field_definition fd on fd.id = c.field_definition_id
            left join classification_type class on fd.classification_type = class.id
            left join lov_type lov on fd.lov_type = lov.id
            left join dimension dim on fd.numerator_dimension = dim.id
            left join lov_entry lov_entry_default on fd.lov_default_value = lov_entry_default.id
            left join classification class_default on fd.classification_default_value = class_default.id
        """
