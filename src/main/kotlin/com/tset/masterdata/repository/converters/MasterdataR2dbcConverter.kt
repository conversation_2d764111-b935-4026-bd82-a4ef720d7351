package com.tset.masterdata.repository.converters

import com.fasterxml.jackson.databind.ObjectMapper
import io.r2dbc.spi.Row
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.convert.converter.Converter
import org.springframework.data.convert.ReadingConverter
import org.springframework.data.convert.WritingConverter
import org.springframework.data.r2dbc.mapping.OutboundRow

abstract class MasterdataR2dbcConverter {
    @Autowired
    lateinit var objectMapper: ObjectMapper
}

abstract class MasterdataR2dbcGenericConverter<S, T> :
    MasterdataR2dbcConverter(),
    Converter<S, T>

@ReadingConverter
abstract class MasterdataR2dbcReadConverter<T> : MasterdataR2dbcGenericConverter<Row, T>()

@WritingConverter
abstract class MasterdataR2dbcWriteConverter<T> : MasterdataR2dbcGenericConverter<T, OutboundRow>()
