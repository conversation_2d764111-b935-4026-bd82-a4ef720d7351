package com.tset.masterdata.controller.examples.providers

import com.nu.masterdata.dto.v1.basicdata.ClassificationFieldRequestDto
import com.nu.masterdata.dto.v1.detail.LovValueDto
import com.nu.masterdata.dto.v1.detail.NumericValueDto
import com.nu.masterdata.dto.v1.detail.TextValueDto
import com.nu.masterdata.dto.v1.detail.UnitMeasurementDto
import com.nu.masterdata.dto.v1.header.HeaderDto
import com.nu.masterdata.dto.v1.header.HeaderKeyType
import com.nu.masterdata.dto.v1.header.HeaderTypeClassificationSchemaDto
import com.nu.masterdata.dto.v1.header.HeaderTypeDto
import com.nu.masterdata.dto.v1.schema.*
import com.tset.masterdata.controller.examples.ExamplesBuilder
import com.tset.masterdata.controller.examples.OpenApiExampleProvider
import com.tset.masterdata.service.api.ClassificationFieldsRequest
import org.springframework.stereotype.Component
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto as K

@Component
class ClassificationExamplePackage : OpenApiExampleProvider {
    companion object {
        private val MATERIAL_TYPE = K("material")
    }

    @Suppress("UNUSED", "UNUSED_VARIABLE")
    override fun provideExamples(builder: ExamplesBuilder) = builder.run {
        val classFiels = object {
            val rawFields: ClassificationFieldsRequest = mapOf(
                K("material-group") to ClassificationFieldRequestDto(0, true, filterVisibleByDefault = false),
            ).addExample { "Raw material fields" }

            val barFields: ClassificationFieldsRequest = mapOf(
                K("bar-length") to ClassificationFieldRequestDto(10, true, filterVisibleByDefault = true),
                K("surface-finish") to ClassificationFieldRequestDto(20, false, filterVisibleByDefault = false),
            ).addExample("Bar also inherits the fields from raw material.") { "Bar fields" }

            val elco: ClassificationFieldsRequest = mapOf(
                K("manufacturer") to ClassificationFieldRequestDto(0, false, filterVisibleByDefault = true),
                K("mpn") to ClassificationFieldRequestDto(10, true, filterVisibleByDefault = true),
                K("mounting-type") to ClassificationFieldRequestDto(30, true, filterVisibleByDefault = true),
            ).addExample { "Electronic component fields" }
        }

        val headerType = HeaderTypeDto(
            key = K("material"),
            name = "Materials - Header Classifications",
            headerKeyType = HeaderKeyType.SIMPLE,
            active = true,
            index = 0,
            effectivities = mapOf(),
            detailValueSchema = NumericDetailValueSchemaDto(
                NumericFieldSchemaDto(
                    UnitOfMeasurementTypeDto(
                        numerator = CurrencyTypeDto(K("EUR")),
                        denominator = UnitTypeDto(K("pcs")),
                    ),
                ),
            ),
            classificationTypes = listOf(
                HeaderTypeClassificationSchemaDto(
                    classificationTypeKey = MATERIAL_TYPE,
                    index = 2,
                    singleSelection = false,
                ),
            ),
        ).addExample { name }

        val headers = object {
            val elco = HeaderDto(
                K("TLE5309D"),
                "IC TLE5309D PG-TDSO-16-2",
                headerType.key,
                true,
                null,
                mapOf(MATERIAL_TYPE to listOf(K("material-elco"))),
                mapOf(
                    K("manufacturer") to TextValueDto("INFINEON TECHNOLOGIES"),
                    K("mpn") to TextValueDto("TLE5309D E1211"),
                    K("mounting-type") to LovValueDto(K("SMD")),
                ),
            ).addExample { "Electronic component" }

            val bar = HeaderDto(
                K("1.6657"),
                "15NiCr13",
                headerType.key,
                true,
                null,
                mapOf(MATERIAL_TYPE to listOf(K("material-bar"))),
                mapOf(
                    K("material-group") to LovValueDto(K("P7")),
                    K("bar-length") to NumericValueDto(3.2, numerator = UnitMeasurementDto(K("m"))),
                    K("surface-finish") to NumericValueDto(25.0, numerator = UnitMeasurementDto(K("rz"))),
                    K("pre-heat-treatment") to LovValueDto(K("annealed")),
                ),
            ).addExample { "Bar material" }
        }
    }
}
