package com.tset.masterdata.controller.basicData

import com.nu.masterdata.dto.v1.basicdata.LovTypeDto
import com.tset.masterdata.configuration.security.PermissionNames
import com.tset.masterdata.controller.annotation.*
import com.tset.masterdata.controller.validateKeysOrThrow
import com.tset.masterdata.model.SimpleKey
import com.tset.masterdata.service.api.basicData.LovEntryApiService
import com.tset.masterdata.service.api.basicData.LovTypeApiService
import io.swagger.v3.oas.annotations.Operation
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/md/v1/lovtypes")
class LovTypeController(
    private val lovTypeApiService: LovTypeApiService,
    private val lovEntryApiService: LovEntryApiService,
) {
    @GetMapping(produces = ["application/json"], path = [ "/{key}/entries" ])
    @PreAuthorize("hasAuthority('${PermissionNames.READ_MASTERDATA}')")
    @Operation(summary = "Get all entries of this lov type")
    fun getLovEntriesOfLovType(
        @SimpleKeyPath
        key: SimpleKey,
        @RequestParam(value = "searchStr", required = false) searchStr: String?,
        @RequestParam(value = "in", required = false) inCondition: String?,
    ) = lovEntryApiService.getLovEntriesOfLovType(key, searchStr, inCondition)

    @CreateResponses
    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping("/{key}", consumes = ["application/json"], produces = ["application/json"])
    @PreAuthorize("hasAuthority('${PermissionNames.CREATE_MASTERDATA}')")
    @Operation(summary = "Create a lov type")
    suspend fun createLovType(
        @SimpleKeyPath
        key: SimpleKey,
        @Valid
        @RequestBody
        dto: LovTypeDto,
    ): LovTypeDto {
        validateKeysOrThrow(key, dto)
        return lovTypeApiService.create(dto)
    }

    @GetResponses
    @GetMapping("/{key}", produces = ["application/json"])
    @PreAuthorize("hasAuthority('${PermissionNames.READ_MASTERDATA}')")
    @Operation(summary = "Get a lov type")
    suspend fun getLovType(
        @SimpleKeyPath
        key: SimpleKey,
    ) = lovTypeApiService.getByKey(key)

    @UpdateResponses
    @PutMapping("/{key}", consumes = ["application/json"], produces = ["application/json"])
    @PreAuthorize("hasAuthority('${PermissionNames.UPDATE_MASTERDATA}')")
    @Operation(summary = "Update or create a lov type")
    suspend fun updateLovType(
        @SimpleKeyPath
        key: SimpleKey,
        @Valid
        @RequestBody
        dto: LovTypeDto,
    ) = lovTypeApiService.update(key, dto)

    @DeleteResponses
    @DeleteMapping("/{key}")
    @PreAuthorize("hasAuthority('${PermissionNames.DELETE_MASTERDATA}')")
    @Operation(summary = "Delete a lov type")
    suspend fun deleteLovType(
        @SimpleKeyPath
        key: SimpleKey,
    ) = lovTypeApiService.deleteByKey(key)

    @GetMapping(produces = ["application/json"])
    @PreAuthorize("hasAuthority('${PermissionNames.READ_MASTERDATA}')")
    @Operation(summary = "Find lov types")
    fun findAllLovTypes(
        @RequestParam(required = false)
        name: String?,
    ) = lovTypeApiService.findByNameContains(name)
}
