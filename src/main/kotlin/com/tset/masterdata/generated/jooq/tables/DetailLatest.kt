/*
 * This file is generated by jOOQ.
 */
package com.tset.masterdata.generated.jooq.tables


import com.tset.masterdata.generated.jooq.DefaultSchema
import com.tset.masterdata.generated.jooq.indexes.DETAIL_LATEST_KEY_INDEX
import com.tset.masterdata.generated.jooq.keys.DETAIL_LATEST_PK
import com.tset.masterdata.generated.jooq.tables.records.DetailLatestRecord

import java.util.UUID

import kotlin.collections.Collection
import kotlin.collections.List

import org.jooq.Condition
import org.jooq.Field
import org.jooq.ForeignKey
import org.jooq.Index
import org.jooq.InverseForeignKey
import org.jooq.JSONB
import org.jooq.Name
import org.jooq.PlainSQL
import org.jooq.QueryPart
import org.jooq.Record
import org.jooq.SQL
import org.jooq.Schema
import org.jooq.Select
import org.jooq.Stringly
import org.jooq.Table
import org.jooq.TableField
import org.jooq.TableOptions
import org.jooq.UniqueKey
import org.jooq.impl.DSL
import org.jooq.impl.SQLDataType
import org.jooq.impl.TableImpl


/**
 * This class is generated by jOOQ.
 */
@Suppress("UNCHECKED_CAST")
open class DetailLatest(
    alias: Name,
    path: Table<out Record>?,
    childPath: ForeignKey<out Record, DetailLatestRecord>?,
    parentPath: InverseForeignKey<out Record, DetailLatestRecord>?,
    aliased: Table<DetailLatestRecord>?,
    parameters: Array<Field<*>?>?,
    where: Condition?
): TableImpl<DetailLatestRecord>(
    alias,
    DefaultSchema.DEFAULT_SCHEMA,
    path,
    childPath,
    parentPath,
    aliased,
    parameters,
    DSL.comment(""),
    TableOptions.table(),
    where,
) {
    companion object {

        /**
         * The reference instance of <code>detail_latest</code>
         */
        val DETAIL_LATEST: DetailLatest = DetailLatest()
    }

    /**
     * The class holding records for this type
     */
    override fun getRecordType(): Class<DetailLatestRecord> = DetailLatestRecord::class.java

    /**
     * The column <code>detail_latest.detail_id</code>.
     */
    val DETAIL_ID: TableField<DetailLatestRecord, UUID?> = createField(DSL.name("detail_id"), SQLDataType.UUID.nullable(false), this, "")

    /**
     * The column <code>detail_latest.key</code>.
     */
    val KEY: TableField<DetailLatestRecord, JSONB?> = createField(DSL.name("key"), SQLDataType.JSONB.nullable(false), this, "")

    private constructor(alias: Name, aliased: Table<DetailLatestRecord>?): this(alias, null, null, null, aliased, null, null)
    private constructor(alias: Name, aliased: Table<DetailLatestRecord>?, parameters: Array<Field<*>?>?): this(alias, null, null, null, aliased, parameters, null)
    private constructor(alias: Name, aliased: Table<DetailLatestRecord>?, where: Condition?): this(alias, null, null, null, aliased, null, where)

    /**
     * Create an aliased <code>detail_latest</code> table reference
     */
    constructor(alias: String): this(DSL.name(alias))

    /**
     * Create an aliased <code>detail_latest</code> table reference
     */
    constructor(alias: Name): this(alias, null)

    /**
     * Create a <code>detail_latest</code> table reference
     */
    constructor(): this(DSL.name("detail_latest"), null)
    override fun getSchema(): Schema? = if (aliased()) null else DefaultSchema.DEFAULT_SCHEMA
    override fun getIndexes(): List<Index> = listOf(DETAIL_LATEST_KEY_INDEX)
    override fun getPrimaryKey(): UniqueKey<DetailLatestRecord> = DETAIL_LATEST_PK
    override fun `as`(alias: String): DetailLatest = DetailLatest(DSL.name(alias), this)
    override fun `as`(alias: Name): DetailLatest = DetailLatest(alias, this)
    override fun `as`(alias: Table<*>): DetailLatest = DetailLatest(alias.qualifiedName, this)

    /**
     * Rename this table
     */
    override fun rename(name: String): DetailLatest = DetailLatest(DSL.name(name), null)

    /**
     * Rename this table
     */
    override fun rename(name: Name): DetailLatest = DetailLatest(name, null)

    /**
     * Rename this table
     */
    override fun rename(name: Table<*>): DetailLatest = DetailLatest(name.qualifiedName, null)

    /**
     * Create an inline derived table from this table
     */
    override fun where(condition: Condition?): DetailLatest = DetailLatest(qualifiedName, if (aliased()) this else null, condition)

    /**
     * Create an inline derived table from this table
     */
    override fun where(conditions: Collection<Condition>): DetailLatest = where(DSL.and(conditions))

    /**
     * Create an inline derived table from this table
     */
    override fun where(vararg conditions: Condition?): DetailLatest = where(DSL.and(*conditions))

    /**
     * Create an inline derived table from this table
     */
    override fun where(condition: Field<Boolean?>?): DetailLatest = where(DSL.condition(condition))

    /**
     * Create an inline derived table from this table
     */
    @PlainSQL override fun where(condition: SQL): DetailLatest = where(DSL.condition(condition))

    /**
     * Create an inline derived table from this table
     */
    @PlainSQL override fun where(@Stringly.SQL condition: String): DetailLatest = where(DSL.condition(condition))

    /**
     * Create an inline derived table from this table
     */
    @PlainSQL override fun where(@Stringly.SQL condition: String, vararg binds: Any?): DetailLatest = where(DSL.condition(condition, *binds))

    /**
     * Create an inline derived table from this table
     */
    @PlainSQL override fun where(@Stringly.SQL condition: String, vararg parts: QueryPart): DetailLatest = where(DSL.condition(condition, *parts))

    /**
     * Create an inline derived table from this table
     */
    override fun whereExists(select: Select<*>): DetailLatest = where(DSL.exists(select))

    /**
     * Create an inline derived table from this table
     */
    override fun whereNotExists(select: Select<*>): DetailLatest = where(DSL.notExists(select))
}
