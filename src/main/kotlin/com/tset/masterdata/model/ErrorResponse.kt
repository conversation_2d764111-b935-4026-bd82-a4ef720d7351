package com.tset.masterdata.model

import io.swagger.v3.oas.annotations.media.Schema
import java.time.Instant

@Schema
data class ErrorResponse(
    val timestamp: Instant?,
    val path: String?,
    val status: Int?,
    val error: String?,
    val message: String?,
    val requestId: String?,
    val trace: String?,
    val errorCode: String?,
    val userErrorCode: String?,
    val userParameters: List<Any?>?,
    val fallbackMessage: String?,
)
