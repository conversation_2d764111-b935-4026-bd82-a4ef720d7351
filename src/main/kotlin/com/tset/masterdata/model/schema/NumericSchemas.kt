package com.tset.masterdata.model.schema

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import java.util.*

/*
 * !!! When you change something here, please check if something needs to be adjusted in extract_unit_ids_from_field_schema.sql !!!
 */

@JsonInclude(JsonInclude.Include.NON_NULL)
data class UnitOfMeasurementType(
    val numerator: NumericType,
    val denominator: NumericType,
) {
    companion object {
        fun scalarUnit(numerator: NumericType): UnitOfMeasurementType =
            UnitOfMeasurementType(
                numerator = numerator,
                denominator = WithoutUnitOfMeasurement(),
            )
    }

    @JsonIgnore
    fun unitIds(): Set<UUID> = measurementIds { it.unitIds() }

    @JsonIgnore
    fun ccyIds(): Set<UUID> = measurementIds { it.ccyIds() }

    @JsonIgnore
    fun dimensionIds(): Set<UUID> = measurementIds { it.dimensionIds() }

    @JsonIgnore
    private fun measurementIds(funKeys: (NumericType) -> List<UUID>): Set<UUID> =
        listOfNotNull(numerator, denominator).flatMap { funKeys(it) }.toSet()

    @JsonIgnore
    fun doesSchemaSatisfyDetailValueRequirements(): Boolean =
        numerator.doesSchemaSatisfyDetailValueRequirements() && denominator.doesSchemaSatisfyDetailValueRequirements()

    /**
     * validates that numerator is either Unit or DimensionType and denominator is WithoutUnitType
     */
    @JsonIgnore
    fun doesSchemaSatisfyFieldDefinitionSchemaRequirements(): Boolean =
        (numerator is CurrencyType || numerator is UnitType || numerator is DimensionType || numerator is WithoutUnitOfMeasurement) && denominator is WithoutUnitOfMeasurement
}

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "type",
)
sealed interface NumericType {
    @JsonIgnore
    fun unitIds(): List<UUID> = emptyList()

    @JsonIgnore
    fun ccyIds(): List<UUID> = emptyList()

    @JsonIgnore
    fun dimensionIds(): List<UUID> = emptyList()

    @JsonIgnore
    fun doesSchemaSatisfyDetailValueRequirements(): Boolean = true
}

/**
 * only a specific currency is allowed
 */
@JsonTypeName("currency")
data class CurrencyType(
    val currencyId: UUID,
) : NumericType {
    @JsonIgnore
    override fun ccyIds(): List<UUID> = listOf(currencyId)
}

/**
 * any currency is allowed
 * [defaultCurrencyId] is e.g. used by Frontend to pre-select a currency on detail creation
 */
@JsonTypeName("any-currency")
@JsonInclude(JsonInclude.Include.NON_NULL)
data class AnyCurrencyType(
    val defaultCurrencyId: UUID,
) : NumericType {
    @JsonIgnore
    override fun ccyIds(): List<UUID> = listOf(defaultCurrencyId)
}

/**
 * all units of [dimensionId] are allowed.
 * [defaultUnitId] is e.g. used by Frontend to pre-select a unit on detail creation
 */
@JsonTypeName("dimension")
@JsonInclude(JsonInclude.Include.NON_NULL)
data class DimensionType(
    val dimensionId: UUID,
    val defaultUnitId: UUID,
) : NumericType {
    @JsonIgnore
    override fun unitIds(): List<UUID> = listOf(defaultUnitId)

    @JsonIgnore
    override fun dimensionIds(): List<UUID> = listOf(dimensionId)
}

/**
 * only a specific unit is allowed
 */
@JsonTypeName("unit")
data class UnitType(
    val unitId: UUID,
) : NumericType {
    @JsonIgnore
    override fun unitIds(): List<UUID> = listOf(unitId)
}

/**
 * dimensionless, without unit of measurement, factor1
 */
@JsonTypeName("without-unit-of-measurement")
class WithoutUnitOfMeasurement : NumericType {
    override fun hashCode(): Int {
        return WithoutUnitOfMeasurement::class.hashCode()
    }

    override fun equals(other: Any?): Boolean {
        return other?.javaClass == this.javaClass
    }
}

/**
 * no restriction on the used unit
 */
@JsonTypeName("any-unit")
class AnyUnitType : NumericType {
    @JsonIgnore
    override fun doesSchemaSatisfyDetailValueRequirements(): Boolean = false

    override fun hashCode(): Int {
        return AnyUnitType::class.hashCode()
    }

    override fun equals(other: Any?): Boolean {
        return other?.javaClass == this.javaClass
    }
}
