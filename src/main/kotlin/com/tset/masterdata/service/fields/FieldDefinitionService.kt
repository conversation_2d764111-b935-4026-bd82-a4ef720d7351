package com.tset.masterdata.service.fields

import com.tset.masterdata.configuration.BuiltinEffectivities
import com.tset.masterdata.model.HeaderNaturalKey
import com.tset.masterdata.model.fields.FieldDefinition
import com.tset.masterdata.model.fields.FieldDefinitionWithType
import com.tset.masterdata.repository.fields.FieldDefinitionRepository
import com.tset.masterdata.service.base.SystemManagedBaseService
import com.tset.masterdata.service.exception.ReservedKeyException
import com.tset.masterdata.service.security.SystemManagedPermissionChecker
import com.tset.masterdata.service.util.toSqlContains
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.firstOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional
class FieldDefinitionService(
    private val fieldDefinitionRepo: FieldDefinitionRepository,
    private val permissionChecker: SystemManagedPermission<PERSON>he<PERSON>,
) : SystemManagedBaseService<FieldDefinition>(fieldDefinitionRepo, permissionChecker) {
    override suspend fun validate(entity: FieldDefinition) =
        if (entity.key.toDto().key.startsWith(BuiltinEffectivities.PREFIX)) {
            throw ReservedKeyException(entity.key)
        } else {
            entity
        }

    @Transactional(readOnly = true)
    suspend fun getByKeyFetchTypeInfo(key: HeaderNaturalKey): FieldDefinitionWithType? =
        findByKeyInFetchType(setOf(key)).firstOrNull()

    @Transactional(readOnly = true)
    fun findByNameContainsJoinType(name: String?) = fieldDefinitionRepo.findByNameLikeJoinType(toSqlContains(name))

    @Transactional(readOnly = true)
    fun findByKeyInFetchType(keys: Collection<HeaderNaturalKey>): Flow<FieldDefinitionWithType> =
        if (keys.isNotEmpty()) {
            fieldDefinitionRepo.findByKeyFetchType(keys)
        } else {
            emptyFlow()
        }

    @Transactional(readOnly = true)
    fun getAllSystemManagedFieldsFetchType() = fieldDefinitionRepo.findAllSystemManagedFieldsFetchType()

    @Transactional(readOnly = true)
    override fun findByNameContains(name: String?) = fieldDefinitionRepo.findByNameContainsIgnoreCase(toSqlContains(name))
}
