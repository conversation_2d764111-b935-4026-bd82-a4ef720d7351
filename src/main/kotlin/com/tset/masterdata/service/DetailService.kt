package com.tset.masterdata.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.nu.masterdata.dto.v1.detail.*
import com.tset.masterdata.generated.jooq.tables.references.DETAIL
import com.tset.masterdata.model.HeaderNaturalKey
import com.tset.masterdata.model.basicData.LovEntry
import com.tset.masterdata.model.basicData.LovEntryWithType
import com.tset.masterdata.model.basicData.NumericCurrency
import com.tset.masterdata.model.basicData.NumericUnit
import com.tset.masterdata.model.fields.LovFieldDefinition
import com.tset.masterdata.model.fields.ids.HeaderId
import com.tset.masterdata.model.fields.ids.MultiFieldEffectivityKey
import com.tset.masterdata.model.fields.ids.UnitId
import com.tset.masterdata.model.headerdetails.Detail
import com.tset.masterdata.model.headerdetails.Header
import com.tset.masterdata.model.headerdetails.detailvalues.LovDetailValue
import com.tset.masterdata.model.headerdetails.detailvalues.NumericDetailValue
import com.tset.masterdata.model.headertype.EffectivityWithField
import com.tset.masterdata.model.headertype.HeaderType
import com.tset.masterdata.model.toKey
import com.tset.masterdata.repository.DetailRepository
import com.tset.masterdata.repository.EffectivityRepository
import com.tset.masterdata.repository.HeaderRepository
import com.tset.masterdata.repository.fields.FieldDefinitionRepository
import com.tset.masterdata.service.exception.ReferencedKeyNotFoundException
import com.tset.masterdata.service.exception.meta.EntityCode
import com.tset.masterdata.service.util.*
import kotlinx.coroutines.reactive.awaitLast
import org.jooq.JSONB
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.*

@Service
@Transactional
class DetailService(
    private val detailRepo: DetailRepository,
    private val effRepo: EffectivityRepository,
    private val headerRepo: HeaderRepository,
    private val jooqService: JooqService,
    private val objectMapper: ObjectMapper,
    private val fieldDefinitionRepository: FieldDefinitionRepository,
    private val detailValueSchemaService: DetailValueSchemaService,
    private val basicDataFetcherService: BasicDataFetcherService,
) {
    companion object {
        private val logger: Logger = LoggerFactory.getLogger(DetailService::class.java)
    }

    @Transactional
    suspend fun saveAll(allDetails: List<Detail>, chunkSize: Int): List<Detail> {
        val (user, requestId) = getUserAndRequestIdFromContext()
        val now = LocalDateTime.now()
        val versTimestamp = OffsetDateTime.ofInstant(Instant.now(), ZoneOffset.UTC)
        logger.info(
            "Insert {} new detail values, chunkSize {}, versTimestamp {} ({})",
            allDetails.size,
            chunkSize,
            versTimestamp,
            versTimestamp.toInstant().toEpochMilli(),
        )
        return allDetails.chunked(chunkSize).map { chunk ->
            jooqService.run { dsl ->
                val insertStmt =
                    dsl.insertInto(
                        DETAIL,
                        DETAIL.ID,
                        DETAIL.KEY,
                        DETAIL.VALUE,
                        DETAIL.HEADER_ID,
                        DETAIL.CREATED_BY,
                        DETAIL.CREATED_AT,
                        DETAIL.CREATED_REQUEST_ID,
                        DETAIL.LAST_MODIFIED_BY,
                        DETAIL.LAST_MODIFIED_AT,
                        DETAIL.LAST_MODIFIED_REQUEST_ID,
                        DETAIL.ACTIVE,
                        DETAIL.VERSION_TIMESTAMP,
                    )

                val detailsWithId = chunk.map { d ->
                    val withId = d.copy(id = UUID.randomUUID())
                    insertStmt.values(
                        withId.id,
                        toJSONB(withId.key),
                        toJSONB(withId.value),
                        withId.headerId,
                        user,
                        now,
                        requestId,
                        user,
                        now,
                        requestId,
                        withId.active,
                        versTimestamp,
                    )
                    withId
                }
                insertStmt.awaitLast()
                detailsWithId
            }
        }.flatten()
    }

    @Transactional
    suspend fun save(detail: Detail): Detail = saveAll(listOf(detail), 1).single()

    // This query *must* include inactive details. Setting a detail inactive creates a new inactive entry,
    // and a future lookup should no longer consider this detail.
    @Transactional(readOnly = true)
    suspend fun getLatestVersion(headerIds: Set<HeaderId>): Instant? =
        headerIds
            .takeUnless { it.isEmpty() }
            ?.let { detailRepo.getLatestVersion(it) }
            ?.toInstant()

    suspend fun deactivateDetail(headerId: UUID, key: MultiFieldEffectivityKey): Boolean? =
        getLatestByHeaderIdAndKey(headerId, key)?.let { oldDetail ->
            if (oldDetail.active) {
                val new = Detail(
                    key = key,
                    headerId = headerId,
                    value = oldDetail.value,
                    active = false,
                )
                save(new)
            }
            oldDetail.active
        }

    suspend fun deleteByHeaderIdAndEffectivities(headerId: UUID, key: MultiFieldEffectivityKey): Boolean {
        val deletionCount = detailRepo.deleteByHeaderIdAndEffectivities(headerId, key)
        // identity of a detail includes header_id, key and timestamp
        // here we ignore the timestamp -> this means we delete the complete history of a detail (multiple details)
        // this is why the count could be greater than 1
        return deletionCount?.let { it > 0 } ?: false
    }

    private fun toJSONB(data: Any?): JSONB? =
        data?.let { JSONB.valueOf(objectMapper.writeValueAsString(it)) }

    @Transactional(readOnly = true)
    suspend fun getPostMappingData(headerType: HeaderType, dtos: List<DetailReference>): DynamicKeyMap {
        val headers = getHeaders(headerType, dtos)
        val effectivities = effRepo.findEffectivitiesOfHeaderTypes(listOf(headerType.id!!)).toList()
        val basicDataIdsAndKeysMaps = getBasicDataIdsAndKeysMap(headerType, headers, effectivities, dtos)

        basicDataIdsAndKeysMaps.addIds<LovEntry>(
            effectivities.map { it.field }
                .filterIsInstance<LovFieldDefinition>()
                .mapNotNull { it.lovDefaultValue },
        )

        val keyMap =
            basicDataFetcherService
                .fetchBasicData(basicDataIdsAndKeysMaps)
                .put(KeyMap.fromEntities(headers))
                .put(KeyMap(effectivities, EntityCode.EFFECTIVITY))

        return keyMap.build()
    }

    private suspend fun getHeaders(headerType: HeaderType, dtos: List<DetailReference>): List<Header> =
        dtos.mapToSet { it.headerKey.toKey() }.let { headerKeys ->
            headerRepo
                .findByHeaderTypeIdAndKeyIn(headerType.id!!, headerKeys)
                .toList()
                .takeUnless { it.isEmpty() }
                // Maybe different exception to say that *none* of the keys were found?
                ?: throw ReferencedKeyNotFoundException(EntityCode.HEADER, headerKeys.first())
        }

    @Transactional
    suspend fun deleteByHeaderIdIn(headerIds: Collection<HeaderId>) {
        detailRepo.deleteByHeaderIdIn(headerIds)
    }

    @Transactional(readOnly = true)
    suspend fun getMappingData(
        headerType: HeaderType,
        headers: List<Header>,
        details: List<Detail>,
        effectivities: List<EffectivityWithField>? = null,
        builder: DynamicKeyMap.Builder,
    ): DynamicKeyMap = if (headers.isEmpty() && details.isEmpty()) {
        DynamicKeyMap.empty()
    } else {
        val loadedEffectivities = effectivities
            ?: effRepo.findEffectivitiesOfHeaderTypes(listOf(headerType.id!!)).toList()
        val basicDataIdsAndKeysMaps = getBasicDataIdsAndKeysMap(headerType, loadedEffectivities, details)

        basicDataFetcherService
            .fetchBasicData(basicDataIdsAndKeysMaps, builder)
            .put(KeyMap.fromEntities(headers))
            .put(KeyMap(loadedEffectivities, EntityCode.EFFECTIVITY))
            .build()
    }

    private suspend fun getBasicDataIdsAndKeysMap(
        headerType: HeaderType,
        headers: List<Header>,
        effectivities: List<EffectivityWithField>,
        dtos: List<DetailReference>,
    ): IdsAndKeysMap {
        val idsAndKeysMap = IdsAndKeysMap()
        MappingCollectorUtil.collectFieldValueDtos(
            dtos.asSequence().flatMap { it.effectivities.values }.filterNotNull().asIterable(),
            idsAndKeysMap,
        )

        val effectivityUnitIds = getUnitIds(effectivities)
        idsAndKeysMap.addIds<NumericUnit>(effectivityUnitIds)

        // Get units for dtos to convert within same dimension
        idsAndKeysMap.addKeys<NumericUnit>(
            dtos
                .flatMap { it.effectivities.values }
                .filterIsInstance<NumericValueDto>()
                .mapNotNull { it.unit?.toKey() },
        )

        dtos
            .asSequence()
            .filterIsInstance<DetailDto>()
            .map { it.value }
            .forEach { value ->
                when (value) {
                    is NumericDetailValueDto -> {
                        idsAndKeysMap.addKeys<NumericUnit>(value.unitKeys().map { it.toKey() })
                        idsAndKeysMap.addKeys<NumericCurrency>(value.ccyKeys().map { it.toKey() })
                    }

                    is LovDetailValueDto -> idsAndKeysMap.addKeys<LovEntry>(listOf(value.key.toKey()))
                }
            }

        val headerAndTypeSchemas = listOfNotNull(headerType.detailValueSchema) + headers.mapNotNull { it.detailValueSchema }
        return detailValueSchemaService.collectionIdsAndKeys(
            detailValueSchemas = headerAndTypeSchemas,
            detailValueSchemaDtos = emptyList(),
            idsAndKeysMap,
            additionalFieldSchemas = effectivities.map { it.field.fieldSchema },
        )
    }

    suspend fun getBasicDataIdsAndKeysMap(
        headerType: HeaderType,
        effectivities: List<EffectivityWithField>,
        details: List<Detail>,
    ): IdsAndKeysMap {
        val idsAndKeysMap = IdsAndKeysMap()

        MappingCollectorUtil.collectFieldValues(
            details.asSequence().flatMap { it.key.effectivities.values }.filterNotNull().asIterable(),
            idsAndKeysMap,
        )
        idsAndKeysMap.addIds<NumericUnit>(getUnitIds(effectivities))
        details
            .asSequence()
            .map { it.value }
            .forEach { value ->
                when (value) {
                    is NumericDetailValue -> {
                        idsAndKeysMap.addIds<NumericUnit>(value.originalMeasure.unitIds())
                        idsAndKeysMap.addIds<NumericCurrency>(value.originalMeasure.ccyIds())
                    }
                    // for the response we need the LovEntry and also the LovType => LOV_ENTRY_WITH_TYPE
                    is LovDetailValue -> idsAndKeysMap.addIds<LovEntryWithType>(listOf(value.lovEntryId))
                }
            }

        val headerAndTypeSchemas = listOfNotNull(headerType.detailValueSchema)

        return detailValueSchemaService.collectionIdsAndKeys(
            detailValueSchemas = headerAndTypeSchemas,
            detailValueSchemaDtos = emptyList(),
            existingIdsAndKeys = idsAndKeysMap,
        )
    }

    private fun getUnitIds(
        effectivities: List<EffectivityWithField>,
    ): List<UnitId> = effectivities.mapNotNull { it.effectivity.numeratorUnitId }

    fun getAllSystemManagedFields() = fieldDefinitionRepository.findAllSystemManagedFields()

    @Transactional(readOnly = true)
    fun getModifiersOfType(headerTypeKey: HeaderNaturalKey, searchStr: String?) =
        if (searchStr == null) {
            detailRepo.findModifiersByHeaderTypeKey(headerTypeKey)
        } else {
            detailRepo.findModifiersByHeaderTypeKeyAndNameLike(headerTypeKey, toSqlContains(searchStr))
        }

    suspend fun getLatestByHeaderIdAndKey(headerId: UUID, key: MultiFieldEffectivityKey): Detail? =
        detailRepo.getLatestByHeaderIdAndKey(headerId, key)

    suspend fun isActive(detail: Detail): Boolean = getLatestByHeaderIdAndKey(detail.headerId, detail.key)?.active == true

    /**
     * returns true if there exists at least one detail row for this header in the DB.
     * This also considers non-active detail values (active = true) and "historic entries" (valid validUntilTimestamp != MAX)
     */
    @Transactional(readOnly = true)
    suspend fun existsDetailsOfHeader(headerId: UUID): Boolean =
        detailRepo.existsDetailsOfHeader(headerId) ?: false
}
