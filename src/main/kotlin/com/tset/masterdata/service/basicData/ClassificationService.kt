package com.tset.masterdata.service.basicData

import com.tset.masterdata.model.HeaderNaturalKey
import com.tset.masterdata.model.SimpleKey
import com.tset.masterdata.model.basicData.Classification
import com.tset.masterdata.model.basicData.ClassificationWithParent
import com.tset.masterdata.model.basicData.ClassificationWithParentAndType
import com.tset.masterdata.model.basicData.ClassificationWithType
import com.tset.masterdata.repository.basicData.ClassificationParentInfo
import com.tset.masterdata.repository.basicData.ClassificationRepository
import com.tset.masterdata.service.base.SystemManagedBaseService
import com.tset.masterdata.service.exception.ClassificationCycleException
import com.tset.masterdata.service.exception.ClassificationMultipleRootsException
import com.tset.masterdata.service.exception.ClassificationReferencesSelfException
import com.tset.masterdata.service.exception.ClassificationTypeMismatchException
import com.tset.masterdata.service.security.ClassificationPermissionChecker
import com.tset.masterdata.service.util.toSqlContains
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.map
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Isolation
import org.springframework.transaction.annotation.Transactional
import java.util.*

@Service
@Transactional
class ClassificationService(
    private val classificationRepository: ClassificationRepository,
    permissionChecker: ClassificationPermissionChecker,
) : SystemManagedBaseService<Classification>(classificationRepository, permissionChecker) {
    override suspend fun validate(entity: Classification): Classification {
        if (entity.parent == null && entity.id == null && classificationRepository.existsByClassificationType(entity.classificationType)) {
            // If there exists at least one node for this type, we cannot add another node with null as parent,
            // because the data consistency checks ensure that one must insert the root first
            // Also we assume that we actually check this condition very rare, and it just exists to be able
            // to generate a nice error. (performance could be optimized by generated the error based on unique
            // constraint violation from DB).
            // the DB constraints will catch this violation anyway as well
            throw ClassificationMultipleRootsException()
        }
        return entity
    }

    override suspend fun validateUpdate(new: Classification, fetchExisting: suspend () -> Classification): Classification {
        if (new.id == new.parent) {
            throw ClassificationReferencesSelfException(new.key)
        }
        val existing = fetchExisting.invoke()
        if (new.parent != existing.parent) {
            new.parent?.let {
                val newParent = classificationRepository.findById(it)!!
                if (newParent.classificationType != existing.classificationType) {
                    throw ClassificationTypeMismatchException(new.key)
                }
            }
        }
        return new
    }

    @Transactional(isolation = Isolation.SERIALIZABLE)
    override suspend fun save(new: Classification, existing: Classification?): Classification {
        val saved = super.save(new, existing)
        if (classificationRepository.doesCycleExist(saved.classificationType)) {
            throw ClassificationCycleException()
        }
        return saved
    }

    @Transactional(readOnly = true)
    suspend fun findByKeyJoinFetchTypeAndParent(classificationKeys: Collection<SimpleKey>): List<ClassificationWithParentAndType> =
        classificationRepository.findByKeyJoinFetchTypeAndParent(classificationKeys)

    private fun searchClassificationsOfType(
        typeKey: HeaderNaturalKey,
        searchStr: String,
        includeDescendants: Boolean,
    ): Flow<ClassificationWithParent> = if (includeDescendants) {
        classificationRepository.findByTypeKeyAndNameLikeJoinFetchParent(
            typeKey,
            toSqlContains(searchStr),
        )
    } else {
        classificationRepository.findRootsByTypeKeyAndNameLike(
            typeKey,
            toSqlContains(searchStr),
        ).map { ClassificationWithParent(it, null) }
    }

    private fun getClassificationsOfType(typeKey: HeaderNaturalKey, includeDescendants: Boolean): Flow<ClassificationWithParent> =
        if (includeDescendants) {
            classificationRepository.findByTypeKeyJoinFetchParent(typeKey)
        } else {
            classificationRepository.findRootsByTypeKey(typeKey)
                .map { ClassificationWithParent(it, null) }
        }

    @Transactional(readOnly = true)
    fun getClassificationsOfType(
        typeKey: HeaderNaturalKey,
        searchStr: String?,
        includeDescendants: Boolean,
    ): Flow<ClassificationWithParent> = if (searchStr != null) {
        searchClassificationsOfType(typeKey, searchStr, includeDescendants)
    } else {
        getClassificationsOfType(typeKey, includeDescendants)
    }

    @Transactional(readOnly = true)
    fun findByClassificationType(classificationTypeId: UUID): Flow<Classification> =
        classificationRepository.findByClassificationType(classificationTypeId)

    @Transactional(readOnly = true)
    fun getChildClassificationsOf(
        parentClassificationKey: HeaderNaturalKey,
        searchStr: String?,
    ): Flow<ClassificationWithType> = if (searchStr == null) {
        classificationRepository.findByParentKeyJoinFetchType(parentClassificationKey)
    } else {
        classificationRepository.findByParentKeyAndNameLikeJoinFetchType(
            parentClassificationKey,
            toSqlContains(searchStr),
        )
    }

    @Transactional(readOnly = true)
    fun getParentClassificationsOf(
        childClassificationKey: HeaderNaturalKey,
    ): Flow<ClassificationWithParentAndType> =
        classificationRepository.findAncestorsByChildKeys(setOf(childClassificationKey))

    @Transactional(readOnly = true)
    fun findDescendants(searchRoots: Set<HeaderNaturalKey>): Flow<Classification> =
        searchRoots
            .takeUnless { it.isEmpty() }
            ?.let(classificationRepository::findDescendants)
            ?: emptyFlow()

    @Transactional(readOnly = true)
    fun findByNameContainsJoinType(name: String?) = classificationRepository.findByNameLikeJoinType(toSqlContains(name))

    @Transactional(readOnly = true)
    fun findAncestorInfosById(searchLeaves: Set<UUID>): Flow<ClassificationParentInfo> {
        return if (searchLeaves.isEmpty()) {
            emptyFlow()
        } else {
            classificationRepository.findAncestors(searchLeaves)
        }
    }
}
