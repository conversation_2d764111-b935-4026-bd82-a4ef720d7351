package com.tset.masterdata.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.nu.masterdata.dto.v1.MeasureCarrier
import com.nu.masterdata.dto.v1.detail.table.*
import com.nu.masterdata.dto.v1.header.HeaderKeyType
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import com.tset.masterdata.configuration.BuiltinEffectivities
import com.tset.masterdata.dto.v1.internal.CountMode
import com.tset.masterdata.generated.jooq.tables.references.*
import com.tset.masterdata.model.MaterialKey
import com.tset.masterdata.model.SimpleKey
import com.tset.masterdata.model.base.AuditInfo
import com.tset.masterdata.model.basicData.Classification
import com.tset.masterdata.model.basicData.LovEntry
import com.tset.masterdata.model.fields.*
import com.tset.masterdata.model.fields.ids.MultiFieldEffectivityKey
import com.tset.masterdata.model.headerdetails.ClassificationFieldValues
import com.tset.masterdata.model.headerdetails.Detail
import com.tset.masterdata.model.headerdetails.Header
import com.tset.masterdata.model.headertype.Effectivity
import com.tset.masterdata.model.headertype.EffectivityWithField
import com.tset.masterdata.model.headertype.HeaderType
import com.tset.masterdata.model.toKey
import com.tset.masterdata.service.basicData.*
import com.tset.masterdata.service.exception.InvalidDataException
import com.tset.masterdata.service.exception.ReferencedKeyNotFoundException
import com.tset.masterdata.service.exception.meta.EntityCode
import com.tset.masterdata.service.util.*
import kotlinx.coroutines.reactive.asFlow
import kotlinx.coroutines.reactive.awaitSingle
import org.jooq.*
import org.jooq.impl.DSL
import org.jooq.impl.DSL.*
import org.jooq.impl.SQLDataType
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import reactor.core.publisher.Flux
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.*

private fun getStringEffectivityValue(
    effectivityId: UUID,
    select: SelectOnConditionStep<Record17<UUID?, JSONB?, JSONB?, UUID?, JSONB?, String?, JSONB?, JSONB?, String?, LocalDateTime?, String?, LocalDateTime?, String?, Boolean?, Boolean?, String?, Int>>,
): Field<String?> =
    jsonbGetAttributeAsText(
        jsonbGetAttribute(
            jsonbGetAttribute(select.field(DETAIL.KEY), DETAIL_KEY_EFFECTIVITIES),
            effectivityId.toString(),
        ),
        DETAIL_KEY_EFFECTIVITIES_VALUE,
    )

private fun <T> getEffectivityValue(
    effectivityId: UUID,
    type: DataType<T>,
    select: SelectOnConditionStep<Record17<UUID?, JSONB?, JSONB?, UUID?, JSONB?, String?, JSONB?, JSONB?, String?, LocalDateTime?, String?, LocalDateTime?, String?, Boolean?, Boolean?, String?, Int>>,
): Field<T?> =
    cast(getStringEffectivityValue(effectivityId, select), type)

private class QueryMappingData(
    val headerType: HeaderType,
    val effectivity: Map<SimpleKey, EffectivityWithField>,
    val measures: DynamicKeyMap,
)

data class DetailQueryResultRow(
    val header: Header,
    val detail: Detail?,
    val auditInfo: AuditInfo,
)

private const val CTE_HEADER_ID_FIELD = "cte_headerId"
private const val CTE_HEADER = "cte_header"
private const val DETAIL_KEY_EFFECTIVITIES = "effectivities"
private const val DETAIL_KEY_EFFECTIVITIES_VALUE = "value"
private const val HEADER_SUB_SELECT_HEADER_ID = "headerId"
private const val HEADER_SUB_SELECT_HEADER_KEY = "headerKey"
private const val HEADER_SUB_SELECT_HEADER_ACTIVE = "headerActive"
private const val HEADER_SUB_SELECT_HEADER_NOTE = "headerNote"
private const val HEADER_SUB_SELECT_HEADER_COUNT = "count"

private const val MAX_COUNT_FOR_ORDER = 100000

@Service
class DetailQueryService(
    private val jooqService: JooqService,
    private val objectMapper: ObjectMapper,
    private val effectivityService: EffectivityService,
    private val dimensionService: DimensionService,
    private val unitService: UnitService,
    private val ccyService: CurrencyService,
    private val headerQueryService: HeaderQueryService,
    private val lovEntryService: LovEntryService,
    private val classificationService: ClassificationService,
    @Value("\${nu-masterdata.maxCountForOrder:$MAX_COUNT_FOR_ORDER}") val maxCountForOrder: Int,
) {

    data class DetailWithCount(
        val detail: DetailQueryResultRow,
        val totalNumberElements: Int,
    )

    data class DetailQueryResult(
        val details: List<DetailQueryResultRow>,
        val effectivities: List<EffectivityWithField>,
        val totalNumberElements: Int,
        val maxCountTruncated: Boolean,
    )

    @Transactional(readOnly = true)
    suspend fun getDetails(
        headerType: HeaderType,
        query: DetailQueryDto,
        page: Int,
        size: Int,
        countMode: CountMode,
    ): DetailQueryResult = jooqService.run { ctx ->
        getDetails(ctx, headerType, query, page, size, countMode)
    }

    private suspend fun getDetails(
        dsl: DSLContext,
        headerType: HeaderType,
        query: DetailQueryDto,
        page: Int,
        size: Int,
        countMode: CountMode,
    ): DetailQueryResult {
        val headerTypeId = headerType.id!!
        val effectivities = effectivityService.getEffectivities(headerTypeId).toList()
        val measures = getMeasureMap(headerType, query)
        val fieldToEffectivity = effectivities.associateBy { it.field.key }
        val mapping = QueryMappingData(headerType, fieldToEffectivity, measures)

        val (headerCte, headerClassificationAndLovCtes) = getHeaderCte(query, headerType)
        val detailClassificationAndLovCtes = getClassificationAndLovCtes(query, mapping)
        val classificationAndLovCtes = headerClassificationAndLovCtes + detailClassificationAndLovCtes

        val detailSubSelect = getDetailSelect(query, mapping, detailClassificationAndLovCtes)

        val headerSubSelectJoinedToDetailSubSelect = getHeaderSelect(dsl, headerCte, headerClassificationAndLovCtes, detailSubSelect, countMode, query, mapping)

        val select = classificationAndLovCtes.fold(dsl.with(headerCte)) { q, c -> q.with(c) }
            .selectFrom(headerSubSelectJoinedToDetailSubSelect)

        val resultCountQuery = if (countMode == CountMode.QUERY) {
            // calculate the count over a huge header type takes too much time
            // the approach here is taking a limit of headers and detail and create the count
            headerSubSelectJoinedToDetailSubSelect.limit(maxCountForOrder)
            val selectCnt = classificationAndLovCtes.fold(dsl.with(headerCte)) { q, c -> q.with(c) }
                .selectCount().from(headerSubSelectJoinedToDetailSubSelect)
            selectCnt.awaitSingle().value1()
        } else {
            maxCountForOrder
        }

        val maxCountTruncated = if (resultCountQuery < maxCountForOrder) {
            // ordering is only allowed if there are enough filters applied to reduce the result data set
            val orderBy = getOrderBy(mapping.headerType, query, mapping.effectivity, headerSubSelectJoinedToDetailSubSelect)
            select.orderBy(orderBy)
            false
        } else {
            true
        }

        val results = Flux.from(
            select
                .offset(page * size)
                .limit(size),
        )

        val details = results.map { (id, key, value, headerId, headerKey, headerName, headerDetailValueSchema, headerClassificationFieldValues, createdBy, createdAt, lastModifiedBy, lastModifiedAt, detailNote, headerActive, active, headerNote, _) ->
            DetailWithCount(
                detail =
                DetailQueryResultRow(
                    header = Header(
                        id = headerId,
                        key = objectMapper.readValue(headerKey!!.data()),
                        name = headerName ?: "unknown",
                        headerTypeId = headerTypeId,
                        active = headerActive ?: false,
                        detailValueSchema = headerDetailValueSchema?.let { objectMapper.readValue(it.data()) },
                        classificationFieldValues = headerClassificationFieldValues?.let { objectMapper.readValue(it.data()) } ?: ClassificationFieldValues(),
                        note = headerNote,
                    ),
                    detail = value?.let { v ->
                        Detail(
                            id = id,
                            key = key?.let { objectMapper.readValue(it.data()) } ?: MultiFieldEffectivityKey(emptyMap()),
                            value = objectMapper.readValue(v.data()),
                            headerId = headerId as UUID,
                            versionTimestamp = null,
                            active = active ?: false,
                            note = detailNote,
                        )
                    },
                    auditInfo = AuditInfo(
                        createdBy = createdBy,
                        createdAt = createdAt?.toInstant(ZoneOffset.UTC),
                        lastModifiedBy = lastModifiedBy,
                        lastModifiedAt = lastModifiedAt?.toInstant(ZoneOffset.UTC),
                    ),
                ),
                totalNumberElements = resultCountQuery,
            )
        }
            .asFlow()
            .toList()
        val totalNumberElements = if (countMode == CountMode.NONE) {
            if (details.size == size) size + 1 else page * size + details.size
        } else {
            details.firstOrNull()?.totalNumberElements ?: 0
        }
        return DetailQueryResult(
            details = details.map { it.detail },
            effectivities = effectivities,
            totalNumberElements = totalNumberElements,
            maxCountTruncated = maxCountTruncated,
        )
    }

    private fun getHeaderSelect(
        dsl: DSLContext,
        cte: CommonTableExpression<out Record1<UUID?>>?,
        headerClassificationAndLovCtes: List<CommonTableExpression<Record1<JSONB>>>,
        detailSubSelect: SelectJoinStep<Record10<UUID?, JSONB?, Boolean?, UUID?, JSONB?, String?, LocalDateTime?, String?, LocalDateTime?, String?>>,
        countMode: CountMode,
        query: DetailQueryDto,
        mapping: QueryMappingData,
    ): SelectOnConditionStep<Record17<UUID?, JSONB?, JSONB?, UUID?, JSONB?, String?, JSONB?, JSONB?, String?, LocalDateTime?, String?, LocalDateTime?, String?, Boolean?, Boolean?, String?, Int>> {
        val headerSubSelect = dsl.select(
            detailSubSelect.field(DETAIL.ID),
            detailSubSelect.field(DETAIL.KEY),
            detailSubSelect.field(DETAIL.VALUE),
            HEADER.ID.`as`(HEADER_SUB_SELECT_HEADER_ID),
            HEADER.KEY.`as`(HEADER_SUB_SELECT_HEADER_KEY),
            HEADER.NAME,
            HEADER.DETAIL_VALUE_SCHEMA,
            HEADER.CLASSIFICATION_FIELD_VALUES,
            detailSubSelect.field(DETAIL.CREATED_BY),
            detailSubSelect.field(DETAIL.CREATED_AT),
            detailSubSelect.field(DETAIL.LAST_MODIFIED_BY),
            detailSubSelect.field(DETAIL.LAST_MODIFIED_AT),
            detailSubSelect.field(DETAIL.NOTE),
            HEADER.ACTIVE.`as`(HEADER_SUB_SELECT_HEADER_ACTIVE),
            detailSubSelect.field(DETAIL.ACTIVE),
            HEADER.NOTE.`as`(HEADER_SUB_SELECT_HEADER_NOTE),
            (if (countMode == CountMode.WINDOW_FUNC) count().over() else inline(0)).`as`(HEADER_SUB_SELECT_HEADER_COUNT),
        )

        val headerSubSelectJoinedToDetailSubSelect =
            if (query.filters.keys.filterNot { it.key == BuiltinEffectivities.HEADER || it.key == BuiltinEffectivities.HEADER_KEY }.isEmpty()) {
                headerSubSelect
                    .from(HEADER)
                    .leftJoin(detailSubSelect).on(HEADER.ID.eq(detailSubSelect.field(DETAIL.HEADER_ID)))
            } else {
                headerSubSelect
                    .from(HEADER)
                    .innerJoin(detailSubSelect).on(HEADER.ID.eq(detailSubSelect.field(DETAIL.HEADER_ID)))
            }

        headerClassificationAndLovCtes.forEach {
            headerSubSelectJoinedToDetailSubSelect.join(it).on("(${HEADER.name}.${HEADER.CLASSIFICATION_FIELD_VALUES.name}) @> ${it.name}.val")
        }

        val headerConditions = getFilters(FilterLevel.Header, query, mapping)
        val headerOrs = headerConditions.map { DSL.or(it) }
        if (headerOrs.isNotEmpty()) {
            headerSubSelectJoinedToDetailSubSelect.where(and(headerOrs))
        }

        if (cte != null) {
            val headerIdField: Field<UUID?> = cte.field(CTE_HEADER_ID_FIELD)!!.cast(UUID::class.java)
            headerSubSelectJoinedToDetailSubSelect
                .join(cte).on(HEADER.ID.eq(headerIdField))
        } else {
            headerSubSelectJoinedToDetailSubSelect.where(HEADER.HEADER_TYPE_ID.eq(mapping.headerType.id))
        }

        return headerSubSelectJoinedToDetailSubSelect
    }

    private fun getDetailSelect(
        query: DetailQueryDto,
        mapping: QueryMappingData,
        classificationAndLovCtes: List<CommonTableExpression<Record1<JSONB>>>,
    ): SelectJoinStep<Record10<UUID?, JSONB?, Boolean?, UUID?, JSONB?, String?, LocalDateTime?, String?, LocalDateTime?, String?>> {
        val detailSubSelect = select(
            DETAIL.ID,
            DETAIL.KEY,
            DETAIL.ACTIVE,
            DETAIL.HEADER_ID,
            DETAIL.VALUE,
            DETAIL.CREATED_BY,
            DETAIL.CREATED_AT,
            DETAIL.LAST_MODIFIED_BY,
            DETAIL.LAST_MODIFIED_AT,
            DETAIL.NOTE,
        )
            .from(DETAIL)

        val showStateOf = query.showStateOf
        if (showStateOf == null) {
            detailSubSelect
                .innerJoin(DETAIL_LATEST).on(DETAIL.ID.eq(DETAIL_LATEST.DETAIL_ID))
        } else {
            val latestValidDetailSelect = select(
                field("row_number() over (partition by ${DETAIL.KEY.name}, ${DETAIL.HEADER_ID.name} order by ${DETAIL.VERSION_TIMESTAMP.name} desc)").`as`("row_number"),
                DETAIL.ID.`as`("detail_id"),
            )
                .from(DETAIL)
                .where(DETAIL.VERSION_TIMESTAMP.lessOrEqual(showStateOf.atOffset(ZoneOffset.UTC)))
            detailSubSelect
                .join(latestValidDetailSelect).on(
                    and(
                        DETAIL.ID.eq(latestValidDetailSelect.field("detail_id", UUID::class.java)),
                        latestValidDetailSelect.field("row_number", Int::class.java)!!.eq(1),
                    ),
                )
        }

        val conditions = getFilters(FilterLevel.Detail, query, mapping)
        val ors = conditions.map { DSL.or(it) }
        val orsPlusAdditionalConditions = if (!query.showInactive) {
            ors + DETAIL.ACTIVE.isTrue
        } else {
            ors
        }
        if (orsPlusAdditionalConditions.isNotEmpty()) {
            detailSubSelect.where(and(orsPlusAdditionalConditions))
        }

        classificationAndLovCtes.forEach {
            detailSubSelect.join(it).on("(${DETAIL.name}.${DETAIL.KEY.name} -> '$DETAIL_KEY_EFFECTIVITIES') @> ${it.name}.val")
        }
        return detailSubSelect
    }

    private suspend fun getHeaderCte(
        query: DetailQueryDto,
        headerType: HeaderType,
    ): Pair<CommonTableExpression<out Record1<UUID?>>?, List<CommonTableExpression<Record1<JSONB>>>> {
        val cte = if (query.classificationFilters.any() || query.classificationFieldFilters.any()) {
            val headerMapping = headerQueryService.getDataForHeaderIdSelect(
                headerType,
                query.classificationFilters,
                query.classificationFieldFilters,
            )
            val (headerSelect, classificationAndLovCtes) = headerQueryService.getHeaderIdSelect(
                headerType,
                query.classificationFilters,
                query.classificationFieldFilters,
                query.showInactive,
                headerMapping,
            )
            name(CTE_HEADER).fields(CTE_HEADER_ID_FIELD).`as`(headerSelect) to classificationAndLovCtes
        } else {
            null to emptyList()
        }
        return cte
    }

    private suspend fun getMeasureMap(headerType: HeaderType, query: DetailQueryDto): DynamicKeyMap {
        val unitIds = headerType.detailValueSchema?.unitIds() ?: emptySet()
        val ccyIds = headerType.detailValueSchema?.ccyIds() ?: emptySet()
        val dimensionIds = headerType.detailValueSchema?.dimensionIds() ?: emptySet()
        val unitKeys = mutableSetOf<SimpleKey>()
        val ccyKeys = mutableSetOf<SimpleKey>()
        val lovEntryKeys = mutableSetOf<SimpleKey>()
        val classificationKeys = mutableSetOf<SimpleKey>()
        query.filters.values
            .flatten()
            .filterIsInstance<MeasureCarrier>()
            .forEach { filter ->
                unitKeys.addAll(filter.unitKeys().map { it.toKey() })
                ccyKeys.addAll(filter.ccyKeys().map { it.toKey() })
            }
        query.filters.values
            .flatten()
            .filterIsInstance<LovFilterDto>()
            .forEach { filter ->
                lovEntryKeys.add(filter.equals.toKey())
            }
        (query.filters.values + query.filters.values)
            .flatten()
            .filterIsInstance<ClassificationFilterDto>()
            .forEach { filter ->
                classificationKeys.add(filter.equals.toKey())
            }
        return DynamicKeyMap.Builder()
            .put(unitService.findByIdInOrKeyIn(unitIds, unitKeys).toList())
            .put(ccyService.findByIdInOrKeyIn(ccyIds, ccyKeys).toList())
            .put(dimensionService.findByIdIn(dimensionIds).toList())
            .put(lovEntryService.findByKeyIn(lovEntryKeys).toList())
            .put(classificationService.findByKeyIn(classificationKeys).toList())
            .build()
    }

    enum class FilterLevel {
        Header,
        Detail,
    }

    private fun getClassificationAndLovCtes(
        query: DetailQueryDto,
        mapping: QueryMappingData,
    ): List<CommonTableExpression<Record1<JSONB>>> {
        val filters = query.filters.map { (fieldKey, allFilters) ->
            val field = mapping.effectivity[fieldKey.toKey()]
            val (nullFilters, filters) = allFilters.partitionIsInstance<NullFilterDto, _>()
            when (field?.field) {
                is LovFieldDefinition -> {
                    val lovEntryIds = filters.mapNotNull { filter ->
                        mapping.measures.getValue<LovEntry>(
                            QueryServiceHelper.castFilter<LovFilterDto>(
                                fieldKey,
                                filter,
                            ).equals,
                        ).id
                    }
                    QueryServiceHelper.createCteForEffectivityOrField("lov", lovEntryIds, field.effectivity.id!!, nullFilters)
                }

                is ClassificationFieldDefinition -> {
                    val classificationIds = filters.mapNotNull { filter ->
                        mapping.measures.getValue<Classification>(
                            QueryServiceHelper.castFilter<ClassificationFilterDto>(
                                fieldKey,
                                filter,
                            ).equals,
                        ).id
                    }

                    QueryServiceHelper.createCteForEffectivityOrField("classification", classificationIds, field.effectivity.id!!, nullFilters)
                }
                else -> null
            }
        }
        return filters.filterNotNull()
    }

    private fun getFilters(
        filterLevel: FilterLevel,
        query: DetailQueryDto,
        mapping: QueryMappingData,
    ): List<List<Condition>> {
        val filters = query.filters.map { (fieldKey, allFilters) ->
            val field = mapping.effectivity[fieldKey.toKey()]
            val (nullFilterDtos, filterDtos) = allFilters.partitionIsInstance<NullFilterDto, _>()
            val nonNullFilters = when (val fieldDefinition = field?.field) {
                null -> filterBuiltin(filterLevel, fieldKey, filterDtos)

                is NumericFieldDefinition -> filterDtos.filter { filterLevel == FilterLevel.Detail }.map { filter ->
                    QueryServiceHelper.numericEffectivityFilterToSql(field.effectivity.id!!, QueryServiceHelper.castFilter(fieldKey, filter), mapping.measures, fieldDefinition)
                }
                is DateFieldDefinition -> filterDtos.filter { filterLevel == FilterLevel.Detail }.map { filter ->
                    QueryServiceHelper.dateEffectivityFilterToSql(field.effectivity.id!!, QueryServiceHelper.castFilter(fieldKey, filter))
                }
                // lovs and classifications are handled by a join to a cte
                is LovFieldDefinition -> emptyList()
                is ClassificationFieldDefinition -> emptyList()
                is TextFieldDefinition -> throw InvalidDataException("Filtering for text fields is not supported.")
            }
            val nullFilters = when (field?.field) {
                // lovs and classifications (null filters) are handled by a join to a cte
                is LovFieldDefinition -> emptyList()
                is ClassificationFieldDefinition -> emptyList()
                else -> listOfNotNull(getNullFilter(filterLevel, nullFilterDtos, fieldKey, field?.effectivity))
            }
            nonNullFilters + nullFilters
        }
        return filters.filter { it.isNotEmpty() }
    }

    private fun getNullFilter(
        filterLevel: FilterLevel,
        nullFilters: List<NullFilterDto>,
        fieldKey: SimpleKeyDto,
        effectivity: Effectivity?,
    ): Condition? = nullFilters
        .takeIf { it.isNotEmpty() }
        ?.let { _ ->
            val effectivityId = effectivity?.id
            if (effectivityId != null) {
                if (filterLevel == FilterLevel.Detail) {
                    QueryServiceHelper.nullEffectivityFilterToSql(effectivityId)
                } else {
                    null
                }
            } else {
                getBuiltinSqlField(filterLevel, fieldKey)?.isNull
            }
        }

    // Duplicates the name -> field mapping but the alternative is duplicating the null filter handling...
    private fun getBuiltinSqlField(
        filterLevel: FilterLevel,
        fieldKey: SimpleKeyDto,
    ) = if (filterLevel == FilterLevel.Header) {
        when (fieldKey.key) {
            BuiltinEffectivities.HEADER, BuiltinEffectivities.HEADER_KEY -> HEADER.KEY
            BuiltinEffectivities.MODIFIER -> null
            BuiltinEffectivities.MODIFICATION_DATE -> null
            BuiltinEffectivities.DETAIL_VALUE_TYPE -> null
            else -> throw ReferencedKeyNotFoundException(EntityCode.EFFECTIVITY, fieldKey.toKey())
        }
    } else {
        when (fieldKey.key) {
            BuiltinEffectivities.HEADER, BuiltinEffectivities.HEADER_KEY -> null
            BuiltinEffectivities.MODIFIER -> DETAIL.LAST_MODIFIED_BY
            BuiltinEffectivities.MODIFICATION_DATE -> DETAIL.LAST_MODIFIED_AT
            BuiltinEffectivities.DETAIL_VALUE_TYPE -> jsonbGetAttributeAsText(DETAIL.KEY, MultiFieldEffectivityKey::detailValueType.name)
            else -> throw ReferencedKeyNotFoundException(EntityCode.EFFECTIVITY, fieldKey.toKey())
        }
    }

    private fun filterBuiltin(
        filterLevel: FilterLevel,
        fieldKey: SimpleKeyDto,
        filters: List<SingleFilterDto>,
    ) = when (fieldKey.key) {
        // todo move header filter to header cte and remove FilterLevel enum
        BuiltinEffectivities.HEADER, BuiltinEffectivities.HEADER_KEY -> filters.filter { filterLevel == FilterLevel.Header }.map { filter ->
            QueryServiceHelper.keyEq(objectMapper, QueryServiceHelper.castFilter<BuiltinLovFilterDto>(fieldKey, filter).equals.toKey(), HEADER.KEY)
        }

        BuiltinEffectivities.MODIFIER -> filters.filter { filterLevel == FilterLevel.Detail }.map { filter ->
            QueryServiceHelper.castFilter<BuiltinLovFilterDto>(fieldKey, filter)
                .equals
                .tryCast<SimpleKeyDto>()
                ?.key
                ?.let(DETAIL.LAST_MODIFIED_BY::eq)
                ?: throw InvalidDataException("Modifier can only be filtered by a simple key")
        }

        BuiltinEffectivities.MODIFICATION_DATE -> filters.filter { filterLevel == FilterLevel.Detail }.map { filter ->
            QueryServiceHelper.dateFilterToSql(
                cast(DETAIL.LAST_MODIFIED_AT, SQLDataType.LOCALDATE),
                QueryServiceHelper.castFilter(fieldKey, filter),
            )
        }

        BuiltinEffectivities.DETAIL_VALUE_TYPE -> filters.filter { filterLevel == FilterLevel.Detail }.map { filter ->
            QueryServiceHelper.castFilter<BuiltinLovFilterDto>(fieldKey, filter)
                .equals
                .tryCast<SimpleKeyDto>()
                ?.key
                ?.let(jsonbGetAttributeAsText(DETAIL.KEY, MultiFieldEffectivityKey::detailValueType.name)::eq)
                ?: throw InvalidDataException("Detail value type can only be filtered by a simple key")
        }

        else -> throw ReferencedKeyNotFoundException(EntityCode.EFFECTIVITY, fieldKey.toKey())
    }

    private fun getOrderBy(
        headerType: HeaderType,
        query: DetailQueryDto,
        fieldToEffectivity: Map<SimpleKey, EffectivityWithField>,
        select: SelectOnConditionStep<Record17<UUID?, JSONB?, JSONB?, UUID?, JSONB?, String?, JSONB?, JSONB?, String?, LocalDateTime?, String?, LocalDateTime?, String?, Boolean?, Boolean?, String?, Int>>,
    ): List<OrderField<*>> = query.sortOrder?.map { sortSegment ->
        val fieldKey = sortSegment.fieldKey
        val eff = fieldToEffectivity[fieldKey.toKey()]
        val sortField = when (eff?.field) {
            null -> when (fieldKey.key) {
                BuiltinEffectivities.HEADER -> upper(select.field(HEADER.NAME)!!)
                BuiltinEffectivities.HEADER_KEY -> orderByHeaderKey(headerType)
                BuiltinEffectivities.MODIFIER -> upper(select.field(DETAIL.LAST_MODIFIED_BY)!!)
                BuiltinEffectivities.MODIFICATION_DATE -> select.field(DETAIL.LAST_MODIFIED_AT)!!
                else -> throw ReferencedKeyNotFoundException(EntityCode.EFFECTIVITY, fieldKey.toKey())
            }
            is NumericFieldDefinition -> getEffectivityValue(eff.effectivity.id!!, SQLDataType.DOUBLE, select)
            // is TextFieldDefinition -> DSL.upper(getStringEffectivityValue(eff.effectivity.id!!, select))
            is DateFieldDefinition -> getEffectivityValue(eff.effectivity.id!!, SQLDataType.LOCALDATE, select)
            else -> error("loc and classification is not supported for ordering")
            // is LovFieldDefinition -> DSL.upper(queryState.lovEntryJoins.getValue(fieldKey).NAME)
            // is ClassificationFieldDefinition -> DSL.upper(queryState.classificationJoins.getValue(fieldKey).NAME)
        }
        when (sortSegment.direction) {
            SortDirection.ASC -> sortField.asc()
            SortDirection.DESC -> sortField.desc()
        }
    } ?: emptyList()

    private fun orderByHeaderKey(headerType: HeaderType): Field<String> =
        when (headerType.headerKeyType) {
            HeaderKeyType.SIMPLE -> jsonbGetAttributeAsText(HEADER.KEY, SimpleKey::key.name)
            HeaderKeyType.MATERIAL ->
                concat(
                    jsonbGetAttributeAsText(HEADER.KEY, MaterialKey::number.name),
                    jsonbGetAttributeAsText(HEADER.KEY, MaterialKey::revision.name),
                )
        }.let(DSL::upper)
}