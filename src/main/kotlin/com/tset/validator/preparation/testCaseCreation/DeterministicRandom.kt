package com.tset.validator.preparation.testCaseCreation

import java.lang.UnsupportedOperationException
import java.math.BigInteger
import java.security.MessageDigest
import kotlin.math.absoluteValue
import kotlin.math.exp
import kotlin.math.ln

fun deterministicRandomBoolean(testCaseName: String, usageName: String): Boolean {
    return deterministicRandom(testCaseName, usageName, 0..1) == 1
}

fun <T> deterministicRandom(testCaseName: String, usageName: String, list: List<T>): T {
    require(list.isNotEmpty())
    val id = deterministicRandom(testCaseName, usageName, list.indices)
    return list[id]
}

fun logarithmicDeterministicRandom(testCaseName: String, usageName: String, range: ClosedRange<Double>, granularity: Int = 100): Double {
    require(range.start > 0.0)
    val rand = deterministicRandom(testCaseName, usageName, ln(range.start)..ln(range.endInclusive), granularity)
    val result = exp(rand)
    require(createSlightlyBiggerRange(range).contains(result))
    return result
}

fun deterministicRandom(testCaseName: String, usageName: String, range: ClosedRange<Double>, granularity: Int = 100): Double {
    require(!range.isEmpty())
    val rand = deterministicRandom(testCaseName, usageName, 0..granularity).toDouble()
    val result = range.start + (range.endInclusive - range.start) * (rand / granularity)
    require(createSlightlyBiggerRange(range).contains(result))
    return result
}

fun deterministicRandom(testCaseName: String, usageName: String, range: IntRange): Int {
    require(range.step == 1)
    val md = MessageDigest.getInstance("MD5")
    val bigNumber = BigInteger(md.digest((testCaseName + usageName).toByteArray())).toInt().absoluteValue

    return range.min() + (bigNumber % (range.last + 1 - range.first))
}

private fun createSlightlyBiggerRange(range: ClosedRange<Double>): ClosedRange<Double> {
    val min = when {
        range.start < 0.0 -> range.start * 1.001
        range.start == 0.0 -> -0.001
        range.start > 0.0 -> range.start * 0.999
        else -> throw UnsupportedOperationException()
    }

    val max = when {
        range.endInclusive < 0.0 -> range.endInclusive * 0.999
        range.endInclusive == 0.0 -> 0.001
        range.endInclusive > 0.0 -> range.endInclusive * 1.001
        else -> throw UnsupportedOperationException()
    }

    return min..max
}
