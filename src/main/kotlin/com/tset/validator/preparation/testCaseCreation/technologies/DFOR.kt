package com.tset.validator.preparation.testCaseCreation.technologies

import com.nu.bom.core.publicapi.dtos.FieldParameter
import com.tset.validator.TestCase
import com.tset.validator.preparation.testCaseCreation.FieldParameterType
import com.tset.validator.preparation.testCaseCreation.addAdditionalInputs
import com.tset.validator.preparation.testCaseCreation.createBooleanFieldParameter
import com.tset.validator.preparation.testCaseCreation.createCleaningInputs
import com.tset.validator.preparation.testCaseCreation.createColdCalibratingInputs
import com.tset.validator.preparation.testCaseCreation.createFieldParameter
import com.tset.validator.preparation.testCaseCreation.createHeatTreatmentSteelInputs
import com.tset.validator.preparation.testCaseCreation.createMaxWallThicknessInput
import com.tset.validator.preparation.testCaseCreation.deterministicRandom

fun addDforInputs(
    baseTestCase: TestCase
): TestCase {
    val testCaseName = baseTestCase.id
    val additionalInputs = listOf(
        *createHeatTreatmentSteelInputs(testCaseName),
        createToleranceInput(testCaseName),
        *createColdCalibratingInputs(testCaseName),
        *createCleaningInputs(testCaseName),
        createMaxWallThicknessInput(testCaseName, 10.0..50.0, true),
        createBooleanFieldParameter(testCaseName, "crackTesting"),
        createBooleanFieldParameter(testCaseName, "inspection")
    )

    return addAdditionalInputs(baseTestCase, additionalInputs)
}

private fun createToleranceInput(testCaseName: String): FieldParameter {
    val enumEntries = listOf("STANDARD_DFOR", "PRECISE_DFOR", "BURRFREE_DFOR")
    return createFieldParameter(
        approvedByCostEngineer = true,
        name = "tolerance",
        value = deterministicRandom(testCaseName, "StepSubTypeTolerancesDfor", enumEntries),
        type = FieldParameterType.StepSubTypeTolerancesDfor
    )
}
