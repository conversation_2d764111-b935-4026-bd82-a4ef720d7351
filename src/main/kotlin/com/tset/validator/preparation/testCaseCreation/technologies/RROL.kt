package com.tset.validator.preparation.testCaseCreation.technologies

import com.tset.validator.TestCase
import com.tset.validator.preparation.testCaseCreation.addAdditionalInputs
import com.tset.validator.preparation.testCaseCreation.createCleaningInputs
import com.tset.validator.preparation.testCaseCreation.createHeatTreatmentSteelInputs
import com.tset.validator.preparation.testCaseCreation.createMaxWallThicknessInput

fun addRrolInputs(
    baseTestCase: TestCase
): TestCase {
    val testCaseName = baseTestCase.id
    val additionalInputs = listOf(
        *createHeatTreatmentSteelInputs(testCaseName),
        *createCleaningInputs(testCaseName),
        createMaxWallThicknessInput(testCaseName, 6.0..40.0, true)
    )

    return addAdditionalInputs(baseTestCase, additionalInputs)
}
