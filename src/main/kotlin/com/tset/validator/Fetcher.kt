package com.tset.validator

import com.nu.bom.core.publicapi.dtos.FolderView
import com.nu.bom.core.publicapi.dtos.WorkspaceDetails
import com.tset.validator.utils.Result
import com.tset.validator.webclient.AccessToken
import com.tset.validator.webclient.PublicApiService
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.FlowCollector
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow

sealed interface Fetcher {
    data class FetchError(
        val nameForReporting: String,
        val accountName: String,
        val projectKey: ProjectKey?,
        val workspaceName: String?,
        val folderName: String?
    )

    suspend fun fetch(publicApiService: PublicApiService): Flow<Result<CalculationReference, FetchError>>

    data class Project(val projectKey: ProjectKey, val accountName: String) : Fetcher {
        override suspend fun fetch(publicApiService: PublicApiService): Flow<Result<CalculationReference, FetchError>> =
            flow {
                val response =
                    publicApiService
                        .getProject(accountName, projectKey)
                        .res()

                if (response.ok()) {
                    val bomNodeIds = response.unwrapSuccess().bomNodesIds!!

                    println("fetched ${bomNodeIds.size} bomnodeIds for project $projectKey")
                    for (bomNodeId in bomNodeIds) {
                        emit(
                            Result.fromSuccess(
                                CalculationReference(accountName, publicApiService.environment, projectKey, bomNodeId, null)
                            )
                        )
                    }
                } else {
                    println("unable to fetch bomNodes of project $projectKey due to ${truncatedErrorString(response.unwrapError())}")
                    emit(Result.fromError(FetchError("errorFetchingBomNodesOfProject_$projectKey", accountName, projectKey, null, null)))
                }
            }
    }

    data class Account(val accountName: String) : Fetcher {
        override suspend fun fetch(publicApiService: PublicApiService): Flow<Result<CalculationReference, FetchError>> =
            flow {
                val response =
                    publicApiService
                        .getProjects(accountName)
                        .res()

                if (response.ok()) {
                    val projects = response.unwrapSuccess().currentPage.map { ProjectKey(it.key) }

                    println("fetched ${projects.size} projects for account $accountName")
                    for (project in projects) {
                        emitAll(Project(project, accountName).fetch(publicApiService))
                    }
                } else {
                    println("unable to fetch projects of account $accountName due to ${truncatedErrorString(response.unwrapError())}")
                    emit(Result.fromError(FetchError("errorFetchingProjectsOfAccount_$accountName", accountName, null, null, null)))
                }
            }
    }

    data class Folder(val workspaceName: String, val folderName: String, val accountName: String) : Fetcher {
        override suspend fun fetch(
            publicApiService: PublicApiService
        ): Flow<Result<CalculationReference, FetchError>> = flow {
            val folderResult = retrieveFolderFromWorkspace(publicApiService)
            if (folderResult.ok()) {
                fetchProjectsForFolder(publicApiService, folderResult.unwrapSuccess().id)
            } else {
                emit(Result.fromError(folderResult.unwrapError()))
            }
        }

        private suspend fun FlowCollector<Result<CalculationReference, FetchError>>.fetchProjectsForFolder(
            publicApiService: PublicApiService,
            folderId: String
        ) {
            val response = publicApiService
                .getFolder(accountName, folderId)
                .res()

            if (response.ok()) {
                val projects = response.unwrapSuccess().projects.map { ProjectKey(it.key) }

                println("fetched ${projects.size} projects for workspace $workspaceName and folder $folderName")
                for (project in projects) {
                    emitAll(Project(project, accountName).fetch(publicApiService))
                }
            } else {
                println(
                    "unable to fetch projects of account $accountName, workspace $workspaceName and folder $folderName due to ${
                    truncatedErrorString(
                        response.unwrapError()
                    )
                    }"
                )
                emit(
                    Result.fromError(
                        FetchError(
                            "errorFetchingProjectsOfAccount_$accountName",
                            accountName,
                            null,
                            workspaceName,
                            folderName
                        )
                    )
                )
            }
        }

        private suspend fun retrieveFolderFromWorkspace(
            publicApiService: PublicApiService
        ): Result<FolderView, FetchError> {
            val workspaceResult = getWorkspace(publicApiService)

            return if (workspaceResult.ok()) {
                val workspace = workspaceResult.unwrapSuccess()
                getFolder(publicApiService, workspace.id)
            } else {
                Result.fromError(workspaceResult.unwrapError())
            }
        }

        private suspend fun getWorkspace(publicApiService: PublicApiService): Result<WorkspaceDetails, FetchError> {
            val response = publicApiService
                .getRootWorkspace(this.accountName)
                .res()

            return if (response.ok()) {
                val workspace = response.unwrapSuccess().find { it.name == this.workspaceName }
                if (workspace == null) {
                    println("unable to fetch workspace with name ${this.workspaceName}")
                    Result.fromError(
                        FetchError(
                            "errorFetchingWorkspaceOfAccount_${this.accountName}",
                            this.accountName,
                            null,
                            this.workspaceName,
                            null
                        )
                    )
                } else {
                    Result.fromSuccess(workspace)
                }
            } else {
                println(
                    "unable to fetch workspaces of account ${this.accountName} due to ${
                    truncatedErrorString(response.unwrapError())
                    }"
                )
                Result.fromError(
                    FetchError(
                        "errorFetchingProjectsOfAccount_${this.accountName}",
                        this.accountName,
                        null,
                        null,
                        null
                    )
                )
            }
        }

        private suspend fun getFolder(
            publicApiService: PublicApiService,
            workspaceId: String
        ): Result<FolderView, FetchError> {
            val response = publicApiService
                .getRootFolder(accountName, workspaceId)
                .res()

            return if (response.ok()) {
                val folder = response.unwrapSuccess().folders.find { it.name == folderName }
                if (folder == null) {
                    println("unable to fetch folder with name $folderName in workspace $workspaceName")
                    Result.fromError(
                        FetchError(
                            "errorFetchingFolderOfAccount_$accountName",
                            accountName,
                            projectKey = null,
                            workspaceName = workspaceName,
                            folderName = folderName
                        )
                    )
                } else {
                    Result.fromSuccess(folder)
                }
            } else {
                println(
                    "unable to fetch folder of account ${this.accountName} and workspace $workspaceId due to ${
                    truncatedErrorString(
                        response.unwrapError()
                    )
                    }"
                )
                Result.fromError(
                    FetchError(
                        "errorFetchingFoldersOfAccount_${this.accountName}",
                        this.accountName,
                        null,
                        workspaceName,
                        null
                    )
                )
            }
        }
    }

    data object Environment : Fetcher {

        suspend fun fetchAccounts(publicApiService: PublicApiService): List<String> {
            val response =
                publicApiService
                    .getAccounts()
                    .res()
                    .mapSuccess {
                        it.map { account -> account.accountName }
                    }

            return response.success ?: throw RuntimeException(
                "unable to get accounts on ${publicApiService.environment} due to ${truncatedErrorString(response.unwrapError())}"
            )
        }

        suspend fun fetchAccounts(
            publicApiService: PublicApiService,
            accessToken: AccessToken
        ): List<PublicApiService.AccountNameWithId> {
            val response = publicApiService.getAccounts(accessToken = accessToken).res()

            return response.success ?: throw RuntimeException(
                "unable to get accounts on ${publicApiService.environment} due to ${truncatedErrorString(response.unwrapError())}"
            )
        }

        override suspend fun fetch(
            publicApiService: PublicApiService
        ): Flow<Result<CalculationReference, FetchError>> = flow {
            val response = publicApiService
                .getAccounts()
                .res()

            val accountNamesWithIds = response.success ?: throw RuntimeException("unable to get accounts on ${publicApiService.environment} due to ${truncatedErrorString(response.unwrapError())}")

            println("fetched ${accountNamesWithIds.size} accounts on ${publicApiService.environment}")
            for (accountNameWithId in accountNamesWithIds) {
                emitAll(Account(accountNameWithId.accountName).fetch(publicApiService))
            }
        }
    }
}
