package com.tset.validator

import com.tset.validator.config.BasicConfig
import com.tset.validator.config.CompareConfig
import com.tset.validator.config.EagerMigrationConfig
import com.tset.validator.config.ExecConfig
import com.tset.validator.config.FetchReferenceConfig
import com.tset.validator.config.MasterdataMigrationConfig
import com.tset.validator.config.ModusOperandi
import com.tset.validator.config.PipelineTriggerInformation
import com.tset.validator.config.PostRunPersistConfig
import com.tset.validator.config.SlackReportingConfig
import com.tset.validator.config.UpdateReferenceConfig
import com.tset.validator.execution.ExecInput
import com.tset.validator.execution.ExecResult
import com.tset.validator.execution.GenericExecResult
import com.tset.validator.execution.executeEagerMigration
import com.tset.validator.execution.executeMasterdataMigration
import com.tset.validator.preparation.prepareCreate
import com.tset.validator.preparation.prepareEagerMigration
import com.tset.validator.preparation.prepareMasterdataMigration
import com.tset.validator.preparation.preparePostRunPersist
import com.tset.validator.preparation.prepareRetrieve
import com.tset.validator.preparation.prepareSimulate
import com.tset.validator.reporting.CompareReporter.Companion.REPORT_FOLDER
import com.tset.validator.reporting.CompareSlacker
import com.tset.validator.reporting.ComparisonObsoleteSlacker
import com.tset.validator.reporting.JUnitTestReportGenerator
import com.tset.validator.reporting.PostRunPersistSlacker
import com.tset.validator.reporting.SlackChannelInfo
import com.tset.validator.s3.S3Client
import com.tset.validator.s3.S3ObjectKey
import com.tset.validator.s3.getWithChecks
import com.tset.validator.utils.Result
import com.tset.validator.webclient.AccountService
import com.tset.validator.webclient.PublicApiService
import com.tset.validator.webclient.ServiceType
import com.tset.validator.webclient.TokenServiceRegistry
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import java.io.File
import java.nio.file.Files
import java.nio.file.Paths
import kotlin.system.exitProcess
import kotlin.time.Duration.Companion.seconds

const val NON_EXISTENT_RUN_ID = "this_run_does_not_exist_dont_at_me"

private const val EXECUTION_PREFIX = "execution-"
private const val REFERENCE_PREFIX = "reference_"

// according to THO, it’s fine if the reference is cleaned up, since the one we need should always be fresh enough
fun getS3ReferenceKey(referenceIdentifier: String) =
    S3ObjectKey<S3ObjectKey<ExecResult>>("$REFERENCE_PREFIX$referenceIdentifier", persistsForever = false)

fun <ResultType : GenericExecResult> getS3ExecResultKey(referenceIdentifier: String) =
    S3ObjectKey<ResultType>(referenceIdentifier, persistsForever = false)

fun <ResultType : GenericExecResult> makeS3ExecKey(
    basicConfig: BasicConfig,
    executionSuffix: Char,
    additionalSuffix: String = ""
): S3ObjectKey<ResultType> = getS3ExecResultKey("$EXECUTION_PREFIX${basicConfig.runId}-$executionSuffix$additionalSuffix")

fun localValidationBotRunMain(
    startingTime: Instant,
    runPostRunPersist: Boolean
) = runBlocking {
    println("Let's localize some validation!")

    val basicConfig = BasicConfig.load("/configuration/local/application-local.yml")
//    val slackReportingConfig = SlackReportingConfig.load("/configuration/local/application-local-slack_reporting.yml")
//    val baselineConfig = ExecConfig.load("/configuration/local/application-local-baseline.yml")
//    val alternativeConfig = ExecConfig.load("/configuration/local/application-local-alternative.yml")
//
//    for (initConfig in listOf(baselineConfig, alternativeConfig)) {
//        val accountService = AccountService(initConfig.httpClientSettings, initConfig.environment)
//        TokenServiceRegistry.initialize(basicConfig, 'l', accountService)
//    }

    val masterdataMigrationConfig = MasterdataMigrationConfig.load("/configuration/application-masterdata_migration.yml")
    masterdataMigrationMain(basicConfig, masterdataMigrationConfig, S3Client(basicConfig.s3), startingTime)

//    val tempCompareConfig = CompareConfig.load("/configuration/local/application-local-compare.yml")!!
//
//    val baselineResult =
//        tempCompareConfig.baselineResult.let {
//            async {
//                if (it.res == "temporary/toBeReplaced") {
//                    executionMain(basicConfig, slackReportingConfig, baselineConfig, startingTime)
//                        .onError { exitProcess(it) }
//                        .unwrapSuccess()
//                } else {
//                    it
//                }
//            }
//        }

}

private suspend fun preExecutionHealthCheck(client: PublicApiService) {
    val health = client.healthCheck()
    val url = client.environment.serviceBaseUrl(ServiceType.Unspecified)

    if (health.isBad()) {
        throw RuntimeException("Health-check failed with '$health' for environment $url.")
    } else {
        println("Environment $url seems healthy :))")
    }
}

suspend fun executionMain(
    basicConfig: BasicConfig,
    slackReportingConfig: SlackReportingConfig,
    execConfig: ExecConfig,
    startingTime: Instant
): Result<S3ObjectKey<ExecResult>, ReturnCode> {
    val s3Client = S3Client(basicConfig.s3)

    val execKey = makeS3ExecKey<ExecResult>(basicConfig, execConfig.preparationSettings.executionSuffix)

    if (s3Client.doesObjectKeyExist(execKey)) {
        println("Results for execution $execKey already exist, exiting early.")
        println("Note: If you want a new result, just run a new pipeline.")
        return Result.fromSuccess(execKey)
    }

    val execInput = ExecInput(execConfig)
    val client = PublicApiService(execInput.environment, execInput.httpClientSettings)
    preExecutionHealthCheck(client)

    val comparisonObsoleteSlacker = ComparisonObsoleteSlacker.create(slackReportingConfig)
    val prepSettings = execConfig.preparationSettings
    when (prepSettings.executionMode) {
        is ExecutionMode.Create -> {
            val setup = prepareCreate(basicConfig, prepSettings, s3Client, client, startingTime)
            execInput.executeCreation(setup, client)
        }
        is ExecutionMode.Simulate -> {
            val setup = prepareSimulate(basicConfig, prepSettings, s3Client, client, startingTime)
            execInput.executeSimulation(setup, client)
        }
        is ExecutionMode.Retrieve -> {
            val setup = prepareRetrieve(basicConfig, prepSettings, s3Client, client, startingTime)
            if (setup.ok()) {
                execInput.executeRetrieval(setup.unwrapSuccess(), client)
            } else {
                comparisonObsoleteSlacker.report(setup.unwrapError())
                return Result.fromError(EXIT_OBSOLETE_COMPARISON)
            }
        }
    }

    return Result.fromSuccess(execKey)
}

suspend fun compareMain(
    basicConfig: BasicConfig,
    slackReportingConfig: SlackReportingConfig,
    compareConfig: CompareConfig
): ReturnCode {
    val s3Client = S3Client(basicConfig.s3)

    val baselineResult = s3Client.get(compareConfig.baselineResult)
    val alternativeResult = s3Client.get(compareConfig.alternativeResult)

    val comparisonResult = compare(baselineResult, alternativeResult, compareConfig.accuracyThreshold)

    val compareSlacker = CompareSlacker.create(slackReportingConfig)
    withContext(Dispatchers.IO) {
        // create folder before adding reports there
        Files.createDirectories(Paths.get(REPORT_FOLDER))
    }
    for (reporter in listOf(JUnitTestReportGenerator(basicConfig.runId), compareSlacker)) {
        reporter.report(comparisonResult, compareConfig)?.let { permalink ->
            println("Report for ${permalink.id} can be found under ${permalink.url}")
            writeFile(
                "$REPORT_FOLDER/${permalink.id}.txt",
                permalink.url
            )
        }
    }

    return when (getComparisonStatus(comparisonResult)) {
        ComparisonStatus.AllAlternativeCouldBeCalculatedAndIsWithinThreshold -> EXIT_SUCCESS
        ComparisonStatus.AllAlternativeIsWithinThreshold -> EXIT_ACCEPTABLE_COMPARISON
        ComparisonStatus.Red -> EXIT_BAD_COMPARISON
    }
}

suspend fun postRunPersistMain(
    basicConfig: BasicConfig,
    slackReportingConfig: SlackReportingConfig,
    postRunPersistConfig: PostRunPersistConfig,
    startingTime: Instant
): ReturnCode {
    val s3Client = S3Client(basicConfig.s3)

    val execKeyToPersistOn = getWithChecks(s3Client, postRunPersistConfig.postRunExecKeyToPersistOn) ?: return EXIT_UNEXPECTED_ERROR
    if (execKeyToPersistOn.input.preparationSettings.executionMode != ExecutionMode.Simulate) {
        println(
            "Execution mode of ${postRunPersistConfig.postRunExecKeyToPersistOn} was '${execKeyToPersistOn.input.preparationSettings.executionMode}' instead of '${ExecutionMode.Simulate}', so there is nothing to persist."
        )
        return EXIT_SUCCESS
    }
    val execKeyToCompareAgainst =
        getWithChecks(
            s3Client,
            postRunPersistConfig.postRunExecKeyToCompareAgainst
        ) ?: return EXIT_UNEXPECTED_ERROR

    val oldExecInput = execKeyToPersistOn.input
    val accountService = AccountService(oldExecInput.httpClientSettings, oldExecInput.environment)
    TokenServiceRegistry.initialize(basicConfig, oldExecInput.preparationSettings.executionSuffix, accountService)
    val client = PublicApiService(oldExecInput.environment, oldExecInput.httpClientSettings)
    preExecutionHealthCheck(client)

    val setup =
        preparePostRunPersist(
            basicConfig,
            oldExecInput.preparationSettings,
            s3Client,
            client,
            startingTime,
            execKeyToPersistOn,
            execKeyToCompareAgainst,
            postRunPersistConfig.accuracyThreshold,
            postRunPersistConfig.additionalSuffix
        )

    val execInput =
        oldExecInput.copy(
            preparationSettings =
            oldExecInput.preparationSettings.copy(
                executionMode = ExecutionMode.Create
            )
        )

    val result = execInput.executeCreation(setup, client)

    val postRunPersistSlacker =
        PostRunPersistSlacker(
            slackReportingConfig.outputSlackChannel,
            slackReportingConfig.slackToken,
            slackReportingConfig.pipelineTriggerInformation
        )
    postRunPersistSlacker.report(postRunPersistConfig.postRunExecKeyToPersistOn, result)
    return EXIT_SUCCESS
}

suspend fun eagerMigrationMain(
    basicConfig: BasicConfig,
    eagerMigrationConfig: EagerMigrationConfig,
    s3Client: S3Client,
    startingTime: Instant
): ReturnCode {
    val suffix = 'e'
    val accountService = AccountService(eagerMigrationConfig.httpClientSettings, eagerMigrationConfig.environment)
    TokenServiceRegistry.initialize(basicConfig, suffix, accountService)

    val client = PublicApiService(eagerMigrationConfig.environment, eagerMigrationConfig.httpClientSettings)
    preExecutionHealthCheck(client)
    val setup = prepareEagerMigration(basicConfig, eagerMigrationConfig, client, s3Client, startingTime, suffix)

    val result =
        executeEagerMigration(
            setup,
            client,
            eagerMigrationConfig.endpoint,
            eagerMigrationConfig.httpClientSettings.maxConcurrentCalculations,
            15.0.seconds
        )

    println("Total migration took ${result.totalDuration}.")

    return if (result.wasSuccessful) {
        EXIT_SUCCESS
    } else {
        result.printErrorsIfFailed()
        EXIT_FAILED_EAGER_MIGRATION
    }
}

suspend fun masterdataMigrationMain(
    basicConfig: BasicConfig,
    masterdataMigrationConfig: MasterdataMigrationConfig,
    s3Client: S3Client,
    startingTime: Instant
): ReturnCode {
    val suffix = 'm'

    println(
        "Migration is executed on env: ${masterdataMigrationConfig.environment.sanitizedBaseUrl}, ${masterdataMigrationConfig.environment.type}, ${masterdataMigrationConfig.environment.realm}."
    )

    val accountService =
        AccountService(
            masterdataMigrationConfig.httpClientSettings,
            masterdataMigrationConfig.environment
        )
    TokenServiceRegistry.initialize(basicConfig, suffix, accountService = accountService)

    val client = PublicApiService(masterdataMigrationConfig.environment, masterdataMigrationConfig.httpClientSettings)
    preExecutionHealthCheck(client)
    val setup = prepareMasterdataMigration(basicConfig, masterdataMigrationConfig, client, s3Client, startingTime, suffix)

    val result =
        executeMasterdataMigration(
            setup,
            client,
            masterdataMigrationConfig
        )

    println("Total masterdata migration took ${result.totalDuration}.")

    return if (result.wasSuccessful) {
        EXIT_SUCCESS
    } else {
        result.printErrorsIfFailed()
        EXIT_FAILED_EAGER_MIGRATION
    }
}

suspend fun fetchReferenceRunId(
    referenceIdentifier: String,
    s3Client: S3Client
): S3ObjectKey<ExecResult>? {
    val referenceKey = getS3ReferenceKey(referenceIdentifier)
    return try {
        if (s3Client.doesObjectKeyExist(referenceKey)) {
            val runId = s3Client.get(referenceKey)
            println("Fetched run-id '$runId' for reference '$referenceIdentifier'.")
            runId
        } else {
            println("No run-id exists (yet) for reference '$referenceIdentifier'.")
            null
        }
    } catch (e: Throwable) {
        println("Failed to fetch run-id for reference '$referenceIdentifier'. Error: $e")
        null
    }
}

fun writeDotenvVariables(
    filename: String,
    variables: Map<String, String>
) {
    val content = variables.map { "${it.key}=${it.value}" }.joinToString("\n")
    writeFile(filename, content)
    println("wrote environment variables $variables to $filename.")
}

fun writeFile(
    filename: String,
    content: String
) {
    val file = File(filename)
    file.writeText(content)
    println("wrote ${content.length} chars to $filename.")
}

typealias ReturnCode = Int

const val EXIT_SUCCESS: ReturnCode = 0
const val EXIT_UNEXPECTED_ERROR: ReturnCode = 1
const val EXIT_BAD_COMPARISON: ReturnCode = 2
const val EXIT_ACCEPTABLE_COMPARISON: ReturnCode = 0
const val EXIT_OBSOLETE_COMPARISON: ReturnCode = 4
const val EXIT_FAILED_EAGER_MIGRATION: ReturnCode = 5

fun main(args: Array<String>) =
    runBlocking {
        var exitCode: ReturnCode
        try {
            val startingTime = Clock.System.now()

            if (args.isNotEmpty()) {
                localValidationBotRunMain(startingTime, runPostRunPersist = false)
                return@runBlocking
            }
            exitCode = mainImpl(startingTime)
        } catch (e: Throwable) {
            exitCode = EXIT_UNEXPECTED_ERROR
            e.printStackTrace()
        } finally {
            TokenServiceRegistry.clear()
        }
        println("Validation-Bot finished. Exiting with code $exitCode")
        exitProcess(exitCode)
    }

// enforce return type
suspend fun mainImpl(startingTime: Instant): ReturnCode {
    println("Non-local hi")

    val basicConfig = BasicConfig.load("/configuration/application.yml")
//    val slackReportingConfig = SlackReportingConfig.load("/configuration/application-slack_reporting.yml")
    val slackReportingConfig =
        SlackReportingConfig(
            slackToken = "fake",
            outputSlackChannel = SlackChannelInfo("fake", "fake"),
            slackMessageTitle = null,
            pipelineUrl = "fake",
            pipelineTriggerInformation = PipelineTriggerInformation("", "")
        )
    println("Selected modus operandi is: ${basicConfig.modusOperandi}")

    return when (basicConfig.modusOperandi) {
        ModusOperandi.Execute -> {
            val execConfig = ExecConfig.load("/configuration/application-execute.yml")
            val accountService = AccountService(execConfig.httpClientSettings, execConfig.environment)
            TokenServiceRegistry.initialize(basicConfig, execConfig.preparationSettings.executionSuffix, accountService)
            val result = executionMain(basicConfig, slackReportingConfig, execConfig, startingTime)
            return if (result.ok()) {
                writeDotenvVariables(
                    "build/exec_result.env",
                    mapOf(execConfig.outputVariable to result.unwrapSuccess().toString())
                )
                EXIT_SUCCESS
            } else {
                result.unwrapError()
            }
        }
        ModusOperandi.Compare -> {
            val compareConfigResource = "/configuration/application-compare.yml"
            val compareConfig = CompareConfig.load(compareConfigResource)

            // COST-45462 RR will pick this up
            writeFile(
                "reference_id.txt",
                CompareConfig.loadAlternativeResult(compareConfigResource).objectName
            )

            if (compareConfig == null) {
                println("no baseline to compare against, we are done.")
                EXIT_SUCCESS
            } else {
                println("comparing baseline '${compareConfig.baselineResult}' with alternative '${compareConfig.alternativeResult}'")
                val exitCode = compareMain(basicConfig, slackReportingConfig, compareConfig)
                println("did comparison")
                exitCode
            }
        }
        ModusOperandi.FetchReference -> {
            val fetchConfig = FetchReferenceConfig.load("/configuration/application-fetch_reference.yml")
            val s3Client = S3Client(basicConfig.s3)
            val referenceRunId = fetchReferenceRunId(fetchConfig.referenceResultIdentifier, s3Client)
            writeDotenvVariables(
                "build/fetch_reference.env",
                mapOf(fetchConfig.outputVariable to (referenceRunId?.toString() ?: NON_EXISTENT_RUN_ID))
            )
            EXIT_SUCCESS
        }
        ModusOperandi.UpdateReference -> {
            val updateConfig = UpdateReferenceConfig.load("/configuration/application-update_reference.yml")
            val s3Client = S3Client(basicConfig.s3)
            val oldExecKey = fetchReferenceRunId(updateConfig.referenceResultIdentifier, s3Client)
            val newExecKey = updateConfig.getNewExecKey(s3Client)

            s3Client.putAsJson(newExecKey, getS3ReferenceKey(updateConfig.referenceResultIdentifier))
            if (oldExecKey == null) {
                println("set reference result '${updateConfig.referenceResultIdentifier}' to '$newExecKey' (didn't exist before)")
            } else {
                println("updated reference result '${updateConfig.referenceResultIdentifier}' to '$newExecKey' (was '$oldExecKey' before)")
            }
            EXIT_SUCCESS
        }
        ModusOperandi.PostRunPersist -> {
            val postRunPersistConfig = PostRunPersistConfig.load("/configuration/application-post_run_persist.yml")
            return postRunPersistMain(basicConfig, slackReportingConfig, postRunPersistConfig, startingTime)
        }

        ModusOperandi.EagerMigration -> {
            val eagerMigrationConfig = EagerMigrationConfig.load("/configuration/application-eager_migration.yml")
            return eagerMigrationMain(basicConfig, eagerMigrationConfig, S3Client(basicConfig.s3), startingTime)
        }

        ModusOperandi.MasterdataMigration -> {
            val masterdataMigrationConfig = MasterdataMigrationConfig.load("/configuration/application-masterdata_migration.yml")
            return masterdataMigrationMain(basicConfig, masterdataMigrationConfig, S3Client(basicConfig.s3), startingTime)
        }
    }
}
