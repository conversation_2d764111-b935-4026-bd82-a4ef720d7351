package com.tset.validator.reporting

import com.nu.qa.utilities.messenger.SlackMessenger
import com.slack.api.methods.SlackApiTextResponse
import com.slack.api.methods.SlackFilesUploadV2Exception
import com.slack.api.methods.request.files.FilesUploadV2Request.UploadFile
import com.tset.validator.ComparisonResult
import com.tset.validator.ComparisonStatus
import com.tset.validator.config.PipelineTriggerInformation
import com.tset.validator.exceptions.InvalidConfigError
import com.tset.validator.exceptions.SlackError
import com.tset.validator.getComparisonStatus
import java.nio.file.Path
import java.time.Month
import java.time.ZoneId
import java.time.ZonedDateTime

data class SlackChannelInfo(
    val name: String,
    val id: String
)

data class FileInMemory(
    val content: String,
    val fileName: String
)

typealias SlackMessageId = String

open class Slacker(
    private val channel: SlackChannelInfo,
    private val pipelineTriggerInformation: PipelineTriggerInformation,
    token: String
) : BasicSlacker(token) {

    private fun sendText(msg: String, parentSlackMessageId: SlackMessageId?): SlackMessageId {
        val response = getSlackInstance().methods(token).chatPostMessage {
            it
                .channel(channel.id)
                .threadTs(parentSlackMessageId)
                .text(celebrateBirthday(msg))
        }
        errorHandling(response)
        return parentSlackMessageId ?: response.ts!!
    }

    fun sendMessage(msg: String): SlackMessageId = sendText(msg, null)

    fun sendComment(msg: String, parentSlackMessageId: SlackMessageId) {
        sendText(msg, parentSlackMessageId)
    }

    // TODO: I can for the love of god not figure out how to return the parentSlackMessageId, so no return it is
    fun sendTextFiles(
        files: List<FileInMemory>,
        msg: String? = null,
        parentSlackMessageId: SlackMessageId? = null
    ) {
        // only channel ids are supported: https://github.com/slackapi/python-slack-sdk/issues/1326
        try {
            val response = getSlackInstance().methods(token).filesUploadV2 {
                it
                    .channel(channel.id)
                    .threadTs(parentSlackMessageId)
                    .initialComment(celebrateBirthday(msg))
                    .uploadFiles(
                        files.map { file ->
                            UploadFile.builder()
                                .content(file.content)
                                .filename(file.fileName)
                                .title(file.fileName)
                                .build()
                        }
                    )
            }
            // if this fails, then something is apparently not handled by SlackFilesUploadV2Exception
            require(response.isOk)
        } catch (e: SlackFilesUploadV2Exception) {
            val errorResponses = (e.getURLResponses + e.completeResponse).filterNotNull().filterNot { it.isOk }
            if (errorResponses.isEmpty()) {
                throw SlackError("'filesUploadV2' failed without stating why")
            }
            errorHandling(errorResponses.first())
        }
    }

    fun sendTextFile(
        path: Path,
        message: String? = null,
        parentSlackMessageId: SlackMessageId? = null
    ) =
        sendTextFiles(listOf(FileInMemory(path.toFile().readText(), path.fileName.toString())), message, parentSlackMessageId)

    fun getPermalink(slackMessageId: SlackMessageId): PermalinkToReport {
        val response = getSlackInstance().methods(token).chatGetPermalink {
            it
                .channel(channel.id)
                .messageTs(slackMessageId)
        }

        errorHandling(response)
        return PermalinkToReport("slacker", response.permalink)
    }

    private fun errorHandling(response: SlackApiTextResponse) {
        if (response.isOk) {
            return
        }
        when (response.error) {
            "not_in_channel" -> throw SlackError("Validation-Bot needs to be added to ${channel.name} to be able to post there.")
            "invalid_auth" -> throw SlackError("Validation bot could not authenticate towards Slack.")
            "not_authed" -> throw SlackError("No (or empty) slack token was provided to validation bot.")
            "channel_not_found" -> /* shouldn’t happen */ throw SlackError("Channel id '${channel.id}' does not exist.")
            else -> throw RuntimeException("No handling implemented for slack error '${response.error}'.")
        }
    }

    private val pingForTriggeringUser: Lazy<String> = lazy {
        SlackMessenger(
            "weebHookUrl, but we hopefully don't need it",
            this.pipelineTriggerInformation.slackUserReadToken
        ).getUserNotifierBlock(this.pipelineTriggerInformation.pipelineTriggeringUser)
    }

    protected fun pingForTriggeringUser(applyFallbackMessage: Boolean): String {
        val ping = pingForTriggeringUser.value
        return if (applyFallbackMessage && ping.isBlank()) {
            "I’d ping someone, but I have no idea who triggered this run :shrug:"
        } else {
            ping
        }
    }

    companion object {
        fun celebrateBirthday(message: String?): String? {
            if (message == null) { return null }
            val time = ZonedDateTime.now().withZoneSameInstant(ZoneId.of("Europe/Vienna"))
            val todayWeCelebrate =
                time.month == Month.AUGUST && time.dayOfMonth == 10

            return if (todayWeCelebrate) {
                val celebrateEmoji = ":valitdnationbot:"
                "$celebrateEmoji\n$message\n$celebrateEmoji"
            } else { message }
        }

        fun fittingSlackEmoji(target: Int, actual: Int): String =
            when {
                actual > target -> ":hackermans:"
                target == 0 -> ":thisisfine:"
                target == actual -> ":pog:"
                actual == 0 -> ":clown_face:"
                actual + 1 == target -> ":face_with_rolling_eyes:"
                actual * 3 < target -> ":omegaluliguess:"
                actual * 2 < target -> ":sadge:"
                else -> ":4weird:"
            }

        fun trafficLightEmoji(comparisonResult: ComparisonResult): String =
            when (getComparisonStatus(comparisonResult)) {
                ComparisonStatus.AllAlternativeCouldBeCalculatedAndIsWithinThreshold -> ":green_heart:"
                ComparisonStatus.AllAlternativeIsWithinThreshold -> ":large_orange_diamond:"
                ComparisonStatus.Red -> ":rotating_light:"
            }

        fun codeBlockMarkDown(str: Any): String = "```$str```"
        fun inlineCodeBlockMarkDown(str: Any): String = "`$str`"
        fun bold(str: Any): String = "*$str*"
        fun linkWithCustomText(url: String, customText: String): String {
            require(customText.isNotEmpty())
            return "<$url|$customText>"
        }
    }
}

fun getSlackChannelInfo(slackToken: String, channelName: String): SlackChannelInfo {
    if (!channelName.startsWith("#")) {
        throw InvalidConfigError("A valid slack channel name needs to start with '#' (was '$channelName')")
    }

    val basicSlacker = BasicSlacker(slackToken)

    val id = basicSlacker.getChannelId(channelName) ?: throw InvalidConfigError("Could not find channel '$channelName'")
    return SlackChannelInfo(channelName, id)
}
