package com.tset.validator.execution.machine

import com.nu.bom.core.publicapi.dtos.FieldParameter
import com.nu.masterdata.dto.v1.detail.*
import com.nu.masterdata.dto.v1.header.HeaderDto
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import com.nu.masterdata.dto.v1.schema.*
import com.tset.validator.execution.MdMigrationMasterDataDetailDto
import jakarta.validation.Valid

object MachineDetailBuilder {
    private fun getField(
        nbkMachine: MdMigrationMasterDataDetailDto,
        fieldName: String,
    ): FieldParameter? = nbkMachine.fields.firstOrNull { it.name == fieldName }

    fun createMachine(
        nbkMachine: MdMigrationMasterDataDetailDto,
        masterDataType: MachineMasterDataType,
    ): Pair<HeaderDto, List<DetailDto>>? {
        println("migrating machine ${nbkMachine.key} of type ${nbkMachine.type}")

        // Get basic machine fields
        val designationField = getField(nbkMachine, "designation")
        val displayDesignationField = getField(nbkMachine, "displayDesignation")
        val manufacturerField = getField(nbkMachine, "manufacturer")
        val typeField = getField(nbkMachine, "type")
        val technicalDescriptionField = getField(nbkMachine, "technicalDescription")

        // Get currency fields
        val masterdataBaseCurrencyField = getField(nbkMachine, "masterdataBaseCurrency") ?:
            FieldParameter(
                name = "masterdataBaseCurrency",
                value = "EUR",
                type = "Currency",
                unit = null,
                source = "M"
            ).also { println("NO masterdataBaseCurrency for machine ${nbkMachine.key} of type ${nbkMachine.type}") }

        val baseCurrencyField = getField(nbkMachine, "baseCurrency") ?:
            masterdataBaseCurrencyField.also { println("NO baseCurrency for machine ${nbkMachine.key} of type ${nbkMachine.type}") }

        // Get investment fields
        val investBaseField = getField(nbkMachine, "investBase")
        val investFundamentField = getField(nbkMachine, "investFundament")
        val investMiscField = getField(nbkMachine, "investMisc")
        val investSetupField = getField(nbkMachine, "investSetup")

        // TODO: Get emission field (placeholder for now)
        // val emissionField = getField(nbkMachine, "cO2PerUnit") // or other emission field

        val classificationFields = listOfNotNull(
            manufacturerField,
            typeField,
            technicalDescriptionField,
            getField(nbkMachine, "connectedLoad"),
            getField(nbkMachine, "powerOnTimeRate"),
            getField(nbkMachine, "gasConsumptionMeltingPerHour"),
            getField(nbkMachine, "gasConsumptionKeepWarmPerHour"),
            getField(nbkMachine, "oxigenConsumptionPerHour"),
            getField(nbkMachine, "castExcipientsConsumptionPerHour"),
            getField(nbkMachine, "machineWeight"),
            getField(nbkMachine, "requiredSpaceGross"),
            getField(nbkMachine, "requiredSpaceNet"),
            getField(nbkMachine, "maintenanceRate"),
            getField(nbkMachine, "consumableRate"),
            getField(nbkMachine, "depreciationTime"),
        )

        val header = createHeader(
            masterDataType,
            nbkMachine.key,
            nbkMachine.type,
            displayDesignationField,
            designationField,
            baseCurrencyField,
            classificationFields,
        )

        val details = createInvestmentAndEmissionDetails(
            headerKey = (header?.key as SimpleKeyDto?)?.key ?: "unknown",
            investBaseField = investBaseField,
            investFundamentField = investFundamentField,
            investMiscField = investMiscField,
            investSetupField = investSetupField,
            // emissionField = emissionField, // TODO: Add when emission mapping is implemented
            currency = masterdataBaseCurrencyField,
            representsAccountData = nbkMachine.representsAccountData,
        )

        return if (header == null) null else header to details
    }

    const val MACHINE_CLASSIFICATION_TYPE_ENERGY_SOURCE = "tset.ref.classification-type.energy-source"
    const val MACHINE_CLASSIFICATION_TYPE_MACHINE_CLASS = "tset.ref.classification-type.machine"
    const val MACHINE_CLASSIFICATION_TYPE_CONSUMPTION = "tset.ref.classification-type.consumption"

    private fun createHeader(
        masterDataType: MachineMasterDataType,
        originalKey: String,
        originalType: String?,
        displayDesignationField: FieldParameter?,
        designationField: FieldParameter?,
        baseCurrencyField: FieldParameter?,
        fields: List<FieldParameter>,
    ): HeaderDto? {
        val key = originalType?.let { "$originalKey-$originalType" } ?: originalKey

        // Determine energy source classification based on machine data
        val energySourceClassification = determineEnergySourceClassification(
            fields.find { it.name == "connectedLoad" },
            fields.find { it.name == "gasConsumptionMeltingPerHour" },
            fields.find { it.name == "gasConsumptionKeepWarmPerHour" }
        )

        val classifications = mutableMapOf<SimpleKeyDto, List<SimpleKeyDto>>()

        // Add energy source classification if determined
        energySourceClassification?.let { energySource ->
            classifications[SimpleKeyDto(MACHINE_CLASSIFICATION_TYPE_ENERGY_SOURCE)] = listOf(SimpleKeyDto(energySource))
        }

        // TODO: Add machine class and consumption classifications when their mappings are provided

        // Create classification field values based on selected classification types
        val classificationFieldValues = createClassificationFieldValues(
            fields,
            key,
            masterDataType,
            energySourceClassification
        )

        val currencyIsoCode = baseCurrencyField?.value?.toString()
        if (currencyIsoCode == null) {
            println("Cannot create machine $key because no currency is available.")
            return null
        }

        val detailValueSchema = createMachineDetailValueSchema()

        return HeaderDto(
            key = SimpleKeyDto(key),
            name = displayDesignationField?.value?.toString() ?: designationField?.value?.toString() ?: key,
            headerTypeKey = SimpleKeyDto(MACHINE_HEADER_TYPE),
            active = true,
            detailValueSchema = detailValueSchema,
            classifications = classifications.toMap(),
            classificationFieldValues = classificationFieldValues,
        )
    }

    private fun createMachineDetailValueSchema(): ValueTypeDetailValueSchemaDto {
        return ValueTypeDetailValueSchemaDto(
            detailValueTypeMapping = mapOf(
                SimpleKeyDto(DETAIL_VALUE_INVEST_BASE_KEY) to
                    DetailValueTypeDto(
                        key = SimpleKeyDto(DETAIL_VALUE_INVEST_BASE_KEY),
                        name = "Base investment",
                        index = 0,
                        detailValueSchema = NumericFieldSchemaDto(
                            unitOfMeasurement = UnitOfMeasurementTypeDto(
                                numerator = AnyCurrencyTypeDto(SimpleKeyDto("EUR")),
                                denominator = UnitTypeDto(SimpleKeyDto("tset.unit.piece.piece")),
                            ),
                        ),
                    ),
                SimpleKeyDto(DETAIL_VALUE_INVEST_FUNDAMENT_KEY) to
                    DetailValueTypeDto(
                        key = SimpleKeyDto(DETAIL_VALUE_INVEST_FUNDAMENT_KEY),
                        name = "Foundation investment",
                        index = 1,
                        detailValueSchema = NumericFieldSchemaDto(
                            unitOfMeasurement = UnitOfMeasurementTypeDto(
                                numerator = AnyCurrencyTypeDto(SimpleKeyDto("EUR")),
                                denominator = UnitTypeDto(SimpleKeyDto("tset.unit.piece.piece")),
                            ),
                        ),
                    ),
                SimpleKeyDto(DETAIL_VALUE_INVEST_MISC_KEY) to
                    DetailValueTypeDto(
                        key = SimpleKeyDto(DETAIL_VALUE_INVEST_MISC_KEY),
                        name = "Other investment",
                        index = 2,
                        detailValueSchema = NumericFieldSchemaDto(
                            unitOfMeasurement = UnitOfMeasurementTypeDto(
                                numerator = AnyCurrencyTypeDto(SimpleKeyDto("EUR")),
                                denominator = UnitTypeDto(SimpleKeyDto("tset.unit.piece.piece")),
                            ),
                        ),
                    ),
                SimpleKeyDto(DETAIL_VALUE_INVEST_SETUP_KEY) to
                    DetailValueTypeDto(
                        key = SimpleKeyDto(DETAIL_VALUE_INVEST_SETUP_KEY),
                        name = "Setup investment",
                        index = 3,
                        detailValueSchema = NumericFieldSchemaDto(
                            unitOfMeasurement = UnitOfMeasurementTypeDto(
                                numerator = AnyCurrencyTypeDto(SimpleKeyDto("EUR")),
                                denominator = UnitTypeDto(SimpleKeyDto("tset.unit.piece.piece")),
                            ),
                        ),
                    ),
                SimpleKeyDto(DETAIL_VALUE_EMISSION_KEY) to
                    DetailValueTypeDto(
                        key = SimpleKeyDto(DETAIL_VALUE_EMISSION_KEY),
                        name = "Emission",
                        index = 4,
                        detailValueSchema = NumericFieldSchemaDto(
                            unitOfMeasurement = UnitOfMeasurementTypeDto(
                                numerator = UnitTypeDto(SimpleKeyDto("tset.unit.emission.kilogram_co2e")),
                                denominator = UnitTypeDto(SimpleKeyDto("tset.unit.piece.piece")),
                            ),
                        ),
                    ),
            ),
        )
    }

    private fun determineEnergySourceClassification(
        connectedLoadField: FieldParameter?,
        gasConsumptionMeltingField: FieldParameter?,
        gasConsumptionKeepWarmField: FieldParameter?,
    ): String? {
        // Check if machine has gas consumption
        val hasGasConsumption = (gasConsumptionMeltingField?.value as? Number)?.toDouble()?.let { it > 0 } == true ||
                               (gasConsumptionKeepWarmField?.value as? Number)?.toDouble()?.let { it > 0 } == true

        // Check if machine has electrical load
        val hasElectricalLoad = (connectedLoadField?.value as? Number)?.toDouble()?.let { it > 0 } == true

        return when {
            hasGasConsumption && hasElectricalLoad -> {
                // If both gas and electricity, prioritize gas as primary energy source
                // This logic can be adjusted based on business requirements
                "tset.ref.classification.energy-source.gas"
            }
            hasGasConsumption -> "tset.ref.classification.energy-source.gas"
            hasElectricalLoad -> "tset.ref.classification.energy-source.electricity"
            else -> {
                // Default to electricity if no clear indication
                // This can be adjusted based on business requirements
                "tset.ref.classification.energy-source.electricity"
            }
        }
    }

    private fun createClassificationFieldValues(
        fields: List<FieldParameter>,
        key: String,
        masterDataType: MachineMasterDataType,
        energySourceClassification: String?,
    ): Map<@Valid SimpleKeyDto, @Valid FieldValueDto> {
        val fieldValues = mutableMapOf<SimpleKeyDto, FieldValueDto>()

        // Add energy source specific fields based on classification
        energySourceClassification?.let { energySource ->
            when (energySource) {
                "tset.ref.classification.energy-source.electricity" -> {
                    // Add electricity-specific fields
                    fields.find { it.name == "powerOnTimeRate" }?.value?.let { value ->
                        fieldValues[SimpleKeyDto("tset.ref.field.machine.electricityPowerOnTimeRate")] =
                            NumericValueDto(
                                value = (value as? Number)?.toDouble() ?: 0.0,
                                numerator = UnitMeasurementDto(SimpleKeyDto("tset.unit.rate.factor_one")),
                            )
                    }

                    // Add connected load field for electricity
                    fields.find { it.name == "connectedLoad" }?.value?.let { value ->
                        fieldValues[SimpleKeyDto("tset.ref.field.machine.electricityConnectedLoad")] =
                            NumericValueDto(
                                value = (value as? Number)?.toDouble() ?: 0.0,
                                numerator = UnitMeasurementDto(SimpleKeyDto("tset.unit.power.watt")),
                            )
                    }
                }
                "tset.ref.classification.energy-source.gas" -> {
                    // Add gas power on time rate
                    fields.find { it.name == "powerOnTimeRate" }?.value?.let { value ->
                        fieldValues[SimpleKeyDto("tset.ref.field.machine.gasPowerOnTimeRate")] =
                            NumericValueDto(
                                value = (value as? Number)?.toDouble() ?: 0.0,
                                numerator = UnitMeasurementDto(SimpleKeyDto("tset.unit.rate.percentage")),
                            )
                    }

                    // Add gas connected load (convert from watts to kilowatts)
                    fields.find { it.name == "connectedLoad" }?.value?.let { value ->
                        val valueInWatts = (value as? Number)?.toDouble() ?: 0.0
                        val valueInKilowatts = valueInWatts / 1000.0 // Convert watts to kilowatts
                        fieldValues[SimpleKeyDto("tset.ref.field.machine.gasConnectedLoad")] =
                            NumericValueDto(
                                value = valueInKilowatts,
                                numerator = UnitMeasurementDto(SimpleKeyDto("tset.unit.power.kilowatt")),
                            )
                    }

                    // Add gas consumption melting per hour (convert from cubic cm to cubic meters)
                    fields.find { it.name == "gasConsumptionMeltingPerHour" }?.value?.let { value ->
                        val valueInCubicCm = (value as? Number)?.toDouble() ?: 0.0
                        val valueInCubicMeters = valueInCubicCm / 1_000_000.0 // Convert cm³ to m³
                        fieldValues[SimpleKeyDto("tset.ref.field.machine.gasConsumptionMeltingPerHour")] =
                            NumericValueDto(
                                value = valueInCubicMeters,
                                numerator = UnitMeasurementDto(SimpleKeyDto("tset.unit.volume.cubic_meter")),
                            )
                    }

                    // Add gas consumption keep warm per hour (convert from cubic cm to cubic meters)
                    fields.find { it.name == "gasConsumptionKeepWarmPerHour" }?.value?.let { value ->
                        val valueInCubicCm = (value as? Number)?.toDouble() ?: 0.0
                        val valueInCubicMeters = valueInCubicCm / 1_000_000.0 // Convert cm³ to m³
                        fieldValues[SimpleKeyDto("tset.ref.field.machine.gasConsumptionKeepWarmPerHour")] =
                            NumericValueDto(
                                value = valueInCubicMeters,
                                numerator = UnitMeasurementDto(SimpleKeyDto("tset.unit.volume.cubic_meter")),
                            )
                    }

                    // Add oxygen consumption per hour
                    fields.find { it.name == "oxigenConsumptionPerHour" }?.value?.let { value ->
                        fieldValues[SimpleKeyDto("tset.ref.field.machine.oxygenConsumptionPerHour")] =
                            NumericValueDto(
                                value = (value as? Number)?.toDouble() ?: 0.0,
                                numerator = UnitMeasurementDto(SimpleKeyDto("tset.unit.weight.kilogram")),
                                denominator = UnitMeasurementDto(SimpleKeyDto("tset.unit.time.hour")),
                            )
                    }

                    // Add cast excipients consumption per hour
                    fields.find { it.name == "castExcipientsConsumptionPerHour" }?.value?.let { value ->
                        fieldValues[SimpleKeyDto("tset.ref.field.machine.castExcipientsConsumptionPerHour")] =
                            NumericValueDto(
                                value = (value as? Number)?.toDouble() ?: 0.0,
                                numerator = UnitMeasurementDto(SimpleKeyDto("tset.unit.weight.kilogram")),
                                denominator = UnitMeasurementDto(SimpleKeyDto("tset.unit.time.hour")),
                            )
                    }
                }

                else -> {
                    println("Unsupported energy source: $energySource")
                }
            }
        }

        // TODO: Add other classification field mappings when machine class and consumption types are provided

        return fieldValues
    }

    private fun createInvestmentAndEmissionDetails(
        headerKey: String,
        investBaseField: FieldParameter?,
        investFundamentField: FieldParameter?,
        investMiscField: FieldParameter?,
        investSetupField: FieldParameter?,
        // emissionField: FieldParameter?, // TODO: Add when emission mapping is implemented
        currency: FieldParameter,
        representsAccountData: Boolean,
    ): List<DetailDto> {
        val details = mutableListOf<DetailDto>()

        // Create investment details
        investBaseField?.let { field ->
            details.add(createInvestmentDetail(headerKey, DETAIL_VALUE_INVEST_BASE_KEY, field, currency, representsAccountData))
        }

        investFundamentField?.let { field ->
            details.add(createInvestmentDetail(headerKey, DETAIL_VALUE_INVEST_FUNDAMENT_KEY, field, currency, representsAccountData))
        }

        investMiscField?.let { field ->
            details.add(createInvestmentDetail(headerKey, DETAIL_VALUE_INVEST_MISC_KEY, field, currency, representsAccountData))
        }

        investSetupField?.let { field ->
            details.add(createInvestmentDetail(headerKey, DETAIL_VALUE_INVEST_SETUP_KEY, field, currency, representsAccountData))
        }

        // TODO: Create emission detail when emission mapping is implemented
        // emissionField?.let { field ->
        //     details.add(createEmissionDetail(headerKey, field, representsAccountData))
        // }

        return details
    }

    private fun createInvestmentDetail(
        headerKey: String,
        detailValueTypeKey: String,
        investmentField: FieldParameter,
        currency: FieldParameter,
        representsAccountData: Boolean,
    ): DetailDto {
        val currencyKey = currency.value?.toString() ?: "EUR"

        return DetailDto(
            effectivities = mapOf(
                SimpleKeyDto("tset.data-source-field") to
                    if (representsAccountData) {
                        LovValueDto(SimpleKeyDto("tset.customer"))
                    } else {
                        LovValueDto(SimpleKeyDto("tset.tset"))
                    }
            ),
            headerKey = SimpleKeyDto(headerKey),
            value = NumericDetailValueDto(
                value = (investmentField.value as? Number)?.toDouble() ?: 0.0,
                numerator = CurrencyMeasurementDto(SimpleKeyDto(currencyKey)),
                denominator = UnitMeasurementDto(SimpleKeyDto("tset.unit.piece.piece")),
            ),
            active = true,
            detailValueTypeKey = SimpleKeyDto(detailValueTypeKey),
        )
    }
}
