package com.tset.validator.execution.material

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonMapperBuilder
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.nu.bom.core.publicapi.dtos.FieldParameter
import com.nu.masterdata.dto.v1.detail.*
import com.nu.masterdata.dto.v1.detail.table.BuiltinLovFilterDto
import com.nu.masterdata.dto.v1.detail.table.DetailQueryDto
import com.nu.masterdata.dto.v1.detail.table.DetailQueryResponseDto
import com.nu.masterdata.dto.v1.detail.table.HeaderAndDetailDto
import com.nu.masterdata.dto.v1.header.HeaderBulkResponseDto
import com.nu.masterdata.dto.v1.header.HeaderBulkResponseErrorDto
import com.nu.masterdata.dto.v1.header.HeaderDto
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import com.tset.validator.AccountReference
import com.tset.validator.MasterdataMigrationResult
import com.tset.validator.config.MasterdataMigrationConfig
import com.tset.validator.config.MasterdataType
import com.tset.validator.execution.*
import com.tset.validator.timing.TimingData
import com.tset.validator.timing.TimingType
import com.tset.validator.utils.DurationSerializable
import com.tset.validator.utils.serializable
import com.tset.validator.webclient.ErrorResponse
import com.tset.validator.webclient.HttpMethod
import com.tset.validator.webclient.PublicApiService
import com.tset.validator.webclient.ServiceType
import java.io.File
import kotlin.time.Duration

enum class MaterialMasterDataType {
    RAW_MATERIAL_CASTING_ALLOY,
    RAW_MATERIAL_INGOT,
    RAW_MATERIAL_SHEET,
    RAW_MATERIAL_LAMELLA,
    RAW_MATERIAL_PLASTIC_GRANULATE,
    RAW_MATERIAL_RUBBER,
    RAW_MATERIAL_SAND,
    RAW_MATERIAL_PIPE,
    RAW_MATERIAL_WIRE_ROD,
    RAW_MATERIAL_BAR,
    RAW_MATERIAL_POWDER,
    RAW_MATERIAL_PCB,
    RAW_MATERIAL_WAX,
    RAW_MATERIAL_RARE_EARTH,
    RAW_MATERIAL_PAINT,
    RAW_MATERIAL_METALLIC_COATING,
    RAW_MATERIAL_COATING_PCBA,
    RAW_MATERIAL_MANUAL,
    RAW_MATERIAL_COIL,
    RAW_MATERIAL_PAPER_COIL,
    RAW_MATERIAL_PAPER_SHEET,
    RAW_MATERIAL_INK,
    RAW_MATERIAL_VARNISH,
}

const val MATERIAL_HEADER_TYPE = "tset.ref.header-type.material"
const val DETAIL_VALUE_PRICE_KEY = "price"
const val DETAIL_VALUE_EMISSION_KEY = "emission"

const val ONLY_ACCOUNT_DATA = true
const val READ_DATA_AND_WRITE_TO_FILE = false
const val READ_DATA_FROM_FILE_AND_POST = false

class MasterdataMaterialMigration(
    private val client: PublicApiService,
    private val config: MasterdataMigrationConfig,
) : MasterdataMigration {
    private val objectMapper: ObjectMapper

    init {
        require(config.masterdataType == MasterdataType.MATERIALS) {
            "invalid masterdata migration type ${config.masterdataType}, expected `${MasterdataType.MATERIALS}`"
        }
        objectMapper =
            jacksonMapperBuilder()
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .findAndAddModules()
                .build()
    }

    override suspend fun executeMigrationForAccount(accountReference: AccountReference): MasterdataMigrationResult =
        try {
            doWork(accountReference)
        } catch (ex: MasterdataMigrationError) {
            println("error during masterdata migration of account ${accountReference.accountName}: $ex")
            MasterdataMigrationResult(
                TimingData(TimingType.Error, DurationSerializable.fromDuration(Duration.ZERO)),
                accountReference,
                listOf(ErrorResponse.ParseError(ex.message ?: "MasterdataMigrationError", "")),
            )
        }

    private suspend fun doWork(accountReference: AccountReference): MasterdataMigrationResult {
        val accountName = accountReference.accountName
        println("migrate materials of account $accountName to Masterdata")


        FieldBuilder.createTextFieldAndAddToRootClassification(
            fieldKey = ITEM_NUMBER_FIELD_KEY,
            fieldName = "Item number",
            client = client,
            accountName = accountName,
            requestHeaders = getMdRequestHeaders(accountName),
            objectMapper = objectMapper
        )

        val queryDto =
            DetailQueryDto(
                filters =
                    mapOf(
                        SimpleKeyDto("_BUILTIN_modifier") to
                            listOf(
                                BuiltinLovFilterDto(
                                    equals = SimpleKeyDto("tsetLegacyMigrator"),
                                ),
                            ),
                    ),
                showStateOf = null,
                showInactive = false,
                sortOrder = null,
            )
        val strRespNbk =
            MaterialMasterDataType.entries.map { entry ->
                entry to
                    client.callAnyEndpoint(
                        accountName,
                        HttpMethod.GET,
                        "api/masterdata/md-migration/type/$entry/account-entities?searchMode=ACCOUNT_ONLY",
                        false,
                        ServiceType.NuBomKotlin,
                    )
            }

        val strRespNbkGlobal =
            MaterialMasterDataType.entries.map { entry ->
                entry to
                    client.callAnyEndpoint(
                        accountName,
                        HttpMethod.GET,
                        "api/masterdata/md-migration/type/$entry/account-entities?searchMode=GLOBAL_ONLY",
                        false,
                        ServiceType.NuBomKotlin,
                    )
            }
        val strResponseMd =
            client.callAnyEndpoint(
                accountName,
                HttpMethod.POST,
                "api/md/v1/headertypes/$MATERIAL_HEADER_TYPE/details/search?size=200000",
                false,
                ServiceType.MasterData,
                body = objectMapper.writeValueAsString(queryDto),
                headers = getMdRequestHeaders(accountName),
            )

        strRespNbk.forEach {
            if (!it.second.res().ok()) {
                println("error fetching account data from NBK for master data type ${it.first} ${it.second.res().unwrapError()}")
            }
        }

        strRespNbkGlobal.forEach {
            if (!it.second.res().ok()) {
                println("error fetching tset data from NBK for master data type ${it.first} ${it.second.res().unwrapError()}")
            }
        }

        if (!strResponseMd.res().ok()) {
            println("error fetching data from MD ${strResponseMd.res().unwrapError()}")
        }

        val fetchErrors =
            (
                strRespNbk.map { it.second } +
                    strRespNbkGlobal.map { it.second } +
                    strResponseMd
            ).mapNotNull {
                if (!it.res().ok()) {
                    it.res().error
                } else {
                    null
                }
            }

//        val fetchError =
//            if (!strRespNbk.res().ok()) {
//                strRespNbk.res().unwrapError()
//            } else if (!strRespNbkGlobal.res().ok()) {
//                strRespNbkGlobal.res().unwrapError()
//            } else if (!strResponseMd.res().ok()) {
//                strResponseMd.res().unwrapError()
//            } else {
//                null
//            }
        val headersAndDetailsToPost =
            if (fetchErrors.isEmpty()) {
                val nbkMdDetails: List<MdMigrationMasterDataDetailDto> =
                    strRespNbk
                        .map { resp ->
                            objectMapper.readValue<List<MdMigrationMasterDataDetailDto>?>(
                                resp.second
                                    .res()
                                    .unwrapSuccess(),
                                jacksonTypeRef(),
                            )
                        }.flatten()

                val nbkMdDetailsGlobal: List<MdMigrationMasterDataDetailDto> =
                    strRespNbkGlobal
                        .map { resp ->
                            objectMapper
                                .readValue<List<MdMigrationMasterDataDetailDto>?>(
                                    resp.second
                                        .res()
                                        .unwrapSuccess(),
                                    jacksonTypeRef(),
                                ).map { it.copy(representsAccountData = false) }
                        }.flatten()

                val allNbkDetails =
                    if (ONLY_ACCOUNT_DATA) {
                        nbkMdDetails
                    } else {
                        nbkMdDetailsGlobal + nbkMdDetails
                    }

                if (READ_DATA_AND_WRITE_TO_FILE) {
                    // create json data file
                    val dataAsString = objectMapper.writeValueAsString(allNbkDetails)
                    File("ref-material.json").writeText(dataAsString)
                    // stop here and switch to target branch
                    return MasterdataMigrationResult(
                        TimingData(TimingType.Fetch, Duration.INFINITE.serializable()),
                        accountReference,
                        emptyList(),
                    )
                }

                val allNbkDetailsToMigrate =
                    if (READ_DATA_FROM_FILE_AND_POST) {
                        // read json data file
                        val dataAsString = File("ref-material.json").readText()
                        objectMapper.readValue<List<MdMigrationMasterDataDetailDto>?>(
                            dataAsString,
                            jacksonTypeRef(),
                        )
                    } else {
                        allNbkDetails
                    }

                // println(allNbkDetails)
                val latestNbkDetails =
                    allNbkDetailsToMigrate
                        // .filter { it.key.lowercase().contains("AlSi10Mg (Fe)".lowercase()) }
                        .groupBy { it.key to it.type }
                        .mapValues { (_, group) -> group.maxBy { it.year } }
                        .values
                        .toList()

                ClassificationBuilder.createOtherClassification(
                    nbkMaterials = allNbkDetailsToMigrate,
                    client = client,
                    accountName = accountName,
                    requestHeaders = getMdRequestHeaders(accountName),
                    objectMapper = objectMapper,
                )

                // println(strResponseMd.res().unwrapSuccess())
                val existingMd: DetailQueryResponseDto =
                    objectMapper.readValue(
                        strResponseMd.res().unwrapSuccess(),
                        jacksonTypeRef(),
                    )

                createHeaderAndDetailsToPost(latestNbkDetails, existingMd.content)
            } else {
                null
            }

        val headersToPost = headersAndDetailsToPost?.first ?: emptyList()
        val headerPostResponse =
            if (headersToPost.isNotEmpty()) {
                val strheadersToPost = objectMapper.writeValueAsString(headersToPost)
                println("POST-Headers (${headersToPost.size} entries):")
                // println(strDetailsToPost)
                client.callAnyEndpoint(
                    accountName,
                    HttpMethod.POST,
                    "api/md/v1/headertypes/$MATERIAL_HEADER_TYPE/headers",
                    false,
                    ServiceType.MasterData,
                    body = strheadersToPost,
                    headers = getMdRequestHeaders(accountName),
                )
            } else {
                println("no headers need to update anything in MD")
                null
            }

        val headerPostError =
            if (headerPostResponse?.res()?.ok() == false) {
                val bulkErrors: List<HeaderBulkResponseErrorDto> =
                    objectMapper
                        .readValue<List<HeaderBulkResponseDto>?>(
                            (headerPostResponse.res().unwrapError() as ErrorResponse.UnknownErrorResponse).content,
                            jacksonTypeRef(),
                        ).filterIsInstance<HeaderBulkResponseErrorDto>()
                val bulkErrorsAsString = objectMapper.writeValueAsString(bulkErrors)
                println("error putting data into MD $bulkErrorsAsString")
                File("c:\\tmp\\header-error.json").writeText(bulkErrorsAsString)
                headerPostResponse.res().unwrapError()
            } else {
                null
            }
        val detailsToPost = headersAndDetailsToPost?.second ?: emptyList()
        val detailPostResponse =
            if (detailsToPost.isNotEmpty()) {
                val strDetailsToPost = objectMapper.writeValueAsString(detailsToPost)
                println("POST-Details (${detailsToPost.size} entries):")
                // println(strDetailsToPost)
                client.callAnyEndpoint(
                    accountName,
                    HttpMethod.POST,
                    "api/md/v1/headertypes/$MATERIAL_HEADER_TYPE/details",
                    false,
                    ServiceType.MasterData,
                    body = strDetailsToPost,
                    headers = getMdRequestHeaders(accountName),
                )
            } else {
                println("no details need to update anything in MD")
                null
            }
        val detailPostError =
            if (detailPostResponse?.res()?.ok() == false) {
                val bulkErrors: List<DetailBulkResponseErrorDto> =
                    objectMapper
                        .readValue<List<DetailBulkResponseDto>?>(
                            (detailPostResponse.res().unwrapError() as ErrorResponse.UnknownErrorResponse).content,
                            jacksonTypeRef(),
                        ).filterIsInstance<DetailBulkResponseErrorDto>()
                println("error putting data into MD $bulkErrors")
                detailPostResponse.res().unwrapError()
            } else {
                null
            }

        val allErrors = listOfNotNull(headerPostError) + listOfNotNull(detailPostError) + fetchErrors
        println(objectMapper.writeValueAsString(allErrors))

        val timingType =
            if (allErrors.isEmpty()) {
                TimingType.Unspecified
            } else {
                TimingType.Error
            }
        return MasterdataMigrationResult(
            TimingData(timingType, Duration.INFINITE.serializable()),
            accountReference,
            allErrors,
        )
    }

    private fun getMdRequestHeaders(accountName: String): Map<String, String> =
        mapOf(
            HEADER_USER to MIGRATION_USERNAME,
            HEADER_ACCOUNT to accountName,
            "Content-Type" to "application/json",
        )

    private fun shouldIgnoreEffectivity(effectivity: Map.Entry<SimpleKeyDto, FieldValueDto?>): Boolean = (effectivity.value == null)

    private fun isUser(detail: DetailDto): Boolean {
        val datasource = detail.effectivities[SimpleKeyDto("tset.data-source-field")]
        return datasource is LovValueDto && datasource.value.key == "tset.customer"
    }

    private fun isSameValue(
        detailA: DetailDto,
        detailB: DetailDto,
    ): Boolean {
        if (detailA.value is NumericDetailValueDto && detailB.value is NumericDetailValueDto) {
            val a = (detailA.value as NumericDetailValueDto).value
            val b = (detailB.value as NumericDetailValueDto).value
            return a.isCloseTo(b)
        }
        return detailA.value == detailB.value
    }

    private fun shouldUpdateDetailInMd(
        valueInNbk: DetailDto,
        existingMd: List<DetailDto>,
    ): Boolean {
        val matchingInMd =
            existingMd
                .filter { it.headerKey == valueInNbk.headerKey }
                .filter { existingInMd ->
                    val filteredEffectivities =
                        existingInMd.effectivities
                            .filter {
                                !shouldIgnoreEffectivity(it)
                            }
                    filteredEffectivities
                        .map {
                            it.key to
                                when (val v = it.value) {
                                    null -> null
                                    is ClassificationValueDto -> ClassificationValueDto(v.value)
                                    is DateValueDto -> DateValueDto(v.value)
                                    is LovValueDto -> LovValueDto(v.value)
                                    is NumericValueDto -> NumericValueDto(v.value, v.numerator)
                                    else -> throw IllegalStateException("not supported field value type: $v")
                                }
                        }.toMap() == valueInNbk.effectivities &&
                        existingInMd.detailValueTypeKey!!.key == valueInNbk.detailValueTypeKey!!.key
                }
        val userEntryInMd = matchingInMd.firstOrNull { isUser(it) }
        val tsetEntryInMd = matchingInMd.firstOrNull { !isUser(it) }
        if (userEntryInMd != null) {
            return !isSameValue(userEntryInMd, valueInNbk)
        }
        if (tsetEntryInMd != null) {
            return !isSameValue(tsetEntryInMd, valueInNbk)
        }
        return true
    }

    private fun createHeaderAndDetailsToPost(
        nbkMdDetails: List<MdMigrationMasterDataDetailDto>,
        existingMd: List<HeaderAndDetailDto>,
    ): Pair<List<HeaderDto>, List<DetailDto>> {
        println("entries in nbk: ${nbkMdDetails.size}, entries in md: ${existingMd.size}")
        if (nbkMdDetails.isEmpty()) {
            return emptyList<HeaderDto>() to emptyList<DetailDto>()
        }

//        val singleDetails = nbkMdDetails
//            .groupBy {it.type}
//            .map { it.value.first() }

        val newMaterials =
            nbkMdDetails
                .mapNotNull { nbkMaterial ->
                    when (MaterialMasterDataType.valueOf(nbkMaterial.type)) {
                        MaterialMasterDataType.RAW_MATERIAL_CASTING_ALLOY ->
                            MaterialDetailBuilder.createRawMaterialCastingAlloy( // fails with Index 0 out of bounds for length 0
                                nbkMaterial,
                                MaterialMasterDataType.RAW_MATERIAL_CASTING_ALLOY,
                            )
                        MaterialMasterDataType.RAW_MATERIAL_INGOT ->
                            MaterialDetailBuilder.createRawMaterialIngot(
                                nbkMaterial,
                                MaterialMasterDataType.RAW_MATERIAL_INGOT,
                            )
                        MaterialMasterDataType.RAW_MATERIAL_SHEET ->
                            MaterialDetailBuilder.createRawMaterialSheet(
                                nbkMaterial,
                                MaterialMasterDataType.RAW_MATERIAL_SHEET,
                            )
                        MaterialMasterDataType.RAW_MATERIAL_LAMELLA ->
                            MaterialDetailBuilder.createRawMaterialLamella(
                                nbkMaterial,
                                MaterialMasterDataType.RAW_MATERIAL_LAMELLA,
                            )
                        MaterialMasterDataType.RAW_MATERIAL_PLASTIC_GRANULATE ->
                            MaterialDetailBuilder.createRawMaterialPlasticGranulate(
                                nbkMaterial,
                                MaterialMasterDataType.RAW_MATERIAL_PLASTIC_GRANULATE,
                            )
                        MaterialMasterDataType.RAW_MATERIAL_RUBBER ->
                            MaterialDetailBuilder.createRawMaterialRubber(
                                nbkMaterial,
                                MaterialMasterDataType.RAW_MATERIAL_RUBBER,
                            )
                        MaterialMasterDataType.RAW_MATERIAL_SAND ->
                            MaterialDetailBuilder.createRawMaterialSand(
                                nbkMaterial,
                                MaterialMasterDataType.RAW_MATERIAL_SAND,
                            )
                        MaterialMasterDataType.RAW_MATERIAL_PIPE ->
                            MaterialDetailBuilder.createRawMaterialPipe(
                                nbkMaterial,
                                MaterialMasterDataType.RAW_MATERIAL_PIPE,
                            )
                        MaterialMasterDataType.RAW_MATERIAL_WIRE_ROD ->
                            MaterialDetailBuilder.createRawMaterialWireRod(
                                nbkMaterial,
                                MaterialMasterDataType.RAW_MATERIAL_WIRE_ROD,
                            )
                        MaterialMasterDataType.RAW_MATERIAL_BAR ->
                            MaterialDetailBuilder.createRawMaterialBar(
                                nbkMaterial,
                                MaterialMasterDataType.RAW_MATERIAL_BAR,
                            )
                        MaterialMasterDataType.RAW_MATERIAL_POWDER ->
                            MaterialDetailBuilder.createRawMaterialPowder(
                                nbkMaterial,
                                MaterialMasterDataType.RAW_MATERIAL_POWDER,
                            )
                        // this typ should be deleted
                        MaterialMasterDataType.RAW_MATERIAL_PCB -> null //  MaterialDetailBuilder.createRawMaterialPCB(nbkMaterial)
                        MaterialMasterDataType.RAW_MATERIAL_WAX ->
                            MaterialDetailBuilder.createRawMaterialWax(
                                nbkMaterial,
                                MaterialMasterDataType.RAW_MATERIAL_WAX,
                            )
                        MaterialMasterDataType.RAW_MATERIAL_RARE_EARTH ->
                            MaterialDetailBuilder.createRawMaterialRareEarth(
                                nbkMaterial,
                                MaterialMasterDataType.RAW_MATERIAL_RARE_EARTH,
                            )
                        MaterialMasterDataType.RAW_MATERIAL_PAINT ->
                            MaterialDetailBuilder.createRawMaterialPaint(
                                nbkMaterial,
                                MaterialMasterDataType.RAW_MATERIAL_PAINT,
                            )
                        MaterialMasterDataType.RAW_MATERIAL_METALLIC_COATING ->
                            MaterialDetailBuilder.createRawMaterialMetallicCoating(
                                nbkMaterial,
                                MaterialMasterDataType.RAW_MATERIAL_METALLIC_COATING,
                            )
                        MaterialMasterDataType.RAW_MATERIAL_COATING_PCBA ->
                            MaterialDetailBuilder.createRawMaterialCoatingPCBA(
                                nbkMaterial,
                                MaterialMasterDataType.RAW_MATERIAL_COATING_PCBA,
                            )
                        MaterialMasterDataType.RAW_MATERIAL_MANUAL ->
                            MaterialDetailBuilder.createRawMaterialManual(
                                nbkMaterial,
                                MaterialMasterDataType.RAW_MATERIAL_MANUAL,
                            )
                        MaterialMasterDataType.RAW_MATERIAL_COIL ->
                            MaterialDetailBuilder.createRawMaterialCoil(
                                nbkMaterial,
                                MaterialMasterDataType.RAW_MATERIAL_COIL,
                            )
                        MaterialMasterDataType.RAW_MATERIAL_PAPER_COIL ->
                            MaterialDetailBuilder.createRawMaterialPaperCoil(
                                nbkMaterial,
                                MaterialMasterDataType.RAW_MATERIAL_PAPER_COIL,
                            )
                        MaterialMasterDataType.RAW_MATERIAL_PAPER_SHEET ->
                            MaterialDetailBuilder.createRawMaterialPaperSheet(
                                nbkMaterial,
                                MaterialMasterDataType.RAW_MATERIAL_PAPER_SHEET,
                            )
                        MaterialMasterDataType.RAW_MATERIAL_INK ->
                            MaterialDetailBuilder.createRawMaterialBase(
                                nbkMaterial,
                                MaterialMasterDataType.RAW_MATERIAL_INK,
                            )
                        MaterialMasterDataType.RAW_MATERIAL_VARNISH ->
                            MaterialDetailBuilder.createRawMaterialBase(
                                nbkMaterial,
                                MaterialMasterDataType.RAW_MATERIAL_VARNISH,
                            )
                        else -> null
                    }
                }

        val headers = newMaterials.map { it.first }
        val details =
            newMaterials
                .map { it.second }
                .flatten()
                .filter {
                    shouldUpdateDetailInMd(it, existingMd.mapNotNull { hd -> hd.detailDto })
                }

        return headers to details
    }
}

object MaterialMapper {
    val manufacturersLovEntries = mutableMapOf<String, String>()

    val lovTypeMap =
        mapOf(
            "tset.ref.lov-type.materialGroup" to
                mapOf(
                    "P1" to "tset.ref.lov-entry.p1",
                    "P2" to "tset.ref.lov-entry.p2",
                    "P3" to "tset.ref.lov-entry.p3",
                    "P4" to "tset.ref.lov-entry.p4",
                    "P5" to "tset.ref.lov-entry.p5",
                    "P6" to "tset.ref.lov-entry.p6",
                    "P7" to "tset.ref.lov-entry.p7",
                    "P8" to "tset.ref.lov-entry.p8",
                    "P9" to "tset.ref.lov-entry.p9",
                    "P10" to "tset.ref.lov-entry.p10",
                    "P11" to "tset.ref.lov-entry.p11",
                    "P12" to "tset.ref.lov-entry.p12",
                    "P13" to "tset.ref.lov-entry.p13",
                    "P14" to "tset.ref.lov-entry.p14",
                    "P15" to "tset.ref.lov-entry.p15",
                    "M1" to "tset.ref.lov-entry.m1",
                    "M2" to "tset.ref.lov-entry.m2",
                    "M3" to "tset.ref.lov-entry.m3",
                    "K1" to "tset.ref.lov-entry.k1",
                    "K2" to "tset.ref.lov-entry.k2",
                    "K3" to "tset.ref.lov-entry.k3",
                    "K4" to "tset.ref.lov-entry.k4",
                    "K5" to "tset.ref.lov-entry.k5",
                    "K6" to "tset.ref.lov-entry.k6",
                    "K7" to "tset.ref.lov-entry.k7",
                    "N1" to "tset.ref.lov-entry.n1",
                    "N2" to "tset.ref.lov-entry.n2",
                    "N3" to "tset.ref.lov-entry.n3",
                    "N4" to "tset.ref.lov-entry.n4",
                    "N5" to "tset.ref.lov-entry.n5",
                    "N6" to "tset.ref.lov-entry.n6",
                    "N7" to "tset.ref.lov-entry.n7",
                    "N8" to "tset.ref.lov-entry.n8",
                    "N9" to "tset.ref.lov-entry.n9",
                    "N10" to "tset.ref.lov-entry.n10",
                    "S5" to "tset.ref.lov-entry.s5",
                    "H1" to "tset.ref.lov-entry.h1",
                    "H2" to "tset.ref.lov-entry.h2",
                    "H3" to "tset.ref.lov-entry.h3",
                ),
            "tset.ref.lov-type.materialGroupQuenched" to
                mapOf(
                    "P3" to "tset.ref.lov-entry.quenched.p3",
                    "P5" to "tset.ref.lov-entry.quenched.p5",
                    "P8" to "tset.ref.lov-entry.quenched.p8",
                    "P9" to "tset.ref.lov-entry.quenched.p9",
                    "P10" to "tset.ref.lov-entry.quenched.p10",
                    "P12" to "tset.ref.lov-entry.quenched.p12",
                    "P13" to "tset.ref.lov-entry.quenched.p13",
                    "P15" to "tset.ref.lov-entry.quenched.p15",
                ),
            "tset.ref.lov-type.sheetMaterialGroup" to
                mapOf(
                    "StructuralSteel" to "tset.ref.lov-entry.structuralsteel",
                    "StructualSteel" to "tset.ref.lov-entry.structuralsteel",
                    "StainlessSteel" to "tset.ref.lov-entry.stainlesssteel",
                    "Copper" to "tset.ref.lov-entry.copper",
                    "Aluminium" to "tset.ref.lov-entry.aluminium",
                ),
            "tset.ref.lov-type.bool" to
                mapOf(
                    "true" to "tset.ref.lov-entry.true",
                    "false" to "tset.ref.lov-entry.false",
                ),
            //        "tset.ref.lov-type.preHeatTreatment" to mapOf(
//            "annealed" to "tset.ref.lov-entry.annealed",
//            "quenched and tempered" to "tset.ref.lov-entry.quenchedandtempered",
//            "softannealed" to "tset.ref.lov-entry.softannealed",
//            "none" to "tset.ref.lov-entry.none"
//        ),
            "tset.ref.lov-type.barProcess" to
                mapOf(
                    "ROLLED" to "tset.ref.lov-entry.rolled",
                    "FORGED" to "tset.ref.lov-entry.forged",
                ),
            "tset.ref.lov-type.fillerMaterial" to
                mapOf(
                    "No filler material" to "tset.ref.lov-entry.nofillermaterial",
                    "Glass fiber" to "tset.ref.lov-entry.glassfiber",
                    "Glass Fiber" to "tset.ref.lov-entry.glassfiber",
                    "Glass-Fiber" to "tset.ref.lov-entry.glassfiber",
                    "Mineral fiber" to "tset.ref.lov-entry.mineralfiber",
                    "Carbon fiber" to "tset.ref.lov-entry.carbonfiber",
                ),
            "tset.ref.lov-type.shrinkageBehavior" to
                mapOf(
                    "LOW_VOLUME_SHRINKAGE" to "tset.ref.lov-entry.lowvolumeshrinkage",
                    "AVERAGE_VOLUME_SHRINKAGE" to "tset.ref.lov-entry.averagevolumeshrinkage",
                    "HIGH_VOLUME_SHRINKAGE" to "tset.ref.lov-entry.highvolumeshrinkage",
                ),
            "tset.ref.lov-type.rollingType" to
                mapOf(
                    "COLD_ROLLED" to "tset.ref.lov-entry.cold_rolled",
                    "HOT_ROLLED" to "tset.ref.lov-entry.hot_rolled",
                ),
            "tset.ref.lov-type.rubberType" to
                mapOf(
                    "RUBBER" to "tset.ref.lov-entry.rubber",
                    "SILICONE" to "tset.ref.lov-entry.silicone",
                ),
            "tset.ref.lov-type.mounting-type" to
                mapOf(
                    "OTHER" to "tset.ref.lov-entry.other",
                    "PRESSFIT" to "tset.ref.lov-entry.pressfit",
                    "SMD" to "tset.ref.lov-entry.smd",
                    "THT" to "tset.ref.lov-entry.tht",
                ),
            "tset.ref.lov-type.elco.manufacturer" to manufacturersLovEntries,
            "tset.ref.lov-type.paperCategoryPaperSheet" to
                mapOf(
                    "OUTER_LINER" to "tset.ref.lov-entry.sheet.outerLiner",
                ),
            "tset.ref.lov-type.paperCategoryPaperCoil" to
                mapOf(
                    "INNER_LINER" to "tset.ref.lov-entry.innerLiner",
                    "CORRUGATED_FLUTE" to "tset.ref.lov-entry.corrugatedFlute",
                ),
            "tset.ref.lov-type.paintCoatType" to
                mapOf(
                    "BASE" to "tset.ref.lov-entry.base",
                    "CLEAR" to "tset.ref.lov-entry.clear",
                    "TOP" to "tset.ref.lov-entry.top",
                ),
        )

    fun setMigratedManufacturers(manufacturersNames: List<String>) {
        manufacturersNames.forEach {
            manufacturersLovEntries[it] = it
        }
    }

    fun getLovEntryKey(
        key: String,
        lovTypeKey: String,
        nbkLovEntry: String,
    ): String {
//        val entitiesWithWrongQuenchType =
//            listOf(
//                "C20C",
//                "C22",
//                "C45 - softannealed",
//                "C35E - softannealed",
//                "C20C - softannealed",
//                "C22 - softannealed",
//                "C15E - softannealed",
//                "C45-RAW_MATERIAL_PIPE"
//            )

        val effLovEntryKey =
            if (
                // (entitiesWithWrongQuenchType.contains(key)) &&
                lovTypeKey == "tset.ref.lov-type.materialGroupQuenched" &&
                (
                    nbkLovEntry == "P1" ||
                        nbkLovEntry == "P2"
                )
            ) {
                "P3"
            } else {
                nbkLovEntry
            }
        return requireNotNull(lovTypeMap[lovTypeKey]?.let { it[effLovEntryKey] }) {
            "could not find lov-entry mapping for nbk entry $nbkLovEntry of type $lovTypeKey for entity $key"
        }
    }

    data class MaterialClassificationField(
        val name: String,
        val type: String,
        val unit: String?,
        val csvUnitKey: String?,
        val mdKey: String,
        val lovTypeKey: String?,
    ) {
        val isLov = lovTypeKey != null
        val isNumeric = !isLov
    }

    private val materialClassificationFieldMap = MaterialClassificationFields.fields.associateBy { it.name }

    fun getMaterialClassificationFieldInfo(
        nbkFieldName: String,
        masterDataType: MaterialMasterDataType,
    ): MaterialClassificationField {
        if (nbkFieldName == "itemNumber") {
            return MaterialClassificationField(
                "itemNumber",
                "Text",
                null,
                null,
                ITEM_NUMBER_FIELD_KEY,
                null,
            )
        }

        if (nbkFieldName == "materialGroup" &&
            (
                masterDataType == MaterialMasterDataType.RAW_MATERIAL_SHEET ||
                    masterDataType == MaterialMasterDataType.RAW_MATERIAL_COIL
            )
        ) {
            return MaterialClassificationField(
                "sheetMaterialGroup",
                "Text",
                null,
                null,
                "tset.ref.field.sheetMaterialGroup",
                "tset.ref.lov-type.sheetMaterialGroup",
            )
        }
        return requireNotNull(materialClassificationFieldMap[nbkFieldName]) { "missing material classification fields $nbkFieldName" }
    }

    fun getMasterDataUnit(
        costUnit: String?,
        type: String,
    ): MdUnitWithFactor? = UnitMapper.getMasterDataUnit(costUnit, type)

    const val RAW_MATERIAL_OTHER_CLASSIFICATION_KEY = "tset.ref.classification.other"
    private val materialClassificationMap =
        mapOf(
            "CONSUMABLE" to "tset.ref.classification.consumable",
            "RAW_MATERIAL_COIL" to "tset.ref.classification.coil",
            "RAW_MATERIAL_SHEET" to "tset.ref.classification.sheet",
            "RAW_MATERIAL_CASTING_ALLOY" to "tset.ref.classification.casting-alloy",
            "RAW_MATERIAL_INGOT" to "tset.ref.classification.ingot",
            "RAW_MATERIAL_LAMELLA" to "tset.ref.classification.lamella",
            "RAW_MATERIAL_PLASTIC_GRANULATE" to "tset.ref.classification.plastic-granulate",
            "RAW_MATERIAL_RUBBER" to "tset.ref.classification.rubber",
            "RAW_MATERIAL_SAND" to "tset.ref.classification.sand",
            "RAW_MATERIAL_PIPE" to "tset.ref.classification.pipe",
            "RAW_MATERIAL_WIRE_ROD" to "tset.ref.classification.wire-rod",
            "RAW_MATERIAL_BAR" to "tset.ref.classification.bar",
            "RAW_MATERIAL_POWDER" to "tset.ref.classification.powder",
            "RAW_MATERIAL_WAX" to "tset.ref.classification.wax",
            "RAW_MATERIAL_RARE_EARTH" to "tset.ref.classification.rare-earth",
            "RAW_MATERIAL_PAINT" to "tset.ref.classification.paint",
            "RAW_MATERIAL_METALLIC_COATING" to "tset.ref.classification.metallic-coating",
            "RAW_MATERIAL_COATING_PCBA" to "tset.ref.classification.coating-pcba",
            "RAW_MATERIAL_MANUAL" to RAW_MATERIAL_OTHER_CLASSIFICATION_KEY,
            "RAW_MATERIAL_PAPER_COIL" to "tset.ref.classification.paper-coil",
            "RAW_MATERIAL_PAPER_SHEET" to "tset.ref.classification.paper-sheet",
            "ELECTRONIC_COMPONENT" to "tset.ref.classification.electronic-component",
            "RAW_MATERIAL_INK" to "tset.ref.classification.ink",
            "RAW_MATERIAL_VARNISH" to "tset.ref.classification.varnish",
        )

    fun getMaterialClassification(masterDataTypeAsString: String): String? = materialClassificationMap[masterDataTypeAsString]

    private val technologyClassificationMap =
        mapOf(
            "CHAT" to "tset.ref.classification.CHAT",
            "INJ2" to "tset.ref.classification.INJ2",
            "BART" to "tset.ref.classification.BART",
            "CEXT" to "tset.ref.classification.CEXT",
            "WHAT" to "tset.ref.classification.WHAT",
            "DFOR" to "tset.ref.classification.DFOR",
            "RROL" to "tset.ref.classification.RROL",
            "CROL" to "tset.ref.classification.CROL",
            "DFORT" to "tset.ref.classification.DFORT",
            "AFOR" to "tset.ref.classification.AFOR",
            "DCA" to "tset.ref.classification.DCA",
            "CHILL" to "tset.ref.classification.CHILL",
            "SAND" to "tset.ref.classification.SAND",
            "PREC" to "tset.ref.classification.PREC",
            "VPREC" to "tset.ref.classification.VPREC",
            "PCBA" to "tset.ref.classification.PCBA",
            "SINT" to "tset.ref.classification.SINT",
            "INJ" to "tset.ref.classification.INJ",
            "MINJ" to "tset.ref.classification.MINJ",
            "CUBE" to "tset.ref.classification.CUBE",
            "FTIPDS" to "tset.ref.classification.FTIPDS",
            "FTITDS" to "tset.ref.classification.FTITDS",
            "LAST" to "tset.ref.classification.LAST",
            "RSWA" to "tset.ref.classification.RSWA",
            "CORE" to "tset.ref.classification.CORE",
            "CORES" to "tset.ref.classification.CORES",
            "MAGN" to "tset.ref.classification.MAGN",
            "ALEX" to "tset.ref.classification.ALEX",
            "RINJ" to "tset.ref.classification.RINJ",
            "PBOX" to "tset.ref.classification.PBOX",
        )

    fun getTechnologyClassification(technologyField: FieldParameter?): List<String> =
        technologyField?.let {
            @Suppress("UNCHECKED_CAST")
            (it.value as ArrayList<Map<String, String>>)
                .flatMap { maps -> maps.entries }
                .mapNotNull { tech -> getTechnologyClassification(tech.value) }
        } ?: emptyList()

    private fun getTechnologyClassification(technology: String): String? = technologyClassificationMap[technology]
}