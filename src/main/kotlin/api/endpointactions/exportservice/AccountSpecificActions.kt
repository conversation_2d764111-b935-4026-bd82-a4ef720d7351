package api.endpointactions.exportservice

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.nu.export.common.framework.dto.SessionDto
import com.nu.export.common.framework.dto.SessionStatus
import com.nu.qa.utilities.api.actions.AbstractEndpointAction
import io.qameta.allure.Step
import io.restassured.RestAssured
import io.restassured.http.Method
import io.restassured.response.Response
import org.apache.http.HttpStatus

abstract class AccountSpecificActions(
    val mainPath: String,
) : AbstractEndpointAction() {
    val startUrl: String = "$mainPath/session"
    private val sessionUrl: String = "$mainPath/session/{requestId}"
    private val typeUrl: String = "$mainPath/type/{typeId}"
    private val resultUrl: String = "$sessionUrl/result"

    @Step("Get integration session")
    fun integrationSession(
        accessToken: String?,
        requestId: String?,
    ): SessionDto {
        attachRequest(Method.GET, sessionUrl)

        val result =
            RestAssured
                .given()
                .header(AUTHORIZATION, accessToken)
                .pathParam(REQUEST_ID, requestId)
                .get(sessionUrl)
                .then()
                .extract()
                .response()

        attachResult(result)

        if (result.statusCode() != HttpStatus.SC_OK) {
            throw AssertionError("Error checking export readiness, details: " + result.asPrettyString())
        }

        val sessionDto = jacksonObjectMapper().readValue<SessionDto>(result.body().asString())

        if (sessionDto.status == SessionStatus.ERROR) {
            throw AssertionError("Session failed!" + sessionDto.statusDescription)
        }
        return sessionDto
    }

    @Step("Get result")
    fun integrationResult(
        accessToken: String?,
        requestId: String?,
    ): Response {
        attachRequest(Method.GET, resultUrl)

        val result =
            RestAssured
                .given()
                .header(AUTHORIZATION, accessToken)
                .pathParam(REQUEST_ID, requestId)
                .get(resultUrl)
                .then()
                .extract()
                .response()

        attachResult(result)

        if (result.statusCode() != HttpStatus.SC_OK) {
            throw AssertionError("Error getting result, details: " + result.asPrettyString())
        }

        return result
    }

    @Step("Enable type for all")
    fun enableTypeForAll(
        accessToken: String?,
        typeId: String?,
    ) {
        attachRequest(Method.POST, typeUrl)

        val result =
            RestAssured
                .given()
                .header(AUTHORIZATION, accessToken)
                .pathParam(TYPE_ID, typeId)
                .queryParam(ACCOUNT, ACCOUNT_ALL)
                .post(typeUrl)
                .then()
                .extract()
                .response()

        if (!listOf(HttpStatus.SC_OK, HttpStatus.SC_NO_CONTENT).contains(result.statusCode())) {
            throw AssertionError("Enabling export type failed, details: " + result.asPrettyString())
        }

        attachResult(result)
    }

    @Step("Disable type for all")
    fun disableTypeForAll(
        accessToken: String?,
        typeId: String?,
    ) {
        attachRequest(Method.DELETE, typeUrl)

        val result =
            RestAssured
                .given()
                .header(AUTHORIZATION, accessToken)
                .pathParam(TYPE_ID, typeId)
                .queryParam(ACCOUNT, ACCOUNT_ALL)
                .delete(typeUrl)
                .then()
                .extract()
                .response()

        if (!listOf(HttpStatus.SC_OK, HttpStatus.SC_NO_CONTENT).contains(result.statusCode())) {
            throw AssertionError("Disabling export type failed, details: " + result.asPrettyString())
        }

        attachResult(result)
    }

    @Step("Delete session")
    fun deleteSession(
        accessToken: String?,
        requestId: String?,
    ) {
        attachRequest(Method.DELETE, sessionUrl)

        val result =
            RestAssured
                .given()
                .header(AUTHORIZATION, accessToken)
                .pathParam(REQUEST_ID, requestId)
                .delete(sessionUrl)
                .then()
                .extract()
                .response()

        attachResult(result)
    }

    companion object {
        const val REQUEST_ID: String = "requestId"
        const val TYPE_ID: String = "typeId"
        const val ACCOUNT: String = "account"
        const val ACCOUNT_ALL: String = "all"
    }
}
