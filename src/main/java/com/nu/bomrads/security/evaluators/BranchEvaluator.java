package com.nu.bomrads.security.evaluators;

import com.nu.bomrads.id.BranchId;
import com.nu.bomrads.security.RolePermissionService;
import com.nu.bomrads.service.history.AccessCheck;
import org.springframework.stereotype.Service;

@Service
public class BranchEvaluator implements TsetPermissionEvaluator, BaseRoleEvaluator {
    private final RolePermissionService rolePermissionService;

    private final EntityRoleService entityRoleService;

    public BranchEvaluator(
        EntityRoleService entityRoleService,
        RolePermissionService rolePermissionService
    ) {
        this.entityRoleService = entityRoleService;
        this.rolePermissionService = rolePermissionService;
    }

    @Override
    public RolePermissionService getRolePermissionService() {
        return rolePermissionService;
    }

    @Override
    public EntityRoleService getEntityRoleService() {
        return entityRoleService;
    }

    @Override
    public String getEntityEvaluator() {
        return "branch";
    }

    @Override
    public boolean hasPermission(AccessCheck accessCheck, Object targetDomainObject, String permission) {
        if (targetDomainObject == null) {
            return true;
        }
        if (targetDomainObject instanceof BranchId branchId) {
            return checkBranchId(accessCheck, branchId, permission);
        }
        return false;
    }

    private boolean checkBranchId(AccessCheck accessCheck, BranchId branchId, String permission) {
        var roles = entityRoleService.getRolesByBranchId(accessCheck, branchId).getRoles();
        var info = new RoleInfo(false, roles, accessCheck.userId(), permission);
        return info.hasPermission(role -> rolePermissionService.getPermissionsForRoleAndBlock(role, accessCheck));
    }
}
