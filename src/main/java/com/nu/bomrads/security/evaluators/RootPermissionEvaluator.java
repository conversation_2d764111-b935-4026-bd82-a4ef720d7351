package com.nu.bomrads.security.evaluators;

import com.nu.bomrads.security.RolePermissionService;
import com.nu.bomrads.security.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.nu.bomrads.utils.StreamUtils.of;

@Service
public class RootPermissionEvaluator {

    private final Map<String, TsetPermissionEvaluator> evaluators;
    private final RolePermissionService rolePermissionService;
    private final Logger log = LoggerFactory.getLogger(RootPermissionEvaluator.class);
    private final WorkspacePreEvaluator workspacePreEvaluator;

    public RootPermissionEvaluator(
            List<TsetPermissionEvaluator> evaluators,
            RolePermissionService rolePermissionService,
            WorkspacePreEvaluator workspacePreEvaluator
    ) {
        this.evaluators = of(evaluators).toMap(TsetPermissionEvaluator::getEntityEvaluator, it -> it);
        this.rolePermissionService = rolePermissionService;
        this.workspacePreEvaluator = workspacePreEvaluator;
    }

    public boolean hasPermission(Object targetDomainObject, String entity, String permission) {
        var accessCheck = SecurityUtils.getCurrentAccessCheck();
        // We check it the roles allow the user to perform the action
        log.trace("Get permissions for {}", accessCheck.roles());

        var userPermissions = rolePermissionService.getPermissionsOfCurrentUserAndBlock(accessCheck);
        if (userPermissions.contains(permission)) {
            return true;
        }

        if (!Objects.equals(entity, "workspace")) {
            return workspacePreEvaluator.checkPermissionForWorkspace(accessCheck, targetDomainObject, entity, permission);
        }

        // Otherwise we check the entity permissions

        var hasPermission = evaluators.get(entity).hasPermission(accessCheck, targetDomainObject, permission);
        if (!hasPermission) {
            log.trace("Access denied for {}:{} with permission {}", entity, targetDomainObject, permission);
        }
        return hasPermission;
    }
}
