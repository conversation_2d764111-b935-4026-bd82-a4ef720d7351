package com.nu.bomrads.repository;

import com.nu.bomrads.domain.Owner;
import com.nu.bomrads.domain.listener.FileUploadListener;
import com.nu.bomrads.repository.annotations.AuthorizedBy;
import com.nu.bomrads.repository.annotations.Secure;
import com.nu.bomrads.repository.insecure.OwnerRepository;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Repository;

@Repository
@Secure
public class SecureOwnerRepository {

    private final OwnerRepository ownerRepository;

    SecureOwnerRepository(OwnerRepository ownerRepository) {
        this.ownerRepository = ownerRepository;
    }

    @AuthorizedBy(FileUploadListener.class)
    public Owner save(@P("owner") Owner owner) {
        return ownerRepository.save(owner);
    }
}
