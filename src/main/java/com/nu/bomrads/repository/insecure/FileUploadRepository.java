package com.nu.bomrads.repository.insecure;

import com.nu.bomrads.domain.BomNodeFileUploadDto;
import com.nu.bomrads.domain.FileUpload;
import com.nu.bomrads.id.AccountId;
import com.nu.bomrads.id.BomNodeId;
import com.nu.bomrads.id.FileUploadId;
import com.nu.bomrads.repository.annotations.Insecure;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
@Insecure
public interface FileUploadRepository extends JpaRepository<FileUpload, FileUploadId>, JpaSpecificationExecutor<FileUpload> {
    @Query("select distinct fu from FileUpload fu " +
        "join fu.owners ow " +
        "join BomNode bn on ow.id = bn.owner.id " +
        "where bn.id = :bomNodeId " +
        "and fu.uploadType = :uploadType " +
        "and fu.account.id = :accountId " +
        "and fu.deleted = false")
    List<FileUpload> findFileUploadsByBomNodeIdAndAccountIdAndUploadTypeAndDeletedFalse(
        @Param("accountId") AccountId accountId,
        @Param("bomNodeId") BomNodeId bomNodeId,
        @Param("uploadType") String uploadType
    );

    Optional<FileUpload> findByIdAndAccountIdAndDeletedIsFalse(@NotNull FileUploadId id, @NotNull AccountId accountId);

    Optional<FileUpload> findByIdAndAccountId(@NotNull FileUploadId fileUploadId, @NotNull AccountId accountId);

    @Query("select distinct fu from FileUpload fu " +
        "join fu.owners ow " +
        "join BomNode bn on ow.id = bn.owner.id " +
        "where bn.id = :bomNodeId " +
        "and fu.account.id = :accountId " +
        "and fu.deleted = false")
    List<FileUpload> findFileUploadsByBomNodeIdAndAccountIdAndDeletedFalse(@Param("accountId") AccountId accountId, @Param("bomNodeId") BomNodeId bomNodeId);

    @Query("""
        select new com.nu.bomrads.domain.BomNodeFileUploadDto(bn.id, fu)
        from FileUpload fu
        join fu.owners ow
        join BomNode bn on ow.id = bn.owner.id
        where bn.id in :bomNodeIds
        and fu.account.id = :accountId
        and fu.deleted = false
        """)
    List<BomNodeFileUploadDto> findFileUploadsByBomNodeIdAndAccountIdAndDeletedFalse(@Param("accountId") AccountId accountId, @Param("bomNodeIds") Set<BomNodeId> bomNodeIds);
}
