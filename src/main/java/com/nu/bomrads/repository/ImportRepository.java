package com.nu.bomrads.repository;

import com.nu.bomrads.domain.Import;
import com.nu.bomrads.id.AccountId;
import com.nu.bomrads.id.ImportId;

import java.util.Set;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Spring Data SQL repository for the Import entity.
 */
@Repository
public interface ImportRepository extends JpaRepository<Import, ImportId>, JpaSpecificationExecutor<Import> {
    @Modifying
    @Query("delete Import i where i.account.id in :accountIds")
    void deleteAllByAccountIdIn(@Param("accountIds") Set<AccountId> accountIdForAccessCheck);

}
