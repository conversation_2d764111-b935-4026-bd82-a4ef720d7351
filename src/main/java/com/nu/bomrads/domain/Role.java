package com.nu.bomrads.domain;

import com.nu.bomrads.config.db.RoleIdType;
import com.nu.bomrads.domain.listener.AccountIdCheckListener;
import com.nu.bomrads.enumeration.Roles;
import com.nu.bomrads.id.RoleId;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Type;
import org.hibernate.envers.Audited;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;
import java.io.Serializable;

import static org.hibernate.envers.RelationTargetAuditMode.NOT_AUDITED;


/**
 * A Role.
 */
@Entity
@Audited
@Table(name = TableName.ROLE)
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@Getter
@Setter
@ToString
@EntityListeners(AccountIdCheckListener.class)
public class Role extends AbstractAuditingEntity implements Serializable, AccountCanBeSet {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @Type(RoleIdType.class)
    private RoleId id;

    @Column(name = "role")
    @Enumerated(EnumType.STRING)
    private Roles role;

    @Column(name = "user_Id")
    @Length(max = 255)
    private String userId;

    @Audited(targetAuditMode = NOT_AUDITED)
    @ManyToOne
    @JoinColumn(name = "role_group_id", nullable = false)
    @NotNull
    @ToString.Exclude
    private RoleGroup roleGroup;

    public Role id(RoleId id) {
        this.id = id;
        return this;
    }

    public Role account(ClientAccount clientAccount) {
        this.setAccount(clientAccount);
        return this;
    }

    public Role userId(String userId) {
        this.userId = userId;
        return this;
    }

    public Role role(Roles roles) {
        this.role = roles;
        return this;
    }

    public Role roleGroup(RoleGroup roleGroup) {
        this.roleGroup = roleGroup;
        return this;
    }

    public Role createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public Role lastModifiedBy(String lastModifiedBy) {
        this.setLastModifiedBy(lastModifiedBy);
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o instanceof Role role) {
            return id != null && id.equals(role.getId());
        } else {
            return false;
        }
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }
}
