package com.nu.bomrads.service;

import com.nu.bomrads.id.AccountId;
import com.nu.bomrads.repository.SecureBranchRepository;
import com.nu.bomrads.repository.SecureChangesetRepository;
import com.nu.bomrads.repository.ClientAccountRepository;
import com.nu.bomrads.repository.ImportRepository;
import com.nu.bomrads.repository.SecurePublishProcessRepository;
import com.nu.bomrads.repository.SecureRelationRepository;
import com.nu.bomrads.repository.SecureBomNodeRepository;
import com.nu.bomrads.repository.SecureFolderRepository;
import com.nu.bomrads.repository.SecureProjectRepository;
import com.nu.bomrads.repository.SecureRoleGroupRepository;
import com.nu.bomrads.repository.SecureRoleRepository;
import com.nu.bomrads.repository.SecureSnapshotRepository;
import com.nu.bomrads.repository.SecureWorkspaceRepository;
import com.nu.bomrads.utils.TraceUtils;
import io.micrometer.tracing.Tracer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Set;
import java.util.function.IntUnaryOperator;

import static com.nu.bomrads.utils.StreamUtils.map;

@Service
@Slf4j
public class GarbageCollectionService implements TraceUtils {

    private static final String COUNT_CHANGESETS = "count-changesets";
    private final Tracer tracer;
    private final SecureSnapshotRepository snapshots;
    private final SecureRelationRepository relations;
    private final SecureBomNodeRepository bomNodes;
    private final SecureChangesetRepository changesets;
    private final SecureBranchRepository branches;
    private final SecureProjectRepository projects;
    private final SecureRoleGroupRepository roleGroups;
    private final SecureRoleRepository roles;
    private final SecureFolderRepository folders;
    private final SecureWorkspaceRepository workspaces;
    private final SecurePublishProcessRepository publishProcesses;
    private final ImportRepository importProcesses;
    private final ClientAccountRepository accountRepository;

    private static final int PAGE_SIZE = 1000;

    public GarbageCollectionService(
            Tracer tracer,
            SecureSnapshotRepository snapshots,
            SecureRelationRepository relations,
            SecureBomNodeRepository bomNodes,
            SecureChangesetRepository changesets,
            SecureBranchRepository branches,
            SecureProjectRepository projects,
            SecureRoleRepository roles,
            SecureRoleGroupRepository roleGroups,
            SecureFolderRepository folders,
            SecureWorkspaceRepository workspaces,
            SecurePublishProcessRepository publishProcesses,
            ImportRepository importProcesses,
            ClientAccountRepository accountRepository
    ) {
        this.tracer = tracer;
        this.snapshots = snapshots;
        this.relations = relations;
        this.bomNodes = bomNodes;
        this.changesets = changesets;
        this.branches = branches;
        this.projects = projects;
        this.folders = folders;
        this.roles = roles;
        this.roleGroups = roleGroups;
        this.workspaces = workspaces;
        this.publishProcesses = publishProcesses;
        this.accountRepository = accountRepository;
        this.importProcesses = importProcesses;
    }

    @Transactional(readOnly = false)
    public int deleteSnapshots(Set<AccountId> accountIds) {
        var changesetCounts = trace(COUNT_CHANGESETS, () -> changesets.countByAccountIdIn(accountIds));
        log.info("Deleting snapshots for {} number of accounts, number of changesets: {}", accountIds.size(), changesetCounts);
        final var uuids = map(accountIds, AccountId::id);
        final var sum = paging(changesetCounts, PAGE_SIZE, pos -> snapshots.deletePageByAccountIdIn(uuids, PAGE_SIZE, pos));
        log.info("deleted {} snapshots for environments: {}", sum, accountIds);
        return sum;
    }

    @Transactional(readOnly = false)
    public int deleteSnapshotTrash(Set<AccountId> accountIds) {
        var changesetCounts = trace(COUNT_CHANGESETS, () -> changesets.countTrashChangesetsByAccountIdIn(accountIds));
        log.info("Deleting trash snapshots for {} number of accounts, number of changesets: {}", accountIds.size(), changesetCounts);
        final var uuids = map(accountIds, AccountId::id);
        final var sum = paging(changesetCounts, PAGE_SIZE, pos -> snapshots.deleteTrashPageByAccountIdIn(uuids, PAGE_SIZE, pos));
        log.info("deleted {} trash snapshots for environments: {}", sum, accountIds);
        return sum;
    }

    private int paging(int fullCounts, int pageSize, IntUnaryOperator taskForPage) {
        var sum = 0;
        for (var offset = 0; offset < fullCounts; offset += pageSize) {
            final var pos = offset;
            var result = trace("page", () -> taskForPage.applyAsInt(pos),
                span -> span.tag("pos", Integer.toString(pos)));
            log.info("deleted {} items for offset={}", result, pos);
            sum += result;
        }
        return sum;
    }

    @Transactional(readOnly = false)
    public int deleteRelations(Set<AccountId> accountIds) {
        log.info("Deleting relations for {} number of accounts", accountIds.size());
        return trace("deleteRelations", () -> relations.deleteAllByAccountIdIn(accountIds));
    }

    @Transactional(readOnly = false)
    public int deleteRelationTrash(Set<AccountId> accountIds) {
        var changesetCounts = trace(COUNT_CHANGESETS, () -> changesets.countTrashChangesetsByAccountIdIn(accountIds));
        log.info("Deleting trash relations for {} number of accounts, number of changesets: {}", accountIds.size(), changesetCounts);
        final var uuids = map(accountIds, AccountId::id);
        final var sum = paging(changesetCounts, PAGE_SIZE, pos -> relations.deleteTrashByAccountIdIn(uuids, PAGE_SIZE, pos));
        log.info("deleted {} trash relations for environments: {}", sum, accountIds);
        return sum;
    }

    @Transactional(readOnly = false)
    public void deleteByAccountIds(Set<AccountId> accountIds) {
        log.info("Deleting remaining rows for {} number of accounts", accountIds.size());
        publishProcesses.deleteAllByAccountIdIn(accountIds);
        branches.deleteAllByAccountIdIn(accountIds);
        trace("deleteChangesets", () -> changesets.deleteAllByAccountIdIn(accountIds));
        bomNodes.deleteAllByAccountIdIn(accountIds);
        projects.deleteAllByAccountIdIn(accountIds);
        folders.deleteAllByAccountIdIn(accountIds);
        workspaces.deleteAllByAccountIdIn(accountIds);
        roles.deleteAllByAccountIdIn(accountIds);
        roleGroups.deleteAllByAccountIdIn(accountIds);
        importProcesses.deleteAllByAccountIdIn(accountIds);
        accountRepository.deleteAllById(accountIds);
    }

    @Transactional(readOnly = false)
    public void deleteTrashByAccountIds(Set<AccountId> accountIds) {
        log.info("deleting trash for {} accounts", accountIds.size());
        publishProcesses.deleteTrashByAccountIdIn(accountIds);
        trace("delete-branches", () -> branches.deleteTrashByAccountIdIn(accountIds));
        trace("delete-changesets", () -> changesets.deleteTrashByAccountIdIn(accountIds));
        trace("delete-bomnodes", () -> bomNodes.deleteTrashByAccountIdIn(accountIds));
        trace("delete-projects", () -> projects.deleteTrashByAccountIdIn(accountIds));
        log.info("deleting trash for {} accounts finished", accountIds.size());
        // TODO, delete imports too?
    }

    @Override
    public Tracer getTracer() {
        return tracer;
    }

}
