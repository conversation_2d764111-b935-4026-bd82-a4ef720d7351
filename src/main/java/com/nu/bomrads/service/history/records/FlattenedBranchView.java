package com.nu.bomrads.service.history.records;

import static java.util.Optional.ofNullable;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;

import io.micrometer.tracing.Tracer;
import jakarta.annotation.Nonnull;

import com.nu.bomrads.domain.BomNode;
import com.nu.bomrads.domain.Branch;
import com.nu.bomrads.domain.Changeset;
import com.nu.bomrads.domain.Import;
import com.nu.bomrads.domain.Relation;
import com.nu.bomrads.domain.Snapshot;
import com.nu.bomrads.dto.BranchViewDTO;
import com.nu.bomrads.dto.NodeSnapshotDTO;
import com.nu.bomrads.enumeration.RelationType;
import com.nu.bomrads.id.BomNodeId;
import com.nu.bomrads.id.ManufacturingTreeId;
import com.nu.bomrads.service.errors.ErrorConstants;
import com.nu.bomrads.service.errors.NotFoundException;
import com.nu.bomrads.service.response.LoadingMode;
import com.nu.bomrads.utils.StreamUtils;
import com.nu.bomrads.utils.StreamUtils.Extension;
import com.tset.common.util.TopologicalSorter;

import static com.nu.bomrads.utils.StreamUtils.lookup;
import static com.nu.bomrads.utils.StreamUtils.of;


/**
 * Object to encapsulate a state of a branch, where for every BomNodeId there is only one Snapshot, which represents the
 * current state of that BomNode in the branch.
 *
 */
public record FlattenedBranchView(
    Branch branch,
    Changeset changeset,
    Map<BomNodeId, Snapshot> nodeIdsToSnapshots,

    /**
     * for a given BomNodeId this map contains all the relations, which point to the children
     */
    Map<BomNodeId, List<Relation>> childrenMap,

    /**
     * for a given BomNodeId this map contains all the relations, which point to a parent
     */
    Map<BomNodeId, List<Relation>> parentMap,
    Map<BomNodeId, Snapshot> orphans
) {

    public record Tree(
        Collection<Snapshot> snapshots,
        Collection<Snapshot> externalSnapshots,
        List<Relation> internalRelations,
        List<Relation> externalRelations) {
    }

    /**
     * return the snapshots for all the parents recursively. If 'includeStarting' is set to false, the result won't contain the snapshot for the starting bomNodes
     * @param nodeIds
     * @return
     */
    public Set<Snapshot> collectExternalParents(Set<BomNodeId> nodeIds, boolean includeStarting) {
        // It is possible, that there was no Snapshot previously for a BomNodeId.
        var snapshots = of(nodeIds).mapNotNull(nodeIdsToSnapshots::get).toList();
        var parents = collectParentSnapshots(snapshots);
        if (!includeStarting) {
            parents.removeAll(snapshots);
        }
        return parents;
    }


    /**
     * Build a tree like structure, where the tree starts from calculation context of the rootId, and contains all the
     * children nodes, and all the external parents.
     *
     * This means there are no "lazy" relations in the subtree that starts from the root of the selectedSnapshot
     *
     */
    public Tree buildNotLazyTree(Snapshot selectedSnapshot) {
        // Jump to calculation context
        final var calculationContextRootId = selectedSnapshot.getCalculationRootId();

        var relations = new ArrayList<Relation>();
        // collect all children, recursively
        var snapshots = collect(calculationContextRootId, new ArrayList<>(), relations, Function.identity());

        // collect all the parents
        var allSnapshots = collectParentSnapshots(snapshots);

        // collect all bomNodeId
        var allNodeIds = of(allSnapshots).map(Snapshot::getBomNodeId).toSet();

        // relations which are going between two bomnode/snapshot inside the branch
        var partitionedRelations = of(allNodeIds)
            .flatMapCollection(this::getChildRelations)
            .partitioningBy(relation -> allNodeIds.contains(relation.getChildBomNodeId()));

        allSnapshots.removeAll(snapshots);
        return new Tree(
            snapshots,
            allSnapshots,
            partitionedRelations.trueValues(),
            partitionedRelations.falseValues());
    }

    /**
     * Build a tree like structure, where the tree starts from calculation context of the rootId, and contains all the
     * children nodes, and all the external parents.
     *
     * <p>
     * The returned tree separates the snapshots and relations into lazy (external) and not lazy (internal) ones:
     * <ul>
     *     <li>{@link Tree#snapshots()} contains all snapshots that will be copied into the new changeset.
     *     These are all snapshots from root to the <code>selectedSnapshot</code></li>
     *     <li>{@link Tree#externalSnapshots()} contains all children of <code>selectedSnapshot</code> which will not be copied into the new changeset (lazy checkout)</li>
     *     <li>{@link Tree#internalRelations()}</li> contains all relations between  {@link Tree#snapshots()}, where both parent and
     *     child are part of {@link Tree#snapshots()} (will be created as {@link RelationType#INTERNAL} in the new changeset</li>
     *     <li>{@link Tree#externalRelations()} ()}</li> contains all relations between a node in {@link Tree#snapshots()} and a
     *     node in  {@link Tree#externalSnapshots()} (will be created as {@link RelationType#EXTERNAL_CHILD} in the new changeset</li>
     * </ul>
     * </p>
     *
     */
    public Tree buildTree(Snapshot selectedSnapshot) {
        // Jump to calculation context
        final var calculationContextRootId = selectedSnapshot.getCalculationRootId();

        var relations = new ArrayList<Relation>();

        // collect all children, recursively, starting from calculation root
        var allChildrenOfCalculationRoot = collect(calculationContextRootId, new ArrayList<>(), relations, Function.identity());

        // collect all parents till the top of the tree, including selected snapshot.
        var allParentsOfSelectedSnapshot = collectParentSnapshots(List.of(selectedSnapshot));
        // has allChildrenOfCalculationRoot and all parents of selectedSnapshot.
        var allParentsAndChildrenOfCalcRoot = collectParentSnapshots(allChildrenOfCalculationRoot);

        // collect all bomNodeId.
        var nodeIdsOfAllParentsAndChildrenCalcRoot = of(allParentsAndChildrenOfCalcRoot)
            .concat(allChildrenOfCalculationRoot)
            .map(Snapshot::getBomNodeId).toSet();

        var allParentsOfSelectedSnapshotNodeIds = of(allParentsOfSelectedSnapshot).map(Snapshot::getBomNodeId).toSet();
        // relations which are going between two bomnode/snapshot inside the branch.
        // values for true key -> all relations that belong to subtree that goes till root from selectedSnapshot.
        // Meaning, that all relations are between 2 nodes that belong to path  "selectedSnapshot -> Root of bom nodes."
        // values for false -> all other relations in the bom node tree
        var partitionedRelations = of(nodeIdsOfAllParentsAndChildrenCalcRoot)
            .flatMapCollection(this::getChildRelations)
            .partitioningBy(relation -> allParentsOfSelectedSnapshotNodeIds.contains(relation.getChildBomNodeId()));
        var externalSnapshots = new HashSet<>(allParentsAndChildrenOfCalcRoot);
        externalSnapshots.addAll(allChildrenOfCalculationRoot);
        externalSnapshots.removeAll(allParentsOfSelectedSnapshot);

        return new Tree(
            allParentsOfSelectedSnapshot,
            externalSnapshots,
            partitionedRelations.trueValues(),
            partitionedRelations.falseValues());
    }

    /**
     * @return a set of snapshots which are child of the given snapshots, recursively, or the given .
     */
    public Set<Snapshot> collectChildSnapshots(BomNodeId starting) {
        var snapshots = List.of(snapshotLookup(starting));
        return new TopologicalSorter<>(snapshots, snapshot -> true,
            snapshot -> of(getChildRelations(snapshot.getBomNodeId()))
                .map(Relation::getChildBomNodeId)
                .map(this::snapshotLookup)
                .toSet()).visitGraph();
    }

    /**
     * @return a set of snapshots which are the direct parents of the given snapshot.
     */
    public Set<Snapshot> collectDirectParentSnapshots(BomNodeId nodeId) {
        return getParentRelations(nodeId)
            .map(Relation::getParentBomNodeId)
            .map(this::snapshotLookup)
            .toSet();
    }

    /**
     * @return the Snapshot for the given BomNodeId
     */
    public Snapshot getSnapshot(BomNodeId nodeId) {
        return nodeIdsToSnapshots.get(nodeId);
    }

    /**
     * @return a set of snapshots which are either parents of the given snapshots or the given snapshots.
     * Subtree, up from given snapshots (inclusive).
     */
    private Set<Snapshot> collectParentSnapshots(List<Snapshot> snapshots) {
        return new TopologicalSorter<>(snapshots, snapshot -> true,
            snapshot -> getParentRelations(snapshot.getBomNodeId())
                .map(Relation::getParentBomNodeId)
                .map(this::snapshotLookup)
                .toSet()).visitGraph();
    }

    private <X> List<X> collect(BomNodeId nodeId, List<X> accumulator, ArrayList<Relation> relations,
                                Function<Snapshot, X> func) {
        var node = snapshotLookup(nodeId);
        if (node == null){
            return accumulator;
        }
        accumulator.add(func.apply(node));
        final var childList = getChildRelations(nodeId);
        relations.addAll(childList);
        for (var child : childList) {
            collect(child.getChildBomNodeId(), accumulator, relations, func);
        }
        return accumulator;
    }

    /**
     * @return the Snapshot which linked to the given BomNodeId.
     * @throws NotFoundException if there is no Snapshot for that BomNodeId.
     */
    public Snapshot snapshotLookup(BomNodeId nodeId) {
        var snapshot = nodeIdsToSnapshots.get(nodeId);
        return NotFoundException.check(snapshot, ErrorConstants.ERROR_MISSING_BOM_NODE, nodeId.idToString());
    }

    @Nonnull
    public List<Relation> getChildRelations(BomNodeId nodeId) {
        return childrenMap.getOrDefault(nodeId, Collections.emptyList());
    }

    @Nonnull
    private Extension<Relation> getInternalChildRelations(BomNodeId nodeId) {
        return lookup(childrenMap, nodeId).filter(Relation::isInternal);
    }

    @Nonnull
    private Extension<Relation> getExternalChildRelations(BomNodeId nodeId) {
        return lookup(childrenMap, nodeId).filter(Relation::isExternalChild);
    }

    @Nonnull
    Extension<Relation> getParentRelations(BomNodeId nodeId) {
        return lookup(parentMap, nodeId);
    }

    public int countChildren(BomNodeId nodeId) {
        return childrenMap.getOrDefault(nodeId, Collections.emptyList()).size();
    }

    /**
     * Used to write new relations during publish action.
     */
    public List<Relation> duplicateRelationsWithChangeset(Changeset changeset) {
        return allRelations().map(relation -> duplicateRelationsWithChangeset(changeset, relation)).toList();
    }

    private Relation duplicateRelationsWithChangeset(Changeset changeset, Relation relation) {
        var newRelation = relation.duplicate(changeset);
        ofNullable(nodeIdsToSnapshots.get(newRelation.getChildBomNodeId()))
            .map(Snapshot::getManufacturingTreeId)
            .ifPresent(childSnapshot -> {
                newRelation.setChildManTreeId(childSnapshot);
                newRelation.setRelationType(RelationType.INTERNAL);

            });
        return newRelation;
    }

    public BranchViewDTO toBranchViewDTO(Tracer tracer, LoadingMode loadingMode, boolean loadSourceChangeset) {
        var span = tracer.startScopedSpan("toBranchViewDTO");
        var result = toBranchViewDTO(loadingMode, loadSourceChangeset);
        if (span != null) {
            span.end();
        }
        return result;
    }

    /**
     * Create a BranchViewDTO considering the selected LoadingMode, which does the filtering
     */
    public BranchViewDTO toBranchViewDTO(LoadingMode loadingMode, boolean loadSourceChangeset) {
        var currentChangeset = getCurrentChangeset();
        var sourceChangeset = loadSourceChangeset ? Optional.ofNullable(branch.getSource()).map(Branch::getCurrentChangeset).map(Changeset::toMinimalDTO).orElse(null) : null;
        return new BranchViewDTO(
            branch.getProjectId(),
            branch.toMinimalDTO(),
            currentChangeset.toMinimalDTO(),
            sourceChangeset,
            loadingMode.toSnapshotDtos(this),
            ofNullable(branch.getImportField()).map(Import::toImportDto).orElse(null));
    }

    public Changeset getCurrentChangeset() {
        return changeset != null ? changeset : branch.getCurrentChangeset();
    }

    /**
     * @return all not-orphan snapshots.
     */
    public StreamUtils.Extension<Snapshot> snapshots() {
        return of(nodeIdsToSnapshots().values()).filter(snapshot -> orphans.get(snapshot.getBomNodeId()) == null);
    }

    @Nonnull
    public Set<BomNodeId> getBomNodeIds() {
        return nodeIdsToSnapshots.keySet();
    }

    public StreamUtils.Extension<Relation> allRelations() {
        return of(childrenMap.values()).flatMapCollection(Function.identity());
    }

    public NodeSnapshotDTO toNodeSnapshotDTO(Snapshot snapshot, Integer noBranches) {
        final var bomNodeId = snapshot.getBomNodeId();
        return snapshot.toNodeSnapshotDTO(getParentRelations(bomNodeId), getInternalChildRelations(bomNodeId),
            getExternalChildRelations(bomNodeId), noBranches, branch.getId());
    }

    public NodeSnapshotDTO toNodeSnapshotDTO(Snapshot snapshot) {
        return toNodeSnapshotDTO(snapshot, null);
    }

    public Map<BomNodeId, ManufacturingTreeId> createManufacturingTree(Collection<Snapshot> newestSnapshots) {
        var mapping = of(nodeIdsToSnapshots.values()).filter(snapshot -> snapshot.getManufacturingTreeId() != null)
            .toMap(Snapshot::getBomNodeId, Snapshot::getManufacturingTreeId);
        of(newestSnapshots).filter(snapshot -> snapshot.getManufacturingTreeId() != null)
            .forEach(snapshot -> mapping.put(snapshot.getBomNodeId(), snapshot.getManufacturingTreeId()));
        return mapping;
    }

    public Extension<BomNode> getOrphanBomNodes() {
        return of(orphans.values()).map(Snapshot::getBomNode);
    }

    /**
     * Validate, that all the relations point to an already existing snapshot
     */
    public void validateRelations() {
        childrenMap.values()
            .forEach(relations -> relations.stream()
                .filter(Relation::isInternal)
                .forEach(this::validateRelation));
    }

    /**
     * Validate, that the relation points to an already existing snapshot
     */
    private void validateRelation(Relation relation) {
        snapshotLookup(relation.getParentBomNodeId());
        snapshotLookup(relation.getChildBomNodeId());
    }

}
