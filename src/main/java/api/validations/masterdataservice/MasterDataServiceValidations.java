package api.validations.masterdataservice;

import io.qameta.allure.Step;
import io.restassured.response.Response;
import org.apache.http.HttpStatus;
import org.assertj.core.api.SoftAssertions;

public class MasterDataServiceValidations {

    @Step("Validate masterdata not found")
    public static void validateMasterDataNotFoundResponse(Response result) {
        SoftAssertions softAssert = new SoftAssertions();

        softAssert.assertThat(result.statusCode())
                .as("Improper HTTP error returned!\n" + result.asPrettyString())
                .isEqualTo(HttpStatus.SC_NOT_FOUND);
        softAssert.assertThat(result.jsonPath().getString("userErrorCode"))
                .as("errorCode is wrong in response!\n" + result.asPrettyString())
                .isEqualTo("MD_KEY_NOT_FOUND");
        softAssert.assertThat(result.jsonPath().getString("fallbackMessage"))
                .as("message record is missing from the response!\n" + result.asPrettyString())
                .isNotNull();
        softAssert.assertAll();
    }

    @Step("Validate headerType and header measurement conflict")
    public static void validateMeasurementConflictResponse(Response result) {
        SoftAssertions softAssert = new SoftAssertions();

        softAssert.assertThat(result.statusCode())
                .as("Improper HTTP error returned!\n" + result.asPrettyString())
                .isEqualTo(HttpStatus.SC_UNPROCESSABLE_ENTITY);
        softAssert.assertThat(result.jsonPath().getString("userErrorCode"))
                .as("errorCode is wrong in response!\n" + result.asPrettyString())
                .isEqualTo("MD_CONFLICTING_VALUE_SCHEMA");
        softAssert.assertThat(result.jsonPath().getString("fallbackMessage"))
                .as("message record is missing from the response!\n" + result.asPrettyString())
                .isNotNull();
        softAssert.assertAll();
    }
}
