import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory, useRoute } from 'vue-router'
import Manu from './manu.vue'

const initialRoute = {
  path: '/go/somewhere',
  query: {
    path: [],
  },
  name: 'somewhere',
  component: {},
}
const initialRouteWithObjectId = {
  path: '/go/somewhere/with/object/id',
  query: {
    path: [],
    objectId: 'b568360943154579845449b7',
    stepId: 'b56836094315457983349b7',
  },
  name: 'somewhere',
  component: {},
}
let currentRoute: Nullable<string> = null

vi.mock('vue-router', async () => {
  const actual = await vi.importActual('vue-router')

  return {
    ...(actual as object),
    useRoute: vi.fn(() => ({ query: {} })),
  }
})

const useRouteMock = vi.mocked(useRoute)

//Note: we mock the global store to get rid of implementation details, like navigationStore calls which fail
vi.mock('@/store', () => ({
  navigationStore: {
    navigateTo: () => {
      currentRoute = null
    },
  },
  userStore: {
    calculationNoteSectionWidth: 360,
  },
  errorStore: {
    clearActiveError: vi.fn(),
  },
  objectStore: {
    reset: vi.fn(),
  },
  manufacturingStore: {},
}))
vi.mock('@/store/navigation')
vi.mock('@tset/shared-utils/helpers/navigation', () => ({}))
vi.mock('@/router', () => ({ getCurrentRoute: () => ({}) }))
vi.mock('@tset/shared-ui/parts/fields/NuField.vue', () => ({ default: {} }))

const defaultMountOptions = {
  global: {
    plugins: [
      createRouter({
        history: createWebHistory(),
        routes: [initialRoute, { path: '/', component: {} }],
      }),
    ],
    mocks: {
      $route: initialRoute,
    },
    stubs: [
      'PortalTarget',
      'TsetWave',
      'RouterView',
      'IconClose',
      'IconChevronDown',
      'NuNavigationLink',
      'TsetButton',
      'ManuHeaderToolbar',
      'TheManuHeader',
      'ManufacturingNavigation',
      'IconChevronRight',
    ],
  },
}

const objectIdMountOptions = {
  global: {
    mocks: {
      $route: initialRouteWithObjectId,
    },
    stubs: [
      'PortalTarget',
      'TsetWave',
      'RouterView',
      'IconClose',
      'IconChevronDown',
      'IconChevronRight',
    ],
  },
}

describe('manu tests', () => {
  it('should mount properly', () => {
    const wrapper = mount(Manu, defaultMountOptions)
    expect(wrapper).toMatchSnapshot()
  })

  it('should mount with `bomNode`', () => {
    const w = mount(Manu, defaultMountOptions)
    expect(w).toMatchSnapshot()
  })
})
