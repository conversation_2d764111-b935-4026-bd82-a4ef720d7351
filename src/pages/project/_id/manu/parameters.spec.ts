import { manufacturingStore } from '@domain/calculation/manufacturing.store'
import { createTesting<PERSON>inia } from '@pinia/testing'
import CardLayout from '@tset/shared-ui/layout/CardLayout.vue'
import CalculationTypeModal from '@tset/shared-ui/manufacturing/CalculationTypeModal.vue'
import NuFieldContainer from '@tset/shared-ui/parts/NuFieldContainer.vue'
import NuUpdate<PERSON>ield from '@tset/shared-ui/parts/fields/NuUpdateField.vue'
import { gBomNodeEntity, gResultField } from '@tset/shared-utils/tests'
import { gManufacturing } from '@tset/shared-utils/tests/generators/manufacturing'
import { gManufacturingEntity } from '@tset/shared-utils/tests/generators/manufacturingEntity'
import { withAxiosMock } from '@tset/shared-utils/tests/mocks/withAxiosMock'
import { withModalsMock } from '@tset/shared-utils/tests/mocks/withModalsMock'
import { mount, type ComponentMountingOptions } from '@vue/test-utils'
import { ref } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import parameters from './parameters.vue'

const { isModalShown } = withModalsMock()
const { setMockResponse } = withAxiosMock()
setMockResponse({ payload: { data: { fields: [] } } })
//#endregion MOCKS

//#region SETUP FACTORY
const setup = ({ newExchangeRates = false } = {}) => {
  const manufacturing = gManufacturing({
    name: 'Test Manufacturing',
    children: newExchangeRates
      ? [
          gManufacturingEntity({
            name: 'Exchange Rates',
            type: 'MD_EXCHANGERATE_PARENT',
            fields: [gResultField({ name: 'mdTableConfig' })],
          }),
        ]
      : [],
    fields: [
      gResultField({ name: 'baseCurrency', type: 'Currency' }),
      gResultField({ name: 'responsible' }),
      gResultField({ name: 'quantityUnit' }),
      gResultField({ name: 'costUnit' }),
      gResultField({ name: 'lifeTime' }),
      gResultField({ name: 'productionVolumePerYear' }),
      gResultField({ name: 'averageProductionVolumePeryear' }),
      gResultField({ name: 'callsPerYear' }),
      gResultField({ name: 'lotSize' }),
      gResultField({ name: 'averageLotSize' }),
      gResultField({ name: 'procurementType' }),
      gResultField({ name: 'location' }),
      gResultField({ name: 'calculationDate' }),
      gResultField({ name: 'productionHoursPerYear' }),
      gResultField({ name: 'overheadMethod' }),
      gResultField({ name: 'elcoPurchaseVolume' }),
      gResultField({ name: 'calculationQualityConfigurationKey' }),
      gResultField({ name: 'totalCurrentCosts' }),
      gResultField({ name: 'totalTargetCosts' }),
      gResultField({ name: 'currentGapToTarget' }),
      gResultField({ name: 'currentGapToCostPerPart' }),
      gResultField({ name: 'targetGapToCostPerPart' }),
    ],
  })

  if (newExchangeRates) {
    const children = manufacturing.getChildren()
    vi.spyOn(manufacturing, 'getChildren').mockReturnValue([
      ...children,
      gManufacturing({
        name: 'Exchange Rates',
        type: 'MD_EXCHANGERATE_PARENT',
        fields: [gResultField({ name: 'mdTableConfig' })],
      }),
    ])
  }

  const manufacturingEntity = gBomNodeEntity({
    manufacturing,
  })
  manufacturingStore.setNodeInt(manufacturingEntity)
  const loadedManufacturing = ref(manufacturingStore.loadedManufacturing)

  const props: InstanceType<typeof parameters>['$props'] = {}
  const mountOptions: ComponentMountingOptions<typeof parameters> = {
    props,
    global: {
      stubs: [
        'IconInfoDetails',
        'IconInputMissing',
        'NuPopup',
        'CalculationTypeModal',
        'NuUpdateField',
        'TsetFieldLayout',
        'IconChevronDown',
        'IconEdit',
        'IconUnlink',
        'CollapseTransition',
      ],
      plugins: [
        createTestingPinia(),
        createRouter({
          history: createWebHistory(),
          routes: [{ path: '/', component: {} }],
        }),
      ],
      provide: {
        loadedManufacturing: loadedManufacturing,
        context: {},
      },
    },
  }

  const wrapper = mount(parameters, mountOptions)

  //#region HELPERS
  const getFields = () => wrapper.findAllComponents(NuUpdateField)
  const getCards = () => wrapper.findAllComponents(CardLayout)
  const getFieldsInCard = (cardIndex: number) =>
    getCards()?.at(cardIndex)?.findAllComponents(NuUpdateField)
  const getFieldContainersInCard = (cardIndex: number) =>
    getCards()?.[cardIndex]?.findAllComponents(NuFieldContainer)
  const getCalculationTypeModal = () =>
    wrapper.findComponent(CalculationTypeModal)
  const getCalculationTypeButton = () =>
    wrapper.find('[data-test="parameters-change-calculation-type"]')
  const getBaseCurrencyField = () =>
    getFields()
      .find((f) => f.props('field').name === 'baseCurrency')
      ?.props('field')
  const clickOnBaseCurrencyDetails = () =>
    getFieldContainersInCard(2)
      ?.at(0)
      ?.vm.$emit('open-details', getBaseCurrencyField())
  //#endregion HELPERS

  return {
    wrapper,
    getFields,
    getCards,
    getFieldsInCard,
    getFieldContainersInCard,
    getCalculationTypeModal,
    getCalculationTypeButton,
    clickOnBaseCurrencyDetails,
    getBaseCurrencyField,
  }
}
//#endregion SETUP FACTORY

//#region TESTS
describe('parameters page', () => {
  describe('when rendered', () => {
    const { getCards, getCalculationTypeModal, getFieldsInCard } = setup()
    it('- contains CardLayouts', () => {
      expect(getCards().length).toBe(3)
    })
    it('- contains a Calculation Quality Card with fields', () => {
      expect(getFieldsInCard(0)?.at(0)?.props('field')?.name).toBe(
        'calculationQualityConfigurationKey'
      )
    })
    it('- contains a Current Target Card with fields', () => {
      expect(getFieldsInCard(1)?.length).toBe(5)
    })
    it('- contains a Economic Card with fields', () => {
      expect(getFieldsInCard(2)?.length).toBe(11)
      expect(getFieldsInCard(2)?.at(0)?.props('field')?.name).toBe(
        'baseCurrency'
      )
    })
    it('- contains a CalculationTypeModal', () => {
      expect(getCalculationTypeModal().exists()).toBe(true)
    })
  })
  describe('when user clicks on the exchange rates button on the baseCurrency field', () => {
    it('- triggers the new exchangerates modal', async () => {
      const { clickOnBaseCurrencyDetails } = setup({ newExchangeRates: true })
      await clickOnBaseCurrencyDetails()
      expect(isModalShown('ExchangeRatesModal')).toBe(true)
    })
  })
  describe('when the user clicks on the calculation type button', () => {
    it('- opens the calculation type modal', async () => {
      const { getCalculationTypeButton } = setup()

      await getCalculationTypeButton().trigger('click')

      // expect($modal.show).toBeCalledWith('calculationTypeModal')
      expect(isModalShown('calculationTypeModal')).toBe(true)
    })
  })
})
//#endregion TESTS
