import type { HeaderTypeResponseDto } from '@domain/masterdata/model/openapi'
import { __BUILTIN_HEADER__ } from '@domain/masterdata/utils/constants'
import { createTestingPinia } from '@pinia/testing'
import { withModalsMock } from '@tset/shared-utils/tests/mocks/withModalsMock'
import { sharedVueQueryClient } from '@tset/shared-utils/vueQuery/vueQueryClient'
import { mount, type ComponentMountingOptions } from '@vue/test-utils'
import { ref } from 'vue'
import DetailList from './headertype-detail-list.vue'

const hoisted = vi.hoisted(() => ({
  headerTypeKey: 'material',
  searchParams: {} as Record<string, string>,
  queryClientMock: {
    invalidateQueries: vi.fn(),
    defaultQueryOptions: vi.fn(),
  },
}))

const headerType: HeaderTypeResponseDto = {
  active: true,
  effectivities: {},
  headerKeyType: 'header.key.simple',
  index: 0,
  key: hoisted.headerTypeKey,
  name: 'Material',
  enableCreateHeaderUi: true,
  headerKeyColumnName: 'value',
}

//#region MOCKS
vi.mock('@domain/masterdata/data/Permission.service', () => ({
  useMasterdataPermissionService: () => ({
    canCreate: ref(true),
    canRead: ref(true),
    canDelete: ref(true),
    isLoading: ref(false),
  }),
}))

vi.mock('@domain/masterdata/utils/router', () => ({
  useMasterdataRouterUtils: () => ({
    headerTypeKey: ref(hoisted.headerTypeKey),
  }),
}))

vi.mock('@domain/masterdata/data/HeaderType.service', () => ({
  useMasterdataHeaderTypeService: () => ({
    getHeaderType: () => ({
      data: ref(headerType),
      isPending: ref(false),
      isError: ref(false),
    }),
  }),
}))

const invalidateSpy = vi.spyOn(sharedVueQueryClient, 'invalidateQueries')

vi.mock('@vueuse/core', async (importOriginal) => {
  const vueUseImport = await importOriginal<
    // eslint-disable-next-line @typescript-eslint/consistent-type-imports
    typeof import('@vueuse/core')
  >()
  return {
    ...vueUseImport,
    useUrlSearchParams: () => hoisted.searchParams,
  }
})
//#endregion MOCKS

//#region LOCALVUE SETTINGS
const { showSpy } = withModalsMock()
//#endregion LOCALVUE SETTINGS

//#region SETUP FACTORY
const setup = (props: InstanceType<typeof DetailList>['$props']) => {
  const mountOptions: ComponentMountingOptions<typeof DetailList> = {
    attachTo: document.body,
    props: {
      ...props,
    },
    data: () => {},
    global: {
      stubs: {
        DetailsList: {
          name: 'DetailsList',
          template: '<div><slot name="tableHeaderTools"></slot></div>',
        },
        IconAdd: true,
      },
      plugins: [createTestingPinia()],
      mocks: {},
    },
  }

  const wrapper = mount(DetailList, mountOptions)

  //#region HELPERS
  const open = () =>
    wrapper
      .find(
        `[data-test="detail-list-add-detail-button-${hoisted.headerTypeKey}"]`
      )
      .trigger('click')

  const addHeader = {
    open,
    triggerSuccess: (headerKey: string) =>
      // TODO fix me master
      // @ts-expect-error no idea why this works
      showSpy.mock.calls.at(0)?.at(0)?.['emits']?.['success'](headerKey),
  }
  //#endregion HELPERS

  return {
    wrapper,
    then: {
      addHeader,
    },
  }
}
//#endregion SETUP FACTORY

//#region TESTS
describe('DetailList', () => {
  describe('CreateHeaderMode', () => {
    describe('onSuccess', async () => {
      const { then } = setup({})
      await then.addHeader.open()
      const headerKey = 'headerKey'
      then.addHeader.triggerSuccess(headerKey)

      it('- invalidates all header lov queries', () => {
        expect(invalidateSpy.mock.calls).toEqual([
          [
            {
              queryKey: [
                'masterdata',
                'lov',
                hoisted.headerTypeKey,
                '_BUILTIN_header',
              ],
              refetchType: 'all',
            },
          ],
          [
            {
              queryKey: [
                'masterdata',
                'lov',
                hoisted.headerTypeKey,
                '_BUILTIN_headerKey',
              ],
              refetchType: 'all',
            },
          ],
        ])
      })

      it('- updates the filter', () => {
        expect(hoisted.searchParams.filters).toEqual(
          JSON.stringify({
            [__BUILTIN_HEADER__]: [
              {
                type: 'builtinlov',
                equals: headerKey,
              },
            ],
          })
        )
      })
    })
  })
})
//#endregion TESTS
