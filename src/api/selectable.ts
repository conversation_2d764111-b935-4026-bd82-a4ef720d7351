import { $axios } from '@tset/shared-utils/helpers/api'
import { toSelectable } from '@tset/shared-utils/helpers/selectable'

type SelectableResponse = Selectable[] | SelectableDto

/**
 * Fetch Selectables list using the ResultField input endpoint for that.
 * (e.g. we use endpoint from field.metaInfo.path)
 *
 * GET
 * @param endpoint the endpoint to use.
 * @returns the usable Selectable list
 * @throws {TypeError} when the ResultField does not provide an endpoint
 */
export async function fetchSelectables(endpoint: string) {
  if (!endpoint) {
    throw new TypeError('api/fetchSelectables(): invalid endpoint.')
  }
  const { data } = await $axios.get<SelectableResponse>(endpoint)
  let items: Selectable[]
  let sections: string[]
  if (Array.isArray(data)) {
    items = data
    sections = []
  } else {
    items = data.items
    sections = data.sections || [] // sections an be `null` in the response
  }

  const getSectionIndex = (s: Selectable) => sections.indexOf(s.section || '')
  const sortBySection = (a: Selectable, b: Selectable) =>
    getSectionIndex(a) - getSectionIndex(b)

  // NB: we sort the items by section to display them in the right order
  return items?.concat().sort(sortBySection).map(toSelectable)
}
