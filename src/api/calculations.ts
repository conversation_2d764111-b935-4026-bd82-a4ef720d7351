import { $axios, addQueryToUrl } from '@tset/shared-utils/helpers/api'

async function getPath(
  projectId: ProjectFolderNode['_id'],
  bomNodeId: BomNodeEntity['id'],
  branchId: Branch['id']
) {
  const url = addQueryToUrl(
    `/api/projects/${projectId}/calculations/${bomNodeId}/path`,
    { branch: branchId }
  )

  const { data } = await $axios.get<string[]>(url)

  return data
}

export { getPath }
