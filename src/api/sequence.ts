import type { TsetSketchData } from '@tset/shared-model/TsetSketch'
import { $axios } from '@tset/shared-utils/helpers/api'

async function fetchSequenceStepTurning(
  sequenceId: ResultField['value'],
  operationName: ManufacturingDTO['name']
) {
  const { data } = await $axios.get(
    `/api/turning/sequence/${sequenceId}/operation/${operationName}`
  )
  return data
}

async function fetchSequenceStepSmf(
  projectId: ProjectFolderNode['_id'],
  sketchId: ResultField['value'],
  operationId: ResultField['value']
) {
  const { data } = await $axios.get<TsetSketchData>(
    `/api/${projectId}/smf/sequence/${sketchId}/operation/${operationId}`
  )
  return data
}

export { fetchSequenceStepSmf, fetchSequenceStepTurning }
