# https://tset.slite.com/app/docs/FVTD51u03vIcuS
import functools


def update_hierarchy(tree, path):
    if len(path) > 1 and path[0] in tree:
        update_hierarchy(tree[path[0]], path[1:])
    elif len(path) == 1 and path[0] not in tree:
        tree[path[0]] = {}
    elif len(path) > 1 and path[0] not in tree:
        tree[path[0]] = {}
        update_hierarchy(tree[path[0]], path[1:])


def update_path(path, next_level, last_level, line):
    if next_level == last_level + 1:
        path.append(line)
    elif next_level == last_level:
        path[-1] = line
    elif next_level < last_level:
        for index in range(0, (last_level - next_level) + 1):
            path.pop()
        path.append(line)


def normalize(line: str) -> str:
    result = line.strip()
    package_name_start = result.rindex('(') + 1
    package_name_end = result.rindex(')')
    test_name_end = result.index('(')
    return result[package_name_start:package_name_end] + '.' + result[:test_name_end]


def find_tests_and_normalize_names(hierarhy: dict) -> set:
    result = set()
    for item in hierarhy:
        if len(hierarhy[item]) == 0:
            result.add(normalize(item))
        else:
            for element in find_tests_and_normalize_names(hierarhy[item]):
                result.add(element)
    return result


FULLY_QUALIFIED_CLASS_NAME = 'fully_qualified_class_name'
TEST_METHOD_NAME = 'test_method_name'


def separate_class_and_method_names(fully_qualified_method_name: str) -> dict:
    split_index = fully_qualified_method_name.rindex('.')
    return {
        FULLY_QUALIFIED_CLASS_NAME: fully_qualified_method_name[:split_index],
        TEST_METHOD_NAME: fully_qualified_method_name[split_index + 1:]
    }


def aggregate_tests_method_for_class(accumulated: dict, new: dict) -> dict:
    result = accumulated
    if new[FULLY_QUALIFIED_CLASS_NAME] not in result:
        result[new[FULLY_QUALIFIED_CLASS_NAME]] = [new[TEST_METHOD_NAME]]
    elif new[TEST_METHOD_NAME] not in result[new[FULLY_QUALIFIED_CLASS_NAME]]:
        result[new[FULLY_QUALIFIED_CLASS_NAME]].append(new[TEST_METHOD_NAME])
    return result


def testng_method_include_xml_fragment(test: str) -> str:
    result = f'''
                    <include name="{test}"/>'''
    return result


def testng_class_xml_fragment(class_name: str, methods: list) -> str:
    tests = ''.join(list(map(testng_method_include_xml_fragment, methods)))
    result = f'''
            <class name="{class_name}">
                <methods>{tests}
                </methods>
            </class>'''
    return result


def testng_suite_xml(classes_xml: str) -> str:
    result = f'''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite thread-count="4" parallel="tests" name="Failed suite [Regression Tests]" verbose="0">

    <listeners>
        <listener class-name="com.nu.qa.ui.framework.testNG.AnnotationTransformer"/>
    </listeners>
    <test thread-count="4" parallel="classes" name="Sanity(failed)" verbose="0">
        <classes>{classes_xml}
        </classes>
    </test>
</suite>'''
    return result


def main():
    test_hierarchy_tree = {}
    path_from_root_node = []
    last_line = None
    # A negative value is needed to add the first line to the path_from_root_node
    initial_last_level = -1
    last_level = initial_last_level

    with open("call_hierarchy.txt", "r") as file:
      for line in file:

        indent = 0
        for c in line:
            if c == ' ':
                indent += 0.25
            else:
                break

        next_level: int = int(indent)
        update_path(path_from_root_node, next_level, last_level, line)
        update_hierarchy(test_hierarchy_tree, path_from_root_node)
        last_level = next_level

    tests = list(find_tests_and_normalize_names(test_hierarchy_tree))
    tests.sort()
    for item in tests:
        print(item)
    print(len(tests))
    test_methods_with_class_name = \
        map(separate_class_and_method_names, filter(lambda test: 'functional' in test, tests))
    test_methods_grouped_by_class =\
        dict(functools.reduce(aggregate_tests_method_for_class, test_methods_with_class_name, {}))
    testng_classes = ''
    for class_name in test_methods_grouped_by_class:
        testng_classes += testng_class_xml_fragment(class_name, test_methods_grouped_by_class[class_name])
    with open("testng_suite.xml", "w") as suite_xml:
        suite_xml.write(testng_suite_xml(testng_classes))


if __name__ == '__main__':
    main()
